#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查并修复documents表结构
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, inspect, text
from app.core.config import settings
from app.db.base_session import SessionLocal

def check_and_fix_documents_table():
    """检查并修复documents表结构"""
    
    try:
        # 创建数据库连接
        db = SessionLocal()
        engine = db.bind
        inspector = inspect(engine)
        
        print("=== 检查documents表结构 ===")
        
        # 获取当前表结构
        columns = inspector.get_columns('documents')
        column_names = [col['name'] for col in columns]
        
        print(f"当前表列: {column_names}")
        
        # 检查需要的列
        required_columns = {
            'document_type': 'VARCHAR',
            'file_type': 'VARCHAR', 
            'document_category': 'VARCHAR',
            'ocr_status': 'VARCHAR',
            'updated_at': 'DATETIME'
        }
        
        missing_columns = []
        for col_name, col_type in required_columns.items():
            if col_name not in column_names:
                missing_columns.append((col_name, col_type))
        
        if missing_columns:
            print(f"\n缺少的列: {missing_columns}")
            
            # 添加缺失的列
            for col_name, col_type in missing_columns:
                try:
                    sql = f"ALTER TABLE documents ADD COLUMN {col_name} {col_type}"
                    print(f"执行SQL: {sql}")
                    db.execute(text(sql))
                    db.commit()
                    print(f"✅ 成功添加列: {col_name}")
                except Exception as e:
                    print(f"❌ 添加列 {col_name} 失败: {str(e)}")
                    db.rollback()
        else:
            print("\n✅ 所有必需的列都存在")
        
        # 再次检查表结构
        print("\n=== 修复后的表结构 ===")
        columns = inspector.get_columns('documents')
        for col in columns:
            print(f"  {col['name']}: {col['type']}")
        
        db.close()
        
    except Exception as e:
        print(f"❌ 检查表结构失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_and_fix_documents_table()