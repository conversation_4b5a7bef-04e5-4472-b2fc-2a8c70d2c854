from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from typing import Union
import logging
import traceback
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def add_cors_headers(response: JSONResponse) -> JSONResponse:
    """添加CORS头部到响应"""
    response.headers["Access-Control-Allow-Origin"] = "http://localhost:8080"
    response.headers["Access-Control-Allow-Credentials"] = "true"
    response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS"
    response.headers["Access-Control-Allow-Headers"] = "Authorization, Content-Type"
    return response

class HealthRecordsErrorHandler:
    """
    健康记录系统错误处理器
    """
    
    @staticmethod
    async def handle_exception(request: Request, exc: Exception) -> JSONResponse:
        """
        统一异常处理
        """
        error_id = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        
        # 记录错误详情
        logger.error(f"Error ID: {error_id}")
        logger.error(f"Request URL: {request.url}")
        logger.error(f"Request method: {request.method}")
        logger.error(f"Exception type: {type(exc).__name__}")
        logger.error(f"Exception message: {str(exc)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        
        # 根据异常类型返回不同的错误信息
        if isinstance(exc, HTTPException):
            return add_cors_headers(JSONResponse(
                status_code=exc.status_code,
                content={
                    "status": "error",
                    "error_code": f"HTTP_{exc.status_code}",
                    "message": exc.detail,
                    "error_id": error_id,
                    "timestamp": datetime.now().isoformat()
                }
            ))
        
        elif isinstance(exc, IntegrityError):
            return add_cors_headers(JSONResponse(
                status_code=400,
                content={
                    "status": "error",
                    "error_code": "INTEGRITY_ERROR",
                    "message": "数据完整性错误，请检查输入数据",
                    "error_id": error_id,
                    "timestamp": datetime.now().isoformat()
                }
            ))
        
        elif isinstance(exc, SQLAlchemyError):
            return add_cors_headers(JSONResponse(
                status_code=500,
                content={
                    "status": "error",
                    "error_code": "DATABASE_ERROR",
                    "message": "数据库操作失败，请稍后重试",
                    "error_id": error_id,
                    "timestamp": datetime.now().isoformat()
                }
            ))
        
        elif isinstance(exc, ValueError):
            return add_cors_headers(JSONResponse(
                status_code=400,
                content={
                    "status": "error",
                    "error_code": "VALIDATION_ERROR",
                    "message": f"参数验证失败: {str(exc)}",
                    "error_id": error_id,
                    "timestamp": datetime.now().isoformat()
                }
            ))
        
        elif isinstance(exc, PermissionError):
            return add_cors_headers(JSONResponse(
                status_code=403,
                content={
                    "status": "error",
                    "error_code": "PERMISSION_DENIED",
                    "message": "权限不足，无法执行此操作",
                    "error_id": error_id,
                    "timestamp": datetime.now().isoformat()
                }
            ))
        
        else:
            # 未知错误
            return add_cors_headers(JSONResponse(
                status_code=500,
                content={
                    "status": "error",
                    "error_code": "INTERNAL_ERROR",
                    "message": "服务器内部错误，请联系管理员",
                    "error_id": error_id,
                    "timestamp": datetime.now().isoformat()
                }
            ))
    
    @staticmethod
    def create_error_response(
        status_code: int,
        error_code: str,
        message: str,
        details: Union[str, dict] = None
    ) -> JSONResponse:
        """
        创建标准错误响应
        """
        error_id = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        
        content = {
            "status": "error",
            "error_code": error_code,
            "message": message,
            "error_id": error_id,
            "timestamp": datetime.now().isoformat()
        }
        
        if details:
            content["details"] = details
        
        return add_cors_headers(JSONResponse(status_code=status_code, content=content))
    
    @staticmethod
    def create_success_response(
        data: Union[dict, list] = None,
        message: str = "操作成功",
        meta: dict = None
    ) -> dict:
        """
        创建标准成功响应
        """
        response = {
            "status": "success",
            "message": message,
            "timestamp": datetime.now().isoformat()
        }
        
        if data is not None:
            response["data"] = data
        
        if meta:
            response["meta"] = meta
        
        return response

# 常用错误响应函数
def user_not_found_error():
    return HealthRecordsErrorHandler.create_error_response(
        status_code=404,
        error_code="USER_NOT_FOUND",
        message="用户不存在"
    )

def permission_denied_error():
    return HealthRecordsErrorHandler.create_error_response(
        status_code=403,
        error_code="PERMISSION_DENIED",
        message="权限不足，无法访问该资源"
    )

def validation_error(details: str):
    return HealthRecordsErrorHandler.create_error_response(
        status_code=400,
        error_code="VALIDATION_ERROR",
        message="参数验证失败",
        details=details
    )

def database_error():
    return HealthRecordsErrorHandler.create_error_response(
        status_code=500,
        error_code="DATABASE_ERROR",
        message="数据库操作失败，请稍后重试"
    )

def cache_error():
    return HealthRecordsErrorHandler.create_error_response(
        status_code=500,
        error_code="CACHE_ERROR",
        message="缓存操作失败，数据可能不是最新的"
    )

def rate_limit_error():
    return HealthRecordsErrorHandler.create_error_response(
        status_code=429,
        error_code="RATE_LIMIT_EXCEEDED",
        message="请求频率过高，请稍后重试"
    )

def service_unavailable_error():
    return HealthRecordsErrorHandler.create_error_response(
        status_code=503,
        error_code="SERVICE_UNAVAILABLE",
        message="服务暂时不可用，请稍后重试"
    )