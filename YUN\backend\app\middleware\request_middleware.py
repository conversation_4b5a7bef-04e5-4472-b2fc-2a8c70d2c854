from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
import time
import logging
from typing import Callable
from app.utils.performance_monitor import log_api_access, performance_monitor

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """
    请求日志记录中间件
    """
    
    def __init__(self, app, exclude_paths: list = None):
        super().__init__(app)
        self.exclude_paths = exclude_paths or [
            "/docs",
            "/redoc",
            "/openapi.json",
            "/favicon.ico",
            "/health/check",
            "/static"
        ]
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 记录请求开始时间
        start_time = time.time()
        
        # 检查是否需要排除此路径
        if any(request.url.path.startswith(path) for path in self.exclude_paths):
            return await call_next(request)
        
        # 提取请求信息
        request_info = {
            "method": request.method,
            "path": request.url.path,
            "query_params": str(request.query_params),
            "client_ip": self._get_client_ip(request),
            "user_agent": request.headers.get("user-agent", "Unknown"),
            "content_type": request.headers.get("content-type", "Unknown"),
            "content_length": request.headers.get("content-length", "0")
        }
        
        # 尝试获取用户信息（如果有认证）
        try:
            # 从请求头或状态中获取用户ID
            auth_header = request.headers.get("authorization")
            if auth_header:
                request_info["has_auth"] = True
            else:
                request_info["has_auth"] = False
        except Exception:
            request_info["has_auth"] = False
        
        # 记录API访问
        log_api_access(request_info)
        
        # 处理请求
        try:
            response = await call_next(request)
            success = 200 <= response.status_code < 400
        except Exception as e:
            logger.error(f"请求处理异常: {e}")
            success = False
            raise
        finally:
            # 计算执行时间
            execution_time = time.time() - start_time
            
            # 记录性能指标
            endpoint_name = f"{request.method} {request.url.path}"
            performance_monitor.record_api_call(endpoint_name, execution_time, success)
            
            # 记录响应信息
            logger.info(
                f"请求完成 - "
                f"方法: {request.method} "
                f"路径: {request.url.path} "
                f"状态码: {response.status_code if 'response' in locals() else 'Error'} "
                f"执行时间: {execution_time:.3f}s "
                f"客户端IP: {request_info['client_ip']}"
            )
        
        return response
    
    def _get_client_ip(self, request: Request) -> str:
        """
        获取客户端真实IP地址
        """
        # 检查代理头
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # 返回直接连接的IP
        return request.client.host if request.client else "Unknown"

class PerformanceMiddleware(BaseHTTPMiddleware):
    """
    性能监控中间件
    """
    
    def __init__(self, app, slow_request_threshold: float = 2.0):
        super().__init__(app)
        self.slow_request_threshold = slow_request_threshold
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        
        # 处理请求
        response = await call_next(request)
        
        # 计算执行时间
        execution_time = time.time() - start_time
        
        # 检查慢请求
        if execution_time > self.slow_request_threshold:
            logger.warning(
                f"慢请求检测 - "
                f"方法: {request.method} "
                f"路径: {request.url.path} "
                f"执行时间: {execution_time:.3f}s "
                f"阈值: {self.slow_request_threshold}s"
            )
        
        # 添加性能头信息
        response.headers["X-Response-Time"] = f"{execution_time:.3f}s"
        response.headers["X-Request-ID"] = str(id(request))
        
        return response

class SecurityMiddleware(BaseHTTPMiddleware):
    """
    安全中间件
    """
    
    def __init__(self, app):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 处理请求
        response = await call_next(request)
        
        # 添加安全头
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        return response

class CORSMiddleware(BaseHTTPMiddleware):
    """
    CORS中间件
    """
    
    def __init__(self, app, allowed_origins: list = None, allowed_methods: list = None):
        super().__init__(app)
        self.allowed_origins = allowed_origins or ["*"]
        self.allowed_methods = allowed_methods or ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 处理预检请求
        if request.method == "OPTIONS":
            response = Response()
            response.headers["Access-Control-Allow-Origin"] = "*"
            response.headers["Access-Control-Allow-Methods"] = ", ".join(self.allowed_methods)
            response.headers["Access-Control-Allow-Headers"] = "*"
            response.headers["Access-Control-Max-Age"] = "86400"
            return response
        
        # 处理实际请求
        response = await call_next(request)
        
        # 添加CORS头
        origin = request.headers.get("origin")
        if origin and ("*" in self.allowed_origins or origin in self.allowed_origins):
            response.headers["Access-Control-Allow-Origin"] = origin
        else:
            response.headers["Access-Control-Allow-Origin"] = "*"
        
        response.headers["Access-Control-Allow-Credentials"] = "true"
        response.headers["Access-Control-Allow-Methods"] = ", ".join(self.allowed_methods)
        response.headers["Access-Control-Allow-Headers"] = "*"
        
        return response