#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试移动端API文档查询功能
"""

import requests
import json

def test_mobile_document_list():
    """测试移动端文档列表查询"""
    base_url = "http://localhost:8006"
    
    # 1. 登录获取token
    login_data = {
        "username": "admin",
        "password": "admin"
    }
    
    try:
        print("1. 登录获取访问令牌...")
        login_response = requests.post(f"{base_url}/auth/login_json", json=login_data)
        print(f"登录响应状态码: {login_response.status_code}")
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            access_token = login_result["access_token"]
            user_info = login_result["user"]
            print(f"登录成功! 用户: {user_info['username']}, Custom ID: {user_info.get('id', 'N/A')}")
            
            # 设置认证头
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
            
            # 2. 测试移动端文档查询API
            print("\n2. 查询移动端文档列表...")
            mobile_docs_response = requests.get(f"{base_url}/api/mobile/documents", headers=headers)
            print(f"移动端文档查询响应状态码: {mobile_docs_response.status_code}")
            
            if mobile_docs_response.status_code == 200:
                mobile_docs_result = mobile_docs_response.json()
                print(f"移动端文档总数: {mobile_docs_result.get('total', 0)}")
                print(f"返回记录数: {len(mobile_docs_result.get('items', []))}")
                
                if mobile_docs_result.get('items'):
                    print("\n移动端文档列表:")
                    for i, doc in enumerate(mobile_docs_result['items'], 1):
                        print(f"{i}. ID: {doc.get('id')}, 标题: {doc.get('title')}, 类型: {doc.get('document_type')}, 状态: {doc.get('status')}")
                else:
                    print("移动端文档列表为空")
            else:
                print(f"移动端文档查询失败: {mobile_docs_response.text}")
            
            # 3. 测试普通文档API
            print("\n3. 查询普通文档列表...")
            docs_response = requests.get(f"{base_url}/api/documents/", headers=headers)
            print(f"普通文档查询响应状态码: {docs_response.status_code}")
            
            if docs_response.status_code == 200:
                docs_result = docs_response.json()
                print(f"普通文档总数: {docs_result.get('total', 0)}")
                print(f"返回记录数: {len(docs_result.get('items', []))}")
                
                # 查找SM_008用户的文档
                sm008_docs = [doc for doc in docs_result.get('items', []) if doc.get('custom_id') == 'SM_008']
                print(f"\nSM_008用户的文档数量: {len(sm008_docs)}")
                if sm008_docs:
                    print("SM_008用户的文档:")
                    for i, doc in enumerate(sm008_docs, 1):
                        print(f"{i}. ID: {doc.get('id')}, 标题: {doc.get('title')}, 类型: {doc.get('document_type')}, 状态: {doc.get('status')}")
            else:
                print(f"普通文档查询失败: {docs_response.text}")
                
        else:
            print(f"登录失败: {login_response.text}")
            
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_mobile_document_list()
    print("\n测试完成!")