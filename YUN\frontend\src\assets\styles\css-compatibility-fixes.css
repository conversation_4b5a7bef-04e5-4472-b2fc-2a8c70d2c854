/* CSS兼容性修复文件 */
/* 修复各种浏览器兼容性问题 */

/* 修复 -moz-appearance 兼容性问题 */
* {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* 修复 text-size-adjust 兼容性问题 */
html {
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

/* 修复 text-align: -webkit-match-parent 兼容性问题 */
[style*="text-align: -webkit-match-parent"] {
  text-align: -webkit-match-parent;
  text-align: match-parent;
}

/* 修复 user-select 兼容性问题 */
.user-select-none {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.user-select-text {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

.user-select-all {
  -webkit-user-select: all;
  -moz-user-select: all;
  -ms-user-select: all;
  user-select: all;
}

/* 修复 color-adjust 兼容性问题 */
* {
  -webkit-print-color-adjust: exact;
  print-color-adjust: exact;
}

/* 修复 scrollbar 兼容性问题 */
/* 对于不支持 scrollbar-color 和 scrollbar-width 的浏览器提供备用方案 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 修复 mask 属性兼容性问题 */
.mask-element {
  -webkit-mask-size: cover;
  mask-size: cover;
  -webkit-mask: url() no-repeat center;
  mask: url() no-repeat center;
}

/* 修复 Element UI 特定的兼容性问题 */
.el-button,
.el-input__inner,
.el-select .el-input__inner,
.el-textarea__inner,
.el-date-editor .el-input__inner {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
}

/* 修复动画性能问题 */
@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 避免在动画中使用会触发layout的属性 */
.animate-transform {
  animation: slideIn 0.3s ease-out;
}

.animate-opacity {
  animation: fadeIn 0.3s ease-out;
}

/* 修复 -webkit-text-decoration-skip 问题 */
a {
  text-decoration-skip-ink: auto;
}

/* 为不支持某些CSS属性的浏览器提供备用方案 */
@supports not (scrollbar-color: auto) {
  /* 为不支持 scrollbar-color 的浏览器提供 webkit 滚动条样式 */
  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
  }
}

@supports not (scrollbar-width: thin) {
  /* 为不支持 scrollbar-width 的浏览器提供备用方案 */
  .thin-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
}

/* 修复 mask-size 和 mask 属性顺序问题 */
.mask-fix {
  -webkit-mask-size: contain;
  -webkit-mask: url() no-repeat center;
  mask-size: contain;
  mask: url() no-repeat center;
}

/* 修复 text-align 属性顺序问题 */
.text-align-fix {
  text-align: -webkit-match-parent;
  text-align: inherit;
}

/* 性能优化 - 避免在关键渲染路径中使用昂贵的属性 */
.performance-optimized {
  will-change: transform, opacity;
  transform: translateZ(0); /* 创建新的合成层 */
}

/* 缓存优化提示 */
.cache-optimized {
  /* 这个类用于标识需要缓存优化的元素 */
  /* 实际的缓存策略需要在服务器端配置 */
  content: '';
}

/* 安全头部相关的CSS修复 */
.secure-content {
  /* 为需要安全头部的内容提供样式 */
  /* 实际的安全头部需要在服务器端配置 */
  content: '';
}