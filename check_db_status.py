import sqlite3
import json

# 连接数据库
conn = sqlite3.connect('YUN/backend/app.db')
cursor = conn.cursor()

print("=== 问卷回答表结构 ===")
try:
    cursor.execute("PRAGMA table_info(questionnaire_responses)")
    questionnaire_columns = cursor.fetchall()
    print('问卷回答表字段:', questionnaire_columns)
except Exception as e:
    print('问卷回答表结构查询失败:', e)

print("\n=== 评估结果表结构 ===")
try:
    cursor.execute("PRAGMA table_info(assessment_results)")
    assessment_columns = cursor.fetchall()
    print('评估结果表字段:', assessment_columns)
except Exception as e:
    print('评估结果表结构查询失败:', e)

print("\n=== SM_008用户的问卷回答记录 ===")
try:
    cursor.execute("""
        SELECT id, custom_id, questionnaire_id, status, created_at, score 
        FROM questionnaire_responses 
        WHERE custom_id = 'SM_008' 
        ORDER BY created_at DESC 
        LIMIT 5
    """)
    sm008_questionnaires = cursor.fetchall()
    print('SM_008问卷记录:', sm008_questionnaires)
except Exception as e:
    print('SM_008问卷记录查询失败:', e)

print("\n=== SM_008用户的评估结果记录 ===")
try:
    cursor.execute("""
        SELECT id, custom_id, assessment_id, status, created_at, score 
        FROM assessment_results 
        WHERE custom_id = 'SM_008' 
        ORDER BY created_at DESC 
        LIMIT 5
    """)
    sm008_assessments = cursor.fetchall()
    print('SM_008评估记录:', sm008_assessments)
except Exception as e:
    print('SM_008评估记录查询失败:', e)

print("\n=== 所有用户的问卷回答记录 ===")
try:
    cursor.execute("""
        SELECT custom_id, COUNT(*), status 
        FROM questionnaire_responses 
        GROUP BY custom_id, status
        ORDER BY custom_id
    """)
    all_questionnaires = cursor.fetchall()
    print('所有问卷记录:', all_questionnaires)
except Exception as e:
    print('所有问卷记录查询失败:', e)

print("\n=== 所有用户的评估结果记录 ===")
try:
    cursor.execute("""
        SELECT custom_id, COUNT(*), status 
        FROM assessment_results 
        GROUP BY custom_id, status
        ORDER BY custom_id
    """)
    all_assessments = cursor.fetchall()
    print('所有评估记录:', all_assessments)
except Exception as e:
    print('所有评估记录查询失败:', e)

conn.close()
print("\n=== 检查完成 ===")