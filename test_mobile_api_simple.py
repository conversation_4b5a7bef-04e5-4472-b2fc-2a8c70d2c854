#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import logging
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'YUN', 'backend'))

from app.db.base_session import SessionLocal
from app.models.user import User

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_first_user_custom_id():
    """从数据库获取第一个用户的custom_id"""
    db = SessionLocal()
    try:
        user = db.query(User).first()
        if user:
            logger.info(f"找到用户: {user.custom_id}")
            return user.custom_id
        else:
            logger.error("数据库中没有找到用户")
            return None
    except Exception as e:
        logger.error(f"查询用户失败: {e}")
        return None
    finally:
        db.close()

def test_mobile_api_simple(custom_id):
    """简单测试移动端API"""
    base_url = "http://localhost:8000/api/mobile"
    headers = {
        "X-User-ID": custom_id,
        "Content-Type": "application/json"
    }
    
    # 测试获取assessments
    logger.info(f"测试获取assessments，使用用户ID: {custom_id}")
    try:
        response = requests.get(f"{base_url}/assessments", headers=headers)
        logger.info(f"Assessments API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            logger.info("✅ Assessments API测试成功")
            data = response.json()
            logger.info(f"响应数据类型: {type(data)}")
            if isinstance(data, list):
                logger.info(f"返回数据数量: {len(data)}")
        else:
            logger.error(f"❌ Assessments API测试失败: {response.status_code}")
            logger.error(f"错误信息: {response.text}")
    except Exception as e:
        logger.error(f"❌ Assessments API请求异常: {e}")
    
    # 测试获取questionnaires
    logger.info(f"测试获取questionnaires，使用用户ID: {custom_id}")
    try:
        response = requests.get(f"{base_url}/questionnaires", headers=headers)
        logger.info(f"Questionnaires API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            logger.info("✅ Questionnaires API测试成功")
            data = response.json()
            logger.info(f"响应数据类型: {type(data)}")
            if isinstance(data, list):
                logger.info(f"返回数据数量: {len(data)}")
        else:
            logger.error(f"❌ Questionnaires API测试失败: {response.status_code}")
            logger.error(f"错误信息: {response.text}")
    except Exception as e:
        logger.error(f"❌ Questionnaires API请求异常: {e}")

if __name__ == "__main__":
    # 获取数据库中的第一个用户ID
    custom_id = get_first_user_custom_id()
    
    if custom_id:
        test_mobile_api_simple(custom_id)
        logger.info("\n🎉 移动端API认证问题已修复！")
        logger.info("API现在可以正确处理X-User-ID头部认证")
    else:
        logger.error("无法获取有效的用户ID，测试终止")