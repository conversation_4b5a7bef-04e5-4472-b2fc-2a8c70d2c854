{"cleanup_time": "2025-07-01T15:09:35.254163", "total_planned": 172, "successfully_deleted": 201, "failed_deletions": 0, "deleted_files": ["test_frontend_api_calls.py", "test_questionnaire_assessment_data.py", "test_comprehensive_system.py", "test_frontend_fix.py", "test_api_fix.py", "test_frontend_clinical_scales.py", "test_data_management_import.py", "test_frontend_simple.py", "test_frontend_backend_integration.py", "test_api_connection.py", "test_frontend_questionnaire_comprehensive.py", "test_api_direct.py", "test_questionnaire_frontend.py", "test_assessment_responses.py", "test_authenticated_api.py", "test_answers_field.py", "test_aggregated_health.py", "test_health_endpoint.py", "test_frontend_assessments.py", "test_frontend_api_direct.py", "test_frontend_login_simple.py", "test_frontend_enhanced.py", "test_api_paths_comparison.py", "test_token_auth.py", "test_clinical_scales_page.py", "test_all_api_routes.py", "test_api_comprehensive.py", "simple_health_test.py", "playwright_test.py", "backend/test_404_fix.py", "backend/test_aggregated_api_simple.py", "backend/test_aggregated_endpoint.py", "backend/test_aggregated_questionnaire.py", "backend/test_aggregated_response.py", "backend/test_api_fields.py", "backend/test_api_full.py", "backend/test_api_restart.py", "backend/test_assessment_api.py", "backend/test_assessment_submit.py", "backend/test_backend_fixes.py", "backend/test_clinical_scales_debug.py", "backend/test_correct_aggregated_paths.py", "backend/test_db_insert.py", "backend/test_direct_login_simple.py", "backend/test_error_fixes.py", "backend/test_final_api.py", "backend/test_fix.py", "backend/test_fixed_aggregated_api.py", "backend/test_frontend_api.py", "backend/test_frontend_field_fix.py", "backend/test_health_profile_tabs.py", "backend/test_history_api.py", "backend/test_login.py", "backend/test_login_fix.py", "backend/test_mobile_api_fields.py", "backend/test_questionnaire_api.py", "backend/test_questionnaire_result.py", "backend/test_questionnaire_result_creation.py", "backend/test_questionnaire_submit.py", "backend/test_reload.py", "backend/test_result_saving_fix.py", "backend/test_route_debug.py", "backend/test_route_registration.py", "backend/test_simple_aggregated.py", "backend/test_simple_api.py", "backend/test_simple_import.py", "backend/test_sm008_api.py", "backend/test_template_debug.py", "backend/test_templates.py", "backend/debug_aggregated_api.py", "backend/debug_aggregated_import.py", "backend/debug_api.py", "backend/debug_api_context.py", "backend/debug_api_functions.py", "backend/debug_api_inline.py", "backend/debug_api_registration.py", "backend/debug_assessment_mapping.py", "backend/debug_assessment_params.py", "backend/debug_dimension_calculation.py", "backend/debug_dimension_scores.py", "backend/debug_full_aggregation.py", "backend/debug_import_process.py", "backend/debug_login.py", "backend/debug_runtime_import.py", "backend/debug_scoring_format.py", "backend/debug_sm008_scoring.py", "backend/debug_template3.py", "backend/debug_template_id.py", "backend/debug_template_import.py", "backend/check_actual_questions.py", "backend/check_all_table_structures.py", "backend/check_all_tables.py", "backend/check_and_fix_template_id.py", "backend/check_answers_data.py", "backend/check_assessment_result_4.py", "backend/check_assessment_results.py", "backend/check_assessment_results_detailed.py", "backend/check_assessment_template.py", "backend/check_assessment_templates.py", "backend/check_constraints.py", "backend/check_custom_id.py", "backend/check_data_format.py", "backend/check_db_assessment.py", "backend/check_db_integrity.py", "backend/check_db_schema.py", "backend/check_db_structure.py", "backend/check_db_templates.py", "backend/check_dimension_scores.py", "backend/check_dimension_scores_final.py", "backend/check_duplicate_tables.py", "backend/check_health_records.py", "backend/check_history_data.py", "backend/check_questionnaire_result.py", "backend/check_questionnaire_submission.py", "backend/check_questionnaire_template.py", "backend/check_questionnaires_schema.py", "backend/check_redis.py", "backend/check_registered_routes.py", "backend/check_response_tables.py", "backend/check_routes.py", "backend/check_routes_debug.py", "backend/check_routes_simple.py", "backend/check_scoring_data.py", "backend/check_scoring_logic.py", "backend/check_scoring_rules.py", "backend/check_scoring_rules_detailed.py", "backend/check_sm008_answers.py", "backend/check_sm008_assessment_results.py", "backend/check_sm008_data_structure.py", "backend/check_sm008_dimension_data.py", "backend/check_sm008_final.py", "backend/check_sm008_questionnaire_data.py", "backend/check_sm008_results.py", "backend/check_sm008_simple.py", "backend/check_table_schema.py", "backend/check_table_structure.py", "backend/check_template_mapping.py", "backend/check_template_questions_structure.py", "backend/check_template_scoring.py", "backend/check_test_data.py", "backend/check_user_password.py", "backend/check_user_passwords.py", "backend/check_users.py", "backend/check_users_and_scoring.py", "backend/fix_all_issues.py", "backend/fix_api_raw_answers_sync.py", "backend/fix_assessment_scoring.py", "backend/fix_assessment_type_enum.py", "backend/fix_database.py", "backend/fix_database_type_errors.py", "backend/fix_dimension_scores.py", "backend/fix_dimensions.py", "backend/fix_dimensions_column.py", "backend/fix_distributions_template_id.py", "backend/fix_empty_answers.py", "backend/fix_imports.py", "backend/fix_jump_logic.py", "backend/fix_missing_records.py", "backend/fix_mobile_scoring.py", "backend/fix_psychological_data.py", "backend/fix_questionnaire_scoring.py", "backend/fix_questionnaire_template.py", "backend/fix_questionnaire_template_id.py", "backend/fix_scoring_complete.py", "backend/fix_scoring_rules.py", "backend/fix_scoring_system.py", "backend/fix_sm008_data.py", "backend/fix_source_files_scoring.py", "backend/fix_template3_format.py", "backend/fix_template_id.py", "backend/fix_template_scoring.py", "backend/fix_user_id_to_custom_id.py", "api_routes_test_report_20250701_124915.json", "comprehensive_api_test_report_20250701_124517.json", "comprehensive_system_test_report_20250701_125523.json", "comprehensive_system_test_report_20250701_125545.json", "comprehensive_test_report_20250701_133446.json", "comprehensive_test_report_20250701_133502.json", "comprehensive_test_report_20250701_133632.json", "comprehensive_test_report_20250701_134203.json", "comprehensive_test_report_20250701_134317.json", "comprehensive_test_report_20250701_134559.json", "comprehensive_test_report_20250701_134835.json", "connectivity_test_report.json", "frontend_backend_integration_test_report_20250701_125825.json", "frontend_enhanced_test_report_20250701_130227.json", "frontend_questionnaire_test_report_20250701_123844.json", "frontend_questionnaire_test_report_20250701_124146.json", "backend\\aggregated_api_test_report_20250629_112520.json", "backend\\aggregated_api_test_report_20250629_203347.json", "backend\\aggregated_api_test_report_20250629_203652.json", "backend\\aggregated_api_test_report_20250629_204145.json", "backend\\aggregated_api_test_report_20250701_123552.json", "backend\\database_check_report_20250629_112327.json", "backend\\database_check_report_20250629_124209.json", "backend\\database_check_report_20250629_124258.json", "backend\\database_check_report_20250630_220929.json", "backend\\database_check_report_20250630_225801.json", "backend\\database_check_report_20250630_230342.json", "backend\\database_check_report_20250701_000454.json", "backend\\database_check_report_20250701_120501.json"], "failed_files": [], "remaining_important_files": ["test_aggregated_api.py", "test_data_export.py", "test_manager.py", "basic_connectivity_test.py", "backend/check_tables.py"]}