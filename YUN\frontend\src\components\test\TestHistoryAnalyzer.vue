<template>
  <div class="test-history-analyzer">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>测试历史分析</h2>
      <p class="page-description">分析测试执行历史，识别趋势和性能问题</p>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" icon="Refresh" @click="refreshHistoryData">
          刷新数据
        </el-button>
        <el-button icon="Download" @click="exportHistoryReport">
          导出报告
        </el-button>
        <el-button icon="Delete" @click="clearOldHistory" :disabled="loading">
          清理历史
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-select v-model="selectedTimeRange" placeholder="时间范围" style="width: 150px;">
          <el-option label="最近7天" value="7d" />
          <el-option label="最近30天" value="30d" />
          <el-option label="最近90天" value="90d" />
          <el-option label="最近1年" value="1y" />
        </el-select>
        <el-select v-model="selectedProject" placeholder="项目" style="width: 150px; margin-left: 10px;">
          <el-option label="全部项目" value="all" />
          <el-option label="前端项目" value="frontend" />
          <el-option label="后端项目" value="backend" />
        </el-select>
        <el-input
          v-model="searchKeyword"
          placeholder="搜索测试套件..."
          prefix-icon="Search"
          style="width: 250px; margin-left: 10px;"
          clearable
        />
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="statistics-overview">
      <div class="stat-cards">
        <div class="stat-card">
          <div class="stat-icon success">
            <el-icon><SuccessFilled /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ totalTests.toLocaleString() }}</div>
            <div class="stat-label">总测试次数</div>
            <div class="stat-change positive">+{{ recentIncrease }}%</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon warning">
            <el-icon><Clock /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ averageExecutionTime }}s</div>
            <div class="stat-label">平均执行时间</div>
            <div class="stat-change" :class="executionTimeChange > 0 ? 'negative' : 'positive'">
              {{ executionTimeChange > 0 ? '+' : '' }}{{ executionTimeChange }}%
            </div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon info">
            <el-icon><TrendCharts /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ successRate }}%</div>
            <div class="stat-label">成功率</div>
            <div class="stat-change" :class="successRateChange > 0 ? 'positive' : 'negative'">
              {{ successRateChange > 0 ? '+' : '' }}{{ successRateChange }}%
            </div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon danger">
            <el-icon><Warning /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ failureCount }}</div>
            <div class="stat-label">失败次数</div>
            <div class="stat-change" :class="failureChange > 0 ? 'negative' : 'positive'">
              {{ failureChange > 0 ? '+' : '' }}{{ failureChange }}%
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 趋势分析图表 -->
    <div class="trend-analysis">
      <div class="chart-container">
        <div class="chart-header">
          <h3>测试执行趋势</h3>
          <div class="chart-controls">
            <el-radio-group v-model="chartType" size="small">
              <el-radio-button label="line">折线图</el-radio-button>
              <el-radio-button label="bar">柱状图</el-radio-button>
              <el-radio-button label="area">面积图</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="chart-content" ref="trendChartRef"></div>
      </div>
    </div>

    <!-- 性能分析 -->
    <div class="performance-analysis">
      <div class="section-header">
        <h3>性能分析</h3>
        <p class="section-description">分析测试执行性能和资源使用情况</p>
      </div>
      
      <div class="performance-grid">
        <div class="performance-chart">
          <h4>执行时间分布</h4>
          <div class="chart-wrapper" ref="executionTimeChartRef"></div>
        </div>
        
        <div class="performance-chart">
          <h4>内存使用情况</h4>
          <div class="chart-wrapper" ref="memoryUsageChartRef"></div>
        </div>
        
        <div class="performance-chart">
          <h4>CPU使用率</h4>
          <div class="chart-wrapper" ref="cpuUsageChartRef"></div>
        </div>
        
        <div class="performance-metrics">
          <h4>性能指标</h4>
          <div class="metrics-list">
            <div class="metric-item">
              <span class="metric-label">最快执行时间:</span>
              <span class="metric-value">{{ performanceMetrics.fastest }}s</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">最慢执行时间:</span>
              <span class="metric-value">{{ performanceMetrics.slowest }}s</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">平均内存使用:</span>
              <span class="metric-value">{{ performanceMetrics.avgMemory }}MB</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">峰值内存使用:</span>
              <span class="metric-value">{{ performanceMetrics.peakMemory }}MB</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">平均CPU使用:</span>
              <span class="metric-value">{{ performanceMetrics.avgCpu }}%</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">峰值CPU使用:</span>
              <span class="metric-value">{{ performanceMetrics.peakCpu }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 测试历史记录表格 -->
    <div class="history-table">
      <div class="section-header">
        <h3>测试执行历史</h3>
        <div class="table-controls">
          <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px;">
            <el-option label="全部" value="" />
            <el-option label="成功" value="success" />
            <el-option label="失败" value="failed" />
            <el-option label="跳过" value="skipped" />
          </el-select>
          <el-button size="small" @click="showComparisonDialog = true" style="margin-left: 10px;">
            <el-icon><Compare /></el-icon>
            对比分析
          </el-button>
        </div>
      </div>
      
      <el-table 
        :data="filteredHistoryData" 
        style="width: 100%" 
        stripe
        v-loading="loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="id" label="执行ID" width="100" sortable>
          <template #default="{ row }">
            <el-link type="primary" @click="viewExecutionDetail(row)">
              #{{ row.id }}
            </el-link>
          </template>
        </el-table-column>
        
        <el-table-column prop="testSuite" label="测试套件" min-width="200" sortable>
          <template #default="{ row }">
            <div class="suite-info">
              <el-icon class="suite-icon"><Folder /></el-icon>
              <span class="suite-name">{{ row.testSuite }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100" sortable>
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="duration" label="执行时间" width="120" sortable>
          <template #default="{ row }">
            <span class="duration-text">{{ row.duration }}s</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="testCount" label="测试数量" width="100" sortable>
          <template #default="{ row }">
            <div class="test-count">
              <span class="total">{{ row.testCount.total }}</span>
              <span class="passed">{{ row.testCount.passed }}</span>
              <span class="failed">{{ row.testCount.failed }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="coverage" label="覆盖率" width="120" sortable>
          <template #default="{ row }">
            <div class="coverage-progress">
              <el-progress
                :percentage="row.coverage"
                :stroke-width="6"
                :color="getCoverageColor(row.coverage)"
                :show-text="false"
              />
              <span class="coverage-text">{{ row.coverage }}%</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="executedAt" label="执行时间" width="180" sortable>
          <template #default="{ row }">
            <div class="execution-time">
              <el-icon><Clock /></el-icon>
              <span>{{ formatDateTime(row.executedAt) }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="executor" label="执行者" width="120">
          <template #default="{ row }">
            <div class="executor-info">
              <el-avatar :size="24" :src="row.executor.avatar">
                {{ row.executor.name.charAt(0) }}
              </el-avatar>
              <span class="executor-name">{{ row.executor.name }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="viewExecutionDetail(row)">
              <el-icon><View /></el-icon>
              详情
            </el-button>
            <el-button type="text" size="small" @click="viewTestReport(row)">
              <el-icon><Document /></el-icon>
              报告
            </el-button>
            <el-button type="text" size="small" @click="rerunTest(row)">
              <el-icon><Refresh /></el-icon>
              重跑
            </el-button>
            <el-dropdown trigger="click">
              <el-button type="text" size="small">
                <el-icon><More /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="downloadLogs(row)">
                    <el-icon><Download /></el-icon>
                    下载日志
                  </el-dropdown-item>
                  <el-dropdown-item @click="shareResult(row)">
                    <el-icon><Share /></el-icon>
                    分享结果
                  </el-dropdown-item>
                  <el-dropdown-item divided @click="deleteExecution(row)">
                    <el-icon><Delete /></el-icon>
                    删除记录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalRecords"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 执行详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      :title="`执行详情 - #${selectedExecution?.id}`"
      width="1000px"
    >
      <div v-if="selectedExecution" class="execution-detail">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h4>基本信息</h4>
          <div class="info-grid">
            <div class="info-item">
              <span class="info-label">测试套件:</span>
              <span class="info-value">{{ selectedExecution.testSuite }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">执行状态:</span>
              <el-tag :type="getStatusType(selectedExecution.status)">
                {{ getStatusText(selectedExecution.status) }}
              </el-tag>
            </div>
            <div class="info-item">
              <span class="info-label">执行时间:</span>
              <span class="info-value">{{ selectedExecution.duration }}s</span>
            </div>
            <div class="info-item">
              <span class="info-label">覆盖率:</span>
              <span class="info-value">{{ selectedExecution.coverage }}%</span>
            </div>
            <div class="info-item">
              <span class="info-label">执行者:</span>
              <span class="info-value">{{ selectedExecution.executor.name }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">执行时间:</span>
              <span class="info-value">{{ formatDateTime(selectedExecution.executedAt) }}</span>
            </div>
          </div>
        </div>

        <!-- 测试结果 -->
        <div class="detail-section">
          <h4>测试结果</h4>
          <div class="result-summary">
            <div class="result-item success">
              <span class="result-count">{{ selectedExecution.testCount.passed }}</span>
              <span class="result-label">通过</span>
            </div>
            <div class="result-item failed">
              <span class="result-count">{{ selectedExecution.testCount.failed }}</span>
              <span class="result-label">失败</span>
            </div>
            <div class="result-item skipped">
              <span class="result-count">{{ selectedExecution.testCount.skipped || 0 }}</span>
              <span class="result-label">跳过</span>
            </div>
          </div>
        </div>

        <!-- 失败详情 -->
        <div v-if="selectedExecution.failures?.length" class="detail-section">
          <h4>失败详情</h4>
          <div class="failures-list">
            <div v-for="failure in selectedExecution.failures" :key="failure.test" class="failure-item">
              <div class="failure-header">
                <el-icon class="failure-icon"><Warning /></el-icon>
                <span class="failure-test">{{ failure.test }}</span>
              </div>
              <div class="failure-message">{{ failure.message }}</div>
              <div class="failure-stack">{{ failure.stack }}</div>
            </div>
          </div>
        </div>

        <!-- 性能数据 -->
        <div class="detail-section">
          <h4>性能数据</h4>
          <div class="performance-data">
            <div class="perf-item">
              <span class="perf-label">内存使用:</span>
              <span class="perf-value">{{ selectedExecution.performance?.memory || 'N/A' }}MB</span>
            </div>
            <div class="perf-item">
              <span class="perf-label">CPU使用:</span>
              <span class="perf-value">{{ selectedExecution.performance?.cpu || 'N/A' }}%</span>
            </div>
            <div class="perf-item">
              <span class="perf-label">网络请求:</span>
              <span class="perf-value">{{ selectedExecution.performance?.requests || 'N/A' }}</span>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 对比分析对话框 -->
    <el-dialog
      v-model="showComparisonDialog"
      title="测试结果对比分析"
      width="1200px"
    >
      <div class="comparison-content">
        <div class="comparison-selector">
          <p>请选择要对比的测试执行记录（最多选择5个）:</p>
          <div class="selected-items">
            <el-tag
              v-for="item in selectedExecutions"
              :key="item.id"
              closable
              @close="removeFromComparison(item)"
            >
              #{{ item.id }} - {{ item.testSuite }}
            </el-tag>
          </div>
        </div>
        
        <div v-if="selectedExecutions.length >= 2" class="comparison-charts">
          <div class="comparison-chart">
            <h4>执行时间对比</h4>
            <div class="chart-wrapper" ref="comparisonTimeChartRef"></div>
          </div>
          
          <div class="comparison-chart">
            <h4>覆盖率对比</h4>
            <div class="chart-wrapper" ref="comparisonCoverageChartRef"></div>
          </div>
          
          <div class="comparison-chart">
            <h4>成功率对比</h4>
            <div class="chart-wrapper" ref="comparisonSuccessChartRef"></div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  SuccessFilled, Clock, TrendCharts, Warning, Refresh, Download, Delete,
  Search, Compare, View, Document, More, Share, Folder
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// 响应式数据
const loading = ref(false)
const selectedTimeRange = ref('30d')
const selectedProject = ref('all')
const searchKeyword = ref('')
const statusFilter = ref('')
const chartType = ref('line')
const currentPage = ref(1)
const pageSize = ref(20)
const totalRecords = ref(0)
const showDetailDialog = ref(false)
const showComparisonDialog = ref(false)
const selectedExecution = ref(null)
const selectedExecutions = ref([])

// 图表引用
const trendChartRef = ref()
const executionTimeChartRef = ref()
const memoryUsageChartRef = ref()
const cpuUsageChartRef = ref()
const comparisonTimeChartRef = ref()
const comparisonCoverageChartRef = ref()
const comparisonSuccessChartRef = ref()

let trendChart = null
let executionTimeChart = null
let memoryUsageChart = null
let cpuUsageChart = null
let comparisonTimeChart = null
let comparisonCoverageChart = null
let comparisonSuccessChart = null

// 统计数据
const totalTests = ref(1248)
const averageExecutionTime = ref(45.6)
const successRate = ref(94.2)
const failureCount = ref(72)
const recentIncrease = ref(12.5)
const executionTimeChange = ref(-8.3)
const successRateChange = ref(2.1)
const failureChange = ref(-15.2)

// 性能指标
const performanceMetrics = reactive({
  fastest: 12.3,
  slowest: 156.7,
  avgMemory: 245.8,
  peakMemory: 512.4,
  avgCpu: 35.2,
  peakCpu: 78.9
})

// 测试历史数据
const historyData = ref([
  {
    id: 1001,
    testSuite: 'Frontend Unit Tests',
    status: 'success',
    duration: 42.5,
    testCount: { total: 156, passed: 154, failed: 2, skipped: 0 },
    coverage: 94.2,
    executedAt: new Date('2024-01-15T10:30:00'),
    executor: { name: '张三', avatar: '' },
    performance: { memory: 234.5, cpu: 45.2, requests: 23 },
    failures: [
      {
        test: 'should render correctly',
        message: 'Expected element to be visible',
        stack: 'at TestComponent.spec.js:45:12'
      }
    ]
  },
  {
    id: 1002,
    testSuite: 'Backend API Tests',
    status: 'failed',
    duration: 67.8,
    testCount: { total: 89, passed: 82, failed: 7, skipped: 0 },
    coverage: 87.6,
    executedAt: new Date('2024-01-15T09:15:00'),
    executor: { name: '李四', avatar: '' },
    performance: { memory: 456.7, cpu: 62.3, requests: 45 },
    failures: [
      {
        test: 'POST /api/users should create user',
        message: 'Request timeout after 5000ms',
        stack: 'at api.test.js:123:8'
      },
      {
        test: 'GET /api/users should return users list',
        message: 'Expected status 200 but got 500',
        stack: 'at api.test.js:89:12'
      }
    ]
  },
  {
    id: 1003,
    testSuite: 'Integration Tests',
    status: 'success',
    duration: 123.4,
    testCount: { total: 45, passed: 45, failed: 0, skipped: 0 },
    coverage: 91.8,
    executedAt: new Date('2024-01-14T16:45:00'),
    executor: { name: '王五', avatar: '' },
    performance: { memory: 678.9, cpu: 78.5, requests: 67 }
  },
  {
    id: 1004,
    testSuite: 'E2E Tests',
    status: 'success',
    duration: 234.7,
    testCount: { total: 23, passed: 22, failed: 1, skipped: 0 },
    coverage: 85.3,
    executedAt: new Date('2024-01-14T14:20:00'),
    executor: { name: '赵六', avatar: '' },
    performance: { memory: 890.1, cpu: 89.2, requests: 89 },
    failures: [
      {
        test: 'User login flow',
        message: 'Element not found: #login-button',
        stack: 'at login.e2e.js:34:15'
      }
    ]
  }
])

// 计算属性
const filteredHistoryData = computed(() => {
  return historyData.value.filter(item => {
    const matchesSearch = !searchKeyword.value || 
      item.testSuite.toLowerCase().includes(searchKeyword.value.toLowerCase())
    
    const matchesStatus = !statusFilter.value || item.status === statusFilter.value
    
    return matchesSearch && matchesStatus
  })
})

// 方法
const refreshHistoryData = async () => {
  loading.value = true
  try {
    ElMessage.info('正在刷新历史数据...')
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // 更新统计数据
    totalTests.value = Math.floor(Math.random() * 500) + 1000
    averageExecutionTime.value = Math.floor(Math.random() * 30) + 30
    successRate.value = Math.floor(Math.random() * 10) + 90
    failureCount.value = Math.floor(Math.random() * 50) + 20
    
    ElMessage.success('历史数据已更新')
    
    // 更新图表
    updateAllCharts()
  } catch (error) {
    ElMessage.error('刷新失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const exportHistoryReport = async () => {
  try {
    const reportData = {
      timestamp: new Date().toISOString(),
      timeRange: selectedTimeRange.value,
      project: selectedProject.value,
      statistics: {
        totalTests: totalTests.value,
        averageExecutionTime: averageExecutionTime.value,
        successRate: successRate.value,
        failureCount: failureCount.value
      },
      performanceMetrics: performanceMetrics,
      historyData: filteredHistoryData.value
    }
    
    const blob = new Blob([JSON.stringify(reportData, null, 2)], {
      type: 'application/json'
    })
    
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `test-history-report-${new Date().toISOString().split('T')[0]}.json`
    a.click()
    URL.revokeObjectURL(url)
    
    ElMessage.success('历史报告已导出')
  } catch (error) {
    ElMessage.error('导出失败: ' + error.message)
  }
}

const clearOldHistory = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清理30天前的历史记录吗？此操作不可恢复。',
      '确认清理',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    loading.value = true
    
    // 模拟清理操作
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('历史记录清理完成')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('清理失败: ' + error.message)
    }
  } finally {
    loading.value = false
  }
}

const viewExecutionDetail = (execution) => {
  selectedExecution.value = execution
  showDetailDialog.value = true
}

const viewTestReport = (execution) => {
  // 打开测试报告
  const reportWindow = window.open('', '_blank')
  reportWindow.document.write(`
    <html>
      <head><title>测试报告 - #${execution.id}</title></head>
      <body>
        <h1>测试执行报告</h1>
        <h2>执行ID: #${execution.id}</h2>
        <p>测试套件: ${execution.testSuite}</p>
        <p>执行状态: ${getStatusText(execution.status)}</p>
        <p>执行时间: ${execution.duration}s</p>
        <p>覆盖率: ${execution.coverage}%</p>
      </body>
    </html>
  `)
}

const rerunTest = async (execution) => {
  try {
    await ElMessageBox.confirm(
      `确定要重新运行测试套件 "${execution.testSuite}" 吗？`,
      '确认重跑',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    
    ElMessage.info('正在重新运行测试...')
    
    // 模拟重跑操作
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    ElMessage.success('测试重跑完成')
    await refreshHistoryData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('重跑失败: ' + error.message)
    }
  }
}

const downloadLogs = (execution) => {
  const logs = `
测试执行日志 - #${execution.id}
测试套件: ${execution.testSuite}
执行时间: ${formatDateTime(execution.executedAt)}
执行状态: ${getStatusText(execution.status)}
执行时长: ${execution.duration}s
覆盖率: ${execution.coverage}%

详细日志:
[INFO] 开始执行测试套件
[INFO] 加载测试用例...
[INFO] 执行测试用例 1/156
[PASS] should render correctly
[INFO] 执行测试用例 2/156
[FAIL] should handle error cases
...
[INFO] 测试执行完成
  `
  
  const blob = new Blob([logs], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `test-logs-${execution.id}.txt`
  a.click()
  URL.revokeObjectURL(url)
  
  ElMessage.success('日志已下载')
}

const shareResult = (execution) => {
  const shareUrl = `${window.location.origin}/test-results/${execution.id}`
  
  if (navigator.share) {
    navigator.share({
      title: `测试结果 - #${execution.id}`,
      text: `测试套件 "${execution.testSuite}" 的执行结果`,
      url: shareUrl
    })
  } else {
    // 复制到剪贴板
    navigator.clipboard.writeText(shareUrl)
    ElMessage.success('分享链接已复制到剪贴板')
  }
}

const deleteExecution = async (execution) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除执行记录 #${execution.id} 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 从数据中移除
    const index = historyData.value.findIndex(item => item.id === execution.id)
    if (index > -1) {
      historyData.value.splice(index, 1)
    }
    
    ElMessage.success('执行记录已删除')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error.message)
    }
  }
}

const handleSelectionChange = (selections) => {
  selectedExecutions.value = selections
}

const removeFromComparison = (execution) => {
  const index = selectedExecutions.value.findIndex(item => item.id === execution.id)
  if (index > -1) {
    selectedExecutions.value.splice(index, 1)
  }
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

// 工具函数
const getStatusType = (status) => {
  const types = {
    success: 'success',
    failed: 'danger',
    skipped: 'warning'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    success: '成功',
    failed: '失败',
    skipped: '跳过'
  }
  return texts[status] || status
}

const getCoverageColor = (percentage) => {
  if (percentage >= 90) return '#67c23a'
  if (percentage >= 80) return '#e6a23c'
  if (percentage >= 60) return '#f56c6c'
  return '#909399'
}

const formatDateTime = (date) => {
  return new Date(date).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 图表相关方法
const initTrendChart = () => {
  if (!trendChartRef.value) return
  
  trendChart = echarts.init(trendChartRef.value)
  updateTrendChart()
}

const updateTrendChart = () => {
  if (!trendChart) return
  
  const dates = []
  const successData = []
  const failedData = []
  const executionTimeData = []
  
  // 生成模拟数据
  for (let i = 29; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    dates.push(date.toISOString().split('T')[0])
    
    successData.push(Math.floor(Math.random() * 20) + 80)
    failedData.push(Math.floor(Math.random() * 10) + 5)
    executionTimeData.push(Math.floor(Math.random() * 30) + 30)
  }
  
  const option = {
    title: {
      text: '测试执行趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['成功测试', '失败测试', '执行时间'],
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates
    },
    yAxis: [
      {
        type: 'value',
        name: '测试数量',
        position: 'left'
      },
      {
        type: 'value',
        name: '执行时间(s)',
        position: 'right'
      }
    ],
    series: [
      {
        name: '成功测试',
        type: chartType.value,
        data: successData,
        itemStyle: { color: '#67c23a' },
        areaStyle: chartType.value === 'area' ? {} : undefined
      },
      {
        name: '失败测试',
        type: chartType.value,
        data: failedData,
        itemStyle: { color: '#f56c6c' },
        areaStyle: chartType.value === 'area' ? {} : undefined
      },
      {
        name: '执行时间',
        type: 'line',
        yAxisIndex: 1,
        data: executionTimeData,
        itemStyle: { color: '#409eff' }
      }
    ]
  }
  
  trendChart.setOption(option)
}

const initPerformanceCharts = () => {
  // 执行时间分布图
  if (executionTimeChartRef.value) {
    executionTimeChart = echarts.init(executionTimeChartRef.value)
    const option = {
      title: { text: '执行时间分布', textStyle: { fontSize: 14 } },
      tooltip: { trigger: 'item' },
      series: [{
        type: 'pie',
        radius: '60%',
        data: [
          { value: 35, name: '< 30s' },
          { value: 45, name: '30-60s' },
          { value: 15, name: '60-120s' },
          { value: 5, name: '> 120s' }
        ]
      }]
    }
    executionTimeChart.setOption(option)
  }
  
  // 内存使用图
  if (memoryUsageChartRef.value) {
    memoryUsageChart = echarts.init(memoryUsageChartRef.value)
    const option = {
      title: { text: '内存使用趋势', textStyle: { fontSize: 14 } },
      tooltip: { trigger: 'axis' },
      xAxis: { type: 'category', data: ['1h', '2h', '3h', '4h', '5h', '6h'] },
      yAxis: { type: 'value', name: 'MB' },
      series: [{
        type: 'line',
        data: [234, 267, 289, 312, 298, 276],
        smooth: true,
        itemStyle: { color: '#e6a23c' }
      }]
    }
    memoryUsageChart.setOption(option)
  }
  
  // CPU使用图
  if (cpuUsageChartRef.value) {
    cpuUsageChart = echarts.init(cpuUsageChartRef.value)
    const option = {
      title: { text: 'CPU使用率', textStyle: { fontSize: 14 } },
      tooltip: { trigger: 'axis' },
      xAxis: { type: 'category', data: ['1h', '2h', '3h', '4h', '5h', '6h'] },
      yAxis: { type: 'value', name: '%', max: 100 },
      series: [{
        type: 'bar',
        data: [45, 52, 38, 67, 43, 39],
        itemStyle: { color: '#409eff' }
      }]
    }
    cpuUsageChart.setOption(option)
  }
}

const updateAllCharts = () => {
  updateTrendChart()
  initPerformanceCharts()
}

// 监听图表类型变化
watch(chartType, () => {
  updateTrendChart()
})

// 监听对比选择变化
watch(selectedExecutions, (newVal) => {
  if (newVal.length >= 2) {
    nextTick(() => {
      initComparisonCharts()
    })
  }
}, { deep: true })

const initComparisonCharts = () => {
  if (!selectedExecutions.value.length) return
  
  const names = selectedExecutions.value.map(item => `#${item.id}`)
  const durations = selectedExecutions.value.map(item => item.duration)
  const coverages = selectedExecutions.value.map(item => item.coverage)
  const successRates = selectedExecutions.value.map(item => 
    (item.testCount.passed / item.testCount.total * 100).toFixed(1)
  )
  
  // 执行时间对比
  if (comparisonTimeChartRef.value) {
    comparisonTimeChart = echarts.init(comparisonTimeChartRef.value)
    const option = {
      tooltip: { trigger: 'axis' },
      xAxis: { type: 'category', data: names },
      yAxis: { type: 'value', name: '秒' },
      series: [{
        type: 'bar',
        data: durations,
        itemStyle: { color: '#409eff' }
      }]
    }
    comparisonTimeChart.setOption(option)
  }
  
  // 覆盖率对比
  if (comparisonCoverageChartRef.value) {
    comparisonCoverageChart = echarts.init(comparisonCoverageChartRef.value)
    const option = {
      tooltip: { trigger: 'axis' },
      xAxis: { type: 'category', data: names },
      yAxis: { type: 'value', name: '%', max: 100 },
      series: [{
        type: 'bar',
        data: coverages,
        itemStyle: { color: '#67c23a' }
      }]
    }
    comparisonCoverageChart.setOption(option)
  }
  
  // 成功率对比
  if (comparisonSuccessChartRef.value) {
    comparisonSuccessChart = echarts.init(comparisonSuccessChartRef.value)
    const option = {
      tooltip: { trigger: 'axis' },
      xAxis: { type: 'category', data: names },
      yAxis: { type: 'value', name: '%', max: 100 },
      series: [{
        type: 'bar',
        data: successRates,
        itemStyle: { color: '#e6a23c' }
      }]
    }
    comparisonSuccessChart.setOption(option)
  }
}

// 生命周期
onMounted(async () => {
  totalRecords.value = historyData.value.length
  
  await refreshHistoryData()
  
  nextTick(() => {
    initTrendChart()
    initPerformanceCharts()
  })
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    trendChart?.resize()
    executionTimeChart?.resize()
    memoryUsageChart?.resize()
    cpuUsageChart?.resize()
    comparisonTimeChart?.resize()
    comparisonCoverageChart?.resize()
    comparisonSuccessChart?.resize()
  })
})
</script>

<style scoped>
.test-history-analyzer {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.statistics-overview {
  margin-bottom: 32px;
}

.stat-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.success {
  background: #67c23a;
}

.stat-icon.warning {
  background: #e6a23c;
}

.stat-icon.info {
  background: #409eff;
}

.stat-icon.danger {
  background: #f56c6c;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.stat-change {
  font-size: 12px;
  font-weight: 600;
}

.stat-change.positive {
  color: #67c23a;
}

.stat-change.negative {
  color: #f56c6c;
}

.trend-analysis,
.performance-analysis,
.history-table {
  margin-bottom: 32px;
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-container {
  width: 100%;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.chart-content {
  height: 400px;
  width: 100%;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.section-description {
  margin: 4px 0 0 0;
  font-size: 14px;
  color: #606266;
}

.performance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.performance-chart {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
}

.performance-chart h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #303133;
}

.chart-wrapper {
  height: 200px;
  width: 100%;
}

.performance-metrics {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
}

.performance-metrics h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #303133;
}

.metrics-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f2f5;
}

.metric-item:last-child {
  border-bottom: none;
}

.metric-label {
  color: #606266;
  font-size: 14px;
}

.metric-value {
  color: #303133;
  font-weight: 600;
  font-size: 14px;
}

.table-controls {
  display: flex;
  align-items: center;
}

.suite-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.suite-icon {
  color: #409eff;
}

.suite-name {
  font-weight: 500;
}

.duration-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 600;
}

.test-count {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 12px;
}

.test-count .total {
  font-weight: 600;
  color: #303133;
}

.test-count .passed {
  color: #67c23a;
}

.test-count .failed {
  color: #f56c6c;
}

.coverage-progress {
  display: flex;
  align-items: center;
  gap: 8px;
}

.coverage-text {
  font-size: 12px;
  font-weight: 600;
  min-width: 35px;
}

.execution-time {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #606266;
}

.executor-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.executor-name {
  font-size: 13px;
  color: #303133;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.execution-detail {
  padding: 20px 0;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #303133;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 8px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.info-label {
  color: #606266;
  font-size: 14px;
}

.info-value {
  color: #303133;
  font-weight: 500;
  font-size: 14px;
}

.result-summary {
  display: flex;
  gap: 24px;
}

.result-item {
  text-align: center;
  padding: 16px;
  border-radius: 8px;
  min-width: 80px;
}

.result-item.success {
  background: rgba(103, 194, 58, 0.1);
  border: 1px solid #67c23a;
}

.result-item.failed {
  background: rgba(245, 108, 108, 0.1);
  border: 1px solid #f56c6c;
}

.result-item.skipped {
  background: rgba(230, 162, 60, 0.1);
  border: 1px solid #e6a23c;
}

.result-count {
  display: block;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 4px;
}

.result-label {
  font-size: 12px;
  color: #606266;
}

.failures-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.failure-item {
  border: 1px solid #f56c6c;
  border-radius: 8px;
  padding: 16px;
  background: rgba(245, 108, 108, 0.05);
}

.failure-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.failure-icon {
  color: #f56c6c;
}

.failure-test {
  font-weight: 600;
  color: #303133;
}

.failure-message {
  color: #f56c6c;
  margin-bottom: 8px;
  font-size: 14px;
}

.failure-stack {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #909399;
  background: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
  overflow-x: auto;
}

.performance-data {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.perf-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.perf-label {
  color: #606266;
  font-size: 14px;
}

.perf-value {
  color: #303133;
  font-weight: 600;
  font-size: 14px;
}

.comparison-content {
  padding: 20px 0;
}

.comparison-selector {
  margin-bottom: 24px;
}

.comparison-selector p {
  margin: 0 0 12px 0;
  color: #606266;
}

.selected-items {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.comparison-charts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.comparison-chart {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
}

.comparison-chart h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #303133;
}
</style>