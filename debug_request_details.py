#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'mobile'))

import requests
import json

def test_direct_request():
    """直接测试HTTP请求"""
    print("=== 直接测试HTTP请求 ===")
    
    # 测试数据
    auth_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    # 测试公网服务器
    url = "http://8.138.188.26/api/direct-login"
    
    print(f"请求URL: {url}")
    print(f"请求数据: {auth_data}")
    
    try:
        # 发送JSON请求
        response = requests.post(
            url,
            json=auth_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"\n响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"\n解析后的JSON: {result}")
                
                if result.get('access_token'):
                    print("✓ 直接请求登录成功")
                else:
                    print("✗ 直接请求登录失败")
                    print(f"错误: {result.get('error', '未知错误')}")
            except json.JSONDecodeError as e:
                print(f"JSON解析失败: {e}")
        else:
            print("✗ HTTP请求失败")
            
    except Exception as e:
        print(f"请求异常: {e}")

def test_mobile_api_request():
    """测试移动端API的请求方法"""
    print("\n=== 测试移动端API请求方法 ===")
    
    from utils.cloud_api import CloudAPI
    
    # 创建API实例
    api = CloudAPI(base_url="http://8.138.188.26")
    
    # 准备认证数据
    auth_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    print(f"使用移动端API发送请求...")
    print(f"认证数据: {auth_data}")
    
    try:
        # 直接调用_make_request方法
        response = api._make_request(
            "POST",
            "direct-login",
            json_data=auth_data,
            bypass_auth=True
        )
        
        print(f"\n移动端API响应: {response}")
        
        if response and response.get('access_token'):
            print("✓ 移动端API请求成功")
        else:
            print("✗ 移动端API请求失败")
            if response:
                print(f"错误: {response.get('error', '未知错误')}")
                
    except Exception as e:
        print(f"移动端API请求异常: {e}")

if __name__ == "__main__":
    test_direct_request()
    test_mobile_api_request()