<template>
  <div class="assessments-container">
    <h1>医学评估量表</h1>

    <el-tabs v-model="activeTab" type="card">
      <el-tab-pane label="可用量表" name="available">
        <el-card class="assessment-card">
          <template #header>
            <div class="card-header">
              <span>医学评估量表列表</span>
              <div>
                <el-button type="primary" @click="handleCreate" v-if="isAdmin">创建量表</el-button>
              </div>
            </div>
          </template>

          <el-table
            v-loading="loading"
            :data="assessments"
            style="width: 100%"
            border
          >
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="name" label="量表名称" min-width="180" />
            <el-table-column prop="category" label="分类" width="120" />
            <el-table-column prop="item_count" label="项目数" width="100" />
            <el-table-column prop="target" label="适用对象" width="120" />
            <el-table-column prop="created_at" label="创建时间" width="180" />
            <el-table-column label="操作" width="260" fixed="right">
              <template #default="scope">
                <el-button size="small" @click="handlePreview(scope.row)">预览</el-button>
                <el-button size="small" type="success" @click="handleStartAssessment(scope.row)">开始评估</el-button>
                <el-button size="small" type="primary" @click="handleEdit(scope.row)" v-if="isAdmin">编辑</el-button>
                <el-button size="small" type="danger" @click="handleDelete(scope.row)" v-if="isAdmin">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>

      <el-tab-pane label="我的评估记录" name="records">
        <el-card class="record-card">
          <template #header>
            <div class="card-header">
              <span>量表评估记录</span>
            </div>
          </template>

          <el-table
            v-loading="recordsLoading"
            :data="assessmentRecords"
            style="width: 100%"
            border
          >
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="assessment_name" label="量表名称" min-width="180">
              <template #default="scope">
                <div>
                  <div>{{ scope.row.assessment_name }}</div>
                  <div class="assessment-meta" v-if="scope.row.round_number">
                    <el-tag size="small" type="info">第{{ scope.row.round_number }}轮</el-tag>
                    <el-tag size="small" type="warning" v-if="scope.row.sequence_number > 1">序号{{ scope.row.sequence_number }}</el-tag>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="score" label="得分" width="100" />
            <el-table-column prop="result" label="评估结果" width="150" />
            <el-table-column prop="completed_at" label="完成时间" width="180" />
            <el-table-column prop="evaluator" label="评估人" width="120" />
            <el-table-column label="操作" width="150" fixed="right">
              <template #default="scope">
                <el-button size="small" @click="handleViewRecord(scope.row)">查看详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <!-- 量表预览对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      :title="currentAssessment.name || currentAssessment.title || '量表预览'"
      width="80%"
      class="assessment-dialog"
      destroy-on-close
    >
      <div v-if="previewDialogVisible" class="assessment-preview-container">
        <assessment-preview
          :assessment="currentAssessment"
          :preview-mode="true"
        />
        <div class="preview-actions">
          <el-button type="primary" @click="previewDialogVisible = false">关闭</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 进行评估对话框 -->
    <el-dialog
      v-model="assessmentDialogVisible"
      :title="currentAssessment.name"
      width="70%"
      class="assessment-dialog"
      :before-close="handleCloseAssessmentDialog"
    >
      <div class="assessment-form">
        <div class="assessment-description">
          <p><strong>量表说明：</strong> {{ currentAssessment.description }}</p>
          <p><strong>适用对象：</strong> {{ currentAssessment.target }}</p>
          <p><strong>评分方法：</strong> {{ currentAssessment.scoring_method }}</p>
        </div>

        <el-divider content-position="center">评估表</el-divider>

        <user-search v-if="!selectedUserId" @select-user="handleUserSelect" />

        <div v-if="selectedUserId" class="selected-user-info">
          <p><strong>当前评估用户：</strong> {{ selectedUserName }}</p>
        </div>

        <el-form :model="assessmentForm" ref="assessmentForm" class="assessment-items-form">
          <div v-for="(item, iIndex) in currentAssessment.items" :key="iIndex" class="assessment-item">
            <el-form-item
              :label="`${iIndex + 1}. ${item.question}`"
              :prop="`responses.${iIndex}`"
              :rules="{ required: true, message: '此项为必选项', trigger: 'change' }"
              label-position="top"
            >
              <el-radio-group v-model="assessmentForm.responses[iIndex]">
                <div v-for="(option, oIndex) in item.options" :key="oIndex" class="option-item">
                  <el-radio :label="option.value">{{ option.label }} ({{ option.score }}分)</el-radio>
                </div>
              </el-radio-group>
            </el-form-item>
          </div>

          <el-form-item label="评估备注" prop="notes">
            <el-input
              v-model="assessmentForm.notes"
              type="textarea"
              :rows="3"
              placeholder="请输入评估备注"
            ></el-input>
          </el-form-item>
        </el-form>

        <div class="dialog-footer">
          <el-button @click="assessmentDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitAssessment">提交评估</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 查看评估记录对话框 -->
    <el-dialog
      v-model="recordDialogVisible"
      title="评估记录详情"
      width="70%"
    >
      <div class="record-detail">
        <div class="record-header">
          <h2>{{ currentRecord.assessment_name }}</h2>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="评估得分">{{ currentRecord.score }}</el-descriptions-item>
            <el-descriptions-item label="评估结果">{{ currentRecord.result }}</el-descriptions-item>
            <el-descriptions-item label="评估时间">{{ currentRecord.completed_at }}</el-descriptions-item>
            <el-descriptions-item label="评估人">{{ currentRecord.evaluator }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <el-divider content-position="center">评估项目详情</el-divider>

        <div v-for="(item, index) in currentRecord.items" :key="index" class="record-item">
          <div class="record-item-question">{{ index + 1 }}. {{ item.question }}</div>
          <div class="record-item-answer">
            <strong>选择答案:</strong> {{ item.selected_option.label }} ({{ item.selected_option.score }}分)
          </div>
        </div>

        <el-divider content-position="center">评估备注</el-divider>

        <div class="record-notes">
          {{ currentRecord.notes || '无评估备注' }}
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, defineAsyncComponent, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from 'axios'
import { useRoute } from 'vue-router'
import { getAssessmentTemplates, getAssessmentTemplate } from '@/api/templates'
import { distributeAssessment, getAssessments } from '@/api/assessment'
import { getStandardAssessments } from '@/api/clinical_scales'
import { useUserStore } from '../store/user'

// 导入组件
import AssessmentList from '../components/assessment/AssessmentList.vue'
import AssessmentEditor from '../components/assessment/AssessmentEditor.vue'
import AssessmentDistributor from '../components/assessment/AssessmentDistributor.vue'
import AssessmentResults from '../components/assessment/AssessmentResults.vue'
import AssessmentPreview from '../components/assessment/AssessmentPreview.vue'
const UserSearch = defineAsyncComponent(() => import('../components/UserSearch.vue'))

// 用户状态
const userStore = useUserStore()
const isAdmin = computed(() => userStore.isAdmin)

// 标签页控制
const activeTab = ref('available')

// 加载状态
const loading = ref(false)
const recordsLoading = ref(false)

// 量表数据
const assessments = ref([])
const assessmentRecords = ref([])

// 对话框控制
const previewDialogVisible = ref(false)
const assessmentDialogVisible = ref(false)
const recordDialogVisible = ref(false)

// 当前选中的量表
const currentAssessment = reactive({
  id: null,
  name: '',
  description: '',
  category: '',
  target: '',
  scoring_method: '',
  interpretation: '',
  items: []
})

// 预览回答
const previewResponses = ref([])

// 选中用户信息
const selectedUserId = ref(null)
const selectedUserName = ref('')

// 评估表单
const assessmentForm = reactive({
  responses: [],
  notes: ''
})
const assessmentForm$ = ref(null)

// 当前查看的评估记录
const currentRecord = reactive({
  id: null,
  assessment_id: null,
  assessment_name: '',
  score: 0,
  result: '',
  completed_at: '',
  evaluator: '',
  items: [],
  notes: ''
})

// 初始化
onMounted(() => {
  fetchAssessments()
  fetchAssessmentRecords()
})

// 获取量表列表
const fetchAssessments = async () => {
  loading.value = true
  try {
    // 调用真实API
    const response = await axios.get('/api/assessments')
    console.log('评估量表接口返回数据:', response.data)
    
    // 检查返回的数据结构
    if (Array.isArray(response.data)) {
      // 如果返回的是数组，直接使用
      assessments.value = response.data
    } else if (response.data && typeof response.data === 'object') {
      // 如果返回的是对象，尝试获取其中的数据数组
      if (Array.isArray(response.data.data)) {
        assessments.value = response.data.data
      } else if (Array.isArray(response.data.items)) {
        assessments.value = response.data.items
      } else if (Array.isArray(response.data.assessments)) {
        assessments.value = response.data.assessments
      } else {
        console.error('评估量表接口返回的数据结构不符合预期:', response.data)
        // 如果找不到数组，使用模拟数据
        assessments.value = [
        {
          id: 1,
          name: '抑郁自评量表 (SDS)',
          description: '抑郁自评量表（Self-Rating Depression Scale, SDS）由Zung于1965年编制，用于评定抑郁症状的严重程度，是临床上常用的抑郁症筛查工具。',
          category: '心理健康',
          item_count: 20,
          target: '成人',
          scoring_method: '每个项目按1-4级评分，总分为20项得分之和，标准分=总分×1.25。标准分<50分为正常，50-59分为轻度抑郁，60-69分为中度抑郁，≥70分为重度抑郁。',
          interpretation: 'SDS标准分<50分为正常，50-59分为轻度抑郁，60-69分为中度抑郁，≥70分为重度抑郁。',
          created_at: '2023-01-10',
          items: [
            {
              question: '我感到情绪沮丧，郁闷',
              options: [
                { value: 1, label: '很少', score: 1 },
                { value: 2, label: '有时', score: 2 },
                { value: 3, label: '经常', score: 3 },
                { value: 4, label: '持续', score: 4 }
              ]
            },
            {
              question: '早晨是我一天中最好的时光',
              options: [
                { value: 1, label: '持续', score: 1 },
                { value: 2, label: '经常', score: 2 },
                { value: 3, label: '有时', score: 3 },
                { value: 4, label: '很少', score: 4 }
              ]
            },
            {
              question: '我常常哭或想哭',
              options: [
                { value: 1, label: '很少', score: 1 },
                { value: 2, label: '有时', score: 2 },
                { value: 3, label: '经常', score: 3 },
                { value: 4, label: '持续', score: 4 }
              ]
            },
            {
              question: '我晚上睡眠不好',
              options: [
                { value: 1, label: '很少', score: 1 },
                { value: 2, label: '有时', score: 2 },
                { value: 3, label: '经常', score: 3 },
                { value: 4, label: '持续', score: 4 }
              ]
            }
          ]
        },
        {
          id: 2,
          name: '焦虑自评量表 (SAS)',
          description: '焦虑自评量表（Self-Rating Anxiety Scale, SAS）由Zung于1971年编制，用于评定焦虑症状的严重程度，是临床上常用的焦虑症筛查工具。',
          category: '心理健康',
          item_count: 20,
          target: '成人',
          scoring_method: '每个项目按1-4级评分，总分为20项得分之和，标准分=总分×1.25。标准分<50分为正常，50-59分为轻度焦虑，60-69分为中度焦虑，≥70分为重度焦虑。',
          interpretation: 'SAS标准分<50分为正常，50-59分为轻度焦虑，60-69分为中度焦虑，≥70分为重度焦虑。',
          created_at: '2023-01-15',
          items: []
        },
        {
          id: 3,
          name: '简易精神状态检查量表 (MMSE)',
          description: '简易精神状态检查量表（Mini-Mental State Examination, MMSE）由Folstein等于1975年编制，是最常用的认知功能筛查工具，用于评估认知障碍的程度。',
          category: '认知功能',
          item_count: 30,
          target: '成人和老年人',
          scoring_method: '总分30分，得分越低表示认知功能损害越严重。一般认为，总分≤17分提示认知功能重度损害，18-23分提示轻度损害，≥24分为正常。',
          interpretation: 'MMSE总分≤17分提示认知功能重度损害，18-23分提示轻度损害，≥24分为正常。受教育程度可能影响得分。',
          created_at: '2023-02-05',
          items: []
        }
      ]
      }
    }
  } catch (error) {
    console.error('获取量表列表失败:', error)
    ElMessage.error('获取量表列表失败')
  } finally {
    loading.value = false
  }
}

// 获取评估记录
const fetchAssessmentRecords = async () => {
  recordsLoading.value = true
  try {
    // 调用真实API
    const response = await axios.get('/api/assessments/records')
    console.log('评估记录接口返回数据:', response.data)
    
    // 检查返回的数据结构
    if (Array.isArray(response.data)) {
      // 如果返回的是数组，直接使用
      assessmentRecords.value = response.data
    } else if (response.data && typeof response.data === 'object') {
      // 如果返回的是对象，尝试获取其中的数据数组
      if (Array.isArray(response.data.data)) {
        assessmentRecords.value = response.data.data
      } else if (Array.isArray(response.data.items)) {
        assessmentRecords.value = response.data.items
      } else if (Array.isArray(response.data.records)) {
        assessmentRecords.value = response.data.records
      } else {
        console.error('评估记录接口返回的数据结构不符合预期:', response.data)
        // 如果找不到数组，使用模拟数据
        assessmentRecords.value = [
        {
          id: 1,
          assessment_id: 1,
          assessment_name: '抑郁自评量表 (SDS)',
          score: 45,
          result: '正常范围',
          completed_at: '2023-04-12 10:15:30',
          evaluator: '王医生',
          items: [
            {
              question: '我感到情绪沮丧，郁闷',
              selected_option: { value: 2, label: '有时', score: 2 }
            },
            {
              question: '早晨是我一天中最好的时光',
              selected_option: { value: 2, label: '经常', score: 2 }
            },
            {
              question: '我常常哭或想哭',
              selected_option: { value: 1, label: '很少', score: 1 }
            },
            {
              question: '我晚上睡眠不好',
              selected_option: { value: 3, label: '经常', score: 3 }
            }
          ],
          notes: '患者近期生活压力较大，但情绪状态良好，无明显抑郁症状。'
        },
        {
          id: 2,
          assessment_id: 2,
          assessment_name: '焦虑自评量表 (SAS)',
          score: 52,
          result: '轻度焦虑',
          completed_at: '2023-04-15 14:30:45',
          evaluator: '李医生',
          items: [],
          notes: '患者表现出轻度焦虑症状，建议适当调整生活节奏，注意休息。'
        }
      ]
      }
    }
  } catch (error) {
    console.error('获取评估记录失败:', error)
    ElMessage.error('获取评估记录失败')
  } finally {
    recordsLoading.value = false
  }
}

// 处理用户选择
const handleUserSelect = (customId, userName) => {
  selectedUserId.value = customId
  selectedUserName.value = userName || `用户${customId}`
}

// 处理量表预览
const handlePreview = async (assessment) => {
  if (!assessment || !assessment.id) {
    ElMessage.error('量表信息不完整，无法预览');
    return;
  }
  try {
    // 标准量表和用户量表的API路径不同
    const apiPath = assessment.is_standard 
      ? `/api/clinical-scales/standard-assessments/${assessment.template_key || assessment.id}` 
      : `/api/assessments/${assessment.id}`;
    
    const res = await axios.get(apiPath);
    if (res.data && (res.data.data || res.data)) {
      Object.assign(currentAssessment, adaptAssessmentDetail(res.data.data || res.data));
      previewDialogVisible.value = true;
    } else {
      ElMessage.error('未找到该量表详情');
    }
  } catch (error) {
    console.error('获取量表详情失败:', error);
    ElMessage.error('获取量表详情失败');
  }
};

// 字段适配函数，兼容标准量表结构
function adaptAssessmentDetail(raw) {
  return {
    title: raw.name || raw.title || '未命名',
    description: raw.description || '',
    category: raw.category || '',
    assessment_type: raw.assessment_type || 'self',
    status: raw.status || 'draft',
    questions: (raw.questions || []).map(q => ({
      id: q.question_id || q.id,
      question_text: q.question_text || q.text,
      question_type: q.question_type,
      required: q.is_required ?? q.required ?? true,
      options: q.options || [],
      order: q.order || 0
    })),
    instructions: raw.instructions || '',
    scoring_method: raw.scoring_method || '',
    result_ranges: raw.result_ranges || [],
    updated_at: raw.updated_at || '',
    revision_reason: raw.revision_reason || ''
  };
}

// 开始评估
const handleStartAssessment = (assessment) => {
  // 深拷贝评估数据
  Object.assign(currentAssessment, assessment)
  
  // 处理评估项目数组可能的不同命名
  if (!currentAssessment.items && assessment.questions) {
    currentAssessment.items = assessment.questions
  } else if (!currentAssessment.items && assessment.assessment_items) {
    currentAssessment.items = assessment.assessment_items
  } else if (!currentAssessment.items) {
    // 如果找不到评估项目数组，创建一个空数组
    console.warn('评估量表数据中找不到项目数组:', assessment)
    currentAssessment.items = []
  }
  
  // 初始化响应数组
  assessmentForm.responses = new Array(currentAssessment.items.length)
  assessmentForm.notes = ''
  selectedUserId.value = null
  selectedUserName.value = ''
  assessmentDialogVisible.value = true
}

// 关闭评估对话框前确认
const handleCloseAssessmentDialog = (done) => {
  ElMessageBox.confirm('确定要关闭评估？未保存的内容将丢失。', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    done()
  }).catch(() => {})
}

// 提交评估
const submitAssessment = async () => {
  if (!selectedUserId.value) {
    ElMessage.warning('请先选择评估用户')
    return
  }

  if (!assessmentForm$.value) return

  try {
    await assessmentForm$.value.validate()

    // 模拟API调用
    setTimeout(() => {
      ElMessage.success('评估提交成功！')
      assessmentDialogVisible.value = false

      // 刷新评估记录
      if (activeTab.value !== 'records') {
        activeTab.value = 'records'
      }
      fetchAssessmentRecords()
    }, 500)
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 查看评估记录详情
const handleViewRecord = (record) => {
  Object.assign(currentRecord, record)
  recordDialogVisible.value = true
}

// 创建量表
const handleCreate = () => {
  ElMessage.info('创建量表功能待实现')
}

// 编辑量表
const handleEdit = (assessment) => {
  ElMessage.info('编辑量表功能待实现')
}

// 删除量表
const handleDelete = (assessment) => {
  ElMessageBox.confirm(
    `确定要删除量表 "${assessment.name}" 吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      // 模拟API调用
      setTimeout(() => {
        assessments.value = assessments.value.filter(item => item.id !== assessment.id)
        ElMessage.success('删除成功')
      }, 500)
    })
    .catch(() => {
      // 用户取消删除
    })
}

// 监听标签页变化
watch(activeTab, (newVal) => {
  if (newVal === 'records') {
    fetchAssessmentRecords()
  }
})
</script>

<style scoped>
.assessments-container {
  padding: 20px;
}

.assessment-card,
.record-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.assessment-description {
  margin-bottom: 20px;
  color: #606266;
}

.assessment-item {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.item-title {
  font-weight: bold;
  margin-bottom: 12px;
}

.item-options,
.option-item {
  margin-top: 8px;
}

.dialog-footer {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
}

.record-header {
  margin-bottom: 20px;
}

.record-item {
  margin-bottom: 16px;
}

.record-item-question {
  font-weight: bold;
  margin-bottom: 8px;
}

.record-item-answer {
  margin-left: 20px;
}

.record-notes {
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 4px;
  margin-top: 16px;
}

.selected-user-info {
  margin: 16px 0;
  padding: 12px;
  background-color: #ecf5ff;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.assessment-items-form {
  margin-top: 20px;
}

/* 评估对话框样式 */
.assessment-dialog {
  .el-dialog__body {
    padding: 20px;
  }
}

.assessment-preview-container {
  width: 100%;
}

.preview-actions {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.assessment-preview {
  .assessment-description {
    background-color: #f5f7fa;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
    
    p {
      margin: 8px 0;
      line-height: 1.6;
    }
  }
}

.assessment-item {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
  background-color: #f9f9f9;
  
  .item-title {
    font-weight: bold;
    margin-bottom: 10px;
    color: #409eff;
  }
}

/* 多次评估样式 */
.assessment-meta {
  margin-top: 4px;
  display: flex;
  gap: 4px;
  
  .el-tag {
    font-size: 11px;
    height: 18px;
    line-height: 16px;
  }
}
  
  .item-options {
    padding-left: 10px;
    
    .option-item {
      margin-bottom: 5px;
    }
  }
</style>
