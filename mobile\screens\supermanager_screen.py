# screens/supermanager_screen.py
from kivy.uix.screenmanager import Screen
from kivy.lang import Builder
from kivy.metrics import dp
from kivy.properties import StringProperty, ListProperty, NumericProperty
from kivy.factory import Factory
from theme import AppTheme, AppMetrics, FontStyles, FontManager
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.label import Label
from kivy.uix.button import Button
from kivy.graphics import RoundedRectangle, Color
# 导入KivyMD组件
from kivymd.uix.button import MDIconButton
import json
import os
from widgets.logo import HealthLogo, add_logo_to_layout  # 导入统一的Logo组件

# 确保图形元素在kv字符串中可用
Factory.register('RoundedRectangle', cls=RoundedRectangle)
Factory.register('Color', cls=Color)

# 定义超级管理员界面
class SuperManagerScreen(Screen):
    # 属性定义
    user_name = StringProperty("李西同志")  # 超级管理员真实姓名
    
    # 今日概览数据
    active_users = NumericProperty(2458)
    active_users_change = NumericProperty(12)  # 百分比变化
    new_registrations = NumericProperty(128)
    new_registrations_change = NumericProperty(5)  # 百分比变化
    waiting_tasks = NumericProperty(23)
    waiting_tasks_change = NumericProperty(-2)  # 百分比变化
    system_status = StringProperty("正常")
    system_uptime = NumericProperty(99.9)  # 系统正常运行时间百分比
    
    # 最近活动列表
    recent_activities = ListProperty([
        {"icon": "account-plus", "text": "新增健康顾问注册申请", "time": "10分钟前"},
        {"icon": "shield-check", "text": "更新系统安全策略", "time": "30分钟前"},
        {"icon": "bell", "text": "发布健康资讯推送", "time": "1小时前"},
        {"icon": "chart-line", "text": "生成月度健康统计报告", "time": "2小时前"},
        {"icon": "cog", "text": "系统维护完成", "time": "4小时前"}
    ])
    
    def __init__(self, **kwargs):
        super(SuperManagerScreen, self).__init__(**kwargs)
        # 加载用户数据
        self.load_user_data()
    
    def load_user_data(self):
        """从本地存储加载用户数据"""
        try:
            # 尝试从data目录读取用户数据
            data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "../data")
            user_data_file = os.path.join(data_dir, "user_data.json")
            
            if os.path.exists(user_data_file):
                with open(user_data_file, "r") as f:
                    data = json.load(f)
                    user_info = data.get("user_info", {})
                    
                    # 设置用户名称
                    if "name" in user_info:
                        self.user_name = user_info["name"]
        except Exception as e:
            print(f"加载用户数据失败: {str(e)}")
    
    def on_enter(self):
        """屏幕进入时调用"""
        # 在实际应用中，这里应该从API获取最新的系统数据和活动
        self.update_activities()
    
    def navigate_to_rights_management(self):
        """导航到权限管理页面"""
        # 在实际应用中，这里应该跳转到权限管理页面
        pass
    
    def navigate_to_health_records(self):
        """导航到健康资料管理页面"""
        # 在实际应用中，这里应该跳转到健康资料管理页面
        pass
    
    def navigate_to_health_analysis(self):
        """导航到健康资料分析页面"""
        # 在实际应用中，这里应该跳转到健康资料分析页面
        pass
    
    def navigate_to_user_management(self):
        """导航到用户管理页面"""
        # 在实际应用中，这里应该跳转到用户管理页面
        pass
    
    def navigate_to_daily_log(self):
        """导航到每日日志页面"""
        # 在实际应用中，这里应该跳转到每日日志页面
        pass
    
    def navigate_to_health_notification(self):
        """导航到健康通知设置页面"""
        # 在实际应用中，这里应该跳转到健康通知设置页面
        pass
    
    def navigate_to_data_collection(self):
        """导航到资料采集页面"""
        # 在实际应用中，这里应该跳转到资料采集页面
        pass
    
    def navigate_to_query_management(self):
        """导航到查询管理页面"""
        # 在实际应用中，这里应该跳转到查询管理页面
        pass
    
    def navigate_to_running_analysis(self):
        """导航到运行分析页面"""
        # 在实际应用中，这里应该跳转到运行分析页面
        pass
    
    def navigate_to_platform_protection(self):
        """导航到平台维护页面"""
        # 在实际应用中，这里应该跳转到平台维护页面
        pass
    
    def navigate_to_data_statistics(self):
        """导航到数据统计页面"""
        # 在实际应用中，这里应该跳转到数据统计页面
        pass
    
    def navigate_to_system_settings(self):
        """导航到系统设置页面"""
        # 在实际应用中，这里应该跳转到系统设置页面
        pass

# 添加活动项的方法
def update_activities(self):
    """更新最近活动列表"""
    activities_container = self.ids.activities_container
    activities_container.clear_widgets()
    
    for activity in self.recent_activities:
        # 创建活动项容器
        item = BoxLayout(
            orientation="horizontal",
            size_hint_y=None,
            height=dp(50),
            padding=dp(5)
        )
        
        # 添加背景
        if item.canvas:
            with item.canvas.before:
                Color(rgba=AppTheme.BACKGROUND_COLOR)
                RoundedRectangle(pos=item.pos, size=item.size, radius=[dp(5)])
        
        # 添加图标
        icon = MDIconButton(
            icon=activity["icon"],
            theme_text_color="Custom",
            text_color=AppTheme.PRIMARY_COLOR,
            user_font_size=dp(24),
            size_hint_x=None,
            width=dp(40)
        )
        
        # 添加文本信息
        text_container = BoxLayout(
            orientation="vertical",
            padding=(dp(5), 0)
        )
        
        text_label = Label(
            text=activity["text"],
            font_size=dp(self.app.metrics.FONT_SIZE_SMALL),
            color=self.app.theme.TEXT_PRIMARY,
            halign="left",
            valign="middle",
            text_size=(None, dp(50))
        )
        
        # 添加时间
        time_label = Label(
            text=activity["time"],
            font_size=dp(self.app.metrics.FONT_SIZE_XSMALL),
            color=self.app.theme.TEXT_SECONDARY,
            size_hint_x=None,
            width=dp(80),
            halign="right",
            valign="middle",
            text_size=(dp(80), dp(50))
        )
        
        # 组装活动项
        text_container.add_widget(text_label)
        item.add_widget(icon)
        item.add_widget(text_container)
        item.add_widget(time_label)
        
        # 添加到容器
        activities_container.add_widget(item)

Builder.load_string("""
<SuperManagerScreen>:
    canvas.before:
        Color:
            rgba: app.theme.BACKGROUND_COLOR
        Rectangle:
            pos: self.pos
            size: self.size
    
    BoxLayout:
        orientation: "vertical"
        padding: dp(app.metrics.PADDING_MEDIUM)
        spacing: dp(app.metrics.PADDING_NORMAL)
        
        # 标题栏 - 使用统一的Logo组件
        BoxLayout:
            size_hint_y: None
            height: dp(150)
            canvas.before:
                Color:
                    rgba: app.theme.PRIMARY_COLOR
                RoundedRectangle:
                    pos: self.pos
                    size: self.size
                    radius: [dp(app.metrics.CORNER_RADIUS)]
            
            # 使用统一的HealthLogo组件
            HealthLogo:
                size_hint: None, None
                size: dp(200), dp(100)
                padding: dp(app.metrics.PADDING_SMALL)
                pos_hint: {"right": 1}
                
            Label:
                text: root.user_name
                font_size: dp(app.metrics.FONT_SIZE_SMALL)
                color: app.theme.TEXT_LIGHT
                halign: "right"
                text_size: self.size
        
        # 当前管理人员信息
        BoxLayout:
            size_hint_y: None
            height: dp(30)
            
            Label:
                text: "超级管理员: " + root.user_name
                font_size: dp(app.metrics.FONT_SIZE_SMALL)
                color: app.theme.TEXT_SECONDARY
                halign: "left"
                valign: "middle"
                text_size: self.size
        
        # 功能模块网格
        GridLayout:
            cols: 3
            spacing: dp(10)
            size_hint_y: None
            height: dp(240)  # 3行，每行80dp
            
            # 权限审批
            BoxLayout:
                orientation: "vertical"
                canvas.before:
                    Color:
                        rgba: app.theme.CARD_BACKGROUND
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [dp(5)]
                
                MDIconButton:
                    icon: "shield-account"
                    theme_text_color: "Custom"
                    text_color: app.theme.PRIMARY_COLOR
                    user_font_size: dp(24)
                    pos_hint: {"center_x": 0.5}
                    on_release: root.navigate_to_rights_management()
                
                Label:
                    text: "权限审批"
                    font_size: dp(app.metrics.FONT_SIZE_SMALL)
                    color: app.theme.TEXT_PRIMARY
                    halign: "center"
                    valign: "middle"
                    text_size: self.size
            
            # 健康资料管理
            BoxLayout:
                orientation: "vertical"
                canvas.before:
                    Color:
                        rgba: app.theme.CARD_BACKGROUND
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [dp(5)]
                
                MDIconButton:
                    icon: "file-document"
                    theme_text_color: "Custom"
                    text_color: app.theme.PRIMARY_COLOR
                    user_font_size: dp(24)
                    pos_hint: {"center_x": 0.5}
                    on_release: root.navigate_to_health_records()
                
                Label:
                    text: "健康资料管理"
                    font_size: dp(app.metrics.FONT_SIZE_SMALL)
                    color: app.theme.TEXT_PRIMARY
                    halign: "center"
                    valign: "middle"
                    text_size: self.size
            
            # 健康资料分析
            BoxLayout:
                orientation: "vertical"
                canvas.before:
                    Color:
                        rgba: app.theme.CARD_BACKGROUND
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [dp(5)]
                
                MDIconButton:
                    icon: "chart-bar"
                    theme_text_color: "Custom"
                    text_color: app.theme.PRIMARY_COLOR
                    user_font_size: dp(24)
                    pos_hint: {"center_x": 0.5}
                    on_release: root.navigate_to_health_analysis()
                
                Label:
                    text: "健康资料分析"
                    font_size: dp(app.metrics.FONT_SIZE_SMALL)
                    color: app.theme.TEXT_PRIMARY
                    halign: "center"
                    valign: "middle"
                    text_size: self.size
            
            # 成员管理
            BoxLayout:
                orientation: "vertical"
                canvas.before:
                    Color:
                        rgba: app.theme.CARD_BACKGROUND
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [dp(5)]
                
                MDIconButton:
                    icon: "account-group"
                    theme_text_color: "Custom"
                    text_color: app.theme.PRIMARY_COLOR
                    user_font_size: dp(24)
                    pos_hint: {"center_x": 0.5}
                    on_release: root.navigate_to_user_management()
                
                Label:
                    text: "成员管理"
                    font_size: dp(app.metrics.FONT_SIZE_SMALL)
                    color: app.theme.TEXT_PRIMARY
                    halign: "center"
                    valign: "middle"
                    text_size: self.size
            
            # 每日日志
            BoxLayout:
                orientation: "vertical"
                canvas.before:
                    Color:
                        rgba: app.theme.CARD_BACKGROUND
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [dp(5)]
                
                MDIconButton:
                    icon: "calendar-text"
                    theme_text_color: "Custom"
                    text_color: app.theme.PRIMARY_COLOR
                    user_font_size: dp(24)
                    pos_hint: {"center_x": 0.5}
                    on_release: root.navigate_to_daily_log()
                
                Label:
                    text: "每日日志"
                    font_size: dp(app.metrics.FONT_SIZE_SMALL)
                    color: app.theme.TEXT_PRIMARY
                    halign: "center"
                    valign: "middle"
                    text_size: self.size
            
            # 健康通知提醒
            BoxLayout:
                orientation: "vertical"
                canvas.before:
                    Color:
                        rgba: app.theme.CARD_BACKGROUND
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [dp(5)]
                
                MDIconButton:
                    icon: "bell-ring"
                    theme_text_color: "Custom"
                    text_color: app.theme.PRIMARY_COLOR
                    user_font_size: dp(24)
                    pos_hint: {"center_x": 0.5}
                    on_release: root.navigate_to_health_notification()
                
                Label:
                    text: "健康通知提醒"
                    font_size: dp(app.metrics.FONT_SIZE_SMALL)
                    color: app.theme.TEXT_PRIMARY
                    halign: "center"
                    valign: "middle"
                    text_size: self.size
            
            # 资料采集
            BoxLayout:
                orientation: "vertical"
                canvas.before:
                    Color:
                        rgba: app.theme.CARD_BACKGROUND
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [dp(5)]
                
                MDIconButton:
                    icon: "database-plus"
                    theme_text_color: "Custom"
                    text_color: app.theme.PRIMARY_COLOR
                    user_font_size: dp(24)
                    pos_hint: {"center_x": 0.5}
                    on_release: root.navigate_to_data_collection()
                
                Label:
                    text: "资料采集"
                    font_size: dp(app.metrics.FONT_SIZE_SMALL)
                    color: app.theme.TEXT_PRIMARY
                    halign: "center"
                    valign: "middle"
                    text_size: self.size
            
            # 查询管理
            BoxLayout:
                orientation: "vertical"
                canvas.before:
                    Color:
                        rgba: app.theme.CARD_BACKGROUND
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [dp(5)]
                
                MDIconButton:
                    icon: "magnify"
                    theme_text_color: "Custom"
                    text_color: app.theme.PRIMARY_COLOR
                    user_font_size: dp(24)
                    pos_hint: {"center_x": 0.5}
                    on_release: root.navigate_to_query_management()
                
                Label:
                    text: "查询管理"
                    font_size: dp(app.metrics.FONT_SIZE_SMALL)
                    color: app.theme.TEXT_PRIMARY
                    halign: "center"
                    valign: "middle"
                    text_size: self.size
            
            # 运行分析
            BoxLayout:
                orientation: "vertical"
                canvas.before:
                    Color:
                        rgba: app.theme.CARD_BACKGROUND
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [dp(5)]
                
                MDIconButton:
                    icon: "chart-line"
                    theme_text_color: "Custom"
                    text_color: app.theme.PRIMARY_COLOR
                    user_font_size: dp(24)
                    pos_hint: {"center_x": 0.5}
                    on_release: root.navigate_to_running_analysis()
                
                Label:
                    text: "运行分析"
                    font_size: dp(app.metrics.FONT_SIZE_SMALL)
                    color: app.theme.TEXT_PRIMARY
                    halign: "center"
                    valign: "middle"
                    text_size: self.size
            
            # 平台维护
            BoxLayout:
                orientation: "vertical"
                canvas.before:
                    Color:
                        rgba: app.theme.CARD_BACKGROUND
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [dp(5)]
                
                MDIconButton:
                    icon: "shield-check"
                    theme_text_color: "Custom"
                    text_color: app.theme.PRIMARY_COLOR
                    user_font_size: dp(24)
                    pos_hint: {"center_x": 0.5}
                    on_release: root.navigate_to_platform_protection()
                
                Label:
                    text: "平台维护"
                    font_size: dp(app.metrics.FONT_SIZE_SMALL)
                    color: app.theme.TEXT_PRIMARY
                    halign: "center"
                    valign: "middle"
                    text_size: self.size
            
            # 数据统计
            BoxLayout:
                orientation: "vertical"
                canvas.before:
                    Color:
                        rgba: app.theme.CARD_BACKGROUND
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [dp(5)]
                
                MDIconButton:
                    icon: "chart-bar"
                    theme_text_color: "Custom"
                    text_color: app.theme.PRIMARY_COLOR
                    user_font_size: dp(24)
                    pos_hint: {"center_x": 0.5}
                    on_release: root.navigate_to_data_statistics()
                
                Label:
                    text: "数据统计"
                    font_size: dp(app.metrics.FONT_SIZE_SMALL)
                    color: app.theme.TEXT_PRIMARY
                    halign: "center"
                    valign: "middle"
                    text_size: self.size
            
            # 系统设置
            BoxLayout:
                orientation: "vertical"
                canvas.before:
                    Color:
                        rgba: app.theme.CARD_BACKGROUND
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [dp(5)]
                
                MDIconButton:
                    icon: "cog"
                    theme_text_color: "Custom"
                    text_color: app.theme.PRIMARY_COLOR
                    user_font_size: dp(24)
                    pos_hint: {"center_x": 0.5}
                    on_release: root.navigate_to_system_settings()
                
                Label:
                    text: "系统设置"
                    font_size: dp(app.metrics.FONT_SIZE_SMALL)
                    color: app.theme.TEXT_PRIMARY
                    halign: "center"
                    valign: "middle"
                    text_size: self.size
        
        # 内容区域 - 分为左右两栏
        BoxLayout:
            orientation: "horizontal"
            spacing: dp(10)
            
            # 左侧 - 今日概览
            BoxLayout:
                orientation: "vertical"
                size_hint_x: 0.6
                canvas.before:
                    Color:
                        rgba: app.theme.CARD_BACKGROUND
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [dp(5)]
                padding: dp(10)
                spacing: dp(5)
                
                # 标题
                Label:
                    text: "今日概览"
                    font_size: dp(app.metrics.FONT_SIZE_MEDIUM)
                    color: app.theme.TEXT_PRIMARY
                    bold: True
                    size_hint_y: None
                    height: dp(30)
                    halign: "left"
                    valign: "middle"
                    text_size: self.size
                
                # 活跃用户
                BoxLayout:
                    orientation: "horizontal"
                    size_hint_y: None
                    height: dp(50)
                    padding: dp(5)
                    canvas.before:
                        Color:
                            rgba: app.theme.BACKGROUND_COLOR
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [dp(5)]
                    
                    # 图标
                    MDIconButton:
                        icon: "account-multiple"
                        theme_text_color: "Custom"
                        text_color: app.theme.PRIMARY_COLOR
                        user_font_size: dp(24)
                        size_hint_x: None
                        width: dp(40)
                    
                    # 文本信息
                    BoxLayout:
                        orientation: "vertical"
                        padding: dp(5), 0
                        
                        Label:
                            text: "活跃用户"
                            font_size: dp(app.metrics.FONT_SIZE_SMALL)
                            color: app.theme.TEXT_SECONDARY
                            halign: "left"
                            valign: "bottom"
                            text_size: self.size
                        
                        Label:
                            text: str(root.active_users)
                            font_size: dp(app.metrics.FONT_SIZE_MEDIUM)
                            color: app.theme.TEXT_PRIMARY
                            bold: True
                            halign: "left"
                            valign: "top"
                            text_size: self.size
                    
                    # 变化百分比
                    Label:
                        text: "+" + str(root.active_users_change) + "%" if root.active_users_change >= 0 else str(root.active_users_change) + "%"
                        font_size: dp(app.metrics.FONT_SIZE_SMALL)
                        color: (0, 0.7, 0, 1) if root.active_users_change >= 0 else (0.7, 0, 0, 1)
                        size_hint_x: None
                        width: dp(50)
                        halign: "right"
                        valign: "middle"
                        text_size: self.size
                
                # 新增注册
                BoxLayout:
                    orientation: "horizontal"
                    size_hint_y: None
                    height: dp(50)
                    padding: dp(5)
                    canvas.before:
                        Color:
                            rgba: app.theme.BACKGROUND_COLOR
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [dp(5)]
                    
                    # 图标
                    MDIconButton:
                        icon: "account-plus"
                        theme_text_color: "Custom"
                        text_color: app.theme.PRIMARY_COLOR
                        user_font_size: dp(24)
                        size_hint_x: None
                        width: dp(40)
                    
                    # 文本信息
                    BoxLayout:
                        orientation: "vertical"
                        padding: dp(5), 0
                        
                        Label:
                            text: "新增注册"
                            font_size: dp(app.metrics.FONT_SIZE_SMALL)
                            color: app.theme.TEXT_SECONDARY
                            halign: "left"
                            valign: "bottom"
                            text_size: self.size
                        
                        Label:
                            text: str(root.new_registrations)
                            font_size: dp(app.metrics.FONT_SIZE_MEDIUM)
                            color: app.theme.TEXT_PRIMARY
                            bold: True
                            halign: "left"
                            valign: "top"
                            text_size: self.size
                    
                    # 变化百分比
                    Label:
                        text: "+" + str(root.new_registrations_change) + "%" if root.new_registrations_change >= 0 else str(root.new_registrations_change) + "%"
                        font_size: dp(app.metrics.FONT_SIZE_SMALL)
                        color: (0, 0.7, 0, 1) if root.new_registrations_change >= 0 else (0.7, 0, 0, 1)
                        size_hint_x: None
                        width: dp(50)
                        halign: "right"
                        valign: "middle"
                        text_size: self.size
                
                # 待处理任务
                BoxLayout:
                    orientation: "horizontal"
                    size_hint_y: None
                    height: dp(50)
                    padding: dp(5)
                    canvas.before:
                        Color:
                            rgba: app.theme.BACKGROUND_COLOR
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [dp(5)]
                    
                    # 图标
                    MDIconButton:
                        icon: "clipboard-text"
                        theme_text_color: "Custom"
                        text_color: app.theme.PRIMARY_COLOR
                        user_font_size: dp(24)
                        size_hint_x: None
                        width: dp(40)
                    
                    # 文本信息
                    BoxLayout:
                        orientation: "vertical"
                        padding: dp(5), 0
                        
                        Label:
                            text: "待处理任务"
                            font_size: dp(app.metrics.FONT_SIZE_SMALL)
                            color: app.theme.TEXT_SECONDARY
                            halign: "left"
                            valign: "bottom"
                            text_size: self.size
                        
                        Label:
                            text: str(root.waiting_tasks)
                            font_size: dp(app.metrics.FONT_SIZE_MEDIUM)
                            color: app.theme.TEXT_PRIMARY
                            bold: True
                            halign: "left"
                            valign: "top"
                            text_size: self.size
                    
                    # 变化百分比
                    Label:
                        text: "+" + str(root.waiting_tasks_change) + "%" if root.waiting_tasks_change >= 0 else str(root.waiting_tasks_change) + "%"
                        font_size: dp(app.metrics.FONT_SIZE_SMALL)
                        color: (0, 0.7, 0, 1) if root.waiting_tasks_change >= 0 else (0.7, 0, 0, 1)
                        size_hint_x: None
                        width: dp(50)
                        halign: "right"
                        valign: "middle"
                        text_size: self.size
                
                # 系统状态
                BoxLayout:
                    orientation: "horizontal"
                    size_hint_y: None
                    height: dp(50)
                    padding: dp(5)
                    canvas.before:
                        Color:
                            rgba: app.theme.BACKGROUND_COLOR
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [dp(5)]
                    
                    # 图标
                    MDIconButton:
                        icon: "server"
                        theme_text_color: "Custom"
                        text_color: app.theme.PRIMARY_COLOR
                        user_font_size: dp(24)
                        size_hint_x: None
                        width: dp(40)
                    
                    # 文本信息
                    BoxLayout:
                        orientation: "vertical"
                        padding: dp(5), 0
                        
                        Label:
                            text: "系统状态"
                            font_size: dp(app.metrics.FONT_SIZE_SMALL)
                            color: app.theme.TEXT_SECONDARY
                            halign: "left"
                            valign: "bottom"
                            text_size: self.size
                        
                        Label:
                            text: root.system_status
                            font_size: dp(app.metrics.FONT_SIZE_MEDIUM)
                            color: app.theme.TEXT_PRIMARY
                            bold: True
                            halign: "left"
                            valign: "top"
                            text_size: self.size
                    
                    # 系统正常运行时间
                    Label:
                        text: str(root.system_uptime) + "%"
                        font_size: dp(app.metrics.FONT_SIZE_SMALL)
                        color: (0, 0.7, 0, 1)
                        size_hint_x: None
                        width: dp(50)
                        halign: "right"
                        valign: "middle"
                        text_size: self.size
            
            # 右侧 - 最近活动
            BoxLayout:
                orientation: "vertical"
                size_hint_x: 0.4
                canvas.before:
                    Color:
                        rgba: app.theme.CARD_BACKGROUND
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [dp(5)]
                padding: dp(10)
                spacing: dp(5)
                
                # 标题
                Label:
                    text: "最近活动"
                    font_size: dp(app.metrics.FONT_SIZE_MEDIUM)
                    color: app.theme.TEXT_PRIMARY
                    bold: True
                    size_hint_y: None
                    height: dp(30)
                    halign: "left"
                    valign: "middle"
                    text_size: self.size
                
                # 活动列表
                ScrollView:
                    do_scroll_x: False
                    do_scroll_y: True
                    
                    BoxLayout:
                        orientation: "vertical"
                        size_hint_y: None
                        height: self.minimum_height
                        spacing: dp(5)
                        id: activities_container
                        
                        # 动态生成活动项
                        # 这里使用Python代码在on_enter中动态添加
""")