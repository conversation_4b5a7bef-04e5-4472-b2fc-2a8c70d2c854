#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合测试评估模板数据的完整性
检查数据库、后端API和前端显示的一致性
"""

import sqlite3
import json
import sys
import os
from datetime import datetime

def check_database_templates():
    """检查数据库中的模板数据"""
    print("=== 检查数据库中的模板数据 ===")
    
    db_paths = [
        'YUN/backend/app.db',
        'app.db'
    ]
    
    db_path = None
    for path in db_paths:
        if os.path.exists(path):
            db_path = path
            break
    
    if not db_path:
        print("未找到数据库文件")
        return None
    
    print(f"使用数据库: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 检查assessment_templates表结构
        print("\n1. assessment_templates表结构:")
        cursor.execute("PRAGMA table_info(assessment_templates)")
        columns = cursor.fetchall()
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
        
        # 2. 检查模板数据
        print("\n2. 模板数据:")
        cursor.execute("""
            SELECT id, template_key, name, assessment_type, max_score
            FROM assessment_templates 
            ORDER BY id
        """)
        templates = cursor.fetchall()
        
        template_data = {}
        for template in templates:
            template_id, template_key, name, assessment_type, max_score = template
            print(f"  ID: {template_id}, Key: {template_key}, Name: {name}, Type: {assessment_type}, Max Score: {max_score}")
            template_data[template_id] = {
                'id': template_id,
                'template_key': template_key,
                'name': name,
                'assessment_type': assessment_type,
                'max_score': max_score
            }
        
        # 3. 检查assessment_template_questions表
        print("\n3. 检查问题表:")
        try:
            cursor.execute("PRAGMA table_info(assessment_template_questions)")
            question_columns = cursor.fetchall()
            print("  问题表结构:")
            for col in question_columns:
                print(f"    {col[1]} ({col[2]})")
            
            # 检查每个模板的问题数量
            for template_id in template_data.keys():
                cursor.execute("""
                    SELECT COUNT(*) FROM assessment_template_questions 
                    WHERE template_id = ?
                """, (template_id,))
                question_count = cursor.fetchone()[0]
                template_data[template_id]['question_count'] = question_count
                print(f"  模板 {template_id} 有 {question_count} 个问题")
                
                # 获取前几个问题的详细信息
                if question_count > 0:
                    cursor.execute("""
                        SELECT question_id, question_text, question_type, options
                        FROM assessment_template_questions 
                        WHERE template_id = ?
                        ORDER BY "order" LIMIT 3
                    """, (template_id,))
                    questions = cursor.fetchall()
                    
                    for i, (q_id, q_text, q_type, q_options) in enumerate(questions):
                        print(f"    问题{i+1}: ID={q_id}, 类型={q_type}")
                        print(f"      文本: {q_text[:50]}..." if len(q_text) > 50 else f"      文本: {q_text}")
                        if q_options:
                            try:
                                options = json.loads(q_options)
                                print(f"      选项数量: {len(options)}")
                            except:
                                print(f"      选项: {q_options[:30]}...")
        
        except sqlite3.OperationalError as e:
            print(f"  问题表不存在或访问失败: {e}")
        
        conn.close()
        return template_data
        
    except Exception as e:
        print(f"检查数据库失败: {e}")
        return None

def check_standard_templates():
    """检查标准模板定义"""
    print("\n=== 检查标准模板定义 ===")
    
    # 查找标准模板定义文件
    template_files = [
        'YUN/backend/app/data/assessment_templates.py',
        'YUN/backend/data/assessment_templates.py',
        'app/data/assessment_templates.py'
    ]
    
    for file_path in template_files:
        if os.path.exists(file_path):
            print(f"找到标准模板文件: {file_path}")
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 查找STANDARD_ASSESSMENT_TEMPLATES
                if 'STANDARD_ASSESSMENT_TEMPLATES' in content:
                    print("包含STANDARD_ASSESSMENT_TEMPLATES定义")
                    
                    # 尝试提取模板数量信息
                    lines = content.split('\n')
                    in_templates = False
                    template_count = 0
                    
                    for line in lines:
                        if 'STANDARD_ASSESSMENT_TEMPLATES' in line and '=' in line:
                            in_templates = True
                        elif in_templates and 'template_key' in line:
                            template_count += 1
                        elif in_templates and line.strip() == ']':
                            break
                    
                    print(f"估计包含 {template_count} 个标准模板")
                    
                    # 查找MMSE相关内容
                    if 'mmse' in content.lower() or 'MMSE' in content:
                        print("包含MMSE相关模板")
                    
                else:
                    print("未找到STANDARD_ASSESSMENT_TEMPLATES定义")
                    
            except Exception as e:
                print(f"读取模板文件失败: {e}")
            break
    else:
        print("未找到标准模板定义文件")

def check_sm008_data():
    """检查用户SM_008的数据"""
    print("\n=== 检查用户SM_008的数据 ===")
    
    db_paths = [
        'YUN/backend/app.db',
        'app.db'
    ]
    
    db_path = None
    for path in db_paths:
        if os.path.exists(path):
            db_path = path
            break
    
    if not db_path:
        print("未找到数据库文件")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 检查用户信息
        print("\n1. 用户SM_008信息:")
        cursor.execute("SELECT id, custom_id, username FROM users WHERE custom_id = 'SM_008'")
        user = cursor.fetchone()
        if user:
            user_id, custom_id, username = user
            print(f"  用户ID: {user_id}, 自定义ID: {custom_id}, 用户名: {username}")
        else:
            print("  未找到用户SM_008")
            conn.close()
            return
        
        # 2. 检查评估分发记录
        print("\n2. 评估分发记录:")
        cursor.execute("""
            SELECT id, template_id, status, created_at
            FROM assessment_distributions 
            WHERE custom_id = 'SM_008'
            ORDER BY created_at DESC
        """)
        distributions = cursor.fetchall()
        
        print(f"  找到 {len(distributions)} 条分发记录")
        for dist in distributions:
            dist_id, template_id, status, created_at = dist
            print(f"    分发ID: {dist_id}, 模板ID: {template_id}, 状态: {status}, 时间: {created_at}")
        
        # 3. 检查评估回答记录
        print("\n3. 评估回答记录:")
        cursor.execute("""
            SELECT id, assessment_id, answers, created_at
            FROM assessment_responses 
            WHERE custom_id = 'SM_008'
            ORDER BY created_at DESC
        """)
        responses = cursor.fetchall()
        
        print(f"  找到 {len(responses)} 条回答记录")
        for resp in responses:
            resp_id, assessment_id, answers, created_at = resp
            print(f"    回答ID: {resp_id}, 评估ID: {assessment_id}, 时间: {created_at}")
            
            # 分析回答数据
            if answers:
                try:
                    answer_data = json.loads(answers)
                    if isinstance(answer_data, list):
                        print(f"      回答数量: {len(answer_data)}")
                        if answer_data:
                            first_answer = answer_data[0]
                            print(f"      第一个回答字段: {list(first_answer.keys())}")
                            if 'question_id' in first_answer:
                                print(f"      第一个问题ID: {first_answer['question_id']}")
                    else:
                        print(f"      回答数据类型: {type(answer_data)}")
                except Exception as e:
                    print(f"      解析回答数据失败: {e}")
                    print(f"      原始数据: {answers[:100]}...")
        
        # 4. 检查评估结果记录
        print("\n4. 评估结果记录:")
        cursor.execute("""
            SELECT id, assessment_id, total_score, report, created_at
            FROM assessment_results 
            WHERE custom_id = 'SM_008'
            ORDER BY created_at DESC
        """)
        results = cursor.fetchall()
        
        print(f"  找到 {len(results)} 条结果记录")
        for result in results:
            result_id, assessment_id, total_score, report, created_at = result
            print(f"    结果ID: {result_id}, 评估ID: {assessment_id}, 总分: {total_score}, 时间: {created_at}")
            if report:
                print(f"      报告长度: {len(report)} 字符")
        
        conn.close()
        
    except Exception as e:
        print(f"检查SM_008数据失败: {e}")

def analyze_template_api_issue():
    """分析模板API问题"""
    print("\n=== 分析模板API问题 ===")
    
    print("\n可能的问题原因:")
    print("1. 前端调用的模板ID与数据库中的不匹配")
    print("2. 后端API返回的数据结构不包含questions字段")
    print("3. 标准模板和数据库模板的数据不同步")
    print("4. 前端处理模板数据时丢失了问题信息")
    
    print("\n建议的解决方案:")
    print("1. 检查前端调用的模板ID是否正确")
    print("2. 验证后端API是否正确返回questions字段")
    print("3. 确保标准模板数据与数据库同步")
    print("4. 在前端添加调试日志，跟踪模板数据获取过程")

def main():
    """主函数"""
    print("=== 综合模板测试 ===")
    print(f"测试时间: {datetime.now()}")
    print(f"当前目录: {os.getcwd()}")
    
    # 1. 检查数据库模板
    template_data = check_database_templates()
    
    # 2. 检查标准模板定义
    check_standard_templates()
    
    # 3. 检查SM_008数据
    check_sm008_data()
    
    # 4. 分析问题
    analyze_template_api_issue()
    
    # 5. 生成测试报告
    test_report = {
        'timestamp': datetime.now().isoformat(),
        'database_templates': template_data,
        'test_summary': {
            'database_accessible': template_data is not None,
            'template_count': len(template_data) if template_data else 0
        }
    }
    
    with open('comprehensive_template_test_report.json', 'w', encoding='utf-8') as f:
        json.dump(test_report, f, ensure_ascii=False, indent=2)
    
    print(f"\n测试报告已保存到: comprehensive_template_test_report.json")
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()