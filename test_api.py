import requests
import json

# 测试移动端登录
def test_mobile_login():
    url = "http://localhost:8006/api/auth/register/login"
    data = {
        "username": "admin",
        "password": "markey0308@163"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"登录响应状态码: {response.status_code}")
        print(f"登录响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('status') == 'success':
                token = result.get('access_token')
                print(f"登录成功，token: {token[:20]}...")
                return token
        return None
    except Exception as e:
        print(f"登录失败: {e}")
        return None

# 测试API端点
def test_api_endpoint(token):
    url = "http://localhost:8006/api/mobile/pending-assessments"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers)
        print(f"API响应状态码: {response.status_code}")
        print(f"API响应内容: {response.text}")
    except Exception as e:
        print(f"API请求失败: {e}")

if __name__ == "__main__":
    print("测试移动端API...")
    token = test_mobile_login()
    if token:
        test_api_endpoint(token)
    else:
        print("登录失败，无法测试API端点")