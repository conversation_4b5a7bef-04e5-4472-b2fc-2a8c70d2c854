# -*- coding: utf-8 -*-
"""
健康管理系统统一部署管理器
统一管理项目的构建、部署、环境配置和版本管理

版本: 1.0
作者: Health Management System
创建时间: 2024-12-30
"""

import os
import sys
import json
import asyncio
import subprocess
import shutil
import zipfile
import tarfile
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from enum import Enum

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入其他管理器
try:
    from config_manager import ConfigManager, ConfigType, Environment
    from service_manager import ServiceManager
except ImportError:
    print("警告：无法导入管理器模块，使用默认配置")
    class ConfigManager:
        def get_config(self, config_type): return {}
    class ServiceManager:
        def __init__(self): pass
    class Environment(Enum):
        DEVELOPMENT = "development"
        TESTING = "testing"
        PRODUCTION = "production"

# 导入后端核心模块
try:
    from backend.app.core.logging_utils import get_logger
except ImportError:
    import logging
    def get_logger(name):
        return logging.getLogger(name)

logger = get_logger(__name__)

class DeploymentType(Enum):
    """部署类型"""
    LOCAL = "local"
    DOCKER = "docker"
    KUBERNETES = "kubernetes"
    CLOUD = "cloud"
    STANDALONE = "standalone"

class BuildStatus(Enum):
    """构建状态"""
    PENDING = "pending"
    BUILDING = "building"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"

class DeploymentStatus(Enum):
    """部署状态"""
    PENDING = "pending"
    DEPLOYING = "deploying"
    SUCCESS = "success"
    FAILED = "failed"
    ROLLBACK = "rollback"

@dataclass
class BuildConfig:
    """构建配置"""
    name: str
    source_dir: Path
    output_dir: Path
    build_command: str
    environment: Environment
    dependencies: List[str] = field(default_factory=list)
    env_vars: Dict[str, str] = field(default_factory=dict)
    pre_build_commands: List[str] = field(default_factory=list)
    post_build_commands: List[str] = field(default_factory=list)
    exclude_patterns: List[str] = field(default_factory=list)

@dataclass
class DeploymentConfig:
    """部署配置"""
    name: str
    type: DeploymentType
    environment: Environment
    target_dir: Path
    backup_dir: Optional[Path] = None
    pre_deploy_commands: List[str] = field(default_factory=list)
    post_deploy_commands: List[str] = field(default_factory=list)
    rollback_commands: List[str] = field(default_factory=list)
    health_check_url: Optional[str] = None
    timeout: int = 300

@dataclass
class BuildResult:
    """构建结果"""
    config: BuildConfig
    status: BuildStatus
    start_time: datetime
    end_time: Optional[datetime] = None
    duration: Optional[float] = None
    output_path: Optional[Path] = None
    error_message: Optional[str] = None
    build_log: List[str] = field(default_factory=list)
    artifacts: List[Path] = field(default_factory=list)

@dataclass
class DeploymentResult:
    """部署结果"""
    config: DeploymentConfig
    status: DeploymentStatus
    start_time: datetime
    end_time: Optional[datetime] = None
    duration: Optional[float] = None
    version: Optional[str] = None
    backup_path: Optional[Path] = None
    error_message: Optional[str] = None
    deploy_log: List[str] = field(default_factory=list)

class DeploymentManager:
    """部署管理器"""
    
    def __init__(self):
        """初始化部署管理器"""
        self.project_root = project_root
        self.backend_dir = self.project_root / "backend"
        self.frontend_dir = self.project_root / "frontend"
        self.build_dir = self.project_root / "build"
        self.dist_dir = self.project_root / "dist"
        self.backup_dir = self.project_root / "backups"
        
        # 创建必要的目录
        for directory in [self.build_dir, self.dist_dir, self.backup_dir]:
            directory.mkdir(exist_ok=True)
        
        self.config_manager = ConfigManager()
        self.service_manager = ServiceManager()
        self.build_configs = {}
        self.deployment_configs = {}
        self.build_history = []
        self.deployment_history = []
        
        logger.info(f"部署管理器初始化完成，项目根目录: {self.project_root}")
        
        # 初始化配置
        self._initialize_configs()
    
    def _initialize_configs(self):
        """初始化构建和部署配置"""
        try:
            # 后端构建配置
            backend_build = BuildConfig(
                name="backend",
                source_dir=self.backend_dir,
                output_dir=self.build_dir / "backend",
                build_command="pip install -r requirements.txt",
                environment=Environment.DEVELOPMENT,
                pre_build_commands=[
                    "python -m pip install --upgrade pip",
                    "pip install wheel setuptools"
                ],
                post_build_commands=[
                    "python -m compileall .",
                    "python -c \"import app.main; print('Backend build verification passed')\""
                ],
                exclude_patterns=[
                    "__pycache__",
                    "*.pyc",
                    "*.pyo",
                    ".pytest_cache",
                    "tests",
                    ".env"
                ]
            )
            
            self.build_configs["backend"] = backend_build
            
            # 前端构建配置
            frontend_build = BuildConfig(
                name="frontend",
                source_dir=self.frontend_dir,
                output_dir=self.build_dir / "frontend",
                build_command="npm run build",
                environment=Environment.DEVELOPMENT,
                pre_build_commands=[
                    "npm install",
                    "npm audit fix --force"
                ],
                post_build_commands=[
                    "npm run lint"
                ],
                exclude_patterns=[
                    "node_modules",
                    ".git",
                    "*.log",
                    ".env.local"
                ]
            )
            
            self.build_configs["frontend"] = frontend_build
            
            # 本地部署配置
            local_deployment = DeploymentConfig(
                name="local",
                type=DeploymentType.LOCAL,
                environment=Environment.DEVELOPMENT,
                target_dir=self.dist_dir / "local",
                backup_dir=self.backup_dir / "local",
                health_check_url="http://localhost:8000/"
            )
            
            self.deployment_configs["local"] = local_deployment
            
            # Docker部署配置
            docker_deployment = DeploymentConfig(
                name="docker",
                type=DeploymentType.DOCKER,
                environment=Environment.TESTING,
                target_dir=self.dist_dir / "docker",
                backup_dir=self.backup_dir / "docker",
                pre_deploy_commands=[
                    "docker-compose down",
                    "docker system prune -f"
                ],
                post_deploy_commands=[
                    "docker-compose up -d",
                    "docker-compose ps"
                ],
                health_check_url="http://localhost:8000/"
            )
            
            self.deployment_configs["docker"] = docker_deployment
            
            logger.info(f"已初始化 {len(self.build_configs)} 个构建配置")
            logger.info(f"已初始化 {len(self.deployment_configs)} 个部署配置")
            
        except Exception as e:
            logger.error(f"初始化配置失败: {str(e)}")
    
    def _run_command(self, command: str, cwd: Path, env_vars: Dict[str, str] = None) -> tuple:
        """运行命令"""
        try:
            env = os.environ.copy()
            if env_vars:
                env.update(env_vars)
            
            logger.info(f"执行命令: {command}")
            logger.info(f"工作目录: {cwd}")
            
            process = subprocess.run(
                command,
                shell=True,
                cwd=cwd,
                env=env,
                capture_output=True,
                text=True,
                timeout=300
            )
            
            return process.returncode, process.stdout, process.stderr
            
        except subprocess.TimeoutExpired:
            return -1, "", "命令执行超时"
        except Exception as e:
            return -1, "", str(e)
    
    def _copy_files(self, source: Path, target: Path, exclude_patterns: List[str] = None) -> bool:
        """复制文件"""
        try:
            if target.exists():
                shutil.rmtree(target)
            
            def ignore_patterns(dir_path, names):
                ignored = set()
                if exclude_patterns:
                    for pattern in exclude_patterns:
                        for name in names:
                            if pattern in name or name.endswith(pattern.replace("*", "")):
                                ignored.add(name)
                return ignored
            
            shutil.copytree(source, target, ignore=ignore_patterns)
            logger.info(f"文件复制完成: {source} -> {target}")
            return True
            
        except Exception as e:
            logger.error(f"文件复制失败: {str(e)}")
            return False
    
    def _create_backup(self, source: Path, backup_dir: Path) -> Optional[Path]:
        """创建备份"""
        try:
            if not source.exists():
                return None
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"{source.name}_backup_{timestamp}"
            backup_path = backup_dir / backup_name
            
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            if source.is_file():
                shutil.copy2(source, backup_path)
            else:
                shutil.copytree(source, backup_path)
            
            logger.info(f"备份创建完成: {backup_path}")
            return backup_path
            
        except Exception as e:
            logger.error(f"创建备份失败: {str(e)}")
            return None
    
    def _create_archive(self, source: Path, output_path: Path, format: str = "zip") -> bool:
        """创建归档文件"""
        try:
            if format == "zip":
                with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    if source.is_file():
                        zipf.write(source, source.name)
                    else:
                        for file_path in source.rglob('*'):
                            if file_path.is_file():
                                arcname = file_path.relative_to(source)
                                zipf.write(file_path, arcname)
            
            elif format == "tar":
                with tarfile.open(output_path, 'w:gz') as tarf:
                    tarf.add(source, arcname=source.name)
            
            logger.info(f"归档文件创建完成: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"创建归档文件失败: {str(e)}")
            return False
    
    async def build_project(self, build_name: str) -> BuildResult:
        """构建项目"""
        if build_name not in self.build_configs:
            raise ValueError(f"构建配置不存在: {build_name}")
        
        config = self.build_configs[build_name]
        result = BuildResult(
            config=config,
            status=BuildStatus.BUILDING,
            start_time=datetime.now()
        )
        
        try:
            logger.info(f"开始构建项目: {build_name}")
            
            # 清理输出目录
            if config.output_dir.exists():
                shutil.rmtree(config.output_dir)
            config.output_dir.mkdir(parents=True, exist_ok=True)
            
            # 执行预构建命令
            for cmd in config.pre_build_commands:
                returncode, stdout, stderr = self._run_command(cmd, config.source_dir, config.env_vars)
                result.build_log.append(f"预构建命令: {cmd}")
                result.build_log.append(f"输出: {stdout}")
                
                if returncode != 0:
                    result.status = BuildStatus.FAILED
                    result.error_message = f"预构建命令失败: {stderr}"
                    result.build_log.append(f"错误: {stderr}")
                    return result
            
            # 执行主构建命令
            returncode, stdout, stderr = self._run_command(
                config.build_command, 
                config.source_dir, 
                config.env_vars
            )
            
            result.build_log.append(f"构建命令: {config.build_command}")
            result.build_log.append(f"输出: {stdout}")
            
            if returncode != 0:
                result.status = BuildStatus.FAILED
                result.error_message = f"构建命令失败: {stderr}"
                result.build_log.append(f"错误: {stderr}")
                return result
            
            # 复制源文件到输出目录
            if not self._copy_files(config.source_dir, config.output_dir, config.exclude_patterns):
                result.status = BuildStatus.FAILED
                result.error_message = "文件复制失败"
                return result
            
            # 执行后构建命令
            for cmd in config.post_build_commands:
                returncode, stdout, stderr = self._run_command(cmd, config.output_dir, config.env_vars)
                result.build_log.append(f"后构建命令: {cmd}")
                result.build_log.append(f"输出: {stdout}")
                
                if returncode != 0:
                    logger.warning(f"后构建命令失败: {stderr}")
                    result.build_log.append(f"警告: {stderr}")
            
            # 创建构建产物归档
            archive_path = self.build_dir / f"{build_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
            if self._create_archive(config.output_dir, archive_path):
                result.artifacts.append(archive_path)
            
            result.status = BuildStatus.SUCCESS
            result.output_path = config.output_dir
            logger.info(f"✅ 项目构建成功: {build_name}")
            
        except Exception as e:
            result.status = BuildStatus.FAILED
            result.error_message = str(e)
            logger.error(f"构建项目失败: {str(e)}")
        
        finally:
            result.end_time = datetime.now()
            result.duration = (result.end_time - result.start_time).total_seconds()
            self.build_history.append(result)
        
        return result
    
    async def deploy_project(self, deployment_name: str, build_result: BuildResult = None) -> DeploymentResult:
        """部署项目"""
        if deployment_name not in self.deployment_configs:
            raise ValueError(f"部署配置不存在: {deployment_name}")
        
        config = self.deployment_configs[deployment_name]
        result = DeploymentResult(
            config=config,
            status=DeploymentStatus.DEPLOYING,
            start_time=datetime.now(),
            version=datetime.now().strftime("%Y%m%d_%H%M%S")
        )
        
        try:
            logger.info(f"开始部署项目: {deployment_name}")
            
            # 创建备份
            if config.backup_dir and config.target_dir.exists():
                backup_path = self._create_backup(config.target_dir, config.backup_dir)
                result.backup_path = backup_path
            
            # 执行预部署命令
            for cmd in config.pre_deploy_commands:
                returncode, stdout, stderr = self._run_command(cmd, self.project_root)
                result.deploy_log.append(f"预部署命令: {cmd}")
                result.deploy_log.append(f"输出: {stdout}")
                
                if returncode != 0:
                    result.status = DeploymentStatus.FAILED
                    result.error_message = f"预部署命令失败: {stderr}"
                    result.deploy_log.append(f"错误: {stderr}")
                    return result
            
            # 部署文件
            if build_result and build_result.output_path:
                source_dir = build_result.output_path
            else:
                # 如果没有构建结果，使用源代码目录
                if deployment_name == "backend":
                    source_dir = self.backend_dir
                elif deployment_name == "frontend":
                    source_dir = self.frontend_dir
                else:
                    source_dir = self.project_root
            
            if not self._copy_files(source_dir, config.target_dir):
                result.status = DeploymentStatus.FAILED
                result.error_message = "部署文件复制失败"
                return result
            
            # 执行后部署命令
            for cmd in config.post_deploy_commands:
                returncode, stdout, stderr = self._run_command(cmd, config.target_dir)
                result.deploy_log.append(f"后部署命令: {cmd}")
                result.deploy_log.append(f"输出: {stdout}")
                
                if returncode != 0:
                    logger.warning(f"后部署命令失败: {stderr}")
                    result.deploy_log.append(f"警告: {stderr}")
            
            # 健康检查
            if config.health_check_url:
                await asyncio.sleep(5)  # 等待服务启动
                
                try:
                    import aiohttp
                    async with aiohttp.ClientSession() as session:
                        async with session.get(
                            config.health_check_url,
                            timeout=aiohttp.ClientTimeout(total=10)
                        ) as response:
                            if response.status == 200:
                                result.deploy_log.append("健康检查通过")
                            else:
                                result.deploy_log.append(f"健康检查失败: HTTP {response.status}")
                except Exception as e:
                    result.deploy_log.append(f"健康检查异常: {str(e)}")
            
            result.status = DeploymentStatus.SUCCESS
            logger.info(f"✅ 项目部署成功: {deployment_name}")
            
        except Exception as e:
            result.status = DeploymentStatus.FAILED
            result.error_message = str(e)
            logger.error(f"部署项目失败: {str(e)}")
        
        finally:
            result.end_time = datetime.now()
            result.duration = (result.end_time - result.start_time).total_seconds()
            self.deployment_history.append(result)
        
        return result
    
    async def build_and_deploy(self, build_name: str, deployment_name: str) -> tuple:
        """构建并部署"""
        logger.info(f"开始构建并部署: {build_name} -> {deployment_name}")
        
        # 构建项目
        build_result = await self.build_project(build_name)
        
        if build_result.status != BuildStatus.SUCCESS:
            logger.error(f"构建失败，取消部署")
            return build_result, None
        
        # 部署项目
        deploy_result = await self.deploy_project(deployment_name, build_result)
        
        return build_result, deploy_result
    
    def rollback_deployment(self, deployment_name: str) -> bool:
        """回滚部署"""
        try:
            if deployment_name not in self.deployment_configs:
                logger.error(f"部署配置不存在: {deployment_name}")
                return False
            
            config = self.deployment_configs[deployment_name]
            
            # 查找最近的备份
            if not config.backup_dir or not config.backup_dir.exists():
                logger.error("没有找到备份目录")
                return False
            
            backups = list(config.backup_dir.glob("*_backup_*"))
            if not backups:
                logger.error("没有找到备份文件")
                return False
            
            # 选择最新的备份
            latest_backup = max(backups, key=lambda x: x.stat().st_mtime)
            
            logger.info(f"开始回滚到备份: {latest_backup}")
            
            # 备份当前版本
            current_backup = self._create_backup(config.target_dir, config.backup_dir)
            
            # 恢复备份
            if config.target_dir.exists():
                shutil.rmtree(config.target_dir)
            
            if latest_backup.is_file():
                # 如果是归档文件，解压
                if latest_backup.suffix == '.zip':
                    with zipfile.ZipFile(latest_backup, 'r') as zipf:
                        zipf.extractall(config.target_dir)
                else:
                    shutil.copy2(latest_backup, config.target_dir)
            else:
                shutil.copytree(latest_backup, config.target_dir)
            
            # 执行回滚命令
            for cmd in config.rollback_commands:
                returncode, stdout, stderr = self._run_command(cmd, config.target_dir)
                if returncode != 0:
                    logger.warning(f"回滚命令失败: {stderr}")
            
            logger.info(f"✅ 回滚完成: {deployment_name}")
            return True
            
        except Exception as e:
            logger.error(f"回滚失败: {str(e)}")
            return False
    
    def get_build_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取构建历史"""
        history = []
        for result in self.build_history[-limit:]:
            history.append({
                'name': result.config.name,
                'status': result.status.value,
                'start_time': result.start_time.isoformat(),
                'end_time': result.end_time.isoformat() if result.end_time else None,
                'duration': result.duration,
                'error_message': result.error_message,
                'artifacts_count': len(result.artifacts)
            })
        return history
    
    def get_deployment_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取部署历史"""
        history = []
        for result in self.deployment_history[-limit:]:
            history.append({
                'name': result.config.name,
                'status': result.status.value,
                'start_time': result.start_time.isoformat(),
                'end_time': result.end_time.isoformat() if result.end_time else None,
                'duration': result.duration,
                'version': result.version,
                'error_message': result.error_message,
                'backup_path': str(result.backup_path) if result.backup_path else None
            })
        return history
    
    def cleanup_old_builds(self, days: int = 7):
        """清理旧的构建文件"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            for file_path in self.build_dir.rglob('*'):
                if file_path.is_file():
                    file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                    if file_time < cutoff_date:
                        file_path.unlink()
                        logger.info(f"删除旧构建文件: {file_path}")
            
            logger.info(f"清理完成，删除了 {days} 天前的构建文件")
            
        except Exception as e:
            logger.error(f"清理旧构建文件失败: {str(e)}")

# 主要功能函数
async def main():
    """主函数"""
    manager = DeploymentManager()
    
    try:
        print("\n" + "="*60)
        print("健康管理系统部署管理器")
        print("="*60)
        
        # 交互式菜单
        while True:
            print("\n" + "-"*40)
            print("请选择操作:")
            print("1. 构建后端")
            print("2. 构建前端")
            print("3. 部署到本地")
            print("4. 部署到Docker")
            print("5. 构建并部署后端")
            print("6. 构建并部署前端")
            print("7. 回滚部署")
            print("8. 查看构建历史")
            print("9. 查看部署历史")
            print("10. 清理旧构建")
            print("0. 退出")
            
            choice = input("\n请输入选择 (0-10): ").strip()
            
            if choice == '1':
                print("\n🔨 构建后端...")
                result = await manager.build_project('backend')
                print(f"结果: {result.status.value}")
                if result.error_message:
                    print(f"错误: {result.error_message}")
                
            elif choice == '2':
                print("\n🔨 构建前端...")
                result = await manager.build_project('frontend')
                print(f"结果: {result.status.value}")
                if result.error_message:
                    print(f"错误: {result.error_message}")
                
            elif choice == '3':
                print("\n🚀 部署到本地...")
                result = await manager.deploy_project('local')
                print(f"结果: {result.status.value}")
                if result.error_message:
                    print(f"错误: {result.error_message}")
                
            elif choice == '4':
                print("\n🐳 部署到Docker...")
                result = await manager.deploy_project('docker')
                print(f"结果: {result.status.value}")
                if result.error_message:
                    print(f"错误: {result.error_message}")
                
            elif choice == '5':
                print("\n🔨🚀 构建并部署后端...")
                build_result, deploy_result = await manager.build_and_deploy('backend', 'local')
                print(f"构建结果: {build_result.status.value}")
                if deploy_result:
                    print(f"部署结果: {deploy_result.status.value}")
                
            elif choice == '6':
                print("\n🔨🚀 构建并部署前端...")
                build_result, deploy_result = await manager.build_and_deploy('frontend', 'local')
                print(f"构建结果: {build_result.status.value}")
                if deploy_result:
                    print(f"部署结果: {deploy_result.status.value}")
                
            elif choice == '7':
                deployment_name = input("请输入要回滚的部署名称 (local/docker): ").strip()
                if deployment_name in manager.deployment_configs:
                    print(f"\n⏪ 回滚部署: {deployment_name}...")
                    success = manager.rollback_deployment(deployment_name)
                    print(f"结果: {'成功' if success else '失败'}")
                else:
                    print("❌ 无效的部署名称")
                
            elif choice == '8':
                print("\n📊 构建历史:")
                history = manager.get_build_history()
                if history:
                    for item in history:
                        status_icon = {
                            'success': '✅',
                            'failed': '❌',
                            'building': '🔨'
                        }.get(item['status'], '❓')
                        
                        print(f"  {status_icon} {item['name']}: {item['status']}")
                        print(f"    时间: {item['start_time'][:19]}")
                        if item['duration']:
                            print(f"    用时: {item['duration']:.1f}秒")
                        if item['error_message']:
                            print(f"    错误: {item['error_message'][:50]}...")
                else:
                    print("  暂无构建历史")
                
            elif choice == '9':
                print("\n📊 部署历史:")
                history = manager.get_deployment_history()
                if history:
                    for item in history:
                        status_icon = {
                            'success': '✅',
                            'failed': '❌',
                            'deploying': '🚀'
                        }.get(item['status'], '❓')
                        
                        print(f"  {status_icon} {item['name']}: {item['status']}")
                        print(f"    时间: {item['start_time'][:19]}")
                        print(f"    版本: {item['version']}")
                        if item['duration']:
                            print(f"    用时: {item['duration']:.1f}秒")
                        if item['error_message']:
                            print(f"    错误: {item['error_message'][:50]}...")
                else:
                    print("  暂无部署历史")
                
            elif choice == '10':
                days = input("请输入要清理多少天前的构建文件 (默认7天): ").strip()
                try:
                    days = int(days) if days else 7
                    print(f"\n🧹 清理 {days} 天前的构建文件...")
                    manager.cleanup_old_builds(days)
                    print("清理完成")
                except ValueError:
                    print("❌ 无效的天数")
                
            elif choice == '0':
                print("\n👋 退出部署管理器")
                break
                
            else:
                print("\n❌ 无效选择，请重新输入")
    
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 运行错误: {str(e)}")
        logger.error(f"主函数运行错误: {str(e)}")
    finally:
        print("\n🧹 清理完成")

if __name__ == "__main__":
    asyncio.run(main())