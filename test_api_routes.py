#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API路由修复
验证assessment_results的连字符和下划线路由是否都能正常工作
"""

import requests
import json

def test_api_routes():
    """测试API路由"""
    base_url = "http://localhost:8006/api"
    
    # 测试用的token（需要根据实际情况调整）
    headers = {
        "Authorization": "Bearer your_token_here",
        "Content-Type": "application/json"
    }
    
    print("=== 测试API路由修复 ===")
    
    # 测试路由列表
    test_routes = [
        # 连字符格式（原有的）
        "/assessment-results/4",
        "/assessment-results/user/SM_008", 
        "/questionnaire-results/user/SM_008",
        
        # 下划线格式（新增的兼容路由）
        "/assessment_results/4",
        "/assessment_results/user/SM_008",
        "/questionnaire_results/user/SM_008"
    ]
    
    for route in test_routes:
        url = base_url + route
        print(f"\n测试路由: {route}")
        
        try:
            # 不使用认证头，先测试路由是否存在
            response = requests.get(url, timeout=5)
            
            if response.status_code == 404:
                print(f"  ❌ 404 - 路由不存在")
            elif response.status_code == 401:
                print(f"  ✅ 401 - 路由存在但需要认证")
            elif response.status_code == 403:
                print(f"  ✅ 403 - 路由存在但权限不足")
            elif response.status_code == 200:
                print(f"  ✅ 200 - 路由正常工作")
            else:
                print(f"  ⚠️  {response.status_code} - 其他状态码")
                
        except requests.exceptions.ConnectionError:
            print(f"  ❌ 连接失败 - 服务器未启动")
            break
        except requests.exceptions.Timeout:
            print(f"  ❌ 请求超时")
        except Exception as e:
            print(f"  ❌ 请求异常: {str(e)}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_api_routes()