<template>
  <div class="test-management-dashboard">
    <!-- 页面标题和导航 -->
    <div class="dashboard-header">
      <div class="header-content">
        <h1>测试管理中心</h1>
        <p class="header-description">统一的测试管理、执行、分析和数据生成平台</p>
      </div>
      
      <div class="header-actions">
        <el-button type="primary" size="large" @click="runAllTests" :loading="isRunningAll">
          <el-icon><VideoPlay /></el-icon>
          运行全部测试
        </el-button>
        <el-button size="large" @click="showSettingsDialog = true">
          <el-icon><Setting /></el-icon>
          设置
        </el-button>
      </div>
    </div>

    <!-- 快速统计概览 -->
    <div class="stats-overview">
      <div class="stat-card">
        <div class="stat-icon success">
          <el-icon><SuccessFilled /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ overallStats.totalTests }}</div>
          <div class="stat-label">总测试数</div>
          <div class="stat-change positive">+{{ overallStats.newTests }} 新增</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon primary">
          <el-icon><Timer /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ overallStats.successRate.toFixed(1) }}%</div>
          <div class="stat-label">成功率</div>
          <div class="stat-change positive">+{{ overallStats.successRateChange.toFixed(1) }}%</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon warning">
          <el-icon><Clock /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ formatDuration(overallStats.avgExecutionTime) }}</div>
          <div class="stat-label">平均耗时</div>
          <div class="stat-change positive">{{ formatDuration(overallStats.timeChange) }}</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon info">
          <el-icon><PieChart /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ overallStats.coverage.toFixed(1) }}%</div>
          <div class="stat-label">代码覆盖率</div>
          <div class="stat-change positive">+{{ overallStats.coverageChange.toFixed(1) }}%</div>
        </div>
      </div>
    </div>

    <!-- 功能模块导航 -->
    <div class="module-navigation">
      <div class="nav-tabs">
        <div 
          v-for="tab in tabs" 
          :key="tab.key"
          class="nav-tab"
          :class="{ active: activeTab === tab.key }"
          @click="activeTab = tab.key"
        >
          <span class="tab-label">{{ tab.label }}</span>
          <el-badge v-if="tab.badge" :value="tab.badge" class="tab-badge" />
        </div>
      </div>
    </div>

    <!-- 模块内容区域 -->
    <div class="module-content">
      <!-- 测试管理 -->
      <div v-show="activeTab === 'management'" class="content-panel">
        <div class="panel-header">
          <h3>测试套件管理</h3>
          <el-button type="primary" @click="createTestSuite">创建测试套件</el-button>
        </div>
        <div class="test-suites-list">
          <div v-for="suite in testSuites" :key="suite.id" class="test-suite-item">
            <div class="suite-info">
              <h4>{{ suite.name }}</h4>
              <p>{{ suite.description }}</p>
              <div class="suite-stats">
                <span class="stat">测试数: {{ suite.testCount }}</span>
                <span class="stat">成功率: {{ suite.successRate.toFixed(1) }}%</span>
                <span class="stat">最后运行: {{ formatTime(suite.lastRun) }}</span>
              </div>
            </div>
            <div class="suite-actions">
              <el-button size="small" @click="runTestSuite(suite.id)">运行</el-button>
              <el-button size="small" type="info" @click="viewTestSuite(suite.id)">查看</el-button>
              <el-button size="small" type="warning" @click="editTestSuite(suite.id)">编辑</el-button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 自动化执行 -->
      <div v-show="activeTab === 'automation'" class="content-panel">
        <div class="panel-header">
          <h3>自动化测试执行</h3>
          <el-button type="success" @click="startAutomation">开始自动化</el-button>
        </div>
        <div class="automation-status">
          <div class="status-item">
            <span class="label">运行中的测试:</span>
            <span class="value">{{ monitoringData.runningTests }}</span>
          </div>
          <div class="status-item">
            <span class="label">队列中的任务:</span>
            <span class="value">{{ monitoringData.queuedTasks }}</span>
          </div>
          <div class="status-item">
            <span class="label">CPU使用率:</span>
            <span class="value">{{ monitoringData.cpuUsage }}%</span>
          </div>
          <div class="status-item">
            <span class="label">内存使用:</span>
            <span class="value">{{ monitoringData.memoryUsage }}MB</span>
          </div>
        </div>
      </div>
      
      <!-- 测试历史 -->
      <div v-show="activeTab === 'history'" class="content-panel">
        <div class="panel-header">
          <h3>测试历史记录</h3>
          <el-button @click="exportHistory">导出历史</el-button>
        </div>
        <div class="history-list">
          <div v-for="record in testHistory" :key="record.id" class="history-item">
            <div class="history-info">
              <h4>{{ record.suiteName }}</h4>
              <p>执行时间: {{ formatTime(record.executionTime) }}</p>
              <p>耗时: {{ formatDuration(record.duration) }}</p>
            </div>
            <div class="history-result" :class="record.status">
              {{ record.status === 'passed' ? '通过' : record.status === 'failed' ? '失败' : '跳过' }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 设置对话框 -->
    <el-dialog
      v-model="showSettingsDialog"
      title="测试设置"
      width="500px"
    >
      <el-form :model="settings" label-width="120px">
        <el-form-item label="刷新间隔">
          <el-input-number 
            v-model="settings.refreshInterval" 
            :min="5000" 
            :max="60000" 
            :step="5000"
            style="width: 100%;"
          />
        </el-form-item>
        
        <el-form-item label="默认超时">
          <el-input-number 
            v-model="settings.defaultTimeout" 
            :min="1000" 
            :max="300000" 
            :step="1000"
            style="width: 100%;"
          />
        </el-form-item>
        
        <el-form-item label="并发数">
          <el-input-number 
            v-model="settings.maxConcurrency" 
            :min="1" 
            :max="10" 
            style="width: 100%;"
          />
        </el-form-item>
        
        <el-form-item label="显示监控">
          <el-switch v-model="settings.showMonitoring" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showSettingsDialog = false">取消</el-button>
          <el-button type="primary" @click="saveSettings">保存设置</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 测试套件详情对话框 -->
    <el-dialog 
      v-model="showDetailDialog" 
      :title="`测试套件详情 - ${selectedTestSuite?.name || ''}`" 
      width="80%"
      top="5vh"
    >
      <div v-if="selectedTestSuite" class="test-suite-detail">
        <!-- 套件基本信息 -->
        <div class="suite-info">
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="info-item">
                <div class="info-label">套件名称</div>
                <div class="info-value">{{ selectedTestSuite.name }}</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <div class="info-label">测试总数</div>
                <div class="info-value">{{ selectedTestSuite.testCount }}</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <div class="info-label">成功率</div>
                <div class="info-value success-rate">{{ selectedTestSuite.successRate.toFixed(1) }}%</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <div class="info-label">最近运行</div>
                <div class="info-value">{{ formatTime(selectedTestSuite.lastRun) }}</div>
              </div>
            </el-col>
          </el-row>
          <div class="suite-description">
            <div class="info-label">套件描述</div>
            <div class="info-value">{{ selectedTestSuite.description }}</div>
          </div>
        </div>

        <!-- 测试用例统计 -->
        <div class="test-stats">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="stat-card passed">
                <div class="stat-number">{{ selectedTestSuite.testCases.filter(t => t.status === 'passed').length }}</div>
                <div class="stat-label">通过</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="stat-card failed">
                <div class="stat-number">{{ selectedTestSuite.testCases.filter(t => t.status === 'failed').length }}</div>
                <div class="stat-label">失败</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="stat-card total">
                <div class="stat-number">{{ selectedTestSuite.testCases.length }}</div>
                <div class="stat-label">总计</div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 测试用例列表 -->
        <div class="test-cases">
          <div class="section-title">测试用例详情</div>
          <el-table :data="selectedTestSuite.testCases" stripe style="width: 100%">
            <el-table-column prop="id" label="ID" width="60" />
            <el-table-column prop="name" label="测试用例名称" min-width="200" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag 
                  :type="scope.row.status === 'passed' ? 'success' : 'danger'"
                  size="small"
                >
                  {{ scope.row.status === 'passed' ? '通过' : '失败' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="duration" label="执行时间" width="120">
              <template #default="scope">
                {{ formatDuration(scope.row.duration) }}
              </template>
            </el-table-column>
            <el-table-column prop="error" label="错误信息" min-width="300">
              <template #default="scope">
                <span v-if="scope.row.error" class="error-message">
                  {{ scope.row.error }}
                </span>
                <span v-else class="success-message">-</span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 失败用例汇总 -->
        <div v-if="selectedTestSuite.testCases.filter(t => t.status === 'failed').length > 0" class="failed-summary">
          <div class="section-title">失败用例汇总</div>
          <div class="failed-cases">
            <div 
              v-for="failedCase in selectedTestSuite.testCases.filter(t => t.status === 'failed')"
              :key="failedCase.id"
              class="failed-case-item"
            >
              <div class="failed-case-name">
                <el-icon><WarningFilled /></el-icon>
                {{ failedCase.name }}
              </div>
              <div class="failed-case-error">{{ failedCase.error }}</div>
              <div class="failed-case-suggestion">
                <strong>建议解决方案：</strong>
                <span v-if="failedCase.error.includes('SSL')">
                  检查SSL证书配置，确保证书有效且正确安装
                </span>
                <span v-else-if="failedCase.error.includes('超时')">
                  优化查询性能，考虑添加索引或重构查询逻辑
                </span>
                <span v-else-if="failedCase.error.includes('清理')">
                  优化数据清理策略，考虑分批处理大量数据
                </span>
                <span v-else>
                  请检查相关配置和日志，联系技术支持团队
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showDetailDialog = false">关闭</el-button>
          <el-button type="primary" @click="runTestSuite(selectedTestSuite)">重新运行</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import {
  VideoPlay, Setting, SuccessFilled, Timer, Clock, PieChart, WarningFilled
} from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('management')
const isRunningAll = ref(false)
const showSettingsDialog = ref(false)
const showDetailDialog = ref(false)
const selectedTestSuite = ref(null)

// 统计数据
const overallStats = reactive({
  totalTests: 1247,
  newTests: 23,
  successRate: 94.2,
  successRateChange: 2.1,
  avgExecutionTime: 2340,
  timeChange: -120,
  coverage: 87.5,
  coverageChange: 1.8
})

// 监控数据
const monitoringData = reactive({
  runningTests: 3,
  queuedTasks: 7,
  cpuUsage: 45,
  memoryUsage: 512
})

// 测试套件数据
const testSuites = ref([
  {
    id: 1,
    name: '用户登录测试套件',
    description: '测试用户登录相关功能',
    testCount: 15,
    successRate: 96.7,
    lastRun: new Date(Date.now() - 2 * 60 * 60 * 1000),
    testCases: [
      { id: 1, name: '用户名密码登录', status: 'passed', duration: 1200, error: null },
      { id: 2, name: '错误密码登录', status: 'passed', duration: 800, error: null },
      { id: 3, name: '空用户名登录', status: 'passed', duration: 600, error: null },
      { id: 4, name: '空密码登录', status: 'passed', duration: 650, error: null },
      { id: 5, name: '记住密码功能', status: 'passed', duration: 900, error: null },
      { id: 6, name: '自动登录功能', status: 'passed', duration: 1100, error: null },
      { id: 7, name: '登录超时处理', status: 'passed', duration: 2000, error: null },
      { id: 8, name: '多设备登录检测', status: 'passed', duration: 1500, error: null },
      { id: 9, name: '密码强度验证', status: 'passed', duration: 700, error: null },
      { id: 10, name: '验证码验证', status: 'passed', duration: 1300, error: null },
      { id: 11, name: '账户锁定机制', status: 'passed', duration: 1800, error: null },
      { id: 12, name: '登录日志记录', status: 'passed', duration: 950, error: null },
      { id: 13, name: '第三方登录集成', status: 'passed', duration: 2200, error: null },
      { id: 14, name: '登录页面响应式', status: 'passed', duration: 800, error: null },
      { id: 15, name: '登录安全检查', status: 'failed', duration: 1600, error: 'SSL证书验证失败，连接不安全' }
    ]
  },
  {
    id: 2,
    name: 'API接口测试套件',
    description: '测试后端API接口',
    testCount: 32,
    successRate: 96.8,
    lastRun: new Date(Date.now() - 1 * 60 * 60 * 1000),
    testCases: [
      { id: 1, name: '用户认证API', status: 'passed', duration: 450, error: null },
      { id: 2, name: '用户信息获取API', status: 'passed', duration: 320, error: null },
      { id: 3, name: '健康数据上传API', status: 'passed', duration: 680, error: null },
      { id: 4, name: '问卷提交API', status: 'passed', duration: 520, error: null },
      { id: 5, name: '评估结果API', status: 'passed', duration: 750, error: null },
      { id: 6, name: '文件上传API', status: 'passed', duration: 1200, error: null },
      { id: 7, name: '数据导出API', status: 'passed', duration: 890, error: null },
      { id: 8, name: '统计数据API', status: 'passed', duration: 650, error: null },
      { id: 9, name: '权限验证API', status: 'passed', duration: 380, error: null },
      { id: 10, name: '数据分页API', status: 'passed', duration: 420, error: null },
      { id: 11, name: '搜索功能API', status: 'passed', duration: 580, error: null },
      { id: 12, name: '数据过滤API', status: 'passed', duration: 490, error: null },
      { id: 13, name: '批量操作API', status: 'passed', duration: 920, error: null },
      { id: 14, name: '数据同步API', status: 'passed', duration: 1100, error: null },
      { id: 15, name: '缓存机制API', status: 'passed', duration: 350, error: null },
      { id: 16, name: '错误处理API', status: 'passed', duration: 280, error: null },
      { id: 17, name: '日志记录API', status: 'passed', duration: 310, error: null },
      { id: 18, name: '性能监控API', status: 'passed', duration: 450, error: null },
      { id: 19, name: '健康检查API', status: 'passed', duration: 200, error: null },
      { id: 20, name: '版本控制API', status: 'passed', duration: 380, error: null },
      { id: 21, name: '配置管理API', status: 'passed', duration: 420, error: null },
      { id: 22, name: '通知服务API', status: 'passed', duration: 680, error: null },
      { id: 23, name: '邮件发送API', status: 'passed', duration: 1200, error: null },
      { id: 24, name: '短信发送API', status: 'passed', duration: 850, error: null },
      { id: 25, name: '推送通知API', status: 'passed', duration: 720, error: null },
      { id: 26, name: '数据备份API', status: 'passed', duration: 2100, error: null },
      { id: 27, name: '数据恢复API', status: 'passed', duration: 1800, error: null },
      { id: 28, name: '安全扫描API', status: 'passed', duration: 950, error: null },
      { id: 29, name: '负载测试API', status: 'passed', duration: 1500, error: null },
      { id: 30, name: '并发测试API', status: 'passed', duration: 1300, error: null },
      { id: 31, name: '压力测试API', status: 'passed', duration: 1600, error: null },
      { id: 32, name: '兼容性测试API', status: 'failed', duration: 2200, error: '响应时间超过3秒阈值，需要优化查询性能' }
    ]
  },
  {
    id: 3,
    name: '数据库操作测试',
    description: '测试数据库CRUD操作',
    testCount: 28,
    successRate: 98.1,
    lastRun: new Date(Date.now() - 6 * 60 * 60 * 1000),
    testCases: [
      { id: 1, name: '用户表创建操作', status: 'passed', duration: 120, error: null },
      { id: 2, name: '用户数据插入', status: 'passed', duration: 80, error: null },
      { id: 3, name: '用户数据查询', status: 'passed', duration: 45, error: null },
      { id: 4, name: '用户数据更新', status: 'passed', duration: 65, error: null },
      { id: 5, name: '用户数据删除', status: 'passed', duration: 55, error: null },
      { id: 6, name: '健康记录插入', status: 'passed', duration: 95, error: null },
      { id: 7, name: '健康记录查询', status: 'passed', duration: 70, error: null },
      { id: 8, name: '健康记录更新', status: 'passed', duration: 85, error: null },
      { id: 9, name: '问卷数据插入', status: 'passed', duration: 110, error: null },
      { id: 10, name: '问卷数据查询', status: 'passed', duration: 60, error: null },
      { id: 11, name: '评估结果插入', status: 'passed', duration: 90, error: null },
      { id: 12, name: '评估结果查询', status: 'passed', duration: 75, error: null },
      { id: 13, name: '数据关联查询', status: 'passed', duration: 150, error: null },
      { id: 14, name: '复杂条件查询', status: 'passed', duration: 180, error: null },
      { id: 15, name: '分页查询测试', status: 'passed', duration: 95, error: null },
      { id: 16, name: '排序查询测试', status: 'passed', duration: 105, error: null },
      { id: 17, name: '聚合查询测试', status: 'passed', duration: 130, error: null },
      { id: 18, name: '事务处理测试', status: 'passed', duration: 200, error: null },
      { id: 19, name: '并发写入测试', status: 'passed', duration: 250, error: null },
      { id: 20, name: '数据完整性检查', status: 'passed', duration: 180, error: null },
      { id: 21, name: '外键约束测试', status: 'passed', duration: 140, error: null },
      { id: 22, name: '索引性能测试', status: 'passed', duration: 160, error: null },
      { id: 23, name: '备份恢复测试', status: 'passed', duration: 800, error: null },
      { id: 24, name: '数据迁移测试', status: 'passed', duration: 600, error: null },
      { id: 25, name: '连接池测试', status: 'passed', duration: 120, error: null },
      { id: 26, name: '死锁检测测试', status: 'passed', duration: 300, error: null },
      { id: 27, name: '性能监控测试', status: 'passed', duration: 220, error: null },
      { id: 28, name: '数据清理测试', status: 'failed', duration: 450, error: '清理脚本执行超时，可能存在大量数据需要优化清理策略' }
    ]
  }
])

// 测试历史数据
const testHistory = ref([
  {
    id: 1,
    suiteName: '用户登录测试套件',
    executionTime: new Date(Date.now() - 1 * 60 * 60 * 1000),
    duration: 45000,
    status: 'passed'
  },
  {
    id: 2,
    suiteName: 'API接口测试套件',
    executionTime: new Date(Date.now() - 1 * 60 * 60 * 1000),
    duration: 65000,
    status: 'passed'
  },
  {
    id: 3,
    suiteName: '数据库操作测试',
    executionTime: new Date(Date.now() - 3 * 60 * 60 * 1000),
    duration: 32000,
    status: 'passed'
  }
])

// 设置
const settings = reactive({
  refreshInterval: 10000,
  defaultTimeout: 30000,
  maxConcurrency: 3,
  showMonitoring: false
})

// 标签页配置
const tabs = computed(() => [
  {
    key: 'management',
    label: '测试管理',
    badge: null
  },
  {
    key: 'automation',
    label: '自动化执行',
    badge: monitoringData.runningTests > 0 ? monitoringData.runningTests : null
  },
  {
    key: 'history',
    label: '历史记录',
    badge: null
  }
])

// 定时器
let refreshTimer = null

// 方法
const runAllTests = async () => {
  try {
    isRunningAll.value = true
    ElMessage.info('开始执行全部测试...')
    
    // 模拟执行全部测试
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    // 更新统计数据
    overallStats.totalTests += 5
    overallStats.newTests = 5
    overallStats.successRate = Math.min(100, overallStats.successRate + Math.random() * 2)
    
    ElNotification({
      title: '测试完成',
      message: '全部测试执行完成，成功率: ' + overallStats.successRate.toFixed(1) + '%',
      type: 'success'
    })
  } catch (error) {
    ElMessage.error('测试执行失败: ' + error.message)
  } finally {
    isRunningAll.value = false
  }
}

const createTestSuite = () => {
  ElMessage.info('创建测试套件功能开发中...')
}

const runTestSuite = async (id) => {
  const suite = testSuites.value.find(s => s.id === id)
  if (!suite) return
  
  ElMessage.info(`正在运行测试套件: ${suite.name}`)
  
  try {
    // 模拟测试执行
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 更新测试套件数据
    suite.lastRun = new Date()
    suite.successRate = Math.min(100, suite.successRate + Math.random() * 2 - 1)
    
    // 添加到历史记录
    testHistory.value.unshift({
      id: Date.now(),
      suiteName: suite.name,
      executionTime: new Date(),
      duration: Math.floor(Math.random() * 60000) + 30000,
      status: Math.random() > 0.1 ? 'passed' : 'failed'
    })
    
    ElNotification({
      title: '测试完成',
      message: `测试套件 "${suite.name}" 执行完成`,
      type: 'success'
    })
  } catch (error) {
    ElMessage.error(`测试套件执行失败: ${error.message}`)
  }
}

const viewTestSuite = (id) => {
  const suite = testSuites.value.find(s => s.id === id)
  if (!suite) return
  
  selectedTestSuite.value = suite
  showDetailDialog.value = true
}

const editTestSuite = (id) => {
  const suite = testSuites.value.find(s => s.id === id)
  if (!suite) return
  
  ElMessage.info(`编辑测试套件: ${suite.name} (功能开发中...)`)
  // 这里可以打开编辑对话框或跳转到编辑页面
}

const startAutomation = () => {
  ElMessage.info('开始自动化测试...')
  monitoringData.runningTests += 1
  monitoringData.queuedTasks += 3
}

const exportHistory = () => {
  ElMessage.info('导出历史记录功能开发中...')
}

const saveSettings = () => {
  localStorage.setItem('testManagementSettings', JSON.stringify(settings))
  showSettingsDialog.value = false
  ElMessage.success('设置已保存')
  setupRefreshTimer()
}

const loadSettings = () => {
  const savedSettings = localStorage.getItem('testManagementSettings')
  if (savedSettings) {
    Object.assign(settings, JSON.parse(savedSettings))
  }
}

const setupRefreshTimer = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
  
  refreshTimer = setInterval(() => {
    refreshData()
  }, settings.refreshInterval)
}

const refreshData = () => {
  // 模拟数据刷新
  monitoringData.cpuUsage = Math.floor(Math.random() * 100)
  monitoringData.memoryUsage = Math.floor(Math.random() * 1024)
  
  // 随机更新运行中的测试数量
  if (Math.random() > 0.7) {
    monitoringData.runningTests = Math.floor(Math.random() * 5)
    monitoringData.queuedTasks = Math.floor(Math.random() * 10)
  }
}

// 工具函数
const formatDuration = (ms) => {
  if (!ms) return '0s'
  
  const seconds = Math.floor(Math.abs(ms) / 1000)
  const minutes = Math.floor(seconds / 60)
  
  if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`
  } else {
    return `${seconds}s`
  }
}

const formatTime = (date) => {
  return new Date(date).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 生命周期
onMounted(() => {
  loadSettings()
  setupRefreshTimer()
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style scoped>
.test-management-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header-content h1 {
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-description {
  margin: 0;
  color: #606266;
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 24px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
}

.stat-icon.success {
  background: linear-gradient(135deg, #67c23a, #85ce61);
  color: white;
}

.stat-icon.primary {
  background: linear-gradient(135deg, #409eff, #66b1ff);
  color: white;
}

.stat-icon.warning {
  background: linear-gradient(135deg, #e6a23c, #ebb563);
  color: white;
}

.stat-icon.info {
  background: linear-gradient(135deg, #909399, #a6a9ad);
  color: white;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.stat-change {
  font-size: 12px;
  font-weight: 500;
}

.stat-change.positive {
  color: #67c23a;
}

.stat-change.negative {
  color: #f56c6c;
}

.module-navigation {
  margin-bottom: 24px;
}

.nav-tabs {
  display: flex;
  background: white;
  border-radius: 12px;
  padding: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow-x: auto;
}

.nav-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  position: relative;
}

.nav-tab:hover {
  background: #f5f7fa;
}

.nav-tab.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.tab-label {
  font-size: 14px;
  font-weight: 500;
}

.tab-badge {
  margin-left: 4px;
}

.module-content {
  margin-bottom: 24px;
}

.content-panel {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  padding: 24px;
  min-height: 400px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.panel-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.test-suites-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.test-suite-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.test-suite-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.suite-info h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.suite-info p {
  margin: 0 0 8px 0;
  color: #606266;
  font-size: 14px;
}

.suite-stats {
  display: flex;
  gap: 16px;
}

.suite-stats .stat {
  font-size: 12px;
  color: #909399;
}

.suite-actions {
  display: flex;
  gap: 8px;
}

.automation-status {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 8px;
}

.status-item .label {
  font-size: 14px;
  color: #606266;
}

.status-item .value {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
}

.history-info h4 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.history-info p {
  margin: 0;
  color: #909399;
  font-size: 12px;
}

.history-result {
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.history-result.passed {
  background: #f0f9ff;
  color: #67c23a;
}

.history-result.failed {
  background: #fef0f0;
  color: #f56c6c;
}

.history-result.skipped {
  background: #fdf6ec;
  color: #e6a23c;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 测试套件详情对话框样式 */
.test-suite-detail {
  padding: 0;
}

.suite-info {
  margin-bottom: 24px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.info-item {
  text-align: center;
}

.info-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.info-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.success-rate {
  color: #67c23a;
}

.suite-description {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #eee;
}

.suite-description .info-label {
  text-align: left;
  margin-bottom: 8px;
}

.suite-description .info-value {
  text-align: left;
  font-weight: normal;
  color: #666;
}

.test-stats {
  margin-bottom: 24px;
}

.test-stats .stat-card {
  text-align: center;
  padding: 16px;
  border-radius: 8px;
  background: white;
  border: 2px solid #eee;
}

.test-stats .stat-card.passed {
  border-color: #67c23a;
  background: #f0f9ff;
}

.test-stats .stat-card.failed {
  border-color: #f56c6c;
  background: #fef0f0;
}

.test-stats .stat-card.total {
  border-color: #409eff;
  background: #ecf5ff;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 4px;
}

.test-stats .stat-card.passed .stat-number {
  color: #67c23a;
}

.test-stats .stat-card.failed .stat-number {
  color: #f56c6c;
}

.test-stats .stat-card.total .stat-number {
  color: #409eff;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.test-cases {
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409eff;
}

.error-message {
  color: #f56c6c;
  font-size: 12px;
}

.success-message {
  color: #909399;
}

.failed-summary {
  margin-top: 24px;
}

.failed-cases {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.failed-case-item {
  padding: 16px;
  background: #fef0f0;
  border: 1px solid #f56c6c;
  border-radius: 8px;
}

.failed-case-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #f56c6c;
  margin-bottom: 8px;
}

.failed-case-error {
  color: #666;
  margin-bottom: 8px;
  font-size: 14px;
}

.failed-case-suggestion {
  font-size: 12px;
  color: #333;
  background: #fff;
  padding: 8px;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}
</style>