{"summary": {"frontend_tests": {"status": "已测试", "total_tests": 17, "successful": 15, "failed": 2, "success_rate": "88.2%", "key_findings": ["发现 2 个失败的测试", "成功通过 15 个测试", "检查前端页面结构，确保关键元素存在且选择器正确", "检查页面导航结构，确保菜单链接正确配置"], "report_file": "frontend_enhanced_test_report_20250701_130227.json"}, "backend_tests": {"status": "已测试", "total_tests": 26, "successful": 15, "failed": 11, "success_rate": "57.7%", "key_findings": ["发现 11 个失败的测试", "参数错误: 2 个", "404错误: 7 个", "500错误: 1 个", "连接错误: 1 个", "成功通过 15 个测试", "检查用户认证配置，确保用户名密码正确，检查令牌生成逻辑", "检查API路由注册，确保所有模块都正确导入和注册到主路由器", "检查聚合API模块的导入和配置，确保所有依赖正确安装"], "report_file": "comprehensive_system_test_report_20250701_125545.json"}, "api_tests": {"status": "已测试", "working_count": 2, "failing_count": 3, "working_apis": ["聚合API健康检查", "API性能测试"], "failing_apis": ["问卷列表API", "评估列表API", "用户健康记录API"], "details": {"working_apis": ["聚合API健康检查", "API性能测试"], "failing_apis": ["问卷列表API", "评估列表API", "用户健康记录API"], "missing_apis": []}}, "integration_tests": {}, "overall_status": {"total_tests": 43, "successful": 30, "failed": 13, "success_rate": "69.8%", "health_status": "一般", "health_color": "orange", "timestamp": "2025-07-01T13:05:21.009277"}}, "recommendations": [{"priority": "高", "category": "前端", "issue": "前端功能测试失败", "action": "检查前端页面元素和导航结构", "impact": "影响用户体验"}, {"priority": "高", "category": "后端", "issue": "后端API测试失败", "action": "检查API路由配置和数据库连接", "impact": "影响系统功能"}], "test_reports_analyzed": ["api_routes_test_report_20250701_124915.json", "comprehensive_api_test_report_20250701_124517.json", "comprehensive_system_test_report_20250701_125523.json", "comprehensive_system_test_report_20250701_125545.json", "frontend_backend_integration_test_report_20250701_125825.json", "frontend_enhanced_test_report_20250701_130227.json", "frontend_questionnaire_test_report_20250701_123844.json", "frontend_questionnaire_test_report_20250701_124146.json", "api_routes_fix_report_20250701_125301.json", "frontend_enhanced_test_report_20250701_130227.json", "frontend_backend_integration_test_report_20250701_125825.json"], "generation_time": "2025-07-01T13:05:21.025591"}