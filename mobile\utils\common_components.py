#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一的基础组件模块

提供KivyMD 2.0.1dev0兼容的基础组件，确保整个应用的UI一致性。
"""

from kivymd.uix.textfield import MD<PERSON>extField
from kivymd.uix.button import MDButton
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
from kivy.metrics import dp
from theme import AppTheme

class BaseFormField(MDTextField):
    """统一的表单输入框基类"""
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.mode = "outlined"
        self.size_hint_y = None
        self.height = dp(56)
        self.font_size = dp(16)

class BaseButton(MDButton):
    """统一的按钮基类"""
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.style = "filled"
        self.height = dp(56)
        self.elevation = 2
        self.md_bg_color = AppTheme.ACCENT_COLOR

class BaseCard(MDCard):
    """统一的卡片基类"""
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.elevation = 2
        self.radius = [dp(16)]
        self.padding = dp(24)
        self.spacing = dp(16)

class BaseLabel(MDLabel):
    """统一的标签基类"""
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.theme_text_color = "Primary"
        self.size_hint_y = None
        self.height = dp(32)

# 兼容性工具函数
def safe_bind_event(widget, event_name, callback):
    """安全的事件绑定，兼容不同版本的KivyMD"""
    try:
        # 先解绑之前的事件
        widget.unbind(**{event_name: callback})
    except:
        pass
    
    try:
        # 绑定新事件
        widget.bind(**{event_name: callback})
        return True
    except Exception as e:
        print(f"事件绑定失败: {e}")
        return False

def get_kivymd_version():
    """获取KivyMD版本信息"""
    try:
        import kivymd
        return getattr(kivymd, '__version__', 'unknown')
    except:
        return 'unknown'

def is_kivymd_2_0_1_or_later():
    """检查是否为KivyMD 2.0.1或更高版本"""
    version = get_kivymd_version()
    if version == 'unknown':
        return False
    
    try:
        # 简单的版本比较
        major, minor, patch = version.split('.')[:3]
        return (int(major) > 2 or 
                (int(major) == 2 and int(minor) > 0) or
                (int(major) == 2 and int(minor) == 0 and int(patch) >= 1))
    except:
        return False
