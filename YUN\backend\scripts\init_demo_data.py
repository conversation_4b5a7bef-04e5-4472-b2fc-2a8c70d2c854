import sqlite3
from datetime import datetime, timedelta

db_path = 'YUN/backend/app.db'
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

now = datetime.now()
future = now + timedelta(days=7)

# 创建测试用户
cursor.execute("""
INSERT OR IGNORE INTO users (custom_id, username, email, is_active, created_at)
VALUES ('SM_DEMO', 'demo', '<EMAIL>', 1, ?)
""", (now,))

# 插入 assessment 主表
cursor.execute("""
INSERT INTO assessments (custom_id, assessment_type, name, status, created_at)
VALUES ('SM_DEMO', 'demo_type', 'Demo Assessment', 'pending', ?)
""", (now,))
assessment_id = cursor.lastrowid

# 插入 questionnaire 主表
cursor.execute("""
INSERT INTO questionnaires (custom_id, questionnaire_type, name, status, created_at)
VALUES ('SM_DEMO', 'demo_type', 'Demo Questionnaire', 'pending', ?)
""", (now,))
questionnaire_id = cursor.lastrowid

# 插入分发表
cursor.execute("""
INSERT INTO assessment_distributions (assessment_id, custom_id, status, due_date)
VALUES (?, 'SM_DEMO', 'distributed', ?)
""", (assessment_id, future))
cursor.execute("""
INSERT INTO questionnaire_distributions (questionnaire_id, custom_id, status, due_date)
VALUES (?, 'SM_DEMO', 'distributed', ?)
""", (questionnaire_id, future))

conn.commit()
conn.close()
print('初始化测试数据完成') 