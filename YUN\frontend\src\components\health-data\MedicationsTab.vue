<template>
  <div class="medications-tab">
    <div class="tab-header">
      <h3>用药管理</h3>
      <div class="action-buttons">
        <el-button type="primary" size="small" @click="openAddDialog">添加用药记录</el-button>
        <el-button type="primary" size="small" @click="refreshData">刷新数据</el-button>
      </div>
    </div>

    <el-table
      :data="medications"
      style="width: 100%"
      v-loading="loading"
      :empty-text="emptyText"
    >
      <el-table-column prop="start_date" label="开始日期" width="120">
        <template #default="scope">
          {{ formatDate(scope.row.start_date) }}
        </template>
      </el-table-column>
      <el-table-column prop="end_date" label="结束日期" width="120">
        <template #default="scope">
          {{ formatDate(scope.row.end_date) || '持续中' }}
        </template>
      </el-table-column>
      <el-table-column prop="medication_name" label="药品名称" width="150" />
      <el-table-column prop="dosage" label="剂量" width="100" />
      <el-table-column prop="frequency" label="频次" width="120" />
      <el-table-column prop="route" label="给药途径" width="120" />
      <el-table-column prop="purpose" label="用药目的" show-overflow-tooltip />
      <el-table-column prop="is_current" label="当前用药" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.is_current ? 'success' : 'info'">
            {{ scope.row.is_current ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180" fixed="right">
        <template #default="scope">
          <el-button type="primary" link @click="viewMedication(scope.row)">查看</el-button>
          <el-button type="success" link @click="editMedication(scope.row)" v-if="editable">编辑</el-button>
          <el-button type="danger" link @click="deleteMedication(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 用药记录详情对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      :title="currentMedication ? currentMedication.medication_name : '用药详情'"
      width="60%"
    >
      <div v-if="currentMedication" class="medication-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="药品名称">{{ currentMedication.medication_name }}</el-descriptions-item>
          <el-descriptions-item label="当前用药">
            <el-tag :type="currentMedication.is_current ? 'success' : 'info'">
              {{ currentMedication.is_current ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="开始日期">{{ formatDate(currentMedication.start_date) }}</el-descriptions-item>
          <el-descriptions-item label="结束日期">{{ formatDate(currentMedication.end_date) || '持续中' }}</el-descriptions-item>
          <el-descriptions-item label="剂量">{{ currentMedication.dosage }}</el-descriptions-item>
          <el-descriptions-item label="频次">{{ currentMedication.frequency }}</el-descriptions-item>
          <el-descriptions-item label="给药途径">{{ currentMedication.route }}</el-descriptions-item>
          <el-descriptions-item label="处方医生">{{ currentMedication.prescriber }}</el-descriptions-item>
          <el-descriptions-item label="用药目的" :span="2">{{ currentMedication.purpose }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ currentMedication.notes }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 添加/编辑用药记录对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      :title="isEditing ? '编辑用药记录' : '添加用药记录'"
      width="60%"
    >
      <el-form :model="medicationForm" label-width="120px" :rules="rules" ref="medicationFormRef">
        <el-form-item label="药品名称" prop="medication_name">
          <el-input v-model="medicationForm.medication_name" placeholder="请输入药品名称" />
        </el-form-item>
        <el-form-item label="开始日期" prop="start_date">
          <el-date-picker v-model="medicationForm.start_date" type="date" placeholder="选择开始日期" style="width: 100%" />
        </el-form-item>
        <el-form-item label="结束日期">
          <el-date-picker v-model="medicationForm.end_date" type="date" placeholder="选择结束日期（留空表示持续用药）" style="width: 100%" />
        </el-form-item>
        <el-form-item label="剂量" prop="dosage">
          <el-input v-model="medicationForm.dosage" placeholder="请输入剂量，如：5mg" />
        </el-form-item>
        <el-form-item label="频次" prop="frequency">
          <el-select v-model="medicationForm.frequency" placeholder="请选择用药频次" style="width: 100%">
            <el-option label="每日一次" value="每日一次" />
            <el-option label="每日两次" value="每日两次" />
            <el-option label="每日三次" value="每日三次" />
            <el-option label="每日四次" value="每日四次" />
            <el-option label="每周一次" value="每周一次" />
            <el-option label="每月一次" value="每月一次" />
            <el-option label="需要时使用" value="需要时使用" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>
        <el-form-item label="给药途径" prop="route">
          <el-select v-model="medicationForm.route" placeholder="请选择给药途径" style="width: 100%">
            <el-option label="口服" value="口服" />
            <el-option label="静脉注射" value="静脉注射" />
            <el-option label="肌肉注射" value="肌肉注射" />
            <el-option label="皮下注射" value="皮下注射" />
            <el-option label="吸入" value="吸入" />
            <el-option label="外用" value="外用" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>
        <el-form-item label="处方医生">
          <el-input v-model="medicationForm.prescriber" placeholder="请输入处方医生姓名" />
        </el-form-item>
        <el-form-item label="用药目的" prop="purpose">
          <el-input v-model="medicationForm.purpose" placeholder="请输入用药目的" />
        </el-form-item>
        <el-form-item label="当前用药">
          <el-switch v-model="medicationForm.is_current" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="medicationForm.notes" type="textarea" rows="3" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveMedication">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import axios from 'axios';

const props = defineProps({
  customId: {
    type: [Number, String],
    required: true
  },
  editable: {
    type: Boolean,
    default: false
  }
});

const loading = ref(false);
const medications = ref([]);
const viewDialogVisible = ref(false);
const editDialogVisible = ref(false);
const currentMedication = ref(null);
const isEditing = ref(false);
const medicationFormRef = ref(null);

const medicationForm = ref({
  medication_name: '',
  start_date: '',
  end_date: '',
  dosage: '',
  frequency: '',
  route: '',
  prescriber: '',
  purpose: '',
  is_current: true,
  notes: ''
});

const rules = {
  medication_name: [
    { required: true, message: '请输入药品名称', trigger: 'blur' }
  ],
  start_date: [
    { required: true, message: '请选择开始日期', trigger: 'change' }
  ],
  dosage: [
    { required: true, message: '请输入剂量', trigger: 'blur' }
  ],
  frequency: [
    { required: true, message: '请选择用药频次', trigger: 'change' }
  ],
  route: [
    { required: true, message: '请选择给药途径', trigger: 'change' }
  ],
  purpose: [
    { required: true, message: '请输入用药目的', trigger: 'blur' }
  ]
};

const emptyText = computed(() => {
  return loading.value ? '加载中...' : '暂无用药记录数据';
});

// 统一聚合接口请求
const fetchUserHealthRecords = async () => {
  if (!props.customId) return;
  loading.value = true;
  try {
    const response = await axios.get(`/api/v1/aggregated/health-profile/${props.customId}`, {
      params: {
        include_medications: true
      }
    });
    const profileData = response.data.profile_data || {};
    medications.value = profileData.medications || [];
  } catch (error) {
    ElMessage.error('获取健康资料失败');
    medications.value = [];
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  if (props.customId) {
    fetchUserHealthRecords();
  }
});

watch(() => props.customId, (newVal) => {
  if (newVal) {
    fetchUserHealthRecords();
  } else {
    medications.value = [];
  }
}, { immediate: true });

// 刷新数据
const refreshData = () => {
  fetchUserHealthRecords();
};

// 查看用药记录详情
const viewMedication = (medication) => {
  currentMedication.value = medication;
  viewDialogVisible.value = true;
};

// 打开添加用药记录对话框
const openAddDialog = () => {
  isEditing.value = false;
  medicationForm.value = {
    medication_name: '',
    start_date: new Date(),
    end_date: '',
    dosage: '',
    frequency: '',
    route: '',
    prescriber: '',
    purpose: '',
    is_current: true,
    notes: ''
  };
  editDialogVisible.value = true;
};

// 编辑用药记录
const editMedication = (medication) => {
  isEditing.value = true;
  medicationForm.value = { ...medication };
  
  // 转换日期字符串为Date对象
  if (medicationForm.value.start_date) {
    medicationForm.value.start_date = new Date(medicationForm.value.start_date);
  }
  if (medicationForm.value.end_date) {
    medicationForm.value.end_date = new Date(medicationForm.value.end_date);
  }
  
  editDialogVisible.value = true;
};

// 保存用药记录
const saveMedication = async () => {
  if (!medicationFormRef.value) return;
  
  await medicationFormRef.value.validate(async (valid) => {
    if (!valid) {
      ElMessage.warning('请完善表单信息');
      return;
    }
    
    try {
      const formData = { ...medicationForm.value };
      formData.custom_id = props.customId;
      
      // 转换日期对象为ISO字符串
      if (formData.start_date instanceof Date) {
        formData.start_date = formData.start_date.toISOString().split('T')[0];
      }
      if (formData.end_date instanceof Date) {
        formData.end_date = formData.end_date.toISOString().split('T')[0];
      }
      
      if (isEditing.value) {
        // 更新现有记录
        await axios.put(`/api/medications/${formData.id}`, formData);
        ElMessage.success('用药记录更新成功');
      } else {
        // 添加新记录
        await axios.post('/api/medications', formData);
        ElMessage.success('用药记录添加成功');
      }
      
      editDialogVisible.value = false;
      fetchUserHealthRecords(); // 刷新数据
      
      // 模拟保存成功（实际项目中应删除）
      if (isEditing.value) {
        const index = medications.value.findIndex(item => item.id === formData.id);
        if (index !== -1) {
          medications.value[index] = formData;
        }
      } else {
        formData.id = Date.now(); // 模拟生成ID
        medications.value.push(formData);
      }
    } catch (error) {
      console.error('保存用药记录失败:', error);
      ElMessage.error('保存用药记录失败，请稍后重试');
    }
  });
};

// 删除用药记录
const deleteMedication = (medication) => {
  ElMessageBox.confirm(
    `确定要删除 ${medication.medication_name} 的用药记录吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await axios.delete(`/api/medications/${medication.id}`);
      ElMessage.success('删除成功');
      fetchUserHealthRecords(); // 刷新数据
    } catch (error) {
      console.error('删除用药记录失败:', error);
      ElMessage.error('删除用药记录失败，请稍后重试');
      
      // 模拟删除成功（实际项目中应删除）
      medications.value = medications.value.filter(item => item.id !== medication.id);
      ElMessage.success('删除成功');
    }
  }).catch(() => {
    // 用户取消删除
  });
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  return date.toLocaleDateString();
};
</script>

<style scoped>
.medications-tab {
  padding: 10px;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.tab-header h3 {
  margin: 0;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.medication-detail {
  padding: 10px;
}
</style>
