import sqlite3

def check_template_mapping():
    """检查评估数据和模板的映射关系"""
    
    conn = sqlite3.connect('YUN/backend/app.db')
    cursor = conn.cursor()
    
    print("=== 检查评估数据的template_id ===")
    cursor.execute('SELECT id, name, template_id, status, created_at FROM assessments ORDER BY created_at DESC')
    assessments = cursor.fetchall()
    for a in assessments:
        print(f'ID={a[0]}, 名称={a[1]}, template_id={a[2]}, 状态={a[3]}, 创建时间={a[4]}')
    
    print("\n=== 检查assessment_templates表 ===")
    cursor.execute('SELECT id, name, description FROM assessment_templates')
    templates = cursor.fetchall()
    for t in templates:
        desc = t[2][:50] + '...' if t[2] and len(t[2]) > 50 else (t[2] or '无描述')
        print(f'模板ID={t[0]}, 名称={t[1]}, 描述={desc}')
    
    print("\n=== 检查template_id匹配情况 ===")
    template_dict = {t[0]: t[1] for t in templates}
    
    for a in assessments:
        assessment_id, assessment_name, template_id = a[0], a[1], a[2]
        if template_id in template_dict:
            print(f'✅ 评估ID={assessment_id} ({assessment_name}) -> 模板ID={template_id} ({template_dict[template_id]})')
        else:
            print(f'❌ 评估ID={assessment_id} ({assessment_name}) -> 模板ID={template_id} (未找到匹配的模板)')
    
    print("\n=== 检查SM_008用户的评估数据 ===")
    cursor.execute("SELECT id, name, template_id, status FROM assessments WHERE custom_id = 'SM_008'")
    sm008_assessments = cursor.fetchall()
    print(f'SM_008用户有 {len(sm008_assessments)} 个评估:')
    for a in sm008_assessments:
        assessment_id, assessment_name, template_id, status = a
        template_name = template_dict.get(template_id, '未知模板')
        print(f'  评估ID={assessment_id}, 名称={assessment_name}, 模板ID={template_id} ({template_name}), 状态={status}')
    
    conn.close()

if __name__ == '__main__':
    check_template_mapping()