/**
 * 前端健康监控工具
 * 监控前端应用的性能、错误和用户体验指标
 * 
 * 版本: 1.0
 * 创建时间: 2024-12-30
 */

import axios from 'axios';

export class HealthMonitor {
  constructor() {
    this.isMonitoring = false;
    this.metrics = {
      errors: [],
      performance: [],
      userActions: [],
      apiCalls: []
    };
    this.startTime = new Date();
    this.errorThreshold = 10; // 错误阈值
    this.performanceThreshold = 3000; // 性能阈值(ms)
    this.monitoringInterval = null;
    this.reportInterval = null;
  }

  /**
   * 开始监控
   */
  startMonitoring() {
    if (this.isMonitoring) {
      console.warn('健康监控已经在运行');
      return;
    }

    console.log('🏥 启动前端健康监控...');
    
    this.isMonitoring = true;
    this.startTime = new Date();
    
    // 设置错误监听
    this.setupErrorHandlers();
    
    // 设置性能监控
    this.setupPerformanceMonitoring();
    
    // 设置用户行为监控
    this.setupUserActionMonitoring();
    
    // 设置API调用监控
    this.setupApiCallMonitoring();
    
    // 定期收集指标
    this.monitoringInterval = setInterval(() => {
      this.collectMetrics();
    }, 30000); // 每30秒收集一次
    
    // 定期发送报告
    this.reportInterval = setInterval(() => {
      this.sendHealthReport();
    }, 300000); // 每5分钟发送一次报告
    
    console.log('✅ 前端健康监控已启动');
  }

  /**
   * 停止监控
   */
  stopMonitoring() {
    if (!this.isMonitoring) {
      return;
    }

    console.log('🛑 停止前端健康监控...');
    
    this.isMonitoring = false;
    
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    
    if (this.reportInterval) {
      clearInterval(this.reportInterval);
      this.reportInterval = null;
    }
    
    // 发送最终报告
    this.sendHealthReport(true);
    
    console.log('✅ 前端健康监控已停止');
  }

  /**
   * 设置错误处理器
   */
  setupErrorHandlers() {
    // JavaScript错误
    window.addEventListener('error', (event) => {
      this.recordError({
        type: 'javascript',
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error ? event.error.stack : null,
        timestamp: new Date().toISOString()
      });
    });
    
    // Promise拒绝错误
    window.addEventListener('unhandledrejection', (event) => {
      this.recordError({
        type: 'promise_rejection',
        message: event.reason ? event.reason.toString() : 'Unknown promise rejection',
        stack: event.reason ? event.reason.stack : null,
        timestamp: new Date().toISOString()
      });
    });
    
    // 资源加载错误
    window.addEventListener('error', (event) => {
      if (event.target !== window) {
        this.recordError({
          type: 'resource',
          message: `Failed to load resource: ${event.target.src || event.target.href}`,
          element: event.target.tagName,
          timestamp: new Date().toISOString()
        });
      }
    }, true);
  }

  /**
   * 设置性能监控
   */
  setupPerformanceMonitoring() {
    // 监控页面加载性能
    if ('performance' in window && 'timing' in performance) {
      window.addEventListener('load', () => {
        setTimeout(() => {
          this.recordPerformance(this.getPageLoadMetrics());
        }, 0);
      });
    }
    
    // 监控长任务
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.duration > 50) { // 超过50ms的任务
              this.recordPerformance({
                type: 'long_task',
                duration: entry.duration,
                startTime: entry.startTime,
                timestamp: new Date().toISOString()
              });
            }
          }
        });
        observer.observe({ entryTypes: ['longtask'] });
      } catch (e) {
        console.warn('Long task monitoring not supported');
      }
    }
  }

  /**
   * 设置用户行为监控
   */
  setupUserActionMonitoring() {
    // 点击事件
    document.addEventListener('click', (event) => {
      this.recordUserAction({
        type: 'click',
        target: this.getElementSelector(event.target),
        timestamp: new Date().toISOString()
      });
    });
    
    // 页面可见性变化
    document.addEventListener('visibilitychange', () => {
      this.recordUserAction({
        type: 'visibility_change',
        visible: !document.hidden,
        timestamp: new Date().toISOString()
      });
    });
    
    // 页面焦点变化
    window.addEventListener('focus', () => {
      this.recordUserAction({
        type: 'focus',
        timestamp: new Date().toISOString()
      });
    });
    
    window.addEventListener('blur', () => {
      this.recordUserAction({
        type: 'blur',
        timestamp: new Date().toISOString()
      });
    });
  }

  /**
   * 设置API调用监控
   */
  setupApiCallMonitoring() {
    // 拦截axios请求
    const originalRequest = axios.interceptors.request.use;
    const originalResponse = axios.interceptors.response.use;
    
    // 请求拦截器
    axios.interceptors.request.use(
      (config) => {
        config.metadata = { startTime: new Date() };
        return config;
      },
      (error) => {
        this.recordApiCall({
          type: 'request_error',
          error: error.message,
          timestamp: new Date().toISOString()
        });
        return Promise.reject(error);
      }
    );
    
    // 响应拦截器
    axios.interceptors.response.use(
      (response) => {
        const endTime = new Date();
        const duration = endTime - response.config.metadata.startTime;
        
        this.recordApiCall({
          type: 'success',
          url: response.config.url,
          method: response.config.method,
          status: response.status,
          duration: duration,
          timestamp: endTime.toISOString()
        });
        
        return response;
      },
      (error) => {
        const endTime = new Date();
        const duration = error.config && error.config.metadata ? 
          endTime - error.config.metadata.startTime : 0;
        
        this.recordApiCall({
          type: 'error',
          url: error.config ? error.config.url : 'unknown',
          method: error.config ? error.config.method : 'unknown',
          status: error.response ? error.response.status : 0,
          duration: duration,
          error: error.message,
          timestamp: endTime.toISOString()
        });
        
        return Promise.reject(error);
      }
    );
  }

  /**
   * 记录错误
   */
  recordError(error) {
    this.metrics.errors.push(error);
    
    // 保持错误列表大小
    if (this.metrics.errors.length > 100) {
      this.metrics.errors = this.metrics.errors.slice(-50);
    }
    
    console.warn('🚨 记录错误:', error);
    
    // 如果错误过多，发送警告
    if (this.metrics.errors.length >= this.errorThreshold) {
      this.sendAlert('high_error_rate', {
        errorCount: this.metrics.errors.length,
        recentErrors: this.metrics.errors.slice(-5)
      });
    }
  }

  /**
   * 记录性能指标
   */
  recordPerformance(metric) {
    this.metrics.performance.push(metric);
    
    // 保持性能指标列表大小
    if (this.metrics.performance.length > 100) {
      this.metrics.performance = this.metrics.performance.slice(-50);
    }
    
    // 检查性能阈值
    if (metric.duration && metric.duration > this.performanceThreshold) {
      console.warn('⚠️ 性能警告:', metric);
      this.sendAlert('poor_performance', metric);
    }
  }

  /**
   * 记录用户行为
   */
  recordUserAction(action) {
    this.metrics.userActions.push(action);
    
    // 保持用户行为列表大小
    if (this.metrics.userActions.length > 200) {
      this.metrics.userActions = this.metrics.userActions.slice(-100);
    }
  }

  /**
   * 记录API调用
   */
  recordApiCall(call) {
    this.metrics.apiCalls.push(call);
    
    // 保持API调用列表大小
    if (this.metrics.apiCalls.length > 200) {
      this.metrics.apiCalls = this.metrics.apiCalls.slice(-100);
    }
    
    // 检查API性能
    if (call.duration && call.duration > 5000) {
      console.warn('⚠️ API响应缓慢:', call);
      this.sendAlert('slow_api', call);
    }
  }

  /**
   * 收集指标
   */
  collectMetrics() {
    const metrics = {
      timestamp: new Date().toISOString(),
      uptime: this.getUptime(),
      memory: this.getMemoryUsage(),
      performance: this.getPerformanceMetrics(),
      errors: this.getErrorSummary(),
      apiCalls: this.getApiCallSummary(),
      userActivity: this.getUserActivitySummary()
    };
    
    console.log('📊 收集健康指标:', metrics);
    return metrics;
  }

  /**
   * 获取页面加载指标
   */
  getPageLoadMetrics() {
    if (!('performance' in window) || !('timing' in performance)) {
      return null;
    }
    
    const timing = performance.timing;
    return {
      type: 'page_load',
      domContentLoaded: timing.domContentLoadedEventEnd - timing.navigationStart,
      loadComplete: timing.loadEventEnd - timing.navigationStart,
      domReady: timing.domComplete - timing.navigationStart,
      firstPaint: this.getFirstPaint(),
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 获取首次绘制时间
   */
  getFirstPaint() {
    if ('performance' in window && 'getEntriesByType' in performance) {
      const paintEntries = performance.getEntriesByType('paint');
      const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
      return firstPaint ? firstPaint.startTime : null;
    }
    return null;
  }

  /**
   * 获取内存使用情况
   */
  getMemoryUsage() {
    if ('memory' in performance) {
      return {
        usedJSHeapSize: performance.memory.usedJSHeapSize,
        totalJSHeapSize: performance.memory.totalJSHeapSize,
        jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
      };
    }
    return null;
  }

  /**
   * 获取性能指标摘要
   */
  getPerformanceMetrics() {
    const recentMetrics = this.metrics.performance.slice(-10);
    if (recentMetrics.length === 0) return null;
    
    const durations = recentMetrics
      .filter(m => m.duration)
      .map(m => m.duration);
    
    return {
      count: recentMetrics.length,
      averageDuration: durations.length > 0 ? 
        durations.reduce((a, b) => a + b, 0) / durations.length : 0,
      maxDuration: durations.length > 0 ? Math.max(...durations) : 0
    };
  }

  /**
   * 获取错误摘要
   */
  getErrorSummary() {
    const recentErrors = this.metrics.errors.slice(-10);
    const errorTypes = {};
    
    recentErrors.forEach(error => {
      errorTypes[error.type] = (errorTypes[error.type] || 0) + 1;
    });
    
    return {
      total: this.metrics.errors.length,
      recent: recentErrors.length,
      types: errorTypes
    };
  }

  /**
   * 获取API调用摘要
   */
  getApiCallSummary() {
    const recentCalls = this.metrics.apiCalls.slice(-20);
    const successCalls = recentCalls.filter(call => call.type === 'success');
    const errorCalls = recentCalls.filter(call => call.type === 'error');
    
    const durations = successCalls
      .filter(call => call.duration)
      .map(call => call.duration);
    
    return {
      total: recentCalls.length,
      success: successCalls.length,
      errors: errorCalls.length,
      averageResponseTime: durations.length > 0 ? 
        durations.reduce((a, b) => a + b, 0) / durations.length : 0
    };
  }

  /**
   * 获取用户活动摘要
   */
  getUserActivitySummary() {
    const recentActions = this.metrics.userActions.slice(-20);
    const actionTypes = {};
    
    recentActions.forEach(action => {
      actionTypes[action.type] = (actionTypes[action.type] || 0) + 1;
    });
    
    return {
      total: recentActions.length,
      types: actionTypes
    };
  }

  /**
   * 获取运行时间
   */
  getUptime() {
    return Math.floor((new Date() - this.startTime) / 1000);
  }

  /**
   * 获取元素选择器
   */
  getElementSelector(element) {
    if (!element) return 'unknown';
    
    if (element.id) {
      return `#${element.id}`;
    }
    
    if (element.className) {
      // 处理不同类型的className
      let className = '';
      if (typeof element.className === 'string') {
        className = element.className;
      } else if (element.className.toString) {
        className = element.className.toString();
      } else if (element.className.value) {
        className = element.className.value;
      }
      
      if (className) {
        const firstClass = className.split(' ')[0];
        return `.${firstClass}`;
      }
    }
    
    return element.tagName ? element.tagName.toLowerCase() : 'unknown';
  }

  /**
   * 发送警告
   */
  async sendAlert(type, data) {
    try {
      await axios.post('/api/management/alerts', {
        type: type,
        data: data,
        timestamp: new Date().toISOString(),
        source: 'frontend_health_monitor'
      });
    } catch (error) {
      console.warn('发送警告失败:', error.message);
    }
  }

  /**
   * 发送健康报告
   */
  async sendHealthReport(isFinal = false) {
    try {
      const report = {
        timestamp: new Date().toISOString(),
        uptime: this.getUptime(),
        metrics: this.collectMetrics(),
        isFinal: isFinal
      };
      
      await axios.post('/api/management/health-report', report);
      console.log('📋 健康报告已发送');
      
    } catch (error) {
      console.warn('发送健康报告失败:', error.message);
    }
  }

  /**
   * 获取健康状态
   */
  getHealthStatus() {
    const errorRate = this.metrics.errors.length;
    const recentApiErrors = this.metrics.apiCalls
      .slice(-20)
      .filter(call => call.type === 'error').length;
    
    let status = 'healthy';
    
    if (errorRate >= this.errorThreshold || recentApiErrors >= 5) {
      status = 'unhealthy';
    } else if (errorRate >= this.errorThreshold / 2 || recentApiErrors >= 2) {
      status = 'warning';
    }
    
    return {
      status: status,
      uptime: this.getUptime(),
      errorCount: errorRate,
      apiErrorCount: recentApiErrors,
      lastCheck: new Date().toISOString()
    };
  }

  /**
   * 重置指标
   */
  resetMetrics() {
    this.metrics = {
      errors: [],
      performance: [],
      userActions: [],
      apiCalls: []
    };
    console.log('🔄 健康监控指标已重置');
  }
}

// 导出单例实例
export const healthMonitor = new HealthMonitor();

// 全局暴露
if (typeof window !== 'undefined') {
  window.HealthMonitor = HealthMonitor;
  window.healthMonitor = healthMonitor;
}