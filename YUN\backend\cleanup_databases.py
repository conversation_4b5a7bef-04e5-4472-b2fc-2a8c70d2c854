#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库清理脚本
删除多余的app.db文件，确保只使用一个主数据库
"""

import os
import shutil
from datetime import datetime

def backup_and_cleanup():
    """备份并清理数据库文件"""
    print("开始数据库清理...")
    
    # 主数据库路径
    main_db = 'app.db'
    
    # 需要清理的数据库文件
    cleanup_files = [
        'app/app.db',
        'app/db/app.db', 
        'app/tools/app.db'
    ]
    
    # 创建备份
    if os.path.exists(main_db):
        backup_name = f'app_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
        shutil.copy(main_db, backup_name)
        print(f"✓ 已备份主数据库为: {backup_name}")
    
    # 清理多余的数据库文件
    cleaned_count = 0
    for db_file in cleanup_files:
        if os.path.exists(db_file):
            try:
                # 检查文件大小
                size = os.path.getsize(db_file)
                if size < 1024:  # 小于1KB，可能是空文件
                    os.remove(db_file)
                    print(f"✓ 已删除空数据库文件: {db_file}")
                    cleaned_count += 1
                else:
                    # 重命名而不是删除，以防万一
                    backup_path = f"{db_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    shutil.move(db_file, backup_path)
                    print(f"✓ 已重命名数据库文件: {db_file} -> {backup_path}")
                    cleaned_count += 1
            except Exception as e:
                print(f"✗ 处理文件 {db_file} 时出错: {e}")
        else:
            print(f"- 文件不存在: {db_file}")
    
    print(f"\n清理完成，处理了 {cleaned_count} 个文件")
    print(f"主数据库: {main_db}")
    
    # 验证主数据库
    if os.path.exists(main_db):
        size = os.path.getsize(main_db)
        print(f"主数据库大小: {size:,} bytes")
        
        # 检查配置文件中的数据库路径
        print("\n检查配置文件...")
        config_files = [
            'app/core/config.py',
            'app/core/db_connection.py',
            'app/db/base_session.py'
        ]
        
        for config_file in config_files:
            if os.path.exists(config_file):
                print(f"✓ 配置文件存在: {config_file}")
            else:
                print(f"✗ 配置文件不存在: {config_file}")
    else:
        print(f"✗ 主数据库文件不存在: {main_db}")

def check_hardcoded_credentials():
    """检查代码中是否有硬编码的用户凭据"""
    print("\n检查硬编码凭据...")
    
    # 需要检查的文件模式
    check_patterns = [
        ('admin', '管理员用户名'),
        ('password', '密码字段'),
        ('123456', '弱密码'),
        ('<EMAIL>', '示例邮箱')
    ]
    
    # 需要检查的文件
    check_files = [
        'app/core/config.py',
        'app/api/auth.py',
        'main.py',
        'app/core/security.py'
    ]
    
    found_issues = []
    
    for file_path in check_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                for pattern, description in check_patterns:
                    if pattern in content and 'example' not in pattern.lower():
                        # 排除注释和文档
                        lines = content.split('\n')
                        for i, line in enumerate(lines, 1):
                            if pattern in line and not line.strip().startswith('#'):
                                found_issues.append({
                                    'file': file_path,
                                    'line': i,
                                    'content': line.strip(),
                                    'issue': description
                                })
            except Exception as e:
                print(f"读取文件 {file_path} 时出错: {e}")
    
    if found_issues:
        print("发现可能的硬编码凭据:")
        for issue in found_issues:
            print(f"  文件: {issue['file']}:{issue['line']}")
            print(f"  问题: {issue['issue']}")
            print(f"  内容: {issue['content']}")
            print()
    else:
        print("✓ 未发现明显的硬编码凭据")

def main():
    """主函数"""
    print("数据库清理和安全检查工具")
    print("=" * 50)
    
    # 备份和清理
    backup_and_cleanup()
    
    # 检查硬编码凭据
    check_hardcoded_credentials()
    
    print("\n建议:")
    print("1. 确保所有用户凭据都存储在数据库中")
    print("2. 不要在代码中硬编码用户名和密码")
    print("3. 使用环境变量或配置文件管理敏感信息")
    print("4. 定期备份数据库文件")
    print("5. 确保数据库文件的访问权限正确设置")

if __name__ == "__main__":
    main()