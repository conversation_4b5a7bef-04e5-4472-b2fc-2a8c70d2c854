#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_mobile_apis():
    base_url = "http://localhost:8006/api"
    
    # 首先登录获取token
    login_data = {
        "username": "markey",
        "password": "markey0308@163"
    }
    
    print("=== 登录获取token ===")
    login_response = requests.post(f"{base_url}/auth/login", data=login_data)
    print(f"登录状态码: {login_response.status_code}")
    print(f"登录响应: {login_response.text}")
    
    if login_response.status_code != 200:
        print(f"登录失败: {login_response.text}")
        return
    
    try:
        token_data = login_response.json()
        token = token_data.get("access_token")
        if not token:
            print(f"未获取到token，响应数据: {token_data}")
            return
        print(f"获取到token: {token[:50]}...")
    except Exception as e:
        print(f"解析token失败: {e}")
        return
    
    headers = {
        "Authorization": f"Bearer {token}",
        "X-User-ID": "SM_008"
    }
    
    # 测试各个移动端API
    endpoints = [
        ("/mobile/pending-assessments", "待完成量表"),
        ("/mobile/pending-questionnaires", "待完成问卷"),
        ("/mobile/history-assessments", "历史量表报告"),
        ("/mobile/history-questionnaires", "历史问卷报告")
    ]
    
    for endpoint, description in endpoints:
        print(f"\n=== 测试 {description} ===")
        print(f"请求URL: {base_url}{endpoint}")
        
        try:
            response = requests.get(f"{base_url}{endpoint}", headers=headers)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"响应状态: {data.get('status')}")
                
                if 'data' in data:
                    items = data['data']
                    if isinstance(items, list):
                        print(f"返回数据条数: {len(items)}")
                        if items:
                            print("数据示例:")
                            for i, item in enumerate(items[:2]):  # 只显示前2条
                                print(f"  [{i+1}] {item}")
                    else:
                        print(f"返回数据: {items}")
                
                if 'debug' in data:
                    print(f"调试信息: {data['debug']}")
            else:
                print(f"请求失败: {response.text}")
                
        except Exception as e:
            print(f"请求异常: {e}")

if __name__ == "__main__":
    test_mobile_apis()