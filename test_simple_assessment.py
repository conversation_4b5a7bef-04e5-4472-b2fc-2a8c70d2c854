#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

# 配置
base_url = "http://localhost:8000"
headers = {
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJTTV8wMDYiLCJleHAiOjE3MzQ2NzQ0MDB9.123",
    "Content-Type": "application/json",
    "X-User-ID": "SM_006"
}

def test_assessments():
    """测试量表API"""
    print("=== 测试量表API ===")
    
    # 测试: 获取所有量表
    print("\n获取所有量表:")
    response = requests.get(f"{base_url}/api/mobile/assessments?custom_id=SM_006&limit=50", headers=headers)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        try:
            data = response.json()
            print(f"\nAPI完整响应:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # 解析数据
            if 'data' in data:
                if isinstance(data['data'], dict) and 'assessments' in data['data']:
                    assessments = data['data']['assessments']
                    total = data['data'].get('total', len(assessments))
                    print(f"\n解析结果:")
                    print(f"总数: {total}")
                    print(f"返回记录数: {len(assessments)}")
                    
                    for i, assessment in enumerate(assessments):
                        print(f"  {i+1}. ID: {assessment.get('id')}, 名称: {assessment.get('name')}, 状态: {assessment.get('status')}")
                elif isinstance(data['data'], list):
                    assessments = data['data']
                    print(f"\n解析结果:")
                    print(f"总数: {len(assessments)}")
                    print(f"返回记录数: {len(assessments)}")
                    
                    for i, assessment in enumerate(assessments):
                        print(f"  {i+1}. ID: {assessment.get('id')}, 名称: {assessment.get('name')}, 状态: {assessment.get('status')}")
                else:
                    print(f"\n未知的数据格式: {type(data['data'])}")
            else:
                print("\n响应中没有data字段")
                
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            print(f"原始响应: {response.text}")
    else:
        print(f"请求失败: {response.text}")

if __name__ == "__main__":
    test_assessments()