#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查SDS抑郁自评量表的计分规则配置
"""

import sqlite3
import json
import sys
import os

def check_sds_scoring():
    """检查SDS量表的计分规则配置"""
    db_path = 'c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db'
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 检查SDS模板信息
        print("=== SDS模板信息 ===")
        cursor.execute("""
            SELECT id, template_key, name, scoring_method, max_score, status
            FROM assessment_templates 
            WHERE template_key = 'sds'
        """)
        
        template_info = cursor.fetchone()
        if not template_info:
            print("❌ 未找到SDS模板")
            return
        
        template_id, template_key, name, scoring_method, max_score, status = template_info
        print(f"模板ID: {template_id}")
        print(f"模板Key: {template_key}")
        print(f"模板名称: {name}")
        print(f"计分方法: {scoring_method}")
        print(f"最大分数: {max_score}")
        print(f"状态: {status}")
        
        # 2. 检查SDS问题的计分规则
        print("\n=== SDS问题计分规则 ===")
        cursor.execute("""
            SELECT question_id, question_text, scoring, dimension_key, is_required
            FROM assessment_template_questions 
            WHERE template_id = ?
            ORDER BY question_id
        """, (template_id,))
        
        questions = cursor.fetchall()
        print(f"问题总数: {len(questions)}")
        
        scoring_issues = []
        for i, (question_id, question_text, scoring, dimension_key, is_required) in enumerate(questions[:5]):
            print(f"\n问题 {i+1}: {question_id}")
            print(f"  文本: {question_text[:50]}...")
            print(f"  维度: {dimension_key}")
            print(f"  必填: {is_required}")
            print(f"  计分规则: {scoring}")
            
            # 检查计分规则是否为空
            if not scoring or scoring.strip() == '':
                scoring_issues.append(f"问题 {question_id} 计分规则为空")
            else:
                try:
                    # 尝试解析JSON格式的计分规则
                    if scoring.startswith('{') or scoring.startswith('['):
                        parsed_scoring = json.loads(scoring)
                        print(f"  解析后的计分规则: {parsed_scoring}")
                    else:
                        print(f"  计分规则(非JSON): {scoring}")
                except json.JSONDecodeError as e:
                    scoring_issues.append(f"问题 {question_id} 计分规则JSON格式错误: {e}")
        
        # 3. 检查最近的SDS评估记录
        print("\n=== 最近的SDS评估记录 ===")
        cursor.execute("""
            SELECT ar.id, ar.user_id, ar.total_score, ar.dimension_scores, ar.raw_answers, ar.created_at
            FROM assessment_results ar
            JOIN assessments a ON ar.assessment_id = a.id
            WHERE a.template_id = ?
            ORDER BY ar.created_at DESC
            LIMIT 3
        """, (template_id,))
        
        recent_results = cursor.fetchall()
        if recent_results:
            for result in recent_results:
                result_id, user_id, total_score, dimension_scores, raw_answers, created_at = result
                print(f"\n评估ID: {result_id}, 用户: {user_id}, 时间: {created_at}")
                print(f"  总分: {total_score}")
                print(f"  维度分: {dimension_scores}")
                print(f"  原始答案: {raw_answers[:100] if raw_answers else 'None'}...")
        else:
            print("未找到SDS评估记录")
        
        # 4. 检查SM_008用户的SDS评估
        print("\n=== SM_008用户的SDS评估 ===")
        cursor.execute("""
            SELECT ar.id, ar.total_score, ar.dimension_scores, ar.raw_answers, ar.created_at
            FROM assessment_results ar
            JOIN assessments a ON ar.assessment_id = a.id
            JOIN users u ON ar.user_id = u.id
            WHERE a.template_id = ? AND u.custom_id = 'SM_008'
            ORDER BY ar.created_at DESC
            LIMIT 1
        """, (template_id,))
        
        sm008_result = cursor.fetchone()
        if sm008_result:
            result_id, total_score, dimension_scores, raw_answers, created_at = sm008_result
            print(f"评估ID: {result_id}, 时间: {created_at}")
            print(f"总分: {total_score}")
            print(f"维度分: {dimension_scores}")
            print(f"原始答案: {raw_answers}")
            
            # 分析原始答案
            if raw_answers:
                try:
                    answers_data = json.loads(raw_answers)
                    print(f"\n原始答案解析成功，包含 {len(answers_data)} 个答案")
                    for i, answer in enumerate(answers_data[:3]):
                        print(f"  答案 {i+1}: {answer}")
                except json.JSONDecodeError:
                    print("原始答案JSON解析失败")
        else:
            print("未找到SM_008用户的SDS评估记录")
        
        # 5. 总结问题
        print("\n=== 问题总结 ===")
        if scoring_issues:
            print("❌ 发现以下计分问题:")
            for issue in scoring_issues:
                print(f"  - {issue}")
        else:
            print("✅ 计分规则检查通过")
        
        conn.close()
        
    except Exception as e:
        print(f"检查过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_sds_scoring()