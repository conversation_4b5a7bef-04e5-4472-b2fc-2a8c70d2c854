#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MySQL数据库配置模块
用于阿里云宝塔轻应用MySQL服务集成
"""

import os
import logging
from typing import Optional, Dict, Any
from sqlalchemy import create_engine, text, inspect
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import SQLAlchemyError
from contextlib import contextmanager

# 创建日志记录器
logger = logging.getLogger("mysql_config")

class MySQLConfig:
    """
    MySQL数据库配置类
    支持阿里云宝塔轻应用MySQL服务
    """
    
    def __init__(self):
        """初始化MySQL配置"""
        # MySQL连接参数
        self.host = os.getenv("MYSQL_HOST", "localhost")
        self.port = int(os.getenv("MYSQL_PORT", "3306"))
        self.user = os.getenv("MYSQL_USER", "healthapp_user")
        self.password = os.getenv("MYSQL_PASSWORD", "")
        self.database = os.getenv("MYSQL_DATABASE", "healthapp_db")
        self.charset = os.getenv("MYSQL_CHARSET", "utf8mb4")
        
        # 连接池配置
        self.pool_size = int(os.getenv("DB_POOL_SIZE", "10"))
        self.max_overflow = int(os.getenv("DB_MAX_OVERFLOW", "20"))
        self.pool_timeout = int(os.getenv("DB_POOL_TIMEOUT", "30"))
        self.pool_recycle = int(os.getenv("DB_POOL_RECYCLE", "3600"))
        
        # 是否启用MySQL
        self.enabled = self._check_mysql_enabled()
        
        # 数据库引擎
        self.engine = None
        self.SessionLocal = None
        
        if self.enabled:
            self._create_engine()
    
    def _check_mysql_enabled(self) -> bool:
        """
        检查是否启用MySQL
        
        Returns:
            bool: 是否启用MySQL
        """
        # 检查是否配置了MySQL密码
        if not self.password:
            logger.info("MySQL密码未配置，使用SQLite数据库")
            return False
        
        # 检查DATABASE_URL是否指向MySQL
        database_url = os.getenv("DATABASE_URL", "")
        if "mysql" in database_url.lower():
            return True
        
        # 检查是否明确启用MySQL
        use_mysql = os.getenv("USE_MYSQL", "false").lower()
        return use_mysql in ("true", "1", "yes")
    
    def _create_engine(self):
        """
        创建MySQL数据库引擎
        """
        try:
            # 构建连接URL
            database_url = self.get_database_url()
            
            # 连接参数
            connect_args = {
                "charset": self.charset,
                "autocommit": False,
                "connect_timeout": 60,
                "read_timeout": 60,
                "write_timeout": 60
            }
            
            # 创建引擎
            self.engine = create_engine(
                database_url,
                connect_args=connect_args,
                pool_size=self.pool_size,
                max_overflow=self.max_overflow,
                pool_timeout=self.pool_timeout,
                pool_recycle=self.pool_recycle,
                pool_pre_ping=True,  # 连接前检查
                echo=False  # 生产环境关闭SQL日志
            )
            
            # 创建会话工厂
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )
            
            logger.info(f"MySQL引擎创建成功: {self.host}:{self.port}/{self.database}")
            
        except Exception as e:
            logger.error(f"创建MySQL引擎失败: {str(e)}")
            self.enabled = False
            raise
    
    def get_database_url(self) -> str:
        """
        获取数据库连接URL
        
        Returns:
            str: 数据库连接URL
        """
        if not self.enabled:
            # 使用绝对路径
            import os
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            db_path = os.path.join(base_dir, "app.db")
            return f"sqlite:///{db_path}"
        
        # 检查环境变量中的DATABASE_URL
        database_url = os.getenv("DATABASE_URL")
        if database_url and "mysql" in database_url.lower():
            return database_url
        
        # 构建MySQL URL
        return f"mysql+pymysql://{self.user}:{self.password}@{self.host}:{self.port}/{self.database}?charset={self.charset}"
    
    def test_connection(self) -> bool:
        """
        测试数据库连接
        
        Returns:
            bool: 连接是否成功
        """
        if not self.enabled or not self.engine:
            return False
        
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                result.fetchone()
            logger.info("MySQL连接测试成功")
            return True
        except Exception as e:
            logger.error(f"MySQL连接测试失败: {str(e)}")
            return False
    
    def get_database_info(self) -> Dict[str, Any]:
        """
        获取数据库信息
        
        Returns:
            Dict[str, Any]: 数据库信息
        """
        if not self.enabled or not self.engine:
            return {"type": "sqlite", "status": "active"}
        
        try:
            with self.engine.connect() as conn:
                # 获取MySQL版本
                version_result = conn.execute(text("SELECT VERSION()"))
                version = version_result.fetchone()[0]
                
                # 获取数据库大小
                size_query = text("""
                    SELECT 
                        ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                    FROM information_schema.tables 
                    WHERE table_schema = :database
                """)
                size_result = conn.execute(size_query, {"database": self.database})
                size_mb = size_result.fetchone()[0] or 0
                
                # 获取表数量
                table_query = text("""
                    SELECT COUNT(*) 
                    FROM information_schema.tables 
                    WHERE table_schema = :database
                """)
                table_result = conn.execute(table_query, {"database": self.database})
                table_count = table_result.fetchone()[0]
                
                return {
                    "type": "mysql",
                    "status": "active",
                    "version": version,
                    "host": self.host,
                    "port": self.port,
                    "database": self.database,
                    "size_mb": float(size_mb),
                    "table_count": int(table_count),
                    "charset": self.charset
                }
        except Exception as e:
            logger.error(f"获取数据库信息失败: {str(e)}")
            return {
                "type": "mysql",
                "status": "error",
                "error": str(e)
            }
    
    @contextmanager
    def get_session(self):
        """
        获取数据库会话（上下文管理器）
        
        Yields:
            Session: 数据库会话
        """
        if not self.enabled or not self.SessionLocal:
            raise RuntimeError("MySQL未启用或未正确配置")
        
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()
    
    def create_database_if_not_exists(self) -> bool:
        """
        创建数据库（如果不存在）
        
        Returns:
            bool: 是否成功创建或已存在
        """
        if not self.enabled:
            return False
        
        try:
            # 连接到MySQL服务器（不指定数据库）
            server_url = f"mysql+pymysql://{self.user}:{self.password}@{self.host}:{self.port}/?charset={self.charset}"
            server_engine = create_engine(server_url)
            
            with server_engine.connect() as conn:
                # 检查数据库是否存在
                check_query = text("""
                    SELECT SCHEMA_NAME 
                    FROM INFORMATION_SCHEMA.SCHEMATA 
                    WHERE SCHEMA_NAME = :database
                """)
                result = conn.execute(check_query, {"database": self.database})
                
                if result.fetchone():
                    logger.info(f"数据库 {self.database} 已存在")
                    return True
                
                # 创建数据库
                create_query = text(f"""
                    CREATE DATABASE `{self.database}` 
                    CHARACTER SET {self.charset} 
                    COLLATE {self.charset}_unicode_ci
                """)
                conn.execute(create_query)
                conn.commit()
                
                logger.info(f"数据库 {self.database} 创建成功")
                return True
                
        except Exception as e:
            logger.error(f"创建数据库失败: {str(e)}")
            return False
    
    def get_table_list(self) -> list:
        """
        获取数据库表列表
        
        Returns:
            list: 表名列表
        """
        if not self.enabled or not self.engine:
            return []
        
        try:
            inspector = inspect(self.engine)
            tables = inspector.get_table_names()
            return tables
        except Exception as e:
            logger.error(f"获取表列表失败: {str(e)}")
            return []
    
    def optimize_tables(self) -> bool:
        """
        优化数据库表
        
        Returns:
            bool: 是否成功
        """
        if not self.enabled or not self.engine:
            return False
        
        try:
            tables = self.get_table_list()
            with self.engine.connect() as conn:
                for table in tables:
                    optimize_query = text(f"OPTIMIZE TABLE `{table}`")
                    conn.execute(optimize_query)
                conn.commit()
            
            logger.info(f"优化了 {len(tables)} 个表")
            return True
            
        except Exception as e:
            logger.error(f"优化表失败: {str(e)}")
            return False

# 创建全局MySQL配置实例
mysql_config = MySQLConfig()

# 导出函数
def get_mysql_engine():
    """获取MySQL引擎"""
    return mysql_config.engine

def get_mysql_session():
    """获取MySQL会话"""
    return mysql_config.get_session()

def is_mysql_enabled() -> bool:
    """检查MySQL是否启用"""
    return mysql_config.enabled

def get_database_url() -> str:
    """获取数据库URL"""
    return mysql_config.get_database_url()