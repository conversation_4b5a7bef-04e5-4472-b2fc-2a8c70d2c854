<template>
  <div class="health-records-tab">
    <div class="tab-header">
      <h3>健康状况一览表</h3>
      <el-button type="primary" size="small" @click="refreshData">刷新数据</el-button>
    </div>

    <el-table
      :data="healthRecords"
      style="width: 100%"
      v-loading="loading"
      :empty-text="emptyText"
    >
      <el-table-column prop="record_date" label="记录日期" width="120">
        <template #default="scope">
          {{ formatDate(scope.row.record_date) }}
        </template>
      </el-table-column>
      <el-table-column prop="record_type" label="记录类型" width="120">
        <template #default="scope">
          {{ getRecordTypeLabel(scope.row.record_type) }}
        </template>
      </el-table-column>
      <el-table-column prop="height" label="身高(cm)" width="100" />
      <el-table-column prop="weight" label="体重(kg)" width="100" />
      <el-table-column prop="bmi" label="BMI" width="80">
        <template #default="scope">
          {{ calculateBMI(scope.row.height, scope.row.weight) }}
        </template>
      </el-table-column>
      <el-table-column prop="blood_pressure" label="血压(mmHg)" width="120" />
      <el-table-column prop="heart_rate" label="心率(次/分)" width="120" />
      <el-table-column prop="blood_glucose" label="血糖(mmol/L)" width="130" />
      <el-table-column prop="notes" label="备注" show-overflow-tooltip />
      <el-table-column label="操作" width="120" fixed="right">
        <template #default="scope">
          <el-button type="primary" link @click="viewRecord(scope.row)">查看</el-button>
          <el-button type="danger" link @click="deleteRecord(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 记录详情对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="50%"
    >
      <div v-if="currentRecord" class="record-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="记录日期">{{ formatDate(currentRecord.record_date) }}</el-descriptions-item>
          <el-descriptions-item label="记录类型">{{ getRecordTypeLabel(currentRecord.record_type) }}</el-descriptions-item>
          <el-descriptions-item label="身高">{{ currentRecord.height }} cm</el-descriptions-item>
          <el-descriptions-item label="体重">{{ currentRecord.weight }} kg</el-descriptions-item>
          <el-descriptions-item label="BMI">{{ calculateBMI(currentRecord.height, currentRecord.weight) }}</el-descriptions-item>
          <el-descriptions-item label="血压">{{ currentRecord.blood_pressure }} mmHg</el-descriptions-item>
          <el-descriptions-item label="心率">{{ currentRecord.heart_rate }} 次/分</el-descriptions-item>
          <el-descriptions-item label="血糖">{{ currentRecord.blood_glucose }} mmol/L</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ currentRecord.notes }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import axios from 'axios';
import { getMockData, isMockEnabled } from '../../mocks/mockDataManager';

// 接收用户ID作为属性
const props = defineProps({
  customId: {
    type: [Number, String],
    required: true
  }
});

// 状态变量
const loading = ref(false);
const healthRecords = ref([]);
const dialogVisible = ref(false);
const currentRecord = ref(null);

// 计算属性
const dialogTitle = computed(() => {
  return currentRecord.value ? `健康记录详情 (${formatDate(currentRecord.value.record_date)})` : '健康记录详情';
});

const emptyText = computed(() => {
  return loading.value ? '加载中...' : '暂无健康记录数据';
});

// 获取健康记录数据
const fetchHealthRecords = async () => {
  if (!props.customId) return;
  
  loading.value = true;
  try {
    const response = await axios.get(`/api/v1/aggregated/health-profile/${props.customId}`, {
        params: {
          include_health_records: true
        }
      });
    const profileData = response.data.profile_data || {};
    healthRecords.value = profileData.health_records || [];
    
    // 如果没有真实数据且启用了模拟数据，使用模拟数据
    if ((!response.data || response.data.length === 0) && isMockEnabled()) {
      healthRecords.value = getMockData('healthRecords', { custom_id: props.customId });
    }
  } catch (error) {
    console.error('获取健康记录失败:', error);
    ElMessage.error('获取健康记录失败，请稍后重试');
    
    // 如果启用了模拟数据，使用模拟数据
    if (isMockEnabled()) {
      healthRecords.value = getMockData('healthRecords', { custom_id: props.customId });
    }
  } finally {
    loading.value = false;
  }
};

// 监听用户ID变化
watch(() => props.customId, (newVal) => {
  if (newVal) {
    fetchHealthRecords();
  } else {
    healthRecords.value = [];
  }
}, { immediate: true });

// 初始化
onMounted(() => {
  if (props.customId) {
    fetchHealthRecords();
  }
});

// 刷新数据
const refreshData = () => {
  fetchHealthRecords();
};

// 查看记录详情
const viewRecord = (record) => {
  currentRecord.value = record;
  dialogVisible.value = true;
};

// 删除记录
const deleteRecord = (record) => {
  ElMessageBox.confirm(
    `确定要删除 ${formatDate(record.record_date)} 的健康记录吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await axios.delete(`/api/user-health-records/user/${props.customId}/${record.id}`, {
        params: {
          record_type: 'health'
        }
      });
      ElMessage.success('删除成功');
      fetchHealthRecords(); // 刷新数据
    } catch (error) {
      console.error('删除健康记录失败:', error);
      ElMessage.error('删除健康记录失败，请稍后重试');
      
      // 如果启用了模拟数据，模拟删除成功
      if (isMockEnabled()) {
        healthRecords.value = healthRecords.value.filter(item => item.id !== record.id);
        ElMessage.success('删除成功');
      }
    }
  }).catch(() => {
    // 用户取消删除
  });
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  return date.toLocaleDateString();
};

// 获取记录类型标签
const getRecordTypeLabel = (type) => {
  const typeMap = {
    'regular': '常规体检',
    'physical': '体格检查',
    'follow_up': '随访检查',
    'special': '专项检查',
    'other': '其他'
  };
  
  return typeMap[type] || type;
};

// 计算BMI
const calculateBMI = (height, weight) => {
  if (!height || !weight) return '-';
  
  const heightInMeters = height / 100;
  const bmi = weight / (heightInMeters * heightInMeters);
  
  return bmi.toFixed(1);
};
</script>

<style scoped>
.health-records-tab {
  padding: 10px;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.tab-header h3 {
  margin: 0;
}

.record-detail {
  padding: 10px;
}
</style>
