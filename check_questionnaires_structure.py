import sqlite3

db_path = 'c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db'

try:
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 查看questionnaires表结构
    print("questionnaires表字段:")
    cursor.execute('PRAGMA table_info(questionnaires)')
    columns = cursor.fetchall()
    for col in columns:
        print(f'  {col[1]} ({col[2]})')
    
    # 查看assessments表结构
    print("\nassessments表字段:")
    cursor.execute('PRAGMA table_info(assessments)')
    columns = cursor.fetchall()
    for col in columns:
        print(f'  {col[1]} ({col[2]})')
    
    conn.close()
    
except Exception as e:
    print(f"错误: {e}")