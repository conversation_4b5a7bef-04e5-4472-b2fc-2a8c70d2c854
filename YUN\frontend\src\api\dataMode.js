/**
 * 数据模式切换API
 * 提供前端与后端数据模式切换接口的通信
 */

import request from '@/utils/request'

/**
 * 获取当前数据模式状态
 * @returns {Promise} API响应
 */
export function getDataModeStatus() {
  return request({
    url: '/api/data-mode/status',
    method: 'get'
  })
}

/**
 * 切换数据模式
 * @param {Object} data - 切换参数
 * @param {string} data.mode - 数据模式 (auto|mock|production)
 * @param {string} data.reason - 切换原因
 * @returns {Promise} API响应
 */
export function switchDataMode(data) {
  return request({
    url: '/api/data-mode/switch',
    method: 'post',
    data
  })
}

/**
 * 测试当前数据模式
 * @returns {Promise} API响应
 */
export function testDataMode() {
  return request({
    url: '/api/data-mode/test',
    method: 'get'
  })
}

/**
 * 获取数据模式切换历史
 * @returns {Promise} API响应
 */
export function getDataModeSwitchHistory() {
  return request({
    url: '/api/data-mode/history',
    method: 'get'
  })
}

/**
 * 数据模式枚举
 */
export const DATA_MODES = {
  AUTO: 'auto',
  MOCK: 'mock',
  PRODUCTION: 'production'
}

/**
 * 数据模式标签映射
 */
export const DATA_MODE_LABELS = {
  [DATA_MODES.AUTO]: '自动模式',
  [DATA_MODES.MOCK]: '模拟数据模式',
  [DATA_MODES.PRODUCTION]: '生产数据模式'
}

/**
 * 数据模式描述映射
 */
export const DATA_MODE_DESCRIPTIONS = {
  [DATA_MODES.AUTO]: '根据环境变量自动选择数据源',
  [DATA_MODES.MOCK]: '强制使用模拟数据，适用于开发和测试',
  [DATA_MODES.PRODUCTION]: '强制使用真实生产数据'
}

/**
 * 数据模式颜色映射（用于UI显示）
 */
export const DATA_MODE_COLORS = {
  [DATA_MODES.AUTO]: '#409EFF',     // 蓝色
  [DATA_MODES.MOCK]: '#E6A23C',     // 橙色
  [DATA_MODES.PRODUCTION]: '#67C23A' // 绿色
}

/**
 * 数据模式图标映射
 */
export const DATA_MODE_ICONS = {
  [DATA_MODES.AUTO]: 'el-icon-magic-stick',
  [DATA_MODES.MOCK]: 'el-icon-cpu',
  [DATA_MODES.PRODUCTION]: 'el-icon-connection'
}