#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 sqlite3 datetime 适配器修复是否有效
"""

import warnings
import datetime
import sqlite3
import os
import sys

# 捕获所有警告
warnings.filterwarnings('error', category=DeprecationWarning)

def setup_sqlite3_adapters_converters():
    """设置 Python 3.12 兼容的 sqlite3 适配器和转换器"""
    
    def adapt_date_iso(val):
        return val.isoformat()
    
    def adapt_datetime_iso(val):
        return val.isoformat()
    
    def convert_date(val):
        if isinstance(val, bytes):
            val = val.decode('utf-8')
        return datetime.date.fromisoformat(val)
    
    def convert_datetime(val):
        if isinstance(val, bytes):
            val = val.decode('utf-8')
        return datetime.datetime.fromisoformat(val)
    
    def convert_timestamp(val):
        if isinstance(val, bytes):
            val = val.decode('utf-8')
        try:
            return datetime.datetime.fromisoformat(val)
        except ValueError:
            try:
                return datetime.datetime.fromtimestamp(float(val))
            except (ValueError, OSError):
                return datetime.datetime.now()
    
    sqlite3.register_adapter(datetime.date, adapt_date_iso)
    sqlite3.register_adapter(datetime.datetime, adapt_datetime_iso)
    sqlite3.register_converter('date', convert_date)
    sqlite3.register_converter('datetime', convert_datetime)
    sqlite3.register_converter('timestamp', convert_timestamp)

def test_without_fix():
    """测试不使用修复时是否有警告"""
    print("🧪 测试不使用修复的情况...")
    
    try:
        # 连接到实际数据库
        db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'app.db')
        if not os.path.exists(db_path):
            print(f"❌ 数据库文件不存在: {db_path}")
            return False
            
        conn = sqlite3.connect(db_path, detect_types=sqlite3.PARSE_DECLTYPES | sqlite3.PARSE_COLNAMES)
        cursor = conn.cursor()
        
        # 执行一个包含 datetime 的查询
        cursor.execute("""
            SELECT created_at FROM assessments 
            WHERE created_at >= ? 
            LIMIT 1
        """, (datetime.datetime.now() - datetime.timedelta(days=30),))
        
        result = cursor.fetchone()
        conn.close()
        
        print("❌ 没有捕获到弃用警告（可能已被修复或不在此环境中触发）")
        return False
        
    except DeprecationWarning as e:
        print(f"✅ 捕获到弃用警告: {e}")
        return True
    except Exception as e:
        print(f"⚠️ 其他错误: {e}")
        return False

def test_with_fix():
    """测试使用修复后是否还有警告"""
    print("\n🔧 测试使用修复后的情况...")
    
    # 应用修复
    setup_sqlite3_adapters_converters()
    
    try:
        # 连接到实际数据库
        db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'app.db')
        if not os.path.exists(db_path):
            print(f"❌ 数据库文件不存在: {db_path}")
            return False
            
        conn = sqlite3.connect(db_path, detect_types=sqlite3.PARSE_DECLTYPES | sqlite3.PARSE_COLNAMES)
        cursor = conn.cursor()
        
        # 执行多个包含 datetime 的查询
        queries = [
            "SELECT created_at FROM assessments WHERE created_at >= ? LIMIT 1",
            "SELECT created_at FROM questionnaires WHERE created_at >= ? LIMIT 1",
            "SELECT updated_at FROM health_records WHERE updated_at >= ? LIMIT 1"
        ]
        
        yesterday = datetime.datetime.now() - datetime.timedelta(days=1)
        
        for query in queries:
            cursor.execute(query, (yesterday,))
            result = cursor.fetchone()
            if result:
                print(f"✅ 查询成功，结果类型: {type(result[0])}")
        
        conn.close()
        print("✅ 所有查询完成，没有弃用警告")
        return True
        
    except DeprecationWarning as e:
        print(f"❌ 仍然有弃用警告: {e}")
        return False
    except Exception as e:
        print(f"⚠️ 其他错误: {e}")
        return False

def test_monitoring_script_import():
    """测试监控脚本导入是否有警告"""
    print("\n📋 测试监控脚本导入...")
    
    try:
        # 临时添加脚本目录到路径
        script_dir = os.path.dirname(__file__)
        if script_dir not in sys.path:
            sys.path.insert(0, script_dir)
        
        # 尝试导入监控脚本的主要函数
        import assessment_questionnaire_monitor
        
        print("✅ 监控脚本导入成功，没有弃用警告")
        return True
        
    except DeprecationWarning as e:
        print(f"❌ 监控脚本导入时有弃用警告: {e}")
        return False
    except Exception as e:
        print(f"⚠️ 监控脚本导入错误: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试 SQLite3 datetime 适配器修复效果...")
    
    # 测试不使用修复的情况
    # test_without_fix()
    
    # 测试使用修复后的情况
    fix_success = test_with_fix()
    
    # 测试监控脚本导入
    import_success = test_monitoring_script_import()
    
    print("\n📊 测试结果总结:")
    print(f"✅ 修复后查询测试: {'通过' if fix_success else '失败'}")
    print(f"✅ 监控脚本导入测试: {'通过' if import_success else '失败'}")
    
    if fix_success and import_success:
        print("\n🎉 所有测试通过！SQLite3 datetime 适配器弃用警告已成功修复")
        print("\n💡 修复说明:")
        print("- 已注册 Python 3.12 兼容的适配器和转换器")
        print("- 监控脚本现在可以正常运行而不会产生弃用警告")
        print("- 数据库操作保持完全兼容")
    else:
        print("\n❌ 部分测试失败，可能需要进一步调查")

if __name__ == '__main__':
    main()