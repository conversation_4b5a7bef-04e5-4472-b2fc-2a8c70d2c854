"""医疗记录API路由
"""
from typing import Any, List, Dict, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body, status
from sqlalchemy.orm import Session
from datetime import datetime
from app.utils.field_compatibility import ensure_field_compatibility, ensure_list_field_compatibility

from app.db.session import get_db
from app.models.user import User
from app.core.auth import get_current_active_user_custom
from app.api import deps

router = APIRouter()

# 纯函数：获取用户医疗记录数据（用于聚合API）
def get_user_medical_records(db: Session, custom_id: str, record_type: Optional[str] = None, **kwargs) -> Dict[str, Any]:
    """
    获取指定用户的医疗记录数据（纯函数，用于聚合API调用）
    """
    print(f"medical_records.py - 获取用户记录 - 用户ID: {custom_id}")
    
    try:
        # 导入必要的模块
        from app.models.medical_record import MedicalRecord, MedicalRecordType
        from app.models.user import User
        
        # 查找用户
        user = None
        if custom_id.isdigit():
            user = db.query(User).filter(User.id == int(custom_id)).first()
        else:
            user = db.query(User).filter(User.custom_id == custom_id).first()
        
        if not user:
            print(f"用户{custom_id}不存在，返回空结果")
            return {"status": "success", "total": 0, "items": []}
        
        record_type_enum = None
        if record_type is not None:
            try:
                # 验证record_type是否为有效的枚举值
                valid_types = [e.value for e in MedicalRecordType]
                if record_type in valid_types:
                    record_type_enum = MedicalRecordType(record_type)
                elif record_type != '':
                    # 仅非法字符串才返回空
                    print(f"record_type非法或未定义: {record_type}, 有效类型为: {valid_types}，返回空结果")
                    return {"status": "success", "total": 0, "items": [], "message": f"无效的record_type: {record_type}，有效类型为: {valid_types}"}
            except Exception as e:
                print(f"record_type枚举校验异常: {e}, 返回空结果")
                return {"status": "success", "total": 0, "items": [], "message": f"record_type参数异常: {str(e)}"}
        
        # 构建查询
        query = db.query(MedicalRecord).filter(MedicalRecord.custom_id == user.custom_id)
        
        if record_type_enum:
            query = query.filter(MedicalRecord.record_type == record_type_enum)
        
        # 获取总数
        total = query.count()
        
        # 获取数据
        records = []
        if total > 0:
            medical_records = query.order_by(MedicalRecord.created_at.desc()).all()
            
            # 转换为字典列表
            for record in medical_records:
                records.append({
                    "id": record.id,
                    "custom_id": record.custom_id,
                    "record_type": record.record_type.value if record.record_type else None,
                    "hospital": record.hospital_name,
                    "department": record.department,
                    "doctor": record.doctor_name,
                    "visit_date": record.visit_date.isoformat() if record.visit_date else None,
                    "diagnosis": record.diagnosis,
                    "treatment": record.treatment,
                    "prescription": record.prescription,
                    "notes": record.notes,
                    "is_important": record.is_important
                })
        
        print(f"用户{custom_id}查询结果 - 总数: {total}, 返回记录数: {len(records)}")
        return {
            "status": "success",
            "total": total,
            "items": records
        }
    except Exception as e:
        print(f"获取用户医疗记录时出错: {str(e)}")
        # 返回空结果而不是抛出异常
        return {
            "status": "success",
            "total": 0,
            "items": []
        }

# 管理端API - 获取特定用户的医疗记录
@router.get("/user/{custom_id}", response_model=Dict[str, Any])
def read_user_records(
    *,
    db: Session = Depends(deps.get_db),
    custom_id: str = Path(..., description="用户ID"),
    record_type: Optional[str] = Query(None, description="记录类型"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    current_user: User = Depends(get_current_active_user_custom)
) -> Any:
    """
    获取指定用户的医疗记录列表
    """
    print(f"medical_records.py - 获取用户记录 - 用户ID: {custom_id}")

    # 支持数字ID和自定义ID查找用户
    user = None
    if custom_id.isdigit():
        user = db.query(User).filter(User.id == int(custom_id)).first()
    else:
        user = db.query(User).filter(User.custom_id == custom_id).first()
    
    if not user:
        print(f"用户{custom_id}不存在，返回404异常")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到用户ID: {custom_id}"
        )

    # 权限校验
    if current_user.id != user.id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        print(f"权限检查失败 - 当前用户: {current_user.custom_id}, 角色: {current_user.role}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限查看此用户的数据"
        )

    try:
        # 导入必要的模块
        from app.models.medical_record import MedicalRecord, MedicalRecordType

        record_type_enum = None
        if record_type is not None:
            try:
                # 验证record_type是否为有效的枚举值
                valid_types = [e.value for e in MedicalRecordType]
                if record_type in valid_types:
                    record_type_enum = MedicalRecordType(record_type)
                elif record_type != '':
                    # 仅非法字符串才返回空
                    print(f"record_type非法或未定义: {record_type}, 有效类型为: {valid_types}，返回空结果")
                    return {"status": "success", "total": 0, "items": [], "message": f"无效的record_type: {record_type}，有效类型为: {valid_types}"}
                # record_type为None或空字符串时，不加类型过滤
            except Exception as e:
                print(f"record_type枚举校验异常: {e}, 返回空结果")
                return {"status": "success", "total": 0, "items": [], "message": f"record_type参数异常: {str(e)}"}
        
        # 构建查询
        query = db.query(MedicalRecord).filter(MedicalRecord.custom_id == user.custom_id)
        
        if record_type_enum:
            query = query.filter(MedicalRecord.record_type == record_type_enum)
        
        if start_date:
            # 确保start_date是datetime对象
            if isinstance(start_date, str):
                try:
                    start_date = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
                except Exception:
                    try:
                        start_date = datetime.strptime(start_date, '%Y-%m-%d')
                    except Exception:
                        start_date = None
            if isinstance(start_date, datetime):
                query = query.filter(MedicalRecord.visit_date >= start_date)
        if end_date:
            # 确保end_date是datetime对象
            if isinstance(end_date, str):
                try:
                    end_date = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
                except Exception:
                    try:
                        end_date = datetime.strptime(end_date, '%Y-%m-%d')
                    except Exception:
                        end_date = None
            if isinstance(end_date, datetime):
                query = query.filter(MedicalRecord.visit_date <= end_date)
        
        # 获取总数
        total = query.count()
        
        # 获取分页数据
        records = []
        if total > 0:
            medical_records = query.order_by(MedicalRecord.created_at.desc()).offset(skip).limit(limit).all()
            
            # 转换为字典列表
            for record in medical_records:
                records.append({
                    "id": record.id,
                    "custom_id": record.custom_id,
                    "record_type": record.record_type.value if record.record_type else None,
                    "hospital": record.hospital_name,
                    "department": record.department,
                    "doctor": record.doctor_name,
                    "visit_date": record.visit_date.isoformat() if record.visit_date else None,
                    "diagnosis": record.diagnosis,
                    "treatment": record.treatment,
                    "prescription": record.prescription,
                    "notes": record.notes,
                    "is_important": record.is_important
                })
        
        print(f"用户{custom_id}查询结果 - 总数: {total}, 返回记录数: {len(records)}")
        return {
            "status": "success",
            "total": total,
            "items": records
        }
    except Exception as e:
        print(f"获取用户医疗记录时出错: {str(e)}")
        # 返回空结果而不是抛出异常
        return {
            "status": "success",
            "total": 0,
            "items": []
        }

# 移动端API
@router.get("/medical-records", response_model=Dict[str, Any])
def mobile_get_medical_records(
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
) -> Any:
    """
    移动端获取医疗记录列表
    """
    try:
        # 导入必要的模块
        from app.models.medical_record import MedicalRecord

        # 确保用户有custom_id
        if not current_user.custom_id:
            from app.services.id_generator import IDGenerator
            custom_id = IDGenerator.generate_custom_id_for_user(db, current_user)
            print(f"为用户 {current_user.username} 生成custom_id: {custom_id}")
        else:
            custom_id = current_user.custom_id

        # 查询总数
        total = db.query(MedicalRecord).filter(MedicalRecord.custom_id == custom_id).count()

        # 分页查询记录
        medical_records = db.query(MedicalRecord).filter(
            MedicalRecord.custom_id == custom_id
        ).order_by(MedicalRecord.created_at.desc()).offset((page - 1) * limit).limit(limit).all()

        # 转换为字典列表
        records = []
        for record in medical_records:
            records.append({
                "id": record.id,
                "custom_id": record.custom_id,
                "record_type": record.record_type.value if record.record_type else None,
                "hospital": record.hospital_name,
                "department": record.department,
                "doctor": record.doctor_name,
                "visit_date": record.visit_date.isoformat() if record.visit_date else None,
                "diagnosis": record.diagnosis,
                "treatment": record.treatment,
                "prescription": record.prescription,
                "notes": record.notes,
                "is_important": record.is_important
            })

        # 如果没有记录，返回空列表
        if not records:
            print(f"用户 {current_user.username}(custom_id: {current_user.custom_id}) 没有医疗记录")

        return {
            "status": "success",
            "total": total,
            "page": page,
            "limit": limit,
            "items": records
        }
    except Exception as e:
        print(f"获取医疗记录列表时出错: {str(e)}")
        return {
            "status": "error",
            "message": f"获取医疗记录列表时出错: {str(e)}"
        }

@router.post("/medical-records", response_model=Dict[str, Any])
def mobile_create_medical_record(
    hospital: str = Body(...),
    department: str = Body(...),
    doctor: str = Body(...),
    visit_date: str = Body(...),
    diagnosis: str = Body(...),
    treatment: str = Body(...),
    prescription: str = Body(None),
    record_type: str = Body("outpatient"),  # 默认为门诊记录
    is_important: bool = Body(False),
    notes: str = Body(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
) -> Any:
    """
    移动端创建医疗记录
    """
    try:
        # 导入必要的模块
        from app.models.medical_record import MedicalRecord, MedicalRecordType
        from datetime import datetime

        # 确保用户有custom_id
        if not current_user.custom_id:
            from app.services.id_generator import IDGenerator
            custom_id = IDGenerator.generate_custom_id_for_user(db, current_user)
            print(f"为用户 {current_user.username} 生成custom_id: {custom_id}")
        else:
            custom_id = current_user.custom_id

        # 解析日期
        try:
            visit_date_obj = datetime.fromisoformat(visit_date) if visit_date else None
        except ValueError as e:
            return {
                "status": "error",
                "message": f"日期格式错误: {str(e)}"
            }

        # 解析记录类型
        try:
            record_type_enum = MedicalRecordType(record_type)
        except ValueError:
            return {
                "status": "error",
                "message": f"无效的记录类型: {record_type}，有效类型为: {[t.value for t in MedicalRecordType]}"
            }

        # 创建新的医疗记录
        new_record = MedicalRecord(
            custom_id=custom_id,
            record_type=record_type_enum,
            hospital_name=hospital,
            department=department,
            doctor_name=doctor,
            visit_date=visit_date_obj,
            diagnosis=diagnosis,
            treatment=treatment,
            prescription=prescription,
            notes=notes,
            is_important=is_important
        )

        # 保存到数据库
        db.add(new_record)
        db.commit()
        db.refresh(new_record)

        # 转换为字典
        record = {
            "id": new_record.id,
            "custom_id": new_record.custom_id,
            "record_type": new_record.record_type.value if new_record.record_type else None,
            "hospital": new_record.hospital_name,
            "department": new_record.department,
            "doctor": new_record.doctor_name,
            "visit_date": new_record.visit_date.isoformat() if new_record.visit_date else None,
            "diagnosis": new_record.diagnosis,
            "treatment": new_record.treatment,
            "prescription": new_record.prescription,
            "notes": new_record.notes,
            "is_important": new_record.is_important
        }

        return {
            "status": "success",
            "message": "医疗记录创建成功",
            "data": record
        }
    except Exception as e:
        print(f"创建医疗记录时出错: {str(e)}")
        return {
            "status": "error",
            "message": f"创建医疗记录时出错: {str(e)}"
        }