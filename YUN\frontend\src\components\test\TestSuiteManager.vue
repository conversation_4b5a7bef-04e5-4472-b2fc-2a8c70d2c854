<template>
  <div class="test-suite-manager">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>测试套件管理</h2>
      <p class="page-description">创建、配置和管理自动化测试套件</p>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" icon="Plus" @click="showCreateDialog = true">
          创建测试套件
        </el-button>
        <el-button icon="Upload" @click="importTestSuites">
          导入套件
        </el-button>
        <el-button icon="Refresh" @click="refreshTestSuites" :loading="loading">
          刷新
        </el-button>
        <el-button icon="Download" @click="exportTestSuites" :disabled="selectedSuites.length === 0">
          导出选中
        </el-button>
        <el-button icon="CopyDocument" @click="cloneSelectedSuites" :disabled="selectedSuites.length === 0">
          批量克隆
        </el-button>
        <el-button icon="Delete" type="danger" @click="deleteSelectedSuites" :disabled="selectedSuites.length === 0">
          批量删除
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索测试套件..."
          prefix-icon="Search"
          style="width: 300px;"
          clearable
        />
        <el-select v-model="filterStatus" placeholder="状态筛选" style="width: 120px; margin-left: 10px;">
          <el-option label="全部" value="" />
          <el-option label="活跃" value="active" />
          <el-option label="禁用" value="disabled" />
          <el-option label="草稿" value="draft" />
        </el-select>
        <el-select v-model="filterType" placeholder="类型筛选" style="width: 120px; margin-left: 10px;">
          <el-option label="全部" value="" />
          <el-option label="单元测试" value="unit" />
          <el-option label="集成测试" value="integration" />
          <el-option label="API测试" value="api" />
          <el-option label="E2E测试" value="e2e" />
          <el-option label="性能测试" value="performance" />
        </el-select>
        <div class="view-controls" style="margin-left: 20px;">
          <el-radio-group v-model="viewMode" size="small">
            <el-radio-button label="grid">网格视图</el-radio-button>
            <el-radio-button label="list">列表视图</el-radio-button>
          </el-radio-group>
        </div>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="statistics-overview">
      <div class="stat-cards">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon primary">
              <el-icon><Folder /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ totalSuites }}</div>
              <div class="stat-label">总套件数</div>
              <div class="stat-trend positive">+{{ recentIncrease }}%</div>
            </div>
          </div>
        </el-card>
        
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon success">
              <el-icon><SuccessFilled /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ activeSuites }}</div>
              <div class="stat-label">活跃套件</div>
              <div class="stat-trend positive">{{ Math.round((activeSuites / totalSuites) * 100) }}%</div>
            </div>
          </div>
        </el-card>
        
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon info">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ totalTestCases }}</div>
              <div class="stat-label">测试用例</div>
              <div class="stat-trend">{{ averageTestsPerSuite }} 平均</div>
            </div>
          </div>
        </el-card>
        
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon warning">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ averageSuccessRate }}%</div>
              <div class="stat-label">平均成功率</div>
              <div class="stat-trend" :class="successRateChange > 0 ? 'positive' : 'negative'">
                {{ successRateChange > 0 ? '+' : '' }}{{ successRateChange }}%
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 测试套件列表 -->
    <div class="test-suites-grid">
      <div 
        v-for="suite in filteredTestSuites" 
        :key="suite.id" 
        class="test-suite-card"
        :class="{ 'disabled': suite.status === 'disabled' }"
      >
        <!-- 卡片头部 -->
        <div class="card-header">
          <div class="suite-info">
            <h3 class="suite-name">{{ suite.name }}</h3>
            <div class="suite-meta">
              <el-tag :type="getStatusTagType(suite.status)" size="small">
                {{ getStatusText(suite.status) }}
              </el-tag>
              <el-tag :type="getTypeTagType(suite.type)" size="small">
                {{ getTypeText(suite.type) }}
              </el-tag>
            </div>
          </div>
          <div class="card-actions">
            <el-dropdown @command="handleSuiteAction">
              <el-button type="text" icon="MoreFilled" />
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="{ action: 'run', suite }" icon="VideoPlay">
                    运行测试
                  </el-dropdown-item>
                  <el-dropdown-item :command="{ action: 'edit', suite }" icon="Edit">
                    编辑配置
                  </el-dropdown-item>
                  <el-dropdown-item :command="{ action: 'clone', suite }" icon="CopyDocument">
                    克隆套件
                  </el-dropdown-item>
                  <el-dropdown-item :command="{ action: 'export', suite }" icon="Download">
                    导出配置
                  </el-dropdown-item>
                  <el-dropdown-item 
                    :command="{ action: 'delete', suite }" 
                    icon="Delete" 
                    divided
                  >
                    删除套件
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>

        <!-- 卡片内容 -->
        <div class="card-content">
          <p class="suite-description">{{ suite.description || '暂无描述' }}</p>
          
          <!-- 测试统计 -->
          <div class="test-stats">
            <div class="stat-item">
              <span class="stat-label">测试用例</span>
              <span class="stat-value">{{ suite.testCount || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">成功率</span>
              <span class="stat-value" :class="getSuccessRateClass(suite.successRate)">
                {{ formatPercentage(suite.successRate) }}
              </span>
            </div>
            <div class="stat-item">
              <span class="stat-label">平均耗时</span>
              <span class="stat-value">{{ formatDuration(suite.avgDuration) }}</span>
            </div>
          </div>

          <!-- 最近运行信息 -->
          <div class="recent-run" v-if="suite.lastRun">
            <div class="run-status">
              <el-icon :class="getRunStatusIcon(suite.lastRun.status)">
                <component :is="getRunStatusIcon(suite.lastRun.status)" />
              </el-icon>
              <span>{{ getRunStatusText(suite.lastRun.status) }}</span>
            </div>
            <div class="run-time">
              {{ formatRelativeTime(suite.lastRun.timestamp) }}
            </div>
          </div>

          <!-- 配置预览 -->
          <div class="config-preview">
            <div class="config-item" v-if="suite.config.timeout">
              <span class="config-label">超时时间:</span>
              <span class="config-value">{{ suite.config.timeout }}ms</span>
            </div>
            <div class="config-item" v-if="suite.config.retries">
              <span class="config-label">重试次数:</span>
              <span class="config-value">{{ suite.config.retries }}</span>
            </div>
            <div class="config-item" v-if="suite.config.parallel !== undefined">
              <span class="config-label">并行执行:</span>
              <span class="config-value">{{ suite.config.parallel ? '是' : '否' }}</span>
            </div>
          </div>
        </div>

        <!-- 卡片底部操作 -->
        <div class="card-footer">
          <el-button 
            type="primary" 
            size="small" 
            @click="runTestSuite(suite)"
            :loading="suite.running"
            :disabled="suite.status === 'disabled'"
          >
            <el-icon><VideoPlay /></el-icon>
            {{ suite.running ? '运行中...' : '运行测试' }}
          </el-button>
          <el-button size="small" @click="editTestSuite(suite)">
            <el-icon><Edit /></el-icon>
            编辑
          </el-button>
          <el-button size="small" @click="viewTestResults(suite)">
            <el-icon><DataAnalysis /></el-icon>
            查看结果
          </el-button>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredTestSuites.length === 0" class="empty-state">
        <el-empty description="暂无测试套件">
          <el-button type="primary" @click="showCreateDialog = true">
            创建第一个测试套件
          </el-button>
        </el-empty>
      </div>
    </div>

    <!-- 创建/编辑测试套件对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingSuite ? '编辑测试套件' : '创建测试套件'"
      width="800px"
      @close="resetForm"
    >
      <el-form
        ref="suiteFormRef"
        :model="suiteForm"
        :rules="suiteFormRules"
        label-width="120px"
      >
        <!-- 基本信息 -->
        <el-form-item label="套件名称" prop="name">
          <el-input v-model="suiteForm.name" placeholder="请输入测试套件名称" />
        </el-form-item>
        
        <el-form-item label="套件类型" prop="type">
          <el-select v-model="suiteForm.type" placeholder="请选择测试类型">
            <el-option label="单元测试" value="unit" />
            <el-option label="集成测试" value="integration" />
            <el-option label="API测试" value="api" />
            <el-option label="E2E测试" value="e2e" />
          </el-select>
        </el-form-item>

        <el-form-item label="描述">
          <el-input 
            v-model="suiteForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入测试套件描述"
          />
        </el-form-item>

        <el-form-item label="状态">
          <el-radio-group v-model="suiteForm.status">
            <el-radio label="active">活跃</el-radio>
            <el-radio label="disabled">禁用</el-radio>
            <el-radio label="draft">草稿</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 配置选项 -->
        <el-divider content-position="left">配置选项</el-divider>
        
        <el-form-item label="超时时间(ms)">
          <el-input-number 
            v-model="suiteForm.config.timeout" 
            :min="1000" 
            :max="300000" 
            :step="1000"
          />
        </el-form-item>

        <el-form-item label="重试次数">
          <el-input-number 
            v-model="suiteForm.config.retries" 
            :min="0" 
            :max="5"
          />
        </el-form-item>

        <el-form-item label="并行执行">
          <el-switch v-model="suiteForm.config.parallel" />
        </el-form-item>

        <el-form-item label="启用覆盖率">
          <el-switch v-model="suiteForm.config.coverage" />
        </el-form-item>

        <!-- 测试文件配置 -->
        <el-divider content-position="left">测试文件</el-divider>
        
        <el-form-item label="测试目录">
          <el-input v-model="suiteForm.config.testDir" placeholder="测试文件目录路径" />
        </el-form-item>

        <el-form-item label="文件模式">
          <el-input v-model="suiteForm.config.testPattern" placeholder="测试文件匹配模式" />
        </el-form-item>

        <el-form-item label="排除文件">
          <el-input v-model="suiteForm.config.excludePattern" placeholder="排除文件模式" />
        </el-form-item>

        <!-- 环境配置 -->
        <el-divider content-position="left">环境配置</el-divider>
        
        <el-form-item label="运行环境">
          <el-select v-model="suiteForm.config.environment" placeholder="选择运行环境">
            <el-option label="开发环境" value="development" />
            <el-option label="测试环境" value="testing" />
            <el-option label="CI环境" value="ci" />
          </el-select>
        </el-form-item>

        <el-form-item label="环境变量">
          <div class="env-vars">
            <div 
              v-for="(envVar, index) in suiteForm.config.envVars" 
              :key="index" 
              class="env-var-item"
            >
              <el-input 
                v-model="envVar.key" 
                placeholder="变量名" 
                style="width: 200px;"
              />
              <el-input 
                v-model="envVar.value" 
                placeholder="变量值" 
                style="width: 200px; margin-left: 10px;"
              />
              <el-button 
                type="danger" 
                icon="Delete" 
                size="small" 
                @click="removeEnvVar(index)"
                style="margin-left: 10px;"
              />
            </div>
            <el-button type="text" icon="Plus" @click="addEnvVar">
              添加环境变量
            </el-button>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveSuite" :loading="saving">
          {{ editingSuite ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 测试结果查看对话框 -->
    <el-dialog
      v-model="showResultsDialog"
      title="测试结果"
      width="1000px"
    >
      <div v-if="selectedSuiteResults" class="test-results">
        <!-- 结果概览 -->
        <div class="results-overview">
          <div class="overview-item">
            <div class="overview-value">{{ selectedSuiteResults.total }}</div>
            <div class="overview-label">总测试数</div>
          </div>
          <div class="overview-item success">
            <div class="overview-value">{{ selectedSuiteResults.passed }}</div>
            <div class="overview-label">通过</div>
          </div>
          <div class="overview-item failed">
            <div class="overview-value">{{ selectedSuiteResults.failed }}</div>
            <div class="overview-label">失败</div>
          </div>
          <div class="overview-item skipped">
            <div class="overview-value">{{ selectedSuiteResults.skipped }}</div>
            <div class="overview-label">跳过</div>
          </div>
        </div>

        <!-- 详细结果 -->
        <el-table :data="selectedSuiteResults.details" style="width: 100%">
          <el-table-column prop="name" label="测试名称" width="300" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getTestStatusTagType(row.status)">
                {{ row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="duration" label="耗时" width="100">
            <template #default="{ row }">
              {{ formatDuration(row.duration) }}
            </template>
          </el-table-column>
          <el-table-column prop="error" label="错误信息">
            <template #default="{ row }">
              <span v-if="row.error" class="error-message">{{ row.error }}</span>
              <span v-else class="success-message">-</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Refresh, Download, Search, MoreFilled, VideoPlay, Edit, 
  CopyDocument, Delete, DataAnalysis, CircleCheck, CircleClose, 
  Warning, Clock
} from '@element-plus/icons-vue'

// 响应式数据
const testSuites = ref([])
const searchKeyword = ref('')
const filterStatus = ref('')
const filterType = ref('')
const viewMode = ref('grid')
const showCreateDialog = ref(false)
const showResultsDialog = ref(false)
const editingSuite = ref(null)
const selectedSuiteResults = ref(null)
const selectedSuites = ref([])
const saving = ref(false)
const loading = ref(false)

// 表单引用
const suiteFormRef = ref()

// 表单数据
const suiteForm = reactive({
  name: '',
  type: '',
  description: '',
  status: 'active',
  config: {
    timeout: 30000,
    retries: 2,
    parallel: true,
    coverage: true,
    testDir: '',
    testPattern: '**/*.test.{js,ts}',
    excludePattern: 'node_modules/**',
    environment: 'testing',
    envVars: []
  }
})

// 表单验证规则
const suiteFormRules = {
  name: [
    { required: true, message: '请输入套件名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择测试类型', trigger: 'change' }
  ]
}

// 计算属性
const filteredTestSuites = computed(() => {
  return testSuites.value.filter(suite => {
    const matchesSearch = !searchKeyword.value || 
      suite.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      suite.description?.toLowerCase().includes(searchKeyword.value.toLowerCase())
    
    const matchesStatus = !filterStatus.value || suite.status === filterStatus.value
    const matchesType = !filterType.value || suite.type === filterType.value
    
    return matchesSearch && matchesStatus && matchesType
  })
})

const totalSuites = computed(() => testSuites.value.length)
const activeSuites = computed(() => testSuites.value.filter(s => s.status === 'active').length)
const totalTestCases = computed(() => testSuites.value.reduce((sum, s) => sum + (s.testCount || 0), 0))
const averageTestsPerSuite = computed(() => totalSuites.value > 0 ? Math.round(totalTestCases.value / totalSuites.value) : 0)
const averageSuccessRate = computed(() => {
  if (testSuites.value.length === 0) return 0
  const sum = testSuites.value.reduce((acc, s) => acc + (s.successRate || 0), 0)
  return Math.round((sum / testSuites.value.length) * 100)
})
const recentIncrease = ref(12)
const successRateChange = ref(5)

// 方法
const refreshTestSuites = async () => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟数据
    testSuites.value = [
      {
        id: '1',
        name: '前端单元测试',
        type: 'unit',
        description: '测试Vue组件和工具函数',
        status: 'active',
        testCount: 45,
        successRate: 0.95,
        avgDuration: 2500,
        lastRun: {
          status: 'success',
          timestamp: new Date(Date.now() - 3600000).toISOString()
        },
        config: {
          timeout: 30000,
          retries: 2,
          parallel: true,
          coverage: true,
          testDir: 'frontend/tests/unit',
          testPattern: '**/*.test.js',
          environment: 'testing'
        },
        running: false
      },
      {
        id: '2',
        name: '后端API测试',
        type: 'api',
        description: '测试REST API接口',
        status: 'active',
        testCount: 32,
        successRate: 0.88,
        avgDuration: 5200,
        lastRun: {
          status: 'failed',
          timestamp: new Date(Date.now() - 7200000).toISOString()
        },
        config: {
          timeout: 45000,
          retries: 1,
          parallel: false,
          coverage: false,
          testDir: 'backend/tests/api',
          testPattern: '**/*.py',
          environment: 'testing'
        },
        running: false
      },
      {
        id: '3',
        name: 'E2E集成测试',
        type: 'e2e',
        description: '端到端用户流程测试',
        status: 'disabled',
        testCount: 12,
        successRate: 0.75,
        avgDuration: 15000,
        lastRun: {
          status: 'warning',
          timestamp: new Date(Date.now() - 86400000).toISOString()
        },
        config: {
          timeout: 120000,
          retries: 0,
          parallel: false,
          coverage: false,
          testDir: 'tests/e2e',
          testPattern: '**/*.spec.js',
          environment: 'testing'
        },
        running: false
      }
    ]
    
    ElMessage.success('测试套件列表已刷新')
  } catch (error) {
    ElMessage.error('刷新失败: ' + error.message)
  }
}

const runTestSuite = async (suite) => {
  try {
    suite.running = true
    ElMessage.info(`开始运行测试套件: ${suite.name}`)
    
    // 模拟测试运行
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    // 更新测试结果
    suite.lastRun = {
      status: Math.random() > 0.3 ? 'success' : 'failed',
      timestamp: new Date().toISOString()
    }
    
    ElMessage.success(`测试套件 ${suite.name} 运行完成`)
  } catch (error) {
    ElMessage.error('运行失败: ' + error.message)
  } finally {
    suite.running = false
  }
}

const editTestSuite = (suite) => {
  editingSuite.value = suite
  Object.assign(suiteForm, {
    ...suite,
    config: { ...suite.config }
  })
  showCreateDialog.value = true
}

const viewTestResults = async (suite) => {
  try {
    // 模拟获取测试结果
    selectedSuiteResults.value = {
      total: suite.testCount,
      passed: Math.floor(suite.testCount * suite.successRate),
      failed: Math.floor(suite.testCount * (1 - suite.successRate)),
      skipped: 0,
      details: Array.from({ length: 5 }, (_, i) => ({
        name: `测试用例 ${i + 1}`,
        status: Math.random() > 0.2 ? 'passed' : 'failed',
        duration: Math.floor(Math.random() * 1000) + 100,
        error: Math.random() > 0.8 ? '断言失败: 期望值不匹配' : null
      }))
    }
    showResultsDialog.value = true
  } catch (error) {
    ElMessage.error('获取测试结果失败: ' + error.message)
  }
}

const handleSuiteAction = async ({ action, suite }) => {
  switch (action) {
    case 'run':
      await runTestSuite(suite)
      break
    case 'edit':
      editTestSuite(suite)
      break
    case 'clone':
      await cloneTestSuite(suite)
      break
    case 'export':
      await exportTestSuite(suite)
      break
    case 'delete':
      await deleteTestSuite(suite)
      break
  }
}

const cloneTestSuite = async (suite) => {
  try {
    const newSuite = {
      ...suite,
      id: Date.now().toString(),
      name: `${suite.name} (副本)`,
      status: 'draft'
    }
    testSuites.value.push(newSuite)
    ElMessage.success('测试套件已克隆')
  } catch (error) {
    ElMessage.error('克隆失败: ' + error.message)
  }
}

const exportTestSuite = async (suite) => {
  try {
    const config = JSON.stringify(suite, null, 2)
    const blob = new Blob([config], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${suite.name}.json`
    a.click()
    URL.revokeObjectURL(url)
    ElMessage.success('配置已导出')
  } catch (error) {
    ElMessage.error('导出失败: ' + error.message)
  }
}

const deleteTestSuite = async (suite) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除测试套件 "${suite.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = testSuites.value.findIndex(s => s.id === suite.id)
    if (index > -1) {
      testSuites.value.splice(index, 1)
      ElMessage.success('测试套件已删除')
    }
  } catch {
    // 用户取消删除
  }
}

const exportTestSuites = async () => {
  try {
    const suitesToExport = selectedSuites.value.length > 0 
      ? testSuites.value.filter(s => selectedSuites.value.includes(s.id))
      : testSuites.value
    
    const config = JSON.stringify(suitesToExport, null, 2)
    const blob = new Blob([config], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = selectedSuites.value.length > 0 
      ? 'selected-test-suites.json' 
      : 'all-test-suites.json'
    a.click()
    URL.revokeObjectURL(url)
    ElMessage.success(`已导出 ${suitesToExport.length} 个测试套件配置`)
  } catch (error) {
    ElMessage.error('导出失败: ' + error.message)
  }
}

const importTestSuites = async () => {
  try {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.json'
    input.onchange = async (e) => {
      const file = e.target.files[0]
      if (!file) return
      
      const text = await file.text()
      const importedSuites = JSON.parse(text)
      
      if (!Array.isArray(importedSuites)) {
        throw new Error('文件格式不正确，应为测试套件数组')
      }
      
      // 为导入的套件生成新ID，避免冲突
      const newSuites = importedSuites.map(suite => ({
        ...suite,
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        name: suite.name + ' (导入)',
        running: false
      }))
      
      testSuites.value.push(...newSuites)
      ElMessage.success(`成功导入 ${newSuites.length} 个测试套件`)
    }
    input.click()
  } catch (error) {
    ElMessage.error('导入失败: ' + error.message)
  }
}

const cloneSelectedSuites = async () => {
  try {
    if (selectedSuites.value.length === 0) {
      ElMessage.warning('请先选择要克隆的测试套件')
      return
    }
    
    const suitesToClone = testSuites.value.filter(s => selectedSuites.value.includes(s.id))
    const clonedSuites = suitesToClone.map(suite => ({
      ...suite,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      name: suite.name + ' (副本)',
      running: false,
      lastRun: null
    }))
    
    testSuites.value.push(...clonedSuites)
    selectedSuites.value = []
    ElMessage.success(`成功克隆 ${clonedSuites.length} 个测试套件`)
  } catch (error) {
    ElMessage.error('克隆失败: ' + error.message)
  }
}

const deleteSelectedSuites = async () => {
  try {
    if (selectedSuites.value.length === 0) {
      ElMessage.warning('请先选择要删除的测试套件')
      return
    }
    
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedSuites.value.length} 个测试套件吗？此操作不可恢复。`,
      '确认批量删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    testSuites.value = testSuites.value.filter(s => !selectedSuites.value.includes(s.id))
    selectedSuites.value = []
    ElMessage.success('选中的测试套件已删除')
  } catch {
    // 用户取消删除
  }
}

const saveSuite = async () => {
  try {
    await suiteFormRef.value.validate()
    saving.value = true
    
    // 模拟保存
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    if (editingSuite.value) {
      // 更新现有套件
      const index = testSuites.value.findIndex(s => s.id === editingSuite.value.id)
      if (index > -1) {
        testSuites.value[index] = { ...testSuites.value[index], ...suiteForm }
      }
      ElMessage.success('测试套件已更新')
    } else {
      // 创建新套件
      const newSuite = {
        ...suiteForm,
        id: Date.now().toString(),
        testCount: 0,
        successRate: 0,
        avgDuration: 0,
        lastRun: null,
        running: false
      }
      testSuites.value.push(newSuite)
      ElMessage.success('测试套件已创建')
    }
    
    showCreateDialog.value = false
    resetForm()
  } catch (error) {
    if (error !== 'validation failed') {
      ElMessage.error('保存失败: ' + error.message)
    }
  } finally {
    saving.value = false
  }
}

const resetForm = () => {
  editingSuite.value = null
  Object.assign(suiteForm, {
    name: '',
    type: '',
    description: '',
    status: 'active',
    config: {
      timeout: 30000,
      retries: 2,
      parallel: true,
      coverage: true,
      testDir: '',
      testPattern: '**/*.test.{js,ts}',
      excludePattern: 'node_modules/**',
      environment: 'testing',
      envVars: []
    }
  })
  suiteFormRef.value?.clearValidate()
}

const addEnvVar = () => {
  suiteForm.config.envVars.push({ key: '', value: '' })
}

const removeEnvVar = (index) => {
  suiteForm.config.envVars.splice(index, 1)
}

// 工具函数
const getStatusTagType = (status) => {
  const types = {
    active: 'success',
    disabled: 'info',
    draft: 'warning'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    active: '活跃',
    disabled: '禁用',
    draft: '草稿'
  }
  return texts[status] || status
}

const getTypeTagType = (type) => {
  const types = {
    unit: 'primary',
    integration: 'success',
    api: 'warning',
    e2e: 'danger'
  }
  return types[type] || 'info'
}

const getTypeText = (type) => {
  const texts = {
    unit: '单元测试',
    integration: '集成测试',
    api: 'API测试',
    e2e: 'E2E测试'
  }
  return texts[type] || type
}

const getSuccessRateClass = (rate) => {
  if (rate >= 0.9) return 'success-rate'
  if (rate >= 0.7) return 'warning-rate'
  return 'danger-rate'
}

const getRunStatusIcon = (status) => {
  const icons = {
    success: 'CircleCheck',
    failed: 'CircleClose',
    warning: 'Warning',
    running: 'Clock'
  }
  return icons[status] || 'Clock'
}

const getRunStatusText = (status) => {
  const texts = {
    success: '成功',
    failed: '失败',
    warning: '警告',
    running: '运行中'
  }
  return texts[status] || status
}

const getTestStatusTagType = (status) => {
  const types = {
    passed: 'success',
    failed: 'danger',
    skipped: 'info'
  }
  return types[status] || 'info'
}

const formatPercentage = (value) => {
  return value ? `${(value * 100).toFixed(1)}%` : '0%'
}

const formatDuration = (ms) => {
  if (!ms) return '-'
  if (ms < 1000) return `${ms}ms`
  return `${(ms / 1000).toFixed(1)}s`
}

const formatRelativeTime = (timestamp) => {
  if (!timestamp) return '-'
  const now = new Date()
  const time = new Date(timestamp)
  const diff = now - time
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return `${Math.floor(diff / 86400000)}天前`
}

// 生命周期
onMounted(() => {
  refreshTestSuites()
})
</script>

<style scoped>
.test-suite-manager {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.test-suites-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.test-suite-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
}

.test-suite-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.test-suite-card.disabled {
  opacity: 0.6;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px 20px 16px;
  border-bottom: 1px solid #f0f2f5;
}

.suite-info {
  flex: 1;
}

.suite-name {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.suite-meta {
  display: flex;
  gap: 8px;
}

.card-content {
  padding: 16px 20px;
}

.suite-description {
  margin: 0 0 16px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.test-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 16px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.stat-value {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.stat-value.success-rate {
  color: #67c23a;
}

.stat-value.warning-rate {
  color: #e6a23c;
}

.stat-value.danger-rate {
  color: #f56c6c;
}

.recent-run {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 16px;
}

.run-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
}

.run-time {
  font-size: 12px;
  color: #909399;
}

.config-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.config-item {
  font-size: 12px;
  color: #606266;
}

.config-label {
  font-weight: 500;
}

.config-value {
  color: #303133;
}

.card-footer {
  display: flex;
  gap: 8px;
  padding: 16px 20px;
  background: #fafbfc;
  border-top: 1px solid #f0f2f5;
}

.empty-state {
  grid-column: 1 / -1;
  padding: 60px 20px;
  text-align: center;
}

.env-vars {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
}

.env-var-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.env-var-item:last-child {
  margin-bottom: 0;
}

.test-results {
  padding: 20px 0;
}

.results-overview {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 24px;
}

.overview-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.overview-item.success {
  background: #f0f9ff;
  color: #67c23a;
}

.overview-item.failed {
  background: #fef0f0;
  color: #f56c6c;
}

.overview-item.skipped {
  background: #fdf6ec;
  color: #e6a23c;
}

.overview-value {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 4px;
}

.overview-label {
  font-size: 14px;
  opacity: 0.8;
}

.error-message {
  color: #f56c6c;
  font-size: 12px;
}

.success-message {
  color: #909399;
}
</style>