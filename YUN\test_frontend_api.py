#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试前端API调用
"""

import requests
import json
from datetime import datetime

def test_frontend_apis():
    """测试前端相关的API"""
    base_url = "http://127.0.0.1:8006"
    
    print(f"开始测试前端API - {datetime.now()}")
    print("=" * 50)
    
    # 首先测试登录获取真实token
    print("\n1. 测试登录获取token")
    login_url = f"{base_url}/api/test-login"
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        login_response = requests.post(login_url, json=login_data, timeout=10)
        print(f"登录状态码: {login_response.status_code}")
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            print(f"登录成功: {login_result.get('message', '')}")
            
            # 获取token
            token = login_result.get('access_token')
            if token:
                print(f"获取到token: {token[:20]}...")
                
                # 测试API端点
                test_endpoints_with_token(base_url, token)
            else:
                print("未获取到token，使用test_token测试")
                test_endpoints_with_token(base_url, "test_token")
        else:
            print(f"登录失败: {login_response.text}")
            print("使用test_token测试")
            test_endpoints_with_token(base_url, "test_token")
            
    except Exception as e:
        print(f"登录请求失败: {str(e)}")
        print("使用test_token测试")
        test_endpoints_with_token(base_url, "test_token")

def test_endpoints_with_token(base_url, token):
    """使用token测试API端点"""
    
    # 测试的API端点
    endpoints = [
        {
            "name": "标准量表列表",
            "url": "/api/clinical-scales/standard-assessments",
            "method": "GET"
        },
        {
            "name": "标准问卷列表", 
            "url": "/api/clinical-scales/standard-questionnaires",
            "method": "GET"
        }
    ]
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    for endpoint in endpoints:
        print(f"\n2. 测试: {endpoint['name']}")
        url = f"{base_url}{endpoint['url']}"
        print(f"URL: {url}")
        
        try:
            if endpoint['method'] == 'GET':
                response = requests.get(url, headers=headers, timeout=10)
            else:
                response = requests.post(url, headers=headers, timeout=10)
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"响应数据类型: {type(data)}")
                    
                    if isinstance(data, dict):
                        print(f"响应数据键: {list(data.keys())}")
                        
                        # 检查status字段
                        if 'status' in data:
                            print(f"状态: {data['status']}")
                        
                        # 检查data字段
                        if 'data' in data:
                            data_field = data['data']
                            print(f"data字段类型: {type(data_field)}")
                            
                            if isinstance(data_field, list):
                                print(f"data数组长度: {len(data_field)}")
                                
                                if len(data_field) > 0:
                                    first_item = data_field[0]
                                    if isinstance(first_item, dict):
                                        print(f"第一个元素的键: {list(first_item.keys())}")
                                        
                                        # 显示第一个元素的基本信息
                                        if 'name' in first_item:
                                            print(f"第一个元素名称: {first_item['name']}")
                                        if 'id' in first_item:
                                            print(f"第一个元素ID: {first_item['id']}")
                                        if 'question_count' in first_item:
                                            print(f"第一个元素问题数: {first_item['question_count']}")
                                else:
                                    print("data数组为空")
                            else:
                                print(f"data字段不是数组: {data_field}")
                        
                        # 检查total字段
                        if 'total' in data:
                            print(f"总数: {data['total']}")
                            
                    elif isinstance(data, list):
                        print(f"响应数组长度: {len(data)}")
                        if len(data) > 0 and isinstance(data[0], dict):
                            print(f"第一个元素键: {list(data[0].keys())}")
                    
                    print("✅ API调用成功")
                    
                except json.JSONDecodeError:
                    print(f"响应内容(非JSON): {response.text[:200]}...")
            else:
                print(f"❌ API调用失败: {response.text[:200]}...")
                
        except requests.exceptions.ConnectionError:
            print("❌ 连接失败 - 服务器可能未启动")
        except requests.exceptions.Timeout:
            print("❌ 请求超时")
        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")
        
        print("-" * 30)
    
    print("\n测试完成")

if __name__ == "__main__":
    test_frontend_apis()