#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库表结构检查功能
检查健康管理系统数据库中的表结构、数据完整性和关系

版本: 2.0
作者: Health Management System
创建时间: 2024
更新时间: 2024-12-30
"""

import os
import sys
import json
import asyncio
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入项目模块
from sqlalchemy import inspect, text, MetaData, Table
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session

from app.core.database_utils import get_session, get_db_manager
from app.core.logging_utils import get_logger
from app.core.error_handler import ErrorHandler
from app.core.response_handler import ResponseHandler
from app.models import *
from app.core.data_export import SUPPORTED_TABLES

logger = get_logger(__name__)

class DatabaseTableChecker:
    """数据库表结构检查器"""
    
    def __init__(self):
        """初始化检查器"""
        self.db_manager = get_db_manager()
        self.engine = self.db_manager.engine
        self.inspector = inspect(self.engine)
        self.metadata = MetaData()
        self.error_handler = ErrorHandler()
        self.response_handler = ResponseHandler()
        
        # 检查结果存储
        self.check_results = {
            'tables': {},
            'relationships': {},
            'indexes': {},
            'constraints': {},
            'data_integrity': {},
            'summary': {}
        }
        
        logger.info("数据库表结构检查器初始化完成")
    
    def get_all_tables(self) -> List[str]:
        """获取数据库中所有表名"""
        try:
            return self.inspector.get_table_names()
        except Exception as e:
            logger.error(f"获取表名失败: {str(e)}")
            return []
    
    def get_table_info(self, table_name: str) -> Dict[str, Any]:
        """获取表的详细信息"""
        try:
            # 获取列信息
            columns = self.inspector.get_columns(table_name)
            
            # 获取主键
            primary_keys = self.inspector.get_pk_constraint(table_name)
            
            # 获取外键
            foreign_keys = self.inspector.get_foreign_keys(table_name)
            
            # 获取索引
            indexes = self.inspector.get_indexes(table_name)
            
            # 获取唯一约束
            unique_constraints = self.inspector.get_unique_constraints(table_name)
            
            # 获取检查约束
            check_constraints = self.inspector.get_check_constraints(table_name)
            
            return {
                'columns': columns,
                'primary_keys': primary_keys,
                'foreign_keys': foreign_keys,
                'indexes': indexes,
                'unique_constraints': unique_constraints,
                'check_constraints': check_constraints,
                'column_count': len(columns)
            }
            
        except Exception as e:
            logger.error(f"获取表 {table_name} 信息失败: {str(e)}")
            return {}
    
    def check_table_structure(self, table_name: str) -> Dict[str, Any]:
        """检查单个表的结构"""
        try:
            table_info = self.get_table_info(table_name)
            
            if not table_info:
                return {
                    'status': 'error',
                    'message': '无法获取表信息',
                    'issues': ['表不存在或无法访问']
                }
            
            issues = []
            warnings = []
            
            # 检查是否有主键
            if not table_info['primary_keys']['constrained_columns']:
                issues.append('表没有定义主键')
            
            # 检查列的数据类型
            for column in table_info['columns']:
                col_name = column['name']
                col_type = str(column['type'])
                
                # 检查常见问题
                if 'VARCHAR' in col_type.upper() and 'VARCHAR()' in col_type.upper():
                    warnings.append(f'列 {col_name} 的VARCHAR长度未指定')
                
                if column['nullable'] and col_name.lower() in ['id', 'uuid', 'created_at']:
                    warnings.append(f'关键列 {col_name} 允许NULL值')
            
            # 检查索引
            indexed_columns = set()
            for index in table_info['indexes']:
                indexed_columns.update(index['column_names'])
            
            # 检查外键列是否有索引
            for fk in table_info['foreign_keys']:
                for col in fk['constrained_columns']:
                    if col not in indexed_columns:
                        warnings.append(f'外键列 {col} 没有索引，可能影响查询性能')
            
            status = 'error' if issues else ('warning' if warnings else 'ok')
            
            return {
                'status': status,
                'message': f'表结构检查完成，发现 {len(issues)} 个问题，{len(warnings)} 个警告',
                'issues': issues,
                'warnings': warnings,
                'details': table_info
            }
            
        except Exception as e:
            logger.error(f"检查表 {table_name} 结构失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'检查失败: {str(e)}',
                'issues': [str(e)]
            }
    
    def check_data_integrity(self, table_name: str) -> Dict[str, Any]:
        """检查表的数据完整性"""
        try:
            db = next(get_session())
            
            # 获取表信息
            table_info = self.get_table_info(table_name)
            if not table_info:
                return {
                    'status': 'error',
                    'message': '无法获取表信息'
                }
            
            issues = []
            warnings = []
            stats = {}
            
            # 获取记录总数
            try:
                result = db.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                total_count = result.scalar()
                stats['total_records'] = total_count
            except Exception as e:
                issues.append(f'无法获取记录总数: {str(e)}')
                total_count = 0
            
            if total_count > 0:
                # 检查NULL值
                null_checks = {}
                for column in table_info['columns']:
                    col_name = column['name']
                    if not column['nullable']:
                        try:
                            result = db.execute(text(f"SELECT COUNT(*) FROM {table_name} WHERE {col_name} IS NULL"))
                            null_count = result.scalar()
                            if null_count > 0:
                                issues.append(f'非空列 {col_name} 存在 {null_count} 个NULL值')
                            null_checks[col_name] = null_count
                        except Exception as e:
                            warnings.append(f'检查列 {col_name} NULL值失败: {str(e)}')
                
                stats['null_checks'] = null_checks
                
                # 检查重复值（对于唯一约束列）
                duplicate_checks = {}
                for constraint in table_info['unique_constraints']:
                    for col_name in constraint['column_names']:
                        try:
                            result = db.execute(text(f"""
                                SELECT COUNT(*) FROM (
                                    SELECT {col_name}, COUNT(*) as cnt 
                                    FROM {table_name} 
                                    WHERE {col_name} IS NOT NULL
                                    GROUP BY {col_name} 
                                    HAVING COUNT(*) > 1
                                ) duplicates
                            """))
                            duplicate_count = result.scalar()
                            if duplicate_count > 0:
                                issues.append(f'唯一约束列 {col_name} 存在 {duplicate_count} 组重复值')
                            duplicate_checks[col_name] = duplicate_count
                        except Exception as e:
                            warnings.append(f'检查列 {col_name} 重复值失败: {str(e)}')
                
                stats['duplicate_checks'] = duplicate_checks
                
                # 检查外键完整性
                fk_checks = {}
                for fk in table_info['foreign_keys']:
                    try:
                        constrained_cols = ', '.join(fk['constrained_columns'])
                        referred_table = fk['referred_table']
                        referred_cols = ', '.join(fk['referred_columns'])
                        
                        # 检查是否有孤立记录
                        result = db.execute(text(f"""
                            SELECT COUNT(*) FROM {table_name} t1
                            LEFT JOIN {referred_table} t2 ON t1.{constrained_cols} = t2.{referred_cols}
                            WHERE t1.{constrained_cols} IS NOT NULL AND t2.{referred_cols} IS NULL
                        """))
                        orphan_count = result.scalar()
                        
                        if orphan_count > 0:
                            issues.append(f'外键 {constrained_cols} 存在 {orphan_count} 个孤立记录')
                        
                        fk_checks[f"{constrained_cols} -> {referred_table}.{referred_cols}"] = orphan_count
                        
                    except Exception as e:
                        warnings.append(f'检查外键完整性失败: {str(e)}')
                
                stats['foreign_key_checks'] = fk_checks
            
            status = 'error' if issues else ('warning' if warnings else 'ok')
            
            return {
                'status': status,
                'message': f'数据完整性检查完成，发现 {len(issues)} 个问题，{len(warnings)} 个警告',
                'issues': issues,
                'warnings': warnings,
                'statistics': stats
            }
            
        except Exception as e:
            logger.error(f"检查表 {table_name} 数据完整性失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'检查失败: {str(e)}',
                'issues': [str(e)]
            }
        finally:
            db.close()
    
    def check_relationships(self) -> Dict[str, Any]:
        """检查表之间的关系"""
        try:
            all_tables = self.get_all_tables()
            relationships = defaultdict(list)
            issues = []
            warnings = []
            
            # 收集所有外键关系
            for table_name in all_tables:
                try:
                    foreign_keys = self.inspector.get_foreign_keys(table_name)
                    for fk in foreign_keys:
                        relationship = {
                            'from_table': table_name,
                            'from_columns': fk['constrained_columns'],
                            'to_table': fk['referred_table'],
                            'to_columns': fk['referred_columns'],
                            'constraint_name': fk.get('name', 'unnamed')
                        }
                        relationships[table_name].append(relationship)
                        
                        # 检查被引用的表是否存在
                        if fk['referred_table'] not in all_tables:
                            issues.append(f"表 {table_name} 引用了不存在的表 {fk['referred_table']}")
                        
                except Exception as e:
                    warnings.append(f"获取表 {table_name} 的外键信息失败: {str(e)}")
            
            # 检查循环依赖
            def has_cycle(graph, start, visited, rec_stack):
                visited[start] = True
                rec_stack[start] = True
                
                for neighbor in graph.get(start, []):
                    if not visited.get(neighbor, False):
                        if has_cycle(graph, neighbor, visited, rec_stack):
                            return True
                    elif rec_stack.get(neighbor, False):
                        return True
                
                rec_stack[start] = False
                return False
            
            # 构建依赖图
            dependency_graph = {}
            for table_name, fks in relationships.items():
                dependency_graph[table_name] = [fk['to_table'] for fk in fks]
            
            # 检查循环依赖
            visited = {}
            rec_stack = {}
            for table in all_tables:
                if not visited.get(table, False):
                    if has_cycle(dependency_graph, table, visited, rec_stack):
                        warnings.append(f"检测到循环依赖，涉及表: {table}")
            
            status = 'error' if issues else ('warning' if warnings else 'ok')
            
            return {
                'status': status,
                'message': f'关系检查完成，发现 {len(issues)} 个问题，{len(warnings)} 个警告',
                'issues': issues,
                'warnings': warnings,
                'relationships': dict(relationships),
                'dependency_graph': dependency_graph
            }
            
        except Exception as e:
            logger.error(f"检查表关系失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'检查失败: {str(e)}',
                'issues': [str(e)]
            }
    
    def check_indexes_performance(self) -> Dict[str, Any]:
        """检查索引和性能相关问题"""
        try:
            all_tables = self.get_all_tables()
            issues = []
            warnings = []
            index_stats = {}
            
            for table_name in all_tables:
                try:
                    table_info = self.get_table_info(table_name)
                    indexes = table_info.get('indexes', [])
                    foreign_keys = table_info.get('foreign_keys', [])
                    
                    # 统计索引信息
                    index_count = len(indexes)
                    indexed_columns = set()
                    for index in indexes:
                        indexed_columns.update(index['column_names'])
                    
                    index_stats[table_name] = {
                        'index_count': index_count,
                        'indexed_columns': list(indexed_columns),
                        'total_columns': table_info.get('column_count', 0)
                    }
                    
                    # 检查外键列是否有索引
                    for fk in foreign_keys:
                        for col in fk['constrained_columns']:
                            if col not in indexed_columns:
                                warnings.append(f"表 {table_name} 的外键列 {col} 没有索引")
                    
                    # 检查是否有过多的索引
                    if index_count > 10:
                        warnings.append(f"表 {table_name} 有 {index_count} 个索引，可能过多")
                    
                    # 检查是否缺少索引
                    if index_count == 0 and table_info.get('column_count', 0) > 1:
                        warnings.append(f"表 {table_name} 没有任何索引")
                    
                except Exception as e:
                    warnings.append(f"检查表 {table_name} 索引失败: {str(e)}")
            
            status = 'error' if issues else ('warning' if warnings else 'ok')
            
            return {
                'status': status,
                'message': f'索引检查完成，发现 {len(issues)} 个问题，{len(warnings)} 个警告',
                'issues': issues,
                'warnings': warnings,
                'index_statistics': index_stats
            }
            
        except Exception as e:
            logger.error(f"检查索引性能失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'检查失败: {str(e)}',
                'issues': [str(e)]
            }
    
    def check_supported_tables(self) -> Dict[str, Any]:
        """检查支持的表是否存在"""
        try:
            all_tables = self.get_all_tables()
            issues = []
            warnings = []
            
            # 检查SUPPORTED_TABLES中的表是否都存在
            missing_tables = []
            existing_tables = []
            
            for table_name in SUPPORTED_TABLES:
                if table_name in all_tables:
                    existing_tables.append(table_name)
                else:
                    missing_tables.append(table_name)
                    issues.append(f"支持列表中的表 {table_name} 在数据库中不存在")
            
            # 检查数据库中是否有未在支持列表中的表
            unsupported_tables = []
            for table_name in all_tables:
                if table_name not in SUPPORTED_TABLES:
                    unsupported_tables.append(table_name)
                    warnings.append(f"数据库中的表 {table_name} 未在支持列表中")
            
            status = 'error' if issues else ('warning' if warnings else 'ok')
            
            return {
                'status': status,
                'message': f'支持表检查完成，发现 {len(issues)} 个问题，{len(warnings)} 个警告',
                'issues': issues,
                'warnings': warnings,
                'statistics': {
                    'total_supported': len(SUPPORTED_TABLES),
                    'existing_supported': len(existing_tables),
                    'missing_supported': len(missing_tables),
                    'total_database': len(all_tables),
                    'unsupported_in_db': len(unsupported_tables)
                },
                'details': {
                    'existing_tables': existing_tables,
                    'missing_tables': missing_tables,
                    'unsupported_tables': unsupported_tables
                }
            }
            
        except Exception as e:
            logger.error(f"检查支持表失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'检查失败: {str(e)}',
                'issues': [str(e)]
            }
    
    async def run_all_checks(self) -> Dict[str, Any]:
        """运行所有检查"""
        logger.info("开始运行数据库表结构检查...")
        
        try:
            all_tables = self.get_all_tables()
            logger.info(f"发现 {len(all_tables)} 个表")
            
            # 1. 检查支持的表
            logger.info("检查支持的表...")
            self.check_results['supported_tables'] = self.check_supported_tables()
            
            # 2. 检查每个表的结构
            logger.info("检查表结构...")
            for table_name in all_tables:
                self.check_results['tables'][table_name] = self.check_table_structure(table_name)
            
            # 3. 检查数据完整性（只检查支持的表）
            logger.info("检查数据完整性...")
            for table_name in SUPPORTED_TABLES:
                if table_name in all_tables:
                    self.check_results['data_integrity'][table_name] = self.check_data_integrity(table_name)
            
            # 4. 检查表关系
            logger.info("检查表关系...")
            self.check_results['relationships'] = self.check_relationships()
            
            # 5. 检查索引性能
            logger.info("检查索引性能...")
            self.check_results['indexes'] = self.check_indexes_performance()
            
            # 6. 生成总结
            self.generate_summary()
            
            logger.info("所有检查完成")
            return self.check_results
            
        except Exception as e:
            logger.error(f"运行检查失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'检查失败: {str(e)}',
                'error': str(e)
            }
    
    def generate_summary(self):
        """生成检查总结"""
        try:
            total_issues = 0
            total_warnings = 0
            
            # 统计各类检查的问题数量
            for category, results in self.check_results.items():
                if category == 'summary':
                    continue
                
                if isinstance(results, dict):
                    if 'issues' in results:
                        total_issues += len(results['issues'])
                    if 'warnings' in results:
                        total_warnings += len(results['warnings'])
                    
                    # 处理嵌套结果
                    for key, value in results.items():
                        if isinstance(value, dict) and 'issues' in value:
                            total_issues += len(value['issues'])
                        if isinstance(value, dict) and 'warnings' in value:
                            total_warnings += len(value['warnings'])
            
            # 确定整体状态
            overall_status = 'ok'
            if total_issues > 0:
                overall_status = 'error'
            elif total_warnings > 0:
                overall_status = 'warning'
            
            self.check_results['summary'] = {
                'overall_status': overall_status,
                'total_issues': total_issues,
                'total_warnings': total_warnings,
                'check_time': datetime.now().isoformat(),
                'tables_checked': len(self.check_results.get('tables', {})),
                'data_integrity_checked': len(self.check_results.get('data_integrity', {})),
                'message': f'检查完成：{total_issues} 个问题，{total_warnings} 个警告'
            }
            
        except Exception as e:
            logger.error(f"生成总结失败: {str(e)}")
            self.check_results['summary'] = {
                'overall_status': 'error',
                'message': f'生成总结失败: {str(e)}',
                'check_time': datetime.now().isoformat()
            }
    
    def save_report(self, output_file: str = None) -> str:
        """保存检查报告"""
        try:
            if not output_file:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_file = f"database_check_report_{timestamp}.json"
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.check_results, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info(f"检查报告已保存到: {output_file}")
            return output_file
            
        except Exception as e:
            logger.error(f"保存报告失败: {str(e)}")
            return ""
    
    def print_summary(self):
        """打印检查总结"""
        try:
            summary = self.check_results.get('summary', {})
            
            print("\n" + "="*80)
            print("数据库表结构检查报告")
            print("="*80)
            
            print(f"检查时间: {summary.get('check_time', 'Unknown')}")
            print(f"整体状态: {summary.get('overall_status', 'Unknown')}")
            print(f"检查表数: {summary.get('tables_checked', 0)}")
            print(f"数据完整性检查: {summary.get('data_integrity_checked', 0)}")
            print(f"发现问题: {summary.get('total_issues', 0)}")
            print(f"发现警告: {summary.get('total_warnings', 0)}")
            
            # 打印各类检查的状态
            print("\n检查详情:")
            print("-" * 40)
            
            status_icons = {'ok': '✅', 'warning': '⚠️', 'error': '❌'}
            
            for category, results in self.check_results.items():
                if category == 'summary':
                    continue
                
                if isinstance(results, dict) and 'status' in results:
                    icon = status_icons.get(results['status'], '❓')
                    print(f"{icon} {category}: {results.get('message', 'No message')}")
            
            print("\n" + "="*80)
            
            # 如果有严重问题，给出建议
            if summary.get('total_issues', 0) > 0:
                print("⚠️  发现严重问题，建议：")
                print("   1. 检查详细报告中的具体问题")
                print("   2. 修复数据完整性问题")
                print("   3. 确保所有必要的表和约束都存在")
            elif summary.get('total_warnings', 0) > 0:
                print("💡 发现一些警告，建议：")
                print("   1. 检查性能相关的警告")
                print("   2. 考虑优化索引配置")
                print("   3. 审查表结构设计")
            else:
                print("🎉 数据库结构检查通过，没有发现问题！")
            
        except Exception as e:
            logger.error(f"打印总结失败: {str(e)}")
            print(f"打印总结失败: {str(e)}")

async def main():
    """主函数"""
    print("="*80)
    print("健康管理系统 - 数据库表结构检查工具")
    print("="*80)
    
    checker = DatabaseTableChecker()
    
    try:
        # 运行所有检查
        results = await checker.run_all_checks()
        
        # 打印总结
        checker.print_summary()
        
        # 保存详细报告
        report_file = checker.save_report()
        if report_file:
            print(f"\n📄 详细报告已保存到: {report_file}")
        
        # 根据检查结果返回适当的退出码
        summary = results.get('summary', {})
        if summary.get('overall_status') == 'error':
            print("\n❌ 检查发现严重问题，退出码: 1")
            return 1
        elif summary.get('overall_status') == 'warning':
            print("\n⚠️  检查发现警告，退出码: 0（但请注意警告）")
            return 0
        else:
            print("\n✅ 检查通过，退出码: 0")
            return 0
            
    except Exception as e:
        logger.error(f"检查执行失败: {str(e)}")
        print(f"\n❌ 检查执行失败: {str(e)}")
        return 1

if __name__ == "__main__":
    # 运行检查
    exit_code = asyncio.run(main())
    sys.exit(exit_code)