# 健康管理系统移动端API请求格式文档

本文档详细说明了移动端与后端交互时的API请求格式，包括用户认证、文件上传等操作。

## 基础URL

```
https://api.example.com/api
```

## 1. 用户认证相关

### 1.1 用户登录

**请求路径**：`/auth/login`  
**请求方法**：`POST`  
**请求格式**：表单数据（Form Data）

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| username | string | 是 | 用户名 |
| password | string | 是 | 密码（明文） |
| timestamp | integer | 否 | 当前时间戳（秒） |

**请求示例**：

```python
# 使用表单格式
form_data = {
    "username": "markey",
    "password": "your_password"
}

# 发送请求
response = requests.post("https://api.example.com/api/auth/login", data=form_data)
```

**成功响应示例**：

```json
{
    "success": true,
    "data": {
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "expires_at": 1651234567,
        "user_id": "user_12345"
    }
}
```

### 1.2 用户注册

**请求路径**：`/auth/register`  
**请求方法**：`POST`  
**请求格式**：JSON

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| username | string | 是 | 用户名 |
| password_hash | string | 是 | 密码的哈希值 |
| email | string | 是 | 电子邮箱 |
| full_name | string | 否 | 用户全名 |
| timestamp | integer | 否 | 当前时间戳（秒） |

**请求示例**：

```python
json_data = {
    "username": "newuser",
    "password_hash": "hashedpassword123",
    "email": "<EMAIL>",
    "full_name": "New User",
    "timestamp": int(time.time())
}

response = requests.post("https://api.example.com/api/auth/register", json=json_data)
```

**成功响应示例**：

```json
{
    "success": true,
    "data": {
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "expires_at": 1651234567,
        "user_id": "user_12345",
        "username": "newuser"
    }
}
```

### 1.3 刷新Token

**请求路径**：`/auth/refresh`  
**请求方法**：`POST`  
**请求格式**：JSON

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| refresh_token | string | 是 | 刷新令牌 |

**请求头**：

```
Authorization: Bearer {access_token}
```

**请求示例**：

```python
headers = {
    "Authorization": f"Bearer {token}"
}

json_data = {
    "refresh_token": refresh_token_str
}

response = requests.post("https://api.example.com/api/auth/refresh", json=json_data, headers=headers)
```

**成功响应示例**：

```json
{
    "success": true,
    "data": {
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "expires_at": 1651234567
    }
}
```

### 1.4 退出登录

**请求路径**：`/auth/logout`  
**请求方法**：`POST`  
**请求格式**：无参数

**请求头**：

```
Authorization: Bearer {access_token}
```

**请求示例**：

```python
headers = {
    "Authorization": f"Bearer {token}"
}

response = requests.post("https://api.example.com/api/auth/logout", headers=headers)
```

**成功响应示例**：

```json
{
    "success": true
}
```

## 2. 用户管理相关

### 2.1 获取当前用户信息

**请求路径**：`/users/me`  
**请求方法**：`GET`  
**请求格式**：无参数

**请求头**：

```
Authorization: Bearer {access_token}
```

**请求示例**：

```python
headers = {
    "Authorization": f"Bearer {token}"
}

response = requests.get("https://api.example.com/api/users/me", headers=headers)
```

**成功响应示例**：

```json
{
    "success": true,
    "data": {
        "id": "user_12345",
        "username": "markey",
        "email": "<EMAIL>",
        "full_name": "马琦",
        "is_active": true,
        "created_at": "2023-01-01T12:00:00Z"
    }
}
```

## 3. 文件上传相关

### 3.1 上传文件

**请求路径**：`/api/mobile/upload`  
**请求方法**：`POST`  
**请求格式**：表单数据（multipart/form-data）

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| file | file | 是 | 要上传的文件 |
| file_type | string | 否 | 文件类型 |
| documentType | string | 否 | 文档类型 |
| description | string | 否 | 文件描述 |

**请求头**：

```
Authorization: Bearer {access_token}
Content-Type: multipart/form-data
```

**请求示例**：

```python
import requests

headers = {
    "Authorization": f"Bearer {token}"
}

files = {
    'file': ('test.jpg', open('test.jpg', 'rb'), 'image/jpeg')
}

data = {
    'file_type': 'image',
    'documentType': 'medical_report',
    'description': '2023年体检报告'
}

response = requests.post('https://api.example.com/api/mobile/upload', 
                       headers=headers, 
                       files=files, 
                       data=data)
```

**成功响应示例**：

```json
{
    "success": true,
    "data": {
        "file_id": "f_a1b2c3d4e5f67890",
        "filename": "test.jpg",
        "file_size": 1024000,
        "file_type": "jpg",
        "created_at": "2023-04-01T10:30:00Z",
        "mime_type": "image/jpeg"
    }
}
```

### 3.2 请求OCR处理

**请求路径**：`/api/documents/{file_id}/ocr`  
**请求方法**：`POST`  
**请求格式**：JSON（可选）

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| options | object | 否 | OCR处理选项 |

**请求路径参数**：

| 参数名 | 类型 | 描述 |
|--------|------|------|
| file_id | string | 文件ID（格式：f_xxxx） |

**请求头**：

```
Authorization: Bearer {access_token}
```

**请求示例**：

```python
headers = {
    "Authorization": f"Bearer {token}"
}

options = {
    "language": "zh-CN",
    "ocr_type": "general"
}

response = requests.post(f"https://api.example.com/api/api/documents/f_a1b2c3d4e5f67890/ocr", 
                       json=options, 
                       headers=headers)
```

**成功响应示例**：

```json
{
    "success": true,
    "data": {
        "task_id": "ocr_task_a1b2c3d4-e5f6-7890-abcd-ef1234567890",
        "status": "processing",
        "file_id": "f_a1b2c3d4e5f67890"
    }
}
```

### 3.3 获取OCR结果

**请求路径**：`/api/documents/{file_id}/ocr`  
**请求方法**：`GET`  
**请求格式**：无参数

**请求路径参数**：

| 参数名 | 类型 | 描述 |
|--------|------|------|
| file_id | string | 文件ID（格式：f_xxxx） |

**请求头**：

```
Authorization: Bearer {access_token}
```

**请求示例**：

```python
headers = {
    "Authorization": f"Bearer {token}"
}

response = requests.get(f"https://api.example.com/api/api/documents/f_a1b2c3d4e5f67890/ocr", 
                      headers=headers)
```

**成功响应示例**：

```json
{
    "success": true,
    "data": {
        "status": "completed",
        "file_id": "f_a1b2c3d4e5f67890",
        "text": "这是OCR识别出的文本内容...",
        "created_at": "2023-04-01T10:35:00Z"
    }
}
```

## 4. 健康数据相关API

系统还提供了以下健康数据相关API，详细格式及参数可参考后端API文档：

- 健康记录（/api/health-records）
- 健康日记（/api/health-diaries）
- 问卷调查（/api/questionnaires）
- 健康状态概览（/api/health-status-overview）
- 评估量表（/api/assessment-scales）

## 5. 通用响应格式

所有API响应都遵循以下格式：

```json
{
    "success": true|false,
    "data": { ... },  // 成功时返回的数据
    "message": "..."  // 失败时的错误消息
}
```

## 6. 错误处理

当API请求失败时，响应中的`success`字段为`false`，并在`message`字段中提供错误信息。

### 常见错误码：

- 400: 请求参数错误
- 401: 未认证或认证失败
- 403: 权限不足
- 404: 资源不存在
- 500: 服务器内部错误

**错误响应示例**：

```json
{
    "success": false,
    "message": "用户名或密码错误"
}
```