<template>
  <div class="test-report-generator">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>测试报告生成器</h2>
      <p>生成和管理各种类型的测试报告，支持多种格式导出</p>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" icon="DocumentAdd" @click="showCreateDialog = true">
          生成报告
        </el-button>
        <el-button icon="Refresh" @click="loadReports">
          刷新
        </el-button>
        <el-button icon="Download" @click="batchExport" :disabled="selectedReports.length === 0">
          批量导出
        </el-button>
        <el-button icon="Delete" @click="batchDelete" :disabled="selectedReports.length === 0">
          批量删除
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索报告名称或描述"
          prefix-icon="Search"
          style="width: 300px"
          clearable
        />
        <el-select v-model="filterType" placeholder="报告类型" style="width: 150px; margin-left: 10px">
          <el-option label="全部" value="" />
          <el-option label="测试执行" value="execution" />
          <el-option label="覆盖率" value="coverage" />
          <el-option label="性能" value="performance" />
          <el-option label="趋势分析" value="trend" />
          <el-option label="对比分析" value="comparison" />
        </el-select>
        <el-select v-model="filterStatus" placeholder="状态" style="width: 120px; margin-left: 10px">
          <el-option label="全部" value="" />
          <el-option label="生成中" value="generating" />
          <el-option label="已完成" value="completed" />
          <el-option label="失败" value="failed" />
        </el-select>
      </div>
    </div>

    <!-- 报告列表 -->
    <div class="reports-container">
      <el-table
        :data="filteredReports"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="name" label="报告名称" min-width="200">
          <template #default="{ row }">
            <div class="report-name">
              <el-icon class="report-icon">
                <Document v-if="row.type === 'execution'" />
                <PieChart v-else-if="row.type === 'coverage'" />
                <TrendCharts v-else-if="row.type === 'performance'" />
                <DataAnalysis v-else-if="row.type === 'trend'" />
                <DataBoard v-else />
              </el-icon>
              <span>{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="type" label="类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeColor(row.type)" size="small">
              {{ getTypeLabel(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)" size="small">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="format" label="格式" width="100">
          <template #default="{ row }">
            <el-tag v-for="format in row.formats" :key="format" size="small" style="margin-right: 4px">
              {{ format.toUpperCase() }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="size" label="文件大小" width="100">
          <template #default="{ row }">
            <span>{{ formatFileSize(row.size) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="{ row }">
            <span>{{ formatDate(row.createdAt) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="creator" label="创建者" width="120" />
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              icon="View"
              @click="previewReport(row)"
              :disabled="row.status !== 'completed'"
            >
              预览
            </el-button>
            <el-button
              type="success"
              size="small"
              icon="Download"
              @click="downloadReport(row)"
              :disabled="row.status !== 'completed'"
            >
              下载
            </el-button>
            <el-button
              type="info"
              size="small"
              icon="Share"
              @click="shareReport(row)"
              :disabled="row.status !== 'completed'"
            >
              分享
            </el-button>
            <el-button
              type="danger"
              size="small"
              icon="Delete"
              @click="deleteReport(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="totalReports"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 生成报告对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="生成测试报告"
      width="800px"
      @close="resetCreateForm"
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="报告名称" prop="name">
              <el-input v-model="createForm.name" placeholder="请输入报告名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报告类型" prop="type">
              <el-select v-model="createForm.type" placeholder="选择报告类型" @change="onTypeChange">
                <el-option label="测试执行报告" value="execution" />
                <el-option label="覆盖率报告" value="coverage" />
                <el-option label="性能测试报告" value="performance" />
                <el-option label="趋势分析报告" value="trend" />
                <el-option label="对比分析报告" value="comparison" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="描述">
          <el-input
            v-model="createForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入报告描述"
          />
        </el-form-item>

        <el-form-item label="数据源" prop="dataSource">
          <el-select v-model="createForm.dataSource" placeholder="选择数据源" multiple>
            <el-option
              v-for="source in availableDataSources"
              :key="source.id"
              :label="source.name"
              :value="source.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="时间范围">
          <el-date-picker
            v-model="createForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>

        <el-form-item label="输出格式">
          <el-checkbox-group v-model="createForm.formats">
            <el-checkbox label="pdf">PDF</el-checkbox>
            <el-checkbox label="html">HTML</el-checkbox>
            <el-checkbox label="excel">Excel</el-checkbox>
            <el-checkbox label="json">JSON</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="包含内容">
          <el-checkbox-group v-model="createForm.sections">
            <el-checkbox label="summary">执行摘要</el-checkbox>
            <el-checkbox label="details">详细结果</el-checkbox>
            <el-checkbox label="charts">图表分析</el-checkbox>
            <el-checkbox label="recommendations">改进建议</el-checkbox>
            <el-checkbox label="appendix">附录</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="高级选项">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-checkbox v-model="createForm.includeScreenshots">
                包含截图
              </el-checkbox>
            </el-col>
            <el-col :span="8">
              <el-checkbox v-model="createForm.includeLogs">
                包含日志
              </el-checkbox>
            </el-col>
            <el-col :span="8">
              <el-checkbox v-model="createForm.autoSchedule">
                定时生成
              </el-checkbox>
            </el-col>
          </el-row>
        </el-form-item>

        <el-form-item v-if="createForm.autoSchedule" label="生成频率">
          <el-select v-model="createForm.schedule" placeholder="选择生成频率">
            <el-option label="每日" value="daily" />
            <el-option label="每周" value="weekly" />
            <el-option label="每月" value="monthly" />
            <el-option label="自定义" value="custom" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="generateReport" :loading="generating">
          生成报告
        </el-button>
      </template>
    </el-dialog>

    <!-- 报告预览对话框 -->
    <el-dialog
      v-model="showPreviewDialog"
      :title="`预览报告: ${currentReport?.name}`"
      width="90%"
      top="5vh"
    >
      <div class="report-preview">
        <div v-if="currentReport?.type === 'execution'" class="execution-preview">
          <h3>测试执行摘要</h3>
          <div class="summary-stats">
            <div class="stat-item">
              <span class="label">总测试数:</span>
              <span class="value">{{ previewData.totalTests }}</span>
            </div>
            <div class="stat-item">
              <span class="label">通过数:</span>
              <span class="value success">{{ previewData.passedTests }}</span>
            </div>
            <div class="stat-item">
              <span class="label">失败数:</span>
              <span class="value danger">{{ previewData.failedTests }}</span>
            </div>
            <div class="stat-item">
              <span class="label">成功率:</span>
              <span class="value">{{ previewData.successRate }}%</span>
            </div>
          </div>
          
          <div class="chart-container">
            <div ref="executionChartRef" style="width: 100%; height: 300px;"></div>
          </div>
        </div>

        <div v-else-if="currentReport?.type === 'coverage'" class="coverage-preview">
          <h3>代码覆盖率报告</h3>
          <div class="coverage-stats">
            <div class="coverage-item">
              <span class="label">行覆盖率:</span>
              <el-progress :percentage="previewData.lineCoverage" />
            </div>
            <div class="coverage-item">
              <span class="label">分支覆盖率:</span>
              <el-progress :percentage="previewData.branchCoverage" />
            </div>
            <div class="coverage-item">
              <span class="label">函数覆盖率:</span>
              <el-progress :percentage="previewData.functionCoverage" />
            </div>
          </div>
          
          <div class="chart-container">
            <div ref="coverageChartRef" style="width: 100%; height: 300px;"></div>
          </div>
        </div>

        <div v-else-if="currentReport?.type === 'performance'" class="performance-preview">
          <h3>性能测试报告</h3>
          <div class="performance-stats">
            <div class="perf-item">
              <span class="label">平均响应时间:</span>
              <span class="value">{{ previewData.avgResponseTime }}ms</span>
            </div>
            <div class="perf-item">
              <span class="label">最大响应时间:</span>
              <span class="value">{{ previewData.maxResponseTime }}ms</span>
            </div>
            <div class="perf-item">
              <span class="label">吞吐量:</span>
              <span class="value">{{ previewData.throughput }} req/s</span>
            </div>
          </div>
          
          <div class="chart-container">
            <div ref="performanceChartRef" style="width: 100%; height: 300px;"></div>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="showPreviewDialog = false">关闭</el-button>
        <el-button type="primary" @click="downloadReport(previewReport)">
          下载报告
        </el-button>
      </template>
    </el-dialog>

    <!-- 分享对话框 -->
    <el-dialog v-model="showShareDialog" title="分享报告" width="500px">
      <div class="share-options">
        <h4>分享链接</h4>
        <el-input
          v-model="shareLink"
          readonly
          style="margin-bottom: 15px"
        >
          <template #append>
            <el-button @click="copyShareLink">复制</el-button>
          </template>
        </el-input>

        <h4>分享设置</h4>
        <el-form label-width="100px">
          <el-form-item label="访问权限">
            <el-radio-group v-model="shareSettings.permission">
              <el-radio label="public">公开访问</el-radio>
              <el-radio label="team">团队成员</el-radio>
              <el-radio label="private">仅限邀请</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="有效期">
            <el-select v-model="shareSettings.expiry">
              <el-option label="永久" value="never" />
              <el-option label="7天" value="7d" />
              <el-option label="30天" value="30d" />
              <el-option label="90天" value="90d" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="允许下载">
            <el-switch v-model="shareSettings.allowDownload" />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="showShareDialog = false">取消</el-button>
        <el-button type="primary" @click="updateShareSettings">
          更新设置
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as echarts from 'echarts'
import { testApi } from '@/api/test'

// 响应式数据
const reports = ref([])
const selectedReports = ref([])
const searchKeyword = ref('')
const filterType = ref('')
const filterStatus = ref('')
const loading = ref(false)
const generating = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const totalReports = ref(0)

// 对话框状态
const showCreateDialog = ref(false)
const showPreviewDialog = ref(false)
const showShareDialog = ref(false)

// 表单引用
const createFormRef = ref()

// 图表引用
const executionChartRef = ref()
const coverageChartRef = ref()
const performanceChartRef = ref()

// 预览数据
const currentReport = ref(null)
const previewData = ref({})

// 分享数据
const shareLink = ref('')
const shareSettings = reactive({
  permission: 'team',
  expiry: '30d',
  allowDownload: true
})

// 创建表单
const createForm = reactive({
  name: '',
  type: 'execution',
  description: '',
  dataSource: [],
  dateRange: [],
  formats: ['pdf'],
  sections: ['summary', 'details'],
  includeScreenshots: false,
  includeLogs: false,
  autoSchedule: false,
  schedule: 'weekly'
})

// 表单验证规则
const createRules = {
  name: [
    { required: true, message: '请输入报告名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择报告类型', trigger: 'change' }
  ],
  dataSource: [
    { required: true, message: '请选择数据源', trigger: 'change' }
  ]
}

// 可用数据源
const availableDataSources = ref([
  { id: 'test-suite-1', name: '用户模块测试套件' },
  { id: 'test-suite-2', name: 'API接口测试套件' },
  { id: 'test-suite-3', name: '性能测试套件' },
  { id: 'coverage-1', name: '代码覆盖率数据' },
  { id: 'history-1', name: '历史测试数据' }
])

// 计算属性
const filteredReports = computed(() => {
  let filtered = reports.value

  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(report =>
      report.name.toLowerCase().includes(keyword) ||
      (report.description && report.description.toLowerCase().includes(keyword))
    )
  }

  if (filterType.value) {
    filtered = filtered.filter(report => report.type === filterType.value)
  }

  if (filterStatus.value) {
    filtered = filtered.filter(report => report.status === filterStatus.value)
  }

  return filtered
})

// 方法
const loadReports = async () => {
  try {
    loading.value = true
    
    // 模拟加载报告数据
    reports.value = [
      {
        id: '1',
        name: '用户模块测试执行报告',
        type: 'execution',
        status: 'completed',
        formats: ['pdf', 'html'],
        size: 2048576,
        createdAt: new Date('2024-01-15'),
        creator: '张三',
        description: '用户注册、登录、权限管理功能的测试执行报告'
      },
      {
        id: '2',
        name: '代码覆盖率分析报告',
        type: 'coverage',
        status: 'completed',
        formats: ['html', 'json'],
        size: 1536000,
        createdAt: new Date('2024-01-14'),
        creator: '李四',
        description: '整体代码覆盖率分析和改进建议'
      },
      {
        id: '3',
        name: 'API性能测试报告',
        type: 'performance',
        status: 'generating',
        formats: ['pdf'],
        size: 0,
        createdAt: new Date('2024-01-16'),
        creator: '王五',
        description: 'API接口性能压测结果分析'
      }
    ]
    
    totalReports.value = reports.value.length
  } catch (error) {
    ElMessage.error('加载报告失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const getTypeColor = (type) => {
  const colors = {
    execution: 'primary',
    coverage: 'success',
    performance: 'warning',
    trend: 'info',
    comparison: 'danger'
  }
  return colors[type] || 'info'
}

const getTypeLabel = (type) => {
  const labels = {
    execution: '执行报告',
    coverage: '覆盖率',
    performance: '性能',
    trend: '趋势分析',
    comparison: '对比分析'
  }
  return labels[type] || type
}

const getStatusColor = (status) => {
  const colors = {
    generating: 'warning',
    completed: 'success',
    failed: 'danger'
  }
  return colors[status] || 'info'
}

const getStatusLabel = (status) => {
  const labels = {
    generating: '生成中',
    completed: '已完成',
    failed: '失败'
  }
  return labels[status] || status
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleString('zh-CN')
}

const handleSelectionChange = (selection) => {
  selectedReports.value = selection
}

const handleSizeChange = (size) => {
  pageSize.value = size
  loadReports()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadReports()
}

const onTypeChange = (type) => {
  // 根据报告类型调整可用数据源
  if (type === 'coverage') {
    createForm.dataSource = ['coverage-1']
  } else if (type === 'performance') {
    createForm.dataSource = ['test-suite-3']
  } else {
    createForm.dataSource = []
  }
}

const generateReport = async () => {
  try {
    await createFormRef.value.validate()
    generating.value = true

    // 模拟报告生成
    const newReport = {
      id: Date.now().toString(),
      name: createForm.name,
      type: createForm.type,
      status: 'generating',
      formats: createForm.formats,
      size: 0,
      createdAt: new Date(),
      creator: '当前用户',
      description: createForm.description
    }

    reports.value.unshift(newReport)
    
    // 模拟生成过程
    setTimeout(() => {
      newReport.status = 'completed'
      newReport.size = Math.floor(Math.random() * 5000000) + 1000000
      ElMessage.success('报告生成完成')
    }, 3000)

    showCreateDialog.value = false
    resetCreateForm()
    ElMessage.success('报告生成任务已启动')
  } catch (error) {
    ElMessage.error('生成报告失败: ' + error.message)
  } finally {
    generating.value = false
  }
}

const resetCreateForm = () => {
  Object.assign(createForm, {
    name: '',
    type: 'execution',
    description: '',
    dataSource: [],
    dateRange: [],
    formats: ['pdf'],
    sections: ['summary', 'details'],
    includeScreenshots: false,
    includeLogs: false,
    autoSchedule: false,
    schedule: 'weekly'
  })
  createFormRef.value?.clearValidate()
}

const previewReport = async (report) => {
  try {
    currentReport.value = report
    
    // 模拟加载预览数据
    if (report.type === 'execution') {
      previewData.value = {
        totalTests: 156,
        passedTests: 142,
        failedTests: 14,
        successRate: 91.0
      }
    } else if (report.type === 'coverage') {
      previewData.value = {
        lineCoverage: 85.6,
        branchCoverage: 78.3,
        functionCoverage: 92.1
      }
    } else if (report.type === 'performance') {
      previewData.value = {
        avgResponseTime: 245,
        maxResponseTime: 1250,
        throughput: 1850
      }
    }
    
    showPreviewDialog.value = true
    
    // 等待DOM更新后渲染图表
    await nextTick()
    renderPreviewCharts(report.type)
  } catch (error) {
    ElMessage.error('加载预览失败: ' + error.message)
  }
}

const renderPreviewCharts = (type) => {
  if (type === 'execution' && executionChartRef.value) {
    const chart = echarts.init(executionChartRef.value)
    chart.setOption({
      title: { text: '测试结果分布' },
      tooltip: { trigger: 'item' },
      series: [{
        type: 'pie',
        data: [
          { value: previewData.value.passedTests, name: '通过' },
          { value: previewData.value.failedTests, name: '失败' }
        ]
      }]
    })
  } else if (type === 'coverage' && coverageChartRef.value) {
    const chart = echarts.init(coverageChartRef.value)
    chart.setOption({
      title: { text: '覆盖率分布' },
      tooltip: { trigger: 'axis' },
      xAxis: { type: 'category', data: ['行覆盖率', '分支覆盖率', '函数覆盖率'] },
      yAxis: { type: 'value', max: 100 },
      series: [{
        type: 'bar',
        data: [
          previewData.value.lineCoverage,
          previewData.value.branchCoverage,
          previewData.value.functionCoverage
        ]
      }]
    })
  } else if (type === 'performance' && performanceChartRef.value) {
    const chart = echarts.init(performanceChartRef.value)
    chart.setOption({
      title: { text: '性能指标' },
      tooltip: { trigger: 'axis' },
      xAxis: { type: 'category', data: ['平均响应时间', '最大响应时间', '吞吐量'] },
      yAxis: { type: 'value' },
      series: [{
        type: 'line',
        data: [
          previewData.value.avgResponseTime,
          previewData.value.maxResponseTime,
          previewData.value.throughput
        ]
      }]
    })
  }
}

const downloadReport = (report) => {
  // 模拟下载
  const link = document.createElement('a')
  link.href = '#'
  link.download = `${report.name}.${report.formats[0]}`
  link.click()
  ElMessage.success('报告下载已开始')
}

const shareReport = (report) => {
  shareLink.value = `${window.location.origin}/reports/share/${report.id}`
  showShareDialog.value = true
}

const copyShareLink = () => {
  navigator.clipboard.writeText(shareLink.value)
  ElMessage.success('分享链接已复制到剪贴板')
}

const updateShareSettings = () => {
  ElMessage.success('分享设置已更新')
  showShareDialog.value = false
}

const deleteReport = async (report) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除报告 "${report.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const index = reports.value.findIndex(r => r.id === report.id)
    if (index > -1) {
      reports.value.splice(index, 1)
      ElMessage.success('报告删除成功')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error.message)
    }
  }
}

const batchExport = () => {
  ElMessage.success(`开始导出 ${selectedReports.value.length} 个报告`)
}

const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedReports.value.length} 个报告吗？`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    selectedReports.value.forEach(report => {
      const index = reports.value.findIndex(r => r.id === report.id)
      if (index > -1) {
        reports.value.splice(index, 1)
      }
    })
    
    selectedReports.value = []
    ElMessage.success('批量删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败: ' + error.message)
    }
  }
}

// 生命周期
onMounted(() => {
  loadReports()
})
</script>

<style scoped>
.test-report-generator {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar-left {
  display: flex;
  gap: 10px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.reports-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.report-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.report-icon {
  color: #409eff;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.report-preview {
  padding: 20px;
}

.summary-stats,
.coverage-stats,
.performance-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-item,
.coverage-item,
.perf-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f5f5f5;
  border-radius: 4px;
}

.stat-item .label,
.coverage-item .label,
.perf-item .label {
  font-weight: 500;
  color: #606266;
}

.stat-item .value,
.perf-item .value {
  font-weight: 600;
  color: #303133;
}

.stat-item .value.success {
  color: #67c23a;
}

.stat-item .value.danger {
  color: #f56c6c;
}

.coverage-item {
  flex-direction: column;
  align-items: stretch;
  gap: 8px;
}

.chart-container {
  margin-top: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.share-options h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .test-report-generator {
    padding: 10px;
  }
  
  .toolbar {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .toolbar-right {
    justify-content: space-between;
  }
  
  .summary-stats,
  .coverage-stats,
  .performance-stats {
    grid-template-columns: 1fr;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .test-report-generator {
    background: #1a1a1a;
  }
  
  .page-header h2 {
    color: #e4e7ed;
  }
  
  .page-header p {
    color: #a8abb2;
  }
  
  .toolbar,
  .reports-container {
    background: #2d2d2d;
    border-color: #4c4d4f;
  }
  
  .stat-item,
  .coverage-item,
  .perf-item {
    background: #1a1a1a;
  }
  
  .stat-item .label,
  .coverage-item .label,
  .perf-item .label {
    color: #a8abb2;
  }
  
  .stat-item .value,
  .perf-item .value {
    color: #e4e7ed;
  }
  
  .share-options h4 {
    color: #e4e7ed;
  }
  
  .chart-container {
    border-color: #4c4d4f;
  }
}
</style>