"""用药管理屏幕模块

提供用药记录的查看、添加、编辑、删除和提醒功能。
包括正在使用的药物管理和用药历史查询两个部分。
"""

from kivy.logger import Logger as KivyLogger
from kivymd.app import MDApp
from kivy.metrics import dp
from kivy.properties import StringProperty, ListProperty, ObjectProperty, BooleanProperty, NumericProperty
from screens.base_screen import BaseScreen
from kivy.clock import Clock
from kivy.lang import Builder
from kivy.core.window import Window
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDIconButton, MDButtonText, MDButton
from kivymd.uix.label import MDLabel
from kivymd.uix.card import MDCard
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.list import MDList, MDListItem
from kivymd.uix.divider import MDDivider
from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText
from kivymd.uix.dialog import MDDialog, MDDialogHeadlineText, MDDialogContentContainer, MDDialogButtonContainer
from kivymd.uix.textfield import MDTextField, MDTextFieldHintText
from kivymd.uix.selectioncontrol import MDCheckbox
from kivymd.uix.menu import MDDropdownMenu
# MDTabs在KivyMD 2.0.1 dev0中已被移除，使用自定义Tab实现
from theme import AppTheme, AppMetrics, FontStyles
from utils.cloud_api import get_cloud_api
from utils.health_data_manager import get_health_data_manager
from widgets.logo import HealthLogo
import os
import logging
from datetime import datetime, timedelta
from kivy.factory import Factory

KV = '''
# 目前用药卡片组件
<CurrentMedicationCard>:
    orientation: 'vertical'
    size_hint_y: None
    height: self.minimum_height
    md_bg_color: app.theme_cls.surfaceColor
    radius: [dp(12)]
    elevation: 2
    padding: [dp(16), dp(12), dp(16), dp(12)]
    spacing: dp(8)
    ripple_behavior: True
    on_release: root.on_card_click()
    
    # 卡片头部 - 选择框、序号和药物名称
    MDBoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(32)
        spacing: dp(12)
        
        # 选择框
        MDCheckbox:
            id: selection_checkbox
            size_hint_x: None
            width: dp(32)
            active: root.is_selected
            on_active: root.toggle_selection()
        
        # 序号
        MDLabel:
            text: f"No.{root.row_index + 1}"
            font_style: "Body"
            role: "small"
            bold: True
            theme_text_color: "Custom"
            text_color: app.theme_cls.primaryColor
            size_hint_x: None
            width: dp(60)
            halign: "left"
            valign: "center"
        
        # 药物名称
        MDLabel:
            text: root.name
            font_style: "Body"
            role: "large"
            bold: True
            theme_text_color: "Primary"
            halign: "left"
            valign: "center"
            text_size: self.width, None
    
    # 药物详细信息网格
    MDGridLayout:
        cols: 2
        size_hint_y: None
        height: self.minimum_height
        spacing: [dp(16), dp(8)]
        
        # 剂量信息
        MDBoxLayout:
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(24)
            spacing: dp(8)
            
            MDIcon:
                icon: "scale-balance"
                theme_icon_color: "Custom"
                icon_color: app.theme_cls.onSurfaceVariantColor
                size_hint_x: None
                width: dp(20)
            
            MDLabel:
                text: f"剂量: {root.dosage}"
                font_style: "Body"
                role: "medium"
                theme_text_color: "Secondary"
                halign: "left"
                valign: "center"
        
        # 使用频次
        MDBoxLayout:
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(24)
            spacing: dp(8)
            
            MDIcon:
                icon: "clock-outline"
                theme_icon_color: "Custom"
                icon_color: app.theme_cls.onSurfaceVariantColor
                size_hint_x: None
                width: dp(20)
            
            MDLabel:
                text: f"频次: {root.frequency}"
                font_style: "Body"
                role: "medium"
                theme_text_color: "Secondary"
                halign: "left"
                valign: "center"
        
        # 起始时间
        MDBoxLayout:
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(24)
            spacing: dp(8)
            
            MDIcon:
                icon: "calendar"
                theme_icon_color: "Custom"
                icon_color: app.theme_cls.onSurfaceVariantColor
                size_hint_x: None
                width: dp(20)
            
            MDLabel:
                text: f"起始: {root.start_date}"
                font_style: "Body"
                role: "medium"
                theme_text_color: "Secondary"
                halign: "left"
                valign: "center"
        
        # 用药原因
        MDBoxLayout:
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(24)
            spacing: dp(8)
            
            MDIcon:
                icon: "heart-pulse"
                theme_icon_color: "Custom"
                icon_color: app.theme_cls.onSurfaceVariantColor
                size_hint_x: None
                width: dp(20)
            
            MDLabel:
                text: f"原因: {root.reason}"
                font_style: "Body"
                role: "medium"
                theme_text_color: "Secondary"
                halign: "left"
                valign: "center"
    
    # 注意事项（如果有）
    MDBoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(24) if root.notes else 0
        spacing: dp(8)
        opacity: 1 if root.notes else 0
        
        MDIcon:
            icon: "information-outline"
            theme_icon_color: "Custom"
            icon_color: app.theme_cls.errorColor
            size_hint_x: None
            width: dp(20)
        
        MDLabel:
            text: f"注意: {root.notes}" if root.notes else ""
            font_style: "Body"
            role: "medium"
            theme_text_color: "Custom"
            text_color: app.theme_cls.errorColor
            halign: "left"
            valign: "center"
            text_size: self.width, None

# 既往用药卡片组件
<HistoryMedicationCard>:
    orientation: 'vertical'
    size_hint_y: None
    height: self.minimum_height
    md_bg_color: app.theme_cls.surfaceContainerColor
    radius: [dp(12)]
    elevation: 1
    padding: [dp(16), dp(12), dp(16), dp(12)]
    spacing: dp(8)
    
    # 卡片头部 - 序号和药物名称
    MDBoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(32)
        spacing: dp(12)
        
        # 序号标签
        MDLabel:
            text: f"No.{root.row_index + 1}"
            font_style: "Body"
            role: "small"
            bold: True
            theme_text_color: "Custom"
            text_color: app.theme_cls.onSurfaceVariantColor
            size_hint_x: None
            width: dp(60)
            halign: "left"
            valign: "center"
        
        # 药物名称
        MDLabel:
            text: root.name
            font_style: "Body"
            role: "large"
            bold: True
            theme_text_color: "Primary"
            halign: "left"
            valign: "center"
            text_size: self.width, None
    
    # 药物详细信息网格
    MDGridLayout:
        cols: 2
        size_hint_y: None
        height: self.minimum_height
        spacing: [dp(16), dp(8)]
        
        # 剂量信息
        MDBoxLayout:
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(24)
            spacing: dp(8)
            
            MDIcon:
                icon: "scale-balance"
                theme_icon_color: "Custom"
                icon_color: app.theme_cls.onSurfaceVariantColor
                size_hint_x: None
                width: dp(20)
            
            MDLabel:
                text: f"剂量: {root.dosage}"
                font_style: "Body"
                role: "medium"
                theme_text_color: "Secondary"
                halign: "left"
                valign: "center"
        
        # 使用频次
        MDBoxLayout:
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(24)
            spacing: dp(8)
            
            MDIcon:
                icon: "clock-outline"
                theme_icon_color: "Custom"
                icon_color: app.theme_cls.onSurfaceVariantColor
                size_hint_x: None
                width: dp(20)
            
            MDLabel:
                text: f"频次: {root.frequency}"
                font_style: "Body"
                role: "medium"
                theme_text_color: "Secondary"
                halign: "left"
                valign: "center"
        
        # 起始时间
        # 用药日期（合并显示起始和停药日期）
        MDBoxLayout:
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(24)
            spacing: dp(8)
            
            MDIcon:
                icon: "calendar-range"
                theme_icon_color: "Custom"
                icon_color: app.theme_cls.onSurfaceVariantColor
                size_hint_x: None
                width: dp(20)
            
            MDLabel:
                text: f"用药日期: {root.start_date} 至 {root.stop_date}"
                font_style: "Body"
                role: "medium"
                theme_text_color: "Secondary"
                halign: "left"
                valign: "center"
                text_size: self.width, None
        
        # 用药原因
        MDBoxLayout:
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(24)
            spacing: dp(8)
            
            MDIcon:
                icon: "heart-pulse"
                theme_icon_color: "Custom"
                icon_color: app.theme_cls.onSurfaceVariantColor
                size_hint_x: None
                width: dp(20)
            
            MDLabel:
                text: f"原因: {root.reason}"
                font_style: "Body"
                role: "medium"
                theme_text_color: "Secondary"
                halign: "left"
                valign: "center"
        
        # 停药原因
        MDBoxLayout:
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(24)
            spacing: dp(8)
            
            MDIcon:
                icon: "alert-circle-outline"
                theme_icon_color: "Custom"
                icon_color: app.theme_cls.errorColor
                size_hint_x: None
                width: dp(20)
            
            MDLabel:
                text: f"停药原因: {root.stop_reason}"
                font_style: "Body"
                role: "medium"
                theme_text_color: "Custom"
                text_color: app.theme_cls.errorColor
                halign: "left"
                valign: "center"
                text_size: self.width, None
# 卡片详情对话框
<MedicationDetailDialog>:
    size_hint: 0.9, None
    height: dp(600)
    md_bg_color: app.theme_cls.surfaceColor
    radius: [dp(16)]
    
    MDBoxLayout:
        orientation: 'vertical'
        padding: [dp(24), dp(20), dp(24), dp(20)]
        spacing: dp(16)
        
        # 对话框标题
        MDLabel:
            text: "药物详情"
            font_style: "Headline"
            role: "small"
            bold: True
            theme_text_color: "Primary"
            size_hint_y: None
            height: dp(32)
            halign: "center"
            valign: "center"
        
        # 药物详细信息
        MDScrollView:
            MDBoxLayout:
                orientation: 'vertical'
                size_hint_y: None
                height: self.minimum_height
                spacing: dp(16)
                
                # 药物名称
                MDBoxLayout:
                    orientation: 'horizontal'
                    size_hint_y: None
                    height: dp(40)
                    spacing: dp(12)
                    
                    MDIcon:
                        icon: "pill"
                        theme_icon_color: "Custom"
                        icon_color: app.theme_cls.primaryColor
                        size_hint_x: None
                        width: dp(24)
                    
                    MDLabel:
                        text: f"药物名称: {root.medication_name}"
                        font_style: "Body"
                        role: "large"
                        bold: True
                        theme_text_color: "Primary"
                        halign: "left"
                        valign: "center"
                
                # 剂量和频次
                MDGridLayout:
                    cols: 2
                    size_hint_y: None
                    height: dp(80)
                    spacing: dp(16)
                    
                    MDBoxLayout:
                        orientation: 'horizontal'
                        spacing: dp(8)
                        
                        MDIcon:
                            icon: "scale-balance"
                            theme_icon_color: "Custom"
                            icon_color: app.theme_cls.onSurfaceVariantColor
                            size_hint_x: None
                            width: dp(20)
                        
                        MDLabel:
                            text: f"剂量: {root.dosage}"
                            font_style: "Body"
                            role: "medium"
                            theme_text_color: "Secondary"
                            halign: "left"
                            valign: "center"
                    
                    MDBoxLayout:
                        orientation: 'horizontal'
                        spacing: dp(8)
                        
                        MDIcon:
                            icon: "clock-outline"
                            theme_icon_color: "Custom"
                            icon_color: app.theme_cls.onSurfaceVariantColor
                            size_hint_x: None
                            width: dp(20)
                        
                        MDLabel:
                            text: f"频次: {root.frequency}"
                            font_style: "Body"
                            role: "medium"
                            theme_text_color: "Secondary"
                            halign: "left"
                            valign: "center"
                
<MedicationManagementScreen>:
    name: 'medication_management_screen'
    
    # 背景画布
    canvas.before:
        Color:
            rgba: app.theme_cls.surfaceColor
        Rectangle:
            pos: self.pos
            size: self.size

    MDBoxLayout:
        orientation: 'vertical'
        spacing: 0
        
        # 顶部应用栏 - 与健康资料管理页面保持一致
        MDBoxLayout:
            id: app_bar
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(56)
            md_bg_color: app.theme.PRIMARY_COLOR
            padding: [dp(4), dp(0), dp(4), dp(0)]
            
            MDIconButton:
                icon: "arrow-left"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.go_back()
            
            MDLabel:
                text: "用药管理"
                font_style: "Body"
                role: "large"
                bold: True
                theme_text_color: "Custom"
                text_color: app.theme.TEXT_LIGHT
                halign: "center"
                valign: "center"
            
            MDIconButton:
                icon: "refresh"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.refresh_data()
        
        # Logo区域 - 移到应用栏下方
        HealthLogo:
            id: health_logo
            size_hint_y: None
            height: dp(60)
            pos_hint: {"center_x": 0.5}
        
        # 主内容区域
        MDScrollView:
            do_scroll_x: False
            do_scroll_y: True
            
            MDBoxLayout:
                orientation: 'vertical'
                size_hint_y: None
                height: self.minimum_height
                padding: [dp(16), dp(16), dp(16), dp(16)]
                spacing: dp(16)
                

                
                # 添加药物区域
                MDCard:
                    orientation: 'vertical'
                    size_hint_y: None
                    height: self.minimum_height
                    padding: [dp(16), dp(16), dp(16), dp(16)]
                    spacing: dp(12)
                    md_bg_color: app.theme_cls.surfaceContainerHighColor
                    radius: [dp(12)]
                    elevation: 3
                    
                    # 标题区域
                    MDBoxLayout:
                        orientation: 'horizontal'
                        size_hint_y: None
                        height: dp(40)
                        spacing: dp(8)
                        
                        MDIcon:
                            icon: "pill"
                            theme_icon_color: "Custom"
                            icon_color: app.theme_cls.primaryColor
                            size_hint_x: None
                            width: dp(24)
                        
                        MDLabel:
                            text: "添加新药物"
                            font_style: "Headline"
                            role: "small"
                            bold: True
                            theme_text_color: "Custom"
                            text_color: app.theme_cls.primaryColor
                            size_hint_y: None
                            height: self.texture_size[1]
                            halign: "left"
                            valign: "center"
                    
                    # 第一行：药物名称和剂量
                    MDBoxLayout:
                        orientation: 'horizontal'
                        size_hint_y: None
                        height: dp(56)
                        spacing: dp(12)
                        
                        MDTextField:
                            id: medication_name_field
                            mode: "outlined"
                            size_hint_x: 0.6
                            
                            MDTextFieldHintText:
                                text: "药物名称"
                            
                            MDTextFieldLeadingIcon:
                                icon: "medical-bag"
                        
                        MDTextField:
                            id: dosage_field
                            mode: "outlined"
                            size_hint_x: 0.4
                            
                            MDTextFieldHintText:
                                text: "剂量"
                            
                            MDTextFieldLeadingIcon:
                                icon: "scale-balance"
                    
                    # 第二行：使用频次和起始时间
                    MDBoxLayout:
                        orientation: 'horizontal'
                        size_hint_y: None
                        height: dp(56)
                        spacing: dp(12)
                        
                        MDTextField:
                            id: frequency_field
                            mode: "outlined"
                            size_hint_x: 0.5
                            readonly: True
                            on_focus: if self.focus: root.show_frequency_menu(self)
                            
                            MDTextFieldHintText:
                                text: "使用频次"
                            
                            MDTextFieldLeadingIcon:
                                icon: "clock-outline"
                            
                            MDTextFieldTrailingIcon:
                                icon: "chevron-down"
                        
                        MDTextField:
                            id: start_date_field
                            mode: "outlined"
                            size_hint_x: 0.5
                            readonly: True
                            on_focus: if self.focus: root.show_date_picker(self)
                            
                            MDTextFieldHintText:
                                text: "起始时间"
                            
                            MDTextFieldLeadingIcon:
                                icon: "calendar"
                            
                            MDTextFieldTrailingIcon:
                                icon: "chevron-down"
                    
                    # 第三行：用药原因和注意事项
                    MDBoxLayout:
                        orientation: 'horizontal'
                        size_hint_y: None
                        height: dp(56)
                        spacing: dp(12)
                        
                        MDTextField:
                            id: reason_field
                            mode: "outlined"
                            size_hint_x: 0.5
                            readonly: True
                            on_focus: if self.focus: root.show_reason_menu(self)
                            
                            MDTextFieldHintText:
                                text: "用药原因"
                            
                            MDTextFieldLeadingIcon:
                                icon: "heart-pulse"
                            
                            MDTextFieldTrailingIcon:
                                icon: "chevron-down"
                        
                        MDTextField:
                            id: notes_field
                            mode: "outlined"
                            size_hint_x: 0.5
                            readonly: True
                            on_focus: if self.focus: root.show_notes_menu(self)
                            
                            MDTextFieldHintText:
                                text: "注意事项"
                            
                            MDTextFieldLeadingIcon:
                                icon: "information-outline"
                            
                            MDTextFieldTrailingIcon:
                                icon: "chevron-down"
                    
                    # 确认添加按钮
                    MDButton:
                        style: "filled"
                        md_bg_color: app.theme_cls.primaryColor
                        size_hint_y: None
                        height: dp(48)
                        radius: [dp(24)]
                        on_release: root.add_medication_to_list()
                        
                        MDButtonIcon:
                            icon: "plus"
                            theme_icon_color: "Custom"
                            icon_color: app.theme_cls.onPrimaryColor
                        
                        MDButtonText:
                            text: "确认添加"
                            theme_text_color: "Custom"
                            text_color: app.theme_cls.onPrimaryColor
                
                # 目前用药表格
                MDCard:
                    orientation: 'vertical'
                    size_hint_y: None
                    height: self.minimum_height
                    padding: [dp(16), dp(16), dp(16), dp(16)]
                    spacing: dp(8)
                    md_bg_color: app.theme_cls.surfaceColor
                    radius: [dp(12)]
                    elevation: 2
                    
                    # Tab导航按钮
                    # Tab切换区域
                    MDCard:
                        orientation: 'horizontal'
                        size_hint_y: None
                        height: dp(56)
                        padding: [dp(4), dp(4), dp(4), dp(4)]
                        spacing: dp(4)
                        md_bg_color: app.theme_cls.surfaceContainerColor
                        radius: [dp(28)]
                        elevation: 1
                        
                        MDButton:
                            id: current_tab_btn
                            style: "filled" if root.current_tab == 'current' else "text"
                            md_bg_color: app.theme_cls.primaryColor if root.current_tab == 'current' else app.theme_cls.surfaceColor
                            size_hint_x: 0.5
                            radius: [dp(24)]
                            on_release: root.switch_tab('current')
                            
                            MDButtonIcon:
                                icon: "pill"
                                theme_icon_color: "Custom"
                                icon_color: app.theme_cls.onPrimaryColor if root.current_tab == 'current' else app.theme_cls.onSurfaceVariantColor
                            
                            MDButtonText:
                                text: "目前用药"
                                theme_text_color: "Custom"
                                text_color: app.theme_cls.onPrimaryColor if root.current_tab == 'current' else app.theme_cls.onSurfaceVariantColor
                        
                        MDButton:
                            id: history_tab_btn
                            style: "filled" if root.current_tab == 'history' else "text"
                            md_bg_color: app.theme_cls.primaryColor if root.current_tab == 'history' else app.theme_cls.surfaceColor
                            size_hint_x: 0.5
                            radius: [dp(24)]
                            on_release: root.switch_tab('history')
                            
                            MDButtonIcon:
                                icon: "history"
                                theme_icon_color: "Custom"
                                icon_color: app.theme_cls.onPrimaryColor if root.current_tab == 'history' else app.theme_cls.onSurfaceVariantColor
                            
                            MDButtonText:
                                text: "既往用药"
                                theme_text_color: "Custom"
                                text_color: app.theme_cls.onPrimaryColor if root.current_tab == 'history' else app.theme_cls.onSurfaceVariantColor
                    
                    # 当前用药内容
                    MDBoxLayout:
                        id: current_content
                        orientation: 'vertical'
                        size_hint_y: None
                        height: self.minimum_height
                        spacing: dp(8)
                        opacity: 1 if root.current_tab == 'current' else 0
                        disabled: False if root.current_tab == 'current' else True
                        
                        # 单个操作按钮区域
                        MDBoxLayout:
                            orientation: 'horizontal'
                            size_hint_y: None
                            height: dp(48)
                            spacing: dp(12)
                            padding: [dp(16), dp(8), dp(16), dp(8)]
                            
                            MDButton:
                                style: "outlined"
                                size_hint_x: 0.33
                                on_release: root.show_delete_dialog()
                                
                                MDButtonIcon:
                                    icon: "delete"
                                    theme_icon_color: "Custom"
                                    icon_color: app.theme_cls.errorColor
                                
                                MDButtonText:
                                    text: "删除"
                                    theme_text_color: "Custom"
                                    text_color: app.theme_cls.errorColor
                            
                            MDButton:
                                style: "outlined"
                                size_hint_x: 0.33
                                on_release: root.show_stop_dialog()
                                
                                MDButtonIcon:
                                    icon: "stop"
                                    theme_icon_color: "Custom"
                                    icon_color: app.theme_cls.onSurfaceVariantColor
                                
                                MDButtonText:
                                    text: "停用"
                                    theme_text_color: "Custom"
                                    text_color: app.theme_cls.onSurfaceVariantColor
                            
                            MDButton:
                                style: "outlined"
                                size_hint_x: 0.33
                                on_release: root.show_reminder_dialog()
                                
                                MDButtonIcon:
                                    icon: "bell"
                                    theme_icon_color: "Custom"
                                    icon_color: app.theme_cls.primaryColor
                                
                                MDButtonText:
                                    text: "提醒设置"
                                    theme_text_color: "Custom"
                                    text_color: app.theme_cls.primaryColor
                        
                        # 当前用药卡片容器
                        MDScrollView:
                            do_scroll_x: False
                            do_scroll_y: True
                            size_hint_y: None
                            height: dp(400)
                            
                            MDBoxLayout:
                                id: current_medications_container
                                orientation: 'vertical'
                                size_hint_y: None
                                height: self.minimum_height
                                spacing: dp(12)
                                padding: [dp(8), dp(8), dp(8), dp(8)]
                    
                    # 既往用药内容
                    MDBoxLayout:
                        id: history_content
                        orientation: 'vertical'
                        size_hint_y: None
                        height: self.minimum_height
                        spacing: dp(8)
                        opacity: 0 if root.current_tab == 'current' else 1
                        disabled: True if root.current_tab == 'current' else False
                        
                        # 搜索功能区域
                        MDBoxLayout:
                            orientation: 'horizontal'
                            size_hint_y: None
                            height: dp(56)
                            spacing: dp(12)
                            padding: [dp(4), dp(8), dp(4), dp(8)]
                            
                            MDTextField:
                                id: history_search_field
                                mode: "outlined"
                                size_hint_x: 0.7
                                
                                MDTextFieldHintText:
                                    text: "输入药物名称搜索"
                            
                            MDButton:
                                style: "filled"
                                md_bg_color: app.theme_cls.primaryColor
                                size_hint_x: 0.15
                                on_release: root.search_history_medications()
                                
                                MDButtonText:
                                    text: "搜索"
                                    theme_text_color: "Custom"
                                    text_color: app.theme_cls.onPrimaryColor
                            
                            MDButton:
                                style: "outlined"
                                size_hint_x: 0.15
                                on_release: root.clear_history_search()
                                
                                MDButtonText:
                                    text: "清空"
                                    theme_text_color: "Custom"
                                    text_color: app.theme_cls.primaryColor
                        
                        # 既往用药表格头部
                        # 既往用药卡片容器
                        MDScrollView:
                            do_scroll_x: False
                            do_scroll_y: True
                            size_hint_y: None
                            height: dp(400)
                            
                            MDBoxLayout:
                                id: history_medications_container
                                orientation: 'vertical'
                                size_hint_y: None
                                height: self.minimum_height
                                spacing: dp(12)
                                padding: [dp(8), dp(8), dp(8), dp(8)]
'''

class CurrentMedicationCard(MDCard):
    """当前用药卡片组件"""
    
    name = StringProperty("")
    dosage = StringProperty("")
    frequency = StringProperty("")
    start_date = StringProperty("")
    reason = StringProperty("")
    notes = StringProperty("")
    row_index = NumericProperty(0)
    medication_data = ObjectProperty(None)
    is_selected = BooleanProperty(False)
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
    
    def toggle_selection(self):
        """切换选择状态"""
        self.is_selected = not self.is_selected
        # 通知父屏幕更新选择列表
        if hasattr(self.parent, 'parent') and hasattr(self.parent.parent, 'parent'):
            screen = self.parent.parent.parent
            if hasattr(screen, 'update_selection'):
                screen.update_selection(self)
    
    def on_card_click(self):
        """处理卡片点击事件"""
        if hasattr(self.parent, 'parent') and hasattr(self.parent.parent, 'parent'):
            screen = self.parent.parent.parent
            if hasattr(screen, 'show_medication_detail'):
                screen.show_medication_detail(self.medication_data)
    


class HistoryMedicationCard(MDCard):
    """既往用药卡片组件"""
    
    name = StringProperty("")
    dosage = StringProperty("")
    frequency = StringProperty("")
    start_date = StringProperty("")
    stop_date = StringProperty("")
    reason = StringProperty("")
    stop_reason = StringProperty("")
    notes = StringProperty("")
    row_index = NumericProperty(0)
    medication_data = ObjectProperty(None)
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

class MedicationManagementScreen(BaseScreen):
    """用药管理屏幕"""
    
    current_medications = ListProperty([])
    current_tab = StringProperty('current')
    selected_medications = ListProperty([])
    
    # 下拉菜单选项
    frequency_options = [
        "每日一次", "每日两次", "每日三次", "每日四次",
        "每周一次", "每周两次", "每周三次",
        "每月一次", "按需服用", "其他"
    ]
    
    reason_options = [
        "高血压", "糖尿病", "高血脂", "冠心病",
        "心律不齐", "慢性肾病", "甲状腺疾病",
        "关节炎", "骨质疏松", "抑郁症", "其他原因"
    ]
    
    notes_options = [
        "空腹", "餐中", "餐后", "无特殊"
    ]
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.dialog = None
        self.frequency_menu = None
        self.reason_menu = None
        self.notes_menu = None
        self.selected_medications = []
    
    def update_selection(self, medication_card, is_selected):
        """更新药物选择状态"""
        try:
            if is_selected:
                if medication_card not in self.selected_medications:
                    self.selected_medications.append(medication_card)
            else:
                if medication_card in self.selected_medications:
                    self.selected_medications.remove(medication_card)
            
            KivyLogger.info(f"MedicationManagement: 已选择 {len(self.selected_medications)} 个药物")
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 更新选择状态失败: {e}")
    
    def show_delete_dialog(self):
        """显示删除对话框"""
        try:
            # 获取当前选中的药物
            selected_items = []
            container = self.ids.current_medications_container
            for child in container.children:
                if hasattr(child, 'is_selected') and child.is_selected:
                    selected_items.append(child)
            
            if not selected_items:
                self.show_error("请先选择要删除的药物")
                return
            
            self.selected_medications = selected_items
            
            from kivymd.uix.dialog import MDDialog, MDDialogHeadlineText, MDDialogSupportingText, MDDialogButtonContainer
            from kivymd.uix.button import MDButton, MDButtonText
            
            # 创建确认删除对话框
            self.dialog = MDDialog(
                MDDialogHeadlineText(text="确认删除"),
                MDDialogSupportingText(text=f"确定要删除选中的 {len(self.selected_medications)} 个药物吗？此操作不可撤销。"),
                MDDialogButtonContainer(
                    MDButton(
                        MDButtonText(text="取消"),
                        style="text",
                        on_release=lambda *x: self.dialog.dismiss()
                    ),
                    MDButton(
                        MDButtonText(text="删除"),
                        style="filled",
                        md_bg_color=self.get_app().theme.ERROR_COLOR,
                        on_release=lambda *x: self.confirm_delete_medications()
                    )
                )
            )
            
            self.dialog.open()
            
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 显示删除对话框失败: {e}")
            self.show_error("显示删除对话框失败")
    
    def show_stop_dialog(self):
        """显示停用对话框"""
        try:
            # 获取当前选中的药物
            selected_items = []
            container = self.ids.current_medications_container
            for child in container.children:
                if hasattr(child, 'is_selected') and child.is_selected:
                    selected_items.append(child)
            
            if not selected_items:
                self.show_error("请先选择要停用的药物")
                return
            
            self.selected_medications = selected_items
            
            from kivymd.uix.dialog import MDDialog, MDDialogHeadlineText, MDDialogSupportingText, MDDialogButtonContainer
            from kivymd.uix.button import MDButton, MDButtonText, MDButtonIcon
            from kivymd.uix.textfield import MDTextField, MDTextFieldHintText
            from kivymd.uix.boxlayout import MDBoxLayout
            from kivymd.uix.label import MDLabel
            from kivymd.uix.card import MDCard
            from kivymd.uix.chip import MDChip, MDChipText
            from kivymd.uix.gridlayout import MDGridLayout
            from kivymd.uix.divider import MDDivider
            from kivy.metrics import dp
            
            # 创建对话框内容
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(16),
                size_hint_y=None,
                height=dp(400)
            )
            
            # 选中药物信息卡片
            info_card = MDCard(
                orientation='vertical',
                padding=[dp(16), dp(12)],
                spacing=dp(8),
                size_hint_y=None,
                height=dp(100),
                md_bg_color=MDApp.get_running_app().theme_cls.errorContainerColor,
                radius=[dp(8)]
            )
            
            info_card.add_widget(MDLabel(
                text=f"即将停用 {len(self.selected_medications)} 个药物",
                font_style="Body",
                role="large",
                bold=True,
                theme_text_color="Custom",
                text_color=MDApp.get_running_app().theme_cls.onErrorContainerColor,
                size_hint_y=None,
                height=dp(32)
            ))
            
            # 显示选中的药物名称
            med_names = []
            for med_card in self.selected_medications:
                if hasattr(med_card, 'medication_data'):
                    med_names.append(med_card.medication_data.get('name', '未知药物'))
            
            info_card.add_widget(MDLabel(
                text=f"药物: {', '.join(med_names[:3])}{'...' if len(med_names) > 3 else ''}",
                font_style="Body",
                role="medium",
                theme_text_color="Custom",
                text_color=MDApp.get_running_app().theme_cls.onErrorContainerColor,
                size_hint_y=None,
                height=dp(24)
            ))
            
            info_card.add_widget(MDLabel(
                text="⚠️ 停用后药物将移至既往用药记录",
                font_style="Body",
                role="small",
                theme_text_color="Custom",
                text_color=MDApp.get_running_app().theme_cls.onErrorContainerColor,
                size_hint_y=None,
                height=dp(20)
            ))
            
            content.add_widget(info_card)
            
            # 常用停药原因选择
            content.add_widget(MDLabel(
                text="常用停药原因 (点击快速选择):",
                font_style="Body",
                role="medium",
                theme_text_color="Primary",
                size_hint_y=None,
                height=dp(24)
            ))
            
            # 停药原因选项
            stop_reasons = [
                "治疗完成", "副作用严重", "过敏反应", "效果不佳",
                "医生建议", "药物冲突", "经济原因", "其他原因"
            ]
            
            reasons_grid = MDGridLayout(
                cols=2,
                spacing=dp(8),
                size_hint_y=None,
                height=dp(120),
                adaptive_height=True
            )
            
            self.selected_stop_reason = ""
            
            for reason in stop_reasons:
                chip = MDChip(
                    MDChipText(text=reason),
                    type="suggestion",
                    size_hint_y=None,
                    height=dp(32),
                    on_release=lambda x, r=reason: self.select_stop_reason(r)
                )
                reasons_grid.add_widget(chip)
            
            content.add_widget(reasons_grid)
            
            # 分隔线
            content.add_widget(MDDivider())
            
            # 自定义停药原因输入
            content.add_widget(MDLabel(
                text="或输入自定义停药原因:",
                font_style="Body",
                role="medium",
                theme_text_color="Primary",
                size_hint_y=None,
                height=dp(24)
            ))
            
            self.stop_reason_field = MDTextField(
                mode="outlined",
                size_hint_y=None,
                height=dp(56)
            )
            self.stop_reason_field.add_widget(MDTextFieldHintText(text="请输入详细的停药原因"))
            content.add_widget(self.stop_reason_field)
            
            # 创建停药确认对话框
            self.stop_dialog = MDDialog(
                MDDialogHeadlineText(text="停用药物确认"),
                content,
                MDDialogButtonContainer(
                    MDButton(
                        MDButtonIcon(icon="close"),
                        MDButtonText(text="取消"),
                        style="outlined",
                        on_release=lambda *x: self.stop_dialog.dismiss()
                    ),
                    MDButton(
                        MDButtonIcon(icon="stop"),
                        MDButtonText(text="确认停用"),
                        style="filled",
                        md_bg_color=MDApp.get_running_app().theme_cls.errorColor,
                        on_release=lambda *x: self.confirm_stop_medications()
                    )
                ),
                size_hint=(0.9, None),
                height=dp(600)
            )
            
            self.stop_dialog.open()
            
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 显示停用对话框失败: {e}")
            self.show_error("显示停用对话框失败")
    
    def select_stop_reason(self, reason):
        """选择停药原因"""
        try:
            self.selected_stop_reason = reason
            self.stop_reason_field.text = reason
            
            # 更新芯片的选中状态视觉效果
            # 这里可以添加视觉反馈，比如改变芯片颜色
            
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 选择停药原因失败: {e}")
    
    def confirm_stop_medications(self):
        """确认停用药物"""
        try:
            # 获取停药原因 - 优先使用选中的原因，否则使用输入框内容
            stop_reason = ""
            if hasattr(self, 'selected_stop_reason') and self.selected_stop_reason:
                stop_reason = self.selected_stop_reason
            elif hasattr(self, 'stop_reason_field') and self.stop_reason_field.text.strip():
                stop_reason = self.stop_reason_field.text.strip()
            
            if not stop_reason:
                self.show_error("请选择或输入停药原因")
                return
            
            stopped_count = 0
            for medication_card in self.selected_medications:
                if hasattr(medication_card, 'medication_data'):
                    medication_data = medication_card.medication_data
                    
                    # 更新数据库状态为stopped
                    success = self.update_medication_status(medication_data['id'], 'stopped', stop_reason)
                    if success:
                        # 从当前用药列表中移除
                        if medication_data in self.current_medications:
                            self.current_medications.remove(medication_data)
                        stopped_count += 1
            
            # 刷新显示
            self.refresh_medications_display()
            self.refresh_history_display()  # 刷新既往用药
            
            # 清空选择
            self.selected_medications.clear()
            if hasattr(self, 'selected_stop_reason'):
                self.selected_stop_reason = ""
            
            # 关闭对话框
            if hasattr(self, 'stop_dialog'):
                self.stop_dialog.dismiss()
            elif hasattr(self, 'dialog'):
                self.dialog.dismiss()
            
            # 显示成功消息
            if stopped_count > 0:
                self.show_info(f"已成功停用 {stopped_count} 个药物\n停药原因: {stop_reason}")
            else:
                self.show_error("停用药物失败，请重试")
            
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 停用药物失败: {e}")
            self.show_error("停用药物失败")
    
    def show_reminder_dialog(self):
        """显示提醒设置对话框"""
        try:
            # 获取当前选中的药物
            selected_items = []
            container = self.ids.current_medications_container
            for child in container.children:
                if hasattr(child, 'is_selected') and child.is_selected:
                    selected_items.append(child)
            
            if not selected_items:
                self.show_error("请先选择要设置提醒的药物")
                return
            
            self.selected_medications = selected_items
            
            from kivymd.uix.dialog import MDDialog, MDDialogHeadlineText, MDDialogSupportingText, MDDialogButtonContainer
            from kivymd.uix.button import MDButton, MDButtonText, MDButtonIcon
            from kivymd.uix.boxlayout import MDBoxLayout
            from kivymd.uix.label import MDLabel
            from kivymd.uix.textfield import MDTextField, MDTextFieldHintText
            from kivymd.uix.selectioncontrol import MDSwitch
            from kivymd.uix.card import MDCard
            from kivymd.uix.divider import MDDivider
            from kivy.metrics import dp
            
            # 创建对话框内容
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(16),
                size_hint_y=None,
                height=dp(500)
            )
            
            # 标题说明
            content.add_widget(MDLabel(
                text=f"为选中的 {len(self.selected_medications)} 个药物设置提醒",
                font_style="Body",
                role="large",
                theme_text_color="Primary",
                size_hint_y=None,
                height=dp(32)
            ))
            
            # 服药时间提醒设置
            medication_card = MDCard(
                orientation='vertical',
                padding=[dp(16), dp(12)],
                spacing=dp(12),
                size_hint_y=None,
                height=dp(180),
                md_bg_color=MDApp.get_running_app().theme_cls.surfaceContainerColor,
                radius=[dp(8)]
            )
            
            # 服药提醒开关
            med_switch_layout = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(40),
                spacing=dp(12)
            )
            
            med_switch_layout.add_widget(MDLabel(
                text="服药时间提醒",
                font_style="Body",
                role="large",
                theme_text_color="Primary",
                halign="left",
                valign="center"
            ))
            
            self.med_reminder_switch = MDSwitch(
                size_hint_x=None,
                width=dp(52),
                active=False
            )
            med_switch_layout.add_widget(self.med_reminder_switch)
            medication_card.add_widget(med_switch_layout)
            
            # 服药时间设置
            time_layout = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(56),
                spacing=dp(12)
            )
            
            self.med_time_field = MDTextField(
                mode="outlined",
                size_hint_x=0.5
            )
            self.med_time_field.add_widget(MDTextFieldHintText(text="服药时间 (如: 08:00,12:00,18:00)"))
            time_layout.add_widget(self.med_time_field)
            
            self.med_advance_field = MDTextField(
                mode="outlined",
                size_hint_x=0.5
            )
            self.med_advance_field.add_widget(MDTextFieldHintText(text="提前提醒(分钟)"))
            time_layout.add_widget(self.med_advance_field)
            
            medication_card.add_widget(time_layout)
            
            # 提醒说明
            medication_card.add_widget(MDLabel(
                text="多个时间请用逗号分隔，如: 08:00,12:00,18:00",
                font_style="Body",
                role="small",
                theme_text_color="Secondary",
                size_hint_y=None,
                height=dp(24)
            ))
            
            content.add_widget(medication_card)
            
            # 复查日期提醒设置
            review_card = MDCard(
                orientation='vertical',
                padding=[dp(16), dp(12)],
                spacing=dp(12),
                size_hint_y=None,
                height=dp(180),
                md_bg_color=MDApp.get_running_app().theme_cls.surfaceContainerColor,
                radius=[dp(8)]
            )
            
            # 复查提醒开关
            review_switch_layout = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(40),
                spacing=dp(12)
            )
            
            review_switch_layout.add_widget(MDLabel(
                text="复查日期提醒",
                font_style="Body",
                role="large",
                theme_text_color="Primary",
                halign="left",
                valign="center"
            ))
            
            self.review_reminder_switch = MDSwitch(
                size_hint_x=None,
                width=dp(52),
                active=False
            )
            review_switch_layout.add_widget(self.review_reminder_switch)
            review_card.add_widget(review_switch_layout)
            
            # 复查日期设置
            review_layout = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(56),
                spacing=dp(12)
            )
            
            self.review_date_field = MDTextField(
                mode="outlined",
                size_hint_x=0.5,
                on_focus=self.show_review_date_picker
            )
            self.review_date_field.add_widget(MDTextFieldHintText(text="复查日期 (YYYY-MM-DD)"))
            review_layout.add_widget(self.review_date_field)
            
            self.review_advance_field = MDTextField(
                mode="outlined",
                size_hint_x=0.5
            )
            self.review_advance_field.add_widget(MDTextFieldHintText(text="提前提醒(天)"))
            review_layout.add_widget(self.review_advance_field)
            
            review_card.add_widget(review_layout)
            
            # 复查说明
            review_card.add_widget(MDLabel(
                text="用于监测治疗效果与副作用，建议定期复查",
                font_style="Body",
                role="small",
                theme_text_color="Secondary",
                size_hint_y=None,
                height=dp(24)
            ))
            
            content.add_widget(review_card)
            
            # 创建提醒设置对话框
            self.reminder_dialog = MDDialog(
                MDDialogHeadlineText(text="提醒设置"),
                content,
                MDDialogButtonContainer(
                    MDButton(
                        MDButtonText(text="取消"),
                        style="outlined",
                        on_release=lambda *x: self.reminder_dialog.dismiss()
                    ),
                    MDButton(
                        MDButtonText(text="保存设置"),
                        style="filled",
                        on_release=self.save_reminder_settings
                    )
                ),
                size_hint=(0.9, None),
                height=dp(700)
            )
            
            self.reminder_dialog.open()
            
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 显示提醒设置对话框失败: {e}")
            self.show_error("显示提醒设置对话框失败")
    
    def show_review_date_picker(self, instance, focus):
        """显示复查日期选择器"""
        if focus:
            try:
                from kivymd.uix.pickers import MDDatePicker
                import datetime
                
                date_picker = MDDatePicker(
                    min_date=datetime.date.today(),
                    max_date=datetime.date.today() + datetime.timedelta(days=365)
                )
                date_picker.bind(on_save=self.on_review_date_save)
                date_picker.open()
                
            except Exception as e:
                KivyLogger.error(f"MedicationManagement: 显示日期选择器失败: {e}")
    
    def on_review_date_save(self, instance, value, date_range):
        """保存选择的复查日期"""
        try:
            self.review_date_field.text = value.strftime("%Y-%m-%d")
            instance.dismiss()
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 保存复查日期失败: {e}")
    
    def save_reminder_settings(self, *args):
        """保存提醒设置"""
        try:
            # 验证输入
            med_reminder_enabled = self.med_reminder_switch.active
            review_reminder_enabled = self.review_reminder_switch.active
            
            if not med_reminder_enabled and not review_reminder_enabled:
                self.show_error("请至少启用一种提醒类型")
                return
            
            # 验证服药时间格式
            med_times = []
            med_advance_minutes = 0
            if med_reminder_enabled:
                med_time_text = self.med_time_field.text.strip()
                if not med_time_text:
                    self.show_error("请输入服药时间")
                    return
                
                # 解析时间格式
                import re
                time_pattern = r'^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$'
                times = [t.strip() for t in med_time_text.split(',')]
                
                for time_str in times:
                    if not re.match(time_pattern, time_str):
                        self.show_error(f"时间格式错误: {time_str}，请使用 HH:MM 格式")
                        return
                    med_times.append(time_str)
                
                # 验证提前提醒时间
                try:
                    advance_text = self.med_advance_field.text.strip()
                    if advance_text:
                        med_advance_minutes = int(advance_text)
                        if med_advance_minutes < 0 or med_advance_minutes > 1440:
                            self.show_error("提前提醒时间应在0-1440分钟之间")
                            return
                except ValueError:
                    self.show_error("提前提醒时间必须是数字")
                    return
            
            # 验证复查日期
            review_date = None
            review_advance_days = 0
            if review_reminder_enabled:
                review_date_text = self.review_date_field.text.strip()
                if not review_date_text:
                    self.show_error("请选择复查日期")
                    return
                
                # 验证日期格式
                import datetime
                try:
                    review_date = datetime.datetime.strptime(review_date_text, "%Y-%m-%d").date()
                    if review_date <= datetime.date.today():
                        self.show_error("复查日期必须是未来日期")
                        return
                except ValueError:
                    self.show_error("日期格式错误，请使用 YYYY-MM-DD 格式")
                    return
                
                # 验证提前提醒天数
                try:
                    advance_text = self.review_advance_field.text.strip()
                    if advance_text:
                        review_advance_days = int(advance_text)
                        if review_advance_days < 0 or review_advance_days > 30:
                            self.show_error("提前提醒天数应在0-30天之间")
                            return
                except ValueError:
                    self.show_error("提前提醒天数必须是数字")
                    return
            
            # 保存提醒设置到每个选中的药物
            reminder_data = {
                'medication_reminder': {
                    'enabled': med_reminder_enabled,
                    'times': med_times,
                    'advance_minutes': med_advance_minutes
                },
                'review_reminder': {
                    'enabled': review_reminder_enabled,
                    'date': review_date.isoformat() if review_date else None,
                    'advance_days': review_advance_days
                }
            }
            
            # 为每个选中的药物保存提醒设置
            for medication_card in self.selected_medications:
                if hasattr(medication_card, 'medication_data'):
                    medication_card.medication_data['reminder_settings'] = reminder_data.copy()
            
            # 保存到本地存储
            self.save_reminder_to_storage(reminder_data)
            
            # 设置系统通知
            self.schedule_notifications(reminder_data)
            
            self.reminder_dialog.dismiss()
            self.show_success(f"已为 {len(self.selected_medications)} 个药物设置提醒")
            
            # 清除选中状态
            for card in self.selected_medications:
                if hasattr(card, 'is_selected'):
                    card.is_selected = False
                    card.md_bg_color = MDApp.get_running_app().theme_cls.surfaceContainerColor
            
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 保存提醒设置失败: {e}")
            self.show_error("保存提醒设置失败")
    
    def save_reminder_to_storage(self, reminder_data):
        """保存提醒设置到本地存储"""
        try:
            import json
            import os
            
            # 确保数据目录存在
            data_dir = os.path.join(os.path.dirname(__file__), '..', 'data')
            os.makedirs(data_dir, exist_ok=True)
            
            # 读取现有提醒数据
            reminders_file = os.path.join(data_dir, 'medication_reminders.json')
            reminders = {}
            if os.path.exists(reminders_file):
                with open(reminders_file, 'r', encoding='utf-8') as f:
                    reminders = json.load(f)
            
            # 添加新的提醒设置
            for medication_card in self.selected_medications:
                if hasattr(medication_card, 'medication_data'):
                    med_name = medication_card.medication_data.get('name', '')
                    if med_name:
                        reminders[med_name] = reminder_data.copy()
            
            # 保存到文件
            with open(reminders_file, 'w', encoding='utf-8') as f:
                json.dump(reminders, f, ensure_ascii=False, indent=2)
            
            KivyLogger.info(f"MedicationManagement: 提醒设置已保存到 {reminders_file}")
            
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 保存提醒设置到存储失败: {e}")
    
    def schedule_notifications(self, reminder_data):
        """安排系统通知"""
        try:
            # 这里可以集成系统通知功能
            # 由于Kivy的通知功能有限，这里只记录日志
            # 在实际应用中可以使用plyer库或其他通知库
            
            if reminder_data['medication_reminder']['enabled']:
                times = reminder_data['medication_reminder']['times']
                advance = reminder_data['medication_reminder']['advance_minutes']
                KivyLogger.info(f"MedicationManagement: 已安排服药提醒 - 时间: {times}, 提前: {advance}分钟")
            
            if reminder_data['review_reminder']['enabled']:
                date = reminder_data['review_reminder']['date']
                advance = reminder_data['review_reminder']['advance_days']
                KivyLogger.info(f"MedicationManagement: 已安排复查提醒 - 日期: {date}, 提前: {advance}天")
            
            # TODO: 实现真正的系统通知调度
            # 可以使用以下库:
            # - plyer: 跨平台通知
            # - APScheduler: 任务调度
            # - 系统原生通知API
            
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 安排通知失败: {e}")
    
    def switch_tab(self, tab_name):
        """切换Tab"""
        try:
            self.current_tab = tab_name
            
            # 更新按钮样式
            current_btn = self.ids.current_tab_btn
            history_btn = self.ids.history_tab_btn
            
            if tab_name == 'current':
                current_btn.style = "filled"
                current_btn.md_bg_color = self.get_app().theme.PRIMARY_COLOR
                history_btn.style = "outlined"
                history_btn.md_bg_color = self.get_app().theme.CARD_BACKGROUND
                
                # 切换内容显示
                self.ids.current_content.opacity = 1
                self.ids.current_content.disabled = False
                self.ids.current_content.size_hint_y = None
                self.ids.current_content.height = self.ids.current_content.minimum_height
                self.ids.history_content.opacity = 0
                self.ids.history_content.disabled = True
                self.ids.history_content.size_hint_y = None
                self.ids.history_content.height = self.ids.history_content.minimum_height
            else:
                current_btn.style = "outlined"
                current_btn.md_bg_color = self.get_app().theme.CARD_BACKGROUND
                history_btn.style = "filled"
                history_btn.md_bg_color = self.get_app().theme.PRIMARY_COLOR
                
                # 切换内容显示
                self.ids.current_content.opacity = 0
                self.ids.current_content.disabled = True
                self.ids.current_content.size_hint_y = None
                self.ids.current_content.height = self.ids.current_content.minimum_height
                self.ids.history_content.opacity = 1
                self.ids.history_content.disabled = False
                self.ids.history_content.size_hint_y = None
                self.ids.history_content.height = self.ids.history_content.minimum_height
                
                # 加载既往用药数据
                self.load_history_medications()
                
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 切换Tab失败: {e}")
    
    def on_enter(self):
        """进入屏幕时调用"""
        super().on_enter()
        self.load_current_medications()
    
    def init_ui(self, dt=0):
        """初始化UI"""
        super().init_ui(dt)
        self.load_current_medications()
        self.load_history_medications()
    
    def load_current_medications(self):
        """加载当前用药数据"""
        try:
            from utils.database import DatabaseManager
            from utils.user_manager import get_user_manager
            
            # 获取当前用户ID
            user_manager = get_user_manager()
            current_user_id = user_manager.get_current_user_custom_id()
            
            medications = []
            
            if current_user_id:
                db = DatabaseManager()
                # 先连接到用户数据库
                db.connect(current_user_id)
                
                # 查询当前用药（状态为active）
                rows = db.execute_query(
                    "SELECT id, name, dosage, frequency, start_date, instructions, notes FROM medications WHERE status = 'active' ORDER BY created_at DESC"
                )
                
                if rows:
                    for row in rows:
                        medications.append({
                            'id': row[0],
                            'name': row[1],
                            'dosage': row[2],
                            'frequency': row[3],
                            'start_date': row[4],
                            'instructions': row[5],
                            'notes': row[6]
                        })
            else:
                KivyLogger.warning("MedicationManagement: 未找到当前用户ID，无法加载用药数据")
            
            self.current_medications = medications
            self.refresh_medications_display()
            
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 加载用药数据失败: {e}")
            self.current_medications = []
            self.refresh_medications_display()
    
    def load_history_medications(self):
        """加载既往用药数据"""
        try:
            from utils.database import DatabaseManager
            from utils.user_manager import get_user_manager
            
            # 获取当前用户ID
            user_manager = get_user_manager()
            current_user_id = user_manager.get_current_user_custom_id()
            
            if not current_user_id:
                KivyLogger.warning("MedicationManagement: 未找到当前用户ID")
                self.refresh_history_display([])
                return
            
            # 使用DatabaseManager查询既往用药数据
            db = DatabaseManager()
            # 先连接到用户数据库
            db.connect(current_user_id)
            
            # 查询既往用药（状态为stopped或deleted）
            query = '''
                SELECT id, name, dosage, frequency, start_date, instructions, notes, status, stop_reason, stop_date
                FROM medications
                WHERE status IN ('stopped', 'deleted')
                ORDER BY created_at DESC
            '''
            
            rows = db.execute_query(query)
            
            history_medications = []
            for row in rows:
                history_medications.append({
                    'id': row[0],
                    'name': row[1],
                    'dosage': row[2],
                    'frequency': row[3],
                    'start_date': row[4],
                    'instructions': row[5],
                    'notes': row[6],
                    'status': row[7],
                    'stop_reason': row[8] or '',
                    'stop_date': row[9] or ''
                })
            
            self.refresh_history_display(history_medications)
            
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 加载既往用药数据失败: {e}")
    
    def show_medication_detail(self, medication_data):
        """显示药物详情对话框"""
        try:
            from kivymd.uix.dialog import MDDialog, MDDialogHeadlineText, MDDialogContentContainer, MDDialogButtonContainer
            from kivymd.uix.button import MDButton, MDButtonText
            from kivymd.uix.boxlayout import MDBoxLayout
            from kivymd.uix.label import MDLabel
            from kivymd.uix.divider import MDDivider
            
            # 创建详情内容
            content = MDBoxLayout(
                orientation='vertical',
                spacing="12dp",
                size_hint_y=None,
                height="400dp"
            )
            
            # 药物名称
            content.add_widget(MDLabel(
                text=f"药物名称: {medication_data.get('name', '')}",
                font_style="Body",
                role="large",
                bold=True,
                theme_text_color="Primary"
            ))
            
            content.add_widget(MDDivider())
            
            # 药物信息
            info_items = [
                ("剂量", medication_data.get('dosage', '')),
                ("使用频次", medication_data.get('frequency', '')),
                ("起始时间", medication_data.get('start_date', '')),
                ("用药原因", medication_data.get('reason', '')),
                ("注意事项", medication_data.get('notes', ''))
            ]
            
            for label, value in info_items:
                if value:
                    content.add_widget(MDLabel(
                        text=f"{label}: {value}",
                        font_style="Body",
                        role="medium",
                        theme_text_color="Secondary"
                    ))
            
            # 创建对话框
            self.detail_dialog = MDDialog(
                MDDialogHeadlineText(text="药物详情"),
                MDDialogContentContainer(
                    content,
                    orientation="vertical"
                ),
                MDDialogButtonContainer(
                    MDButton(
                        MDButtonText(text="关闭"),
                        style="text",
                        on_release=lambda *x: self.detail_dialog.dismiss()
                    ),
                    MDButton(
                        MDButtonText(text="删除"),
                        style="text",
                        theme_bg_color="Custom",
                        md_bg_color=self.get_app().theme.ERROR_COLOR,
                        on_release=lambda *x: self.delete_medication(medication_data)
                    ),
                    MDButton(
                        MDButtonText(text="停用"),
                        style="text",
                        theme_bg_color="Custom",
                        md_bg_color=self.get_app().theme.WARNING_COLOR,
                        on_release=lambda *x: self.stop_medication(medication_data)
                    ),
                    MDButton(
                        MDButtonText(text="提醒设置"),
                        style="filled",
                        md_bg_color=self.get_app().theme.PRIMARY_COLOR,
                        on_release=lambda *x: self.set_medication_reminder(medication_data)
                    ),
                    spacing="8dp"
                )
            )
            
            self.detail_dialog.open()
            
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 显示药物详情失败: {e}")
    
    def delete_medication(self, medication_data):
        """删除药物"""
        try:
            self.detail_dialog.dismiss()
            
            # 确认删除对话框
            from kivymd.uix.dialog import MDDialog, MDDialogHeadlineText, MDDialogSupportingText, MDDialogButtonContainer
            from kivymd.uix.button import MDButton, MDButtonText
            
            confirm_dialog = MDDialog(
                MDDialogHeadlineText(text="确认删除"),
                MDDialogSupportingText(text=f"确定要删除药物 '{medication_data.get('name', '')}' 吗？此操作不可撤销。"),
                MDDialogButtonContainer(
                    MDButton(
                        MDButtonText(text="取消"),
                        style="text",
                        on_release=lambda *x: confirm_dialog.dismiss()
                    ),
                    MDButton(
                        MDButtonText(text="删除"),
                        style="filled",
                        md_bg_color=self.get_app().theme.ERROR_COLOR,
                        on_release=lambda *x: self.confirm_delete_medication(medication_data, confirm_dialog)
                    )
                )
            )
            
            confirm_dialog.open()
            
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 删除药物失败: {e}")
    
    def confirm_delete_medication(self, medication_data, dialog):
        """确认删除药物"""
        try:
            dialog.dismiss()
            
            from utils.database import DatabaseManager
            from utils.user_manager import get_user_manager
            
            # 获取当前用户ID
            user_manager = get_user_manager()
            current_user_id = user_manager.get_current_user_custom_id()
            
            if not current_user_id:
                KivyLogger.warning("MedicationManagement: 未找到当前用户ID")
                self.show_error("删除失败：用户未登录")
                return
            
            # 使用DatabaseManager删除药物
            db = DatabaseManager()
            # 先连接到用户数据库
            db.connect(current_user_id)
            db.execute_query('DELETE FROM medications WHERE id = ?', (medication_data.get('id'),))
            
            self.load_current_medications()
            self.show_info("药物已删除")
            
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 确认删除药物失败: {e}")
            self.show_error("删除失败")
    
    def stop_medication(self, medication_data):
        """停用药物"""
        try:
            self.detail_dialog.dismiss()
            
            # 停药原因对话框
            from kivymd.uix.dialog import MDDialog, MDDialogHeadlineText, MDDialogContentContainer, MDDialogButtonContainer
            from kivymd.uix.button import MDButton, MDButtonText
            from kivymd.uix.textfield import MDTextField, MDTextFieldHintText
            
            stop_field = MDTextField(
                mode="outlined",
                size_hint_y=None,
                height="56dp"
            )
            stop_field.add_widget(MDTextFieldHintText(text="请输入停药原因"))
            
            stop_dialog = MDDialog(
                MDDialogHeadlineText(text="停药原因"),
                MDDialogContentContainer(
                    stop_field,
                    orientation="vertical"
                ),
                MDDialogButtonContainer(
                    MDButton(
                        MDButtonText(text="取消"),
                        style="text",
                        on_release=lambda *x: stop_dialog.dismiss()
                    ),
                    MDButton(
                        MDButtonText(text="确认"),
                        style="filled",
                        md_bg_color=self.get_app().theme.PRIMARY_COLOR,
                        on_release=lambda *x: self.confirm_stop_medication_single(medication_data, stop_field.text, stop_dialog)
                    )
                )
            )
            
            stop_dialog.open()
            
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 停用药物失败: {e}")
    
    def confirm_stop_medication_single(self, medication_data, stop_reason, dialog):
        """确认停用单个药物"""
        try:
            if not stop_reason.strip():
                self.show_error("请输入停药原因")
                return
            
            dialog.dismiss()
            
            from datetime import datetime
            from utils.database import DatabaseManager
            from utils.user_manager import get_user_manager
            
            # 获取当前用户ID
            user_manager = get_user_manager()
            current_user_id = user_manager.get_current_user_custom_id()
            
            if not current_user_id:
                self.show_error("未找到当前用户信息")
                return
            
            db = DatabaseManager()
            # 先连接到用户数据库
            db.connect(current_user_id)
            stop_date = datetime.now().strftime('%Y-%m-%d')
            
            db.execute_query(
                '''
                UPDATE medications
                SET status = 'stopped', stop_reason = ?, stop_date = ?
                WHERE id = ?
                ''', 
                (stop_reason.strip(), stop_date, medication_data.get('id'))
            )
            
            self.load_current_medications()
            self.load_history_medications()
            self.show_info("药物已停用")
            
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 确认停用药物失败: {e}")
            self.show_error("停用失败")
    
    def set_medication_reminder(self, medication_data):
        """设置药物提醒"""
        try:
            self.detail_dialog.dismiss()
            
            # 提醒设置对话框
            from kivymd.uix.dialog import MDDialog, MDDialogHeadlineText, MDDialogSupportingText, MDDialogButtonContainer
            from kivymd.uix.button import MDButton, MDButtonText
            
            reminder_dialog = MDDialog(
                MDDialogHeadlineText(text="提醒设置"),
                MDDialogSupportingText(text=f"为药物 '{medication_data.get('name', '')}' 设置提醒功能暂未实现，敬请期待。"),
                MDDialogButtonContainer(
                    MDButton(
                        MDButtonText(text="确定"),
                        style="filled",
                        md_bg_color=self.get_app().theme.PRIMARY_COLOR,
                        on_release=lambda *x: reminder_dialog.dismiss()
                    )
                )
            )
            
            reminder_dialog.open()
            
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 设置提醒失败: {e}")
    
    def load_stopped_medications(self):
        """加载停用药物数据"""
        try:
            from utils.database import DatabaseManager
            from utils.user_manager import get_user_manager
            
            # 获取当前用户ID
            user_manager = get_user_manager()
            current_user_id = user_manager.get_current_user_custom_id()
            
            if not current_user_id:
                return []
            
            db = DatabaseManager()
            # 先连接到用户数据库
            db.connect(current_user_id)
            stopped_medications = []
            
            # 查询停用药物（状态为stopped）
            rows = db.execute_query(
                '''
                SELECT id, name, dosage, frequency, start_date, instructions, notes, status, stop_reason, stop_date
                FROM medications
                WHERE status = 'stopped'
                ORDER BY created_at DESC
                '''
            )
            
            for row in rows:
                stopped_medications.append({
                    'id': row[0],
                    'name': row[1],
                    'dosage': row[2],
                    'frequency': row[3],
                    'start_date': row[4],
                    'instructions': row[5],
                    'notes': row[6],
                    'status': row[7],
                    'stop_reason': row[8] or '',
                    'stop_date': row[9] or ''
                })
            
            return stopped_medications
            
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 加载停用药物数据失败: {e}")
            return []
    
    def search_history_medications(self):
        """搜索既往用药记录"""
        try:
            if not hasattr(self, 'ids') or 'history_search_field' not in self.ids:
                return
            
            search_text = self.ids.history_search_field.text.strip()
            
            # 从数据库搜索既往用药数据
            from utils.database import DatabaseManager
            from utils.user_manager import get_user_manager
            
            history_medications = []
            
            try:
                # 获取当前用户ID
                user_manager = get_user_manager()
                current_user_id = user_manager.get_current_user_custom_id()
                
                if not current_user_id:
                    KivyLogger.error("MedicationManagement: 未找到当前用户")
                    return
                
                # 使用DatabaseManager查询数据
                db_manager = DatabaseManager()
                # 先连接到用户数据库
                db_manager.connect(current_user_id)
                
                if search_text:
                    # 按药物名称搜索
                    query = '''
                        SELECT id, name, dosage, frequency, start_date, instructions, notes, status, stop_reason, stop_date
                        FROM medications
                        WHERE status IN ('stopped', 'deleted') AND name LIKE ?
                        ORDER BY created_at DESC
                    '''
                    rows = db_manager.execute_query(query, (f'%{search_text}%',))
                else:
                    # 显示所有既往用药
                    query = '''
                        SELECT id, name, dosage, frequency, start_date, instructions, notes, status, stop_reason, stop_date
                        FROM medications
                        WHERE status IN ('stopped', 'deleted')
                        ORDER BY created_at DESC
                    '''
                    rows = db_manager.execute_query(query)
                
                if rows:
                    for row in rows:
                        history_medications.append({
                            'id': row[0],
                            'name': row[1],
                            'dosage': row[2],
                            'frequency': row[3],
                            'start_date': row[4],
                            'instructions': row[5],
                            'notes': row[6],
                            'status': row[7],
                            'stop_reason': row[8] if len(row) > 8 else '',
                            'stop_date': row[9] if len(row) > 9 else ''
                        })
            except Exception as e:
                KivyLogger.error(f"MedicationManagement: 搜索既往用药数据库操作失败: {e}")
                return
            
            self.refresh_history_display(history_medications)
            
            if search_text:
                self.show_info(f"找到 {len(history_medications)} 条匹配记录")
            
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 搜索既往用药失败: {e}")
            self.show_error("搜索失败")
    
    def clear_history_search(self):
        """清空搜索条件"""
        try:
            if hasattr(self, 'ids') and 'history_search_field' in self.ids:
                self.ids.history_search_field.text = ""
            
            # 重新加载所有既往用药记录
            self.load_history_medications()
            
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 清空搜索失败: {e}")
    
    def refresh_history_display(self, history_medications=None):
        """刷新既往用药显示"""
        try:
            if not hasattr(self, 'ids') or 'history_medications_container' not in self.ids:
                return
            
            # 清空现有内容
            self.ids.history_medications_container.clear_widgets()
            
            # 如果没有传入数据，从数据库加载停用药物
            if not history_medications:
                history_medications = self.load_stopped_medications()
            
            if not history_medications:
                # 显示空状态
                from kivymd.uix.label import MDLabel
                empty_label = MDLabel(
                    text="暂无既往用药记录",
                    halign="center",
                    theme_text_color="Secondary",
                    font_style="Body",
                    role="large"
                )
                self.ids.history_medications_container.add_widget(empty_label)
                return
            
            # 添加既往用药记录
            for index, med in enumerate(history_medications):
                card = HistoryMedicationCard(
                    name=med["name"],
                    dosage=med["dosage"],
                    frequency=med["frequency"],
                    start_date=med["start_date"],
                    stop_date=med.get("stop_date", ""),
                    reason=med.get("reason", ""),
                    stop_reason=med.get("stop_reason", ""),
                    notes=med.get("notes", ""),
                    row_index=index,
                    medication_data=med
                )
                self.ids.history_medications_container.add_widget(card)
                
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 刷新既往用药显示失败: {e}")
    
    def refresh_medications_display(self):
        """刷新用药显示"""
        container = self.ids.current_medications_container
        container.clear_widgets()
        
        for index, medication in enumerate(self.current_medications):
            card = CurrentMedicationCard(
                name=medication.get('name', ''),
                dosage=medication.get('dosage', ''),
                frequency=medication.get('frequency', ''),
                start_date=medication.get('start_date', ''),
                reason=medication.get('reason', ''),
                notes=medication.get('notes', ''),
                row_index=index,
                medication_data=medication
            )
            container.add_widget(card)
    
    def stop_selected_medications(self):
        """停用选中的药物"""
        if not self.selected_medications:
            self.show_error("请选择要停用的药物")
            return
        
        # 弹出对话框确认停药原因
        self.stop_dialog = MDDialog(
            MDDialogHeadlineText(text="停药原因"),
            MDDialogContentContainer(
                MDTextField(id="stop_reason", hint_text="请输入停药原因")
            ),
            MDDialogButtonContainer(
                MDButton(text="取消", on_release=lambda *x: self.stop_dialog.dismiss()),
                MDButton(text="确认", on_release=self.confirm_stop_medication)
            )
        )
        self.stop_dialog.open()
    
    def confirm_stop_medication(self, *args):
        stop_reason = self.stop_dialog.content_cls.ids.stop_reason.text.strip()
        if not stop_reason:
            self.show_error("请输入停药原因")
            return
        
        self.stop_dialog.dismiss()
        
        from utils.database import DatabaseManager
        from utils.user_manager import get_user_manager
        from datetime import datetime
        
        try:
            # 获取当前用户ID
            user_manager = get_user_manager()
            current_user_id = user_manager.get_current_user_custom_id()
            
            if not current_user_id:
                KivyLogger.error("MedicationManagement: 未找到当前用户")
                self.show_error("用户验证失败")
                return
            
            # 使用DatabaseManager更新数据
            db_manager = DatabaseManager()
            # 先连接到用户数据库
            db_manager.connect(current_user_id)
            stop_date = datetime.now().strftime('%Y-%m-%d')
            
            for med_id in self.selected_medications:
                query = '''
                    UPDATE medications
                    SET status = 'stopped', stop_reason = ?, stop_date = ?
                    WHERE id = ?
                '''
                db_manager.execute_query(query, (stop_reason, stop_date, med_id))
                
                if stop_reason.lower() == '过敏':
                    self.update_allergy_record(med_id)
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 停用药物数据库操作失败: {e}")
            self.show_error("停用药物失败")
            return
        
        self.selected_medications = []
        self.load_current_medications()
        self.load_history_medications()
        self.show_info("药物已停用")
    
    def update_allergy_record(self, med_id):
        """更新过敏记录"""
        try:
            from utils.health_data_manager import HealthDataManager
            from utils.database_manager import DatabaseManager
            from utils.user_manager import UserManager
            
            # 获取当前用户ID
            user_manager = UserManager()
            current_user = user_manager.get_current_user()
            if not current_user:
                print("未找到当前用户")
                return
            
            user_custom_id = current_user.get('custom_id')
            if not user_custom_id:
                print("用户custom_id为空")
                return
            
            # 使用DatabaseManager查询药物名称
            db_manager = DatabaseManager()
            query = 'SELECT medication_name FROM medications WHERE id = ? AND user_id = ?'
            result = db_manager.fetch_one(query, (med_id, user_custom_id))
            
            if result:
                med_name = result[0]
                manager = HealthDataManager.get_instance()
                allergy_data = {
                    'type': 'allergy',
                    'medication': med_name,
                    'recorded_at': datetime.now().isoformat()
                }
                manager.save_health_data(allergy_data)
        except Exception as e:
            KivyLogger.error(f"更新过敏记录失败: {e}")
    
    def show_frequency_menu(self, text_field):
        """显示使用频次下拉菜单"""
        if self.frequency_menu:
            self.frequency_menu.dismiss()
        
        menu_items = []
        for option in self.frequency_options:
            menu_items.append({
                "text": option,
                "on_release": lambda x=option: self.set_frequency(x, text_field)
            })
        
        self.frequency_menu = MDDropdownMenu(
            caller=text_field,
            items=menu_items,
            max_height=dp(200)
        )
        self.frequency_menu.open()
    
    def set_frequency(self, frequency, text_field):
        """设置使用频次"""
        text_field.text = frequency
        if self.frequency_menu:
            self.frequency_menu.dismiss()
    
    def show_reason_menu(self, text_field):
        """显示用药原因下拉菜单"""
        if self.reason_menu:
            self.reason_menu.dismiss()
        
        menu_items = []
        for option in self.reason_options:
            menu_items.append({
                "text": option,
                "on_release": lambda x=option: self.set_reason(x, text_field)
            })
        
        self.reason_menu = MDDropdownMenu(
            caller=text_field,
            items=menu_items,
            max_height=dp(200)
        )
        self.reason_menu.open()
    
    def set_reason(self, reason, text_field):
        """设置用药原因"""
        text_field.text = reason
        if self.reason_menu:
            self.reason_menu.dismiss()
    
    def show_notes_menu(self, text_field):
        """显示注意事项下拉菜单"""
        if self.notes_menu:
            self.notes_menu.dismiss()
        
        menu_items = []
        for option in self.notes_options:
            menu_items.append({
                "text": option,
                "on_release": lambda x=option: self.set_notes(x, text_field)
            })
        
        self.notes_menu = MDDropdownMenu(
            caller=text_field,
            items=menu_items,
            max_height=dp(200)
        )
        self.notes_menu.open()
    
    def set_notes(self, notes, text_field):
        """设置注意事项"""
        text_field.text = notes
        if self.notes_menu:
            self.notes_menu.dismiss()
    
    def show_date_picker(self, text_field):
        """显示日期选择器"""
        # 简化版日期选择，直接设置今天的日期
        today = datetime.now().strftime('%Y-%m-%d')
        text_field.text = today
    
    def add_medication_to_list(self):
        """添加药物到列表"""
        try:
            # 获取输入字段的值
            name = self.ids.medication_name_field.text.strip()
            dosage = self.ids.dosage_field.text.strip()
            frequency = self.ids.frequency_field.text.strip()
            start_date = self.ids.start_date_field.text.strip()
            reason = self.ids.reason_field.text.strip()
            notes = self.ids.notes_field.text.strip()
            
            # 验证必填字段
            if not name:
                self.show_error("请输入药物名称")
                return
            
            # 检查是否已存在相同药物名称
            for medication in self.current_medications:
                if medication.get('name', '').lower() == name.lower():
                    self.show_error(f"该药物'{name}'已存在列表中，请先停用再添加")
                    return
            
            if not dosage:
                self.show_error("请输入药物剂量")
                return
            
            if not frequency:
                self.show_error("请选择使用频次")
                return
            
            if not start_date:
                self.show_error("请选择起始时间")
                return
            
            if not reason:
                self.show_error("请选择用药原因")
                return
            
            if not notes:
                self.show_error("请选择注意事项")
                return
            
            # 创建新的药物记录
            new_medication = {
                'id': len(self.current_medications) + 1,
                'name': name,
                'dosage': dosage,
                'frequency': frequency,
                'start_date': start_date,
                'reason': reason,
                'notes': notes
            }
            
            # 添加到列表
            self.current_medications.append(new_medication)
            
            # 刷新显示
            self.refresh_medications_display()
            
            # 清空输入字段
            self.clear_input_fields()
            
            # 显示成功消息
            self.show_info("药物添加成功")
            
            # 这里应该保存到数据库
            self.save_medication_to_db(new_medication)
            
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 添加药物失败: {e}")
            self.show_error("添加药物失败")
    
    def clear_input_fields(self):
        """清空输入字段"""
        self.ids.medication_name_field.text = ""
        self.ids.dosage_field.text = ""
        self.ids.frequency_field.text = ""
        self.ids.start_date_field.text = ""
        self.ids.reason_field.text = ""
        self.ids.notes_field.text = ""
    
    def save_medication_to_db(self, medication_data):
        """保存药物数据到数据库"""
        try:
            from utils.database import DatabaseManager
            from utils.user_manager import get_user_manager
            
            # 获取当前用户ID
            user_manager = get_user_manager()
            current_user_id = user_manager.get_current_user_custom_id()
            if not current_user_id:
                self.show_error("未找到当前用户ID")
                return
            
            # 使用DatabaseManager执行数据库操作
            db_manager = DatabaseManager()
            # 先连接到用户数据库
            db_manager.connect(current_user_id)
            
            # 创建表（如果不存在），与后端数据库结构保持一致
            db_manager.execute_query('''
                CREATE TABLE IF NOT EXISTS medications (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    custom_id TEXT NOT NULL,
                    name TEXT NOT NULL,
                    dosage TEXT,
                    frequency TEXT,
                    start_date TEXT,
                    end_date TEXT,
                    instructions TEXT,
                    prescription_required INTEGER DEFAULT 0,
                    notes TEXT,
                    medication_type TEXT,
                    status TEXT DEFAULT 'active',
                    stop_reason TEXT,
                    stop_date TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 插入数据到本地数据库
            db_manager.execute_query('''
                INSERT INTO medications (custom_id, name, dosage, frequency, start_date, notes, status, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
            ''', (
                current_user_id,
                medication_data['name'],
                medication_data['dosage'],
                medication_data['frequency'],
                medication_data['start_date'],
                medication_data.get('notes', ''),
                'active'
            ))
            
            # 同步到后端服务器
            self.sync_medication_to_backend(medication_data, current_user_id)
            
            KivyLogger.info(f"MedicationManagement: 保存药物数据成功: {medication_data['name']}")
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 保存药物数据失败: {e}")
            self.show_error(f"保存失败: {str(e)}")

    
    def sync_to_server(self, medication_data):
        """同步数据到后端服务器"""
        try:
            # TODO: 实现与后端服务器的数据同步
            # 这里应该调用后端API
            import requests
            import json
            
            # 示例API调用（需要根据实际后端接口调整）
            # api_url = "https://your-backend-api.com/medications"
            # headers = {'Content-Type': 'application/json'}
            # response = requests.post(api_url, data=json.dumps(medication_data), headers=headers)
            
            KivyLogger.info(f"MedicationManagement: 数据同步到服务器: {medication_data['name']}")
        except Exception as e:
            KivyLogger.warning(f"MedicationManagement: 服务器同步失败，数据已保存到本地: {e}")
    
    def on_medication_selected(self, medication_data, selected):
        """处理药物选择状态变化"""
        if selected:
            if medication_data not in self.selected_medications:
                self.selected_medications.append(medication_data)
        else:
            if medication_data in self.selected_medications:
                self.selected_medications.remove(medication_data)
    
    def delete_selected_medications(self):
        """删除选中的药物"""
        # 获取当前选中的药物
        selected_items = []
        container = self.ids.current_medications_container
        for child in container.children:
            if hasattr(child, 'selected') and child.selected:
                selected_items.append(child)
        
        if not selected_items:
            self.show_error("请先选择要删除的药物")
            return
        
        self.selected_medications = selected_items
        # 显示确认对话框
        self.show_delete_confirmation_dialog()
    
    def show_delete_confirmation_dialog(self):
        """显示删除确认对话框"""
        if self.dialog:
            self.dialog.dismiss()
        
        content = MDBoxLayout(
            orientation='vertical',
            spacing=dp(16),
            size_hint_y=None,
            height=dp(100)
        )
        
        content.add_widget(MDLabel(
            text=f"确定要删除选中的 {len(self.selected_medications)} 个药物吗？",
            theme_text_color="Primary",
            size_hint_y=None,
            height=dp(40)
        ))
        
        self.dialog = MDDialog(
            MDDialogHeadlineText(text="确认删除"),
            MDDialogContentContainer(content),
            MDDialogButtonContainer(
                MDButton(
                    MDButtonText(text="取消"),
                    style="text",
                    on_release=lambda x: self.dialog.dismiss()
                ),
                MDButton(
                    MDButtonText(text="确认删除"),
                    style="filled",
                    md_bg_color=self.get_app().theme.ERROR_COLOR,
                    on_release=self.confirm_delete_medications
                )
            )
        )
        self.dialog.open()
    
    def confirm_delete_medications(self, *args):
        """确认删除药物"""
        try:
            deleted_count = 0
            for medication_row in self.selected_medications:
                if hasattr(medication_row, 'medication_data'):
                    medication_data = medication_row.medication_data
                    
                    # 更新数据库状态为deleted
                    self.update_medication_status(medication_data['id'], 'deleted')
                    
                    # 记录过敏信息
                    self.record_allergy_info(medication_data['name'], "药物删除")
                    
                    # 从当前用药列表中移除
                    if medication_data in self.current_medications:
                        self.current_medications.remove(medication_data)
                    
                    deleted_count += 1
            
            # 刷新显示
            self.refresh_medications_display()
            self.refresh_history_display()  # 刷新既往用药
            
            # 清空选择
            self.selected_medications.clear()
            
            # 关闭对话框
            if self.dialog:
                self.dialog.dismiss()
            
            # 显示成功消息
            self.show_info(f"已删除 {deleted_count} 个药物")
            
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 删除药物失败: {e}")
            self.show_error("删除药物失败")
    
    def stop_selected_medications(self):
        """停用选中的药物"""
        # 获取当前选中的药物
        selected_items = []
        container = self.ids.current_medications_container
        for child in container.children:
            if hasattr(child, 'selected') and child.selected:
                selected_items.append(child)
        
        if not selected_items:
            self.show_error("请先选择要停用的药物")
            return
        
        self.selected_medications = selected_items
        # 显示停药原因对话框
        self.show_stop_reason_dialog()
    
    def show_stop_reason_dialog(self):
        """显示停药原因对话框"""
        if self.dialog:
            self.dialog.dismiss()
        
        content = MDBoxLayout(
            orientation='vertical',
            spacing=dp(16),
            size_hint_y=None,
            height=dp(120)
        )
        
        content.add_widget(MDLabel(
            text="请选择停药原因：",
            theme_text_color="Primary",
            size_hint_y=None,
            height=dp(40)
        ))
        
        reason_field = MDTextField(
            mode="outlined",
            size_hint_y=None,
            height=dp(56)
        )
        reason_field.add_widget(MDTextFieldHintText(text="停药原因"))
        content.add_widget(reason_field)
        
        self.dialog = MDDialog(
            MDDialogHeadlineText(text="停用药物"),
            MDDialogContentContainer(content),
            MDDialogButtonContainer(
                MDButton(
                    MDButtonText(text="取消"),
                    style="text",
                    on_release=lambda x: self.dialog.dismiss()
                ),
                MDButton(
                    MDButtonText(text="确认停用"),
                    style="filled",
                    md_bg_color=self.get_app().theme.WARNING_COLOR,
                    on_release=lambda x: self.confirm_stop_medications(reason_field.text)
                )
            )
        )
        self.dialog.open()
    

    
    def record_allergy_info(self, medication_name, reason):
        """记录过敏信息到基本健康信息"""
        try:
            # 这里应该实现将过敏信息保存到基本健康信息数据表的逻辑
            allergy_data = {
                'medication_name': medication_name,
                'allergy_reason': reason,
                'record_date': datetime.now().strftime('%Y-%m-%d')
            }
            
            KivyLogger.info(f"MedicationManagement: 记录过敏信息: {allergy_data}")
            # 这里应该调用API或数据库保存过敏信息
            
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 记录过敏信息失败: {e}")
    
    def set_reminders_for_selected(self):
        """为选中的药物设置提醒"""
        # 获取当前选中的药物
        selected_items = []
        container = self.ids.current_medications_container
        for child in container.children:
            if hasattr(child, 'selected') and child.selected:
                selected_items.append(child)
        
        if not selected_items:
            self.show_error("请先选择要设置提醒的药物")
            return
        
        for medication_row in selected_items:
            if hasattr(medication_row, 'medication_data'):
                self.set_medication_reminder(medication_row.medication_data)
        
        self.show_info(f"已为 {len(selected_items)} 个药物设置提醒")
    
    def set_medication_reminder(self, medication_data):
        """设置单个药物的提醒"""
        try:
            # 显示提醒设置对话框
            content_label = MDLabel(
                text=f"已为 {medication_data.get('name', '此药品')} 设置提醒，将在服药时间前通知您。\n\n同时已设置复查提醒，用于观察疗效及副作用。",
                font_style="Body",
                role="large",
                size_hint_y=None,
                height=dp(80)
            )
            
            dialog = MDDialog(
                MDDialogHeadlineText(
                    text="用药提醒",
                ),
                MDDialogContentContainer(
                    content_label,
                    orientation="vertical",
                ),
                MDDialogButtonContainer(
                    MDButton(
                        MDButtonText(text="确定"),
                        style="text",
                        on_release=lambda x: dialog.dismiss()
                    ),
                ),
            )
            dialog.open()
            
            KivyLogger.info(f"MedicationManagement: 设置药物提醒: {medication_data.get('name', '')}")
            
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 设置药物提醒失败: {e}")
    
    def refresh_data(self):
        """刷新数据"""
        self.load_current_medications()
        self.load_history_medications()
        self.show_info("数据已刷新")
    
    def go_back(self):
        """返回上一页"""
        app = self.get_app()
        if app and app.root:
            app.root.current = 'health_data_management_screen'
    
    def show_info(self, message):
        """显示信息提示"""
        try:
            snackbar = MDSnackbar(
                MDSnackbarText(text=message),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                size_hint_x=0.8,
                md_bg_color=self.get_app().theme.SUCCESS_COLOR
            )
            snackbar.open()
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 显示信息提示失败: {e}")
    

    

    

    

        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 设置检查复查提醒失败: {e}")
    
    def delete_medication_from_db(self, medication_id):
        """从数据库删除药物"""
        try:
            from utils.database import DatabaseManager
            db = DatabaseManager()
            
            # 删除药物记录
            db.execute_query(
                "DELETE FROM medications WHERE id = ?",
                (medication_id,)
            )
            
            KivyLogger.info(f"MedicationManagement: 已删除药物ID: {medication_id}")
            
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 删除药物失败: {e}")
            raise e
    
    def update_medication_status(self, medication_id, status, stop_reason=None):
        """更新药物状态"""
        try:
            from utils.database import DatabaseManager
            from utils.user_manager import get_user_manager
            from datetime import datetime
            
            # 获取当前用户ID
            user_manager = get_user_manager()
            current_user_id = user_manager.get_current_user_custom_id()
            
            if not current_user_id:
                KivyLogger.warning("MedicationManagement: 未找到当前用户ID，无法更新药物状态")
                return
            
            db = DatabaseManager()
            
            if status == 'stopped':
                # 更新为停用状态
                db.execute_query(
                    "UPDATE medications SET status = ?, stop_date = ?, stop_reason = ? WHERE id = ?",
                    (status, datetime.now().strftime('%Y-%m-%d'), stop_reason, medication_id),
                    user_id=current_user_id
                )
            else:
                # 更新其他状态
                db.execute_query(
                    "UPDATE medications SET status = ? WHERE id = ?",
                    (status, medication_id),
                    user_id=current_user_id
                )
            
            KivyLogger.info(f"MedicationManagement: 已更新药物ID {medication_id} 状态为: {status}")
            
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 更新药物状态失败: {e}")
            raise e
    


    def show_error(self, message):
        """显示错误提示"""
        try:
            snackbar = MDSnackbar(
                MDSnackbarText(text=message),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                size_hint_x=0.8,
                md_bg_color=self.get_app().theme_cls.errorColor
            )
            snackbar.open()
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 显示错误提示失败: {e}")
    
    def sync_medication_to_backend(self, medication_data, user_id):
        """同步药物数据到后端服务器"""
        try:
            import requests
            from utils.app_config import API_CONFIG
            from utils.auth_manager import get_auth_manager
            
            API_BASE_URL = API_CONFIG['BASE_URL']
            
            # 获取认证头部
            auth_manager = get_auth_manager()
            headers = auth_manager.get_auth_headers()
            
            if not headers.get('Authorization'):
                KivyLogger.warning("MedicationManagement: 未找到有效的认证token，无法同步到后端")
                return
            
            # 准备发送到后端的数据
            backend_data = {
                'custom_id': user_id,
                'name': medication_data['name'],
                'dosage': medication_data.get('dosage', ''),
                'frequency': medication_data.get('frequency', ''),
                'start_date': medication_data.get('start_date', ''),
                'instructions': medication_data.get('notes', ''),
                'notes': medication_data.get('notes', ''),
                'status': 'active',
                'medication_type': medication_data.get('medication_type', ''),
                'prescription_required': 0
            }
            
            # 添加Content-Type到headers
            headers['Content-Type'] = 'application/json'
            
            # 发送POST请求到后端API
            response = requests.post(
                f"{API_BASE_URL}/medications",
                json=backend_data,
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 201:
                KivyLogger.info(f"MedicationManagement: 药物数据已成功同步到后端: {medication_data['name']}")
            else:
                KivyLogger.warning(f"MedicationManagement: 后端同步失败，状态码: {response.status_code}, 响应: {response.text}")
                
        except requests.exceptions.RequestException as e:
            KivyLogger.warning(f"MedicationManagement: 网络错误，无法同步到后端: {e}")
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 同步到后端时发生错误: {e}")

class HistoryMedicationTableRow(MDBoxLayout):
    """既往用药表格行组件"""
    name = StringProperty("")
    dosage = StringProperty("")
    frequency = StringProperty("")
    start_date = StringProperty("")
    stop_date = StringProperty("")
    reason = StringProperty("")
    stop_reason = StringProperty("")
    row_index = NumericProperty(0)
    medication_data = ObjectProperty(None)
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
    def on_select(self, active):
        """选择状态改变时的回调"""
        self.selected = active
        screen = self.get_screen()
        if screen:
            if active:
                if self.medication_data not in screen.selected_medications:
                    screen.selected_medications.append(self.medication_data)
            else:
                if self.medication_data in screen.selected_medications:
                    screen.selected_medications.remove(self.medication_data)
    
    def get_screen(self):
        """获取所属的屏幕对象"""
        parent = self.parent
        while parent:
            if hasattr(parent, 'name') and parent.name == 'medication_management_screen':
                return parent
            parent = parent.parent
        return None

class HistoryMedicationTableHeader(MDBoxLayout):
    """既往用药表格头部组件"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

# 注册组件
Factory.register('MedicationManagementScreen', cls=MedicationManagementScreen)
Factory.register('CurrentMedicationCard', cls=CurrentMedicationCard)
Factory.register('HistoryMedicationCard', cls=HistoryMedicationCard)
Factory.register('HistoryMedicationTableRow', cls=HistoryMedicationTableRow)

# 加载KV字符串
Builder.load_string(KV)