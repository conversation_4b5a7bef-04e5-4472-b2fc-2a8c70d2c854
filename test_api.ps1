$headers = @{
    'Authorization' = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************.Uy8Ej8Uy8Ej8Uy8Ej8Uy8Ej8Uy8Ej8Uy8Ej8Uy8Ej8'
    'Content-Type' = 'application/json'
}

try {
    $response = Invoke-WebRequest -Uri 'http://localhost:8006/api/user-health-records/SM_008' -Method GET -Headers $headers
    Write-Host "Status: $($response.StatusCode)"
    Write-Host "Content: $($response.Content)"
} catch {
    Write-Host "Error: $($_.Exception.Message)"
    Write-Host "Response: $($_.Exception.Response)"
}