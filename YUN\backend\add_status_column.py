#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为documents表添加status列
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, inspect, text
from app.core.config import settings
from app.db.base_session import SessionLocal

def add_status_column():
    """为documents表添加status列"""
    print("=== 为documents表添加status列 ===")
    
    try:
        # 创建数据库连接
        db = SessionLocal()
        engine = db.bind
        inspector = inspect(engine)
        
        # 检查表是否存在
        if 'documents' not in inspector.get_table_names():
            print("❌ documents表不存在")
            return
            
        # 获取当前列信息
        columns = inspector.get_columns('documents')
        column_names = [col['name'] for col in columns]
        
        print(f"当前表列: {column_names}")
        
        # 检查status列是否存在
        if 'status' in column_names:
            print("✅ status列已存在")
            return
            
        # 添加status列
        print("添加status列...")
        sql = "ALTER TABLE documents ADD COLUMN status VARCHAR DEFAULT 'active'"
        print(f"执行SQL: {sql}")
        
        db.execute(text(sql))
        db.commit()
        
        print("✅ 成功添加status列")
        
        # 验证添加结果
        print("\n=== 验证添加结果 ===")
        columns_after = inspector.get_columns('documents')
        for i, col in enumerate(columns_after, 1):
            print(f"{i:2d}. {col['name']:25s} {str(col['type']):20s} {'NOT NULL' if not col['nullable'] else 'NULL':8s}")
            
        db.close()
        
    except Exception as e:
        print(f"❌ 添加失败: {e}")
        import traceback
        traceback.print_exc()
        if 'db' in locals():
            db.rollback()
            db.close()

if __name__ == "__main__":
    add_status_column()