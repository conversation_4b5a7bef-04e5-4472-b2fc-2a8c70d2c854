module.exports = {
  // 基础格式化选项
  printWidth: 100, // 每行最大字符数
  tabWidth: 2, // 缩进空格数
  useTabs: false, // 使用空格而不是制表符
  semi: false, // 不使用分号
  singleQuote: true, // 使用单引号
  quoteProps: 'as-needed', // 仅在需要时为对象属性添加引号
  trailingComma: 'none', // 不使用尾随逗号
  bracketSpacing: true, // 对象字面量的大括号间添加空格
  bracketSameLine: false, // 将多行元素的 > 放在下一行
  arrowParens: 'avoid', // 箭头函数参数只有一个时不使用括号
  
  // Vue 特定选项
  vueIndentScriptAndStyle: true, // Vue 文件中的 script 和 style 标签缩进
  
  // HTML 相关选项
  htmlWhitespaceSensitivity: 'css', // HTML 空白敏感性
  
  // 换行符选项
  endOfLine: 'lf', // 使用 LF 换行符
  
  // 嵌入语言格式化
  embeddedLanguageFormatting: 'auto',
  
  // 文件覆盖配置
  overrides: [
    {
      files: '*.vue',
      options: {
        parser: 'vue',
        printWidth: 120, // Vue 文件允许更长的行
        htmlWhitespaceSensitivity: 'ignore'
      }
    },
    {
      files: '*.json',
      options: {
        parser: 'json',
        trailingComma: 'none'
      }
    },
    {
      files: '*.md',
      options: {
        parser: 'markdown',
        printWidth: 80,
        proseWrap: 'always'
      }
    },
    {
      files: '*.css',
      options: {
        parser: 'css',
        singleQuote: false
      }
    },
    {
      files: '*.scss',
      options: {
        parser: 'scss',
        singleQuote: false
      }
    },
    {
      files: '*.less',
      options: {
        parser: 'less',
        singleQuote: false
      }
    },
    {
      files: '*.yaml',
      options: {
        parser: 'yaml',
        singleQuote: false
      }
    },
    {
      files: '*.yml',
      options: {
        parser: 'yaml',
        singleQuote: false
      }
    }
  ]
}