#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试评估模板API响应，检查是否包含完整的问题信息
"""

import requests
import json
import sqlite3
from datetime import datetime

def test_template_api():
    """测试评估模板API响应"""
    print("=== 测试评估模板API响应 ===")
    print(f"测试时间: {datetime.now()}")
    
    # 后端API基础URL
    base_url = "http://localhost:8006"
    
    # 首先检查数据库中的模板信息
    print("\n1. 检查数据库中的评估模板...")
    try:
        conn = sqlite3.connect('YUN/backend/app.db')
        cursor = conn.cursor()
        
        # 查询评估模板
        cursor.execute("""
            SELECT id, template_key, name, assessment_type 
            FROM assessment_templates 
            ORDER BY id
        """)
        templates = cursor.fetchall()
        
        print(f"找到 {len(templates)} 个评估模板:")
        for template in templates:
            print(f"  ID: {template[0]}, Key: {template[1]}, Name: {template[2]}, Type: {template[3]}")
        
        conn.close()
        
        if not templates:
            print("数据库中没有评估模板，无法测试API")
            return
            
    except Exception as e:
        print(f"检查数据库失败: {e}")
        return
    
    # 测试获取模板列表API
    print("\n2. 测试获取模板列表API...")
    try:
        response = requests.get(f"{base_url}/api/templates/assessment-templates")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")
            
            if 'data' in data:
                templates_list = data['data']
                print(f"模板数量: {len(templates_list)}")
                
                if templates_list:
                    first_template = templates_list[0]
                    print(f"第一个模板字段: {list(first_template.keys())}")
                    print(f"第一个模板ID: {first_template.get('id')}")
                    print(f"第一个模板名称: {first_template.get('name')}")
                    
                    # 检查是否包含questions字段
                    if 'questions' in first_template:
                        print(f"包含questions字段，问题数量: {len(first_template['questions'])}")
                    else:
                        print("不包含questions字段（这是正常的，列表API通常不返回详细问题）")
        else:
            print(f"API调用失败: {response.text}")
            
    except Exception as e:
        print(f"测试模板列表API失败: {e}")
    
    # 测试获取单个模板详情API
    print("\n3. 测试获取单个模板详情API...")
    
    # 测试几个常见的模板ID
    test_template_ids = [1, 4, 5, 'standard_1', 'standard_4']
    
    for template_id in test_template_ids:
        print(f"\n--- 测试模板ID: {template_id} ---")
        try:
            response = requests.get(f"{base_url}/api/templates/assessment-templates/{template_id}")
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"响应数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")
                
                if 'data' in data:
                    template_data = data['data']
                    print(f"模板字段: {list(template_data.keys())}")
                    print(f"模板名称: {template_data.get('name')}")
                    print(f"模板类型: {template_data.get('assessment_type')}")
                    
                    # 检查questions字段
                    if 'questions' in template_data:
                        questions = template_data['questions']
                        print(f"包含questions字段，问题数量: {len(questions)}")
                        
                        if questions:
                            first_question = questions[0]
                            print(f"第一个问题字段: {list(first_question.keys())}")
                            print(f"第一个问题文本: {first_question.get('question_text', '无')}")
                            print(f"第一个问题类型: {first_question.get('question_type', '无')}")
                            print(f"第一个问题选项: {first_question.get('options', [])}")
                        else:
                            print("questions字段为空")
                    else:
                        print("不包含questions字段")
                        
                elif 'success' in data and not data['success']:
                    print(f"API返回失败: {data.get('message', '未知错误')}")
                else:
                    print(f"响应数据: {json.dumps(data, ensure_ascii=False, indent=2)[:500]}...")
                    
            elif response.status_code == 404:
                print(f"模板不存在")
            else:
                print(f"API调用失败: {response.text}")
                
        except Exception as e:
            print(f"测试模板{template_id}失败: {e}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_template_api()