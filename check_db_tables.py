import sqlite3

# 连接数据库
db_path = "YUN/backend/app.db"
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

print("检查数据库表结构...")
print("="*50)

# 获取所有表名
cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
tables = cursor.fetchall()

print(f"数据库中共有 {len(tables)} 个表:")
for table in tables:
    table_name = table[0]
    print(f"\n表名: {table_name}")
    
    # 获取表结构
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = cursor.fetchall()
    
    print("  字段:")
    for col in columns:
        print(f"    {col[1]} ({col[2]})")
    
    # 获取记录数
    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
    count = cursor.fetchone()[0]
    print(f"  记录数: {count}")

conn.close()
print("\n表结构检查完成")