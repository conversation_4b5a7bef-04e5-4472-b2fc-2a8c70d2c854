{"summary": {"total_checks": 12, "passed_checks": 1, "failed_checks": 11, "success_rate": 8.333333333333332, "check_time": "2025-06-30T09:47:39.284913", "database_path": "C:\\Users\\<USER>\\Desktop\\health-Trea\\app.db"}, "results": [{"check_name": "数据库文件存在性", "success": true, "message": "数据库文件存在 (路径: C:\\Users\\<USER>\\Desktop\\health-Trea\\app.db, 大小: 475136 bytes)", "timestamp": "2025-06-30T09:47:39.202796", "details": {"path": "C:\\Users\\<USER>\\Desktop\\health-Trea\\app.db", "size": 475136}}, {"check_name": "核心表完整性", "success": false, "message": "缺少 1 个核心表", "timestamp": "2025-06-30T09:47:39.207904", "details": {"missing_tables": ["physical_exams (体检报告表)"], "existing_tables": ["users (用户表)", "questionnaires (问卷表)", "questionnaire_results (问卷结果表)", "questionnaire_templates (问卷模板表)", "assessments (评估量表表)", "assessment_results (评估结果表)", "assessment_templates (评估模板表)", "health_records (健康记录表)", "medical_records (医疗记录表)", "lab_reports (实验室报告表)"], "total_tables": 44}}, {"check_name": "问卷模板表结构", "success": false, "message": "问卷模板表缺少必要字段: ['template_key', 'questions']", "timestamp": "2025-06-30T09:47:39.214395", "details": {"missing_columns": ["template_key", "questions"], "existing_columns": ["id", "questionnaire_type", "name", "version", "description", "instructions", "is_active", "created_by", "created_at", "updated_at"]}}, {"check_name": "问卷表结构", "success": false, "message": "问卷表缺少必要字段: ['title', 'description', 'questions']", "timestamp": "2025-06-30T09:47:39.219258", "details": {"missing_columns": ["title", "description", "questions"], "existing_columns": ["id", "custom_id", "questionnaire_type", "name", "version", "completed_at", "conclusion", "notes", "created_at", "updated_at", "status", "max_score"]}}, {"check_name": "问卷结果表结构", "success": false, "message": "问卷结果表缺少必要字段: ['user_id', 'answers']", "timestamp": "2025-06-30T09:47:39.223815", "details": {"missing_columns": ["user_id", "answers"], "existing_columns": ["id", "questionnaire_id", "response_id", "custom_id", "template_id", "total_score", "max_score", "percentage", "result_level", "result_category", "interpretation", "recommendations", "dimension_scores", "calculation_details", "raw_answers", "report_generated", "report_content", "report_format", "report_template", "status", "calculated_at", "created_at", "updated_at"]}}, {"check_name": "评估模板表结构", "success": false, "message": "评估模板表缺少必要字段: ['template_key', 'questions']", "timestamp": "2025-06-30T09:47:39.227679", "details": {"missing_columns": ["template_key", "questions"], "existing_columns": ["id", "assessment_type", "name", "version", "description", "instructions", "scoring_method", "max_score", "result_ranges", "is_active", "created_by", "created_at", "updated_at"]}}, {"check_name": "评估量表表结构", "success": false, "message": "评估量表表缺少必要字段: ['title', 'description', 'questions', 'scoring_rules']", "timestamp": "2025-06-30T09:47:39.232476", "details": {"missing_columns": ["title", "description", "questions", "scoring_rules"], "existing_columns": ["id", "custom_id", "assessment_type", "name", "version", "completed_at", "assessor", "score", "max_score", "result", "conclusion", "notes", "created_at", "updated_at", "status"]}}, {"check_name": "评估结果表结构", "success": false, "message": "评估结果表缺少必要字段: ['user_id', 'answers', 'score']", "timestamp": "2025-06-30T09:47:39.237600", "details": {"missing_columns": ["user_id", "answers", "score"], "existing_columns": ["id", "assessment_id", "custom_id", "template_id", "total_score", "max_score", "percentage", "result_level", "result_category", "interpretation", "recommendations", "dimension_scores", "calculation_details", "raw_answers", "report_generated", "report_content", "report_format", "report_template", "status", "calculated_at", "created_at", "updated_at"]}}, {"check_name": "外键关系检查", "success": false, "message": "发现 4 个外键关系问题", "timestamp": "2025-06-30T09:47:39.247490", "details": {"issues": ["questionnaire_results.user_id -> users.id (缺失)", "assessment_results.user_id -> users.id (缺失)", "health_records.user_id -> users.id (缺失)", "medical_records.user_id -> users.id (缺失)"]}}, {"check_name": "数据完整性检查", "success": false, "message": "关键表为空: ['assessment_templates']", "timestamp": "2025-06-30T09:47:39.263239", "details": {"table_counts": {"alert_channels": 0, "alert_rules": 0, "alerts": 0, "assessment_distributions": 0, "assessment_items": 0, "assessment_responses": 0, "assessment_results": 0, "assessment_template_questions": 0, "assessment_templates": 0, "assessments": 0, "documents": 0, "examination_reports": 0, "follow_up_records": 0, "health_diaries": 0, "health_overviews": 0, "health_records": 0, "imaging_records": 0, "imaging_reports": 0, "inpatient_records": 0, "lab_report_items": 0, "lab_reports": 0, "laboratory_records": 0, "medical_records": 0, "medication_usages": 0, "medications": 0, "notifications": 0, "operation_logs": 0, "other_records": 0, "prescription_records": 0, "questionnaire_answers": 0, "questionnaire_distributions": 0, "questionnaire_instances": 0, "questionnaire_items": 0, "questionnaire_responses": 0, "questionnaire_results": 0, "questionnaire_template_questions": 76, "questionnaire_templates": 5, "questionnaires": 0, "registration_records": 0, "report_templates": 0, "role_applications": 0, "service_stats": 0, "surgery_records": 0, "users": 1}}}, {"check_name": "模板数据样本检查", "success": false, "message": "检查失败: no such column: template_key", "timestamp": "2025-06-30T09:47:39.271859", "details": null}, {"check_name": "索引优化检查", "success": false, "message": "发现 10 个索引优化建议", "timestamp": "2025-06-30T09:47:39.273952", "details": {"suggestions": ["questionnaire_results.user_id 建议添加索引", "questionnaire_results.questionnaire_id 建议添加索引", "questionnaire_results.created_at 建议添加索引", "assessment_results.user_id 建议添加索引", "assessment_results.assessment_id 建议添加索引", "assessment_results.created_at 建议添加索引", "health_records.user_id 建议添加索引", "health_records.created_at 建议添加索引", "medical_records.user_id 建议添加索引", "medical_records.created_at 建议添加索引"]}}]}