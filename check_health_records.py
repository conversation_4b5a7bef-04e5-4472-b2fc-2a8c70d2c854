import sqlite3
import os

# 数据库路径
db_path = 'c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db'

if not os.path.exists(db_path):
    print(f"数据库文件不存在: {db_path}")
    exit(1)

try:
    # 连接数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    print("=== SM_001用户健康记录按类型统计 ===")
    cursor.execute('''
        SELECT record_type, COUNT(*) as count
        FROM health_records 
        WHERE custom_id = 'SM_001'
        GROUP BY record_type
        ORDER BY count DESC
    ''')
    
    type_stats = cursor.fetchall()
    for stat in type_stats:
        print(f"{stat[0]}: {stat[1]} 条")
    
    print("\n=== 最近5条DOCUMENT类型记录 ===")
    cursor.execute('''
        SELECT id, custom_id, record_type, title, created_at 
        FROM health_records 
        WHERE custom_id = 'SM_001' AND record_type = 'DOCUMENT'
        ORDER BY created_at DESC 
        LIMIT 5
    ''')
    
    doc_records = cursor.fetchall()
    for r in doc_records:
        print(f"ID: {r[0]}, 用户: {r[1]}, 类型: {r[2]}, 标题: {r[3]}, 创建时间: {r[4]}")
    
    print("\n=== 最近5条HEALTH类型记录 ===")
    cursor.execute('''
        SELECT id, custom_id, record_type, title, created_at 
        FROM health_records 
        WHERE custom_id = 'SM_001' AND record_type = 'HEALTH'
        ORDER BY created_at DESC 
        LIMIT 5
    ''')
    
    health_records = cursor.fetchall()
    for r in health_records:
        print(f"ID: {r[0]}, 用户: {r[1]}, 类型: {r[2]}, 标题: {r[3]}, 创建时间: {r[4]}")
    
    conn.close()
    
except Exception as e:
    print(f"查询失败: {str(e)}")
    import traceback
    traceback.print_exc()