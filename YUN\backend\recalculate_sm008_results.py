# -*- coding: utf-8 -*-

import sqlite3
import os
import json
from datetime import datetime

def recalculate_sm008_results():
    """重新计算SM_008用户的评估结果"""
    
    # 数据库文件路径
    db_path = os.path.join(os.path.dirname(__file__), 'app.db')
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("=== 重新计算SM_008用户的评估结果 ===")
        
        # 1. 获取SM_008的评估回答记录
        cursor.execute("""
            SELECT ar.id, ar.assessment_id, ar.answers, ar.created_at
            FROM assessment_responses ar
            WHERE ar.custom_id = 'SM_008' AND ar.status = 'completed'
            ORDER BY ar.created_at DESC
        """)
        responses = cursor.fetchall()
        
        print(f"找到 {len(responses)} 个已完成的评估回答")
        
        for response in responses:
            response_id, assessment_id, answers_json, created_at = response
            print(f"\n处理评估回答 ID: {response_id}, 评估ID: {assessment_id}")
            
            # 解析答案
            if not answers_json:
                print("  答案数据为空，跳过")
                continue
            
            try:
                answers = json.loads(answers_json)
            except json.JSONDecodeError as e:
                print(f"  答案解析失败: {e}，跳过")
                continue
            
            # 获取评估和模板信息
            cursor.execute("""
                SELECT a.template_id, at.name, at.max_score, at.result_ranges
                FROM assessments a
                JOIN assessment_templates at ON a.template_id = at.id
                WHERE a.id = ?
            """, (assessment_id,))
            assessment_info = cursor.fetchone()
            
            if not assessment_info:
                print(f"  未找到评估信息，跳过")
                continue
            
            template_id, template_name, max_score, result_ranges = assessment_info
            print(f"  模板: {template_name} (ID: {template_id})")
            
            # 获取模板问题的计分规则
            cursor.execute("""
                SELECT question_id, scoring
                FROM assessment_template_questions
                WHERE template_id = ?
            """, (template_id,))
            questions = cursor.fetchall()
            
            question_scoring = {}
            for q_id, scoring in questions:
                if scoring and scoring.strip() != '':
                    try:
                        question_scoring[q_id] = json.loads(scoring)
                    except json.JSONDecodeError as e:
                        print(f"    计分规则解析失败 {q_id}: {e}")
                        pass
            
            print(f"  找到 {len(question_scoring)} 个有计分规则的问题")
            
            # 重新计算分数
            total_score = 0
            scored_answers = []
            
            for answer_data in answers:
                question_id = answer_data.get('question_id')
                raw_answer = answer_data.get('answer')
                
                if question_id not in question_scoring:
                    continue
                
                scoring_rules = question_scoring[question_id]
                
                # 计算分数
                score = 0
                if 'option_scores' in scoring_rules:
                    score = scoring_rules['option_scores'].get(str(raw_answer), 0)
                
                total_score += score
                
                scored_answer = {
                    'question_id': question_id,
                    'answer': raw_answer,
                    'score': score,
                    'calculated_score': score
                }
                scored_answers.append(scored_answer)
            
            print(f"  重新计算的总分: {total_score}/{max_score}")
            
            # 计算百分比
            percentage = (total_score / max_score * 100) if max_score > 0 else 0
            
            # 确定结果等级
            result_category = "未分类"
            interpretation = "评估完成"
            
            if result_ranges:
                try:
                    ranges = json.loads(result_ranges)
                    for range_config in ranges:
                        min_score = range_config.get('min', 0)
                        max_score_range = range_config.get('max', float('inf'))
                        if min_score <= total_score <= max_score_range:
                            result_category = range_config.get('result', '未分类')
                            interpretation = range_config.get('description', '评估完成')
                            break
                except json.JSONDecodeError:
                    pass
            
            print(f"  结果等级: {result_category}")
            print(f"  百分比: {percentage:.1f}%")
            
            # 更新assessment_responses表
            cursor.execute("""
                UPDATE assessment_responses
                SET answers = ?, score = ?
                WHERE id = ?
            """, (json.dumps(scored_answers, ensure_ascii=False), total_score, response_id))
            
            # 更新或创建assessment_results记录
            cursor.execute("""
                SELECT id FROM assessment_results
                WHERE custom_id = 'SM_008' AND assessment_id = ?
            """, (assessment_id,))
            existing_result = cursor.fetchone()
            
            if existing_result:
                result_id = existing_result[0]
                cursor.execute("""
                    UPDATE assessment_results
                    SET total_score = ?, max_score = ?, percentage = ?,
                        result_level = ?, result_category = ?, interpretation = ?,
                        raw_answers = ?, status = 'calculated'
                    WHERE id = ?
                """, (total_score, max_score, percentage, result_category, 
                      result_category, interpretation, 
                      json.dumps(answers, ensure_ascii=False), result_id))
                print(f"  更新了结果记录 ID: {result_id}")
            else:
                cursor.execute("""
                    INSERT INTO assessment_results 
                    (assessment_id, custom_id, template_id, total_score, max_score, 
                     percentage, result_level, result_category, interpretation, 
                     raw_answers, status, created_at)
                    VALUES (?, 'SM_008', ?, ?, ?, ?, ?, ?, ?, ?, 'calculated', ?)
                """, (assessment_id, template_id, total_score, max_score, 
                      percentage, result_category, result_category, interpretation,
                      json.dumps(answers, ensure_ascii=False), datetime.now()))
                print(f"  创建了新的结果记录")
            
            # 更新assessments表
            cursor.execute("""
                UPDATE assessments
                SET score = ?, max_score = ?, result = ?
                WHERE id = ?
            """, (total_score, max_score, result_category, assessment_id))
        
        conn.commit()
        print(f"\n重新计算完成！")
        
        # 验证结果
        cursor.execute("""
            SELECT ar.assessment_id, ar.total_score, ar.max_score, ar.percentage, 
                   ar.result_category, ar.created_at
            FROM assessment_results ar
            WHERE ar.custom_id = 'SM_008'
            ORDER BY ar.created_at DESC
        """)
        results = cursor.fetchall()
        
        print(f"\n=== 验证更新后的结果 ===")
        for result in results:
            assessment_id, total_score, max_score, percentage, result_category, created_at = result
            print(f"评估ID: {assessment_id}")
            print(f"  总分: {total_score}/{max_score} ({percentage:.1f}%)")
            print(f"  结果: {result_category}")
            print(f"  时间: {created_at}")
            print()
        
        conn.close()
        
    except Exception as e:
        print(f"重新计算时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    recalculate_sm008_results()