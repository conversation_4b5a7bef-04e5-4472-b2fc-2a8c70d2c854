#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文档列表查询
验证移动端上传的文档是否能在文档列表中显示
"""

import requests
import json

def test_document_list():
    """测试文档列表查询"""
    base_url = "http://localhost:8006"
    
    # 1. 登录获取token (先用admin测试)
    login_data = {
        "username": "admin",
        "password": "admin"
    }
    
    try:
        print("1. 登录获取访问令牌...")
        login_response = requests.post(f"{base_url}/auth/login_json", json=login_data)
        print(f"登录响应状态码: {login_response.status_code}")
        
        if login_response.status_code != 200:
            print(f"登录失败: {login_response.text}")
            return
        
        login_result = login_response.json()
        print(f"登录响应内容: {login_result}")
        access_token = login_result["access_token"]
        user_info = login_result["user"]
        print(f"登录成功! 用户: {user_info['username']} ({user_info['custom_id']})")
        
        # 2. 查询文档列表
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        
        print("\n2. 查询文档列表...")
        docs_response = requests.get(f"{base_url}/api/documents/", headers=headers)
        print(f"文档查询响应状态码: {docs_response.status_code}")
        
        if docs_response.status_code != 200:
            print(f"文档查询失败: {docs_response.text}")
            return
        
        docs_result = docs_response.json()
        print(f"\n文档查询结果:")
        print(f"状态: {docs_result['status']}")
        
        if 'data' in docs_result:
            total = docs_result['data']['total']
            records = docs_result['data']['records']
            print(f"总文档数: {total}")
            print(f"返回记录数: {len(records)}")
            
            if records:
                print("\n文档列表:")
                for i, doc in enumerate(records, 1):
                    print(f"{i}. ID: {doc['id']}, 标题: {doc['title']}, 类型: {doc['document_type']}, 状态: {doc['status']}, 创建时间: {doc['created_at']}")
            else:
                print("\n文档列表为空!")
        else:
            print("响应中没有data字段")
            
        # 3. 查询特定类型的文档
        print("\n3. 查询lab_reports类型文档...")
        lab_docs_response = requests.get(f"{base_url}/api/documents/?document_type=lab_reports", headers=headers)
        print(f"实验报告查询响应状态码: {lab_docs_response.status_code}")
        
        if lab_docs_response.status_code == 200:
            lab_docs_result = lab_docs_response.json()
            if 'data' in lab_docs_result:
                lab_total = lab_docs_result['data']['total']
                lab_records = lab_docs_result['data']['records']
                print(f"实验报告总数: {lab_total}")
                print(f"返回记录数: {len(lab_records)}")
                
                if lab_records:
                    print("\n实验报告列表:")
                    for i, doc in enumerate(lab_records, 1):
                        print(f"{i}. ID: {doc['id']}, 标题: {doc['title']}, 文件名: {doc.get('filename', 'N/A')}, 状态: {doc['status']}")
        
        print("\n测试完成!")
        
    except requests.exceptions.ConnectionError:
        print("连接失败! 请确保后端服务正在运行 (端口8006)")
    except Exception as e:
        print(f"测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_document_list()