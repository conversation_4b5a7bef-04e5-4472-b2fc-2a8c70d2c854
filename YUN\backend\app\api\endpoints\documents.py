"""
文档管理API
"""
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, status, UploadFile, File, Form, Request, Body, BackgroundTasks
from sqlalchemy.orm import Session
import os
import json
from datetime import datetime
from app.utils.field_compatibility import ensure_field_compatibility
from app.utils.performance_monitor import monitor_performance

from app.api.deps import get_db
from app.core.auth import get_current_active_user_custom
from app.models.user import User
from app.models.document import Document
from app.models.enums import DocumentType, DocumentCategory
from app.schemas.document import DocumentCreate, Document as DocumentSchema, DocumentResponse
from app.core.config import settings

router = APIRouter()

@router.get("/", response_model=dict)
@monitor_performance("get_documents")
def get_documents(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=1000),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    document_type: Optional[str] = None,
    type: Optional[str] = None,
    custom_id: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None
):
    """获取文档列表"""
    try:
        from datetime import datetime
        
        query = db.query(Document)
        
        # 权限控制：如果不是超级管理员，只能看到自己的文档
        if not current_user.is_superuser:
            if custom_id:
                # 管理员可以查看指定用户的文档
                query = query.filter(Document.custom_id == custom_id)
            else:
                query = query.filter(Document.custom_id == current_user.custom_id)
        else:
            # 超级管理员可以查看所有文档，或指定用户的文档
            if custom_id:
                query = query.filter(Document.custom_id == custom_id)

        # 文档类型过滤
        if document_type:
            query = query.filter(Document.document_type == document_type)
        if type:
            query = query.filter(Document.document_type == type)
            
        # 日期范围过滤
        if start_date:
            try:
                start_dt = datetime.fromisoformat(start_date)
                query = query.filter(Document.created_at >= start_dt)
            except ValueError:
                pass
                
        if end_date:
            try:
                end_dt = datetime.fromisoformat(end_date + 'T23:59:59')
                query = query.filter(Document.created_at <= end_dt)
            except ValueError:
                pass
        
        # 计算分页参数
        if page and page_size:
            skip = (page - 1) * page_size
            limit = page_size
        
        total = query.count()
        documents = query.order_by(Document.created_at.desc()).offset(skip).limit(limit).all()
        
        # 获取文档所属用户信息
        document_list = []
        for doc in documents:
            # 查询文档所属用户
            doc_user = db.query(User).filter(User.custom_id == doc.custom_id).first()
            
            document_list.append({
                "id": doc.id,
                "title": doc.title or doc.filename,
                "file_name": doc.filename,
                "type": doc.document_type,
                "file_path": doc.file_path,
                "description": doc.description,
                "uploaded_at": doc.created_at.isoformat() if doc.created_at else None,
                "created_at": doc.created_at.isoformat() if doc.created_at else None,
                "updated_at": doc.updated_at.isoformat() if doc.updated_at else None,
                "status": doc.status,
                "file_size": doc.file_size,
                "user": {
                    "id": doc_user.id if doc_user else None,
                    "username": doc_user.username if doc_user else "未知用户",
                    "custom_id": doc.custom_id
                } if doc_user else {
                    "id": None,
                    "username": "未知用户",
                    "custom_id": doc.custom_id
                },
                "metadata": json.loads(doc.file_metadata) if doc.file_metadata else None
            })
        
        return {
            "status": "success",
            "data": {
                "total": total,
                "documents": document_list
            }
        }
    except Exception as e:
        print(f"获取文档列表失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取文档列表失败: {str(e)}"
        )

@router.get("/{document_id}", response_model=dict)
@monitor_performance("get_document")
def get_document(
    document_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """获取单个文档详情"""
    try:
        document = db.query(Document).filter(
            Document.id == document_id,
            Document.custom_id == current_user.custom_id
        ).first()
        
        if not document:
            raise HTTPException(
                status_code=404,
                detail="文档不存在"
            )
        
        return {
            "status": "success",
            "data": {
                "id": document.id,
                "title": document.title,
                "document_type": document.document_type,
                "file_path": document.file_path,
                "description": document.description,
                "created_at": document.created_at.isoformat() if document.created_at else None,
                "updated_at": document.updated_at.isoformat() if document.updated_at else None,
                "status": document.status,
                "metadata": json.loads(document.file_metadata) if document.file_metadata else None
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        print(f"获取文档详情失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取文档详情失败: {str(e)}"
        )

@router.post("/upload", response_model=dict)
@monitor_performance("upload_document")
async def upload_document(
    file: UploadFile = File(...),
    title: str = Form(None),
    document_type: str = Form(None),
    description: Optional[str] = Form(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """上传文档"""
    try:
        # 读取文件内容
        contents = await file.read()
        file_size = len(contents)
        
        # 创建上传目录
        upload_dir = os.path.join(settings.UPLOAD_DIR, current_user.custom_id, datetime.now().strftime('%Y%m%d'))
        os.makedirs(upload_dir, exist_ok=True)
        
        # 保存文件
        file_path = os.path.join(upload_dir, file.filename)
        with open(file_path, "wb") as f:
            f.write(contents)
        
        # 创建文档记录
        import json
        document = Document(
            custom_id=current_user.custom_id,
            title=title or file.filename,
            document_type=document_type,
            file_path=os.path.relpath(file_path, settings.BASE_DIR),
            description=description,
            status="active",
            filename=file.filename,
            file_size=file_size,
            mime_type=file.content_type,
            file_metadata=json.dumps({
                "original_filename": file.filename,
                "content_type": file.content_type,
                "size": file_size
            })
        )
        
        db.add(document)
        db.commit()
        db.refresh(document)
        
        return {
            "status": "success",
            "message": "文档上传成功",
            "data": {
                "id": document.id,
                "title": document.title,
                "file_path": document.file_path
            }
        }
    except Exception as e:
        db.rollback()
        print(f"文档上传失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"文档上传失败: {str(e)}"
        )

@router.get("/{document_id}/preview")
def preview_document(
    document_id: int,
    request: Request,
    db: Session = Depends(get_db),
    token: Optional[str] = Query(None, description="可选token参数，用于无header时认证"),
    current_user: Optional[User] = Depends(get_current_active_user_custom),
):
    """
    文档预览接口，直接返回文件内容，Content-Disposition: inline，浏览器可直接预览
    """
    from fastapi.responses import FileResponse
    import os
    from urllib.parse import quote

    # 认证处理，兼容token参数
    user = current_user
    if not user and token:
        user = get_user_by_token(db, token)
    if not user:
        raise HTTPException(status_code=403, detail="Not authenticated")

    document = db.query(Document).filter(Document.id == document_id).first()
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"文档 ID {document_id} 不存在"
        )

    # 统一用 BASE_DIR 拼接 file_path
    file_abspath = os.path.join(settings.BASE_DIR, document.file_path) \
        if not os.path.isabs(document.file_path) else document.file_path

    if not os.path.exists(file_abspath):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="服务器上未找到文件"
        )

    # 输出文件存在性和大小
    print(f"[DEBUG] 预览: 文件存在: {os.path.exists(file_abspath)}, 大小: {os.path.getsize(file_abspath) if os.path.exists(file_abspath) else 'N/A'}")

    # 可预览的文件类型
    filename = document.filename
    file_extension = os.path.splitext(filename.lower())[1]
    previewable_extensions = ['.pdf', '.jpg', '.jpeg', '.png', '.gif', '.txt', '.md']
    if file_extension not in previewable_extensions:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"不支持预览此类型的文件: {file_extension}"
        )

    # 兼容中文/特殊字符文件名
    filename_ascii = filename.encode('ascii', 'ignore').decode('ascii') or 'preview.pdf'
    filename_utf8 = quote(filename)
    content_disposition = f"inline; filename=\"{filename_ascii}\"; filename*=UTF-8''{filename_utf8}"

    print(f"文档预览 - 文件路径: {file_abspath}, 文件名: {filename}")
    return FileResponse(
        path=file_abspath,
        filename=filename,
        media_type=document.mime_type or "application/octet-stream",
        headers={"Content-Disposition": content_disposition}
    )

@router.get("/{document_id}/download", name="download_document")
def download_document(
    document_id: int,
    request: Request,
    db: Session = Depends(get_db),
    token: Optional[str] = Query(None, description="可选token参数，用于无header时认证"),
    current_user: User = Depends(get_current_active_user_custom),
):
    """
    下载文档，支持token query参数认证，支持自动重定向
    """
    from fastapi.responses import FileResponse, RedirectResponse
    import os
    from urllib.parse import quote, urlencode

    # 自动重定向逻辑：无token，referer来源于preview，header有token
    if not token:
        referer = request.headers.get("referer", "")
        auth_header = request.headers.get("Authorization", "")
        if "/preview" in referer and auth_header.startswith("Bearer "):
            header_token = auth_header.replace("Bearer ", "")
            # 拼接带token的下载链接
            download_url = str(request.url_for("download_document", document_id=document_id))
            download_url += f"?{urlencode({'token': header_token})}"
            return RedirectResponse(url=download_url, status_code=307)

    # 优先用header认证，否则用token参数
    user = current_user
    if not user and token:
        user = get_user_by_token(db, token)
    if not user:
        raise HTTPException(status_code=403, detail="Not authenticated")

    document = db.query(Document).filter(Document.id == document_id).first()
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Document with ID {document_id} not found"
        )

    file_abspath = os.path.join(settings.BASE_DIR, document.file_path) \
        if not os.path.isabs(document.file_path) else document.file_path
    if not os.path.exists(file_abspath):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found on server"
        )

    # 输出文件存在性和大小
    print(f"[DEBUG] 下载: 文件存在: {os.path.exists(file_abspath)}, 大小: {os.path.getsize(file_abspath) if os.path.exists(file_abspath) else 'N/A'}")

    # 日志输出真实下载URL
    download_url = str(request.url_for("download_document", document_id=document_id))
    print(f"文档下载 - 文件路径: {file_abspath}, 下载URL: {download_url}")

    # 返回文件，强制下载，兼容中文/特殊字符文件名
    filename = document.filename
    filename_ascii = filename.encode('ascii', 'ignore').decode('ascii') or 'download.pdf'
    filename_utf8 = quote(filename)
    content_disposition = f"attachment; filename=\"{filename_ascii}\"; filename*=UTF-8''{filename_utf8}"
    return FileResponse(
        path=file_abspath,
        filename=filename,
        media_type=document.mime_type or "application/octet-stream",
        headers={"Content-Disposition": content_disposition}
    )

@router.delete("/{document_id}", response_model=dict)
def delete_document(
    document_id: int,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
):
    """
    删除文档
    """
    document = db.query(Document).filter(Document.id == document_id).first()
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Document with ID {document_id} not found"
        )

    # 检查权限
    user_custom_id = getattr(current_user, 'custom_id', None)
    if document.custom_id != user_custom_id and not current_user.is_super_admin():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to delete this document"
        )

    # 删除文件
    try:
        if os.path.exists(document.file_path):
            os.remove(document.file_path)
    except Exception as e:
        # 记录错误但继续删除数据库记录
        print(f"Error deleting file: {e}")

    # 删除数据库记录
    db.delete(document)
    db.commit()

    return {
        "status": "success",
        "message": f"Document with ID {document_id} deleted successfully"
    }

# 为移动端添加路由别名，使用重定向
@router.post("/mobile-upload", response_model=DocumentSchema)
async def mobile_upload_redirect():
    """
    移动端上传重定向 - 将请求重定向到统一的上传接口

    此接口使用307临时重定向，保持原始请求方法和请求体
    """
    from fastapi.responses import RedirectResponse
    # 使用307临时重定向，保持原始请求方法和请求体
    return RedirectResponse(url="/api/documents/upload", status_code=307)

@router.post("/{document_id}/ocr", response_model=Dict[str, Any])
async def request_ocr_processing(
    document_id: int,
    request: Request,
    background_tasks: BackgroundTasks,
    options: Optional[Dict[str, Any]] = Body(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
):
    """
    请求对文档进行OCR处理

    此接口提供简化版的OCR处理功能，用于移动端请求OCR处理
    """
    # 查找文档
    document = db.query(Document).filter(Document.id == document_id).first()
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"文件不存在: {document_id}"
        )

    # 检查文件是否存在
    if not os.path.exists(document.file_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="服务器上未找到文件"
        )

    # 生成任务ID
    import uuid
    task_id = str(uuid.uuid4())

    # 更新文档状态
    document.ocr_status = "processing"
    if hasattr(document, 'ocr_task_id'):
        document.ocr_task_id = task_id
    db.add(document)
    db.commit()

    # 在后台处理OCR
    def process_ocr_background(doc_id, task_id, file_path, opt):
        """后台处理OCR的函数"""
        try:
            # 导入OCR服务
            try:
                from app.services.ocr_service import process_ocr
                # 使用OCR服务处理
                process_ocr(
                    file_path=file_path,
                    document_id=doc_id,
                    task_id=task_id,
                    options=opt or {},
                    db=db
                )
            except ImportError:
                # OCR服务不可用，使用简化版处理
                import json
                from datetime import datetime

                # 更新文档状态为完成
                doc = db.query(Document).filter(Document.id == doc_id).first()
                if doc:
                    doc.ocr_status = "completed"
                    doc.ocr_processed = True
                    doc.ocr_content = f"这是文档 {doc_id} 的模拟OCR结果。实际OCR功能需要安装相关依赖。"

                    # 提取文件名和扩展名
                    filename = os.path.basename(file_path)
                    file_extension = os.path.splitext(filename)[1].lower()

                    # 生成模拟提取字段
                    extracted_fields = {
                        "file_name": filename,
                        "processed_time": datetime.now().isoformat(),
                        "note": "这是模拟提取的字段，实际OCR功能需要安装相关依赖。"
                    }

                    # 根据文件类型添加模拟字段
                    if file_extension in ['.jpg', '.jpeg', '.png', '.gif']:
                        extracted_fields["document_type"] = "image"
                    elif file_extension == '.pdf':
                        extracted_fields["document_type"] = "pdf"
                    else:
                        extracted_fields["document_type"] = "other"

                    # 保存提取的字段
                    if hasattr(doc, 'ocr_extracted_fields'):
                        doc.ocr_extracted_fields = json.dumps(extracted_fields)

                    # 更新完成时间
                    if hasattr(doc, 'ocr_completed_at'):
                        doc.ocr_completed_at = datetime.now()

                    db.add(doc)
                    db.commit()
        except Exception as e:
            # 处理异常
            print(f"OCR处理异常: {str(e)}")
            try:
                # 更新文档状态为失败
                doc = db.query(Document).filter(Document.id == doc_id).first()
                if doc:
                    doc.ocr_status = "failed"
                    if hasattr(doc, 'ocr_error'):
                        doc.ocr_error = str(e)
                    db.add(doc)
                    db.commit()
            except Exception as db_error:
                print(f"更新文档状态失败: {str(db_error)}")

    # 添加后台任务
    background_tasks.add_task(
        process_ocr_background,
        doc_id=document.id,
        task_id=task_id,
        file_path=document.file_path,
        opt=options
    )

    return {
        "status": "success",
        "message": "OCR处理请求已提交",
        "data": {
            "task_id": task_id,
            "file_id": document_id,
            "status": "processing"
        }
    }

@router.get("/{document_id}/ocr-result", response_model=Dict[str, Any])
async def get_ocr_result(
    document_id: int,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
):
    """
    获取OCR处理结果

    此接口用于查询OCR处理状态和结果
    """
    # 查找文档
    document = db.query(Document).filter(Document.id == document_id).first()
    if not document:
        raise HTTPException(
            status_code,
            detail=f"文件不存在: {document_id}"
        )

    # 构建基本响应
    result = {
        "status": "success",
        "data": {
            "file_id": document_id,
            "ocr_status": document.ocr_status or "unknown",
            "ocr_processed": document.ocr_processed or False
        }
    }

    # 如果OCR已完成，添加结果
    if document.ocr_status == "completed" and document.ocr_processed:
        result["data"]["ocr_content"] = document.ocr_content

        # 尝试解析提取的字段
        if hasattr(document, 'ocr_extracted_fields') and document.ocr_extracted_fields:
            try:
                import json
                result["data"]["extracted_fields"] = json.loads(document.ocr_extracted_fields)
            except json.JSONDecodeError:
                result["data"]["extracted_fields"] = {}

        # 添加完成时间
        if hasattr(document, 'ocr_completed_at') and document.ocr_completed_at:
            result["data"]["completed_at"] = document.ocr_completed_at.isoformat()

    # 如果OCR失败，添加错误信息
    elif document.ocr_status == "failed":
        result["data"]["error"] = document.ocr_error if hasattr(document, 'ocr_error') else "OCR处理失败"

    return result
