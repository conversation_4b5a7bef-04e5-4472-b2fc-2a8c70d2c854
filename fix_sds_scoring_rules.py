#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复SDS抑郁自评量表的计分规则
"""

import sqlite3
import json
import os

def fix_sds_scoring_rules():
    """修复SDS量表的计分规则"""
    db_path = 'c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db'
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 检查SDS模板
        print("=== 1. 检查SDS模板 ===")
        cursor.execute("""
            SELECT id, template_key, name, scoring_method, max_score
            FROM assessment_templates 
            WHERE template_key = 'sds' OR name LIKE '%抑郁%'
        """)
        
        template = cursor.fetchone()
        if not template:
            print("❌ 未找到SDS模板")
            return
        
        template_id, template_key, name, scoring_method, max_score = template
        print(f"找到模板: ID={template_id}, Key={template_key}, Name={name}")
        
        # 2. 检查问题的计分规则
        print("\n=== 2. 检查问题计分规则 ===")
        cursor.execute("""
            SELECT question_id, question_text, scoring, dimension_key
            FROM assessment_template_questions 
            WHERE template_id = ?
            ORDER BY question_id
        """, (template_id,))
        
        questions = cursor.fetchall()
        print(f"找到 {len(questions)} 个问题")
        
        # 3. 根据SDS量表标准定义计分规则
        # SDS量表：1-4分制，部分题目需要反向计分
        # 正向计分题：1,3,4,7,8,9,10,13,15,19 (很少=1, 有时=2, 经常=3, 持续=4)
        # 反向计分题：2,5,6,11,12,14,16,17,18,20 (很少=4, 有时=3, 经常=2, 持续=1)
        
        positive_questions = ['sds_1', 'sds_3', 'sds_4', 'sds_7', 'sds_8', 'sds_9', 'sds_10', 'sds_13', 'sds_15', 'sds_19']
        negative_questions = ['sds_2', 'sds_5', 'sds_6', 'sds_11', 'sds_12', 'sds_14', 'sds_16', 'sds_17', 'sds_18', 'sds_20']
        
        # 正向计分规则
        positive_scoring = {
            "1": 1, "2": 2, "3": 3, "4": 4
        }
        
        # 反向计分规则
        negative_scoring = {
            "1": 4, "2": 3, "3": 2, "4": 1
        }
        
        # 4. 更新计分规则
        print("\n=== 3. 更新计分规则 ===")
        updated_count = 0
        
        for question_id, question_text, scoring, dimension_key in questions:
            # 确定使用哪种计分规则
            if question_id in positive_questions:
                new_scoring = json.dumps(positive_scoring, ensure_ascii=False)
                scoring_type = "正向"
            elif question_id in negative_questions:
                new_scoring = json.dumps(negative_scoring, ensure_ascii=False)
                scoring_type = "反向"
            else:
                # 默认使用正向计分
                new_scoring = json.dumps(positive_scoring, ensure_ascii=False)
                scoring_type = "默认正向"
            
            # 检查是否需要更新
            if not scoring or scoring.strip() == '' or scoring == 'null':
                print(f"更新问题 {question_id} ({scoring_type}计分): {question_text[:30]}...")
                
                cursor.execute("""
                    UPDATE assessment_template_questions 
                    SET scoring = ?
                    WHERE template_id = ? AND question_id = ?
                """, (new_scoring, template_id, question_id))
                
                updated_count += 1
            else:
                print(f"问题 {question_id} 已有计分规则，跳过")
        
        # 5. 提交更改
        if updated_count > 0:
            conn.commit()
            print(f"\n✅ 成功更新了 {updated_count} 个问题的计分规则")
        else:
            print("\n⚠️ 没有需要更新的计分规则")
        
        # 6. 验证更新结果
        print("\n=== 4. 验证更新结果 ===")
        cursor.execute("""
            SELECT question_id, scoring
            FROM assessment_template_questions 
            WHERE template_id = ?
            ORDER BY question_id
            LIMIT 5
        """, (template_id,))
        
        updated_questions = cursor.fetchall()
        for question_id, scoring in updated_questions:
            print(f"问题 {question_id}: {scoring}")
        
        # 7. 检查SM_008用户的评估情况
        print("\n=== 5. 检查SM_008用户的评估情况 ===")
        cursor.execute("""
            SELECT ar.id, ar.total_score, ar.dimension_scores, ar.raw_answers
            FROM assessment_results ar
            JOIN assessments a ON ar.assessment_id = a.id
            WHERE a.template_id = ? AND a.custom_id = 'SM_008'
            ORDER BY ar.created_at DESC
            LIMIT 1
        """, (template_id,))
        
        user_result = cursor.fetchone()
        if user_result:
            result_id, total_score, dimension_scores, raw_answers = user_result
            print(f"找到SM_008的评估结果:")
            print(f"  结果ID: {result_id}")
            print(f"  总分: {total_score}")
            print(f"  维度分: {dimension_scores}")
            
            if raw_answers:
                try:
                    answers_data = json.loads(raw_answers)
                    if isinstance(answers_data, list) and len(answers_data) > 0:
                        print(f"  原始答案: 包含 {len(answers_data)} 个答案")
                        print(f"  第一个答案示例: {answers_data[0]}")
                        
                        # 检查答案中的分数
                        scores = []
                        for answer in answers_data:
                            if isinstance(answer, dict) and 'score' in answer:
                                scores.append(answer['score'])
                        
                        if scores:
                            print(f"  答案中的分数: {scores[:5]}... (显示前5个)")
                            print(f"  答案分数总和: {sum(scores)}")
                        else:
                            print(f"  ❌ 答案中没有分数字段")
                    else:
                        print(f"  原始答案格式异常: {type(answers_data)}")
                except json.JSONDecodeError as e:
                    print(f"  原始答案JSON解析失败: {e}")
            else:
                print(f"  ❌ 原始答案为空")
        else:
            print("未找到SM_008的SDS评估记录")
        
        conn.close()
        
        print("\n=== 修复完成 ===")
        print("建议操作:")
        print("1. 重启后端服务")
        print("2. 重新测试SDS量表")
        print("3. 检查计分是否正常")
        
    except Exception as e:
        print(f"修复过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_sds_scoring_rules()