from sqlalchemy import Column, Integer, String, ForeignKey, DateTime, Boolean
from sqlalchemy.orm import relationship
from app.db.base_class import Base

class ConsultantClient(Base):
    __tablename__ = "consultant_clients"

    id = Column(Integer, primary_key=True, index=True)
    consultant_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    client_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    status = Column(String(20), default="active")
    created_at = Column(DateTime)
    updated_at = Column(DateTime)

    consultant = relationship("User", foreign_keys=[consultant_id])
    client = relationship("User", foreign_keys=[client_id])
