#!/usr/bin/env python3
"""
添加report字段到assessment_responses和questionnaire_responses表
"""

import sqlite3
import os

def add_report_fields():
    """添加report字段到数据库表"""
    db_path = 'app.db'
    
    if not os.path.exists(db_path):
        print(f"数据库文件 {db_path} 不存在")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查assessment_responses表是否已有report字段
        cursor.execute("PRAGMA table_info(assessment_responses)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'report' not in columns:
            print("添加report字段到assessment_responses表...")
            cursor.execute('ALTER TABLE assessment_responses ADD COLUMN report JSON')
            print("✓ assessment_responses表添加report字段成功")
        else:
            print("assessment_responses表已有report字段")
        
        # 检查questionnaire_responses表是否已有report字段
        cursor.execute("PRAGMA table_info(questionnaire_responses)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'report' not in columns:
            print("添加report字段到questionnaire_responses表...")
            cursor.execute('ALTER TABLE questionnaire_responses ADD COLUMN report JSON')
            print("✓ questionnaire_responses表添加report字段成功")
        else:
            print("questionnaire_responses表已有report字段")
        
        conn.commit()
        conn.close()
        print("所有操作完成")
        return True
        
    except Exception as e:
        print(f"添加字段时出错: {e}")
        return False

if __name__ == "__main__":
    add_report_fields()