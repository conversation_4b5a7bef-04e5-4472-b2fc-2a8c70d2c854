#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查找实际的评估数据表
"""

import sqlite3
import json
import sys
import os

def find_actual_tables():
    """查找实际的评估数据表"""
    try:
        # 连接数据库
        db_path = os.path.join(os.path.dirname(__file__), 'app.db')
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("=" * 60)
        print("查找所有数据库表")
        print("=" * 60)
        
        # 查看所有表
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table'
            ORDER BY name
        """)
        
        all_tables = cursor.fetchall()
        print("所有表:")
        for table in all_tables:
            print(f"  {table[0]}")
        
        print("\n" + "=" * 60)
        print("检查每个表的结构和数据")
        print("=" * 60)
        
        # 检查每个表的结构和数据
        for table_name in [t[0] for t in all_tables]:
            print(f"\n表: {table_name}")
            print("-" * 40)
            
            try:
                # 查看表结构
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                print("结构:")
                for col in columns:
                    print(f"  {col[1]} ({col[2]})")
                
                # 查看记录数
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"记录数: {count}")
                
                # 如果有数据，查看几条记录
                if count > 0:
                    cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                    rows = cursor.fetchall()
                    print("示例数据:")
                    for i, row in enumerate(rows, 1):
                        print(f"  记录{i}: {row}")
                        
                        # 如果有JSON字段，尝试解析
                        for j, value in enumerate(row):
                            if isinstance(value, str) and (value.startswith('{') or value.startswith('[')):
                                try:
                                    parsed = json.loads(value)
                                    print(f"    字段{j+1}解析: {type(parsed)} - {str(parsed)[:100]}...")
                                except:
                                    pass
                
            except Exception as e:
                print(f"检查表 {table_name} 失败: {e}")
        
        # 特别查找包含评估、问卷、答案、分数等关键词的表
        print("\n" + "=" * 60)
        print("查找包含评估相关关键词的表")
        print("=" * 60)
        
        keywords = ['assessment', 'questionnaire', 'response', 'result', 'answer', 'score']
        
        for table_name in [t[0] for t in all_tables]:
            # 检查表名是否包含关键词
            if any(keyword in table_name.lower() for keyword in keywords):
                print(f"\n找到相关表: {table_name}")
                
                try:
                    cursor.execute(f"PRAGMA table_info({table_name})")
                    columns = cursor.fetchall()
                    
                    # 查找包含关键词的列
                    relevant_columns = []
                    for col in columns:
                        if any(keyword in col[1].lower() for keyword in keywords + ['raw', 'data', 'json']):
                            relevant_columns.append(col)
                    
                    if relevant_columns:
                        print("  相关列:")
                        for col in relevant_columns:
                            print(f"    {col[1]} ({col[2]})")
                        
                        # 查看这些列的数据
                        col_names = [col[1] for col in relevant_columns]
                        cursor.execute(f"SELECT id, {', '.join(col_names)} FROM {table_name} LIMIT 2")
                        rows = cursor.fetchall()
                        if rows:
                            print("  数据示例:")
                            for row in rows:
                                print(f"    {row}")
                
                except Exception as e:
                    print(f"  检查失败: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"查找失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    find_actual_tables()