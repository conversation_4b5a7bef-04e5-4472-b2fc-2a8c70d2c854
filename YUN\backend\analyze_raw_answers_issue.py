# -*- coding: utf-8 -*-
"""
深入分析raw_answers数据缺失问题的根本原因
并提供全面的解决方案
"""

import sqlite3
import os
import json
from datetime import datetime

def analyze_raw_answers_issue():
    """分析raw_answers数据缺失的根本原因"""
    
    db_path = os.path.join(os.path.dirname(__file__), 'app.db')
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("=== 深入分析raw_answers数据缺失问题 ===")
        print(f"分析时间: {datetime.now()}")
        
        # 1. 检查assessment_results表中的raw_answers字段
        print("\n=== 1. 检查assessment_results表中的raw_answers数据 ===")
        cursor.execute("""
            SELECT id, custom_id, template_id, total_score, dimension_scores, raw_answers, status
            FROM assessment_results
            ORDER BY created_at DESC
        """)
        results = cursor.fetchall()
        
        total_results = len(results)
        missing_raw_answers = 0
        empty_raw_answers = 0
        valid_raw_answers = 0
        
        print(f"总评估结果数: {total_results}")
        
        for result in results:
            result_id, custom_id, template_id, total_score, dimension_scores, raw_answers, status = result
            
            if raw_answers is None:
                missing_raw_answers += 1
                print(f"  缺失raw_answers: ID={result_id}, custom_id={custom_id}, template_id={template_id}")
            elif raw_answers == '' or raw_answers == '{}':
                empty_raw_answers += 1
                print(f"  空raw_answers: ID={result_id}, custom_id={custom_id}, template_id={template_id}")
            else:
                valid_raw_answers += 1
                try:
                    answers_data = json.loads(raw_answers)
                    print(f"  有效raw_answers: ID={result_id}, custom_id={custom_id}, template_id={template_id}, 答案数={len(answers_data)}")
                except:
                    print(f"  格式错误raw_answers: ID={result_id}, custom_id={custom_id}, template_id={template_id}")
        
        print(f"\n统计结果:")
        print(f"  缺失raw_answers: {missing_raw_answers}")
        print(f"  空raw_answers: {empty_raw_answers}")
        print(f"  有效raw_answers: {valid_raw_answers}")
        print(f"  缺失率: {(missing_raw_answers + empty_raw_answers) / total_results * 100:.1f}%")
        
        # 2. 检查assessment_responses表中的answers数据
        print("\n=== 2. 检查assessment_responses表中的answers数据 ===")
        cursor.execute("""
            SELECT ar.id, ar.custom_id, ar.assessment_id, ar.answers, ar.score, ar.dimension_scores,
                   a.template_id, a.name as assessment_name
            FROM assessment_responses ar
            LEFT JOIN assessments a ON ar.assessment_id = a.id
            ORDER BY ar.created_at DESC
        """)
        responses = cursor.fetchall()
        
        print(f"评估回答总数: {len(responses)}")
        
        for response in responses:
            resp_id, custom_id, assessment_id, answers, score, dimension_scores, template_id, assessment_name = response
            
            if answers:
                try:
                    answers_data = json.loads(answers)
                    print(f"  回答ID={resp_id}, custom_id={custom_id}, template_id={template_id}, 评估={assessment_name}")
                    print(f"    答案数={len(answers_data)}, 得分={score}")
                    
                    # 检查这个回答是否在assessment_results中有对应记录
                    cursor.execute("""
                        SELECT id, raw_answers FROM assessment_results 
                        WHERE custom_id = ? AND template_id = ?
                    """, (custom_id, template_id))
                    result_records = cursor.fetchall()
                    
                    if result_records:
                        for result_record in result_records:
                            result_id, raw_answers = result_record
                            if raw_answers:
                                print(f"    ✓ 对应结果记录ID={result_id}有raw_answers")
                            else:
                                print(f"    ✗ 对应结果记录ID={result_id}缺失raw_answers")
                    else:
                        print(f"    ✗ 没有找到对应的assessment_results记录")
                        
                except Exception as e:
                    print(f"  回答ID={resp_id}答案解析失败: {e}")
            else:
                print(f"  回答ID={resp_id}, custom_id={custom_id}无答案数据")
        
        # 3. 检查questionnaire_responses表中的answers数据
        print("\n=== 3. 检查questionnaire_responses表中的answers数据 ===")
        cursor.execute("""
            SELECT qr.id, qr.custom_id, qr.questionnaire_id, qr.answers, qr.total_score,
                   q.template_id, q.title as questionnaire_title, q.questionnaire_type
            FROM questionnaire_responses qr
            LEFT JOIN questionnaires q ON qr.questionnaire_id = q.id
            ORDER BY qr.created_at DESC
        """)
        qr_responses = cursor.fetchall()
        
        print(f"问卷回答总数: {len(qr_responses)}")
        
        for qr_response in qr_responses:
            qr_id, custom_id, questionnaire_id, answers, total_score, template_id, title, qr_type = qr_response
            
            if answers:
                try:
                    answers_data = json.loads(answers)
                    print(f"  问卷回答ID={qr_id}, custom_id={custom_id}, template_id={template_id}")
                    print(f"    问卷={title}, 类型={qr_type}, 答案数={len(answers_data)}, 得分={total_score}")
                except Exception as e:
                    print(f"  问卷回答ID={qr_id}答案解析失败: {e}")
            else:
                print(f"  问卷回答ID={qr_id}, custom_id={custom_id}无答案数据")
        
        # 4. 分析数据流程问题
        print("\n=== 4. 数据流程分析 ===")
        
        # 检查是否存在数据同步问题
        print("\n检查数据同步问题:")
        cursor.execute("""
            SELECT ar.custom_id, ar.assessment_id, a.template_id, a.name,
                   COUNT(ar.id) as response_count,
                   COUNT(res.id) as result_count
            FROM assessment_responses ar
            LEFT JOIN assessments a ON ar.assessment_id = a.id
            LEFT JOIN assessment_results res ON res.custom_id = ar.custom_id AND res.template_id = a.template_id
            GROUP BY ar.custom_id, ar.assessment_id, a.template_id
            HAVING response_count != result_count
        """)
        sync_issues = cursor.fetchall()
        
        if sync_issues:
            print("发现数据同步问题:")
            for issue in sync_issues:
                custom_id, assessment_id, template_id, name, response_count, result_count = issue
                print(f"  custom_id={custom_id}, template_id={template_id}, 评估={name}")
                print(f"    回答记录数={response_count}, 结果记录数={result_count}")
        else:
            print("未发现明显的数据同步问题")
        
        # 5. 检查模板配置问题
        print("\n=== 5. 检查模板配置问题 ===")
        
        # 检查评估模板
        cursor.execute("""
            SELECT id, template_key, name, assessment_type, dimensions, is_active
            FROM assessment_templates
            ORDER BY id
        """)
        assessment_templates = cursor.fetchall()
        
        print(f"评估模板数: {len(assessment_templates)}")
        for template in assessment_templates:
            template_id, template_key, name, assessment_type, dimensions, is_active = template
            print(f"  模板ID={template_id}, key={template_key}, 名称={name}, 类型={assessment_type}, 激活={is_active}")
            
            if dimensions:
                try:
                    dim_data = json.loads(dimensions)
                    print(f"    维度数={len(dim_data)}")
                except:
                    print(f"    维度配置解析失败")
            else:
                print(f"    无维度配置")
        
        # 检查问卷模板
        cursor.execute("""
            SELECT id, template_key, name, questionnaire_type, dimensions, is_active
            FROM questionnaire_templates
            ORDER BY id
        """)
        questionnaire_templates = cursor.fetchall()
        
        print(f"\n问卷模板数: {len(questionnaire_templates)}")
        for template in questionnaire_templates:
            template_id, template_key, name, qr_type, dimensions, is_active = template
            print(f"  模板ID={template_id}, key={template_key}, 名称={name}, 类型={qr_type}, 激活={is_active}")
            
            if dimensions:
                try:
                    dim_data = json.loads(dimensions)
                    print(f"    维度数={len(dim_data)}")
                except:
                    print(f"    维度配置解析失败")
            else:
                print(f"    无维度配置")
        
        print("\n=== 6. 问题总结和建议 ===")
        
        print("\n发现的主要问题:")
        print("1. assessment_results表中存在raw_answers数据缺失")
        print("2. 数据从assessment_responses到assessment_results的同步可能存在问题")
        print("3. 问卷类型的数据可能没有正确处理计分和维度分析")
        
        print("\n建议的解决方案:")
        print("1. 修复现有数据: 从assessment_responses和questionnaire_responses中恢复raw_answers")
        print("2. 完善数据同步机制: 确保评估提交时正确保存raw_answers")
        print("3. 区分处理逻辑: 标准调查问卷和自定义问卷可选择性进行计分")
        print("4. 建立数据验证机制: 定期检查数据完整性")
        
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    analyze_raw_answers_issue()