# -*- coding: utf-8 -*-
"""
医疗记录管理屏幕
整合住院资料、门诊资料、检验报告、技诊报告的统一管理界面

功能特点：
1. 分类管理：住院、门诊、检验、技诊四大类别
2. 时间排序：每类记录按时间顺序排列
3. 多种上传：直接上传、二维码上传、拍照上传
4. 文档查看：原始文档和结构化文档查看
5. 复查提醒：检验和技诊报告支持复查提醒设置
6. 数据分析：检验报告支持动态数据分析和折线图
"""

from kivy.clock import Clock
from kivy.metrics import dp
from kivy.properties import StringProperty, ObjectProperty
from kivymd.app import MDApp
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDIconButton, MDFabButton

from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
from kivymd.uix.list import MDList, MDListItem
from kivymd.uix.scrollview import MDScrollView
# MDTabsPrimary, MDTabsItem, MDTabsItemIcon, MDTabsItemText 在 KivyMD 2.0.1 dev0 中已被弃用
from kivymd.uix.floatlayout import MDFloatLayout
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText
from kivymd.uix.dialog import MDDialog
from kivymd.uix.button import MDButton, MDButtonText, MDButtonIcon
from kivymd.uix.textfield import MDTextField
from kivymd.uix.selectioncontrol import MDSwitch
from kivymd.uix.menu import MDDropdownMenu
from kivymd.uix.filemanager import MDFileManager
from utils.toast import toast
from utils.cloud_api import get_cloud_api
from utils.user_manager import get_user_manager

from screens.base_screen import BaseScreen
from widgets.logo import HealthLogo
from theme import ThemeManager, AppTheme, AppMetrics, FontStyles
from theme import FontManager

import logging
logger = logging.getLogger(__name__)

# KV语言字符串
KV = '''
<RecordCard>:
    orientation: 'vertical'
    size_hint_y: None
    height: dp(140)
    md_bg_color: app.theme.ELEVATED_SURFACE
    radius: [dp(16)]
    elevation: 4
    padding: [dp(20), dp(16), dp(20), dp(16)]
    ripple_behavior: True
    
    MDBoxLayout:
        orientation: 'horizontal'
        spacing: dp(20)
        
        # 图标区域
        MDCard:
            size_hint: None, None
            size: dp(56), dp(56)
            md_bg_color: root.icon_color if root.icon_color else app.theme.PRIMARY_COLOR
            radius: [dp(28)]
            elevation: 2
            pos_hint: {'center_y': 0.5}
            
            MDIconButton:
                icon: root.icon
                icon_size: dp(28)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                pos_hint: {'center_x': 0.5, 'center_y': 0.5}
        
        # 内容区域
        MDBoxLayout:
            orientation: 'vertical'
            spacing: dp(6)
            
            MDLabel:
                text: root.title
                font_size: dp(16)
                bold: True
                theme_text_color: "Custom"
                text_color: app.theme.TEXT_PRIMARY
                size_hint_y: None
                height: self.texture_size[1]
                halign: 'left'
                shorten: True
                shorten_from: 'right'
            
            MDLabel:
                text: root.date
                font_size: dp(13)
                theme_text_color: "Custom"
                text_color: app.theme.PRIMARY_COLOR
                size_hint_y: None
                height: self.texture_size[1]
                halign: 'left'
                bold: True
            
            MDLabel:
                text: root.description
                font_size: dp(12)
                theme_text_color: "Custom"
                text_color: app.theme.TEXT_SECONDARY
                size_hint_y: None
                height: self.texture_size[1]
                halign: 'left'
                shorten: True
                shorten_from: 'right'
        
        # 操作按钮区域
        MDBoxLayout:
            orientation: 'horizontal'
            spacing: dp(8)
            size_hint_x: None
            width: dp(120)
            pos_hint: {'center_y': 0.5}
            
            MDIconButton:
                icon: "eye"
                icon_size: dp(22)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                md_bg_color: app.theme.PRIMARY_COLOR
                radius: [dp(20)]
                on_release: root.on_view()
            
            MDIconButton:
                icon: "pencil"
                icon_size: dp(22)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                md_bg_color: app.theme.SUCCESS_COLOR
                radius: [dp(20)]
                on_release: root.on_edit()
            
            MDIconButton:
                icon: "bell"
                icon_size: dp(22)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                md_bg_color: app.theme.WARNING_COLOR
                radius: [dp(20)]
                on_release: root.on_reminder()
                opacity: 1

<MedicalRecordsScreen>:
    md_bg_color: app.theme.BACKGROUND_COLOR
    
    MDBoxLayout:
        orientation: "vertical"
        spacing: dp(0)
        
        # 顶部应用栏
        MDBoxLayout:
            id: app_bar
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(56)
            md_bg_color: app.theme.PRIMARY_COLOR
            padding: [dp(4), dp(0), dp(4), dp(0)]
            
            MDIconButton:
                icon: "arrow-left"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.go_back()
            
            MDLabel:
                text: "医疗记录管理"
                font_size: dp(18)
                bold: True
                theme_text_color: "Custom"
                text_color: app.theme.TEXT_LIGHT
                halign: "center"
                valign: "center"
            
            MDIconButton:
                icon: "upload"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.show_upload_menu()
            
            MDIconButton:
                icon: "plus"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.add_record()
        
        # 添加Logo组件 - 增加高度
        MDCard:
            size_hint_y: None
            height: dp(180)
            md_bg_color: app.theme.SURFACE_COLOR
            elevation: 1
            radius: [0]
            
            HealthLogo:
                id: health_logo
                logo_size: dp(100), dp(100)
                title_font_size: dp(12)
                subtitle_font_size: dp(8)
        
        # Tab导航栏 - 使用BoxLayout确保均匀分布
        MDBoxLayout:
            id: tab_bar
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(48)
            md_bg_color: app.theme.SURFACE_COLOR
            elevation: 2
            padding: [dp(8), dp(4), dp(8), dp(4)]
            spacing: dp(8)
            
            MDButton:
                id: hospital_btn
                style: "filled" if root.current_category == 'hospital' else "outlined"
                size_hint_x: 1
                on_release: root.switch_tab('hospital')
                md_bg_color: app.theme.HOSPITAL_COLOR if root.current_category == 'hospital' else app.theme.SURFACE_COLOR
                line_color: app.theme.HOSPITAL_COLOR
                radius: [dp(6)]
                padding: [dp(2), dp(2)]
                
                MDButtonText:
                    text: "住院"
                    theme_text_color: "Custom"
                    text_color: app.theme.TEXT_LIGHT if root.current_category == 'hospital' else app.theme.HOSPITAL_COLOR
                    font_size: dp(12)
                    bold: True
            
            MDButton:
                id: outpatient_btn
                style: "filled" if root.current_category == 'outpatient' else "outlined"
                size_hint_x: 1
                on_release: root.switch_tab('outpatient')
                md_bg_color: app.theme.OUTPATIENT_COLOR if root.current_category == 'outpatient' else app.theme.SURFACE_COLOR
                line_color: app.theme.OUTPATIENT_COLOR
                radius: [dp(6)]
                padding: [dp(2), dp(2)]
                
                MDButtonText:
                    text: "门诊"
                    theme_text_color: "Custom"
                    text_color: app.theme.TEXT_LIGHT if root.current_category == 'outpatient' else app.theme.OUTPATIENT_COLOR
                    font_size: dp(12)
                    bold: True
            
            MDButton:
                id: lab_btn
                style: "filled" if root.current_category == 'lab' else "outlined"
                size_hint_x: 1
                on_release: root.switch_tab('lab')
                md_bg_color: app.theme.LAB_COLOR if root.current_category == 'lab' else app.theme.SURFACE_COLOR
                line_color: app.theme.LAB_COLOR
                radius: [dp(6)]
                padding: [dp(2), dp(2)]
                
                MDButtonText:
                    text: "检验"
                    theme_text_color: "Custom"
                    text_color: app.theme.TEXT_LIGHT if root.current_category == 'lab' else app.theme.LAB_COLOR
                    font_size: dp(12)
                    bold: True
            
            MDButton:
                id: tech_btn
                style: "filled" if root.current_category == 'tech' else "outlined"
                size_hint_x: 1
                on_release: root.switch_tab('tech')
                md_bg_color: app.theme.TECH_COLOR if root.current_category == 'tech' else app.theme.SURFACE_COLOR
                line_color: app.theme.TECH_COLOR
                radius: [dp(6)]
                padding: [dp(2), dp(2)]
                
                MDButtonText:
                    text: "技诊"
                    theme_text_color: "Custom"
                    text_color: app.theme.TEXT_LIGHT if root.current_category == 'tech' else app.theme.TECH_COLOR
                    font_size: dp(12)
                    bold: True
        
        # 内容区域
        MDScrollView:
            id: content_scroll
            do_scroll_x: False
            do_scroll_y: True
            md_bg_color: app.theme.BACKGROUND_COLOR
            
            MDBoxLayout:
                id: content_container
                orientation: 'vertical'
                size_hint_y: None
                height: self.minimum_height
                padding: [dp(12), dp(12), dp(12), dp(12)]
                spacing: dp(12)
'''

# 加载KV字符串
from kivy.lang import Builder
Builder.load_string(KV)

class RecordCard(MDCard):
    """记录卡片组件"""
    
    # 声明Kivy属性
    icon = StringProperty()
    icon_color = ObjectProperty()
    title = StringProperty()
    date = StringProperty()
    description = StringProperty()
    
    def __init__(self, record_data, category, **kwargs):
        # 先设置基本属性，再调用父类初始化
        self.record_data = record_data
        self.category = category
        
        # 设置卡片属性
        self.icon = self.get_icon_by_category()
        self.icon_color = self.get_color_by_category()
        self.title = record_data.get('title', '未知记录')
        self.date = record_data.get('date', '未知日期')
        self.description = record_data.get('description', '暂无描述')
        
        # 初始化父类
        super().__init__(**kwargs)
        
        # 在父类初始化后设置图标可见性
        Clock.schedule_once(self._update_reminder_button, 0.1)
    
    def get_icon_by_category(self):
        """根据类别获取图标"""
        icons = {
            'hospital': 'hospital-building',
            'outpatient': 'doctor',
            'lab': 'flask',
            'tech': 'stethoscope'
        }
        return icons.get(self.category, 'file-document')
    
    def get_color_by_category(self):
        """根据类别获取颜色"""
        app = MDApp.get_running_app()
        colors = {
            'hospital': app.theme.HOSPITAL_COLOR,
            'outpatient': app.theme.OUTPATIENT_COLOR,
            'lab': app.theme.LAB_COLOR,
            'tech': app.theme.TECH_COLOR
        }
        return colors.get(self.category, app.theme.PRIMARY_COLOR)
    
    def on_view(self):
        """查看记录"""
        screen = self.get_screen()
        if screen:
            screen.view_record(self.record_data, self.category)
    
    def on_edit(self):
        """编辑记录"""
        screen = self.get_screen()
        if screen:
            screen.edit_record(self.record_data, self.category)
    
    def on_reminder(self):
        """设置提醒"""
        screen = self.get_screen()
        if screen:
            screen.set_reminder(self.record_data, self.category)
    
    def _update_reminder_button(self, dt=None):
        """更新提醒按钮的显示状态"""
        try:
            # 查找按钮区域，应该是最后一个MDBoxLayout
            for child in self.children:
                if isinstance(child, MDBoxLayout):
                    # 在布局中找到按钮区域（最后一个MDBoxLayout）
                    for box in child.children:
                        if isinstance(box, MDBoxLayout) and box.size_hint_x is None and box.width == dp(120):
                            # 获取第三个按钮（索引为0，因为children是反向排列的）
                            button = box.children[0] if len(box.children) >= 3 else None
                            if button and hasattr(button, 'icon'):
                                # 只有检验和技诊报告显示提醒按钮，其他隐藏
                                if self.category in ['lab', 'tech']:
                                    button.opacity = 1
                                    has_reminder = 'reminder_date' in self.record_data
                                    button.icon = 'bell' if has_reminder else 'bell-off'
                                    button.theme_icon_color = 'Custom'
                                    # 使用直接的颜色值，避免获取app实例
                                    if has_reminder:
                                        button.icon_color = (1, 1, 1, 1)  # 白色
                                    else:
                                        button.icon_color = (1, 1, 1, 0.7)  # 半透明白色
                                else:
                                    button.opacity = 0
                            break
        except Exception as e:
            print(f"更新提醒按钮时出错: {e}")
            import traceback
            traceback.print_exc()
    
    def get_screen(self):
        """获取父屏幕"""
        parent = self.parent
        while parent:
            if hasattr(parent, 'name') and parent.name == 'medical_records':
                return parent
            if isinstance(parent, MedicalRecordsScreen):
                return parent
            parent = parent.parent
        return None

class MedicalRecordsScreen(BaseScreen):
    """医疗记录管理屏幕"""
    title = StringProperty("医疗记录管理")
    records_data = {
        'hospital': [
            {'id': 'h001', 'title': '心脏手术住院记录', 'date': '2024-01-15', 'description': '心脏搭桥手术，住院7天，治疗效果良好', 'type': '手术记录', 'files': ['入院记录.pdf', '手术记录.pdf', '出院小结.pdf']},
            {'id': 'h002', 'title': '肺炎住院治疗', 'date': '2023-12-20', 'description': '急性肺炎，住院5天，已完全康复', 'type': '入院记录', 'files': ['入院记录.pdf', '出院小结.pdf']}
        ],
        'outpatient': [
            {'id': 'o001', 'title': '内科门诊复查', 'date': '2024-02-01', 'description': '术后复查，恢复良好，各项指标正常', 'type': '门诊病历', 'files': ['门诊病历.pdf']},
            {'id': 'o002', 'title': '外科伤口换药', 'date': '2024-01-25', 'description': '手术伤口换药，愈合良好', 'type': '门诊处置', 'files': ['处置记录.pdf']}
        ],
        'lab': [
            {'id': 'l001', 'title': '血常规检查', 'date': '2024-02-10', 'description': '各项指标正常，无异常发现', 'type': '血液检查', 'files': ['血常规报告.pdf'], 'reminder_date': '2024-05-10'},
            {'id': 'l002', 'title': '肝功能检查', 'date': '2024-01-20', 'description': '肝功能指标轻度异常，需注意饮食', 'type': '生化检查', 'files': ['肝功能报告.pdf']}
        ],
        'tech': [
            {'id': 't001', 'title': 'CT检查报告', 'date': '2024-02-05', 'description': '胸部CT，未见异常，肺部正常', 'type': 'CT检查', 'files': ['CT报告.pdf'], 'reminder_date': '2024-08-05'},
            {'id': 't002', 'title': 'B超检查', 'date': '2024-01-15', 'description': '腹部B超，肝胆胰脾未见明显异常', 'type': 'B超检查', 'files': ['B超报告.pdf']}
        ]
    }
    
    def __init__(self, **kwargs):
        # 在super().__init__()之前设置属性，确保KV文件解析时可以访问
        self.current_category = 'hospital'
        self.upload_dialog = None
        self.file_manager = None
        super().__init__(**kwargs)
        self.app = MDApp.get_running_app()
        self.name = 'medical_records'
        # 延迟初始化UI
        Clock.schedule_once(self.init_ui, 0.2)

    def init_ui(self, dt=0):
        """初始化UI"""
        try:
            # 延迟确保ids已绑定
            def _do_init(*_):
                try:
                    if not hasattr(self, 'ids'):
                        print("警告：ids未初始化")
                        return
                    
                    # 初始化当前分类
                    self.current_category = 'hospital'
                    
                    # 更新tab按钮状态
                    self.update_tab_buttons()
                    
                    # 加载默认分类的记录
                    self.load_records('hospital')
                    
                    # 打印界面结构以便调试
                    self._print_widget_hierarchy(self)
                
                except Exception as e:
                    print(f"_do_init初始化失败: {e}")
                    import traceback
                    traceback.print_exc()
            
            # 使用更长的延迟确保KV规则已完全应用
            Clock.schedule_once(_do_init, 0.5)
        except Exception as e:
            print(f"init_ui初始化失败: {e}")
            import traceback
            traceback.print_exc()
    
    def _print_widget_hierarchy(self, widget, level=0):
        """打印组件层次结构，用于调试"""
        indent = "  " * level
        widget_type = type(widget).__name__
        widget_id = widget.id if hasattr(widget, 'id') else 'no_id'
        print(f"{indent}{widget_type} (id={widget_id})")
        
        if hasattr(widget, 'children'):
            for child in widget.children:
                self._print_widget_hierarchy(child, level + 1)
                
    def on_enter(self):
        """屏幕进入时调用"""
        super().on_enter()
        # 重新加载当前分类的记录
        if hasattr(self, 'current_category'):
            self.load_records(self.current_category)

    def switch_tab(self, category):
        """切换tab"""
        print(f"切换到分类: {category}")
        self.current_category = category
        self.load_records(category)
        self.update_tab_buttons()
    
    def update_tab_buttons(self):
        """更新tab按钮状态"""
        try:
            if not hasattr(self, 'ids'):
                print("警告：ids未初始化，无法更新tab按钮")
                return
            
            # 更新按钮背景色和文字颜色
            button_config = [
                ('hospital_btn', 'hospital', self.app.theme.HOSPITAL_COLOR),
                ('outpatient_btn', 'outpatient', self.app.theme.OUTPATIENT_COLOR), 
                ('lab_btn', 'lab', self.app.theme.LAB_COLOR),
                ('tech_btn', 'tech', self.app.theme.TECH_COLOR)
            ]
            
            for btn_id, category, color in button_config:
                btn = self.ids.get(btn_id)
                if not btn:
                    print(f"警告：找不到按钮 {btn_id}")
                    continue
                    
                # 更新按钮状态
                is_active = category == self.current_category
                
                # 活动状态使用完全颜色，非活动状态使用表面颜色但保持边框颜色
                btn.md_bg_color = color if is_active else self.app.theme.SURFACE_COLOR
                btn.line_color = color  # 始终保持边框颜色
                
                # 更新按钮文本颜色
                button_text = None
                for child in btn.children:
                    if isinstance(child, MDButtonText):
                        button_text = child
                        break
                
                if button_text:
                    button_text.text_color = self.app.theme.TEXT_LIGHT if is_active else color
                else:
                    print(f"警告：找不到按钮 {btn_id} 的文本组件")
                
            print(f"已更新tab按钮状态，当前选中：{self.current_category}")
        except Exception as e:
            print(f"更新tab按钮状态时出错: {e}")
            import traceback
            traceback.print_exc()

    def load_records(self, category):
        """加载记录"""
        try:
            if not hasattr(self, 'ids'):
                print("警告：ids未初始化")
                return
                
            container = self.ids.get('content_container')
            if not container:
                print("警告：找不到内容容器")
                return
            
            print(f"正在加载 {category} 类别的记录")
            container.clear_widgets()
            records = self.records_data.get(category, [])
            
            print(f"获取到 {len(records)} 条记录数据")
            for i, record in enumerate(records):
                print(f"记录 {i+1}: {record}")
            
            if not records:
                empty_label = MDLabel(
                    text=f"暂无{self.get_category_name(category)}记录\n点击右上角 + 号添加记录",
                    theme_text_color="Hint",
                    halign="center",
                    valign="middle",
                    size_hint_y=None,
                    height=dp(100),
                    font_size=dp(14)
                )
                container.add_widget(empty_label)
                print("显示空记录提示")
                return

            # 添加一个小标题
            title_label = MDLabel(
                text=f"{self.get_category_name(category)}（{len(records)}条记录）",
                theme_text_color="Primary",
                bold=True,
                font_size=dp(18),
                size_hint_y=None,
                height=dp(40)
            )
            container.add_widget(title_label)
            print(f"添加标题: {self.get_category_name(category)}（{len(records)}条记录）")

            for record in records:
                try:
                    print(f"创建卡片: {record.get('title')}")
                    card = RecordCard(record, category)
                    container.add_widget(card)
                    print(f"成功添加卡片：{record.get('title', '未知')}")
                except Exception as e:
                    print(f"创建卡片时出错：{e}")
                    import traceback
                    traceback.print_exc()
                    print(f"记录数据：{record}")
            
            # 强制更新布局
            container.height = container.minimum_height
            print(f"设置容器高度为 {container.height}")
            Clock.schedule_once(lambda dt: self._force_layout_update(container), 0.1)
        except Exception as e:
            print(f"加载记录时出错: {e}")
            import traceback
            traceback.print_exc()
    
    def _force_layout_update(self, container):
        """强制更新布局"""
        try:
            if not container:
                print("警告：布局更新时container为空")
                return
                
            # 打印容器中的子组件
            print(f"容器中有 {len(container.children)} 个子组件:")
            for i, child in enumerate(container.children):
                print(f"  子组件 {i+1}: {type(child).__name__}")
                
            # 设置正确的高度
            container.height = container.minimum_height
            print(f"更新后的容器高度: {container.height}")
            
            # 对于MDBoxLayout，使用_trigger_layout
            if hasattr(container, '_trigger_layout'):
                container._trigger_layout()
                print("已触发容器的_trigger_layout()")
            elif hasattr(container, 'do_layout'):
                container.do_layout()
                print("已触发容器的do_layout()")
            
            # 更新父容器（MDScrollView）
            parent = container.parent
            if parent and hasattr(parent, '_trigger_layout'):
                parent._trigger_layout()
                print("已触发父容器的_trigger_layout()")
            
            # 主动设置一些组件的可见性属性
            for child in container.children:
                if hasattr(child, 'opacity'):
                    child.opacity = 1
                    print(f"设置子组件 {type(child).__name__} 的opacity为1")
                    
            # 强制刷新一次更多
            def delayed_refresh(dt):
                if hasattr(container, '_trigger_layout'):
                    container._trigger_layout()
                if parent and hasattr(parent, '_trigger_layout'):
                    parent._trigger_layout()
                print("延迟刷新完成")
            
            Clock.schedule_once(delayed_refresh, 0.3)
            
            print(f"布局更新完成，容器中有 {len(container.children)} 个子组件")
        except Exception as e:
            print(f"布局更新时出错: {e}")
            import traceback
            traceback.print_exc()
    
    def get_category_name(self, category):
        """获取分类中文名称"""
        names = {
            'hospital': '住院资料',
            'outpatient': '门诊资料',
            'lab': '检验报告',
            'tech': '技诊报告'
        }
        return names.get(category, '未知分类')
    
    def show_upload_menu(self):
        """显示上传菜单"""
        menu_items = [
            {
                "text": "直接上传文件",
                "on_release": lambda: self.upload_file('direct'),
                "padding": [dp(16), dp(8), dp(16), dp(8)]
            },
            {
                "text": "二维码上传",
                "on_release": lambda: self.upload_file('qr'),
                "padding": [dp(16), dp(8), dp(16), dp(8)]
            },
            {
                "text": "拍照上传",
                "on_release": lambda: self.upload_file('camera'),
                "padding": [dp(16), dp(8), dp(16), dp(8)]
            }
        ]
        self.upload_menu = MDDropdownMenu(
            caller=self.ids.app_bar,
            items=menu_items,
            width=dp(200),
            md_bg_color=self.app.theme.CARD_BACKGROUND,
            elevation=2,
            position="center",
            radius=[dp(8), dp(8), dp(8), dp(8)],
            padding=[dp(8), dp(8), dp(8), dp(8)]
        )
        self.upload_menu.open()
    
    def upload_file(self, method):
        """上传文件"""
        if hasattr(self, 'upload_menu'):
            self.upload_menu.dismiss()
        
        if method == 'direct':
            self.open_file_manager()
        elif method == 'qr':
            self.scan_qr_code()
        elif method == 'camera':
            self.take_photo()
    
    def open_file_manager(self):
        """打开文件管理器"""
        if not self.file_manager:
            self.file_manager = MDFileManager(
                exit_manager=self.exit_file_manager,
                select_path=self.select_file_path
            )
        
        self.file_manager.show('/')  # 显示根目录
    
    def exit_file_manager(self, *args):
        """退出文件管理器"""
        if self.file_manager:
            self.file_manager.close()
    
    def select_file_path(self, path):
        """选择文件路径"""
        self.exit_file_manager()
        toast(f"已选择文件: {path}")
        # 实现文件上传逻辑
        self.upload_selected_file(path)
    
    def scan_qr_code(self):
        """扫描二维码"""
        toast("二维码扫描功能开发中...")
        # TODO: 实现二维码扫描功能
    
    def take_photo(self):
        """拍照上传"""
        toast("拍照功能开发中...")
        # TODO: 实现拍照功能
    
    def upload_selected_file(self, file_path):
        """上传选中的文件"""
        try:
            import os
            if not os.path.exists(file_path):
                self.show_error("文件不存在")
                return
            
            # 获取cloud_api实例
            cloud_api = get_cloud_api()
            if not cloud_api:
                self.show_error("云服务未初始化")
                return
            
            # 获取用户信息
            user_manager = get_user_manager()
            user = user_manager.get_current_user()
            custom_id = getattr(user, 'custom_id', None) if user else None
            
            if not custom_id:
                logger.warning("未获取到有效的custom_id，无法上传文件")
                self.show_error("未获取到用户ID，无法上传")
                return
            
            # 确定文档类型
            document_type = f"medical_record_{self.current_category}"
            
            # 组装元数据
            metadata = {
                'custom_id': custom_id,
                'document_type': document_type,
                'category': self.current_category,
                'category_name': self.get_category_name(self.current_category)
            }
            
            logger.info(f"上传文件元数据: {metadata}")
            
            # 走cloud_api高层API
            result = cloud_api.upload_file(file_path, metadata=metadata, document_type=document_type)
            logger.info(f"上传结果: {result}")
            
            if result and (result.get('success') or result.get('status') == 'success'):
                logger.info("文件上传成功")
                self.show_success("上传成功")
                # 刷新当前类别的记录
                self.load_records(self.current_category)
            else:
                # 提取详细错误信息
                error_msg = None
                if isinstance(result, dict):
                    error_msg = result.get('message')
                    error_detail = result.get('detail')
                    if error_detail:
                        logger.error(f"上传失败详情: {error_detail}")
                        error_msg = f"{error_msg}: {error_detail}" if error_msg else f"错误: {error_detail}"
                
                if not error_msg:
                    error_msg = '上传失败，未知错误'
                
                logger.error(f"上传失败: {error_msg}")
                self.show_error(error_msg)
                
                # 检查是否需要添加到上传队列
                if cloud_api.is_in_local_mode() or "网络" in str(error_msg) or "连接" in str(error_msg):
                    logger.info("尝试添加到上传队列")
                    if cloud_api.add_to_upload_queue(file_path, metadata):
                        self.show_info("文件已添加到上传队列，将在网络恢复后自动上传")
        except Exception as e:
            logger.error(f"上传文件异常: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error(f"上传失败: {e}")
    
    def show_success(self, message):
        """显示成功信息提示"""
        try:
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            snackbar = MDSnackbar(
                MDSnackbarText(text=message),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                size_hint_x=0.8,
                md_bg_color=self.app.theme.SUCCESS_COLOR,
                duration=3
            )
            snackbar.open()
        except Exception as e:
            logger.error(f"显示成功消息时出错: {e}")
            toast(message)
    
    def show_error(self, message):
        """显示错误信息提示"""
        try:
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            snackbar = MDSnackbar(
                MDSnackbarText(text=message),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                size_hint_x=0.8,
                md_bg_color=self.app.theme.ERROR_COLOR,
                duration=3
            )
            snackbar.open()
        except Exception as e:
            logger.error(f"显示错误消息时出错: {e}")
            toast(message)
    
    def show_info(self, message):
        """显示信息提示"""
        try:
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            snackbar = MDSnackbar(
                MDSnackbarText(text=message),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                size_hint_x=0.8,
                md_bg_color=self.app.theme.INFO_COLOR,
                duration=3
            )
            snackbar.open()
        except Exception as e:
            logger.error(f"显示信息消息时出错: {e}")
            toast(message)
    
    def add_record(self):
        """添加新记录"""
        category_names = {
            'hospital': '住院资料',
            'outpatient': '门诊资料', 
            'lab': '检验报告',
            'tech': '技诊报告'
        }
        
        category_name = category_names.get(self.current_category, '记录')
        toast(f"添加{category_name}功能开发中...")
        # TODO: 实现添加记录功能
    
    def view_record(self, record_data, category):
        """查看记录详情"""
        toast(f"查看记录: {record_data.get('title', '未知记录')}")
        # TODO: 实现查看记录详情功能
    
    def edit_record(self, record_data, category):
        """编辑记录"""
        toast(f"编辑记录: {record_data.get('title', '未知记录')}")
        # TODO: 实现编辑记录功能
    
    def set_reminder(self, record_data, category):
        """设置提醒"""
        if category in ['lab', 'tech']:
            toast(f"设置提醒: {record_data.get('title', '未知记录')}")
            # TODO: 实现设置提醒功能
        else:
            toast("此类型记录不支持提醒功能")
    
    def go_back(self):
        """返回上一页"""
        if hasattr(self.app, 'root') and hasattr(self.app.root, 'current'):
            self.app.root.current = 'homepage_screen'
        else:
            toast("返回功能异常")
    
    def view_record_document(self, record_data, category):
        """查看记录文档"""
        title = record_data.get('title', '记录详情')
        files = record_data.get('files', [])
        
        content = f"记录类型: {record_data.get('type', '未知')}\n"
        content += f"日期: {record_data.get('date', '未知')}\n"
        content += f"描述: {record_data.get('description', '暂无')}\n\n"
        content += "相关文件:\n"
        for file in files:
            content += f"• {file}\n"
        
        dialog = MDDialog(
            title=title,
            text=content,
            buttons=[
                MDButton(
                    MDButtonText(text="关闭"),
                    style="text",
                    on_release=lambda x: dialog.dismiss()
                ),
                MDButton(
                    MDButtonText(text="查看原始文档"),
                    style="elevated",
                    on_release=lambda x: self.view_original_document(record_data)
                ),
                MDButton(
                    MDButtonText(text="查看结构化文档"),
                    style="elevated",
                    on_release=lambda x: self.view_structured_document(record_data)
                )
            ]
        )
        dialog.open()
    
    def view_record_with_analysis(self, record_data, category):
        """查看带数据分析的记录"""
        title = record_data.get('title', '记录详情')
        
        dialog = MDDialog(
            title=title,
            text=f"记录详情和数据分析功能开发中...\n\n类型: {record_data.get('type', '未知')}\n日期: {record_data.get('date', '未知')}",
            buttons=[
                MDButton(
                    MDButtonText(text="关闭"),
                    style="text",
                    on_release=lambda x: dialog.dismiss()
                ),
                MDButton(
                    MDButtonText(text="查看数据分析"),
                    style="elevated",
                    on_release=lambda x: self.view_data_analysis(record_data, category)
                )
            ]
        )
        dialog.open()
    
    def view_original_document(self, record_data):
        """查看原始文档"""
        toast("原始文档查看功能开发中...")
        # TODO: 实现原始文档查看
    
    def view_structured_document(self, record_data):
        """查看结构化文档"""
        toast("结构化文档查看功能开发中...")
        # TODO: 实现结构化文档查看
    
    def view_data_analysis(self, record_data, category):
        """查看数据分析"""
        toast(f"{self.get_category_name(category)}数据分析功能开发中...")
        # TODO: 实现数据分析和折线图
    
    def save_reminder(self, record_data, category, dialog):
        """保存提醒设置"""
        dialog.dismiss()
        toast("复查提醒设置功能开发中...")
        # TODO: 实现提醒设置功能