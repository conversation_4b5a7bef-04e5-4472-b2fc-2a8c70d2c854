#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
列出所有注册的API路由
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def list_all_routes():
    """列出所有注册的路由"""
    try:
        print("正在导入FastAPI应用...")
        from app.main import app
        
        print("\n=== 所有注册的路由 ===")
        routes = app.routes
        print(f"总路由数量: {len(routes)}")
        
        api_routes = []
        other_routes = []
        
        for route in routes:
            route_info = {
                'path': getattr(route, 'path', 'N/A'),
                'methods': getattr(route, 'methods', set()),
                'name': getattr(route, 'name', 'N/A'),
                'tags': getattr(route, 'tags', [])
            }
            
            if route_info['path'].startswith('/api'):
                api_routes.append(route_info)
            else:
                other_routes.append(route_info)
        
        print(f"\n=== API路由 ({len(api_routes)}个) ===")
        for route in sorted(api_routes, key=lambda x: x['path']):
            methods_str = ', '.join(sorted(route['methods'])) if route['methods'] else 'N/A'
            tags_str = ', '.join(route['tags']) if route['tags'] else 'N/A'
            print(f"  {methods_str:15} {route['path']:50} [{tags_str}]")
        
        print(f"\n=== 数据管理相关路由 ===")
        data_mgmt_routes = [r for r in api_routes if 'data' in r['path'].lower() or 'management' in r['path'].lower()]
        if data_mgmt_routes:
            for route in data_mgmt_routes:
                methods_str = ', '.join(sorted(route['methods'])) if route['methods'] else 'N/A'
                tags_str = ', '.join(route['tags']) if route['tags'] else 'N/A'
                print(f"  {methods_str:15} {route['path']:50} [{tags_str}]")
        else:
            print("  未找到数据管理相关路由")
        
        print(f"\n=== 聚合API相关路由 ===")
        aggregated_routes = [r for r in api_routes if 'aggregated' in r['path'].lower() or 'v1' in r['path'].lower()]
        if aggregated_routes:
            for route in aggregated_routes:
                methods_str = ', '.join(sorted(route['methods'])) if route['methods'] else 'N/A'
                tags_str = ', '.join(route['tags']) if route['tags'] else 'N/A'
                print(f"  {methods_str:15} {route['path']:50} [{tags_str}]")
        else:
            print("  未找到聚合API相关路由")
        
        print(f"\n=== 其他路由 ({len(other_routes)}个) ===")
        for route in sorted(other_routes, key=lambda x: x['path']):
            methods_str = ', '.join(sorted(route['methods'])) if route['methods'] else 'N/A'
            print(f"  {methods_str:15} {route['path']}")
            
    except Exception as e:
        print(f"❌ 列出路由失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    list_all_routes()
    print("\n路由列表完成")