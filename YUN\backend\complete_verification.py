#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整验证修复结果
"""

import sqlite3
import json
import os
from datetime import datetime

def complete_verification():
    """完整验证修复结果"""
    db_path = "app.db"
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print(f"开始完整验证 - {datetime.now()}")
        print("="*60)
        
        # 1. 验证评估结果的维度分数
        print("\n1. 验证评估结果的维度分数:")
        print("-" * 40)
        
        cursor.execute("""
            SELECT custom_id, template_id, total_score, dimension_scores
            FROM assessment_results
            WHERE template_id IN (1, 3, 5)
            ORDER BY template_id, custom_id
        """)
        
        results = cursor.fetchall()
        total_assessments = len(results)
        valid_assessments = 0
        
        # 模板名称映射
        template_names = {1: '抑郁自评量表', 3: '焦虑自评量表', 5: '蒙特利尔认知评估量表'}
        
        for custom_id, template_id, total_score, dimension_scores in results:
            template_name = template_names.get(template_id, f'模板{template_id}')
            print(f"\n📊 评估 {custom_id} - {template_name} (模板{template_id}):")
            print(f"   总分: {total_score}")
            
            # 检查维度分数
            if dimension_scores:
                try:
                    dim_data = json.loads(dimension_scores)
                    if dim_data and len(dim_data) > 0:
                        print(f"   ✅ 维度分数: {len(dim_data)} 个维度")
                        for dim_key, dim_info in dim_data.items():
                            if isinstance(dim_info, dict):
                                score = dim_info.get('score', 0)
                                name = dim_info.get('name', dim_key)
                                count = dim_info.get('question_count', 0)
                                print(f"      - {name}: {score} 分 ({count} 题)")
                            else:
                                print(f"      - {dim_key}: {dim_info}")
                        valid_assessments += 1
                    else:
                        print(f"   ❌ 维度分数为空")
                except Exception as e:
                    print(f"   ❌ 维度分数解析失败: {e}")
            else:
                print(f"   ❌ 无维度分数")
        
        # 2. 验证答案数据格式
        print(f"\n\n2. 验证答案数据格式:")
        print("-" * 40)
        
        cursor.execute("""
            SELECT custom_id, template_id, raw_answers
            FROM assessment_results
            WHERE template_id IN (1, 3, 5)
            ORDER BY template_id, custom_id
        """)
        
        answer_results = cursor.fetchall()
        valid_answers = 0
        
        for custom_id, template_id, raw_answers in answer_results:
            print(f"\n📝 评估 {custom_id} (模板{template_id}) 答案格式:")
            
            if raw_answers:
                try:
                    answers = json.loads(raw_answers)
                    if isinstance(answers, dict) and len(answers) > 0:
                        print(f"   ✅ 字典格式，包含 {len(answers)} 个答案")
                        # 显示前3个答案示例
                        sample_answers = list(answers.items())[:3]
                        for key, value in sample_answers:
                            print(f"      {key}: {value}")
                        if len(answers) > 3:
                            print(f"      ... 还有 {len(answers) - 3} 个答案")
                        valid_answers += 1
                    elif isinstance(answers, list):
                        print(f"   ⚠️  列表格式，长度 {len(answers)} (应该是字典格式)")
                    else:
                        print(f"   ❌ 格式错误: {type(answers)}")
                except Exception as e:
                    print(f"   ❌ 解析失败: {e}")
            else:
                print(f"   ❌ 答案数据为空")
        
        # 3. 验证模板问题配置
        print(f"\n\n3. 验证模板问题配置:")
        print("-" * 40)
        
        for template_id in [1, 3, 5]:
            cursor.execute("""
                SELECT COUNT(*) as question_count
                FROM assessment_template_questions
                WHERE template_id = ?
            """, (template_id,))
            
            question_count = cursor.fetchone()[0]
            
            template_name = template_names.get(template_id, f"模板{template_id}")
            
            print(f"\n📋 {template_name} (模板{template_id}):")
            print(f"   问题数量: {question_count}")
            
            if question_count > 0:
                print(f"   ✅ 配置正常")
            else:
                print(f"   ❌ 无问题配置")
        
        # 4. 总结报告
        print(f"\n\n4. 修复总结报告:")
        print("="*60)
        
        assessment_success_rate = (valid_assessments / total_assessments * 100) if total_assessments > 0 else 0
        answer_success_rate = (valid_answers / len(answer_results) * 100) if len(answer_results) > 0 else 0
        
        print(f"\n📈 评估结果修复状态:")
        print(f"   总评估数: {total_assessments}")
        print(f"   有效维度分数: {valid_assessments}")
        print(f"   维度分数成功率: {assessment_success_rate:.1f}%")
        
        print(f"\n📝 答案数据修复状态:")
        print(f"   总答案记录: {len(answer_results)}")
        print(f"   有效答案格式: {valid_answers}")
        print(f"   答案格式成功率: {answer_success_rate:.1f}%")
        
        # 5. 最终状态
        print(f"\n🎯 最终修复状态:")
        if assessment_success_rate == 100 and answer_success_rate == 100:
            print(f"   ✅ 所有修复完成！系统已完全修复")
            print(f"   ✅ 维度分数计算正常")
            print(f"   ✅ 答案数据格式正确")
            print(f"   ✅ 支持未来量表扩展")
            success = True
        else:
            print(f"   ⚠️  部分修复未完成")
            if assessment_success_rate < 100:
                print(f"   ❌ 维度分数需要进一步修复")
            if answer_success_rate < 100:
                print(f"   ❌ 答案数据格式需要进一步修复")
            success = False
        
        print(f"\n📋 建议后续操作:")
        print(f"   1. 重启后端服务以应用所有更改")
        print(f"   2. 测试前端显示效果")
        print(f"   3. 验证新评估的完整流程")
        print(f"   4. 测试评估报告生成功能")
        print(f"   5. 添加新量表测试扩展能力")
        
        conn.close()
        return success
        
    except Exception as e:
        print(f"验证过程中出现错误: {e}")
        return False

if __name__ == "__main__":
    complete_verification()