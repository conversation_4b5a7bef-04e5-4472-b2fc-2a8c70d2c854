2025-06-27 23:41:55,331 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-27 23:41:55,340 - auth_service - INFO - 统一认证服务初始化完成
2025-06-27 23:41:55,434 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-27 23:41:55,437 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-27 23:41:56,894 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-06-27 23:41:56,900 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-06-27 23:41:57,276 - health_monitor - INFO - 健康监控器初始化完成
2025-06-27 23:41:57,287 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-27 23:41:57,298 - alert_manager - INFO - 已初始化默认告警规则
2025-06-27 23:41:57,299 - alert_manager - INFO - 已初始化默认通知渠道
2025-06-27 23:41:57,304 - alert_manager - INFO - 告警管理器初始化完成
2025-06-27 23:41:58,236 - db_service - INFO - 数据库引擎和会话工厂创建成功
2025-06-27 23:41:58,236 - db_service - INFO - 数据库服务初始化完成
2025-06-27 23:41:58,244 - notification_service - INFO - 通知服务初始化完成
2025-06-27 23:41:58,247 - main - INFO - 错误处理模块导入成功
2025-06-27 23:41:58,305 - main - INFO - 监控模块导入成功
2025-06-27 23:41:58,305 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-06-27 23:42:09,188 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-27 23:42:09,197 - auth_service - INFO - 统一认证服务初始化完成
2025-06-27 23:42:09,298 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-27 23:42:09,301 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-27 23:42:10,949 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-06-27 23:42:10,954 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-06-27 23:42:11,957 - health_monitor - INFO - 健康监控器初始化完成
2025-06-27 23:42:11,987 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-27 23:42:12,000 - alert_manager - INFO - 已初始化默认告警规则
2025-06-27 23:42:12,001 - alert_manager - INFO - 已初始化默认通知渠道
2025-06-27 23:42:12,006 - alert_manager - INFO - 告警管理器初始化完成
2025-06-27 23:42:13,952 - db_service - INFO - 数据库引擎和会话工厂创建成功
2025-06-27 23:42:13,954 - db_service - INFO - 数据库服务初始化完成
2025-06-27 23:42:13,966 - notification_service - INFO - 通知服务初始化完成
2025-06-27 23:42:13,967 - main - INFO - 错误处理模块导入成功
2025-06-27 23:42:14,083 - main - INFO - 监控模块导入成功
2025-06-27 23:42:14,107 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-06-27 23:42:18,290 - main - INFO - 错误处理模块导入成功
2025-06-27 23:42:18,351 - main - INFO - 监控模块导入成功
2025-06-27 23:42:18,370 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-06-27 23:42:20,199 - main - INFO - 应用启动中...
2025-06-27 23:42:20,200 - error_handling - INFO - 错误处理已设置
2025-06-27 23:42:20,201 - main - INFO - 错误处理系统初始化完成
2025-06-27 23:42:20,202 - monitoring - INFO - 添加指标端点成功: /metrics
2025-06-27 23:42:20,203 - monitoring - INFO - 添加健康检查端点成功: /health
2025-06-27 23:42:20,206 - monitoring - INFO - 添加详细健康检查端点成功: /api/health/detailed
2025-06-27 23:42:20,207 - monitoring - INFO - 系统信息初始化完成: Markey, Windows-10-10.0.19045-SP0, Python 3.13.2, CPU核心数: 4
2025-06-27 23:42:20,219 - monitoring - INFO - 启动资源监控线程成功
2025-06-27 23:42:20,219 - monitoring - INFO - 监控系统初始化成功（不使用中间件）
2025-06-27 23:42:20,221 - monitoring - INFO - 监控系统初始化完成
2025-06-27 23:42:20,221 - main - INFO - 监控系统初始化完成
2025-06-27 23:42:20,223 - app.db.init_db - INFO - 所有模型导入成功
2025-06-27 23:42:20,224 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-06-27 23:42:20,238 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-27 23:42:20,246 - app.db.init_db - INFO - 所有模型导入成功
2025-06-27 23:42:20,247 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-06-27 23:42:20,249 - app.db.init_db - INFO - 正在运行数据库迁移...
2025-06-27 23:42:20,250 - app.db.init_db - INFO - 正在检查并更新数据库表结构...
2025-06-27 23:42:20,252 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-27 23:42:20,254 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alerts")
2025-06-27 23:42:20,256 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,262 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_rules")
2025-06-27 23:42:20,264 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,267 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_channels")
2025-06-27 23:42:20,270 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,272 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-06-27 23:42:20,274 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,281 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_records")
2025-06-27 23:42:20,282 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,284 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_overviews")
2025-06-27 23:42:20,316 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,317 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medical_records")
2025-06-27 23:42:20,319 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,330 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("inpatient_records")
2025-06-27 23:42:20,334 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,335 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("surgery_records")
2025-06-27 23:42:20,337 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,339 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_reports")
2025-06-27 23:42:20,340 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,347 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_report_items")
2025-06-27 23:42:20,349 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,351 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_reports")
2025-06-27 23:42:20,353 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,355 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("examination_reports")
2025-06-27 23:42:20,356 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,363 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("documents")
2025-06-27 23:42:20,366 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,368 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("other_records")
2025-06-27 23:42:20,371 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,373 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("operation_logs")
2025-06-27 23:42:20,381 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,383 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("follow_up_records")
2025-06-27 23:42:20,385 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,387 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_diaries")
2025-06-27 23:42:20,390 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,392 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("registration_records")
2025-06-27 23:42:20,399 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,401 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("prescription_records")
2025-06-27 23:42:20,404 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,406 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("laboratory_records")
2025-06-27 23:42:20,409 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,416 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_records")
2025-06-27 23:42:20,417 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,419 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medications")
2025-06-27 23:42:20,422 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,424 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medication_usages")
2025-06-27 23:42:20,432 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,433 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("role_applications")
2025-06-27 23:42:20,438 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,445 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessments")
2025-06-27 23:42:20,451 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,454 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_items")
2025-06-27 23:42:20,456 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,458 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_responses")
2025-06-27 23:42:20,465 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,468 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_templates")
2025-06-27 23:42:20,470 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,472 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_template_questions")
2025-06-27 23:42:20,474 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,483 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaires")
2025-06-27 23:42:20,488 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,490 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_items")
2025-06-27 23:42:20,492 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,498 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_responses")
2025-06-27 23:42:20,499 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,501 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_answers")
2025-06-27 23:42:20,504 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,505 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_templates")
2025-06-27 23:42:20,507 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,514 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_template_questions")
2025-06-27 23:42:20,517 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,525 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_distributions")
2025-06-27 23:42:20,532 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,534 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_distributions")
2025-06-27 23:42:20,537 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,539 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_results")
2025-06-27 23:42:20,545 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,547 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_results")
2025-06-27 23:42:20,550 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,554 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("report_templates")
2025-06-27 23:42:20,555 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,557 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("service_stats")
2025-06-27 23:42:20,560 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-27 23:42:20,565 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-27 23:42:20,567 - app.db.init_db - INFO - 数据库表结构检查和更新完成
2025-06-27 23:42:20,573 - app.db.init_db - INFO - 模型关系初始化完成
2025-06-27 23:42:20,574 - app.db.init_db - INFO - 模型关系设置完成
2025-06-27 23:42:20,581 - main - INFO - 数据库初始化完成（强制重建）
2025-06-27 23:42:20,584 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-27 23:42:20,585 - main - INFO - 数据库连接正常
2025-06-27 23:42:20,586 - main - INFO - 开始初始化模板数据
2025-06-27 23:42:20,589 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-27 23:42:20,949 - app.db.init_templates - INFO - 开始初始化评估量表模板...
2025-06-27 23:42:21,011 - app.db.init_templates - INFO - 成功初始化 5 个评估量表模板
2025-06-27 23:42:21,024 - app.db.init_templates - INFO - 开始初始化调查问卷模板...
2025-06-27 23:42:21,122 - app.db.init_templates - INFO - 成功初始化 5 个调查问卷模板
2025-06-27 23:42:21,123 - main - INFO - 模板数据初始化完成
2025-06-27 23:42:21,124 - main - INFO - 数据库表结构已经标准化，不需要迁移
2025-06-27 23:42:21,125 - main - INFO - 应用启动完成
