<template>
  <div class="statistics-panel">
    <el-row :gutter="20">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-icon">
            <el-icon><el-icon-user /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-title">总用户数</div>
            <div class="stat-value">{{ statistics.totalUsers }}</div>
            <div class="stat-change" :class="{ 'positive': statistics.userGrowth > 0, 'negative': statistics.userGrowth < 0 }">
              <el-icon v-if="statistics.userGrowth > 0"><el-icon-arrow-up /></el-icon>
              <el-icon v-else-if="statistics.userGrowth < 0"><el-icon-arrow-down /></el-icon>
              {{ Math.abs(statistics.userGrowth) }}%
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-icon">
            <el-icon><el-icon-plus /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-title">新增用户</div>
            <div class="stat-value">{{ statistics.newUsers }}</div>
            <div class="stat-change" :class="{ 'positive': statistics.newUserGrowth > 0, 'negative': statistics.newUserGrowth < 0 }">
              <el-icon v-if="statistics.newUserGrowth > 0"><el-icon-arrow-up /></el-icon>
              <el-icon v-else-if="statistics.newUserGrowth < 0"><el-icon-arrow-down /></el-icon>
              {{ Math.abs(statistics.newUserGrowth) }}%
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-icon">
            <el-icon><el-icon-view /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-title">活跃用户</div>
            <div class="stat-value">{{ statistics.activeUsers }}</div>
            <div class="stat-change" :class="{ 'positive': statistics.activeUserGrowth > 0, 'negative': statistics.activeUserGrowth < 0 }">
              <el-icon v-if="statistics.activeUserGrowth > 0"><el-icon-arrow-up /></el-icon>
              <el-icon v-else-if="statistics.activeUserGrowth < 0"><el-icon-arrow-down /></el-icon>
              {{ Math.abs(statistics.activeUserGrowth) }}%
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-icon">
            <el-icon><el-icon-document /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-title">总文档数</div>
            <div class="stat-value">{{ statistics.totalDocuments }}</div>
            <div class="stat-change" :class="{ 'positive': statistics.documentGrowth > 0, 'negative': statistics.documentGrowth < 0 }">
              <el-icon v-if="statistics.documentGrowth > 0"><el-icon-arrow-up /></el-icon>
              <el-icon v-else-if="statistics.documentGrowth < 0"><el-icon-arrow-down /></el-icon>
              {{ Math.abs(statistics.documentGrowth) }}%
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { getMockData, isMockEnabled } from '../../mocks/mockDataManager';
import axios from 'axios';

const statistics = ref({
  totalUsers: 0,
  userGrowth: 0,
  newUsers: 0,
  newUserGrowth: 0,
  activeUsers: 0,
  activeUserGrowth: 0,
  totalDocuments: 0,
  documentGrowth: 0
});

onMounted(async () => {
  try {
    // 实际项目中应该从API获取数据
    // const response = await axios.get('/api/dashboard/statistics');
    // statistics.value = response.data;
    
    // 如果启用了模拟数据，使用模拟数据
    if (isMockEnabled()) {
      statistics.value = getMockData('dashboardStats');
    }
  } catch (error) {
    console.error('获取统计数据失败:', error);
    // 如果启用了模拟数据，使用模拟数据
    if (isMockEnabled()) {
      statistics.value = getMockData('dashboardStats');
    }
  }
});
</script>

<style scoped>
.statistics-panel {
  margin-bottom: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 15px;
  margin-bottom: 20px;
  transition: transform 0.3s;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-icon {
  font-size: 2.5rem;
  margin-right: 15px;
  color: #409EFF;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: rgba(64, 158, 255, 0.1);
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 0.9rem;
  color: #909399;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 1.8rem;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-change {
  font-size: 0.8rem;
  display: flex;
  align-items: center;
}

.positive {
  color: #67C23A;
}

.negative {
  color: #F56C6C;
}
</style>
