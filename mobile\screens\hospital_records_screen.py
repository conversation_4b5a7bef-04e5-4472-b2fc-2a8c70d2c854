"""住院资料管理模块 - 管理用户住院记录和相关资料"""
"""住院资料管理屏幕模块

提供住院记录的查看、添加和管理功能。
"""

from kivymd.app import MDApp
from kivy.logger import Logger as KivyLogger
from kivy.metrics import dp
from kivy.properties import StringProperty, ListProperty
from kivy.clock import Clock
from kivy.lang import Builder
from kivy.core.window import Window
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDIconButton, MDButtonText, MDButton
from kivymd.uix.label import MDLabel
from kivymd.uix.card import MDCard
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.list import MDList, MDListItem
from kivymd.uix.divider import MDDivider
from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText
from kivymd.uix.dialog import MDDialog
from kivy.uix.widget import Widget

from .base_screen import BaseScreen
from widgets.logo import HealthLogo
from theme import AppTheme, AppMetrics, FontStyles

from kivy.factory import Factory

# 设置日志
logger = KivyLogger

KV = '''
<HospitalRecordCard>:
    orientation: 'vertical'
    size_hint_y: None
    height: dp(100)
    md_bg_color: app.theme.CARD_BACKGROUND
    radius: [dp(12)]
    elevation: 2
    padding: [dp(16), dp(8), dp(16), dp(8)]
    MDLabel:
        text: root.title
        font_size: app.font_styles.TITLE_MEDIUM['font_size']
        font_name: app.font_styles.TITLE_MEDIUM['font_name']
        bold: app.font_styles.TITLE_MEDIUM['bold']
        theme_text_color: "Custom"
        text_color: app.theme.TEXT_PRIMARY
        size_hint_y: None
        height: self.texture_size[1]
    MDLabel:
        text: root.date
        font_size: app.font_styles.BODY_SMALL['font_size']
        font_name: app.font_styles.BODY_SMALL['font_name']
        theme_text_color: "Custom"
        text_color: app.theme.TEXT_SECONDARY
        size_hint_y: None
        height: self.texture_size[1]
    MDLabel:
        text: root.summary
        font_size: app.font_styles.BODY_SMALL['font_size']
        font_name: app.font_styles.BODY_SMALL['font_name']
        theme_text_color: "Custom"
        text_color: app.theme.TEXT_SECONDARY
        size_hint_y: None
        height: self.texture_size[1]

<HospitalRecordsScreen>:
    md_bg_color: app.theme.BACKGROUND_COLOR
    
    MDBoxLayout:
        orientation: "vertical"
        spacing: dp(8)
        
        # 顶部应用栏
        MDBoxLayout:
            size_hint_y: None
            height: dp(56)
            spacing: dp(10)
            padding: [dp(10), 0, dp(10), 0]
            md_bg_color: app.theme.PRIMARY_COLOR
            
            MDIconButton:
                icon: "arrow-left"
                on_release: root.go_back()
                pos_hint: {"center_y": .5}
                theme_icon_color: "Custom"
                icon_color: "white"
                
            MDLabel:
                text: "住院资料管理"
                halign: 'center'
                valign: 'middle'
                font_size: '20sp'
                theme_text_color: "Custom"
                text_color: "white"
                bold: True
                
            MDIconButton:
                icon: "plus"
                on_release: root.add_record()
                pos_hint: {"center_y": .5}
                theme_icon_color: "Custom"
                icon_color: "white"
        
        # 添加Logo组件
        HealthLogo:
            id: health_logo
            size_hint_y: None
            height: dp(120)
            logo_size: dp(80), dp(80)
            title_font_size: dp(18)
            subtitle_font_size: dp(14)
            
        MDScrollView:
            do_scroll_x: False
            do_scroll_y: True
            
            MDBoxLayout:
                id: records_container
                orientation: 'vertical'
                size_hint_y: None
                height: self.minimum_height
                padding: [dp(16), dp(16), dp(16), dp(16)]
                spacing: dp(12)
'''

class HospitalRecordCard(MDCard):
    title = StringProperty("")
    date = StringProperty("")
    summary = StringProperty("")
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.ripple_behavior = True  # 启用涟漪效果
        self.ripple_duration_in_slow = 0.1  # 加快涟漪动画
        self.ripple_color = (0.8, 0.8, 0.8, 0.5)  # 设置涟漪颜色
        
    def on_release(self, *args):
        """点击卡片时调用"""
        app = MDApp.get_running_app()
        if hasattr(app.root.current_screen, 'show_record_detail'):
            # 创建记录数据字典
            record_data = {
                "title": self.title,
                "date": self.date,
                "summary": self.summary,
                "details": self.summary  # 这里可以添加更多详细信息
            }
            app.root.current_screen.show_record_detail(record_data)
            
    def on_touch_up(self, touch):
        """处理触摸释放事件"""
        if self.collide_point(*touch.pos) and touch.is_mouse_scrolling is False:
            self.on_release()
        return super().on_touch_up(touch)

class HospitalRecordsScreen(BaseScreen):
    """住院资料管理屏幕"""
    records = ListProperty([])
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.app = MDApp.get_running_app()
        self.hospital_records = []
        Clock.schedule_once(self.init_ui, 0.2)

    def init_ui(self, dt=0):
        self.load_hospital_records()

    def on_enter(self):
        super().on_enter()
        self.init_ui()

    def load_hospital_records(self):
        """加载住院记录"""
        try:
            user_id = getattr(self.app, 'user_data', {}).get('custom_id', None)
            if not user_id:
                self.show_error("未获取到用户ID，请重新登录")
                # 检查控件是否存在
                if not hasattr(self, 'ids'):
                    return
                    
                if 'records_container' not in self.ids or not self.ids.records_container:
                    return
                    
                try:
                    self.ids.records_container.clear_widgets()
                except ReferenceError:
                    return
                    
                empty_label = MDLabel(
                    text="暂无住院记录\n请先登录后查看",
                    halign="center",
                    theme_text_color="Secondary",
                    font_size=app.font_styles.BODY_MEDIUM['font_size'],
                    font_name=app.font_styles.BODY_MEDIUM['font_name'],
                    role="medium"
                )
                
                # 再次检查控件是否存在
                if not hasattr(self, 'ids') or 'records_container' not in self.ids or not self.ids.records_container:
                    return
                    
                self.ids.records_container.add_widget(empty_label)
                return
            
            # 检查控件是否存在
            if not hasattr(self, 'ids'):
                return
                
            if 'records_container' not in self.ids or not self.ids.records_container:
                return
            
            try:
                self.ids.records_container.clear_widgets()
            except ReferenceError:
                return
            
            # 示例数据，后续对接API
            self.records = [
                {
                    "id": "hr_20230510_001",
                    "title": "2023年5月住院", 
                    "date": "2023-05-10", 
                    "category": "入院记录",
                    "hospital": "市人民医院",
                    "summary": "因高血压住院，治疗顺利出院。", 
                    "details": "入院时血压180/110mmHg，经过降压药物治疗和饮食调整，出院时血压稳定在135/85mmHg。",
                    "original_doc": None,
                    "structured_doc": {
                        "admission_reason": "高血压",
                        "vital_signs": {"bp_in": "180/110", "bp_out": "135/85"},
                        "treatment": "降压药物治疗和饮食调整",
                        "outcome": "治疗顺利出院"
                    }
                },
                {
                    "id": "hr_20221002_001",
                    "title": "2022年10月住院", 
                    "date": "2022-10-02", 
                    "category": "手术记录",
                    "hospital": "中心医院",
                    "summary": "因阑尾炎手术，恢复良好。", 
                    "details": "右下腹疼痛伴恶心呕吐，确诊为急性阑尾炎，行腹腔镜阑尾切除术，术后恢复良好。",
                    "original_doc": None,
                    "structured_doc": {
                        "diagnosis": "急性阑尾炎",
                        "symptoms": "右下腹疼痛伴恶心呕吐",
                        "surgery": "腹腔镜阑尾切除术",
                        "recovery": "术后恢复良好"
                    }
                }
            ]
            
            # 再次检查控件是否存在
            if not hasattr(self, 'ids') or 'records_container' not in self.ids or not self.ids.records_container:
                return
                
            if not self.records:
                # 显示空状态
                empty_label = MDLabel(
                    text="暂无住院记录\n点击右上角的 + 按钮添加记录",
                    halign="center",
                    theme_text_color="Secondary",
                    font_size=app.font_styles.BODY_MEDIUM['font_size'],
                    font_name=app.font_styles.BODY_MEDIUM['font_name'],
                    role="medium"
                )
                self.ids.records_container.add_widget(empty_label)
                return
            
            for rec in self.records:
                card = HospitalRecordCard(
                    title=rec["title"], 
                    date=rec["date"], 
                    summary=rec["summary"]
                )
                
                # 添加点击事件
                card.bind(on_release=lambda card_widget, rec_data=rec: self.show_record_detail(rec_data))
                
                # 再次检查控件是否存在
                if not hasattr(self, 'ids') or 'records_container' not in self.ids or not self.ids.records_container:
                    return
                    
                self.ids.records_container.add_widget(card)
                
        except Exception as e:
            KivyLogger.error(f"HospitalRecordsScreen: 加载住院记录失败: {e}")
            self.show_error(f"加载住院记录失败: {str(e)}")

    def go_back(self):
        """返回上一页"""
        try:
            # 直接返回主页，避免调用不存在的super().go_back()
            app = MDApp.get_running_app()
            app.root.current = 'homepage_screen'
        except Exception as e:
            KivyLogger.error(f"HospitalRecordsScreen: 返回失败: {e}")
            app = MDApp.get_running_app()
            app.root.current = 'health_overview_screen'

    def add_record(self):
        """添加住院记录"""
        try:
            self.show_upload_dialog()
        except Exception as e:
            KivyLogger.error(f"HospitalRecordsScreen: 添加记录失败: {e}")
            self.show_error(f"添加记录失败: {str(e)}")
    
    def show_upload_dialog(self):
        """显示上传对话框"""
        from kivymd.uix.textfield import MDTextField
        from kivymd.uix.button import MDButton, MDButtonText
        from kivymd.uix.menu import MDDropdownMenu
        from datetime import datetime
        
        # 创建内容区域
        content = MDBoxLayout(
            orientation='vertical',
            spacing=dp(16),
            size_hint_y=None,
            height=dp(400),
            padding=[dp(16), dp(8), dp(16), dp(8)]
        )
        
        # 记录标题
        self.title_field = MDTextField(
            hint_text='记录标题',
            required=True,
            size_hint_y=None,
            height=dp(56)
        )
        
        # 分类选择
        self.category_field = MDTextField(
            hint_text='选择分类',
            readonly=True,
            text='入院记录',
            size_hint_y=None,
            height=dp(56)
        )
        
        # 医院名称
        self.hospital_field = MDTextField(
            hint_text='医院名称',
            required=True,
            size_hint_y=None,
            height=dp(56)
        )
        
        # 日期
        self.date_field = MDTextField(
            hint_text='日期 (YYYY-MM-DD)',
            text=datetime.now().strftime('%Y-%m-%d'),
            size_hint_y=None,
            height=dp(56)
        )
        
        # 上传方式选择
        upload_layout = MDBoxLayout(
            orientation='horizontal',
            spacing=dp(8),
            size_hint_y=None,
            height=dp(48)
        )
        
        direct_upload_btn = MDButton(
            style="outlined",
            on_release=self.direct_upload
        )
        direct_upload_btn.add_widget(MDButtonText(
            text="直接上传",
            font_size=app.font_styles.BUTTON_MEDIUM['font_size'],
            font_name=app.font_styles.BUTTON_MEDIUM['font_name'],
            bold=app.font_styles.BUTTON_MEDIUM['bold'],
            theme_text_color="Primary"
        ))
        
        qr_upload_btn = MDButton(
            style="outlined",
            on_release=self.qr_upload
        )
        qr_upload_btn.add_widget(MDButtonText(
            text="二维码上传",
            font_size=app.font_styles.BUTTON_MEDIUM['font_size'],
            font_name=app.font_styles.BUTTON_MEDIUM['font_name'],
            bold=app.font_styles.BUTTON_MEDIUM['bold'],
            theme_text_color="Primary"
        ))
        
        photo_upload_btn = MDButton(
            style="outlined",
            on_release=self.photo_upload
        )
        photo_upload_btn.add_widget(MDButtonText(
            text="拍照上传",
            font_size=app.font_styles.BUTTON_MEDIUM['font_size'],
            font_name=app.font_styles.BUTTON_MEDIUM['font_name'],
            bold=app.font_styles.BUTTON_MEDIUM['bold'],
            theme_text_color="Primary"
        ))
        
        upload_layout.add_widget(direct_upload_btn)
        upload_layout.add_widget(qr_upload_btn)
        upload_layout.add_widget(photo_upload_btn)
        
        content.add_widget(self.title_field)
        content.add_widget(self.category_field)
        content.add_widget(self.hospital_field)
        content.add_widget(self.date_field)
        content.add_widget(upload_layout)
        
        # 创建按钮
        cancel_button = MDButton(
            style="text",
            on_release=lambda x: self.upload_dialog.dismiss()
        )
        cancel_button.add_widget(MDButtonText(
            text="取消",
            font_size=app.font_styles.BUTTON_MEDIUM['font_size'],
            font_name=app.font_styles.BUTTON_MEDIUM['font_name'],
            bold=app.font_styles.BUTTON_MEDIUM['bold'],
            theme_text_color="Primary"
        ))
        
        save_button = MDButton(
            style="filled",
            on_release=self.save_record
        )
        save_button.add_widget(MDButtonText(
            text="保存",
            font_size=app.font_styles.BUTTON_MEDIUM['font_size'],
            font_name=app.font_styles.BUTTON_MEDIUM['font_name'],
            bold=app.font_styles.BUTTON_MEDIUM['bold'],
            theme_text_color="Primary"
        ))
        
        # 创建对话框
        self.upload_dialog = MDDialog(
            title='添加住院记录',
            content=content,
            buttons=[cancel_button, save_button]
        )
        
        # 绑定分类选择
        self.category_field.bind(on_focus=self.show_category_menu)
        
        self.upload_dialog.open()
    
    def show_category_menu(self, instance, focus):
        """显示分类选择菜单"""
        if not focus:
            return
            
        categories = ['入院记录', '出院小结', '手术记录', '病程记录', '检查报告', '其他记录']
        
        menu_items = [
            {
                'text': category,
                'viewclass': 'OneLineListItem',
                'on_release': lambda x=category: self.select_category(x)
            }
            for category in categories
        ]
        
        self.category_menu = MDDropdownMenu(
            caller=self.category_field,
            items=menu_items,
            width_mult=4
        )
        self.category_menu.open()
    
    def select_category(self, category):
        """选择分类"""
        self.category_field.text = category
        self.category_menu.dismiss()
    
    def direct_upload(self, instance):
        """直接上传文件"""
        self.show_info('直接上传功能开发中')
    
    def qr_upload(self, instance):
        """二维码上传"""
        self.show_info('二维码上传功能开发中')
    
    def photo_upload(self, instance):
        """拍照上传"""
        self.show_info('拍照上传功能开发中')
    
    def save_record(self, instance):
        """保存记录"""
        try:
            # 验证必填字段
            if not self.title_field.text or not self.hospital_field.text:
                self.show_error('请填写完整信息')
                return
            
            # 生成记录数据
            from datetime import datetime
            record_data = {
                'id': f"hr_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                'title': self.title_field.text,
                'category': self.category_field.text,
                'hospital': self.hospital_field.text,
                'date': self.date_field.text,
                'summary': f"{self.category_field.text} - {self.hospital_field.text}",
                'details': f"记录标题：{self.title_field.text}\n医院：{self.hospital_field.text}\n日期：{self.date_field.text}",
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat(),
                'original_doc': None,
                'structured_doc': {
                    'title': self.title_field.text,
                    'category': self.category_field.text,
                    'hospital': self.hospital_field.text,
                    'date': self.date_field.text
                }
            }
            
            # 添加到记录列表
            self.records.append(record_data)
            
            # 这里可以调用API保存到后端
            # self.sync_record_to_api(record_data)
            
            # 更新显示
            self.load_hospital_records()
            
            self.upload_dialog.dismiss()
            self.show_info('记录保存成功')
            
        except Exception as e:
            KivyLogger.error(f"保存记录失败: {e}")
            self.show_error('保存失败')

    def show_info(self, message):
        """显示信息提示"""
        try:
            app = MDApp.get_running_app()
            if hasattr(app, 'show_notification'):
                app.show_notification(message)
            else:
                snackbar = MDSnackbar(
                    MDSnackbarText(
                        text=message,
                    ),
                    pos_hint={"center_x": 0.5},
                    duration=2,
                )
                snackbar.open()
        except Exception as e:
            KivyLogger.error(f"HospitalRecordsScreen: 显示信息失败: {e}")
    
    def show_error(self, message):
        """显示错误提示"""
        try:
            app = MDApp.get_running_app()
            if hasattr(app, 'show_error'):
                app.show_error(message)
            elif hasattr(app, 'show_notification'):
                app.show_notification(f"错误: {message}")
            else:
                snackbar = MDSnackbar(
                    MDSnackbarText(
                        text=f"错误: {message}",
                        theme_text_color="Custom",
                        text_color=(1, 0, 0, 1)
                    ),
                    pos_hint={"center_x": 0.5},
                    duration=2,
                )
                snackbar.open()
        except Exception as e:
            KivyLogger.error(f"HospitalRecordsScreen: 显示错误失败: {e}")

    def show_record_detail(self, record_data):
        """显示住院记录详情"""
        try:
            from kivymd.uix.button import MDButton, MDButtonText
            
            title = record_data.get("title", "住院详情")
            details = record_data.get("details", record_data.get("summary", "无详细信息"))
            date = record_data.get("date", "")
            
            # 创建内容区域
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(8),
                size_hint_y=None,
                height=dp(200),
                padding=[dp(16), dp(8), dp(16), dp(8)]
            )
            
            # 添加日期标签
            date_label = MDLabel(
                text=f"日期: {date}",
                font_size=app.font_styles.BODY_SMALL['font_size'],
                font_name=app.font_styles.BODY_SMALL['font_name'],
                theme_text_color="Secondary",
                size_hint_y=None,
                height=dp(30)
            )
            content.add_widget(date_label)
            
            # 添加详情标签
            details_label = MDLabel(
                text=details,
                font_size=app.font_styles.BODY_MEDIUM['font_size'],
                font_name=app.font_styles.BODY_MEDIUM['font_name'],
                theme_text_color="Primary",
                size_hint_y=None,
                height=dp(150)
            )
            content.add_widget(details_label)
            
            # 创建按钮
            close_button = MDButton(
                style="text",
                on_release=lambda x: dialog.dismiss()
            )
            close_button.add_widget(MDButtonText(
                text="关闭",
                font_size=app.font_styles.BUTTON_MEDIUM['font_size'],
                font_name=app.font_styles.BUTTON_MEDIUM['font_name'],
                bold=app.font_styles.BUTTON_MEDIUM['bold'],
                theme_text_color="Primary"
            ))
            
            # 添加按钮到底部区域
            button_box = MDBoxLayout(
                orientation="horizontal", 
                spacing=dp(8), 
                size_hint_y=None, 
                height=dp(48), 
                pos_hint={"right": 1.0}
            )
            button_box.add_widget(Widget(size_hint_x=1))  # 弹性空间
            button_box.add_widget(close_button)
            content.add_widget(button_box)
            
            # 创建对话框
            dialog = MDDialog(
                title=title,
                content_cls=content
            )
            dialog.open()
            
        except Exception as e:
            KivyLogger.error(f"HospitalRecordsScreen: 显示记录详情失败: {e}")
            self.show_error(f"显示记录详情失败: {str(e)}")

# 在类定义后注册Factory
Factory.register('HospitalRecordsScreen', cls=HospitalRecordsScreen)
Builder.load_string(KV)