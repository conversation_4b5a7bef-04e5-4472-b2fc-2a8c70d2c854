#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试custom_id传递问题
检查移动端API调用时custom_id的传递情况
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'YUN', 'backend'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'mobile'))

import requests
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 数据库配置
DATABASE_URL = "sqlite:///YUN/backend/app.db"
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# API配置
BASE_URL = "http://localhost:8006"

def get_auth_token():
    """获取认证token"""
    print("=== 获取认证token ===")
    
    # 尝试使用已知的用户凭据登录
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/auth/login_json",
            json=login_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            token = data.get('access_token')
            custom_id = data.get('custom_id')
            print(f"登录成功，获取到token: {token[:20]}...")
            print(f"用户custom_id: {custom_id}")
            return token, custom_id
        else:
            print(f"登录失败: {response.status_code} - {response.text}")
            return None, None
            
    except Exception as e:
        print(f"登录异常: {e}")
        return None, None

def test_custom_id_in_database():
    """检查数据库中的custom_id数据"""
    print("=== 检查数据库中的custom_id数据 ===")
    
    db = SessionLocal()
    try:
        # 检查用户表中的custom_id
        result = db.execute(text("""
            SELECT id, username, custom_id, full_name 
            FROM users 
            WHERE custom_id IS NOT NULL 
            LIMIT 10
        """))
        
        users = result.fetchall()
        print(f"找到 {len(users)} 个有custom_id的用户:")
        for user in users:
            print(f"  ID: {user[0]}, 用户名: {user[1]}, Custom ID: {user[2]}, 姓名: {user[3]}")
        
        # 检查评估表中的custom_id
        result = db.execute(text("""
            SELECT id, name, custom_id, template_id, status 
            FROM assessments 
            WHERE custom_id IS NOT NULL 
            LIMIT 10
        """))
        
        assessments = result.fetchall()
        print(f"\n找到 {len(assessments)} 个有custom_id的评估:")
        for assessment in assessments:
            print(f"  ID: {assessment[0]}, 名称: {assessment[1]}, Custom ID: {assessment[2]}, Template ID: {assessment[3]}, 状态: {assessment[4]}")
            
        # 检查所有评估数据（不限制custom_id）
        result = db.execute(text("""
            SELECT id, name, custom_id, template_id, status 
            FROM assessments 
            LIMIT 10
        """))
        
        all_assessments = result.fetchall()
        print(f"\n所有评估数据（前10个）:")
        for assessment in all_assessments:
            print(f"  ID: {assessment[0]}, 名称: {assessment[1]}, Custom ID: {assessment[2]}, Template ID: {assessment[3]}, 状态: {assessment[4]}")
            
        # 检查特定custom_id的评估
        test_custom_ids = ['SM_001', 'SM_006', 'SM_008']
        for custom_id in test_custom_ids:
            result = db.execute(text("""
                SELECT id, name, custom_id, template_id, status 
                FROM assessments 
                WHERE custom_id = :custom_id
            """), {'custom_id': custom_id})
            
            specific_assessments = result.fetchall()
            print(f"\nCustom ID '{custom_id}' 的评估数据: {len(specific_assessments)} 个")
            for assessment in specific_assessments:
                print(f"  ID: {assessment[0]}, 名称: {assessment[1]}, Template ID: {assessment[3]}, 状态: {assessment[4]}")
            
    except Exception as e:
        print(f"数据库查询错误: {e}")
    finally:
        db.close()

def test_api_with_custom_id(token, user_custom_id):
    """测试API调用时custom_id的传递"""
    print("\n=== 测试API调用时custom_id的传递 ===")
    
    if not token:
        print("没有有效的token，跳过API测试")
        return
    
    # 使用一个已知的custom_id进行测试
    test_custom_id = "SM_006"  # 从之前的测试文件中看到的
    
    # 测试不同的API调用方式
    test_cases = [
        {
            "name": "使用Bearer token + X-User-ID头部",
            "headers": {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}",
                "X-User-ID": test_custom_id
            },
            "params": {}
        },
        {
            "name": "使用Bearer token + custom_id查询参数",
            "headers": {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}"
            },
            "params": {
                "custom_id": test_custom_id
            }
        },
        {
            "name": "使用Bearer token + 登录用户的custom_id",
            "headers": {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}",
                "X-User-ID": user_custom_id
            },
            "params": {}
        },
        {
            "name": "仅使用Bearer token（无custom_id）",
            "headers": {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}"
            },
            "params": {}
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {test_case['name']}")
        print(f"Headers: {test_case['headers']}")
        print(f"Params: {test_case['params']}")
        
        try:
            response = requests.get(
                f"{BASE_URL}/api/mobile/assessments",
                headers=test_case['headers'],
                params=test_case['params'],
                timeout=10
            )
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"完整响应数据: {data}")
                
                if isinstance(data, dict) and 'data' in data:
                    data_content = data['data']
                    if 'assessments' in data_content:
                        assessments = data_content['assessments']
                        print(f"返回评估数量: {len(assessments)}")
                        
                        # 检查第一个评估的template字段
                        if assessments:
                            first_assessment = assessments[0]
                            print(f"第一个评估:")
                            print(f"  ID: {first_assessment.get('id')}")
                            print(f"  名称: {first_assessment.get('name')}")
                            print(f"  Template字段: {first_assessment.get('template')}")
                            
                            template = first_assessment.get('template')
                            if template:
                                print(f"  Template描述: {template.get('description')}")
                                print(f"  Template说明: {template.get('instructions')}")
                            else:
                                print(f"  Template字段为空!")
                        else:
                            print("  没有返回评估数据")
                    else:
                        assessments = data_content
                        print(f"返回评估数量: {len(assessments) if isinstance(assessments, list) else 'N/A'}")
                elif isinstance(data, list):
                    print(f"直接返回列表，评估数量: {len(data)}")
                    if data:
                        first_assessment = data[0]
                        template = first_assessment.get('template')
                        if template:
                            print(f"  Template描述: {template.get('description')}")
                            print(f"  Template说明: {template.get('instructions')}")
                        else:
                            print(f"  Template字段为空!")
                else:
                    print(f"响应数据: {data}")
            else:
                print(f"请求失败: {response.text}")
                
        except Exception as e:
            print(f"请求异常: {e}")

def test_backend_template_data():
    """检查后端模板数据"""
    print("\n=== 检查后端模板数据 ===")
    
    db = SessionLocal()
    try:
        # 检查评估模板表
        result = db.execute(text("""
            SELECT id, name, description, instructions 
            FROM assessment_templates 
            WHERE description IS NOT NULL AND instructions IS NOT NULL
            LIMIT 5
        """))
        
        templates = result.fetchall()
        print(f"找到 {len(templates)} 个有描述和说明的模板:")
        for template in templates:
            print(f"  ID: {template[0]}")
            print(f"  名称: {template[1]}")
            print(f"  描述: {template[2][:50]}..." if template[2] else "无")
            print(f"  说明: {template[3][:50]}..." if template[3] else "无")
            print()
            
        # 检查评估表与模板的关联
        result = db.execute(text("""
            SELECT a.id, a.name, a.template_id, t.name as template_name, t.description
            FROM assessments a
            LEFT JOIN assessment_templates t ON a.template_id = t.id
            WHERE a.template_id IS NOT NULL
            LIMIT 5
        """))
        
        assessments_with_templates = result.fetchall()
        print(f"找到 {len(assessments_with_templates)} 个有模板关联的评估:")
        for assessment in assessments_with_templates:
            print(f"  评估ID: {assessment[0]}, 评估名称: {assessment[1]}")
            print(f"  模板ID: {assessment[2]}, 模板名称: {assessment[3]}")
            print(f"  模板描述: {assessment[4][:50]}..." if assessment[4] else "无")
            print()
            
    except Exception as e:
        print(f"数据库查询错误: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    print("开始调试custom_id传递问题...")
    
    test_custom_id_in_database()
    test_backend_template_data()
    
    # 获取认证token
    token, user_custom_id = get_auth_token()
    
    # 测试API调用
    test_api_with_custom_id(token, user_custom_id)
    
    print("\n调试完成!")