<template>
  <div class="medical-records-container">
    <h1>医疗记录管理</h1>
    
    <el-card class="filter-card">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="用户">
          <el-select v-model="filterForm.customId" placeholder="选择用户" clearable>
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="user.username"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="类型">
          <el-select v-model="filterForm.recordType" placeholder="记录类型" clearable>
            <el-option label="门诊记录" value="outpatient" />
            <el-option label="住院记录" value="inpatient" />
            <el-option label="手术记录" value="surgery" />
            <el-option label="入院记录" value="admission" />
            <el-option label="出院小结" value="discharge" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="医院">
          <el-input v-model="filterForm.hospitalName" placeholder="医院名称" clearable />
        </el-form-item>
        
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="fetchRecords">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <el-card class="records-card">
      <template #header>
        <div class="card-header">
          <span>医疗记录列表</span>
          <el-button type="primary" @click="handleCreate">新增记录</el-button>
        </div>
      </template>
      
      <el-table
        v-loading="loading"
        :data="records"
        style="width: 100%"
        border
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="record_type" label="类型" width="120">
          <template #default="scope">
            <el-tag :type="getRecordTypeTag(scope.row.record_type)">
              {{ getRecordTypeLabel(scope.row.record_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="hospital_name" label="医院" width="180" />
        <el-table-column prop="department" label="科室" width="120" />
        <el-table-column prop="doctor_name" label="医生" width="120" />
        <el-table-column prop="visit_date" label="就诊日期" width="180" />
        <el-table-column prop="diagnosis" label="诊断" show-overflow-tooltip />
        <el-table-column prop="is_important" label="重要" width="80">
          <template #default="scope">
            <el-tag v-if="scope.row.is_important" type="danger">重要</el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="handleView(scope.row)">查看</el-button>
            <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 查看/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'view' ? '查看医疗记录' : (dialogType === 'create' ? '新增医疗记录' : '编辑医疗记录')"
      width="60%"
    >
      <el-form
        ref="recordForm"
        :model="currentRecord"
        :rules="formRules"
        label-width="100px"
        :disabled="dialogType === 'view'"
      >
        <el-form-item label="记录类型" prop="record_type">
          <el-select v-model="currentRecord.record_type" placeholder="请选择记录类型">
            <el-option label="门诊记录" value="outpatient" />
            <el-option label="住院记录" value="inpatient" />
            <el-option label="手术记录" value="surgery" />
            <el-option label="入院记录" value="admission" />
            <el-option label="出院小结" value="discharge" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="医院名称" prop="hospital_name">
          <el-input v-model="currentRecord.hospital_name" placeholder="请输入医院名称" />
        </el-form-item>
        
        <el-form-item label="科室">
          <el-input v-model="currentRecord.department" placeholder="请输入科室" />
        </el-form-item>
        
        <el-form-item label="医生姓名">
          <el-input v-model="currentRecord.doctor_name" placeholder="请输入医生姓名" />
        </el-form-item>
        
        <el-form-item label="就诊日期">
          <el-date-picker
            v-model="currentRecord.visit_date"
            type="datetime"
            placeholder="请选择就诊日期"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        
        <el-form-item label="诊断">
          <el-input
            v-model="currentRecord.diagnosis"
            type="textarea"
            :rows="3"
            placeholder="请输入诊断"
          />
        </el-form-item>
        
        <el-form-item label="治疗方案">
          <el-input
            v-model="currentRecord.treatment"
            type="textarea"
            :rows="3"
            placeholder="请输入治疗方案"
          />
        </el-form-item>
        
        <el-form-item label="处方">
          <el-input
            v-model="currentRecord.prescription"
            type="textarea"
            :rows="3"
            placeholder="请输入处方"
          />
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input
            v-model="currentRecord.notes"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
          />
        </el-form-item>
        
        <el-form-item label="重要标记">
          <el-switch v-model="currentRecord.is_important" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveRecord" v-if="dialogType !== 'view'">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from 'axios'

// 用户列表
const users = ref([])

// 记录列表
const records = ref([])
const loading = ref(false)

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 筛选表单
const filterForm = reactive({
  customId: null,
  recordType: '',
  hospitalName: '',
  dateRange: []
})

// 当前记录
const currentRecord = reactive({
  id: null,
  record_type: '',
  hospital_name: '',
  department: '',
  doctor_name: '',
  visit_date: '',
  diagnosis: '',
  treatment: '',
  prescription: '',
  notes: '',
  is_important: false
})

// 对话框
const dialogVisible = ref(false)
const dialogType = ref('view') // view, edit, create
const recordForm = ref(null)

// 表单验证规则
const formRules = {
  record_type: [
    { required: true, message: '请选择记录类型', trigger: 'change' }
  ],
  hospital_name: [
    { required: true, message: '请输入医院名称', trigger: 'blur' }
  ]
}

// 获取用户列表
const fetchUsers = async () => {
  try {
    const response = await axios.get('/api/users')
    users.value = response.data
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  }
}

// 获取医疗记录列表
const fetchRecords = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params = {
      skip: (pagination.currentPage - 1) * pagination.pageSize,
      limit: pagination.pageSize
    }
    
    if (filterForm.customId) {
      params.custom_id = filterForm.customId
    }
    
    if (filterForm.recordType) {
      params.record_type = filterForm.recordType
    }
    
    if (filterForm.hospitalName) {
      params.hospital_name = filterForm.hospitalName
    }
    
    if (filterForm.dateRange && filterForm.dateRange.length === 2) {
      params.start_date = filterForm.dateRange[0]
      params.end_date = filterForm.dateRange[1]
    }
    
    const response = await axios.get(`/api/user-health-records/user/${filterForm.customId}`, {
      params: {
        ...params,
        record_type: 'medical'
      }
    })
    records.value = response.data
    pagination.total = response.data.length // 假设后端返回总数
  } catch (error) {
    console.error('获取医疗记录列表失败:', error)
    ElMessage.error('获取医疗记录列表失败')
  } finally {
    loading.value = false
  }
}

// 重置筛选条件
const resetFilter = () => {
  filterForm.customId = null
  filterForm.recordType = ''
  filterForm.hospitalName = ''
  filterForm.dateRange = []
  fetchRecords()
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pagination.pageSize = size
  fetchRecords()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  pagination.currentPage = page
  fetchRecords()
}

// 获取记录类型标签类型
const getRecordTypeTag = (type) => {
  const typeMap = {
    outpatient: '',
    inpatient: 'success',
    surgery: 'danger',
    admission: 'warning',
    discharge: 'info',
    other: 'info'
  }
  return typeMap[type] || ''
}

// 获取记录类型标签文本
const getRecordTypeLabel = (type) => {
  const typeMap = {
    outpatient: '门诊记录',
    inpatient: '住院记录',
    surgery: '手术记录',
    admission: '入院记录',
    discharge: '出院小结',
    other: '其他'
  }
  return typeMap[type] || type
}

// 查看记录
const handleView = (row) => {
  Object.assign(currentRecord, row)
  dialogType.value = 'view'
  dialogVisible.value = true
}

// 编辑记录
const handleEdit = (row) => {
  Object.assign(currentRecord, row)
  dialogType.value = 'edit'
  dialogVisible.value = true
}

// 新增记录
const handleCreate = () => {
  // 重置表单
  Object.assign(currentRecord, {
    id: null,
    record_type: '',
    hospital_name: '',
    department: '',
    doctor_name: '',
    visit_date: '',
    diagnosis: '',
    treatment: '',
    prescription: '',
    notes: '',
    is_important: false
  })
  dialogType.value = 'create'
  dialogVisible.value = true
}

// 删除记录
const handleDelete = (row) => {
  ElMessageBox.confirm(
    '确定要删除这条医疗记录吗？此操作不可恢复。',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await axios.delete(`/api/user-health-records/user/${currentRecord.custom_id || filterForm.customId}/${row.id}`, {
      params: {
        record_type: 'medical'
      }
    })
      ElMessage.success('删除成功')
      fetchRecords()
    } catch (error) {
      console.error('删除医疗记录失败:', error)
      ElMessage.error('删除医疗记录失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 保存记录
const saveRecord = async () => {
  try {
    await recordForm.value.validate()
    
    if (dialogType.value === 'create') {
      // 创建新记录
      await axios.post(`/api/user-health-records/user/${currentRecord.custom_id || filterForm.customId}`, {
        ...currentRecord,
        record_type: 'medical'
      })
      ElMessage.success('创建成功')
    } else {
      // 更新记录
      await axios.put(`/api/user-health-records/user/${currentRecord.custom_id || filterForm.customId}/${currentRecord.id}`, {
        ...currentRecord,
        record_type: 'medical'
      })
      ElMessage.success('更新成功')
    }
    
    dialogVisible.value = false
    fetchRecords()
  } catch (error) {
    console.error('保存医疗记录失败:', error)
    ElMessage.error('保存医疗记录失败')
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchUsers()
  fetchRecords()
})
</script>

<style scoped>
.medical-records-container {
  padding: 20px;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
