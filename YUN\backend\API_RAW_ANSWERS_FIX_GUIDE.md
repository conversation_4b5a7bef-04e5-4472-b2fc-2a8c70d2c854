
# API层面raw_answers数据同步修复指南

## 问题描述
评估和问卷提交时，raw_answers数据没有正确同步到assessment_results表，导致数据缺失。

## 修复方案

### 1. 在评估提交API中添加raw_answers同步

```python
# 在评估提交的API端点中添加以下代码
from app.utils.raw_answers_sync import ensure_raw_answers_sync, validate_answers_format

@app.post("/api/assessments/submit")
async def submit_assessment(assessment_data: dict, db: Session = Depends(get_db)):
    try:
        # 验证答案格式
        answers = validate_answers_format(assessment_data.get('answers', {}))
        
        # 保存评估回答
        assessment_response = AssessmentResponse(
            custom_id=assessment_data['custom_id'],
            assessment_id=assessment_data['assessment_id'],
            answers=json.dumps(answers),
            # ... 其他字段
        )
        db.add(assessment_response)
        db.commit()
        
        # 确保raw_answers同步到assessment_results
        template_id = assessment_data.get('template_id')
        if template_id:
            ensure_raw_answers_sync(
                db, 
                assessment_data['custom_id'], 
                template_id, 
                answers, 
                'assessment'
            )
        
        return {"status": "success", "message": "评估提交成功"}
        
    except Exception as e:
        db.rollback()
        return {"status": "error", "message": str(e)}
```

### 2. 在问卷提交API中添加raw_answers同步

```python
@app.post("/api/questionnaires/submit")
async def submit_questionnaire(questionnaire_data: dict, db: Session = Depends(get_db)):
    try:
        # 验证答案格式
        answers = validate_answers_format(questionnaire_data.get('answers', {}))
        
        # 保存问卷回答
        questionnaire_response = QuestionnaireResponse(
            custom_id=questionnaire_data['custom_id'],
            questionnaire_id=questionnaire_data['questionnaire_id'],
            answers=json.dumps(answers),
            # ... 其他字段
        )
        db.add(questionnaire_response)
        db.commit()
        
        # 检查是否需要计分和同步raw_answers
        template_id = questionnaire_data.get('template_id')
        if template_id:
            template_config = get_template_scoring_config(template_id, 'questionnaire')
            
            if template_config.get('needs_scoring', False):
                # 计算分数并同步raw_answers
                scores = calculate_scores_with_raw_answers(answers, template_config)
                
                ensure_raw_answers_sync(
                    db, 
                    questionnaire_data['custom_id'], 
                    template_id, 
                    answers, 
                    'questionnaire'
                )
        
        return {"status": "success", "message": "问卷提交成功"}
        
    except Exception as e:
        db.rollback()
        return {"status": "error", "message": str(e)}
```

### 3. 添加数据验证中间件

```python
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

class RawAnswersValidationMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        response = await call_next(request)
        
        # 在评估或问卷提交后验证raw_answers
        if request.url.path in ["/api/assessments/submit", "/api/questionnaires/submit"]:
            # 这里可以添加验证逻辑
            pass
        
        return response
```

### 4. 定期数据同步任务

```python
from celery import Celery

@celery.task
def sync_missing_raw_answers():
    """定期同步缺失的raw_answers数据"""
    
    db = get_db_session()
    
    try:
        # 查找缺失raw_answers的记录
        missing_records = db.query(AssessmentResult).filter(
            AssessmentResult.raw_answers.is_(None)
        ).all()
        
        for record in missing_records:
            # 从对应的response表中恢复数据
            response = db.query(AssessmentResponse).filter(
                AssessmentResponse.custom_id == record.custom_id,
                AssessmentResponse.assessment.has(template_id=record.template_id)
            ).first()
            
            if response and response.answers:
                record.raw_answers = response.answers
                record.updated_at = datetime.now()
        
        db.commit()
        
    except Exception as e:
        db.rollback()
        print(f"同步任务失败: {e}")
    finally:
        db.close()
```

## 实施步骤

1. 将同步函数集成到现有API中
2. 添加数据验证中间件
3. 设置定期同步任务
4. 测试API功能
5. 监控数据完整性

## 注意事项

1. 确保答案格式的一致性
2. 处理不同类型问卷的计分逻辑
3. 保持向后兼容性
4. 添加适当的错误处理
5. 记录操作日志
