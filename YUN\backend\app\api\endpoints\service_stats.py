"""
服务统计API端点 - 提供各服务组件的统计信息
"""
import time
import logging
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Request, Response, status
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from datetime import datetime, timedelta

from app.core.db_connection import get_db_session
from app.core.config import settings
from app.core.auth import get_current_active_user_custom, get_current_user
from app.core.mock_data_manager import (
    is_mock_enabled,
    get_mock_service_stats,
    get_mock_public_metrics,
    get_mock_detailed_service_stats
)
from app.models.user import User
from app.models.enums import UserRole
from app.db.session import get_db

# 如果找不到ServiceStats模型，使用内联定义
try:
    from app.models.service_stats import ServiceStats
except ImportError:
    from sqlalchemy import Column, Integer, Float, DateTime, String
    from app.db.base_session import Base
    
    class ServiceStats(Base):
        """
        服务统计信息表
        """
        __tablename__ = "service_stats"
    
        id = Column(Integer, primary_key=True, index=True, autoincrement=True)
        timestamp = Column(DateTime, default=datetime.utcnow, index=True, nullable=False)
        request_count = Column(Integer, default=0, nullable=False)
        active_users = Column(Integer, default=0, nullable=False)
        avg_response_time = Column(Float, default=0.0, nullable=False)
        error_count = Column(Integer, default=0, nullable=False)
        service_name = Column(String(100), nullable=True)
        endpoint = Column(String(200), nullable=True)
        method = Column(String(10), nullable=True)
        status_code = Column(Integer, nullable=True)
        cpu_usage = Column(Float, nullable=True)
        memory_usage = Column(Float, nullable=True)
        disk_usage = Column(Float, nullable=True)

# 使用函数来延迟导入服务实例，避免循环导入问题
def get_auth_service():
    from app.services.auth_service import auth_service
    return auth_service

def get_db_service():
    from app.services.db_service import db_service
    return db_service

def get_token_manager():
    from app.services.token_manager import token_manager
    return token_manager

# 创建logger实例
logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("")
async def get_all_service_stats(
    current_user: User = Depends(get_current_active_user_custom),
    db: Session = Depends(get_db_session)
):
    """
    获取所有服务组件的统计信息

    需要管理员权限
    """
    # 检查用户权限
    if current_user.role not in [UserRole.ADMIN, UserRole.SUPER_ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限"
        )

    try:
        # 获取各服务组件的统计信息
        auth_service = get_auth_service()
        db_service = get_db_service()
        token_manager = get_token_manager()
        
        # 获取服务统计，并处理可能出现的错误
        try:
            auth_stats = auth_service.get_stats()
        except Exception as e:
            print(f"获取认证服务统计信息失败: {str(e)}")
            auth_stats = {
                "status": "error",
                "message": "获取认证服务统计信息失败",
                "error": str(e)
            }
            
        try:
            db_stats = db_service.get_stats()
        except Exception as e:
            print(f"获取数据库服务统计信息失败: {str(e)}")
            db_stats = {
                "status": "error",
                "message": "获取数据库服务统计信息失败",
                "error": str(e)
            }
            
        try:
            token_stats = token_manager.get_stats()
        except Exception as e:
            print(f"获取令牌管理器统计信息失败: {str(e)}")
            token_stats = {
                "status": "error",
                "message": "获取令牌管理器统计信息失败",
                "error": str(e)
            }

        # 构建响应
        response = {
            "status": "success",
            "timestamp": datetime.utcnow().isoformat(),
            "auth_service": auth_stats,
            "db_service": db_stats,
            "token_manager": token_stats
        }

        return response
    except Exception as e:
        print(f"获取服务统计信息失败: {str(e)}")
        import traceback
        traceback.print_exc()
        
        # 发生错误时，返回错误响应而不是抛出异常
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "status": "error",
                "message": f"获取服务统计信息失败: {str(e)}",
                "timestamp": datetime.utcnow().isoformat()
            }
        )

@router.get("/auth", response_model=Dict[str, Any])
async def get_auth_service_stats(
    current_user: User = Depends(get_current_active_user_custom)
):
    """
    获取认证服务的统计信息

    需要管理员权限
    """
    # 检查用户权限
    if current_user.role not in [UserRole.ADMIN, UserRole.SUPER_ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限"
        )

    try:
        # 获取认证服务的统计信息
        auth_stats = get_auth_service().get_stats()

        # 构建响应
        response = {
            "timestamp": datetime.utcnow().isoformat(),
            "auth_service": auth_stats
        }

        return response
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取认证服务统计信息失败: {str(e)}"
        )

@router.get("/db", response_model=Dict[str, Any])
async def get_db_service_stats(
    current_user: User = Depends(get_current_active_user_custom)
):
    """
    获取数据库服务的统计信息

    需要管理员权限
    """
    # 检查用户权限
    if current_user.role not in [UserRole.ADMIN, UserRole.SUPER_ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限"
        )

    try:
        # 获取数据库服务的统计信息
        db_stats = get_db_service().get_stats()

        # 构建响应
        response = {
            "timestamp": datetime.utcnow().isoformat(),
            "db_service": db_stats
        }

        return response
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取数据库服务统计信息失败: {str(e)}"
        )

@router.get("/token", response_model=Dict[str, Any])
async def get_token_manager_stats(
    current_user: User = Depends(get_current_active_user_custom)
):
    """
    获取令牌管理器的统计信息

    需要管理员权限
    """
    # 检查用户权限
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限"
        )

    try:
        # 获取令牌管理器的统计信息
        token_stats = get_token_manager().get_stats()

        # 构建响应
        response = {
            "timestamp": datetime.utcnow().isoformat(),
            "token_manager": token_stats
        }

        return response
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取令牌管理器统计信息失败: {str(e)}"
        )

@router.get("/public")
async def get_public_service_stats(db: Session = Depends(get_db)) -> Dict[str, Any]:
    """
    获取公开的服务统计信息
    """
    try:
        # 获取最近24小时的统计数据
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(hours=24)
        
        stats = db.query(ServiceStats).filter(
            ServiceStats.timestamp >= start_time,
            ServiceStats.timestamp <= end_time
        ).all()
        
        # 如果没有数据，尝试创建一些基础数据
        if not stats:
            # 尝试获取系统指标并创建一条记录
            try:
                from app.core.health_monitor import health_monitor
                import psutil
                
                # 获取基本指标
                metrics = health_monitor.get_basic_metrics()
                
                # 创建一条服务统计记录
                new_stat = ServiceStats(
                    timestamp=datetime.utcnow(),
                    request_count=metrics.get("application", {}).get("requests", {}).get("total", 0),
                    active_users=5,  # 默认值
                    avg_response_time=metrics.get("application", {}).get("avg_response_time", 0),
                    error_count=metrics.get("application", {}).get("requests", {}).get("error", 0),
                    service_name="backend",
                    cpu_usage=metrics.get("system", {}).get("cpu_usage", 0),
                    memory_usage=metrics.get("system", {}).get("memory_usage", 0),
                    disk_usage=metrics.get("system", {}).get("disk_percent", 0)
                )
                
                db.add(new_stat)
                db.commit()
                
                # 重新查询
                stats = [new_stat]
                logger.info("创建了初始服务统计记录")
            except Exception as e:
                logger.error(f"创建初始服务统计记录失败: {str(e)}")
        
        # 计算统计信息
        total_requests = sum(stat.request_count for stat in stats) if stats else 0
        total_users = sum(stat.active_users for stat in stats) if stats else 0
        avg_response_time = sum(stat.avg_response_time for stat in stats) / len(stats) if stats else 0
        
        return {
            "status": "success",
            "data": {
                "total_requests": total_requests,
                "total_users": total_users,
                "avg_response_time": round(avg_response_time, 2),
                "timestamp": datetime.utcnow().isoformat(),
                "is_real_data": True
            }
        }
    except Exception as e:
        logger.error(f"获取服务统计信息失败: {str(e)}")
        # 如果发生错误，返回模拟数据
        mock_data = get_mock_public_metrics()
        if mock_data:
            return mock_data
        import random
        return {
            "status": "success",
            "data": {
                "total_requests": random.randint(1000, 5000),
                "total_users": random.randint(50, 200),
                "avg_response_time": round(random.uniform(50, 500), 2),
                "timestamp": datetime.utcnow().isoformat(),
                "note": "模拟数据 (原因: 数据库查询失败)"
            }
        }

@router.get("/metrics-public")
async def get_public_metrics(db: Session = Depends(get_db)) -> Dict[str, Any]:
    """
    获取公开的性能指标数据
    """
    try:
        # 获取最近24小时的统计数据
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(hours=24)
        
        stats = db.query(ServiceStats).filter(
            ServiceStats.timestamp >= start_time,
            ServiceStats.timestamp <= end_time
        ).all()
        
        # 如果没有数据，返回模拟数据
        if not stats:
            mock_data = get_mock_public_metrics()
            if mock_data:
                return mock_data
            import random
            return {
                "status": "success",
                "data": {
                    "cpu_usage": round(random.uniform(10, 80), 1),
                    "memory_usage": round(random.uniform(30, 90), 1),
                    "disk_usage": round(random.uniform(20, 70), 1),
                    "request_rate": round(random.uniform(10, 100), 1),
                    "error_rate": round(random.uniform(0.1, 5), 2),
                    "response_time": round(random.uniform(50, 500), 1),
                    "timestamp": datetime.utcnow().isoformat(),
                    "note": "模拟数据 (原因: 无统计数据)"
                }
            }
        
        # 计算性能指标
        latest_stats = sorted(stats, key=lambda x: x.timestamp, reverse=True)[:5]
        
        avg_cpu = sum(stat.cpu_usage for stat in latest_stats if stat.cpu_usage is not None) / len([stat for stat in latest_stats if stat.cpu_usage is not None]) if any(stat.cpu_usage is not None for stat in latest_stats) else 0
        avg_memory = sum(stat.memory_usage for stat in latest_stats if stat.memory_usage is not None) / len([stat for stat in latest_stats if stat.memory_usage is not None]) if any(stat.memory_usage is not None for stat in latest_stats) else 0
        avg_disk = sum(stat.disk_usage for stat in latest_stats if stat.disk_usage is not None) / len([stat for stat in latest_stats if stat.disk_usage is not None]) if any(stat.disk_usage is not None for stat in latest_stats) else 0
        
        total_requests = sum(stat.request_count for stat in latest_stats)
        total_errors = sum(stat.error_count for stat in latest_stats)
        error_rate = (total_errors / total_requests * 100) if total_requests > 0 else 0
        avg_response_time = sum(stat.avg_response_time for stat in latest_stats) / len(latest_stats) if latest_stats else 0
        
        # 计算每分钟请求率
        time_span = (latest_stats[0].timestamp - latest_stats[-1].timestamp).total_seconds() / 60 if len(latest_stats) > 1 else 1
        request_rate = total_requests / time_span if time_span > 0 else total_requests
        
        return {
            "status": "success",
            "data": {
                "cpu_usage": round(avg_cpu, 1),
                "memory_usage": round(avg_memory, 1),
                "disk_usage": round(avg_disk, 1),
                "request_rate": round(request_rate, 1),
                "error_rate": round(error_rate, 2),
                "response_time": round(avg_response_time, 1),
                "timestamp": datetime.utcnow().isoformat()
            }
        }
    except Exception as e:
        # 如果发生错误，返回模拟数据
        mock_data = get_mock_public_metrics()
        if mock_data:
            return mock_data
        import random
        return {
            "status": "success",
            "data": {
                "cpu_usage": round(random.uniform(10, 80), 1),
                "memory_usage": round(random.uniform(30, 90), 1),
                "disk_usage": round(random.uniform(20, 70), 1),
                "request_rate": round(random.uniform(10, 100), 1),
                "error_rate": round(random.uniform(0.1, 5), 2),
                "response_time": round(random.uniform(50, 500), 1),
                "timestamp": datetime.utcnow().isoformat(),
                "note": "模拟数据 (原因: 数据库查询失败)"
            }
        }

@router.get("")
async def get_service_stats(
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    获取详细的服务统计信息（需要认证）
    """
    try:
        # 获取最近24小时的统计数据
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(hours=24)
        
        stats = db.query(ServiceStats).filter(
            ServiceStats.timestamp >= start_time,
            ServiceStats.timestamp <= end_time
        ).all()
        
        # 计算详细统计信息
        total_requests = sum(stat.request_count for stat in stats) if stats else 0
        total_users = sum(stat.active_users for stat in stats) if stats else 0
        avg_response_time = sum(stat.avg_response_time for stat in stats) / len(stats) if stats else 0
        error_count = sum(stat.error_count for stat in stats) if stats else 0
        
        # 检查用户角色，如果是管理员则返回更详细的信息
        is_admin = hasattr(current_user, 'role') and current_user.role == UserRole.ADMIN
        
        response = {
            "status": "success",
            "data": {
                "total_requests": total_requests,
                "total_users": total_users,
                "avg_response_time": round(avg_response_time, 2),
                "error_count": error_count,
                "error_rate": round(error_count / total_requests * 100, 2) if total_requests > 0 else 0,
                "timestamp": datetime.utcnow().isoformat(),
            }
        }
        
        # 管理员可以看到更详细的信息
        if is_admin:
            response["data"]["detailed_stats"] = [
                {
                    "timestamp": stat.timestamp.isoformat(),
                    "request_count": stat.request_count,
                    "active_users": stat.active_users,
                    "avg_response_time": stat.avg_response_time,
                    "error_count": stat.error_count,
                    "cpu_usage": stat.cpu_usage,
                    "memory_usage": stat.memory_usage,
                    "disk_usage": stat.disk_usage
                }
                for stat in stats
            ]
        
        return response
    except Exception as e:
        # 如果发生错误，返回模拟数据
        mock_data = get_mock_detailed_service_stats()
        if mock_data:
            return mock_data
        import random
        return {
            "status": "success",
            "data": {
                "total_requests": random.randint(1000, 5000),
                "total_users": random.randint(50, 200),
                "avg_response_time": round(random.uniform(50, 500), 2),
                "error_count": random.randint(10, 100),
                "error_rate": round(random.uniform(1, 5), 2),
                "timestamp": datetime.utcnow().isoformat(),
                "note": "模拟数据 (原因: 数据库查询失败)"
            }
        }

def get_mock_service_stats() -> Dict[str, Any]:
    """
    获取模拟的服务统计信息
    """
    import random
    from datetime import datetime

    return {
        "timestamp": datetime.utcnow().isoformat(),
        "auth_service": {
            "login_attempts": random.randint(1000, 2000),
            "login_success": random.randint(800, 1500),
            "login_failed": random.randint(50, 200)
        },
        "db_service": {
            "connections_successful": random.randint(5000, 10000),
            "connections_failed": random.randint(10, 50),
            "connection_errors": random.randint(5, 20)
        },
        "token_manager": {
            "active_tokens": random.randint(10, 50),
            "active_users": random.randint(5, 20)
        }
    }
