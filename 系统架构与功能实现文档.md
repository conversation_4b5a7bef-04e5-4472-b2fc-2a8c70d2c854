# 健康管理系统架构与功能实现文档（最新版）

## 📋 目录
- [系统总体架构](#系统总体架构)
- [移动端架构](#移动端架构)
- [后端服务架构](#后端服务架构)
- [前端架构](#前端架构)
- [数据库设计](#数据库设计)
- [API接口设计](#api接口设计)
- [系统流程图](#系统流程图)
- [部署架构](#部署架构)
- [优化建议](#优化建议)
- [下一步工作指导](#下一步工作指导)

---

## 🏗️ 系统总体架构

```mermaid
graph TB
    subgraph "移动端 (Kivy + KivyMD 2.0.1)"
        MA[移动应用]
        MS[屏幕管理器]
        MU[工具类]
        MD[本地数据库]
    end
    
    subgraph "前端 (Vue3 + Element Plus)"
        FE[前端应用]
        FR[路由管理]
        FS[状态管理 Pinia]
        FC[组件库]
    end
    
    subgraph "后端 (FastAPI)"
        BE[FastAPI应用]
        AR[API路由]
        AU[认证模块]
        DB[数据库服务]
    end
    
    subgraph "数据层"
        SQL[SQLite主数据库]
        CACHE[Redis缓存]
        FILES[文件存储]
    end
    
    MA --> |HTTP/HTTPS| BE
    FE --> |HTTP/HTTPS| BE
    BE --> SQL
    BE --> CACHE
    BE --> FILES
    
    MA --> MD
```

### 系统组件说明

| 组件 | 技术栈 | 端口 | 功能描述 |
|------|--------|------|----------|
| 移动端 | Kivy + KivyMD 2.0.1 dev0 | - | 移动应用客户端 |
| 前端 | Vue3 + Element Plus | 8080 | Web管理界面 |
| 后端 | FastAPI + SQLAlchemy | 8006 | API服务器 |
| 数据库 | SQLite3 | - | 主数据存储 |
| 缓存 | Redis (备用) | 6379 | 缓存服务 |

---

## 📱 移动端架构

### 技术栈
- **框架**: Kivy + KivyMD 2.0.1 dev0
- **语言**: Python 3.13
- **本地存储**: SQLite3
- **网络**: HTTP/HTTPS

### 目录结构
```
mobile/
├── main.py                 # 应用入口
├── screens/                # 屏幕模块
│   ├── login_screen.py     # 登录界面
│   ├── homepage_screen.py  # 主页
│   ├── profile_page.py     # 个人资料
│   ├── assessment_screen.py # 评估量表
│   └── ...
├── utils/                  # 工具类
│   ├── cloud_api.py        # 云端API客户端
│   ├── database.py         # 数据库管理
│   ├── db_models.py        # 数据模型
│   └── ...
├── api/                    # API模块
│   ├── api_client.py       # API客户端
│   └── user_api.py         # 用户API
├── data/                   # 本地数据
└── assets/                 # 资源文件
```

### 核心功能模块

#### 1. 屏幕管理系统
```python
# 主要屏幕组件
Screens = {
    'login': LoginScreen,
    'homepage': HomepageScreen,
    'profile': ProfilePage,
    'assessment': AssessmentScreen,
    'health_overview': HealthOverviewScreen,
    'questionnaire': QuestionnaireFormScreen
}
```

#### 2. 数据同步机制
```mermaid
sequenceDiagram
    participant M as 移动端
    participant L as 本地数据库
    participant C as 云端API
    
    M->>L: 保存本地数据
    M->>C: 同步到云端
    C-->>M: 返回同步结果
    M->>L: 更新同步状态
```

#### 3. API端点映射
| 功能 | 本地方法 | 云端API |
|------|----------|----------|
| 用户登录 | `login()` | `POST /api/auth/mobile_login` |
| 获取评估 | `get_assessments()` | `GET /api/mobile/assessments` |
| 提交问卷 | `submit_questionnaire()` | `POST /api/mobile/questionnaires` |
| 健康记录 | `get_health_records()` | `GET /api/health_records` |

---

## 🖥️ 后端服务架构

### 技术栈
- **框架**: FastAPI 0.104.1
- **ORM**: SQLAlchemy
- **数据库**: SQLite3
- **认证**: JWT
- **文档**: Swagger/OpenAPI

### API路由结构
```
api/
├── auth.py                 # 认证相关
├── users.py                # 用户管理
├── health_records.py       # 健康记录
├── medical_records.py      # 医疗记录
├── lab_reports.py          # 化验报告
├── assessments.py          # 评估量表
├── questionnaires.py       # 问卷调查
├── examination_reports.py  # 检查报告
├── imaging_reports.py      # 影像报告
├── follow_up_records.py    # 随访记录
├── health_diaries.py       # 健康日记
├── alerts.py               # 告警系统
├── assessment_results.py   # 评估结果
├── questionnaire_results.py # 问卷结果
├── mobile_api_new.py       # 移动端专用API
├── clinical_scales.py      # 临床量表
├── templates.py            # 模板管理
├── monitoring.py           # 系统监控
└── dashboard.py            # 仪表盘
```

### 核心API端点

#### 认证模块 (`/api/auth`)
```python
POST /api/auth/login          # 标准登录
POST /api/auth/mobile_login   # 移动端登录
POST /api/auth/frontend_login # 前端登录
POST /api/auth/refresh        # 刷新令牌
POST /api/auth/logout         # 登出
```

#### 移动端API (`/api/mobile`)
```python
GET  /api/mobile/assessments           # 获取评估列表
POST /api/mobile/assessments          # 创建评估
GET  /api/mobile/assessments/{id}     # 获取评估详情
POST /api/mobile/assessments/{id}/submit # 提交评估

GET  /api/mobile/questionnaires       # 获取问卷列表
POST /api/mobile/questionnaires       # 创建问卷
POST /api/mobile/questionnaires/{id}/submit # 提交问卷

GET  /api/mobile/test                 # API测试端点
```

#### 用户管理 (`/api/users`)
```python
GET    /api/users                     # 获取用户列表
POST   /api/users                     # 创建用户
GET    /api/users/{id}                # 获取用户详情
PUT    /api/users/{id}                # 更新用户
DELETE /api/users/{id}                # 删除用户
GET    /api/users/profile             # 获取当前用户资料
PUT    /api/users/profile             # 更新当前用户资料
```

#### 健康记录 (`/api/health_records`)
```python
GET    /api/health_records            # 获取健康记录列表
POST   /api/health_records           # 创建健康记录
GET    /api/health_records/{id}      # 获取健康记录详情
PUT    /api/health_records/{id}      # 更新健康记录
DELETE /api/health_records/{id}      # 删除健康记录
```

### 数据库模型

#### 核心模型关系
```mermaid
erDiagram
    User ||--o{ Assessment : has
    User ||--o{ HealthRecord : has
    User ||--o{ MedicalRecord : has
    User ||--o{ LabReport : has
    
    Assessment ||--|| AssessmentTemplate : uses
    Assessment ||--o{ AssessmentItem : contains
    
    AssessmentTemplate ||--o{ AssessmentTemplateQuestion : contains
    AssessmentDistribution ||--|| User : targets
    AssessmentDistribution ||--|| AssessmentTemplate : distributes
    
    User {
        int id PK
        string username
        string email
        string custom_id
        string role
        datetime created_at
    }
    
    Assessment {
        int id PK
        string custom_id FK
        string template_id
        string name
        string status
        float score
        datetime created_at
    }
    
    HealthRecord {
        int id PK
        string custom_id FK
        string record_type
        json data
        datetime created_at
    }
```

---

## 🌐 前端架构

### 技术栈
- **框架**: Vue 3.2.47
- **UI库**: Element Plus 2.3.4
- **路由**: Vue Router 4.1.6
- **状态管理**: Pinia 2.0.35
- **构建工具**: Vite 5.3.1
- **图表**: ECharts 5.6.0

### 项目结构
```
frontend/src/
├── App.vue                 # 根组件
├── main.js                 # 入口文件
├── router/                 # 路由配置
│   ├── index.js            # 主路由
│   └── security.js         # 安全路由
├── store/                  # 状态管理
│   └── user.js             # 用户状态
├── views/                  # 页面组件
│   ├── Layout.vue          # 布局组件
│   ├── Login.vue           # 登录页
│   ├── HomePage.vue        # 首页
│   ├── Assessments.vue     # 评估管理
│   └── ...
├── components/             # 通用组件
│   ├── dashboard/          # 仪表盘组件
│   ├── charts/             # 图表组件
│   ├── assessment/         # 评估组件
│   └── ...
└── services/               # 服务层
    └── api.js              # API服务
```

### 路由架构
```javascript
const routes = [
  {
    path: '/',
    redirect: '/admin/dashboard'
  },
  {
    path: '/login',
    component: Login
  },
  {
    path: '/admin',
    component: Layout,
    children: [
      { path: 'dashboard', component: HomePage },
      { path: 'users', component: UserManagement },
      { path: 'assessments', component: Assessments },
      { path: 'health-records', component: HealthRecords },
      { path: 'templates', component: TemplateManagement },
      { path: 'security/alerts', component: SystemAlerts }
    ]
  }
]
```

### 状态管理
```javascript
// 用户状态管理
export const useUserStore = defineStore('user', {
  state: () => ({
    user: null,
    token: localStorage.getItem('token'),
    isLoggedIn: false,
    permissions: []
  }),
  
  actions: {
    async login(credentials) { /* ... */ },
    async logout() { /* ... */ },
    async fetchProfile() { /* ... */ }
  }
})
```

---

## 🗄️ 数据库设计

### 主数据库 (SQLite)
**位置**: `YUN/backend/app.db`
**总表数**: 43个数据表

#### 数据表分类与功能分析

### 📊 数据表功能分析与API映射

#### 🔐 用户认证与权限管理 (4张表)

##### 1. users - 用户基础信息表
**作用**: 存储用户基本信息、认证凭据和角色权限
**相关API**:
- `GET /api/users` - 获取用户列表
- `POST /api/users` - 创建用户
- `GET /api/users/me` - 获取当前用户信息
- `PUT /api/users/me` - 更新用户信息
- `GET /api/users/mobile-profile/{custom_id}` - 移动端获取用户资料
- `PUT /api/users/mobile-profile/{custom_id}` - 移动端更新用户资料

**调用路线图**:
```mermaid
flowchart LR
    A[移动端/前端] --> B[用户认证API]
    B --> C[users表]
    C --> D[返回用户信息]
    D --> E[JWT令牌生成]
```

##### 2. role_applications - 角色申请表
**作用**: 管理用户角色变更申请流程
**相关API**:
- `POST /api/role-applications/apply` - 提交角色申请
- `GET /api/role-applications/my-applications` - 查看我的申请
- `GET /api/role-applications/applications` - 管理员查看所有申请
- `POST /api/role-applications/applications/{id}/process` - 处理申请

**使用状态**: ✅ 活跃使用

##### 3. notifications - 通知消息表
**作用**: 系统通知和消息推送
**相关API**:
- `GET /api/mobile/notifications` - 移动端获取通知
- `PUT /api/mobile/notifications/{id}/read` - 标记通知已读

**使用状态**: ✅ 活跃使用

##### 4. operation_logs - 操作日志表
**作用**: 记录系统操作日志，用于审计和安全监控
**相关API**: 暂无直接API，后台记录
**使用状态**: ✅ 活跃使用

#### 📋 评估量表系统 (6张表)

##### 5. assessments - 评估实例表
**作用**: 存储用户的评估量表实例和完成状态
**相关API**:
- `GET /api/mobile/assessments` - 移动端获取评估列表
- `POST /api/mobile/assessments/{id}/submit` - 提交评估答案
- `GET /api/mobile/pending-assessments` - 获取待完成评估
- `GET /api/mobile/history-assessments` - 获取历史评估
- `GET /api/mobile/completed-assessments` - 获取已完成评估

##### 6. assessment_items - 评估题目表
**作用**: 存储评估量表的具体题目和答案
**相关API**: 通过assessments API间接调用

##### 7. assessment_responses - 评估回答记录表
**作用**: 记录用户对评估量表的完整回答
**相关API**: 通过assessments API间接调用

##### 8. assessment_templates - 评估模板表
**作用**: 定义标准评估量表模板
**相关API**:
- `GET /api/templates/assessment-templates` - 获取模板列表
- `POST /api/templates/assessment-templates` - 创建模板
- `POST /api/templates/assessment-templates/{id}/distribute` - 分发模板

##### 9. assessment_template_questions - 评估模板题目表
**作用**: 存储评估模板的标准题目
**相关API**: 通过templates API间接调用

##### 10. assessment_distributions - 评估分发记录表
**作用**: 记录评估量表的分发历史和状态
**相关API**: 通过templates distribute API间接调用

**调用路线图**:
```mermaid
flowchart TD
    A[管理员创建模板] --> B[assessment_templates]
    B --> C[分发给用户]
    C --> D[assessment_distributions]
    D --> E[创建用户实例]
    E --> F[assessments]
    F --> G[用户答题]
    G --> H[assessment_items]
    H --> I[提交答案]
    I --> J[assessment_responses]
```

#### 📝 问卷调查系统 (7张表)

##### 11. questionnaires - 问卷实例表
**作用**: 存储用户的问卷调查实例
**相关API**:
- `GET /api/questionnaires` - 获取问卷列表
- `POST /api/questionnaires` - 创建问卷
- `GET /api/mobile/questionnaires` - 移动端获取问卷
- `POST /api/mobile/questionnaires/{id}/submit` - 提交问卷

##### 12. questionnaire_items - 问卷题目表
**作用**: 存储问卷的具体题目
**相关API**: 通过questionnaires API间接调用

##### 13. questionnaire_responses - 问卷回答记录表
**作用**: 记录用户对问卷的完整回答
**相关API**: 通过questionnaires API间接调用

##### 14. questionnaire_answers - 问卷答案表
**作用**: 存储问卷的详细答案数据
**相关API**: 通过questionnaires API间接调用

##### 15. questionnaire_templates - 问卷模板表
**作用**: 定义标准问卷模板
**相关API**:
- `GET /api/templates/questionnaire-templates` - 获取模板列表
- `POST /api/templates/questionnaire-templates` - 创建模板
- `POST /api/templates/questionnaire-templates/{id}/distribute` - 分发模板

##### 16. questionnaire_template_questions - 问卷模板题目表
**作用**: 存储问卷模板的标准题目
**相关API**: 通过templates API间接调用

##### 17. questionnaire_distributions - 问卷分发记录表
**作用**: 记录问卷的分发历史和状态
**相关API**: 通过templates distribute API间接调用

##### 18. questionnaire_instances - 问卷实例管理表 (已整合)
**作用**: 原用于管理问卷的实例化过程，现已整合到questionnaires表中
**相关API**: 功能已合并到questionnaires API
**使用状态**: ✅ 已整合到questionnaires表，通过template_id字段关联模板

#### 🏥 医疗健康记录 (12张表)

##### 19. health_records - 健康记录表
**作用**: 存储用户的基础健康记录
**相关API**:
- `GET /api/health_records` - 获取健康记录
- `POST /api/health_records` - 创建健康记录
- `GET /api/mobile/health-records` - 移动端获取健康记录

##### 20. health_overviews - 健康概览表
**作用**: 存储用户的健康状况概览信息
**相关API**:
- `GET /api/health_records/overview` - 获取健康概览
- `POST /api/health_records/overview` - 创建健康概览

##### 21. medical_records - 医疗记录表
**作用**: 存储用户的医疗就诊记录
**相关API**:
- `GET /api/medical_records` - 获取医疗记录
- `POST /api/medical_records` - 创建医疗记录
- `GET /api/mobile/medical-records` - 移动端获取医疗记录

##### 22. inpatient_records - 住院记录表
**作用**: 存储用户的住院治疗记录
**相关API**:
- `GET /api/medical_records/inpatient` - 获取住院记录
- `POST /api/medical_records/inpatient` - 创建住院记录

##### 23. surgery_records - 手术记录表
**作用**: 存储用户的手术治疗记录
**相关API**:
- `GET /api/medical_records/surgery` - 获取手术记录
- `POST /api/medical_records/surgery` - 创建手术记录

##### 24. lab_reports - 化验报告表
**作用**: 存储用户的实验室检查报告
**相关API**:
- `GET /api/lab_reports` - 获取化验报告
- `POST /api/lab_reports` - 创建化验报告
- `GET /api/mobile/lab-reports` - 移动端获取化验报告

##### 25. lab_report_items - 化验报告项目表
**作用**: 存储化验报告的详细检查项目
**相关API**: 通过lab_reports API间接调用

##### 26. examination_reports - 检查报告表
**作用**: 存储用户的医学检查报告
**相关API**:
- `GET /api/examination_reports/user/{custom_id}` - 获取指定用户检查报告列表
- `POST /api/examination_reports` - 创建检查报告
- `GET /api/examination_reports/{id}` - 获取单个检查报告详情
- `PUT /api/examination_reports/{id}` - 更新检查报告
- `DELETE /api/examination_reports/{id}` - 删除检查报告
**使用状态**: ✅ 已完善API端点

##### 27. imaging_reports - 影像报告表
**作用**: 存储用户的医学影像检查报告
**相关API**:
- `GET /api/imaging_reports/user/{custom_id}` - 获取指定用户影像报告列表
- `POST /api/imaging_reports` - 创建影像报告
- `GET /api/imaging_reports/{id}` - 获取单个影像报告详情
- `PUT /api/imaging_reports/{id}` - 更新影像报告
- `DELETE /api/imaging_reports/{id}` - 删除影像报告
**使用状态**: ✅ 已完善API端点

##### 28. follow_up_records - 随访记录表
**作用**: 存储用户的医疗随访记录
**相关API**:
- `GET /api/follow_up_records/user/{custom_id}` - 获取指定用户随访记录列表
- `POST /api/follow_up_records` - 创建随访记录
- `GET /api/follow_up_records/{id}` - 获取单个随访记录详情
- `PUT /api/follow_up_records/{id}` - 更新随访记录
- `DELETE /api/follow_up_records/{id}` - 删除随访记录
**使用状态**: ✅ 已完善API端点

##### 29. health_diaries - 健康日记表
**作用**: 存储用户的日常健康记录
**相关API**:
- `GET /api/health_diaries/user/{custom_id}` - 获取指定用户健康日记列表
- `POST /api/health_diaries` - 创建健康日记
- `GET /api/health_diaries/{id}` - 获取单个健康日记详情
- `PUT /api/health_diaries/{id}` - 更新健康日记
- `DELETE /api/health_diaries/{id}` - 删除健康日记
**使用状态**: ✅ 已完善API端点

##### 30. other_records - 其他记录表
**作用**: 存储不属于其他分类的健康相关记录
**相关API**:
- `GET /api/other_records/user/{custom_id}` - 获取其他记录
- `POST /api/other_records` - 创建其他记录

#### 🏥 医院业务记录 (4张表)

##### 31. registration_records - 挂号记录表
**作用**: 存储医院挂号信息
**相关API**:
- `GET /api/hospital_records/registration` - 获取挂号记录
- `POST /api/hospital_records/registration` - 创建挂号记录

##### 32. prescription_records - 处方记录表
**作用**: 存储医生开具的处方信息
**相关API**:
- `GET /api/hospital_records/prescription` - 获取处方记录
- `POST /api/hospital_records/prescription` - 创建处方记录

##### 33. laboratory_records - 实验室记录表
**作用**: 存储实验室检查的详细记录
**相关API**:
- `GET /api/hospital_records/laboratory` - 获取实验室记录
- `POST /api/hospital_records/laboratory` - 创建实验室记录

##### 34. imaging_records - 影像记录表
**作用**: 存储医学影像检查的记录
**相关API**:
- `GET /api/hospital_records/imaging` - 获取影像记录
- `POST /api/hospital_records/imaging` - 创建影像记录

#### 💊 药物管理 (2张表)

##### 35. medications - 药物信息表
**作用**: 存储药物的基本信息
**相关API**:
- `GET /api/health_records/medications` - 获取药物列表
- `POST /api/health_records/medications` - 添加药物

##### 36. medication_usages - 用药记录表
**作用**: 存储用户的用药历史和使用情况
**相关API**:
- `GET /api/health_records/medications/{id}/usage` - 获取用药记录
- `POST /api/health_records/medications/{id}/usage` - 记录用药

#### 📊 结果管理 (3张表)

##### 37. assessment_results - 评估结果表
**作用**: 存储评估量表的计算结果和报告
**相关API**:
- `GET /api/assessment_results/user/{custom_id}` - 获取指定用户评估结果列表
- `POST /api/assessment_results` - 创建评估结果
- `GET /api/assessment_results/{id}` - 获取单个评估结果详情
- `PUT /api/assessment_results/{id}` - 更新评估结果
- `DELETE /api/assessment_results/{id}` - 删除评估结果
- `POST /api/assessment_results/{id}/generate-report` - 生成评估报告
**使用状态**: ✅ 新增功能，支持结果存储和报告生成

##### 38. questionnaire_results - 问卷结果表
**作用**: 存储问卷调查的计算结果和报告
**相关API**:
- `GET /api/questionnaire_results/user/{custom_id}` - 获取指定用户问卷结果列表
- `POST /api/questionnaire_results` - 创建问卷结果
- `GET /api/questionnaire_results/{id}` - 获取单个问卷结果详情
- `PUT /api/questionnaire_results/{id}` - 更新问卷结果
- `DELETE /api/questionnaire_results/{id}` - 删除问卷结果
- `POST /api/questionnaire_results/{id}/generate-report` - 生成问卷报告
- `POST /api/questionnaire_results/calculate-from-response/{response_id}` - 根据问卷回复计算结果
**使用状态**: ✅ 新增功能，支持结果存储和报告生成

##### 39. report_templates - 报告模板表
**作用**: 存储评估和问卷的报告模板
**相关API**: 通过结果API间接调用
**使用状态**: ✅ 新增功能，支持报告模板管理

#### 📄 文档管理 (1张表)

##### 40. documents - 文档表
**作用**: 存储用户上传的各类文档和文件
**相关API**:
- `POST /api/documents/upload` - 上传文档
- `GET /api/documents` - 获取文档列表
- `POST /api/documents/{id}/ocr` - OCR文字识别

#### 🔔 系统监控 (2张表)

##### 41. service_stats - 服务统计表
**作用**: 记录系统服务的使用统计和性能指标
**相关API**:
- `GET /api/service_stats` - 获取服务统计
- `GET /api/metrics` - 获取系统指标

##### 42. alerts - 告警表
**作用**: 存储系统告警和异常信息
**相关API**:
- `GET /api/alerts` - 获取告警列表
- `POST /api/alerts/resolve/{alert_id}` - 解决告警
- `GET /api/alerts/rules` - 获取告警规则
- `GET /api/alerts/user/{custom_id}` - 获取指定用户告警记录
- `POST /api/alerts` - 创建告警记录
- `GET /api/alerts/{id}` - 获取单个告警详情
- `PUT /api/alerts/{id}` - 更新告警记录
- `DELETE /api/alerts/{id}` - 删除告警记录
- `POST /api/alerts/rules` - 创建告警规则
**使用状态**: ✅ 已完善API端点

#### 🔧 系统辅助表 (1张表)

##### 43. sqlite_sequence - SQLite序列表
**作用**: SQLite内部使用的自增序列管理
**相关API**: 无，系统内部表
**使用状态**: ✅ 系统必需

### 📈 数据表使用状况分析

#### ✅ 高频使用表 (核心业务)
- `users` - 用户认证和管理
- `assessments` - 评估量表核心功能
- `questionnaires` - 问卷调查核心功能
- `health_records` - 健康记录管理
- `assessment_templates` - 模板管理
- `questionnaire_templates` - 问卷模板
- `documents` - 文档管理

#### ⚠️ 中频使用表 (辅助功能)
- `medical_records` - 医疗记录
- `lab_reports` - 化验报告
- `notifications` - 通知系统
- `role_applications` - 角色申请
- `medications` - 药物管理

#### ❌ 低频/未使用表 (可考虑优化)
- `examination_reports` - 检查报告 (缺少API)
- `imaging_reports` - 影像报告 (缺少API)
- `follow_up_records` - 随访记录 (缺少API)
- `health_diaries` - 健康日记 (缺少API)
- `alerts` - 告警系统 (缺少API)
- `questionnaire_instances` - 与questionnaires功能重叠

### 🔄 数据表关系图
```mermaid
erDiagram
    users ||--o{ assessments : "custom_id"
    users ||--o{ questionnaires : "custom_id"
    users ||--o{ health_records : "custom_id"
    users ||--o{ medical_records : "custom_id"
    users ||--o{ lab_reports : "custom_id"
    users ||--o{ notifications : "custom_id"
    
    assessment_templates ||--o{ assessments : "template_id"
    assessment_templates ||--o{ assessment_distributions : "template_id"
    assessments ||--o{ assessment_items : "assessment_id"
    assessments ||--o{ assessment_responses : "assessment_id"
    
    questionnaire_templates ||--o{ questionnaires : "template_id"
    questionnaire_templates ||--o{ questionnaire_distributions : "template_id"
    questionnaires ||--o{ questionnaire_items : "questionnaire_id"
    questionnaires ||--o{ questionnaire_responses : "questionnaire_id"
    
    lab_reports ||--o{ lab_report_items : "report_id"
    medications ||--o{ medication_usages : "medication_id"
```

### 🗑️ 优化建议

#### ✅ 已完成的优化
1. **questionnaire_instances** - ✅ 已整合到questionnaires表中，通过template_id字段关联模板
2. **examination_reports** - ✅ 已创建完整的CRUD API端点
3. **imaging_reports** - ✅ 已创建完整的CRUD API端点
4. **follow_up_records** - ✅ 已创建完整的CRUD API端点
5. **health_diaries** - ✅ 已创建完整的CRUD API端点
6. **alerts** - ✅ 已创建完整的告警系统API端点
7. **评估和问卷结果存储** - ✅ 已创建assessment_results和questionnaire_results表及API

#### 🔄 数据表关系优化
- **questionnaires表增强**: 添加了template_id字段，支持基于模板创建问卷
- **新增结果管理**: 创建了专门的结果存储表，支持计算结果和报告生成
- **API完整性**: 所有主要数据表都已具备完整的CRUD API端点

#### 📊 量表和问卷结果处理流程
```mermaid
flowchart TD
    A[移动端提交] --> B[后端计算]
    B --> C[保存到结果表]
    C --> D[生成报告]
    D --> E[返回结果]
    
    B --> F[AssessmentResult表]
    B --> G[QuestionnaireResult表]
    F --> H[评估报告生成]
    G --> I[问卷报告生成]
```

#### 建议整合的表
- `imaging_reports` 和 `imaging_records` 功能相似，可考虑整合
- `examination_reports` 可整合到 `medical_records` 中

### 移动端本地数据库
**位置**: `mobile/data/`

#### 本地数据表结构
```python
# 移动端数据表分类
TABLE_CATEGORIES = {
    'user': '用户信息',
    'personal': '个人基本信息',
    'health': '健康基本信息',
    'hospital': '住院记录',
    'outpatient': '门诊记录',
    'lab': '实验室化验报告',
    'exam': '技诊检查报告',
    'questionnaire': '调查问卷',
    'assessment': '评估量表',
    'health_log': '健康日志',
    'medication': '药物使用信息',
    'wearable': '可穿戴设备数据'
}
```

---

## 🔄 系统流程图

### 用户登录流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant M as 移动端/前端
    participant B as 后端API
    participant D as 数据库
    
    U->>M: 输入用户名密码
    M->>B: POST /api/auth/login
    B->>D: 验证用户凭据
    D-->>B: 返回用户信息
    B->>B: 生成JWT令牌
    B-->>M: 返回令牌和用户信息
    M->>M: 存储令牌
    M-->>U: 登录成功，跳转主页
```

### 评估量表分发流程
```mermaid
sequenceDiagram
    participant A as 管理员
    participant F as 前端
    participant B as 后端
    participant D as 数据库
    participant M as 移动端
    
    A->>F: 选择量表模板
    A->>F: 选择分发对象
    F->>B: POST /api/templates/{id}/distribute
    B->>D: 创建分发记录
    B->>D: 创建评估实例
    B-->>F: 分发成功
    
    M->>B: GET /api/mobile/assessments
    B->>D: 查询用户评估
    D-->>B: 返回评估列表
    B-->>M: 返回待完成评估
```

### 数据同步流程
```mermaid
sequenceDiagram
    participant M as 移动端
    participant L as 本地数据库
    participant B as 后端API
    participant C as 云端数据库
    
    M->>L: 保存本地数据
    M->>M: 检查网络连接
    
    alt 网络可用
        M->>B: 同步数据到云端
        B->>C: 保存到云端数据库
        C-->>B: 确认保存
        B-->>M: 同步成功
        M->>L: 更新同步状态
    else 网络不可用
        M->>L: 标记为待同步
        M->>M: 稍后重试
    end
```

### 评估量表和问卷完整流程思维导图
```mermaid
flowchart TD
    A[后端标准量表库] --> B[前端管理界面]
    B --> C{选择分发对象}
    C --> D[创建分发任务]
    D --> E[POST /api/templates/{id}/distribute]
    E --> F[数据库创建评估实例]
    F --> G[POST /api/mobile/assessments]
    G --> H[状态标记为 pending]
    
    H --> I[移动端获取]
    I --> J[GET /api/mobile/assessments]
    J --> K[显示待完成评估列表]
    K --> L[用户开始答题]
    L --> M[移动端本地保存答案]
    M --> N[POST /api/mobile/assessments/{id}/submit]
    
    N --> O[后端接收答案]
    O --> P[计算评分和分析]
    P --> Q[生成评估报告]
    Q --> R[保存到数据库]
    R --> S[状态更新为 completed]
    
    S --> T{查看结果}
    T --> U[前端健康资料页面]
    T --> V[移动端历史记录]
    
    U --> W[GET /api/assessments/{id}/report]
    V --> X[GET /api/mobile/assessments/history]
    
    W --> Y[问卷与评估Tab显示]
    X --> Z[移动端历史记录显示]
    
    style A fill:#e1f5fe
    style G fill:#fff3e0
    style S fill:#e8f5e8
    style Y fill:#f3e5f5
    style Z fill:#f3e5f5
```

#### 评估量表流程关键API端点

| 阶段 | API端点 | 方法 | 功能描述 |
|------|---------|------|----------|
| 分发 | `/api/templates/{id}/distribute` | POST | 创建评估分发任务 |
| 获取 | `/api/mobile/assessments` | GET | 移动端获取待完成评估 |
| 提交 | `/api/mobile/assessments/{id}/submit` | POST | 提交评估答案 |
| 查看 | `/api/assessments/{id}/report` | GET | 前端查看评估报告 |
| 历史 | `/api/mobile/assessments/history` | GET | 移动端查看历史记录 |

#### 数据库状态变化
```mermaid
stateDiagram-v2
    [*] --> pending : 创建评估实例
    pending --> in_progress : 用户开始答题
    in_progress --> completed : 提交答案
    completed --> archived : 长期存档
    
    pending --> expired : 超时未完成
    in_progress --> expired : 答题超时
```

### 健康资料管理流程思维导图
```mermaid
flowchart TD
    A[移动端数据采集] --> B{数据来源}
    B --> C[直接文件上传]
    B --> D[拍照上传]
    B --> E[二维码扫描]
    B --> F[手动输入]
    
    C --> G[POST /api/documents/upload]
    D --> H[POST /api/documents/photo]
    E --> I[POST /api/documents/qr-scan]
    F --> J[POST /api/health_records]
    
    G --> K[文件存储服务]
    H --> K
    I --> K
    
    K --> L[原始文档保存]
    L --> M[文档管理模块]
    M --> N[GET /api/documents]
    
    K --> O{需要OCR处理?}
    O -->|是| P[OCR文字识别]
    O -->|否| Q[直接数据提取]
    
    P --> R[文字数字化处理]
    Q --> R
    R --> S[数据分类处理]
    
    S --> T{数据类型分类}
    T --> U[检验报告 - Lab Tab]
    T --> V[影像报告 - Imaging Tab]
    T --> W[病历记录 - Medical Tab]
    T --> X[体征数据 - Vital Tab]
    T --> Y[用药记录 - Medication Tab]
    
    U --> Z[POST /api/lab_reports]
    V --> AA[POST /api/medical_records]
    W --> BB[POST /api/medical_records]
    X --> CC[POST /api/health_records]
    Y --> DD[POST /api/health_records]
    
    Z --> EE[数据库存储]
    AA --> EE
    BB --> EE
    CC --> EE
    DD --> EE
    
    EE --> FF{数据查询}
    FF --> GG[前端查询]
    FF --> HH[移动端查询]
    
    GG --> II[GET /api/health_records]
    GG --> JJ[GET /api/lab_reports]
    GG --> KK[GET /api/medical_records]
    
    HH --> LL[GET /api/mobile/health_records]
    HH --> MM[GET /api/mobile/lab_reports]
    
    II --> NN[前端健康资料页面]
    JJ --> NN
    KK --> NN
    LL --> OO[移动端健康记录]
    MM --> OO
    
    NN --> PP[生成趋势图表]
    OO --> QQ[移动端趋势图]
    
    PP --> RR[ECharts可视化]
    QQ --> SS[移动端图表组件]
    
    style A fill:#e3f2fd
    style K fill:#fff3e0
    style EE fill:#e8f5e8
    style NN fill:#f3e5f5
    style OO fill:#f3e5f5
    style RR fill:#fce4ec
    style SS fill:#fce4ec
```

#### 健康资料管理关键API端点

| 功能模块 | API端点 | 方法 | 功能描述 |
|----------|---------|------|----------|
| **文档上传** | `/api/documents/upload` | POST | 文件直接上传 |
| | `/api/documents/photo` | POST | 拍照上传处理 |
| | `/api/documents/qr-scan` | POST | 二维码扫描上传 |
| **文档管理** | `/api/documents` | GET | 获取文档列表 |
| | `/api/documents/{id}` | GET | 获取文档详情 |
| | `/api/documents/{id}/ocr` | POST | 触发OCR处理 |
| **数据存储** | `/api/health_records` | POST | 创建健康记录 |
| | `/api/lab_reports` | POST | 创建检验报告 |
| | `/api/medical_records` | POST | 创建医疗记录 |
| **数据查询** | `/api/health_records` | GET | 前端查询健康记录 |
| | `/api/mobile/health_records` | GET | 移动端查询记录 |
| | `/api/health_records/trends` | GET | 获取趋势数据 |
| **图表生成** | `/api/charts/health-trends` | GET | 生成健康趋势图 |
| | `/api/charts/lab-trends` | GET | 生成检验趋势图 |

#### 数据处理流水线
```mermaid
flowchart LR
    A[原始数据] --> B[格式验证]
    B --> C[OCR识别]
    C --> D[数据清洗]
    D --> E[分类标记]
    E --> F[结构化存储]
    F --> G[索引建立]
    G --> H[可视化准备]
    
    style A fill:#ffebee
    style D fill:#e8f5e8
    style F fill:#e3f2fd
    style H fill:#f3e5f5
```

---

## 🚀 部署架构

### 开发环境
```mermaid
graph TB
    subgraph "开发机器"
        subgraph "移动端开发"
            MD[移动端应用]
            MP[Python 3.13]
            MK[Kivy + KivyMD]
        end
        
        subgraph "前端开发"
            FD[Vue3应用]
            FN[Node.js]
            FV[Vite开发服务器:8080]
        end
        
        subgraph "后端开发"
            BD[FastAPI应用]
            BP[Python 3.11+]
            BU[Uvicorn服务器:8006]
        end
        
        subgraph "数据存储"
            DS[SQLite数据库]
            DF[文件存储]
        end
    end
    
    MD --> BU
    FV --> BU
    BU --> DS
    BU --> DF
```

### 生产环境建议
```mermaid
graph TB
    subgraph "负载均衡层"
        LB[Nginx负载均衡器]
    end
    
    subgraph "应用层"
        FE1[前端服务器1]
        FE2[前端服务器2]
        BE1[后端服务器1]
        BE2[后端服务器2]
    end
    
    subgraph "数据层"
        DB[PostgreSQL主库]
        DBR[PostgreSQL从库]
        REDIS[Redis缓存]
        FILES[文件存储服务]
    end
    
    LB --> FE1
    LB --> FE2
    LB --> BE1
    LB --> BE2
    
    BE1 --> DB
    BE2 --> DB
    BE1 --> DBR
    BE2 --> DBR
    BE1 --> REDIS
    BE2 --> REDIS
    BE1 --> FILES
    BE2 --> FILES
```

---

## 📊 API接口详细设计

### 移动端专用API

#### 评估量表API
```yaml
# 获取用户评估列表
GET /api/mobile/assessments
Parameters:
  - custom_id: string (用户ID)
  - limit: integer (限制数量)
  - offset: integer (偏移量)
  - status: string (状态筛选)
Response:
  {
    "assessments": [
      {
        "id": 1,
        "template_id": "standard_phq9",
        "name": "PHQ-9抑郁量表",
        "status": "pending",
        "created_at": "2024-01-15T10:30:00Z",
        "questions": [...]
      }
    ],
    "total": 10,
    "has_more": true
  }

# 提交评估结果
POST /api/mobile/assessments/{id}/submit
Body:
  {
    "answers": [
      {"question_id": 1, "answer": "经常"},
      {"question_id": 2, "answer": "偶尔"}
    ],
    "completion_time": 300
  }
Response:
  {
    "success": true,
    "score": 15,
    "max_score": 27,
    "interpretation": "轻度抑郁"
  }
```

#### 问卷调查API
```yaml
# 获取问卷列表
GET /api/mobile/questionnaires
Parameters:
  - custom_id: string
  - category: string
Response:
  {
    "questionnaires": [
      {
        "id": 1,
        "title": "生活质量调查",
        "description": "评估患者生活质量",
        "questions_count": 20,
        "estimated_time": 10
      }
    ]
  }

# 提交问卷答案
POST /api/mobile/questionnaires/{id}/submit
Body:
  {
    "answers": {
      "q1": "很满意",
      "q2": "一般",
      "q3": ["选项A", "选项B"]
    }
  }
```

### 前端管理API

#### 用户管理API
```yaml
# 获取用户列表
GET /api/users
Parameters:
  - page: integer
  - size: integer
  - search: string
  - role: string
Response:
  {
    "users": [...],
    "total": 100,
    "page": 1,
    "size": 20
  }

# 创建用户
POST /api/users
Body:
  {
    "username": "user001",
    "email": "<EMAIL>",
    "password": "password123",
    "role": "patient",
    "profile": {...}
  }
```

#### 模板管理API
```yaml
# 获取评估模板
GET /api/templates/assessment-templates
Response:
  {
    "templates": [
      {
        "id": "standard_phq9",
        "name": "PHQ-9抑郁量表",
        "category": "心理评估",
        "questions": [...]
      }
    ]
  }

# 分发模板
POST /api/templates/{template_id}/distribute
Body:
  {
    "distribution_type": "role",
    "target_roles": ["patient"],
    "target_users": ["SM_006"],
    "due_date": "2024-02-01T00:00:00Z"
  }
```

---

## 🔧 优化建议

### 1. 性能优化

#### 数据库优化
- **索引优化**: 为常用查询字段添加索引
```sql
CREATE INDEX idx_assessments_custom_id ON assessments(custom_id);
CREATE INDEX idx_assessments_status ON assessments(status);
CREATE INDEX idx_health_records_custom_id ON health_records(custom_id);
```

- **查询优化**: 使用分页和限制查询
```python
# 优化前
assessments = db.query(Assessment).all()

# 优化后
assessments = db.query(Assessment)\
    .filter(Assessment.custom_id == user_id)\
    .order_by(Assessment.created_at.desc())\
    .limit(20).offset(page * 20).all()
```

#### 缓存策略
```python
# Redis缓存配置
CACHE_CONFIG = {
    'user_sessions': 3600,      # 用户会话缓存1小时
    'assessment_templates': 86400,  # 模板缓存24小时
    'api_responses': 300        # API响应缓存5分钟
}
```

### 2. 安全优化

#### API安全
```python
# 请求限制
from slowapi import Limiter

limiter = Limiter(key_func=get_remote_address)

@app.post("/api/auth/login")
@limiter.limit("5/minute")
async def login(request: Request, ...):
    # 登录逻辑
    pass
```

#### 数据验证
```python
# 输入验证
from pydantic import BaseModel, validator

class UserCreate(BaseModel):
    username: str
    email: str
    password: str
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('密码长度至少8位')
        return v
```

### 3. 移动端优化

#### 启动优化
```python
# 延迟加载非关键模块
def lazy_import():
    global heavy_module
    if 'heavy_module' not in globals():
        import heavy_module
    return heavy_module
```

#### 内存优化
```python
# 图片缓存管理
class ImageCache:
    def __init__(self, max_size=50):
        self.cache = {}
        self.max_size = max_size
    
    def get_image(self, path):
        if len(self.cache) > self.max_size:
            # 清理最旧的缓存
            oldest = min(self.cache.keys())
            del self.cache[oldest]
        # 加载图片逻辑
```

### 4. 前端优化

#### 代码分割
```javascript
// 路由懒加载
const routes = [
  {
    path: '/assessments',
    component: () => import('../views/Assessments.vue')
  }
]
```

#### 状态管理优化
```javascript
// 持久化关键状态
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)
```

---

## 🎯 下一步工作指导

### 短期目标 (1-2周)

#### 1. 移动端优化
- [ ] 修复KivyMD 2.0.1兼容性问题
- [ ] 优化应用启动速度
- [ ] 完善离线数据同步机制
- [ ] 添加网络状态检测

#### 2. API完善
- [ ] 统一错误处理机制
- [ ] 添加API版本控制
- [ ] 完善API文档
- [ ] 增加请求限制和安全验证

#### 3. 前端改进
- [ ] 优化用户界面响应速度
- [ ] 添加实时数据更新
- [ ] 完善权限控制
- [ ] 增加数据可视化图表

### 中期目标 (1-2月)

#### 1. 系统架构升级
- [ ] 数据库迁移到PostgreSQL
- [ ] 引入Redis缓存
- [ ] 实现微服务架构
- [ ] 添加消息队列系统

#### 2. 功能扩展
- [ ] 实现实时通知系统
- [ ] 添加数据分析模块
- [ ] 开发报告生成功能
- [ ] 集成第三方医疗设备API

#### 3. 性能优化
- [ ] 实现CDN加速
- [ ] 优化数据库查询
- [ ] 添加监控和日志系统
- [ ] 实现自动化测试

### 长期目标 (3-6月)

#### 1. 平台化发展
- [ ] 开发插件系统
- [ ] 支持多租户架构
- [ ] 实现API开放平台
- [ ] 添加第三方集成能力

#### 2. 智能化升级
- [ ] 集成AI诊断辅助
- [ ] 实现智能推荐系统
- [ ] 添加自然语言处理
- [ ] 开发预测分析功能

#### 3. 生态建设
- [ ] 开发开发者工具
- [ ] 建立合作伙伴体系
- [ ] 创建社区平台
- [ ] 提供培训和支持服务

### 技术债务清理

#### 代码质量
- [ ] 统一代码风格和规范
- [ ] 增加单元测试覆盖率
- [ ] 重构遗留代码
- [ ] 优化数据库设计

#### 文档完善
- [ ] 更新API文档
- [ ] 编写开发者指南
- [ ] 创建部署手册
- [ ] 建立故障排除指南

#### 安全加固
- [ ] 实施安全审计
- [ ] 加强数据加密
- [ ] 完善访问控制
- [ ] 建立安全监控

---

## 📝 总结

本健康管理系统采用现代化的技术栈，实现了移动端、Web前端和后端服务的完整架构。系统具有以下特点：

### 优势
1. **技术栈先进**: 使用Vue3、FastAPI、KivyMD等现代框架
2. **架构清晰**: 前后端分离，模块化设计
3. **功能完整**: 涵盖用户管理、健康记录、评估量表等核心功能
4. **扩展性强**: 支持插件化和微服务架构

### 待改进点
1. **性能优化**: 需要引入缓存和数据库优化
2. **安全加固**: 需要完善认证和权限控制
3. **监控完善**: 需要添加系统监控和日志
4. **测试覆盖**: 需要增加自动化测试

通过按照本文档的指导进行开发和优化，可以构建一个稳定、高效、安全的健康管理平台。

## 1. 数据库表结构（详细字段+建表SQL）

### 1.1 assessment_templates（量表模板）
| 字段名         | 类型      | 说明           |
| -------------- | --------- | -------------- |
| id             | int       | 主键           |
| name           | string    | 模板名称       |
| version        | string    | 版本           |
| description    | text      | 描述           |
| ...            | ...       | 其他字段       |

**建表SQL**：
```sql
CREATE TABLE assessment_templates (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  version VARCHAR(20),
  description TEXT,
  -- 其他字段
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 1.2 questionnaire_templates（问卷模板）
| 字段名         | 类型      | 说明           |
| -------------- | --------- | -------------- |
| id             | int       | 主键           |
| name           | string    | 模板名称       |
| version        | string    | 版本           |
| description    | text      | 描述           |
| ...            | ...       | 其他字段       |

**建表SQL**：
```sql
CREATE TABLE questionnaire_templates (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  version VARCHAR(20),
  description TEXT,
  -- 其他字段
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 1.3 assessment_distributions（量表分发表）
| 字段名         | 类型      | 说明           |
| -------------- | --------- | -------------- |
| id             | int       | 主键           |
| assessment_id  | int       | 关联量表ID     |
| custom_id      | string    | 用户唯一标识   |
| status         | string    | pending/completed/expired |
| due_date       | datetime  | 截止日期       |
| completed_at   | datetime  | 完成时间       |
| message        | text      | 分发说明       |
| ...            | ...       | 其他字段       |

**建表SQL**：
```sql
CREATE TABLE assessment_distributions (
  id SERIAL PRIMARY KEY,
  assessment_id INT NOT NULL REFERENCES assessments(id) ON DELETE CASCADE,
  custom_id VARCHAR(50) NOT NULL REFERENCES users(custom_id) ON DELETE CASCADE,
  status VARCHAR(20) DEFAULT 'pending',
  due_date TIMESTAMP,
  completed_at TIMESTAMP,
  message TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 1.4 questionnaire_distributions（问卷分发表）
| 字段名         | 类型      | 说明           |
| -------------- | --------- | -------------- |
| id             | int       | 主键           |
| questionnaire_id| int      | 关联问卷ID     |
| custom_id      | string    | 用户唯一标识   |
| status         | string    | pending/completed/expired |
| due_date       | datetime  | 截止日期       |
| completed_at   | datetime  | 完成时间       |
| message        | text      | 分发说明       |
| ...            | ...       | 其他字段       |

**建表SQL**：
```sql
CREATE TABLE questionnaire_distributions (
  id SERIAL PRIMARY KEY,
  questionnaire_id INT NOT NULL REFERENCES questionnaires(id) ON DELETE CASCADE,
  custom_id VARCHAR(50) NOT NULL REFERENCES users(custom_id) ON DELETE CASCADE,
  status VARCHAR(20) DEFAULT 'pending',
  due_date TIMESTAMP,
  completed_at TIMESTAMP,
  message TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 1.5 assessment_responses（量表答题表）
| 字段名         | 类型      | 说明           |
| -------------- | --------- | -------------- |
| id             | int       | 主键           |
| assessment_id  | int       | 关联量表ID     |
| custom_id      | string    | 用户唯一标识   |
| answers        | json      | 答案           |
| score          | float     | 得分           |
| status         | string    | completed      |
| created_at     | datetime  | 创建时间       |
| updated_at     | datetime  | 更新时间       |
| ...            | ...       | 其他字段       |

**建表SQL**：
```sql
CREATE TABLE assessment_responses (
  id SERIAL PRIMARY KEY,
  assessment_id INT NOT NULL REFERENCES assessments(id) ON DELETE CASCADE,
  custom_id VARCHAR(50) NOT NULL REFERENCES users(custom_id) ON DELETE CASCADE,
  answers JSONB NOT NULL,
  score FLOAT,
  status VARCHAR(20) DEFAULT 'completed',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 1.6 questionnaire_responses（问卷答题表）
| 字段名         | 类型      | 说明           |
| -------------- | --------- | -------------- |
| id             | int       | 主键           |
| questionnaire_id| int      | 关联问卷ID     |
| custom_id      | string    | 用户唯一标识   |
| answers        | json      | 答案           |
| total_score    | float     | 总分           |
| status         | string    | completed      |
| created_at     | datetime  | 创建时间       |
| updated_at     | datetime  | 更新时间       |
| ...            | ...       | 其他字段       |

**建表SQL**：
```sql
CREATE TABLE questionnaire_responses (
  id SERIAL PRIMARY KEY,
  questionnaire_id INT NOT NULL REFERENCES questionnaires(id) ON DELETE CASCADE,
  custom_id VARCHAR(50) NOT NULL REFERENCES users(custom_id) ON DELETE CASCADE,
  answers JSONB NOT NULL,
  total_score FLOAT,
  status VARCHAR(20) DEFAULT 'completed',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 1.7 users（用户表）
| 字段名         | 类型      | 说明           |
| -------------- | --------- | -------------- |
| custom_id      | string    | 用户唯一标识   |
| name           | string    | 用户名         |
| role           | string    | 角色           |
| ...            | ...       | 其他字段       |

**建表SQL**：
```sql
CREATE TABLE users (
  custom_id VARCHAR(50) PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  role VARCHAR(20),
  -- 其他字段
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## 2. 后端API参数校验规则（Pydantic/FastAPI风格）

### 2.1 提交量表答题
```python
from pydantic import BaseModel, Field
from typing import List, Optional

class AssessmentAnswer(BaseModel):
    question_id: str = Field(..., description="问题ID")
    answer: str = Field(..., description="用户答案")
    score: Optional[float] = Field(None, description="得分")

class SubmitAssessmentRequest(BaseModel):
    answers: List[AssessmentAnswer]

# FastAPI 路由示例
@app.post("/mobile/assessments/{id}/submit")
def submit_assessment(id: int, req: SubmitAssessmentRequest, ...):
    ...
```

### 2.2 提交问卷答题
```python
class QuestionnaireAnswer(BaseModel):
    question_id: str = Field(..., description="问题ID")
    answer: str = Field(..., description="用户答案")
    score: Optional[float] = Field(None, description="得分")

class SubmitQuestionnaireRequest(BaseModel):
    answers: List[QuestionnaireAnswer]

@app.post("/mobile/questionnaires/{id}/submit")
def submit_questionnaire(id: int, req: SubmitQuestionnaireRequest, ...):
    ...
```

### 2.3 API通用响应模型
```python
class APIResponse(BaseModel):
    status: str
    message: Optional[str] = None
    data: Optional[dict] = None
```

---

## 3. 前端页面结构说明（Vue3+TypeScript+Pinia）

### 3.1 健康资料页（HealthData.vue）
- Tab切换：
  - Tab1：未完成（调用 getPendingAssessments, getPendingQuestionnaires）
  - Tab2：已完成（调用 getCompletedAssessments, getCompletedQuestionnaires），并将original/reports分类显示
- 历史记录：
  - 移动端“历史记录”卡片调用 getHistoryAssessments, getHistoryQuestionnaires，展示已形成报告

### 3.2 典型组件结构（已完成Tab分类）
```vue
<template>
  <el-tabs v-model="activeTab">
    <el-tab-pane label="未完成" name="pending">
      <!-- ... -->
    </el-tab-pane>
    <el-tab-pane label="已完成" name="completed">
      <h3>原始回答</h3>
      <el-table :data="completedItems.original">
        <!-- ... -->
      </el-table>
      <h3>已形成报告</h3>
      <el-table :data="completedItems.reports">
        <!-- ... -->
      </el-table>
    </el-tab-pane>
  </el-tabs>
</template>
```

### 3.3 Pinia状态管理
- 用户信息（custom_id）全局存储
- 量表/问卷列表、答题结果等可用模块化store管理

### 3.4 类型定义（TypeScript）
```ts
export interface AssessmentItem {
  id: number;
  title: string;
  due_date?: string;
  status: string;
  distribution_id?: number;
  created_at?: string;
  type: 'assessment' | 'questionnaire';
  completed_at?: string;
  score?: number;
  response_id?: number;
}
```

---

## 4. 业务流程与开发建议

1. **分发表插入**：管理员后台分发任务，插入分发表，status=pending
2. **前端Tab"待完成"**：调用pending相关API，查分发表，展示待完成任务
3. **用户作答**：填写后调用submit API，写入答题表，分发表status可同步更新为completed
4. **前端Tab"已完成"**：调用completed相关API，查答题表，展示已完成任务
5. **所有数据流均以custom_id为主键关联，确保安全与隔离**
6. **API参数校验严格，前后端类型一致，便于维护和扩展**

---

如需进一步细化API参数、前端类型定义或页面UI设计，请继续告知。