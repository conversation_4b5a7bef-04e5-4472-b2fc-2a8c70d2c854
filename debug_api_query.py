#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试API查询问题
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'YUN', 'backend'))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_api_query():
    """调试API查询问题"""
    
    # 数据库连接
    database_url = "sqlite:///YUN/backend/app.db"
    engine = create_engine(database_url)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        custom_id = "SM_006"
        
        # 1. 测试直接SQL查询
        logger.info("=== 测试直接SQL查询 ===")
        
        # 查询量表
        query = text("""
            SELECT id, name, status, custom_id, created_at, template_id
            FROM assessments 
            WHERE custom_id = :custom_id
            ORDER BY created_at DESC
        """)
        
        result = db.execute(query, {"custom_id": custom_id})
        assessments = result.fetchall()
        
        logger.info(f"直接SQL查询到的量表数量: {len(assessments)}")
        for assessment in assessments:
            logger.info(f"量表: ID={assessment.id}, 名称={assessment.name}, 状态={assessment.status}, custom_id={assessment.custom_id}")
        
        # 查询问卷
        query = text("""
            SELECT id, name, status, custom_id, created_at
            FROM questionnaires 
            WHERE custom_id = :custom_id
            ORDER BY created_at DESC
        """)
        
        result = db.execute(query, {"custom_id": custom_id})
        questionnaires = result.fetchall()
        
        logger.info(f"直接SQL查询到的问卷数量: {len(questionnaires)}")
        for questionnaire in questionnaires:
            logger.info(f"问卷: ID={questionnaire.id}, 名称={questionnaire.name}, 状态={questionnaire.status}, custom_id={questionnaire.custom_id}")
        
        # 2. 测试ORM查询
        logger.info("\n=== 测试ORM查询 ===")
        
        # 导入模型
        from app.models.assessment import Assessment
        from app.models.questionnaire import Questionnaire
        
        # 查询量表
        orm_assessments = db.query(Assessment).filter(Assessment.custom_id == custom_id).all()
        logger.info(f"ORM查询到的量表数量: {len(orm_assessments)}")
        for assessment in orm_assessments:
            logger.info(f"ORM量表: ID={assessment.id}, 名称={assessment.name}, 状态={assessment.status}, custom_id={assessment.custom_id}")
        
        # 查询问卷
        orm_questionnaires = db.query(Questionnaire).filter(Questionnaire.custom_id == custom_id).all()
        logger.info(f"ORM查询到的问卷数量: {len(orm_questionnaires)}")
        for questionnaire in orm_questionnaires:
            logger.info(f"ORM问卷: ID={questionnaire.id}, 名称={questionnaire.name}, 状态={questionnaire.status}, custom_id={questionnaire.custom_id}")
        
        # 3. 测试不带custom_id的查询
        logger.info("\n=== 测试不带custom_id的查询 ===")
        
        all_assessments = db.query(Assessment).all()
        logger.info(f"所有量表数量: {len(all_assessments)}")
        
        sm_006_assessments = [a for a in all_assessments if a.custom_id == custom_id]
        logger.info(f"custom_id为SM_006的量表数量: {len(sm_006_assessments)}")
        
        all_questionnaires = db.query(Questionnaire).all()
        logger.info(f"所有问卷数量: {len(all_questionnaires)}")
        
        sm_006_questionnaires = [q for q in all_questionnaires if q.custom_id == custom_id]
        logger.info(f"custom_id为SM_006的问卷数量: {len(sm_006_questionnaires)}")
        
        # 4. 检查数据库表结构
        logger.info("\n=== 检查数据库表结构 ===")
        
        # 检查assessments表结构
        result = db.execute(text("PRAGMA table_info(assessments)"))
        columns = result.fetchall()
        logger.info("assessments表结构:")
        for col in columns:
            logger.info(f"  {col.name}: {col.type}")
        
        # 检查questionnaires表结构
        result = db.execute(text("PRAGMA table_info(questionnaires)"))
        columns = result.fetchall()
        logger.info("questionnaires表结构:")
        for col in columns:
            logger.info(f"  {col.name}: {col.type}")
            
    except Exception as e:
        logger.error(f"调试过程中出错: {e}")
        import traceback
        logger.error(traceback.format_exc())
    finally:
        db.close()

if __name__ == "__main__":
    debug_api_query()