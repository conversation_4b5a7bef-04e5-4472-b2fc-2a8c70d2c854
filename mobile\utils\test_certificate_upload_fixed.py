"""
测试修复后的证书上传功能
"""
import os
import sys
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.DEBUG, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
if project_root not in sys.path:
    sys.path.append(project_root)

def create_test_file(file_path):
    """创建测试文件"""
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # 创建一个简单的文本文件
        with open(file_path, 'w') as f:
            f.write("This is a test file for certificate upload.")
        
        logger.info(f"已创建测试文件: {file_path}")
        return True
    except Exception as e:
        logger.error(f"创建测试文件失败: {e}")
        return False

def test_upload_certificate():
    """测试上传证书功能"""
    try:
        # 导入API客户端
        from utils.local_api_client import LocalApiClient
        
        # 获取API客户端实例
        api_client = LocalApiClient()
        logger.info(f"已获取API客户端实例: {type(api_client).__name__}")
        
        # 创建测试文件
        data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data')
        test_file = os.path.join(data_dir, 'test_certificate_fixed.txt')
        
        if not os.path.exists(test_file):
            if not create_test_file(test_file):
                logger.error("无法创建测试文件，测试终止")
                return False
        
        # 测试上传证书
        logger.info(f"开始测试上传证书: {test_file}")
        result = api_client.upload_certificate(
            file_path=test_file,
            certificate_type="test_certificate",
            description="Test certificate upload fixed"
        )
        
        # 检查结果
        if result:
            logger.info(f"上传结果: {result}")
            if result.get('success'):
                logger.info(f"证书上传成功，文档ID: {result.get('document_id')}")
                return True
            else:
                logger.error(f"证书上传失败: {result.get('message')}")
                return False
        else:
            logger.error("上传证书返回空结果")
            return False
    
    except Exception as e:
        logger.error(f"测试上传证书时出错: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_mock_response():
    """测试模拟响应处理"""
    try:
        # 导入API客户端
        from utils.local_api_client import LocalApiClient
        
        # 创建API客户端实例
        api_client = LocalApiClient()
        
        # 模拟不同类型的响应
        test_responses = [
            # 1. 标准成功响应
            {
                'success': True,
                'document_id': '12345',
                'message': '证书上传成功'
            },
            # 2. 只有ID的响应
            {
                'id': '67890'
            },
            # 3. 空响应
            {},
            # 4. 错误响应
            {
                'success': False,
                'message': '上传失败，未知错误'
            },
            # 5. 非标准响应
            {
                'status': 'ok',
                'file_id': '54321'
            }
        ]
        
        # 测试每种响应
        for i, response in enumerate(test_responses):
            logger.info(f"测试响应 {i+1}: {response}")
            
            # 模拟处理响应
            # 这里我们直接调用_process_certificate_response方法（如果存在）
            # 或者手动实现类似的逻辑
            if hasattr(api_client, '_process_certificate_response'):
                result = api_client._process_certificate_response(response)
            else:
                # 手动实现类似的逻辑
                result = process_mock_response(response)
                
            logger.info(f"处理结果: {result}")
            
            # 验证结果
            if result:
                if result.get('success'):
                    logger.info(f"处理成功，文档ID: {result.get('document_id')}")
                else:
                    logger.warning(f"处理失败: {result.get('message')}")
            else:
                logger.error("处理返回空结果")
        
        return True
    
    except Exception as e:
        logger.error(f"测试模拟响应处理时出错: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def process_mock_response(response):
    """手动实现响应处理逻辑"""
    if not response:
        return {'success': False, 'message': '响应为空'}
    
    # 检查是否有success字段
    if 'success' in response:
        if response['success']:
            # 成功响应
            document_id = response.get('document_id') or response.get('id') or response.get('file_id')
            if document_id:
                return {
                    'success': True,
                    'document_id': document_id,
                    'message': response.get('message', '处理成功')
                }
            else:
                return {
                    'success': True,
                    'message': '处理成功，但未返回文档ID'
                }
        else:
            # 失败响应
            return {
                'success': False,
                'message': response.get('message', '处理失败，未知错误')
            }
    
    # 没有success字段，检查其他字段
    document_id = response.get('document_id') or response.get('id') or response.get('file_id')
    if document_id:
        return {
            'success': True,
            'document_id': document_id,
            'message': '处理成功'
        }
    elif 'status' in response and response['status'] == 'ok':
        return {
            'success': True,
            'message': '处理成功，但未返回文档ID'
        }
    else:
        return {
            'success': False,
            'message': '处理失败，未知错误'
        }

if __name__ == "__main__":
    # 执行测试
    logger.info("开始测试修复后的证书上传功能")
    
    # 测试模拟响应处理
    logger.info("测试模拟响应处理...")
    mock_success = test_mock_response()
    
    # 测试实际上传
    logger.info("测试实际上传...")
    upload_success = test_upload_certificate()
    
    if mock_success and upload_success:
        logger.info("测试成功: 证书上传功能修复有效")
        sys.exit(0)
    elif mock_success:
        logger.warning("模拟测试成功，但实际上传测试失败")
        sys.exit(1)
    else:
        logger.error("测试失败: 证书上传功能修复无效")
        sys.exit(2)
