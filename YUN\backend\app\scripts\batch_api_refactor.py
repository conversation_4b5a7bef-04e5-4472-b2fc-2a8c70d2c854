import os
import re

API_DIR = os.path.join(os.path.dirname(__file__), '../api')

# 1. 替换HTTPException为AppException
# 2. 简化return结构（仅做简单演示，复杂return需人工复查）
def refactor_api_files():
    for fname in os.listdir(API_DIR):
        if not fname.endswith('.py'):
            continue
        fpath = os.path.join(API_DIR, fname)
        with open(fpath, 'r', encoding='utf-8') as f:
            code = f.read()
        # 替换HTTPException
        code = re.sub(r'raise HTTPException\(([^)]*)\)', r'raise AppException(ErrorCodes.SERVER_ERROR, details=\1)', code)
        # 替换import
        code = code.replace('from fastapi import APIRouter, Depends, HTTPException', 'from fastapi import APIRouter, Depends')
        code = code.replace('from fastapi import HTTPException', '')
        # 可扩展：return结构简化
        # ...
        with open(fpath, 'w', encoding='utf-8') as f:
            f.write(code)
    print('API 路由批量初步重构完成，请人工复查复杂逻辑！')

if __name__ == '__main__':
    refactor_api_files() 