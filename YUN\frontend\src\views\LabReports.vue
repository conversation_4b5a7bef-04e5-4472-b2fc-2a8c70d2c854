<template>
  <div class="lab-reports-container">
    <h1>实验室检验报告管理</h1>

    <!-- 用户查询组件 -->
    <user-search @select-user="handleUserSelect" />

    <el-card class="filter-card">
      <el-form :inline="true" :model="filterForm" class="filter-form">

        <el-form-item label="类型">
          <el-select v-model="filterForm.reportType" placeholder="报告类型" clearable>
            <el-option label="血常规" value="blood_routine" />
            <el-option label="尿常规" value="urine_routine" />
            <el-option label="肝功能" value="liver_function" />
            <el-option label="肾功能" value="kidney_function" />
            <el-option label="血脂" value="blood_lipids" />
            <el-option label="血糖" value="blood_glucose" />
            <el-option label="甲状腺功能" value="thyroid_function" />
            <el-option label="肿瘤标志物" value="tumor_marker" />
            <el-option label="凝血功能" value="coagulation" />
            <el-option label="电解质" value="electrolyte" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>

        <el-form-item label="医院">
          <el-input v-model="filterForm.hospitalName" placeholder="医院名称" clearable />
        </el-form-item>

        <el-form-item label="异常">
          <el-select v-model="filterForm.isAbnormal" placeholder="是否异常" clearable>
            <el-option label="是" :value="true" />
            <el-option label="否" :value="false" />
          </el-select>
        </el-form-item>

        <el-form-item label="日期范围">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="fetchReports">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="reports-card">
      <template #header>
        <div class="card-header">
          <span>实验室检验报告列表</span>
          <div>
            <el-button type="success" @click="exportReports">导出数据</el-button>
            <el-button type="primary" @click="handleCreate" v-if="isAdmin">新增报告</el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="reports"
        style="width: 100%"
        border
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="report_type" label="类型" width="120">
          <template #default="scope">
            <el-tag :type="getReportTypeTag(scope.row.report_type)">
              {{ getReportTypeLabel(scope.row.report_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="hospital_name" label="医院" width="180" />
        <el-table-column prop="department" label="科室" width="120" />
        <el-table-column prop="test_date" label="检验日期" width="180" />
        <el-table-column prop="report_date" label="报告日期" width="180" />
        <el-table-column prop="is_abnormal" label="异常" width="80">
          <template #default="scope">
            <el-tag v-if="scope.row.is_abnormal" type="danger">异常</el-tag>
            <el-tag v-else type="success">正常</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="items" label="检验项目" width="100">
          <template #default="scope">
            {{ scope.row.items ? scope.row.items.length : 0 }} 项
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="handleView(scope.row)">查看</el-button>
            <el-button size="small" type="success" @click="handleShowTrend(scope.row)">趋势图</el-button>
            <el-button size="small" type="primary" @click="handleEdit(scope.row)" v-if="isAdmin">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)" v-if="isAdmin">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 查看/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'view' ? '查看检验报告' : (dialogType === 'create' ? '新增检验报告' : '编辑检验报告')"
      width="80%"
    >
      <el-tabs v-model="activeTab">
        <el-tab-pane label="基本信息" name="basic">
          <el-form
            ref="reportForm"
            :model="currentReport"
            :rules="formRules"
            label-width="100px"
            :disabled="dialogType === 'view'"
          >
            <el-form-item label="报告类型" prop="report_type">
              <el-select v-model="currentReport.report_type" placeholder="请选择报告类型">
                <el-option label="血常规" value="blood_routine" />
                <el-option label="尿常规" value="urine_routine" />
                <el-option label="肝功能" value="liver_function" />
                <el-option label="肾功能" value="kidney_function" />
                <el-option label="血脂" value="blood_lipids" />
                <el-option label="血糖" value="blood_glucose" />
                <el-option label="甲状腺功能" value="thyroid_function" />
                <el-option label="肿瘤标志物" value="tumor_marker" />
                <el-option label="凝血功能" value="coagulation" />
                <el-option label="电解质" value="electrolyte" />
                <el-option label="其他" value="other" />
              </el-select>
            </el-form-item>

            <el-form-item label="医院名称" prop="hospital_name">
              <el-input v-model="currentReport.hospital_name" placeholder="请输入医院名称" />
            </el-form-item>

            <el-form-item label="科室">
              <el-input v-model="currentReport.department" placeholder="请输入科室" />
            </el-form-item>

            <el-form-item label="检验日期">
              <el-date-picker
                v-model="currentReport.test_date"
                type="datetime"
                placeholder="请选择检验日期"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>

            <el-form-item label="报告日期">
              <el-date-picker
                v-model="currentReport.report_date"
                type="datetime"
                placeholder="请选择报告日期"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>

            <el-form-item label="医生姓名">
              <el-input v-model="currentReport.doctor_name" placeholder="请输入医生姓名" />
            </el-form-item>

            <el-form-item label="诊断">
              <el-input
                v-model="currentReport.diagnosis"
                type="textarea"
                :rows="3"
                placeholder="请输入诊断"
              />
            </el-form-item>

            <el-form-item label="备注">
              <el-input
                v-model="currentReport.notes"
                type="textarea"
                :rows="3"
                placeholder="请输入备注"
              />
            </el-form-item>

            <el-form-item label="异常标记">
              <el-switch v-model="currentReport.is_abnormal" />
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <el-tab-pane label="检验项目" name="items">
          <div class="items-header">
            <h3>检验项目列表</h3>
            <el-button
              type="primary"
              size="small"
              @click="handleAddItem"
              v-if="dialogType !== 'view'"
            >
              添加项目
            </el-button>
          </div>

          <el-table
            :data="currentReport.items || []"
            style="width: 100%"
            border
          >
            <el-table-column prop="item_name" label="项目名称" width="180" />
            <el-table-column prop="item_value" label="检验值" width="120" />
            <el-table-column prop="item_unit" label="单位" width="100" />
            <el-table-column prop="reference_range" label="参考范围" width="180" />
            <el-table-column prop="is_abnormal" label="异常" width="80">
              <template #default="scope">
                <el-tag v-if="scope.row.is_abnormal" type="danger">异常</el-tag>
                <el-tag v-else type="success">正常</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="abnormal_level" label="异常程度" width="100" />
            <el-table-column prop="notes" label="备注" show-overflow-tooltip />
            <el-table-column label="操作" width="150" v-if="dialogType !== 'view'">
              <template #default="scope">
                <el-button size="small" type="primary" @click="handleEditItem(scope.$index)">编辑</el-button>
                <el-button size="small" type="danger" @click="handleDeleteItem(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveReport" v-if="dialogType !== 'view'">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 趋势图对话框 -->
    <el-dialog
      v-model="trendDialogVisible"
      title="检验项目趋势分析"
      width="80%"
    >
      <div v-if="selectedItemForTrend">
        <el-form :inline="true" class="trend-form">
          <el-form-item label="选择项目">
            <el-select v-model="selectedItemForTrend" placeholder="请选择检验项目" @change="handleItemChange">
              <el-option
                v-for="item in availableItems"
                :key="item.name"
                :label="item.name"
                :value="item.name"
              />
            </el-select>
          </el-form-item>
        </el-form>

        <lab-report-chart
          :title="selectedItemForTrend + '趋势图'"
          :chart-data="trendChartData"
          :unit="trendItemUnit"
          :reference-range="trendItemReferenceRange"
        />
      </div>
      <div v-else class="empty-trend">
        <el-empty description="请选择一个检验项目查看趋势" />
      </div>
    </el-dialog>

    <!-- 项目编辑对话框 -->
    <el-dialog
      v-model="itemDialogVisible"
      :title="itemDialogType === 'create' ? '添加检验项目' : '编辑检验项目'"
      width="50%"
      append-to-body
    >
      <el-form
        ref="itemForm"
        :model="currentItem"
        :rules="itemRules"
        label-width="100px"
      >
        <el-form-item label="项目名称" prop="item_name">
          <el-input v-model="currentItem.item_name" placeholder="请输入项目名称" />
        </el-form-item>

        <el-form-item label="检验值" prop="item_value">
          <el-input v-model="currentItem.item_value" placeholder="请输入检验值" />
        </el-form-item>

        <el-form-item label="单位">
          <el-input v-model="currentItem.item_unit" placeholder="请输入单位" />
        </el-form-item>

        <el-form-item label="参考范围">
          <el-input v-model="currentItem.reference_range" placeholder="请输入参考范围" />
        </el-form-item>

        <el-form-item label="是否异常">
          <el-switch v-model="currentItem.is_abnormal" />
        </el-form-item>

        <el-form-item label="异常程度" v-if="currentItem.is_abnormal">
          <el-select v-model="currentItem.abnormal_level" placeholder="请选择异常程度">
            <el-option label="轻度" value="mild" />
            <el-option label="中度" value="moderate" />
            <el-option label="重度" value="severe" />
          </el-select>
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            v-model="currentItem.notes"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="itemDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveItem">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from 'axios'
import UserSearch from '../components/UserSearch.vue'
import LabReportChart from '../components/LabReportChart.vue'
import { useUserStore } from '../store/user'

// 用户列表
const users = ref([])

// 当前选中的用户
const selectedUser = ref(null)

// 报告列表
const reports = ref([])
const loading = ref(false)

// 用户权限
const userStore = useUserStore()
const isAdmin = computed(() => {
  return userStore.user?.role === 'admin' || userStore.user?.role === 'super_admin'
})

// 趋势图相关
const trendDialogVisible = ref(false)
const selectedItemForTrend = ref('')
const availableItems = ref([])
const trendChartData = ref([])
const trendItemUnit = ref('')
const trendItemReferenceRange = ref('')

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 筛选表单
const filterForm = reactive({
  reportType: '',
  hospitalName: '',
  isAbnormal: null,
  dateRange: []
})

// 当前报告
const currentReport = reactive({
  id: null,
  report_type: '',
  hospital_name: '',
  department: '',
  test_date: '',
  report_date: '',
  doctor_name: '',
  diagnosis: '',
  notes: '',
  is_abnormal: false,
  items: []
})

// 当前项目
const currentItem = reactive({
  id: null,
  item_name: '',
  item_value: '',
  item_unit: '',
  reference_range: '',
  is_abnormal: false,
  abnormal_level: '',
  notes: ''
})

// 对话框
const dialogVisible = ref(false)
const dialogType = ref('view') // view, edit, create
const reportForm = ref(null)
const activeTab = ref('basic')

// 项目对话框
const itemDialogVisible = ref(false)
const itemDialogType = ref('create') // create, edit
const itemForm = ref(null)
const currentItemIndex = ref(-1)

// 表单验证规则
const formRules = {
  report_type: [
    { required: true, message: '请选择报告类型', trigger: 'change' }
  ],
  hospital_name: [
    { required: true, message: '请输入医院名称', trigger: 'blur' }
  ]
}

// 项目表单验证规则
const itemRules = {
  item_name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' }
  ],
  item_value: [
    { required: true, message: '请输入检验值', trigger: 'blur' }
  ]
}

// 获取用户列表
const fetchUsers = async () => {
  try {
    const response = await axios.get('/api/users')
    users.value = response.data
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败: ' + (error.response?.data?.detail || error.message || '未知错误'))
    users.value = []
  }
}

// 处理用户选择
const handleUserSelect = (user) => {
  selectedUser.value = user
  fetchReports()
}

// 导出数据
const exportReports = () => {
  ElMessage.success('数据导出成功')
}

// 获取检验报告列表
const fetchReports = async () => {
  if (!selectedUser.value) {
    ElMessage.warning('请先选择用户')
    return
  }

  loading.value = true
  try {
    // 构建查询参数
    const params = {
      skip: (pagination.currentPage - 1) * pagination.pageSize,
      limit: pagination.pageSize,
      custom_id: selectedUser.value.id
    }

    if (filterForm.reportType) {
      params.report_type = filterForm.reportType
    }

    if (filterForm.hospitalName) {
      params.hospital_name = filterForm.hospitalName
    }

    if (filterForm.isAbnormal !== null) {
      params.is_abnormal = filterForm.isAbnormal
    }

    if (filterForm.dateRange && filterForm.dateRange.length === 2) {
      params.start_date = filterForm.dateRange[0]
      params.end_date = filterForm.dateRange[1]
    }

    try {
      // 调用后端API
      const response = await axios.get(`/api/user-health-records/user/${selectedUser.value.id}`, {
      params: {
        ...params,
        record_type: 'lab'
      }
    })
      reports.value = response.data
      pagination.total = response.headers['x-total-count'] || response.data.length
    } catch (error) {
      console.error('获取检验报告列表失败:', error)
      ElMessage.error('获取检验报告列表失败: ' + (error.response?.data?.detail || error.message || '未知错误'))
      reports.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('获取检验报告列表失败:', error)
    ElMessage.error('获取检验报告列表失败')
  } finally {
    loading.value = false
  }
}

// 重置筛选条件
const resetFilter = () => {
  filterForm.reportType = ''
  filterForm.hospitalName = ''
  filterForm.isAbnormal = null
  filterForm.dateRange = []
  if (selectedUser.value) {
    fetchReports()
  }
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pagination.pageSize = size
  fetchReports()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  pagination.currentPage = page
  fetchReports()
}

// 获取报告类型标签类型
const getReportTypeTag = (type) => {
  const typeMap = {
    blood_routine: '',
    urine_routine: 'success',
    liver_function: 'warning',
    kidney_function: 'info',
    blood_lipids: 'danger',
    blood_glucose: 'warning',
    thyroid_function: 'info',
    tumor_marker: 'danger',
    coagulation: 'warning',
    electrolyte: 'info',
    other: ''
  }
  return typeMap[type] || ''
}

// 获取报告类型标签文本
const getReportTypeLabel = (type) => {
  const typeMap = {
    blood_routine: '血常规',
    urine_routine: '尿常规',
    liver_function: '肝功能',
    kidney_function: '肾功能',
    blood_lipids: '血脂',
    blood_glucose: '血糖',
    thyroid_function: '甲状腺功能',
    tumor_marker: '肿瘤标志物',
    coagulation: '凝血功能',
    electrolyte: '电解质',
    other: '其他'
  }
  return typeMap[type] || type
}

// 查看报告
const handleView = async (row) => {
  try {
    // 获取完整报告信息（包括项目）
    const response = await axios.get(`/api/user-health-records/user/${selectedUser.value.id}`, {
      params: {
        record_type: 'lab',
        record_id: row.id
      }
    })
    Object.assign(currentReport, response.data)

    dialogType.value = 'view'
    dialogVisible.value = true
    activeTab.value = 'basic'
  } catch (error) {
    console.error('获取报告详情失败:', error)
    ElMessage.error('获取报告详情失败')
  }
}

// 显示趋势图
const handleShowTrend = (row) => {
  // 获取该用户所有相同类型的报告
  fetchHistoricalReports(row)
}

// 获取历史报告数据
const fetchHistoricalReports = async (currentReport) => {
  if (!selectedUser.value) return

  try {
    // 调用后端API获取历史报告数据
    const response = await axios.get(`/api/user-health-records/${selectedUser.value.id}`, {
      params: {
        record_type: 'lab',
        sub_type: currentReport.report_type,
        history: true
      }
    })
    const historicalReports = response.data || []

    // 提取所有可用的检验项目
    const allItems = new Set()
    historicalReports.forEach(report => {
      report.items.forEach(item => {
        allItems.add(item.item_name)
      })
    })

    availableItems.value = Array.from(allItems).map(name => ({ name }))

    // 默认选择第一个项目
    if (availableItems.value.length > 0) {
      selectedItemForTrend.value = availableItems.value[0].name
      prepareChartData()
    }

    trendDialogVisible.value = true
  } catch (error) {
    console.error('获取历史报告数据失败:', error)
    ElMessage.error('获取历史报告数据失败')
  }
}

// 处理项目选择变化
const handleItemChange = () => {
  prepareChartData()
}

// 准备图表数据
const prepareChartData = async () => {
  try {
    // 调用后端API获取趋势数据
    const response = await axios.get(`/api/user-health-records/${selectedUser.value.id}`, {
      params: {
        record_type: 'lab',
        item_name: selectedItemForTrend.value,
        trend: true
      }
    })
    trendChartData.value = response.data.data
    trendItemUnit.value = response.data.unit
    trendItemReferenceRange.value = response.data.reference_range
  } catch (error) {
    console.error('获取趋势数据失败:', error)
    ElMessage.error('获取趋势数据失败: ' + (error.response?.data?.detail || error.message || '未知错误'))
    trendChartData.value = []
    trendItemUnit.value = ''
    trendItemReferenceRange.value = ''
  }
}

// 编辑报告
const handleEdit = async (row) => {
  try {
    // 获取完整报告信息（包括项目）
    const response = await axios.get(`/api/user-health-records/${selectedUser.value.id}`, {
      params: {
        record_type: 'lab',
        record_id: row.id
      }
    })
    Object.assign(currentReport, response.data)

    dialogType.value = 'edit'
    dialogVisible.value = true
    activeTab.value = 'basic'
  } catch (error) {
    console.error('获取报告详情失败:', error)
    ElMessage.error('获取报告详情失败')
  }
}

// 新增报告
const handleCreate = () => {
  // 重置表单
  Object.assign(currentReport, {
    id: null,
    report_type: '',
    hospital_name: '',
    department: '',
    test_date: '',
    report_date: '',
    doctor_name: '',
    diagnosis: '',
    notes: '',
    is_abnormal: false,
    items: []
  })
  dialogType.value = 'create'
  dialogVisible.value = true
  activeTab.value = 'basic'
}

// 删除报告
const handleDelete = (row) => {
  ElMessageBox.confirm(
    '确定要删除这份检验报告吗？此操作不可恢复。',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await axios.delete(`/api/user-health-records/user/${selectedUser.value.id}/${row.id}`, {
        params: {
          record_type: 'lab'
        }
      })
      ElMessage.success('删除成功')
      fetchReports()
    } catch (error) {
      console.error('删除检验报告失败:', error)
      ElMessage.error('删除检验报告失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 保存报告
const saveReport = async () => {
  try {
    await reportForm.value.validate()

    if (dialogType.value === 'create') {
      // 创建新报告
      await axios.post(`/api/user-health-records/user/${selectedUser.value.id}`, {
        ...currentReport,
        record_type: 'lab'
      })
    } else {
      // 更新报告
      await axios.put(`/api/user-health-records/user/${selectedUser.value.id}/${currentReport.id}`, {
        ...currentReport,
        record_type: 'lab'
      })
    }

    ElMessage.success(dialogType.value === 'create' ? '创建成功' : '更新成功')
    dialogVisible.value = false
    fetchReports()
  } catch (error) {
    console.error('保存检验报告失败:', error)
    ElMessage.error('保存检验报告失败')
  }
}

// 添加项目
const handleAddItem = () => {
  // 重置项目表单
  Object.assign(currentItem, {
    id: null,
    item_name: '',
    item_value: '',
    item_unit: '',
    reference_range: '',
    is_abnormal: false,
    abnormal_level: '',
    notes: ''
  })
  itemDialogType.value = 'create'
  currentItemIndex.value = -1
  itemDialogVisible.value = true
}

// 编辑项目
const handleEditItem = (index) => {
  // 复制项目数据
  Object.assign(currentItem, currentReport.items[index])
  itemDialogType.value = 'edit'
  currentItemIndex.value = index
  itemDialogVisible.value = true
}

// 删除项目
const handleDeleteItem = (index) => {
  ElMessageBox.confirm(
    '确定要删除这个检验项目吗？',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    currentReport.items.splice(index, 1)

    // 检查是否还有异常项目
    const hasAbnormal = currentReport.items.some(item => item.is_abnormal)
    currentReport.is_abnormal = hasAbnormal
  }).catch(() => {
    // 取消删除
  })
}

// 保存项目
const saveItem = async () => {
  try {
    await itemForm.value.validate()

    if (itemDialogType.value === 'create') {
      // 添加新项目
      if (!currentReport.items) {
        currentReport.items = []
      }
      currentReport.items.push({ ...currentItem })
    } else {
      // 更新项目
      Object.assign(currentReport.items[currentItemIndex.value], currentItem)
    }

    // 检查是否有异常项目
    const hasAbnormal = currentReport.items.some(item => item.is_abnormal)
    currentReport.is_abnormal = hasAbnormal

    itemDialogVisible.value = false
  } catch (error) {
    console.error('保存检验项目失败:', error)
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchUsers()
  fetchReports()
})
</script>

<style scoped>
.lab-reports-container {
  padding: 20px;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.items-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
</style>
