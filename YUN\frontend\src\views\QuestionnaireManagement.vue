<template>
  <div class="questionnaire-management">
    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <h2>调查问卷管理</h2>
          <el-button type="primary" @click="openTemplateDialog">新建问卷</el-button>
        </div>
      </template>

      <!-- 问卷列表 -->
      <el-tabs v-model="activeTab" class="questionnaire-tabs">
        <el-tab-pane label="全部问卷" name="all">
          <assessment-list
            :assessments="filteredQuestionnaires"
            :loading="loading"
            @edit="handleEdit"
            @delete="handleDelete"
            @distribute="handleDistribute"
            @view-results="handleViewResults"
            @preview="handlePreview"
            @review="handleReview"
            @push="handlePush"
          />
        </el-tab-pane>
        <el-tab-pane label="已发布" name="published">
          <assessment-list
            :assessments="filteredQuestionnaires"
            :loading="loading"
            @edit="handleEdit"
            @delete="handleDelete"
            @distribute="handleDistribute"
            @view-results="handleViewResults"
            @preview="handlePreview"
            @review="handleReview"
            @push="handlePush"
          />
        </el-tab-pane>
        <el-tab-pane label="标准问卷" name="standard">
          <assessment-list
            :assessments="filteredQuestionnaires"
            :loading="loading"
            @edit="handleEdit"
            @delete="handleDelete"
            @distribute="handleDistribute"
            @view-results="handleViewResults"
            @preview="handlePreview"
            @review="handleReview"
            @push="handlePush"
          />
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 创建/编辑问卷对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEditing ? '编辑问卷' : '使用模板新建问卷'"
      width="80%"
      :before-close="handleDialogClose"
    >
      <assessment-editor
        v-if="dialogVisible"
        :assessment="currentQuestionnaire"
        :is-editing="isEditing"
        @save="saveQuestionnaire"
        @cancel="dialogVisible = false"
      />
    </el-dialog>

    <!-- 分发问卷对话框 -->
    <el-dialog
      v-model="distributeDialogVisible"
      title="分发问卷"
      width="60%"
    >
      <assessment-distributor
        v-if="distributeDialogVisible"
        :assessment="currentQuestionnaire"
        @distribute="confirmDistribute"
        @cancel="distributeDialogVisible = false"
      />
    </el-dialog>

    <!-- 查看问卷结果对话框 -->
    <el-dialog
      v-model="resultsDialogVisible"
      title="问卷结果"
      width="80%"
    >
      <assessment-results
        v-if="resultsDialogVisible"
        :assessment="currentQuestionnaire"
        @close="resultsDialogVisible = false"
      />
    </el-dialog>

    <!-- 选择问卷模板对话框 -->
    <el-dialog v-model="templateDialogVisible" title="选择问卷模板" width="40%">
      <el-input v-model="templateSearch" placeholder="搜索模板名称" clearable style="margin-bottom: 12px;" />
      <el-radio-group v-model="selectedTemplateId" style="display:block;">
        <el-radio :label="0">空白新建</el-radio>
        <div v-for="tpl in filteredTemplates" :key="tpl.id" style="display:flex;align-items:center;justify-content:space-between;margin-bottom:8px;">
          <el-radio :label="tpl.id">{{ tpl.name }}（{{ tpl.type }}）</el-radio>
          <div>
            <el-button size="small" type="primary" @click.stop="onEditTemplate(tpl.id)">编辑</el-button>
            <el-button size="small" @click.stop="onUseTemplate(tpl.id)">使用</el-button>
          </div>
        </div>
      </el-radio-group>
      <template #footer>
        <el-button @click="templateDialogVisible=false">取消</el-button>
        <el-button @click="openQuestionnaireGenerator">问卷生成器</el-button>
        <el-button type="primary" @click="handleTemplateSelect">确定</el-button>
      </template>
    </el-dialog>

    <!-- 问卷生成器对话框 -->
    <el-dialog v-model="generatorDialogVisible" title="问卷生成器" width="80%">
      <questionnaire-generator v-if="generatorDialogVisible" @created="onQuestionnaireCreated" />
    </el-dialog>

    <!-- 问卷审核预览对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      title="问卷审核预览"
      width="80%"
      destroy-on-close
    >
      <div v-if="previewDialogVisible" class="questionnaire-preview-container">
        <assessment-preview
          :assessment="currentAssessment"
          :preview-mode="true"
        />
        <div class="review-actions">
          <el-button type="danger" @click="confirmReview(false)">返修</el-button>
          <el-button type="success" @click="confirmReview(true)">审核通过</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 简单预览对话框 -->
    <el-dialog
      v-model="simplePreviewDialogVisible"
      title="问卷预览"
      width="80%"
      destroy-on-close
    >
      <div v-if="simplePreviewDialogVisible" class="questionnaire-preview-container">
        <assessment-preview
          :assessment="currentAssessment"
          :preview-mode="true"
        />
        <div class="preview-actions">
          <el-button type="primary" @click="simplePreviewDialogVisible = false">关闭</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import axios from 'axios';
import { useRoute } from 'vue-router';
import { getQuestionnaireTemplates, getQuestionnaireTemplate } from '@/api/templates';
import { distributeQuestionnaire, getQuestionnaires } from '@/api/questionnaire';
import { getStandardQuestionnaires } from '@/api/clinical_scales';

// 导入组件
import AssessmentList from '../components/assessment/AssessmentList.vue';
import AssessmentEditor from '../components/assessment/AssessmentEditor.vue';
import AssessmentDistributor from '../components/assessment/AssessmentDistributor.vue';
import AssessmentResults from '../components/assessment/AssessmentResults.vue';
import AssessmentPreview from '../components/assessment/AssessmentPreview.vue';
import QuestionnaireGenerator from '../components/QuestionnaireGenerator.vue';

// 路由
const route = useRoute();

// 状态变量
const loading = ref(false);
const questionnaires = ref([]);
const activeTab = ref('all');
const dialogVisible = ref(false);
const distributeDialogVisible = ref(false);
const resultsDialogVisible = ref(false);
const isEditing = ref(false);
const currentQuestionnaire = ref(null);
const templateDialogVisible = ref(false);
const templateSearch = ref('');
const selectedTemplateId = ref(0);
const templates = ref([]);
const generatorDialogVisible = ref(false);
const currentAssessment = ref({});
const previewDialogVisible = ref(false);
const simplePreviewDialogVisible = ref(false);

// 计算属性：根据当前标签页筛选问卷
const filteredQuestionnaires = computed(() => {
  if (activeTab.value === 'all') {
    // 显示全部问卷
    return questionnaires.value;
  } else if (activeTab.value === 'published') {
    // 只显示已发布的用户问卷（排除标准问卷）
    return questionnaires.value.filter(q => q.status === 'published' && q.source === 'user');
  } else if (activeTab.value === 'standard') {
    // 只显示标准问卷
    return questionnaires.value.filter(q => q.is_standard);
  }
  return [];
});

const filteredTemplates = computed(() => {
  if (!templateSearch.value) return templates.value;
  return templates.value.filter(t => t.name.includes(templateSearch.value));
});

// 初始化
onMounted(() => {
  currentAssessment.value = {};
  fetchQuestionnaires();
  handleRouteParams();
});



// 处理路由参数
const handleRouteParams = () => {
  const action = route.query.action;
  const id = route.query.id;

  if (action === 'create-template') {
    openTemplateDialog();
  } else if ((action === 'edit-template' || action === 'use-template') && id) {
    const templateId = parseInt(id);
    if (action === 'edit-template') {
      handleEditTemplate(templateId);
    } else if (action === 'use-template') {
      handleUseTemplate(templateId);
    }
  }
};

// 标准化模板字段映射函数
function mapQuestionnaireTemplateToForm(templateData) {
  return {
    id: templateData.id,
    title: templateData.name || '',
    description: templateData.description || '',
    category: templateData.category || '',
    questionnaire_type: templateData.questionnaire_type || 'general',
    assessment_type: 'self',
    status: templateData.status || (templateData.is_active ? 'published' : 'draft'),
    question_count: Array.isArray(templateData.questions) ? templateData.questions.length : templateData.question_count || 0,
    response_count: templateData.response_count || 0,
    updated_at: templateData.updated_at || new Date().toISOString(),
    questions: Array.isArray(templateData.questions) ? templateData.questions.map(q => ({
      id: q.question_id || q.id || '',
      question_id: q.question_id || q.id || '',
      question_text: q.question_text || q.text || '',
      question_type: q.question_type || 'single_choice',
      required: q.is_required ?? q.required ?? true,
      is_required: q.is_required ?? q.required ?? true,
      options: q.options || [],
      order: q.order || 0
    })) : templateData.questions || [],
    instructions: templateData.instructions || '',
    revision_reason: templateData.revision_reason || ''
  };
}

// 获取问卷列表（包含标准问卷和用户创建的问卷）
const fetchQuestionnaires = async () => {
  loading.value = true;
  try {
    console.log('开始获取问卷列表...');
    
    // 并行获取用户创建的问卷和标准问卷
    const [userQuestionnairesRes, standardQuestionnairesRes] = await Promise.all([
      getQuestionnaires({ page: 1, page_size: 100 }),
      getStandardQuestionnaires().catch(err => {
        console.warn('获取标准问卷失败:', err);
        return { data: { data: [] } };
      })
    ]);
    
    // 处理用户创建的问卷
    const userList = userQuestionnairesRes.data?.data || userQuestionnairesRes.data || [];
    console.log(`获取到 ${userList.length} 个用户创建的问卷`);
    
    // 处理标准问卷
    const standardList = standardQuestionnairesRes.data?.records || standardQuestionnairesRes.data?.data || standardQuestionnairesRes.data || [];
    console.log(`获取到 ${standardList.length} 个标准问卷`);
    
    // 合并并去重
    const uniqueQuestionnaires = new Map();
    
    // 添加用户创建的问卷
    if (Array.isArray(userList)) {
      userList.forEach(questionnaire => {
        uniqueQuestionnaires.set(questionnaire.id, {
          ...questionnaire,
          source: 'user',
          is_standard: false
        });
      });
    }
    
    // 添加标准问卷
    if (Array.isArray(standardList)) {
      standardList.forEach(tpl => {
        // 为标准问卷生成唯一ID
        const standardId = `standard_${tpl.id}`;
        const mappedQuestionnaire = {
          id: standardId,
          title: tpl.name || tpl.title || '未命名问卷',
          description: tpl.description || '',
          category: '标准问卷',
          questionnaire_type: tpl.questionnaire_type || 'general',
          assessment_type: tpl.assessment_type || 'self',
          status: 'published', // 标准问卷默认为已发布状态
          question_count: tpl.question_count || 0,
          response_count: 0,
          updated_at: new Date().toISOString(),
          questions: tpl.questions || [],
          instructions: tpl.instructions || '',
          revision_reason: '',
          source: 'standard', // 标记为标准问卷
          is_standard: true,
          template_key: tpl.template_key
        };
        console.log(`标准问卷ID ${standardId}, 名称: ${mappedQuestionnaire.title}`);
        uniqueQuestionnaires.set(standardId, mappedQuestionnaire);
      });
    }
    
    // 先保存之前选中的问卷ID
    const currentQuestionnaireId = currentQuestionnaire.value?.id;
    questionnaires.value = Array.from(uniqueQuestionnaires.values());
    console.log(`成功加载 ${questionnaires.value.length} 个问卷（用户: ${userList.length}, 标准: ${standardList.length}）`);
    
    // 如果在操作返修，确保对应问卷的状态为draft
    if (currentQuestionnaireId) {
      const updatedIndex = questionnaires.value.findIndex(q => q.id === currentQuestionnaireId);
      if (updatedIndex !== -1 && activeTab.value === 'draft') {
          // 确保该问卷在draft状态
          if (questionnaires.value[updatedIndex].status !== 'draft') {
            console.log(`强制将问卷 ${currentQuestionnaireId} 状态更新为draft`);
            questionnaires.value[updatedIndex].status = 'draft';
          }
        }
      }
  } catch (error) {
    console.error('获取问卷列表失败:', error);
    questionnaires.value = [];
    ElMessage.error('获取问卷列表失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 编辑模板
const handleEditTemplate = async (templateId) => {
  // 检查是否为标准问卷（标准问卷ID以standard_q_开头）
  if (typeof templateId === 'string' && templateId.startsWith('standard_q_')) {
    ElMessage.warning('标准问卷不可编辑，可使用复制功能创建自定义问卷');
    return;
  }
  
  try {
    loading.value = true;
    const response = await getQuestionnaireTemplate(templateId);
    const templateData = response.data.data || response.data;
    if (templateData) {
      isEditing.value = true;
      currentQuestionnaire.value = mapQuestionnaireTemplateToForm(templateData);
      dialogVisible.value = true;
    } else {
      ElMessage.error('未找到该模板');
    }
  } catch (error) {
    ElMessage.error('获取模板详情失败');
  } finally {
    loading.value = false;
  }
};

// 使用模板创建新问卷
const handleUseTemplate = async (templateId) => {
  try {
    loading.value = true;
    
    // 处理标准问卷的情况
    if (typeof templateId === 'string' && templateId.startsWith('standard_q_')) {
      // 从questionnaires中查找对应的标准问卷
      const standardQuestionnaire = questionnaires.value.find(q => q.id === templateId);
      if (standardQuestionnaire) {
        isEditing.value = false;
        // 复制标准问卷数据，但不保留ID和标准标记
        currentQuestionnaire.value = {
          ...standardQuestionnaire,
          id: null,
          title: `${standardQuestionnaire.title} - 副本`,
          source: 'user',
          is_standard: false
        };
        dialogVisible.value = true;
        loading.value = false;
        return;
      }
    }
    
    // 处理普通模板
    const response = await getQuestionnaireTemplate(templateId);
    const templateData = response.data.data || response.data;
    if (templateData) {
      isEditing.value = false;
      currentQuestionnaire.value = mapQuestionnaireTemplateToForm(templateData);
      // 创建新问卷时不带ID
      currentQuestionnaire.value.id = null;
      dialogVisible.value = true;
    } else {
      ElMessage.error('未找到该模板');
    }
  } catch (error) {
    console.error('获取模板详情失败:', error);
    ElMessage.error('获取模板详情失败');
  } finally {
    loading.value = false;
  }
};

// 打开模板选择对话框时，加载模板列表
function openTemplateDialog() {
  templateDialogVisible.value = true;
  if (templates.value.length === 0) {
    getQuestionnaireTemplates().then(res => {
      templates.value = res.data?.data || res.data || [];
    });
  }
}

// 打开问卷生成器
const openQuestionnaireGenerator = () => {
  templateDialogVisible.value = false;
  generatorDialogVisible.value = true;
};

// 问卷生成器创建成功回调
const onQuestionnaireCreated = (questionnaire) => {
  generatorDialogVisible.value = false;
  fetchQuestionnaires();
  ElMessage.success('问卷创建成功');
};

// 处理模板选择
const handleTemplateSelect = () => {
  templateDialogVisible.value = false;
  if (selectedTemplateId.value === 0) {
    // 空白新建
    openCreateDialog();
  } else {
    // 使用模板
    handleUseTemplate(selectedTemplateId.value);
  }
};

// 编辑模板
const onEditTemplate = (templateId) => {
  // 阻止事件冒泡，避免触发radio选择
  handleEditTemplate(templateId);
};

// 使用模板
const onUseTemplate = (templateId) => {
  // 阻止事件冒泡，避免触发radio选择
  handleUseTemplate(templateId);
};

// 创建新问卷
const openCreateDialog = () => {
  isEditing.value = false;
  currentQuestionnaire.value = {
    title: '',
    description: '',
    questionnaire_type: 'general',
    questions: [],
    instructions: ''
  };
  dialogVisible.value = true;
};

// 编辑问卷
const handleEdit = async (questionnaire) => {
  isEditing.value = true;
  loading.value = true;

  try {
    const response = await axios.get(`/api/questionnaires/${questionnaire.id}`);
    currentQuestionnaire.value = mapQuestionnaireTemplateToForm(response.data.data || response.data);
    dialogVisible.value = true;
  } catch (error) {
    console.error('获取问卷详情失败:', error);
    ElMessage.error('获取问卷详情失败');
  } finally {
    loading.value = false;
  }
};

// 删除问卷
const handleDelete = (questionnaire) => {
  ElMessageBox.confirm(
    `确定要删除问卷 "${questionnaire.title}" 吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await axios.delete(`/api/questionnaires/${questionnaire.id}`);
      ElMessage.success('删除成功');
      fetchQuestionnaires();
    } catch (error) {
      console.error('删除问卷失败:', error);
      ElMessage.error('删除问卷失败');
    }
  }).catch(() => {
    // 用户取消删除
  });
};

// 分发问卷
const handleDistribute = (questionnaire) => {
  currentQuestionnaire.value = questionnaire;
  distributeDialogVisible.value = true;
};

// 确认分发
const confirmDistribute = async (distributionData) => {
  try {
    // 使用完整的问卷ID，包括标准问卷的前缀格式
    const questionnaireId = currentQuestionnaire.value.id;
    
    // 根据ID类型选择正确的API端点
    let apiEndpoint;
    if (typeof questionnaireId === 'string' && questionnaireId.startsWith('standard_')) {
      // 标准问卷使用模板分发接口
      apiEndpoint = `/api/templates/questionnaire-templates/${questionnaireId}/distribute`;
    } else {
      // 自定义问卷使用问卷分发接口
      apiEndpoint = `/api/questionnaires/${questionnaireId}/distribute`;
    }
    
    await axios.post(apiEndpoint, distributionData);
    ElMessage.success('问卷分发成功');
    distributeDialogVisible.value = false;
    fetchQuestionnaires();
  } catch (error) {
    console.error('问卷分发失败:', error);
    ElMessage.error('问卷分发失败');
  }
};

// 查看问卷结果
const handleViewResults = (questionnaire) => {
  currentQuestionnaire.value = questionnaire;
  resultsDialogVisible.value = true;
};

// 保存问卷
const saveQuestionnaire = async (questionnaireData) => {
  try {
    if (isEditing.value) {
      // 更新问卷
      await axios.put(`/api/questionnaires/${questionnaireData.id}`, questionnaireData);
      ElMessage.success('问卷更新成功');
    } else {
      // 创建问卷
      await axios.post('/api/questionnaires', questionnaireData);
      ElMessage.success('问卷创建成功');
    }

    dialogVisible.value = false;
    fetchQuestionnaires();
  } catch (error) {
    console.error('保存问卷失败:', error);
    ElMessage.error('保存问卷失败');
  }
};

// 处理对话框关闭
const handleDialogClose = (done) => {
  ElMessageBox.confirm(
    '确定要关闭吗？未保存的更改将会丢失',
    '关闭确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    done();
  }).catch(() => {
    // 用户取消关闭
  });
};

// 预览问卷
const handlePreview = async (questionnaire) => {
  if (!questionnaire || !questionnaire.id) {
    ElMessage.error('问卷信息不完整，无法预览');
    return;
  }
  try {
    let questionnaireData;
    
    // 标准问卷和用户问卷的处理方式不同
    if (questionnaire.is_standard) {
      if (!questionnaire.template_key) {
        console.error('标准问卷缺少template_key:', questionnaire);
      }
      // 标准问卷调用专门的API
      try {
        const apiPath = `/api/clinical-scales/standard-questionnaires/${questionnaire.template_key || questionnaire.id}`;
        const response = await axios.get(apiPath);
        questionnaireData = response.data.data || response.data;
      } catch (error) {
        console.warn('获取标准问卷详情失败，使用现有数据:', error);
        questionnaireData = questionnaire;
      }
    } else {
      // 用户创建的问卷，尝试获取详细数据
      try {
        const response = await axios.get(`/api/questionnaires/${questionnaire.id}`);
        questionnaireData = response.data.data || response.data;
      } catch (error) {
        console.warn('获取问卷详情失败，使用现有数据:', error);
        questionnaireData = questionnaire;
      }
    }
    
    // 适配问卷数据格式
    currentAssessment.value = {
      id: questionnaireData.id,
      title: questionnaireData.name || questionnaireData.title || '未命名',
      description: questionnaireData.description || '',
      category: questionnaireData.category || '',
      questionnaire_type: questionnaireData.questionnaire_type || 'general',
      status: questionnaireData.status || 'draft',
      questions: questionnaireData.questions || [],
      instructions: questionnaireData.instructions || '',
      updated_at: questionnaireData.updated_at || '',
      revision_reason: questionnaireData.revision_reason || ''
    };
    
    // 打开简单预览对话框（不显示审核按钮）
    simplePreviewDialogVisible.value = true;
  } catch (error) {
    console.error('预览问卷失败:', error);
    ElMessage.error('预览问卷失败');
  }
};

// 审核问卷
const handleReview = async (questionnaire) => {
  if (!questionnaire || !questionnaire.id) {
    ElMessage.error('问卷信息不完整，无法进行审核');
    return;
  }
  try {
    // 直接使用传入的问卷数据，不再调用模板API
    console.log('审核问卷, ID:', questionnaire.id, '类型:', typeof questionnaire.id);
    
    // 确保ID是数字类型
    let questionnaireId = questionnaire.id;
    if (typeof questionnaireId === 'string') {
      questionnaireId = parseInt(questionnaireId, 10);
    }
    
    // 直接使用问卷数据，不再调用模板API
    currentAssessment.value = {
      id: questionnaireId,
      title: questionnaire.name || questionnaire.title || '未命名',
      description: questionnaire.description || questionnaire.notes || '',
      category: questionnaire.category || '',
      questionnaire_type: questionnaire.questionnaire_type || 'general',
      status: questionnaire.status || 'draft',
      questions: questionnaire.questions || [],
      instructions: questionnaire.instructions || '',
      updated_at: questionnaire.updated_at || '',
      revision_reason: questionnaire.revision_reason || ''
    };
    
    console.log('审核使用的问卷数据:', currentAssessment.value);
    previewDialogVisible.value = true;
  } catch (error) {
    console.error('处理问卷审核数据失败:', error, questionnaire);
    ElMessage.error('处理问卷审核数据失败');
  }
};

// 字段适配函数，兼容标准问卷结构
function adaptQuestionnaireDetail(raw) {
  let questions = [];
  if (Array.isArray(raw.questions)) {
    questions = raw.questions.map(q => ({
      id: q.question_id || q.id,
      question_text: q.question_text || q.text,
      question_type: q.question_type,
      required: q.is_required ?? q.required ?? true,
      options: q.options || [],
      order: q.order || 0,
      scale_max: q.scale_max,
      scale_min_label: q.scale_min_label,
      scale_max_label: q.scale_max_label,
      score_values: q.score_values
    }));
  } else {
    console.warn('问卷questions字段异常:', raw.questions);
  }
  
  // 确保ID是数字类型
  let id = raw.id;
  if (id && typeof id === 'string') {
    id = parseInt(id, 10);
  }
  
  return {
    id: id, // 使用转换后的ID
    title: raw.name || raw.title || '未命名',
    description: raw.description || '',
    category: raw.category || '',
    questionnaire_type: raw.questionnaire_type || 'general',
    status: raw.status || 'draft',
    questions,
    instructions: raw.instructions || '',
    updated_at: raw.updated_at || '',
    revision_reason: raw.revision_reason || ''
  };
}

// 确认审核操作
const confirmReview = (approved) => {
  previewDialogVisible.value = false;
  
  if (!currentAssessment.value || !currentAssessment.value.id) {
    ElMessage.error('问卷信息不完整，无法进行审核操作');
    return;
  }
  
  // 获取正确的问卷ID
  const questionnaireId = currentAssessment.value.id;
  console.log('审核操作使用的问卷ID:', questionnaireId, '类型:', typeof questionnaireId);
  const questionnaireTitle = currentAssessment.value.title || '未命名问卷';
  
  if (approved) {
    ElMessageBox.confirm(
      `确认通过问卷"${questionnaireTitle}"审核？`,
      '审核确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'success'
      }
    ).then(async () => {
      try {
        console.log('发送审核通过请求, 问卷ID:', questionnaireId);
        const response = await axios.post(`/api/questionnaires/${questionnaireId}/review`, {
          status: 'published'
        });
        console.log('审核通过API响应:', response);
        
        ElMessage.success('问卷审核通过');
        
        // 更新本地状态
        const index = questionnaires.value.findIndex(q => q.id === questionnaireId);
        if (index !== -1) {
          console.log('更新本地问卷状态为published');
          questionnaires.value[index].status = 'published';
        }
        
        // 强制刷新列表
        await fetchQuestionnaires();
        
        // 如果当前在标准问卷tab，切换到全部tab
        if (activeTab.value === 'standard') {
          activeTab.value = 'all';
        }
      } catch (error) {
        console.error('问卷审核操作失败:', error);
        ElMessage.error('问卷审核操作失败');
      }
    });
  } else {
    // 打开退回原因输入框
    ElMessageBox.prompt('请输入退回原因', '退回问卷', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPlaceholder: '请输入退回原因...',
      inputValidator: (value) => {
        if (!value) {
          return '退回原因不能为空';
        }
        return true;
      }
    }).then(async ({ value }) => {
      try {
        console.log('发送返修请求, 问卷ID:', questionnaireId, '原因:', value);
        const response = await axios.post(`/api/questionnaires/${questionnaireId}/review`, {
          status: 'draft',
          revision_reason: value
        });
        console.log('返修API响应:', response);
        
        // 检查API是否正确处理了状态更新
        if (response && response.data) {
          console.log('返修后的状态数据:', response.data);
        }
        
        ElMessage.success('问卷已退回');
        
        // 直接强制更新当前记录状态
        const index = questionnaires.value.findIndex(q => q.id === questionnaireId);
        if (index !== -1) {
          console.log('更新本地问卷状态从', questionnaires.value[index].status, '到 draft');
          questionnaires.value[index].status = 'draft';
          // 保存返修原因
          questionnaires.value[index].revision_reason = value;
        } else {
          console.warn('未找到ID为', questionnaireId, '的问卷');
        }
        
        // 切换到全部tab
        activeTab.value = 'all';
        
        // 强制刷新整个问卷列表
        await fetchQuestionnaires();
        
        // 再次确认状态更新
        setTimeout(() => {
          const updatedIndex = questionnaires.value.findIndex(q => q.id === questionnaireId);
          if (updatedIndex !== -1) {
            console.log('刷新后问卷状态:', questionnaires.value[updatedIndex].status);
            if (questionnaires.value[updatedIndex].status !== 'draft') {
              // 强制设置为draft状态
              questionnaires.value[updatedIndex].status = 'draft';
              // 确保返修原因被保存
              questionnaires.value[updatedIndex].revision_reason = value;
              console.log('强制更新问卷状态为draft');
            }
          }
        }, 500);
      } catch (error) {
        console.error('问卷退回操作失败:', error);
        ElMessage.error('问卷退回操作失败');
      }
    }).catch(() => {
      // 用户取消操作
    });
  }
};

// 推送问卷
const handlePush = (questionnaire) => {
  ElMessageBox.confirm(
    `确定要推送问卷 "${questionnaire.title}" 吗？`,
    '推送确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    }
  ).then(async () => {
    try {
      await axios.post(`/api/questionnaires/${questionnaire.id}/push`);
      ElMessage.success('问卷推送成功');
      fetchQuestionnaires();
    } catch (error) {
      console.error('问卷推送失败:', error);
      ElMessage.error('问卷推送失败');
    }
  }).catch(() => {
    // 用户取消操作
  });
};
</script>

<style scoped>
.questionnaire-management {
  width: 100%;
}

.main-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.questionnaire-tabs {
  margin-top: 15px;
}

.questionnaire-preview-container {
  max-height: 60vh;
  overflow-y: auto;
}

.review-actions {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.preview-actions {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 10px;
}
</style>
