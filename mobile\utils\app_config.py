#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
应用程序配置模块
集中管理应用的配置项，包括API设置、重试策略、超时等
"""

import os
import logging
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent.parent

# API配置 - 统一使用聚合端口策略
API_CONFIG = {
    'BASE_URL': 'http://localhost:8006/api',  # 本地开发服务器作为主要URL
    'BACKUP_URL': 'http://127.0.0.1:8006/api',  # 本地回环地址作为备用
    'FALLBACK_URL': 'http://************/api',  # 公网服务器作为第三备选（当前不可用）
    'TIMEOUT': 30,  # 减少默认超时时间（秒）
    'RETRY_COUNT': 2,  # 减少重试次数以加快失败检测
    'RETRY_DELAY': 2,  # 重试间隔（秒）
    'MAX_FAILURES': 3,  # 减少进入降级模式的失败次数阈值
    'FAILURE_WINDOW': 60,  # 减少失败计数窗口期（秒）
    'HEALTH_CHECK_INTERVAL': 60,  # 减少健康检查间隔（秒）
    'PROXY': None,  # 禁用代理
}

# 日志配置
LOG_CONFIG = {
    'LOG_DIR': os.path.join(PROJECT_ROOT, 'logs'),
    'LOG_LEVEL': logging.INFO,
    'LOG_FORMAT': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'LOG_FILE': 'mobile_app.log',
    'MAX_BYTES': 10 * 1024 * 1024,  # 10MB
    'BACKUP_COUNT': 5,
}

# 本地存储配置
STORAGE_CONFIG = {
    'DATA_DIR': os.path.join(PROJECT_ROOT, 'mobile', 'data'),
    'CACHE_DIR': os.path.join(PROJECT_ROOT, 'mobile', 'cache'),
    'QUEUE_DIR': os.path.join(PROJECT_ROOT, 'mobile', 'queue'),
}

# 确保必要的目录存在
def ensure_directories():
    """确保所有必要的目录都存在"""
    directories = [
        LOG_CONFIG['LOG_DIR'],
        STORAGE_CONFIG['DATA_DIR'],
        STORAGE_CONFIG['CACHE_DIR'],
        STORAGE_CONFIG['QUEUE_DIR'],
    ]

    created_dirs = []
    failed_dirs = []

    for directory in directories:
        try:
            if not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
                created_dirs.append(directory)
        except Exception as e:
            failed_dirs.append((directory, str(e)))

    # 记录创建结果
    if created_dirs:
        print(f"已创建目录: {', '.join(created_dirs)}")

    if failed_dirs:
        print(f"警告: 部分目录创建失败:")
        for dir_path, error in failed_dirs:
            print(f"  - {dir_path}: {error}")

    return len(failed_dirs) == 0  # 返回是否全部成功

# 健康检查配置
HEALTH_CHECK_CONFIG = {
    'ENABLED': True,
    'INTERVAL': 60,  # 减少检查间隔到1分钟
    'TIMEOUT': 10,  # 减少健康检查超时时间
    'ACCEPTABLE_CODES': [200, 204],  # 可接受的响应状态码
    'CHECK_ENDPOINT': '/health',  # 修正健康检查端点路径
    'MAX_RETRIES': 1,  # 减少健康检查重试次数
}

# 用户配置
USER_CONFIG = {
    'TOKEN_EXPIRY': 24 * 60 * 60,  # Token有效期（秒）
    'PASSWORD_MIN_LENGTH': 8,
    'REQUIRE_SPECIAL_CHAR': True,
    'MAX_LOGIN_ATTEMPTS': 5,
}

# 初始化配置
ensure_directories()