import asyncio
from playwright.async_api import async_playwright
import json
import time

class SimpleFrontendTest:
    def __init__(self):
        self.frontend_url = "http://localhost:8080"
        self.backend_url = "http://localhost:8006"
        self.username = "admin"
        self.password = "admin123"
        self.test_user_id = "SM_008"
        self.api_calls = []
        
    async def run_tests(self):
        """运行简单的前端测试"""
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False, slow_mo=500)
            context = await browser.new_context()
            page = await context.new_page()
            
            try:
                # 设置网络监控
                await self.setup_network_monitoring(page)
                
                # 1. 访问前端首页
                print("=== 1. 访问前端首页 ===")
                await page.goto(self.frontend_url)
                await page.wait_for_load_state('networkidle')
                await asyncio.sleep(2)
                print(f"✓ 成功访问: {page.url}")
                
                # 2. 尝试登录
                await self.try_login(page)
                
                # 3. 直接访问问卷管理页面
                print("\n=== 3. 访问问卷管理页面 ===")
                questionnaire_url = f"{self.frontend_url}/admin/questionnaires"
                await page.goto(questionnaire_url)
                await page.wait_for_load_state('networkidle')
                await asyncio.sleep(3)
                print(f"✓ 访问问卷管理页面: {page.url}")
                
                # 4. 检查页面内容
                await self.check_page_content(page)
                
                # 5. 尝试访问健康资料页面
                print("\n=== 5. 访问健康资料页面 ===")
                health_url = f"{self.frontend_url}/admin/health-data"
                await page.goto(health_url)
                await page.wait_for_load_state('networkidle')
                await asyncio.sleep(3)
                print(f"✓ 访问健康资料页面: {page.url}")
                
                # 6. 查找用户搜索功能
                await self.test_user_search(page)
                
                # 7. 输出API调用统计
                await self.print_api_summary()
                
                print("\n=== 测试完成 ===")
                
            except Exception as e:
                print(f"测试过程中发生错误: {e}")
                await page.screenshot(path="error_screenshot.png")
                
            finally:
                await asyncio.sleep(5)  # 保持浏览器打开5秒
                await browser.close()
    
    async def setup_network_monitoring(self, page):
        """设置网络请求监控"""
        async def handle_request(request):
            if self.backend_url in request.url:
                self.api_calls.append({
                    'url': request.url,
                    'method': request.method,
                    'timestamp': time.time()
                })
                print(f"🌐 API请求: {request.method} {request.url}")
        
        async def handle_response(response):
            if self.backend_url in response.url:
                status = response.status
                if status >= 400:
                    print(f"❌ API错误: {status} {response.url}")
                else:
                    print(f"✅ API成功: {status} {response.url}")
        
        page.on("request", handle_request)
        page.on("response", handle_response)
    
    async def try_login(self, page):
        """尝试登录"""
        print("\n=== 2. 尝试登录 ===")
        
        # 检查是否已经登录
        if "login" not in page.url.lower():
            print("✓ 可能已经登录")
            return
        
        # 查找登录表单
        try:
            username_input = page.locator('input[type="text"], input[name="username"]').first
            password_input = page.locator('input[type="password"]').first
            login_button = page.locator('button:has-text("登录"), button[type="submit"]').first
            
            if await username_input.count() > 0:
                await username_input.fill(self.username)
                await password_input.fill(self.password)
                await login_button.click()
                
                await page.wait_for_load_state('networkidle')
                await asyncio.sleep(2)
                
                if "login" not in page.url.lower():
                    print("✓ 登录成功")
                else:
                    print("⚠️ 登录可能失败")
            else:
                print("⚠️ 未找到登录表单")
        except Exception as e:
            print(f"⚠️ 登录过程出错: {e}")
    
    async def check_page_content(self, page):
        """检查页面内容"""
        print("\n=== 4. 检查页面内容 ===")
        
        # 检查页面标题
        title = await page.title()
        print(f"页面标题: {title}")
        
        # 检查是否有表格
        tables = page.locator('table, .el-table')
        table_count = await tables.count()
        print(f"表格数量: {table_count}")
        
        # 检查是否有按钮
        buttons = page.locator('button, .el-button')
        button_count = await buttons.count()
        print(f"按钮数量: {button_count}")
        
        # 检查是否有标签页
        tabs = page.locator('.el-tabs__item, .el-tab-pane')
        tab_count = await tabs.count()
        print(f"标签页数量: {tab_count}")
        
        # 检查是否有问卷相关文本
        questionnaire_text = page.locator('text=问卷')
        questionnaire_count = await questionnaire_text.count()
        print(f"包含'问卷'的元素数量: {questionnaire_count}")
    
    async def test_user_search(self, page):
        """测试用户搜索功能"""
        print("\n=== 6. 测试用户搜索功能 ===")
        
        # 查找搜索框
        search_selectors = [
            'input[placeholder*="用户"]',
            'input[placeholder*="ID"]',
            'input[placeholder*="搜索"]',
            'input[type="search"]',
            '.search-input',
            '.el-input__inner'
        ]
        
        search_found = False
        for selector in search_selectors:
            try:
                search_input = page.locator(selector).first
                if await search_input.count() > 0:
                    print(f"✓ 找到搜索框: {selector}")
                    await search_input.fill(self.test_user_id)
                    await search_input.press('Enter')
                    await page.wait_for_load_state('networkidle')
                    await asyncio.sleep(2)
                    print(f"✓ 搜索用户: {self.test_user_id}")
                    search_found = True
                    break
            except Exception as e:
                continue
        
        if not search_found:
            print("⚠️ 未找到搜索框")
            
            # 尝试点击可能的用户相关按钮或链接
            user_selectors = [
                'text=用户',
                'text=患者',
                'text=SM_008',
                '[href*="SM_008"]',
                'button:has-text("查看")',
                'button:has-text("详情")'
            ]
            
            for selector in user_selectors:
                try:
                    element = page.locator(selector).first
                    if await element.count() > 0:
                        print(f"✓ 找到用户相关元素: {selector}")
                        await element.click()
                        await page.wait_for_load_state('networkidle')
                        await asyncio.sleep(2)
                        break
                except Exception as e:
                    continue
    
    async def print_api_summary(self):
        """输出API调用统计"""
        print("\n=== 7. API调用统计 ===")
        print(f"总API调用数: {len(self.api_calls)}")
        
        # 按类型分类
        auth_calls = [call for call in self.api_calls if 'auth' in call['url'] or 'login' in call['url']]
        questionnaire_calls = [call for call in self.api_calls if 'questionnaire' in call['url'] or 'aggregated' in call['url']]
        other_calls = [call for call in self.api_calls if call not in auth_calls and call not in questionnaire_calls]
        
        print(f"认证相关: {len(auth_calls)} 个")
        for call in auth_calls:
            print(f"  {call['method']} {call['url']}")
        
        print(f"问卷相关: {len(questionnaire_calls)} 个")
        for call in questionnaire_calls:
            print(f"  {call['method']} {call['url']}")
        
        print(f"其他API: {len(other_calls)} 个")
        for call in other_calls:
            print(f"  {call['method']} {call['url']}")
        
        # 检查聚合API调用
        aggregated_calls = [call for call in self.api_calls if 'aggregated' in call['url']]
        if aggregated_calls:
            print(f"\n✅ 检测到聚合API调用 ({len(aggregated_calls)} 个)")
        else:
            print(f"\n❌ 未检测到聚合API调用")

async def main():
    """主函数"""
    print("开始简单前端测试...")
    print(f"前端地址: http://localhost:8080")
    print(f"后端地址: http://localhost:8006")
    print(f"测试用户: admin")
    print(f"测试用户ID: SM_008")
    
    tester = SimpleFrontendTest()
    await tester.run_tests()

if __name__ == "__main__":
    asyncio.run(main())