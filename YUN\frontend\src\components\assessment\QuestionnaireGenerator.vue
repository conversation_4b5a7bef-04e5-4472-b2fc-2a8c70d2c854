<template>
  <div class="questionnaire-generator">
    <h2 class="text-xl font-bold mb-4">基于规则自动生成问卷</h2>
    
    <el-form :model="ruleForm" label-width="120px" :rules="rules" ref="ruleFormRef">
      <!-- 基本信息 -->
      <el-form-item label="问卷类型" prop="questionnaire_type">
        <el-select v-model="ruleForm.questionnaire_type" placeholder="请选择问卷类型">
          <el-option 
            v-for="item in questionnaireTypes" 
            :key="item.value" 
            :label="item.label" 
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="问卷名称" prop="name">
        <el-input v-model="ruleForm.name" placeholder="请输入问卷名称"></el-input>
      </el-form-item>
      
      <el-form-item label="问卷描述" prop="description">
        <el-input type="textarea" v-model="ruleForm.description" placeholder="请输入问卷描述"></el-input>
      </el-form-item>
      
      <el-form-item label="使用说明" prop="instructions">
        <el-input type="textarea" v-model="ruleForm.instructions" placeholder="请输入使用说明"></el-input>
      </el-form-item>
      
      <!-- 生成规则 -->
      <div class="bg-gray-50 p-4 rounded-md mb-4">
        <h3 class="text-lg font-medium mb-2">生成规则</h3>
        
        <el-form-item label="问题数量" prop="question_count">
          <el-input-number v-model="ruleForm.question_count" :min="1" :max="50"></el-input-number>
        </el-form-item>
        
        <el-form-item label="问题类型" prop="question_types">
          <el-checkbox-group v-model="ruleForm.question_types">
            <el-checkbox label="single_choice">单选题</el-checkbox>
            <el-checkbox label="multiple_choice">多选题</el-checkbox>
            <el-checkbox label="text">填空题</el-checkbox>
            <el-checkbox label="rating">评分题</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <el-form-item label="问题主题">
          <el-tag
            v-for="(topic, index) in ruleForm.topics"
            :key="index"
            closable
            @close="removeTopic(index)"
            class="mr-2 mb-2"
          >
            {{ topic }}
          </el-tag>
          
          <el-input
            v-if="inputTopicVisible"
            ref="topicInputRef"
            v-model="inputTopicValue"
            class="w-80"
            @keyup.enter="addTopic"
            @blur="addTopic"
          ></el-input>
          
          <el-button v-else @click="showTopicInput" plain>+ 添加主题</el-button>
        </el-form-item>
      </div>
      
      <!-- 问题模板 -->
      <div class="bg-gray-50 p-4 rounded-md mb-4">
        <h3 class="text-lg font-medium mb-2">问题模板 <small class="text-gray-500">(可选)</small></h3>
        <p class="text-sm text-gray-500 mb-4">如果提供问题模板，将优先使用模板生成问卷，否则根据上述规则自动生成</p>
        
        <div v-for="(template, index) in ruleForm.question_templates" :key="index" class="border p-3 rounded mb-3">
          <div class="flex justify-between items-center mb-2">
            <h4 class="font-medium">问题 {{ index + 1 }}</h4>
            <el-button type="danger" size="small" @click="removeQuestionTemplate(index)" icon="el-icon-delete">删除</el-button>
          </div>
          
          <el-form-item :label="'问题文本'" :prop="`question_templates.${index}.question_text`">
            <el-input v-model="template.question_text" placeholder="请输入问题文本"></el-input>
          </el-form-item>
          
          <el-form-item :label="'问题类型'" :prop="`question_templates.${index}.question_type`">
            <el-select v-model="template.question_type" placeholder="请选择问题类型">
              <el-option label="单选题" value="single_choice"></el-option>
              <el-option label="多选题" value="multiple_choice"></el-option>
              <el-option label="填空题" value="text"></el-option>
              <el-option label="评分题" value="rating"></el-option>
            </el-select>
          </el-form-item>
          
          <div v-if="['single_choice', 'multiple_choice'].includes(template.question_type)">
            <el-form-item :label="'选项'">
              <div v-for="(option, optIndex) in template.options" :key="optIndex" class="flex items-center mb-2">
                <el-input v-model="option.label" placeholder="选项文本" class="mr-2"></el-input>
                <el-input v-model="option.value" placeholder="选项值" style="width: 100px" class="mr-2"></el-input>
                <el-button type="danger" size="small" @click="removeOption(template, optIndex)" icon="el-icon-delete">删除</el-button>
              </div>
              <el-button type="primary" plain size="small" @click="addOption(template)">添加选项</el-button>
            </el-form-item>
          </div>
          
          <el-form-item :label="'是否必填'">
            <el-switch v-model="template.is_required"></el-switch>
          </el-form-item>
        </div>
        
        <el-button type="primary" plain @click="addQuestionTemplate">添加问题模板</el-button>
      </div>
      
      <el-form-item>
        <el-button type="primary" @click="generateQuestionnaire">生成问卷</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
    
    <!-- 结果展示 -->
    <div v-if="generatedQuestionnaire" class="mt-8 border-t pt-4">
      <h3 class="text-lg font-bold mb-4">生成结果</h3>
      
      <el-descriptions :column="1" border>
        <el-descriptions-item label="问卷ID">{{ generatedQuestionnaire.id }}</el-descriptions-item>
        <el-descriptions-item label="问卷名称">{{ generatedQuestionnaire.name }}</el-descriptions-item>
        <el-descriptions-item label="问卷类型">{{ generatedQuestionnaire.questionnaire_type }}</el-descriptions-item>
        <el-descriptions-item label="问题数量">{{ generatedQuestionnaire.questions.length }}</el-descriptions-item>
      </el-descriptions>
      
      <div class="mt-4">
        <el-button type="primary" @click="viewQuestionnaire">查看问卷</el-button>
        <el-button type="success" @click="editQuestionnaire">编辑问卷</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import { useRouter } from 'vue-router'
import axios from 'axios'

export default {
  name: 'QuestionnaireGenerator',
  
  setup() {
    const router = useRouter()
    const ruleFormRef = ref(null)
    
    // 问卷类型选项
    const questionnaireTypes = [
      { value: 'general', label: '通用问卷' },
      { value: 'health', label: '健康调查' },
      { value: 'satisfaction', label: '满意度调查' },
      { value: 'feedback', label: '反馈问卷' },
      { value: 'medical_history', label: '病史调查' },
    ]
    
    // 表单数据
    const ruleForm = reactive({
      questionnaire_type: 'general',
      name: '',
      description: '',
      instructions: '',
      question_count: 10,
      question_types: ['single_choice', 'multiple_choice', 'text'],
      topics: [],
      question_templates: []
    })
    
    // 表单验证规则
    const rules = {
      questionnaire_type: [
        { required: true, message: '请选择问卷类型', trigger: 'change' }
      ],
      name: [
        { required: true, message: '请输入问卷名称', trigger: 'blur' }
      ],
      question_count: [
        { required: true, message: '请设置问题数量', trigger: 'blur' }
      ],
      question_types: [
        { type: 'array', required: true, message: '请至少选择一种问题类型', trigger: 'change' }
      ]
    }
    
    // 主题输入相关
    const inputTopicVisible = ref(false)
    const inputTopicValue = ref('')
    const topicInputRef = ref(null)
    
    // 生成的问卷
    const generatedQuestionnaire = ref(null)
    
    // 显示主题输入框
    const showTopicInput = () => {
      inputTopicVisible.value = true
      // 等待DOM更新后聚焦
      setTimeout(() => {
        topicInputRef.value.focus()
      })
    }
    
    // 添加主题
    const addTopic = () => {
      if (inputTopicValue.value) {
        ruleForm.topics.push(inputTopicValue.value)
        inputTopicValue.value = ''
      }
      inputTopicVisible.value = false
    }
    
    // 移除主题
    const removeTopic = (index) => {
      ruleForm.topics.splice(index, 1)
    }
    
    // 添加问题模板
    const addQuestionTemplate = () => {
      ruleForm.question_templates.push({
        question_text: '',
        question_type: 'single_choice',
        options: [
          { value: 'A', label: '选项A' },
          { value: 'B', label: '选项B' },
          { value: 'C', label: '选项C' }
        ],
        is_required: true
      })
    }
    
    // 移除问题模板
    const removeQuestionTemplate = (index) => {
      ruleForm.question_templates.splice(index, 1)
    }
    
    // 添加选项
    const addOption = (template) => {
      const newValue = String.fromCharCode(65 + template.options.length) // A, B, C...
      template.options.push({ value: newValue, label: `选项${newValue}` })
    }
    
    // 移除选项
    const removeOption = (template, index) => {
      template.options.splice(index, 1)
    }
    
    // 生成问卷
    const generateQuestionnaire = () => {
      ruleFormRef.value.validate(async (valid) => {
        if (valid) {
          const loading = ElLoading.service({
            lock: true,
            text: '正在生成问卷...',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          
          try {
            const response = await axios.post('/api/questionnaires/generate', ruleForm)
            if (response.data.status === 'success') {
              ElMessage.success('问卷生成成功')
              generatedQuestionnaire.value = response.data.data
            } else {
              ElMessage.error(response.data.message || '问卷生成失败')
            }
          } catch (error) {
            console.error('生成问卷出错:', error)
            ElMessage.error(error.response?.data?.detail || '生成问卷失败，请稍后重试')
          } finally {
            loading.close()
          }
        } else {
          ElMessage.warning('请完善表单信息')
          return false
        }
      })
    }
    
    // 重置表单
    const resetForm = () => {
      ruleFormRef.value.resetFields()
      ruleForm.topics = []
      ruleForm.question_templates = []
      generatedQuestionnaire.value = null
    }
    
    // 查看问卷
    const viewQuestionnaire = () => {
      if (generatedQuestionnaire.value) {
        router.push(`/questionnaires/${generatedQuestionnaire.value.id}`)
      }
    }
    
    // 编辑问卷
    const editQuestionnaire = () => {
      if (generatedQuestionnaire.value) {
        router.push(`/questionnaires/${generatedQuestionnaire.value.id}/edit`)
      }
    }
    
    return {
      ruleFormRef,
      questionnaireTypes,
      ruleForm,
      rules,
      inputTopicVisible,
      inputTopicValue,
      topicInputRef,
      generatedQuestionnaire,
      showTopicInput,
      addTopic,
      removeTopic,
      addQuestionTemplate,
      removeQuestionTemplate,
      addOption,
      removeOption,
      generateQuestionnaire,
      resetForm,
      viewQuestionnaire,
      editQuestionnaire
    }
  }
}
</script>

<style scoped>
.questionnaire-generator {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
}
</style>