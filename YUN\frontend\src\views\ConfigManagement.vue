<template>
  <div class="config-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><Setting /></el-icon>
        配置管理
      </h1>
      <p class="page-description">管理系统配置文件、环境变量和应用设置</p>
    </div>

    <!-- 配置概览 -->
    <el-row :gutter="20" class="config-overview">
      <el-col :span="6">
        <el-card class="overview-card" shadow="hover">
          <div class="overview-content">
            <div class="overview-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="overview-info">
              <h3>{{ totalConfigs }}</h3>
              <p>配置文件</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card" shadow="hover">
          <div class="overview-content">
            <div class="overview-icon env">
              <el-icon><Key /></el-icon>
            </div>
            <div class="overview-info">
              <h3>{{ totalEnvVars }}</h3>
              <p>环境变量</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card" shadow="hover">
          <div class="overview-content">
            <div class="overview-icon modified">
              <el-icon><EditPen /></el-icon>
            </div>
            <div class="overview-info">
              <h3>{{ modifiedConfigs }}</h3>
              <p>已修改</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card" shadow="hover">
          <div class="overview-content">
            <div class="overview-icon backup">
              <el-icon><FolderOpened /></el-icon>
            </div>
            <div class="overview-info">
              <h3>{{ totalBackups }}</h3>
              <p>备份文件</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 配置文件管理 -->
    <el-card class="config-files" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>配置文件</span>
          <div class="header-actions">
            <el-input
              v-model="searchText"
              placeholder="搜索配置文件"
              size="small"
              style="width: 200px; margin-right: 10px;"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            
            <el-select v-model="categoryFilter" size="small" style="width: 120px; margin-right: 10px;">
              <el-option label="全部分类" value="all" />
              <el-option label="应用配置" value="app" />
              <el-option label="数据库" value="database" />
              <el-option label="缓存" value="cache" />
              <el-option label="安全" value="security" />
            </el-select>
            
            <el-button 
              type="primary" 
              size="small" 
              @click="refreshConfigs"
              :loading="refreshLoading"
            >
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            
            <el-button 
              type="success" 
              size="small" 
              @click="showCreateDialog"
            >
              <el-icon><Plus /></el-icon>
              新建配置
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table :data="filteredConfigs" stripe style="width: 100%">
        <el-table-column prop="name" label="配置名称" width="200">
          <template #default="{ row }">
            <div class="config-name">
              <div class="config-icon" :class="row.category">
                <el-icon><component :is="getCategoryIcon(row.category)" /></el-icon>
              </div>
              <div>
                <div class="name">{{ row.name }}</div>
                <div class="path">{{ row.path }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="category" label="分类" width="100">
          <template #default="{ row }">
            <el-tag :type="getCategoryType(row.category)" size="small">
              {{ getCategoryText(row.category) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="size" label="文件大小" width="100" />
        
        <el-table-column prop="lastModified" label="最后修改" width="150">
          <template #default="{ row }">
            {{ formatTime(row.lastModified) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="description" label="描述" min-width="200" />
        
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button-group size="small">
              <el-button type="primary" @click="editConfig(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              
              <el-button type="info" @click="viewConfig(row)">
                <el-icon><View /></el-icon>
                查看
              </el-button>
              
              <el-button type="warning" @click="backupConfig(row)">
                <el-icon><Download /></el-icon>
                备份
              </el-button>
              
              <el-button type="danger" @click="deleteConfig(row)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 环境变量管理 -->
    <el-card class="env-variables" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>环境变量</span>
          <div class="header-actions">
            <el-button 
              type="success" 
              size="small" 
              @click="showEnvDialog"
            >
              <el-icon><Plus /></el-icon>
              添加变量
            </el-button>
            
            <el-button 
              type="warning" 
              size="small" 
              @click="exportEnvVars"
            >
              <el-icon><Download /></el-icon>
              导出
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table :data="envVariables" stripe style="width: 100%">
        <el-table-column prop="key" label="变量名" width="200" />
        
        <el-table-column prop="value" label="值" min-width="300">
          <template #default="{ row }">
            <div class="env-value">
              <span v-if="!row.sensitive">{{ row.value }}</span>
              <span v-else class="sensitive-value">{{ showSensitive[row.key] ? row.value : '••••••••' }}</span>
              <el-button 
                v-if="row.sensitive"
                link 
                size="small"
                @click="toggleSensitive(row.key)"
              >
                <el-icon><component :is="showSensitive[row.key] ? 'Hide' : 'View'" /></el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="description" label="描述" min-width="200" />
        
        <el-table-column prop="lastModified" label="最后修改" width="150">
          <template #default="{ row }">
            {{ formatTime(row.lastModified) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button-group size="small">
              <el-button type="primary" @click="editEnvVar(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              
              <el-button type="danger" @click="deleteEnvVar(row)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 配置编辑对话框 -->
    <el-dialog 
      v-model="editDialogVisible" 
      :title="editMode === 'create' ? '新建配置文件' : '编辑配置文件'"
      width="80%"
      top="5vh"
    >
      <el-form :model="currentConfig" :rules="configRules" ref="configForm" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="配置名称" prop="name">
              <el-input v-model="currentConfig.name" :disabled="editMode === 'edit'" />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="分类" prop="category">
              <el-select v-model="currentConfig.category" style="width: 100%">
                <el-option label="应用配置" value="app" />
                <el-option label="数据库" value="database" />
                <el-option label="缓存" value="cache" />
                <el-option label="安全" value="security" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="文件路径" prop="path">
              <el-input v-model="currentConfig.path" />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="格式" prop="format">
              <el-select v-model="currentConfig.format" style="width: 100%">
                <el-option label="JSON" value="json" />
                <el-option label="YAML" value="yaml" />
                <el-option label="INI" value="ini" />
                <el-option label="ENV" value="env" />
                <el-option label="XML" value="xml" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="描述" prop="description">
          <el-input v-model="currentConfig.description" type="textarea" :rows="2" />
        </el-form-item>
        
        <el-form-item label="配置内容" prop="content">
          <div class="config-editor">
            <div class="editor-toolbar">
              <el-button-group size="small">
                <el-button @click="formatContent">
                  <el-icon><MagicStick /></el-icon>
                  格式化
                </el-button>
                <el-button @click="validateContent">
                  <el-icon><CircleCheck /></el-icon>
                  验证
                </el-button>
                <el-button @click="previewContent">
                  <el-icon><View /></el-icon>
                  预览
                </el-button>
              </el-button-group>
            </div>
            
            <el-input 
              v-model="currentConfig.content"
              type="textarea"
              :rows="15"
              placeholder="请输入配置内容"
              style="font-family: 'Courier New', monospace;"
            />
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveConfig" :loading="saveLoading">
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 配置查看对话框 -->
    <el-dialog 
      v-model="viewDialogVisible" 
      :title="viewConfig?.name + ' - 配置内容'"
      width="70%"
      top="5vh"
    >
      <div class="config-viewer">
        <div class="viewer-toolbar">
          <el-button-group size="small">
            <el-button @click="downloadConfig">
              <el-icon><Download /></el-icon>
              下载
            </el-button>
            <el-button @click="copyContent">
              <el-icon><CopyDocument /></el-icon>
              复制
            </el-button>
          </el-button-group>
        </div>
        
        <div class="config-content">
          <pre>{{ viewConfigContent }}</pre>
        </div>
      </div>
    </el-dialog>

    <!-- 环境变量编辑对话框 -->
    <el-dialog 
      v-model="envDialogVisible" 
      :title="envEditMode === 'create' ? '添加环境变量' : '编辑环境变量'"
      width="50%"
    >
      <el-form :model="currentEnvVar" :rules="envRules" ref="envForm" label-width="100px">
        <el-form-item label="变量名" prop="key">
          <el-input v-model="currentEnvVar.key" :disabled="envEditMode === 'edit'" />
        </el-form-item>
        
        <el-form-item label="值" prop="value">
          <el-input 
            v-model="currentEnvVar.value" 
            :type="currentEnvVar.sensitive ? 'password' : 'text'"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input v-model="currentEnvVar.description" type="textarea" :rows="2" />
        </el-form-item>
        
        <el-form-item>
          <el-checkbox v-model="currentEnvVar.sensitive">敏感信息</el-checkbox>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="envDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveEnvVar" :loading="saveLoading">
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Setting,
  Document,
  Key,
  EditPen,
  FolderOpened,
  Search,
  Refresh,
  Plus,
  Edit,
  View,
  Download,
  Delete,
  MagicStick,
  CircleCheck,
  CopyDocument,
  Hide
} from '@element-plus/icons-vue';
import axios from 'axios';

// 响应式数据
const searchText = ref('');
const categoryFilter = ref('all');
const refreshLoading = ref(false);
const editDialogVisible = ref(false);
const viewDialogVisible = ref(false);
const envDialogVisible = ref(false);
const editMode = ref('create');
const envEditMode = ref('create');
const saveLoading = ref(false);
const showSensitive = ref({});
const viewConfigContent = ref('');

const currentConfig = reactive({
  name: '',
  category: 'app',
  path: '',
  format: 'json',
  description: '',
  content: ''
});

const currentEnvVar = reactive({
  key: '',
  value: '',
  description: '',
  sensitive: false
});

const configs = ref([
  {
    id: 1,
    name: 'app.json',
    category: 'app',
    path: '/config/app.json',
    format: 'json',
    status: 'active',
    size: '2.5KB',
    lastModified: new Date().toISOString(),
    description: '应用主配置文件',
    content: '{\n  "app_name": "Health Management System",\n  "version": "1.0.0",\n  "debug": false\n}'
  },
  {
    id: 2,
    name: 'database.yaml',
    category: 'database',
    path: '/config/database.yaml',
    format: 'yaml',
    status: 'active',
    size: '1.8KB',
    lastModified: new Date(Date.now() - 86400000).toISOString(),
    description: '数据库连接配置',
    content: 'database:\n  host: localhost\n  port: 5432\n  name: health_db\n  user: admin'
  },
  {
    id: 3,
    name: 'redis.conf',
    category: 'cache',
    path: '/config/redis.conf',
    format: 'ini',
    status: 'modified',
    size: '3.2KB',
    lastModified: new Date(Date.now() - 3600000).toISOString(),
    description: 'Redis缓存配置',
    content: 'port 6379\nmaxmemory 256mb\nmaxmemory-policy allkeys-lru'
  }
]);

const envVariables = ref([
  {
    key: 'DATABASE_URL',
    value: 'postgresql://user:pass@localhost:5432/health_db',
    description: '数据库连接字符串',
    sensitive: true,
    lastModified: new Date().toISOString()
  },
  {
    key: 'SECRET_KEY',
    value: 'your-secret-key-here',
    description: '应用密钥',
    sensitive: true,
    lastModified: new Date(Date.now() - 86400000).toISOString()
  },
  {
    key: 'DEBUG',
    value: 'false',
    description: '调试模式开关',
    sensitive: false,
    lastModified: new Date(Date.now() - 3600000).toISOString()
  },
  {
    key: 'LOG_LEVEL',
    value: 'INFO',
    description: '日志级别',
    sensitive: false,
    lastModified: new Date(Date.now() - 7200000).toISOString()
  }
]);

// 表单验证规则
const configRules = {
  name: [{ required: true, message: '请输入配置名称', trigger: 'blur' }],
  category: [{ required: true, message: '请选择分类', trigger: 'change' }],
  path: [{ required: true, message: '请输入文件路径', trigger: 'blur' }],
  format: [{ required: true, message: '请选择格式', trigger: 'change' }],
  content: [{ required: true, message: '请输入配置内容', trigger: 'blur' }]
};

const envRules = {
  key: [{ required: true, message: '请输入变量名', trigger: 'blur' }],
  value: [{ required: true, message: '请输入值', trigger: 'blur' }]
};

// 计算属性
const filteredConfigs = computed(() => {
  let filtered = configs.value;
  
  // 分类过滤
  if (categoryFilter.value !== 'all') {
    filtered = filtered.filter(config => config.category === categoryFilter.value);
  }
  
  // 搜索过滤
  if (searchText.value) {
    const search = searchText.value.toLowerCase();
    filtered = filtered.filter(config => 
      config.name.toLowerCase().includes(search) ||
      config.description.toLowerCase().includes(search)
    );
  }
  
  return filtered;
});

const totalConfigs = computed(() => configs.value.length);
const totalEnvVars = computed(() => envVariables.value.length);
const modifiedConfigs = computed(() => configs.value.filter(c => c.status === 'modified').length);
const totalBackups = computed(() => 15); // 模拟数据

// 方法
const getCategoryIcon = (category) => {
  const iconMap = {
    'app': 'Setting',
    'database': 'DataBase',
    'cache': 'Service',
    'security': 'Lock'
  };
  return iconMap[category] || 'Document';
};

const getCategoryType = (category) => {
  const typeMap = {
    'app': 'primary',
    'database': 'success',
    'cache': 'warning',
    'security': 'danger'
  };
  return typeMap[category] || 'info';
};

const getCategoryText = (category) => {
  const textMap = {
    'app': '应用',
    'database': '数据库',
    'cache': '缓存',
    'security': '安全'
  };
  return textMap[category] || '其他';
};

const getStatusType = (status) => {
  const typeMap = {
    'active': 'success',
    'modified': 'warning',
    'inactive': 'info',
    'error': 'danger'
  };
  return typeMap[status] || 'info';
};

const getStatusText = (status) => {
  const textMap = {
    'active': '正常',
    'modified': '已修改',
    'inactive': '未激活',
    'error': '错误'
  };
  return textMap[status] || '未知';
};

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleString();
};

const refreshConfigs = async () => {
  refreshLoading.value = true;
  try {
    const response = await axios.get('/api/management/configs');
    configs.value = response.data.configs || [];
    
    const envResponse = await axios.get('/api/management/configs/env');
    envVariables.value = envResponse.data.variables || [];
    
    ElMessage.success('配置已刷新');
  } catch (error) {
    console.error('刷新配置失败:', error);
    ElMessage.error('刷新配置失败');
  } finally {
    refreshLoading.value = false;
  }
};

const showCreateDialog = () => {
  editMode.value = 'create';
  Object.assign(currentConfig, {
    name: '',
    category: 'app',
    path: '',
    format: 'json',
    description: '',
    content: ''
  });
  editDialogVisible.value = true;
};

const editConfig = (config) => {
  editMode.value = 'edit';
  Object.assign(currentConfig, { ...config });
  editDialogVisible.value = true;
};

const viewConfig = async (config) => {
  try {
    const response = await axios.get(`/api/management/configs/${config.id}/content`);
    viewConfigContent.value = response.data.content || config.content;
    viewDialogVisible.value = true;
  } catch (error) {
    console.error('获取配置内容失败:', error);
    viewConfigContent.value = config.content;
    viewDialogVisible.value = true;
  }
};

const saveConfig = async () => {
  try {
    saveLoading.value = true;
    
    if (editMode.value === 'create') {
      const response = await axios.post('/api/management/configs', currentConfig);
      configs.value.push({
        ...currentConfig,
        id: response.data.id,
        status: 'active',
        size: '0KB',
        lastModified: new Date().toISOString()
      });
      ElMessage.success('配置文件创建成功');
    } else {
      await axios.put(`/api/management/configs/${currentConfig.id}`, currentConfig);
      const index = configs.value.findIndex(c => c.id === currentConfig.id);
      if (index !== -1) {
        configs.value[index] = {
          ...currentConfig,
          status: 'modified',
          lastModified: new Date().toISOString()
        };
      }
      ElMessage.success('配置文件保存成功');
    }
    
    editDialogVisible.value = false;
  } catch (error) {
    console.error('保存配置失败:', error);
    ElMessage.error('保存配置失败');
  } finally {
    saveLoading.value = false;
  }
};

const backupConfig = async (config) => {
  try {
    await axios.post(`/api/management/configs/${config.id}/backup`);
    ElMessage.success(`${config.name} 备份成功`);
  } catch (error) {
    console.error('备份配置失败:', error);
    ElMessage.error('备份配置失败');
  }
};

const deleteConfig = async (config) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除配置文件 ${config.name} 吗？此操作不可恢复。`,
      '确认删除',
      { type: 'warning' }
    );
    
    await axios.delete(`/api/management/configs/${config.id}`);
    const index = configs.value.findIndex(c => c.id === config.id);
    if (index !== -1) {
      configs.value.splice(index, 1);
    }
    
    ElMessage.success('配置文件删除成功');
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除配置失败:', error);
      ElMessage.error('删除配置失败');
    }
  }
};

const formatContent = () => {
  try {
    if (currentConfig.format === 'json') {
      const parsed = JSON.parse(currentConfig.content);
      currentConfig.content = JSON.stringify(parsed, null, 2);
      ElMessage.success('JSON格式化成功');
    } else {
      ElMessage.info('当前格式不支持自动格式化');
    }
  } catch (error) {
    ElMessage.error('格式化失败：内容格式不正确');
  }
};

const validateContent = () => {
  try {
    if (currentConfig.format === 'json') {
      JSON.parse(currentConfig.content);
      ElMessage.success('JSON格式验证通过');
    } else {
      ElMessage.info('当前格式不支持验证');
    }
  } catch (error) {
    ElMessage.error('验证失败：JSON格式不正确');
  }
};

const previewContent = () => {
  ElMessage.info('预览功能开发中');
};

const downloadConfig = () => {
  const content = viewConfigContent.value;
  const blob = new Blob([content], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `config_${new Date().toISOString().slice(0, 10)}.txt`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

const copyContent = async () => {
  try {
    await navigator.clipboard.writeText(viewConfigContent.value);
    ElMessage.success('内容已复制到剪贴板');
  } catch (error) {
    ElMessage.error('复制失败');
  }
};

const showEnvDialog = () => {
  envEditMode.value = 'create';
  Object.assign(currentEnvVar, {
    key: '',
    value: '',
    description: '',
    sensitive: false
  });
  envDialogVisible.value = true;
};

const editEnvVar = (envVar) => {
  envEditMode.value = 'edit';
  Object.assign(currentEnvVar, { ...envVar });
  envDialogVisible.value = true;
};

const saveEnvVar = async () => {
  try {
    saveLoading.value = true;
    
    if (envEditMode.value === 'create') {
      await axios.post('/api/management/configs/env', currentEnvVar);
      envVariables.value.push({
        ...currentEnvVar,
        lastModified: new Date().toISOString()
      });
      ElMessage.success('环境变量添加成功');
    } else {
      await axios.put(`/api/management/configs/env/${currentEnvVar.key}`, currentEnvVar);
      const index = envVariables.value.findIndex(v => v.key === currentEnvVar.key);
      if (index !== -1) {
        envVariables.value[index] = {
          ...currentEnvVar,
          lastModified: new Date().toISOString()
        };
      }
      ElMessage.success('环境变量保存成功');
    }
    
    envDialogVisible.value = false;
  } catch (error) {
    console.error('保存环境变量失败:', error);
    ElMessage.error('保存环境变量失败');
  } finally {
    saveLoading.value = false;
  }
};

const deleteEnvVar = async (envVar) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除环境变量 ${envVar.key} 吗？`,
      '确认删除',
      { type: 'warning' }
    );
    
    await axios.delete(`/api/management/configs/env/${envVar.key}`);
    const index = envVariables.value.findIndex(v => v.key === envVar.key);
    if (index !== -1) {
      envVariables.value.splice(index, 1);
    }
    
    ElMessage.success('环境变量删除成功');
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除环境变量失败:', error);
      ElMessage.error('删除环境变量失败');
    }
  }
};

const toggleSensitive = (key) => {
  showSensitive.value[key] = !showSensitive.value[key];
};

const exportEnvVars = () => {
  const content = envVariables.value
    .map(v => `${v.key}=${v.value}`)
    .join('\n');
  
  const blob = new Blob([content], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `env_variables_${new Date().toISOString().slice(0, 10)}.env`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
  
  ElMessage.success('环境变量已导出');
};

// 生命周期
onMounted(() => {
  refreshConfigs();
});
</script>

<style scoped>
.config-management {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.config-overview {
  margin-bottom: 20px;
}

.overview-card {
  height: 100px;
}

.overview-content {
  display: flex;
  align-items: center;
  gap: 15px;
  height: 100%;
}

.overview-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  background-color: #409eff;
}

.overview-icon.env {
  background-color: #67c23a;
}

.overview-icon.modified {
  background-color: #e6a23c;
}

.overview-icon.backup {
  background-color: #909399;
}

.overview-info h3 {
  margin: 0 0 5px 0;
  font-size: 24px;
  color: #303133;
  font-weight: 600;
}

.overview-info p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.config-files,
.env-variables {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
}

.config-name {
  display: flex;
  align-items: center;
  gap: 10px;
}

.config-icon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
}

.config-icon.app {
  background-color: #409eff;
}

.config-icon.database {
  background-color: #67c23a;
}

.config-icon.cache {
  background-color: #e6a23c;
}

.config-icon.security {
  background-color: #f56c6c;
}

.config-name .name {
  font-weight: 500;
  color: #303133;
}

.config-name .path {
  font-size: 12px;
  color: #909399;
}

.env-value {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sensitive-value {
  font-family: monospace;
  letter-spacing: 2px;
}

.config-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.editor-toolbar {
  background-color: #f5f7fa;
  padding: 8px 12px;
  border-bottom: 1px solid #dcdfe6;
}

.config-viewer {
  height: 60vh;
  display: flex;
  flex-direction: column;
}

.viewer-toolbar {
  margin-bottom: 10px;
  display: flex;
  justify-content: flex-end;
}

.config-content {
  flex: 1;
  background-color: #1e1e1e;
  color: #d4d4d4;
  padding: 16px;
  border-radius: 4px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.config-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.dialog-footer {
  text-align: right;
}

@media (max-width: 768px) {
  .config-management {
    padding: 10px;
  }
  
  .config-overview {
    margin-bottom: 15px;
  }
  
  .overview-content {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }
  
  .header-actions {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .config-name {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>