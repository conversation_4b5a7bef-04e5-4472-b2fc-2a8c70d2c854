"""用药管理屏幕模块

提供用药记录的查看、添加、编辑、删除和提醒功能。
"""

from kivy.logger import Logger as KivyLogger
from kivymd.app import MDApp
from kivy.metrics import dp
from kivy.properties import StringProperty, ListProperty, ObjectProperty, BooleanProperty
from screens.base_screen import BaseScreen
from kivy.clock import Clock
from kivy.lang import Builder
from kivy.core.window import Window
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDIconButton, MDButtonText, MDButton
from kivymd.uix.label import MDLabel
from kivymd.uix.card import MDCard
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.list import MDList, MDListItem
from kivymd.uix.divider import MDDivider
from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText
from theme import AppTheme, AppMetrics, FontStyles
from utils.cloud_api import get_cloud_api
from utils.health_data_manager import get_health_data_manager
from kivymd.uix.dialog import MDDialog
from kivymd.uix.textfield import MDTextField
from kivymd.uix.selectioncontrol import MDCheckbox
from widgets.logo import HealthLogo
import os
import logging
from datetime import datetime, timedelta
from kivy.factory import Factory

KV = '''
<MedicationCard>:
    orientation: 'vertical'
    size_hint_y: None
    height: dp(160)
    md_bg_color: app.theme.CARD_BACKGROUND
    radius: [dp(12)]
    elevation: 2
    padding: [dp(16), dp(12), dp(16), dp(12)]
    spacing: dp(8)
    ripple_behavior: True
    on_release: root.on_card_click()

    # 药物名称和状态
    MDBoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(32)
        spacing: dp(8)

        MDLabel:
            text: root.name
            font_size: app.font_styles.TITLE_MEDIUM['font_size']
            font_name: app.font_styles.TITLE_MEDIUM['font_name']
            bold: app.font_styles.TITLE_MEDIUM['bold']
            theme_text_color: "Custom"
            text_color: app.theme.TEXT_PRIMARY
            halign: "left"
            valign: "center"

        Widget:
            size_hint_x: 1

        MDLabel:
            text: root.status_text
            font_size: app.font_styles.BODY_SMALL['font_size']
            font_name: app.font_styles.BODY_SMALL['font_name']
            theme_text_color: "Custom"
            text_color: app.theme.SUCCESS_COLOR if root.status_text == "正在使用" else app.theme.TEXT_SECONDARY
            size_hint_x: None
            width: self.texture_size[0]
            halign: "right"
            valign: "center"

    # 药物信息
    MDBoxLayout:
        orientation: 'vertical'
        size_hint_y: None
        height: dp(60)
        spacing: dp(4)

        MDLabel:
            text: f"剂量: {root.dosage}"
            font_size: app.font_styles.BODY_SMALL['font_size']
            font_name: app.font_styles.BODY_SMALL['font_name']
            theme_text_color: "Custom"
            text_color: app.theme.TEXT_SECONDARY
            size_hint_y: None
            height: dp(20)
            halign: "left"
            valign: "center"

        MDLabel:
            text: f"用法: {root.schedule}"
            font_size: app.font_styles.BODY_SMALL['font_size']
            font_name: app.font_styles.BODY_SMALL['font_name']
            theme_text_color: "Custom"
            text_color: app.theme.TEXT_SECONDARY
            size_hint_y: None
            height: dp(20)
            halign: "left"
            valign: "center"

        MDLabel:
            text: f"开始时间: {root.start_date}"
            font_size: app.font_styles.BODY_SMALL['font_size']
            font_name: app.font_styles.BODY_SMALL['font_name']
            theme_text_color: "Custom"
            text_color: app.theme.TEXT_SECONDARY
            size_hint_y: None
            height: dp(20)
            halign: "left"
            valign: "center"

    # 操作按钮区域
    MDBoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(40)
        spacing: dp(8)

        MDButton:
            style: "outlined"
            size_hint_x: 0.3
            on_release: root.on_remind()

            MDButtonIcon:
                icon: "bell"
                theme_icon_color: "Custom"
                icon_color: app.theme.PRIMARY_COLOR

            MDButtonText:
                text: "提醒"
                font_size: app.font_styles.BUTTON_SMALL['font_size']
                font_name: app.font_styles.BUTTON_SMALL['font_name']
                theme_text_color: "Custom"
                text_color: app.theme.PRIMARY_COLOR

        MDButton:
            style: "outlined"
            size_hint_x: 0.3
            on_release: root.on_stop()

            MDButtonIcon:
                icon: "stop"
                theme_icon_color: "Custom"
                icon_color: app.theme.WARNING_COLOR

            MDButtonText:
                text: "停药"
                font_size: app.font_styles.BUTTON_SMALL['font_size']
                font_name: app.font_styles.BUTTON_SMALL['font_name']
                theme_text_color: "Custom"
                text_color: app.theme.WARNING_COLOR

        MDButton:
            style: "outlined"
            size_hint_x: 0.3
            on_release: root.on_delete()

            MDButtonIcon:
                icon: "delete"
                theme_icon_color: "Custom"
                icon_color: app.theme.ERROR_COLOR

            MDButtonText:
                text: "删除"
                font_size: app.font_styles.BUTTON_SMALL['font_size']
                font_name: app.font_styles.BUTTON_SMALL['font_name']
                theme_text_color: "Custom"
                text_color: app.theme.ERROR_COLOR

<MedicationManagementScreen>:
    md_bg_color: app.theme.BACKGROUND_COLOR

    MDBoxLayout:
        orientation: 'vertical'
        spacing: dp(8)

        # 顶部应用栏
        MDBoxLayout:
            id: app_bar
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(56)
            md_bg_color: app.theme.PRIMARY_COLOR
            padding: [dp(4), dp(0), dp(4), dp(0)]

            MDIconButton:
                icon: "arrow-left"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.go_back()

            MDLabel:
                text: "用药管理"
                font_size: app.font_styles.TITLE_LARGE['font_size']
                font_name: app.font_styles.TITLE_LARGE['font_name']
                bold: app.font_styles.TITLE_LARGE['bold']
                theme_text_color: "Custom"
                text_color: app.theme.TEXT_LIGHT
                halign: "center"
                valign: "center"

            MDIconButton:
                icon: "plus"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.add_medication()

        # Logo区域
        HealthLogo:
            id: health_logo
            size_hint_y: None
            height: dp(120)
            logo_size: dp(80), dp(80)
            title_font_size: dp(18)
            subtitle_font_size: dp(14)

        # Tab切换区域 - 修复水平对齐问题
        MDCard:
            size_hint_y: None
            height: dp(56)
            md_bg_color: app.theme.SURFACE_COLOR
            radius: [dp(28)]
            elevation: 1
            padding: [dp(4), dp(4), dp(4), dp(4)]

            MDBoxLayout:
                orientation: 'horizontal'
                spacing: dp(4)

                MDButton:
                    id: current_tab_btn
                    style: "filled" if root.current_tab == 'current' else "outlined"
                    md_bg_color: app.theme.PRIMARY_COLOR if root.current_tab == 'current' else app.theme.SURFACE_COLOR
                    size_hint_x: 0.5
                    radius: [dp(24)]
                    on_release: root.switch_tab('current')

                    MDButtonIcon:
                        icon: "pill"
                        theme_icon_color: "Custom"
                        icon_color: app.theme.TEXT_LIGHT if root.current_tab == 'current' else app.theme.TEXT_PRIMARY

                    MDButtonText:
                        text: "目前用药"
                        theme_text_color: "Custom"
                        text_color: app.theme.TEXT_LIGHT if root.current_tab == 'current' else app.theme.TEXT_PRIMARY

                MDButton:
                    id: history_tab_btn
                    style: "filled" if root.current_tab == 'history' else "outlined"
                    md_bg_color: app.theme.PRIMARY_COLOR if root.current_tab == 'history' else app.theme.SURFACE_COLOR
                    size_hint_x: 0.5
                    radius: [dp(24)]
                    on_release: root.switch_tab('history')

                    MDButtonIcon:
                        icon: "history"
                        theme_icon_color: "Custom"
                        icon_color: app.theme.TEXT_LIGHT if root.current_tab == 'history' else app.theme.TEXT_PRIMARY

                    MDButtonText:
                        text: "既往用药"
                        theme_text_color: "Custom"
                        text_color: app.theme.TEXT_LIGHT if root.current_tab == 'history' else app.theme.TEXT_PRIMARY

        # 内容区域 - 修复对齐问题
        MDCard:
            size_hint_y: None
            height: dp(600)  # 固定高度确保对齐
            md_bg_color: app.theme.SURFACE_COLOR
            radius: [dp(12)]
            elevation: 1
            padding: [dp(16), dp(16), dp(16), dp(16)]

            MDBoxLayout:
                orientation: 'vertical'
                spacing: dp(12)

                # 目前用药内容
                MDBoxLayout:
                    id: current_content
                    orientation: 'vertical'
                    size_hint_y: None
                    height: dp(560) if root.current_tab == 'current' else 0
                    opacity: 1 if root.current_tab == 'current' else 0
                    spacing: dp(12)

                    MDScrollView:
                        do_scroll_x: False
                        do_scroll_y: True

                        MDBoxLayout:
                            id: medications_container
                            orientation: 'vertical'
                            size_hint_y: None
                            height: self.minimum_height
                            spacing: dp(12)

                # 既往用药内容
                MDBoxLayout:
                    id: history_content
                    orientation: 'vertical'
                    size_hint_y: None
                    height: dp(560) if root.current_tab == 'history' else 0
                    opacity: 1 if root.current_tab == 'history' else 0
                    spacing: dp(12)

                    # 搜索框
                    MDTextField:
                        id: history_search_field
                        hint_text: "搜索既往用药记录..."
                        size_hint_y: None
                        height: dp(56)
                        on_text: root.search_history_medications()

                        MDTextFieldHintText:
                            text: "搜索既往用药记录..."

                    # 既往用药列表
                    MDScrollView:
                        do_scroll_x: False
                        do_scroll_y: True

                        MDBoxLayout:
                            id: history_medications_container
                            orientation: 'vertical'
                            size_hint_y: None
                            height: self.minimum_height
                            spacing: dp(12)
'''

class MedicationCard(MDCard):
    """用药卡片组件"""
    name = StringProperty("")
    dosage = StringProperty("")
    schedule = StringProperty("")
    start_date = StringProperty("")
    status_text = StringProperty("正在使用")
    medication_data = ObjectProperty(None)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.ripple_behavior = True  # 启用涟漪效果
        self.ripple_duration_in_slow = 0.1  # 加快涟漪动画
        self.ripple_color = (0.8, 0.8, 0.8, 0.5)  # 设置涟漪颜色

    def on_card_click(self):
        """点击卡片查看详情"""
        app = MDApp.get_running_app()
        if hasattr(app.root.current_screen, 'show_medication_detail'):
            app.root.current_screen.show_medication_detail(self.medication_data)

    def on_remind(self):
        """设置用药提醒"""
        app = MDApp.get_running_app()
        if hasattr(app.root.current_screen, 'show_reminder_dialog'):
            app.root.current_screen.show_reminder_dialog(self.medication_data)

    def on_stop(self):
        """停药"""
        app = MDApp.get_running_app()
        if hasattr(app.root.current_screen, 'show_stop_confirmation'):
            app.root.current_screen.show_stop_confirmation(self.medication_data)

    def on_delete(self):
        """删除用药记录"""
        app = MDApp.get_running_app()
        if hasattr(app.root.current_screen, 'show_delete_confirmation'):
            app.root.current_screen.show_delete_confirmation(self.medication_data)

    def on_touch_up(self, touch):
        """处理触摸释放事件"""
        if self.collide_point(*touch.pos) and touch.is_mouse_scrolling is False:
            self.on_release()
        return super().on_touch_up(touch)

    def on_release(self):
        """点击卡片时调用"""
        self.on_card_click()

class MedicationManagementScreen(BaseScreen):
    """用药管理屏幕"""
    medications = ListProperty([])
    history_medications = ListProperty([])
    current_tab = StringProperty('current')
    dialog = None
    editing_medication = None

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.app = MDApp.get_running_app()
        self.medications = []
        self.history_medications = []
        Clock.schedule_once(self.init_ui, 0.2)
    
    def on_enter(self):
        super().on_enter()
        self.init_ui()
    
    def init_ui(self, dt=0):
        self.load_medications()
    
    def load_medications(self):
        """加载用药记录"""
        try:
            # 获取当前用户信息，避免触发不必要的认证警告
            custom_id = None
            access_token = None

            # 首先尝试从app.user_data获取
            if hasattr(self.app, 'user_data') and self.app.user_data:
                custom_id = self.app.user_data.get('custom_id')
                access_token = self.app.user_data.get('access_token')
                KivyLogger.info(f"[MedicationManagement] 从app.user_data获取用户信息: {custom_id}")

            # 如果没有获取到，尝试从认证管理器获取
            if not custom_id:
                try:
                    from utils.auth_manager import get_auth_manager
                    auth_manager = get_auth_manager()
                    user_info = auth_manager.get_current_user_info()
                    if user_info:
                        custom_id = user_info.get('custom_id')
                        access_token = user_info.get('access_token')
                        KivyLogger.info(f"[MedicationManagement] 从认证管理器获取用户信息: {custom_id}")
                except Exception as auth_error:
                    KivyLogger.warning(f"[MedicationManagement] 获取认证信息失败: {auth_error}")

            # 如果仍然没有用户信息，显示登录提示
            if not custom_id:
                KivyLogger.info("[MedicationManagement] 未找到用户信息，显示登录提示")
                if hasattr(self, 'ids') and 'medications_container' in self.ids:
                    self.ids.medications_container.clear_widgets()
                    empty_label = MDLabel(
                        text="暂无用药记录\n请先登录后查看",
                        halign="center",
                        theme_text_color="Secondary",
                        font_size=self.app.font_styles.BODY_MEDIUM['font_size'],
                        font_name=self.app.font_styles.BODY_MEDIUM['font_name'],
                        role="medium"
                    )
                    self.ids.medications_container.add_widget(empty_label)
                return

            if not hasattr(self, 'ids') or 'medications_container' not in self.ids:
                return

            try:
                self.ids.medications_container.clear_widgets()
            except ReferenceError:
                return

            # 静默设置云API认证信息（不触发警告）
            try:
                cloud_api = get_cloud_api()
                if cloud_api:
                    if access_token:
                        cloud_api.token = access_token
                        KivyLogger.info(f"[MedicationManagement] 已设置token认证")
                    if custom_id:
                        cloud_api.custom_id = custom_id
                        KivyLogger.info(f"[MedicationManagement] 已设置custom_id: {custom_id}")

                    # 这里可以调用后端API获取用药记录
                    # medications_response = cloud_api.get_medications(custom_id)
                    # if medications_response and medications_response.get('success'):
                    #     self.medications = medications_response.get('data', [])
                    #     KivyLogger.info(f"[MedicationManagement] 从后端获取到 {len(self.medications)} 条用药记录")
                    # else:
                    #     KivyLogger.info(f"[MedicationManagement] 后端暂无用药记录，使用本地数据")

            except Exception as api_error:
                KivyLogger.warning(f"[MedicationManagement] 设置API认证信息失败: {api_error}")

            # 使用示例数据（后续对接API时替换）
            self.medications = [
                {"id": "1", "name": "阿司匹林", "dosage": "100mg", "schedule": "每日1次，早餐后服用", "start_date": "2023-06-01", "end_date": "2023-12-31", "notes": "心血管疾病预防用药"},
                {"id": "2", "name": "维生素C", "dosage": "500mg", "schedule": "每日2次，早晚餐后服用", "start_date": "2023-05-15", "end_date": "2023-11-15", "notes": "增强免疫力"}
            ]

            if not self.medications:
                # 显示空状态
                empty_label = MDLabel(
                    text="暂无用药记录\n点击右上角的 + 按钮添加记录",
                    halign="center",
                    theme_text_color="Secondary",
                    font_size=self.app.font_styles.BODY_MEDIUM['font_size'],
                    font_name=self.app.font_styles.BODY_MEDIUM['font_name'],
                    role="medium"
                )
                self.ids.medications_container.add_widget(empty_label)
                return

            for med in self.medications:
                card = MedicationCard(
                    name=med["name"],
                    dosage=med['dosage'],
                    schedule=med['schedule'],
                    start_date=med.get('start_date', ''),
                    status_text="正在使用",
                    medication_data=med
                )
                self.ids.medications_container.add_widget(card)

            KivyLogger.info(f"[MedicationManagement] 成功加载 {len(self.medications)} 条用药记录")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 加载用药记录时出错: {e}")
            self.show_error(f"加载用药记录失败: {str(e)}")
    
    def go_back(self):
        """返回上一页"""
        try:
            # 直接返回主页，避免调用不存在的super().go_back()
            app = MDApp.get_running_app()
            app.root.current = 'homepage_screen'
        except Exception as e:
            KivyLogger.error(f"MedicationManagementScreen: 返回失败: {e}")
            app = MDApp.get_running_app()
            app.root.current = 'homepage_screen'
    
    def switch_tab(self, tab_name):
        """切换Tab"""
        try:
            KivyLogger.info(f"[MedicationManagement] 切换到Tab: {tab_name}")
            self.current_tab = tab_name

            # 更新内容显示 - 使用固定高度确保对齐
            if hasattr(self, 'ids'):
                current_content = self.ids.get('current_content')
                history_content = self.ids.get('history_content')

                if current_content and history_content:
                    if tab_name == 'current':
                        # 显示目前用药
                        current_content.height = dp(560)
                        current_content.opacity = 1
                        current_content.disabled = False

                        # 隐藏既往用药
                        history_content.height = 0
                        history_content.opacity = 0
                        history_content.disabled = True

                        # 加载数据
                        self.load_medications()
                        KivyLogger.info("[MedicationManagement] 显示目前用药Tab")

                    else:  # history
                        # 隐藏目前用药
                        current_content.height = 0
                        current_content.opacity = 0
                        current_content.disabled = True

                        # 显示既往用药
                        history_content.height = dp(560)
                        history_content.opacity = 1
                        history_content.disabled = False

                        # 加载数据
                        self.load_history_medications()
                        KivyLogger.info("[MedicationManagement] 显示既往用药Tab")

                else:
                    KivyLogger.warning("[MedicationManagement] 未找到Tab内容容器")

        except Exception as e:
            KivyLogger.error(f"MedicationManagementScreen: 切换Tab失败: {e}")

    def load_history_medications(self):
        """加载既往用药记录"""
        try:
            # 模拟既往用药数据
            self.history_medications = [
                {
                    "id": "h1",
                    "name": "头孢克肟",
                    "dosage": "200mg",
                    "schedule": "每日2次",
                    "start_date": "2023-05-01",
                    "stop_date": "2023-05-15",
                    "stop_reason": "疗程结束",
                    "status": "stopped"
                },
                {
                    "id": "h2",
                    "name": "布洛芬",
                    "dosage": "400mg",
                    "schedule": "每日3次",
                    "start_date": "2023-04-20",
                    "stop_date": "2023-04-25",
                    "stop_reason": "症状缓解",
                    "status": "stopped"
                }
            ]

            self.refresh_history_display()

        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 加载既往用药失败: {e}")

    def refresh_history_display(self):
        """刷新既往用药显示"""
        try:
            KivyLogger.info(f"[MedicationManagement] 开始刷新既往用药显示，数量: {len(self.history_medications)}")

            if not hasattr(self, 'ids') or 'history_medications_container' not in self.ids:
                KivyLogger.warning("[MedicationManagement] 未找到history_medications_container")
                return

            container = self.ids.history_medications_container
            container.clear_widgets()

            if not self.history_medications:
                empty_label = MDLabel(
                    text="暂无既往用药记录",
                    halign="center",
                    theme_text_color="Secondary",
                    font_size=self.app.font_styles.BODY_MEDIUM['font_size'],
                    font_name=self.app.font_styles.BODY_MEDIUM['font_name'],
                    role="medium",
                    size_hint_y=None,
                    height=dp(100)
                )
                container.add_widget(empty_label)
                KivyLogger.info("[MedicationManagement] 显示空状态")
                return

            # 按停药日期排序（最新的在前）
            sorted_medications = sorted(
                self.history_medications,
                key=lambda x: x.get('stop_date', ''),
                reverse=True
            )

            for index, med in enumerate(sorted_medications):
                try:
                    card = HistoryMedicationCard(
                        name=med["name"],
                        dosage=med['dosage'],
                        schedule=med['schedule'],
                        start_date=med.get("start_date", ""),
                        stop_date=med.get("stop_date", ""),
                        stop_reason=med.get("stop_reason", ""),
                        medication_data=med
                    )
                    container.add_widget(card)
                    KivyLogger.info(f"[MedicationManagement] 添加既往用药卡片: {med['name']}")
                except Exception as card_error:
                    KivyLogger.error(f"[MedicationManagement] 创建既往用药卡片失败: {card_error}")

            KivyLogger.info(f"[MedicationManagement] 既往用药显示刷新完成，共 {len(sorted_medications)} 条记录")

        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 刷新既往用药显示失败: {e}")

    def search_history_medications(self):
        """搜索既往用药记录"""
        try:
            if not hasattr(self, 'ids') or 'history_search_field' not in self.ids:
                return

            search_text = self.ids.history_search_field.text.strip().lower()

            if not search_text:
                self.refresh_history_display()
                return

            # 过滤既往用药记录
            filtered_medications = [
                med for med in self.history_medications
                if search_text in med["name"].lower() or
                   search_text in med.get("stop_reason", "").lower()
            ]

            # 临时更新显示
            original_history = self.history_medications
            self.history_medications = filtered_medications
            self.refresh_history_display()
            self.history_medications = original_history

        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 搜索既往用药失败: {e}")

    def add_medication(self):
        """添加用药记录"""
        self.editing_medication = None
        self.show_medication_dialog()
    
    def edit_medication(self, medication):
        """编辑用药记录"""
        self.editing_medication = medication
        self.show_medication_dialog(medication)
    
    def show_medication_dialog(self, medication=None):
        """显示用药记录对话框"""
        try:
            if self.dialog:
                self.dialog.dismiss(force=True)

            name_field = MDTextField(hint_text="药品名称", text=medication.get('name', '') if medication else '')
            dosage_field = MDTextField(hint_text="剂量", text=medication.get('dosage', '') if medication else '')
            schedule_field = MDTextField(hint_text="服用方式", text=medication.get('schedule', '') if medication else '')
            start_date_field = MDTextField(hint_text="开始日期(YYYY-MM-DD)", text=medication.get('start_date', '') if medication else '')
            end_date_field = MDTextField(hint_text="结束日期(YYYY-MM-DD)", text=medication.get('end_date', '') if medication else '')
            notes_field = MDTextField(hint_text="备注", text=medication.get('notes', '') if medication else '')

            content = MDBoxLayout(orientation='vertical', spacing=dp(8), size_hint_y=None, height=dp(320))
            content.add_widget(name_field)
            content.add_widget(dosage_field)
            content.add_widget(schedule_field)
            content.add_widget(start_date_field)
            content.add_widget(end_date_field)
            content.add_widget(notes_field)

            self.dialog = MDDialog(
                title="用药记录",
                type="custom",
                content_cls=content,
                actions=[
                    MDButton(
                        MDButtonText(text="取消"),
                        style="text",
                        on_release=lambda x: self.dialog.dismiss()
                    ),
                    MDButton(
                        MDButtonText(text="保存"),
                        style="text",
                        on_release=lambda x: self.save_medication(
                            name_field.text,
                            dosage_field.text,
                            schedule_field.text,
                            start_date_field.text,
                            end_date_field.text,
                            notes_field.text
                        )
                    )
                ]
            )
            self.dialog.open()

        except Exception as e:
            KivyLogger.error(f"MedicationManagementScreen: 显示用药对话框失败: {e}")
            self.show_error(f"显示对话框失败: {str(e)}")
    
    def save_medication(self, name, dosage, schedule, start_date, end_date, notes):
        """保存用药记录"""
        try:
            self.dialog.dismiss()

            if not name or not dosage or not schedule:
                self.show_error("药品名称、剂量和服用方式不能为空")
                return

            # 获取用户认证信息（静默方式）
            custom_id = None
            access_token = None

            if hasattr(self.app, 'user_data') and self.app.user_data:
                custom_id = self.app.user_data.get('custom_id')
                access_token = self.app.user_data.get('access_token')

            # 创建或更新用药记录
            medication = {
                "id": self.editing_medication.get('id') if self.editing_medication else str(len(self.medications) + 1),
                "name": name,
                "dosage": dosage,
                "schedule": schedule,
                "start_date": start_date,
                "end_date": end_date,
                "notes": notes
            }

            # 尝试保存到后端API（静默方式）
            try:
                if custom_id and access_token:
                    cloud_api = get_cloud_api()
                    if cloud_api:
                        cloud_api.token = access_token
                        cloud_api.custom_id = custom_id

                        # 这里可以调用后端API保存数据
                        # if self.editing_medication:
                        #     result = cloud_api.update_medication(medication['id'], medication)
                        # else:
                        #     result = cloud_api.create_medication(medication)
                        #
                        # if result and result.get('success'):
                        #     KivyLogger.info(f"[MedicationManagement] 成功保存到后端: {medication['name']}")
                        # else:
                        #     KivyLogger.info(f"[MedicationManagement] 后端保存失败，使用本地存储")

                        KivyLogger.info(f"[MedicationManagement] 已设置认证信息用于保存: {medication['name']}")
                else:
                    KivyLogger.info("[MedicationManagement] 无认证信息，仅保存到本地")
            except Exception as api_error:
                KivyLogger.warning(f"[MedicationManagement] API保存失败: {api_error}")

            # 本地数据更新
            if self.editing_medication:
                # 更新现有记录
                for i, med in enumerate(self.medications):
                    if med['id'] == medication['id']:
                        self.medications[i] = medication
                        break
            else:
                # 添加新记录
                self.medications.append(medication)

            self.show_info("保存成功")
            self.load_medications()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 保存用药记录失败: {e}")
            self.show_error(f"保存用药记录失败: {str(e)}")
    
    def show_stop_confirmation(self, medication):
        """显示停药确认对话框"""
        try:
            from kivymd.uix.dialog import MDDialog, MDDialogHeadlineText, MDDialogContentContainer, MDDialogButtonContainer
            from kivymd.uix.textfield import MDTextField, MDTextFieldHintText
            from kivymd.uix.button import MDButton, MDButtonText
            from kivymd.uix.boxlayout import MDBoxLayout
            from kivymd.uix.label import MDLabel
            from kivymd.uix.card import MDCard

            # 创建主内容容器
            content_container = MDDialogContentContainer(
                orientation="vertical",
                spacing=dp(20),
                size_hint_y=None,
                height=dp(280),
                padding=[dp(16), dp(16), dp(16), dp(16)]
            )

            # 药物信息卡片
            info_card = MDCard(
                orientation="vertical",
                size_hint_y=None,
                height=dp(100),
                md_bg_color=self.app.theme.SURFACE_CONTAINER_COLOR,
                radius=[dp(12)],
                elevation=1,
                padding=[dp(16), dp(12), dp(16), dp(12)],
                spacing=dp(8)
            )

            # 药物名称
            name_label = MDLabel(
                text=f"药物名称: {medication.get('name', '未知药物')}",
                theme_text_color="Primary",
                font_size=self.app.font_styles.TITLE_MEDIUM['font_size'],
                font_name=self.app.font_styles.TITLE_MEDIUM['font_name'],
                bold=True,
                size_hint_y=None,
                height=dp(24),
                halign="left"
            )
            info_card.add_widget(name_label)

            # 药物信息
            dosage_label = MDLabel(
                text=f"剂量: {medication.get('dosage', '')}",
                theme_text_color="Secondary",
                font_size=self.app.font_styles.BODY_MEDIUM['font_size'],
                size_hint_y=None,
                height=dp(20),
                halign="left"
            )
            info_card.add_widget(dosage_label)

            # 警告信息
            warning_label = MDLabel(
                text="⚠️ 停用后药物将移至既往用药记录",
                theme_text_color="Custom",
                text_color=self.app.theme.WARNING_COLOR,
                font_size=self.app.font_styles.BODY_SMALL['font_size'],
                size_hint_y=None,
                height=dp(20),
                halign="left"
            )
            info_card.add_widget(warning_label)

            content_container.add_widget(info_card)

            # 停药原因输入框
            reason_field = MDTextField(
                size_hint_y=None,
                height=dp(80),
                multiline=True,
                max_height=dp(120),
                mode="outlined"
            )
            reason_field.add_widget(MDTextFieldHintText(text="请详细说明停药原因（必填）"))
            content_container.add_widget(reason_field)

            # 创建按钮容器
            button_container = MDDialogButtonContainer(
                spacing=dp(12),
                padding=[dp(16), dp(8), dp(16), dp(16)]
            )

            # 取消按钮
            cancel_button = MDButton(
                style="outlined",
                size_hint_x=0.4,
                on_release=lambda x: self.stop_dialog.dismiss()
            )
            cancel_button.add_widget(MDButtonText(text="取消"))
            button_container.add_widget(cancel_button)

            # 确认按钮
            confirm_button = MDButton(
                style="filled",
                size_hint_x=0.6,
                md_bg_color=self.app.theme.WARNING_COLOR,
                on_release=lambda x: self.confirm_stop_medication(medication, reason_field.text, self.stop_dialog)
            )
            confirm_button.add_widget(MDButtonText(text="确认停药"))
            button_container.add_widget(confirm_button)

            # 创建对话框
            self.stop_dialog = MDDialog(
                MDDialogHeadlineText(text="停用药物确认"),
                content_container,
                button_container,
                size_hint=(0.9, None),
                height=dp(450)
            )

            self.stop_dialog.open()

        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 显示停药对话框失败: {e}")
            self.show_error("显示停药对话框失败")

    def confirm_stop_medication(self, medication, reason, dialog):
        """确认停药"""
        try:
            if not reason or not reason.strip():
                self.show_error("请输入停药原因")
                return

            dialog.dismiss()

            # 获取当前日期
            from datetime import datetime
            current_date = datetime.now().strftime('%Y-%m-%d')

            # 将药物从当前用药移至既往用药
            medication_copy = medication.copy()
            medication_copy['status'] = 'stopped'
            medication_copy['stop_reason'] = reason.strip()
            medication_copy['stop_date'] = current_date

            # 确保ID唯一性
            if 'id' in medication_copy:
                medication_copy['id'] = f"h_{medication_copy['id']}"

            # 从当前用药中移除
            self.medications = [med for med in self.medications if med['id'] != medication['id']]

            # 添加到既往用药
            self.history_medications.append(medication_copy)

            # 记录日志
            KivyLogger.info(f"[MedicationManagement] 停用药物: {medication.get('name')}, 原因: {reason.strip()}")
            KivyLogger.info(f"[MedicationManagement] 既往用药数量: {len(self.history_medications)}")

            # 刷新显示
            self.load_medications()

            # 强制刷新既往用药显示
            self.refresh_history_display()

            # 切换到既往用药Tab显示结果
            self.switch_tab('history')

            self.show_info(f"已停用 {medication.get('name', '药物')}")

        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 停药失败: {e}")
            self.show_error("停药失败")

    def show_delete_confirmation(self, medication):
        """显示删除确认对话框"""
        try:
            from kivymd.uix.dialog import MDDialog, MDDialogHeadlineText, MDDialogContentContainer, MDDialogButtonContainer
            from kivymd.uix.button import MDButton, MDButtonText
            from kivymd.uix.label import MDLabel

            # 创建内容容器
            content_container = MDDialogContentContainer(
                orientation="vertical",
                spacing=dp(16),
                size_hint_y=None,
                height=dp(120)
            )

            # 添加警告文字
            warning_label = MDLabel(
                text=f"确定要永久删除 {medication.get('name', '此药品')} 的用药记录吗？\n\n⚠️ 此操作不可撤销！",
                theme_text_color="Error",
                halign="center",
                size_hint_y=None,
                height=dp(80)
            )
            content_container.add_widget(warning_label)

            # 创建按钮容器
            button_container = MDDialogButtonContainer(
                spacing=dp(8)
            )

            # 取消按钮
            cancel_button = MDButton(
                style="text",
                on_release=lambda x: self.delete_dialog.dismiss()
            )
            cancel_button.add_widget(MDButtonText(text="取消"))
            button_container.add_widget(cancel_button)

            # 删除按钮
            delete_button = MDButton(
                style="filled",
                md_bg_color=self.app.theme.ERROR_COLOR,
                on_release=lambda x: self.confirm_delete_medication(medication, self.delete_dialog)
            )
            delete_button.add_widget(MDButtonText(text="永久删除"))
            button_container.add_widget(delete_button)

            # 创建对话框
            self.delete_dialog = MDDialog(
                MDDialogHeadlineText(text="删除用药记录"),
                content_container,
                button_container
            )

            self.delete_dialog.open()

        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 显示删除对话框失败: {e}")
            self.show_error("显示删除对话框失败")

    def confirm_delete_medication(self, medication, dialog=None):
        """确认删除用药记录"""
        try:
            if dialog:
                dialog.dismiss()

            # 从当前用药中删除
            self.medications = [med for med in self.medications if med['id'] != medication['id']]

            # 刷新显示
            self.load_medications()

            self.show_info(f"已删除 {medication.get('name', '药物')} 的用药记录")

        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 删除用药记录失败: {e}")
            self.show_error("删除用药记录失败")
    
    def delete_medication(self, medication, dialog=None):
        """删除用药记录"""
        try:
            # 获取用户认证信息（静默方式）
            custom_id = None
            access_token = None

            if hasattr(self.app, 'user_data') and self.app.user_data:
                custom_id = self.app.user_data.get('custom_id')
                access_token = self.app.user_data.get('access_token')

            # 尝试从后端API删除数据（静默方式）
            try:
                if custom_id and access_token:
                    cloud_api = get_cloud_api()
                    if cloud_api:
                        cloud_api.token = access_token
                        cloud_api.custom_id = custom_id

                        # 这里可以调用后端API删除数据
                        # result = cloud_api.delete_medication(medication['id'])
                        # if result and result.get('success'):
                        #     KivyLogger.info(f"[MedicationManagement] 成功从后端删除: {medication['name']}")
                        # else:
                        #     KivyLogger.info(f"[MedicationManagement] 后端删除失败，仅删除本地数据")

                        KivyLogger.info(f"[MedicationManagement] 已设置认证信息用于删除: {medication['name']}")
                else:
                    KivyLogger.info("[MedicationManagement] 无认证信息，仅删除本地数据")
            except Exception as api_error:
                KivyLogger.warning(f"[MedicationManagement] API删除失败: {api_error}")

            # 本地数据删除
            self.medications = [med for med in self.medications if med['id'] != medication['id']]

            if dialog:
                dialog.dismiss()

            self.show_info("删除成功")
            self.load_medications()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 删除用药记录失败: {e}")
            self.show_error(f"删除用药记录失败: {str(e)}")
    
    def set_medication_reminder(self, medication):
        """设置用药提醒"""
        try:
            dialog = MDDialog(
                title="用药提醒",
                text=f"已为 {medication.get('name', '此药品')} 设置提醒，将在服药时间前通知您。",
                actions=[
                    MDButton(
                        MDButtonText(text="确定"),
                        style="text",
                        on_release=lambda x: dialog.dismiss()
                    )
                ]
            )
            dialog.open()
        except Exception as e:
            KivyLogger.error(f"MedicationManagementScreen: 设置用药提醒失败: {e}")
            self.show_error(f"设置用药提醒失败: {str(e)}")
    
    def show_medication_detail(self, medication):
        """显示用药详情"""
        try:
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(8),
                size_hint_y=None,
                height=dp(250),
                padding=[dp(16), dp(8), dp(16), dp(8)]
            )
            
            # 添加各项信息
            fields = [
                ("药品名称", medication.get('name', '')),
                ("剂量", medication.get('dosage', '')),
                ("服用方式", medication.get('schedule', '')),
                ("开始日期", medication.get('start_date', '')),
                ("结束日期", medication.get('end_date', '')),
                ("备注", medication.get('notes', ''))
            ]
            
            for label, value in fields:
                if value:
                    field_box = MDBoxLayout(
                        orientation='vertical',
                        size_hint_y=None,
                        height=dp(40)
                    )
                    
                    label_widget = MDLabel(
                        text=label,
                        font_size=app.font_styles.BODY_SMALL['font_size'],
                        font_name=app.font_styles.BODY_SMALL['font_name'],
                        theme_text_color="Secondary",
                        size_hint_y=None,
                        height=dp(20)
                    )
                    
                    value_widget = MDLabel(
                        text=value,
                        font_size=app.font_styles.BODY_MEDIUM['font_size'],
                        font_name=app.font_styles.BODY_MEDIUM['font_name'],
                        theme_text_color="Primary",
                        size_hint_y=None,
                        height=dp(20)
                    )
                    
                    field_box.add_widget(label_widget)
                    field_box.add_widget(value_widget)
                    content.add_widget(field_box)
            
            # 创建对话框
            dialog = MDDialog(
                title="用药详情",
                type="custom",
                content_cls=content,
                actions=[
                    MDButton(
                        MDButtonText(text="编辑"),
                        style="text",
                        on_release=lambda x: (dialog.dismiss(), self.edit_medication(medication))
                    ),
                    MDButton(
                        MDButtonText(text="关闭"),
                        style="text",
                        on_release=lambda x: dialog.dismiss()
                    )
                ]
            )
            dialog.open()
        except Exception as e:
            KivyLogger.error(f"MedicationManagementScreen: 显示用药详情失败: {e}")
            self.show_error(f"显示用药详情失败: {str(e)}")
    
    def show_info(self, message):
        """显示信息提示"""
        try:
            app = MDApp.get_running_app()
            if hasattr(app, 'show_notification'):
                app.show_notification(message)
            else:
                snackbar = MDSnackbar(
                    MDSnackbarText(
                        text=message,
                    ),
                    pos_hint={"center_x": 0.5},
                    duration=2,
                )
                snackbar.open()
        except Exception as e:
            KivyLogger.error(f"MedicationManagementScreen: 显示信息失败: {e}")
    
    def show_error(self, message):
        """显示错误提示"""
        try:
            app = MDApp.get_running_app()
            if hasattr(app, 'show_error'):
                app.show_error(message)
            else:
                self.show_info(f"错误: {message}")
        except Exception as e:
            KivyLogger.error(f"MedicationManagementScreen: 显示错误失败: {e}")

    def show_reminder_dialog(self, medication):
        """显示提醒设置对话框"""
        try:
            from kivymd.uix.dialog import MDDialog, MDDialogHeadlineText, MDDialogContentContainer, MDDialogButtonContainer
            from kivymd.uix.button import MDButton, MDButtonText
            from kivymd.uix.label import MDLabel
            from kivymd.uix.card import MDCard
            from kivymd.uix.boxlayout import MDBoxLayout
            from kivymd.uix.selectioncontrol import MDCheckbox
            from kivymd.uix.textfield import MDTextField, MDTextFieldHintText

            # 创建主内容容器
            content_container = MDDialogContentContainer(
                orientation="vertical",
                spacing=dp(20),
                size_hint_y=None,
                height=dp(400),
                padding=[dp(16), dp(16), dp(16), dp(16)]
            )

            # 药物信息卡片
            info_card = MDCard(
                orientation="vertical",
                size_hint_y=None,
                height=dp(80),
                md_bg_color=self.app.theme.SURFACE_CONTAINER_COLOR,
                radius=[dp(12)],
                elevation=1,
                padding=[dp(16), dp(12), dp(16), dp(12)],
                spacing=dp(8)
            )

            name_label = MDLabel(
                text=f"药物: {medication.get('name', '未知药物')}",
                theme_text_color="Primary",
                font_size=self.app.font_styles.TITLE_MEDIUM['font_size'],
                bold=True,
                size_hint_y=None,
                height=dp(24),
                halign="left"
            )
            info_card.add_widget(name_label)

            schedule_label = MDLabel(
                text=f"用法: {medication.get('schedule', '')}",
                theme_text_color="Secondary",
                font_size=self.app.font_styles.BODY_MEDIUM['font_size'],
                size_hint_y=None,
                height=dp(20),
                halign="left"
            )
            info_card.add_widget(schedule_label)

            content_container.add_widget(info_card)

            # 提醒选项
            reminder_options = [
                ("每日提醒", "daily"),
                ("按用药时间提醒", "schedule"),
                ("自定义提醒", "custom")
            ]

            self.selected_reminder_type = "daily"  # 默认选择

            for option_text, option_value in reminder_options:
                option_layout = MDBoxLayout(
                    orientation='horizontal',
                    size_hint_y=None,
                    height=dp(48),
                    spacing=dp(12),
                    adaptive_width=True
                )

                checkbox = MDCheckbox(
                    size_hint=(None, None),
                    size=(dp(24), dp(24)),
                    active=option_value == "daily",
                    on_active=lambda checkbox, active, value=option_value: self.on_reminder_option_selected(value, active)
                )
                option_layout.add_widget(checkbox)

                option_label = MDLabel(
                    text=option_text,
                    theme_text_color="Primary",
                    font_size=self.app.font_styles.BODY_MEDIUM['font_size'],
                    size_hint_y=None,
                    height=dp(24),
                    halign="left",
                    valign="center"
                )
                option_layout.add_widget(option_label)

                content_container.add_widget(option_layout)

            # 提醒时间输入
            time_field = MDTextField(
                size_hint_y=None,
                height=dp(56),
                mode="outlined",
                text="08:00"
            )
            time_field.add_widget(MDTextFieldHintText(text="提醒时间 (HH:MM)"))
            content_container.add_widget(time_field)

            # 创建按钮容器
            button_container = MDDialogButtonContainer(
                spacing=dp(12),
                padding=[dp(16), dp(8), dp(16), dp(16)]
            )

            # 取消按钮
            cancel_button = MDButton(
                style="outlined",
                size_hint_x=0.4,
                on_release=lambda x: self.reminder_dialog.dismiss()
            )
            cancel_button.add_widget(MDButtonText(text="取消"))
            button_container.add_widget(cancel_button)

            # 确认按钮
            confirm_button = MDButton(
                style="filled",
                size_hint_x=0.6,
                on_release=lambda x: self.confirm_reminder_setting(medication, time_field.text, self.reminder_dialog)
            )
            confirm_button.add_widget(MDButtonText(text="设置提醒"))
            button_container.add_widget(confirm_button)

            # 创建对话框
            self.reminder_dialog = MDDialog(
                MDDialogHeadlineText(text="用药提醒设置"),
                content_container,
                button_container,
                size_hint=(0.9, None),
                height=dp(550),
                auto_dismiss=False  # 防止意外关闭
            )

            self.reminder_dialog.open()

        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 显示提醒对话框失败: {e}")
            self.show_error("显示提醒对话框失败")

    def on_reminder_option_selected(self, option_value, active):
        """处理提醒选项选择"""
        if active:
            self.selected_reminder_type = option_value
            KivyLogger.info(f"[MedicationManagement] 选择提醒类型: {option_value}")

    def confirm_reminder_setting(self, medication, reminder_time, dialog):
        """确认提醒设置"""
        try:
            dialog.dismiss()

            # 这里可以添加实际的提醒设置逻辑
            KivyLogger.info(f"[MedicationManagement] 为 {medication.get('name')} 设置提醒: {reminder_time}")

            self.show_info(f"已为 {medication.get('name', '药物')} 设置 {reminder_time} 的用药提醒")

        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 设置提醒失败: {e}")
            self.show_error("设置提醒失败")

    def show_medication_detail(self, medication):
        """显示药物详情对话框"""
        try:
            from kivymd.uix.dialog import MDDialog, MDDialogHeadlineText, MDDialogContentContainer, MDDialogButtonContainer
            from kivymd.uix.button import MDButton, MDButtonText
            from kivymd.uix.label import MDLabel
            from kivymd.uix.boxlayout import MDBoxLayout

            # 创建内容容器
            content_container = MDDialogContentContainer(
                orientation="vertical",
                spacing=dp(12),
                size_hint_y=None,
                height=dp(300)
            )

            # 药物基本信息
            info_items = [
                ("药物名称", medication.get('name', '')),
                ("剂量", medication.get('dosage', '')),
                ("用法", medication.get('schedule', '')),
                ("开始时间", medication.get('start_date', '')),
                ("结束时间", medication.get('end_date', '')),
                ("备注", medication.get('notes', '无'))
            ]

            for label_text, value_text in info_items:
                item_layout = MDBoxLayout(
                    orientation='horizontal',
                    size_hint_y=None,
                    height=dp(32),
                    spacing=dp(8)
                )

                label = MDLabel(
                    text=f"{label_text}:",
                    theme_text_color="Primary",
                    size_hint_x=0.3,
                    halign="left",
                    valign="center"
                )
                item_layout.add_widget(label)

                value = MDLabel(
                    text=str(value_text),
                    theme_text_color="Secondary",
                    size_hint_x=0.7,
                    halign="left",
                    valign="center"
                )
                item_layout.add_widget(value)

                content_container.add_widget(item_layout)

            # 创建按钮容器
            button_container = MDDialogButtonContainer(
                spacing=dp(8)
            )

            # 关闭按钮
            close_button = MDButton(
                style="filled",
                on_release=lambda x: self.detail_dialog.dismiss()
            )
            close_button.add_widget(MDButtonText(text="关闭"))
            button_container.add_widget(close_button)

            # 创建对话框
            self.detail_dialog = MDDialog(
                MDDialogHeadlineText(text="药物详情"),
                content_container,
                button_container
            )

            self.detail_dialog.open()

        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 显示详情对话框失败: {e}")
            self.show_error("显示详情对话框失败")

class HistoryMedicationCard(MDCard):
    """既往用药卡片组件"""

    name = StringProperty("")
    dosage = StringProperty("")
    schedule = StringProperty("")
    start_date = StringProperty("")
    stop_date = StringProperty("")
    stop_reason = StringProperty("")
    medication_data = ObjectProperty(None)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.orientation = 'vertical'
        self.size_hint_y = None
        self.height = dp(120)
        try:
            app = MDApp.get_running_app()
            self.md_bg_color = app.theme.SURFACE_CONTAINER_COLOR
        except:
            self.md_bg_color = (0.95, 0.95, 0.95, 1)
        self.radius = [dp(12)]
        self.elevation = 1
        self.padding = [dp(16), dp(12), dp(16), dp(12)]
        self.spacing = dp(6)
        self.ripple_behavior = True

        # 延迟添加内容，确保属性已设置
        Clock.schedule_once(self.add_content, 0.1)

    def add_content(self, dt=0):
        """添加卡片内容"""
        try:
            # 清空现有内容
            self.clear_widgets()

            # 药物名称
            name_label = MDLabel(
                text=self.name or "未知药物",
                theme_text_color="Primary",
                font_size=dp(16),
                bold=True,
                size_hint_y=None,
                height=dp(24),
                halign="left"
            )
            self.add_widget(name_label)

            # 药物信息
            info_text = f"{self.dosage} | {self.schedule}"
            info_label = MDLabel(
                text=info_text,
                theme_text_color="Secondary",
                font_size=dp(14),
                size_hint_y=None,
                height=dp(20),
                halign="left"
            )
            self.add_widget(info_label)

            # 使用期间
            if self.start_date and self.stop_date:
                date_text = f"使用期间: {self.start_date} 至 {self.stop_date}"
            else:
                date_text = "使用期间: 未知"

            date_label = MDLabel(
                text=date_text,
                theme_text_color="Secondary",
                font_size=dp(12),
                size_hint_y=None,
                height=dp(18),
                halign="left"
            )
            self.add_widget(date_label)

            # 停药原因
            reason_text = f"停药原因: {self.stop_reason or '未填写'}"
            reason_label = MDLabel(
                text=reason_text,
                theme_text_color="Custom",
                text_color=(0.8, 0.4, 0.2, 1),  # 橙色
                font_size=dp(12),
                size_hint_y=None,
                height=dp(18),
                halign="left"
            )
            self.add_widget(reason_label)

            KivyLogger.info(f"[HistoryMedicationCard] 成功添加内容: {self.name}")

        except Exception as e:
            KivyLogger.error(f"HistoryMedicationCard: 添加内容失败: {e}")
            # 添加错误提示
            error_label = MDLabel(
                text=f"显示错误: {self.name}",
                theme_text_color="Error",
                halign="center"
            )
            self.add_widget(error_label)

# 在类定义后注册Factory
Factory.register('MedicationManagementScreen', cls=MedicationManagementScreen)
Factory.register('MedicationCard', cls=MedicationCard)
Factory.register('HistoryMedicationCard', cls=HistoryMedicationCard)
Builder.load_string(KV)