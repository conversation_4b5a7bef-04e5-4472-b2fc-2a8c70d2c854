"""用药管理屏幕模块

提供用药记录的查看、添加、编辑、删除和提醒功能。
"""

from kivy.logger import Logger as KivyLogger
from kivymd.app import MDApp
from kivy.metrics import dp
from kivy.properties import StringProperty, ListProperty, ObjectProperty, BooleanProperty
from screens.base_screen import BaseScreen
from kivy.clock import Clock
from kivy.lang import Builder
from kivy.core.window import Window
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDIconButton, MDButtonText, MDButton
from kivymd.uix.label import MDLabel
from kivymd.uix.card import MDCard
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.list import MDList, MDListItem
from kivymd.uix.divider import MDDivider
from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText
from theme import AppTheme, AppMetrics, FontStyles
from utils.cloud_api import get_cloud_api
from utils.health_data_manager import get_health_data_manager
from kivymd.uix.dialog import MDDialog
from kivymd.uix.textfield import MDTextField
from kivymd.uix.selectioncontrol import MDCheckbox
from widgets.logo import HealthLogo
import os
import logging
from datetime import datetime, timedelta
from kivy.factory import Factory

KV = '''
<MedicationCard>:
    orientation: 'vertical'
    size_hint_y: None
    height: dp(120)
    md_bg_color: app.theme.CARD_BACKGROUND
    radius: [dp(12)]
    elevation: 2
    padding: [dp(16), dp(8), dp(16), dp(8)]
    
    MDLabel:
        text: root.name
        font_size: app.font_styles.TITLE_MEDIUM['font_size']
        font_name: app.font_styles.TITLE_MEDIUM['font_name']
        bold: app.font_styles.TITLE_MEDIUM['bold']
        theme_text_color: "Custom"
        text_color: app.theme.TEXT_PRIMARY
        size_hint_y: None
        height: self.texture_size[1]
    
    MDLabel:
        text: root.dosage
        font_size: app.font_styles.BODY_SMALL['font_size']
        font_name: app.font_styles.BODY_SMALL['font_name']
        theme_text_color: "Custom"
        text_color: app.theme.TEXT_SECONDARY
        size_hint_y: None
        height: self.texture_size[1]
    
    MDLabel:
        text: root.schedule
        font_size: app.font_styles.BODY_SMALL['font_size']
        font_name: app.font_styles.BODY_SMALL['font_name']
        theme_text_color: "Custom"
        text_color: app.theme.TEXT_SECONDARY
        size_hint_y: None
        height: self.texture_size[1]
    
    MDBoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(36)
        spacing: dp(8)
        
        MDButton:
            style: "text"
            on_release: root.on_remind()
            size_hint_x: None
            width: dp(100)
            
            MDButtonText:
                text: "设置提醒"
                font_size: app.font_styles.BUTTON_MEDIUM['font_size']
                font_name: app.font_styles.BUTTON_MEDIUM['font_name']
                bold: app.font_styles.BUTTON_MEDIUM['bold']
                theme_text_color: "Primary"
        
        Widget:
            size_hint_x: 1
        
        MDButton:
            style: "text"
            on_release: root.on_delete()
            size_hint_x: None
            width: dp(80)
            
            MDButtonText:
                text: "删除"
                font_size: app.font_styles.BUTTON_MEDIUM['font_size']
                font_name: app.font_styles.BUTTON_MEDIUM['font_name']
                bold: app.font_styles.BUTTON_MEDIUM['bold']
                theme_text_color: "Error"

<MedicationManagementScreen>:
    md_bg_color: app.theme.BACKGROUND_COLOR
    
    MDBoxLayout:
        orientation: 'vertical'
        spacing: dp(8)
        
        # 顶部应用栏
        MDBoxLayout:
            id: app_bar
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(56)
            md_bg_color: app.theme.PRIMARY_COLOR
            padding: [dp(4), dp(0), dp(4), dp(0)]
            
            MDIconButton:
                icon: "arrow-left"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.go_back()
            
            MDLabel:
                text: "用药管理"
                font_size: app.font_styles.TITLE_LARGE['font_size']
                font_name: app.font_styles.TITLE_LARGE['font_name']
                bold: app.font_styles.TITLE_LARGE['bold']
                theme_text_color: "Custom"
                text_color: app.theme.TEXT_LIGHT
                halign: "center"
                valign: "center"
            
            MDIconButton:
                icon: "plus"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.add_medication()
        
        # Logo区域
        HealthLogo:
            id: health_logo
            size_hint_y: None
            height: dp(120)
            logo_size: dp(80), dp(80)
            title_font_size: dp(18)
            subtitle_font_size: dp(14)
        
        # 内容区域
        MDScrollView:
            do_scroll_x: False
            do_scroll_y: True
            
            MDBoxLayout:
                id: medications_container
                orientation: 'vertical'
                size_hint_y: None
                height: self.minimum_height
                padding: [dp(16), dp(16), dp(16), dp(16)]
                spacing: dp(12)
'''

class MedicationCard(MDCard):
    """用药卡片组件"""
    name = StringProperty("")
    dosage = StringProperty("")
    schedule = StringProperty("")
    medication_data = ObjectProperty(None)
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.ripple_behavior = True  # 启用涟漪效果
        self.ripple_duration_in_slow = 0.1  # 加快涟漪动画
        self.ripple_color = (0.8, 0.8, 0.8, 0.5)  # 设置涟漪颜色
    
    def on_remind(self):
        """设置用药提醒"""
        app = MDApp.get_running_app()
        if hasattr(app.root.current_screen, 'set_medication_reminder'):
            app.root.current_screen.set_medication_reminder(self.medication_data)
    
    def on_delete(self):
        """删除用药记录"""
        app = MDApp.get_running_app()
        if hasattr(app.root.current_screen, 'confirm_delete_medication'):
            app.root.current_screen.confirm_delete_medication(self.medication_data)
    
    def on_touch_up(self, touch):
        """处理触摸释放事件"""
        if self.collide_point(*touch.pos) and touch.is_mouse_scrolling is False:
            self.on_release()
        return super().on_touch_up(touch)
    
    def on_release(self):
        """点击卡片时调用"""
        app = MDApp.get_running_app()
        if hasattr(app.root.current_screen, 'show_medication_detail'):
            app.root.current_screen.show_medication_detail(self.medication_data)

class MedicationManagementScreen(BaseScreen):
    """用药管理屏幕"""
    medications = ListProperty([])
    dialog = None
    editing_medication = None
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.app = MDApp.get_running_app()
        self.medications = []
        Clock.schedule_once(self.init_ui, 0.2)
    
    def on_enter(self):
        super().on_enter()
        self.init_ui()
    
    def init_ui(self, dt=0):
        self.load_medications()
    
    def load_medications(self):
        """加载用药记录"""
        try:
            custom_id = getattr(self.app, 'user_data', {}).get('custom_id', None)
            if not custom_id:
                self.show_error("未获取到用户ID，请重新登录")
                if hasattr(self, 'ids') and 'medications_container' in self.ids:
                    self.ids.medications_container.clear_widgets()
                    empty_label = MDLabel(
                        text="暂无用药记录\n请先登录后查看",
                        halign="center",
                        theme_text_color="Secondary",
                        font_size=app.font_styles.BODY_MEDIUM['font_size'],
                        font_name:app.font_styles.BODY_MEDIUM['font_name'],
                        role="medium"
                    )
                    self.ids.medications_container.add_widget(empty_label)
                return
            
            if not hasattr(self, 'ids') or 'medications_container' not in self.ids:
                return
            
            try:
                self.ids.medications_container.clear_widgets()
            except ReferenceError:
                return
            
            # 示例数据，后续对接API
            self.medications = [
                {"id": "1", "name": "阿司匹林", "dosage": "100mg", "schedule": "每日1次，早餐后服用", "start_date": "2023-06-01", "end_date": "2023-12-31", "notes": "心血管疾病预防用药"},
                {"id": "2", "name": "维生素C", "dosage": "500mg", "schedule": "每日2次，早晚餐后服用", "start_date": "2023-05-15", "end_date": "2023-11-15", "notes": "增强免疫力"}
            ]
            
            if not self.medications:
                # 显示空状态
                empty_label = MDLabel(
                    text="暂无用药记录\n点击右上角的 + 按钮添加记录",
                    halign="center",
                    theme_text_color="Secondary",
                    font_size=app.font_styles.BODY_MEDIUM['font_size'],
                    font_name:app.font_styles.BODY_MEDIUM['font_name'],
                    role="medium"
                )
                self.ids.medications_container.add_widget(empty_label)
                return
            
            for med in self.medications:
                card = MedicationCard(
                    name=med["name"],
                    dosage=f"剂量: {med['dosage']}",
                    schedule=f"服用方式: {med['schedule']}",
                    medication_data=med
                )
                self.ids.medications_container.add_widget(card)
                
        except Exception as e:
            KivyLogger.error(f"加载用药记录时出错: {e}")
            self.show_error(f"加载用药记录失败: {str(e)}")
    
    def go_back(self):
        """返回上一页"""
        try:
            # 直接返回主页，避免调用不存在的super().go_back()
            app = MDApp.get_running_app()
            app.root.current = 'homepage_screen'
        except Exception as e:
            KivyLogger.error(f"MedicationManagementScreen: 返回失败: {e}")
            app = MDApp.get_running_app()
            app.root.current = 'homepage_screen'
    
    def add_medication(self):
        """添加用药记录"""
        self.editing_medication = None
        self.show_medication_dialog()
    
    def edit_medication(self, medication):
        """编辑用药记录"""
        self.editing_medication = medication
        self.show_medication_dialog(medication)
    
    def show_medication_dialog(self, medication=None):
        """显示用药记录对话框"""
        try:
            if self.dialog:
                self.dialog.dismiss(force=True)

            name_field = MDTextField(hint_text="药品名称", text=medication.get('name', '') if medication else '')
            dosage_field = MDTextField(hint_text="剂量", text=medication.get('dosage', '') if medication else '')
            schedule_field = MDTextField(hint_text="服用方式", text=medication.get('schedule', '') if medication else '')
            start_date_field = MDTextField(hint_text="开始日期(YYYY-MM-DD)", text=medication.get('start_date', '') if medication else '')
            end_date_field = MDTextField(hint_text="结束日期(YYYY-MM-DD)", text=medication.get('end_date', '') if medication else '')
            notes_field = MDTextField(hint_text="备注", text=medication.get('notes', '') if medication else '')

            content = MDBoxLayout(orientation='vertical', spacing=dp(8), size_hint_y=None, height=dp(320))
            content.add_widget(name_field)
            content.add_widget(dosage_field)
            content.add_widget(schedule_field)
            content.add_widget(start_date_field)
            content.add_widget(end_date_field)
            content.add_widget(notes_field)

            self.dialog = MDDialog(
                title="用药记录",
                type="custom",
                content_cls=content,
                actions=[
                    MDButton(
                        MDButtonText(text="取消"),
                        style="text",
                        on_release=lambda x: self.dialog.dismiss()
                    ),
                    MDButton(
                        MDButtonText(text="保存"),
                        style="text",
                        on_release=lambda x: self.save_medication(
                            name_field.text,
                            dosage_field.text,
                            schedule_field.text,
                            start_date_field.text,
                            end_date_field.text,
                            notes_field.text
                        )
                    )
                ]
            )
            self.dialog.open()

        except Exception as e:
            KivyLogger.error(f"MedicationManagementScreen: 显示用药对话框失败: {e}")
            self.show_error(f"显示对话框失败: {str(e)}")
    
    def save_medication(self, name, dosage, schedule, start_date, end_date, notes):
        """保存用药记录"""
        try:
            self.dialog.dismiss()
            
            if not name or not dosage or not schedule:
                self.show_error("药品名称、剂量和服用方式不能为空")
                return
            
            # 创建或更新用药记录
            medication = {
                "id": self.editing_medication.get('id') if self.editing_medication else str(len(self.medications) + 1),
                "name": name,
                "dosage": dosage,
                "schedule": schedule,
                "start_date": start_date,
                "end_date": end_date,
                "notes": notes
            }
            
            # 这里应该调用API保存数据
            # 目前使用本地数据模拟
            if self.editing_medication:
                # 更新现有记录
                for i, med in enumerate(self.medications):
                    if med['id'] == medication['id']:
                        self.medications[i] = medication
                        break
            else:
                # 添加新记录
                self.medications.append(medication)
            
            self.show_info("保存成功")
            self.load_medications()
            
        except Exception as e:
            KivyLogger.error(f"MedicationManagementScreen: 保存用药记录失败: {e}")
            self.show_error(f"保存用药记录失败: {str(e)}")
    
    def confirm_delete_medication(self, medication):
        """确认删除用药记录"""
        dialog = MDDialog(
            title="确认删除",
            text=f"确定要删除 {medication.get('name', '此药品')} 的用药记录吗？",
            actions=[
                MDButton(
                    MDButtonText(text="取消"),
                    style="text",
                    on_release=lambda x: dialog.dismiss()
                ),
                MDButton(
                    MDButtonText(text="删除"),
                    style="text",
                    on_release=lambda x: self.delete_medication(medication, dialog)
                )
            ]
        )
        dialog.open()
    
    def delete_medication(self, medication, dialog=None):
        """删除用药记录"""
        try:
            # 这里应该调用API删除数据
            # 目前使用本地数据模拟
            self.medications = [med for med in self.medications if med['id'] != medication['id']]
            
            if dialog:
                dialog.dismiss()
            
            self.show_info("删除成功")
            self.load_medications()
            
        except Exception as e:
            KivyLogger.error(f"MedicationManagementScreen: 删除用药记录失败: {e}")
            self.show_error(f"删除用药记录失败: {str(e)}")
    
    def set_medication_reminder(self, medication):
        """设置用药提醒"""
        try:
            dialog = MDDialog(
                title="用药提醒",
                text=f"已为 {medication.get('name', '此药品')} 设置提醒，将在服药时间前通知您。",
                actions=[
                    MDButton(
                        MDButtonText(text="确定"),
                        style="text",
                        on_release=lambda x: dialog.dismiss()
                    )
                ]
            )
            dialog.open()
        except Exception as e:
            KivyLogger.error(f"MedicationManagementScreen: 设置用药提醒失败: {e}")
            self.show_error(f"设置用药提醒失败: {str(e)}")
    
    def show_medication_detail(self, medication):
        """显示用药详情"""
        try:
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(8),
                size_hint_y=None,
                height=dp(250),
                padding=[dp(16), dp(8), dp(16), dp(8)]
            )
            
            # 添加各项信息
            fields = [
                ("药品名称", medication.get('name', '')),
                ("剂量", medication.get('dosage', '')),
                ("服用方式", medication.get('schedule', '')),
                ("开始日期", medication.get('start_date', '')),
                ("结束日期", medication.get('end_date', '')),
                ("备注", medication.get('notes', ''))
            ]
            
            for label, value in fields:
                if value:
                    field_box = MDBoxLayout(
                        orientation='vertical',
                        size_hint_y=None,
                        height=dp(40)
                    )
                    
                    label_widget = MDLabel(
                        text=label,
                        font_size=app.font_styles.BODY_SMALL['font_size'],
                        font_name=app.font_styles.BODY_SMALL['font_name'],
                        theme_text_color="Secondary",
                        size_hint_y=None,
                        height=dp(20)
                    )
                    
                    value_widget = MDLabel(
                        text=value,
                        font_size=app.font_styles.BODY_MEDIUM['font_size'],
                        font_name=app.font_styles.BODY_MEDIUM['font_name'],
                        theme_text_color="Primary",
                        size_hint_y=None,
                        height=dp(20)
                    )
                    
                    field_box.add_widget(label_widget)
                    field_box.add_widget(value_widget)
                    content.add_widget(field_box)
            
            # 创建对话框
            dialog = MDDialog(
                title="用药详情",
                type="custom",
                content_cls=content,
                actions=[
                    MDButton(
                        MDButtonText(text="编辑"),
                        style="text",
                        on_release=lambda x: (dialog.dismiss(), self.edit_medication(medication))
                    ),
                    MDButton(
                        MDButtonText(text="关闭"),
                        style="text",
                        on_release=lambda x: dialog.dismiss()
                    )
                ]
            )
            dialog.open()
        except Exception as e:
            KivyLogger.error(f"MedicationManagementScreen: 显示用药详情失败: {e}")
            self.show_error(f"显示用药详情失败: {str(e)}")
    
    def show_info(self, message):
        """显示信息提示"""
        try:
            app = MDApp.get_running_app()
            if hasattr(app, 'show_notification'):
                app.show_notification(message)
            else:
                snackbar = MDSnackbar(
                    MDSnackbarText(
                        text=message,
                    ),
                    pos_hint={"center_x": 0.5},
                    duration=2,
                )
                snackbar.open()
        except Exception as e:
            KivyLogger.error(f"MedicationManagementScreen: 显示信息失败: {e}")
    
    def show_error(self, message):
        """显示错误提示"""
        try:
            app = MDApp.get_running_app()
            if hasattr(app, 'show_error'):
                app.show_error(message)
            else:
                self.show_info(f"错误: {message}")
        except Exception as e:
            KivyLogger.error(f"MedicationManagementScreen: 显示错误失败: {e}")

# 在类定义后注册Factory
Factory.register('MedicationManagementScreen', cls=MedicationManagementScreen)
Builder.load_string(KV)