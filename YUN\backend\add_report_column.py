#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from app.core.config import settings

def add_report_column():
    """向questionnaire_responses表添加report列"""
    try:
        # 创建数据库引擎
        engine = create_engine(settings.DATABASE_URL)
        
        with engine.connect() as connection:
            # 检查report列是否已存在
            result = connection.execute(text("""
                SELECT COUNT(*) as count 
                FROM pragma_table_info('questionnaire_responses') 
                WHERE name = 'report'
            """))
            
            count = result.fetchone()[0]
            
            if count == 0:
                # 添加report列
                connection.execute(text("""
                    ALTER TABLE questionnaire_responses 
                    ADD COLUMN report TEXT
                """))
                connection.commit()
                print("✅ 成功向questionnaire_responses表添加report列")
            else:
                print("ℹ️ questionnaire_responses表中已存在report列")
            
            # 验证表结构
            result = connection.execute(text("PRAGMA table_info(questionnaire_responses)"))
            columns = result.fetchall()
            
            print("\n📋 questionnaire_responses表当前结构:")
            for col in columns:
                print(f"  - {col[1]} ({col[2]})")
                
    except Exception as e:
        print(f"❌ 添加report列时出错: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🔧 开始向questionnaire_responses表添加report列...")
    success = add_report_column()
    if success:
        print("\n✅ 数据库迁移完成！")
    else:
        print("\n❌ 数据库迁移失败！")