# -*- coding: utf-8 -*-
import sqlite3
import json
import os

def recalculate_total_scores():
    """重新计算SM_008用户的总分"""
    
    # 数据库文件路径
    db_path = os.path.join(os.path.dirname(__file__), 'app.db')
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("=== 重新计算SM_008用户的总分 ===")
        
        # 查找有维度分数但总分为0的评估结果
        cursor.execute("""
            SELECT ar.id, ar.assessment_id, ar.total_score, ar.dimension_scores, ar.raw_answers,
                   at.name as template_name, at.id as template_id
            FROM assessment_results ar
            JOIN assessments a ON ar.assessment_id = a.id
            JOIN assessment_templates at ON a.template_id = at.id
            WHERE ar.custom_id = 'SM_008' 
            AND ar.dimension_scores IS NOT NULL 
            AND ar.dimension_scores != '{}'
            AND ar.dimension_scores != ''
        """)
        results = cursor.fetchall()
        
        print(f"\n找到 {len(results)} 个需要重新计算总分的评估结果:")
        
        for result in results:
            result_id, assessment_id, current_total, dimension_scores, raw_answers, template_name, template_id = result
            print(f"\n处理结果ID: {result_id} - {template_name}")
            print(f"  当前总分: {current_total}")
            
            try:
                # 解析答案数据
                if raw_answers:
                    answers = json.loads(raw_answers)
                    print(f"  答案数量: {len(answers)}")
                    
                    # 获取计分规则
                    cursor.execute("""
                        SELECT scoring FROM assessment_template_questions 
                        WHERE template_id = ? AND scoring IS NOT NULL AND scoring != ''
                    """, (template_id,))
                    scoring_data = cursor.fetchall()
                    
                    if scoring_data:
                        rules = []
                        for (scoring,) in scoring_data:
                            try:
                                rule = json.loads(scoring)
                                rules.append(rule)
                            except:
                                continue
                        
                        print(f"  计分规则数量: {len(rules)}")
                        
                        # 重新计算总分
                        total_score = 0
                        max_score = 0
                        
                        # 获取问题ID和计分规则的映射
                        cursor.execute("""
                            SELECT question_id, scoring FROM assessment_template_questions 
                            WHERE template_id = ? AND scoring IS NOT NULL AND scoring != ''
                        """, (template_id,))
                        question_rules = cursor.fetchall()
                        
                        # 创建问题ID到计分规则的映射
                        rules_map = {}
                        for question_id, scoring in question_rules:
                            try:
                                rule_options = json.loads(scoring)
                                rules_map[question_id] = rule_options
                            except:
                                continue
                        
                        print(f"  计分规则映射: {len(rules_map)} 个问题")
                        
                        # 计算总分
                        for question_id, selected_value in answers.items():
                            if question_id in rules_map:
                                options = rules_map[question_id]
                                question_score = 0
                                question_max = 0
                                
                                # 计算当前问题的分数和最大分数
                                for option in options:
                                    if option.get('value') == selected_value:
                                        question_score = option.get('score', 0)
                                    question_max = max(question_max, option.get('score', 0))
                                
                                total_score += question_score
                                max_score += question_max
                                
                                print(f"    {question_id}: {selected_value} -> {question_score}/{question_max}")
                        
                        # 计算百分比
                        percentage = (total_score / max_score * 100) if max_score > 0 else 0
                        
                        print(f"  重新计算的总分: {total_score}/{max_score} ({percentage:.1f}%)")
                        
                        # 确定结果等级
                        result_category = "正常"
                        if template_name == "抑郁自评量表":
                            if total_score >= 70:
                                result_category = "重度抑郁"
                            elif total_score >= 60:
                                result_category = "中度抑郁"
                            elif total_score >= 50:
                                result_category = "轻度抑郁"
                            else:
                                result_category = "正常"
                        
                        print(f"  结果等级: {result_category}")
                        
                        # 更新数据库
                        cursor.execute("""
                            UPDATE assessment_results 
                            SET total_score = ?, max_score = ?, percentage = ?, result_category = ?
                            WHERE id = ?
                        """, (total_score, max_score, percentage, result_category, result_id))
                        
                        print(f"  ✓ 已更新数据库")
                        
                    else:
                        print(f"  ✗ 没有计分规则")
                else:
                    print(f"  ✗ 没有答案数据")
                    
            except Exception as e:
                print(f"  ✗ 处理失败: {e}")
        
        # 提交更改
        conn.commit()
        
        # 验证更新结果
        print(f"\n=== 验证更新结果 ===")
        cursor.execute("""
            SELECT ar.id, ar.total_score, ar.max_score, ar.percentage, ar.result_category,
                   at.name as template_name
            FROM assessment_results ar
            JOIN assessments a ON ar.assessment_id = a.id
            JOIN assessment_templates at ON a.template_id = at.id
            WHERE ar.custom_id = 'SM_008'
            ORDER BY ar.id
        """)
        updated_results = cursor.fetchall()
        
        for result in updated_results:
            result_id, total_score, max_score, percentage, result_category, template_name = result
            print(f"结果ID {result_id} - {template_name}: {total_score}/{max_score} ({percentage}%) - {result_category}")
        
        conn.close()
        print(f"\n✓ 总分重新计算完成")
        
    except Exception as e:
        print(f"重新计算时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    recalculate_total_scores()