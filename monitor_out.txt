2025-06-14 10:55:41,538 - __main__ - INFO - 开始运行完整检查...
{"timestamp": "2025-06-14T10:55:41.556073", "total_checks": 7, "issues_count": 0, "results": [{"stage": "template_loading", "status": "healthy", "message": "模板加载正常: 评估模板5个, 问卷模板5个", "details": {"assessment_template_count": 5, "questionnaire_template_count": 5, "has_assessment_template_key": true, "has_questionnaire_template_key": true, "assessment_with_key": 5, "questionnaire_with_key": 5}, "timestamp": "2025-06-14T10:55:41.548377", "suggestions": []}, {"stage": "distribution", "status": "healthy", "message": "没有待完成的分发", "details": {"assessment_distributions": {"completed": 15, "pending": 11}, "questionnaire_distributions": {"completed": 6, "pending": 18}, "overdue_assessments": 0, "overdue_questionnaires": 0}, "timestamp": "2025-06-14T10:55:41.551091", "suggestions": []}, {"stage": "mobile_fetch", "status": "healthy", "message": "移动端获取状态正常", "details": {"assessment_total": 26, "assessment_missing_template_id": 0, "questionnaire_total": 22, "questionnaire_missing_template_id": 0}, "timestamp": "2025-06-14T10:55:41.551985", "suggestions": []}, {"stage": "mobile_submit", "status": "healthy", "message": "最近24小时提交: 评估0个, 问卷0个", "details": {"recent_assessments": 0, "recent_questionnaires": 0}, "timestamp": "2025-06-14T10:55:41.552828", "suggestions": []}, {"stage": "backend_calculation", "status": "healthy", "message": "后端计算状态正常", "details": {"incomplete_calculations": 0, "invalid_scores": 0, "missing_conclusions": 0}, "timestamp": "2025-06-14T10:55:41.553764", "suggestions": []}, {"stage": "database_save", "status": "healthy", "message": "数据库保存状态正常", "details": {"missing_unique_ids": 0, "duplicate_unique_ids": 0, "orphan_assessments": 0, "orphan_questionnaires": 0}, "timestamp": "2025-06-14T10:55:41.555108", "suggestions": []}, {"stage": "frontend_query", "status": "healthy", "message": "查询性能正常: 0.000秒", "details": {"template_key_indexes": ["idx_assessment_templates_template_key", "idx_questionnaire_templates_template_key"], "query_time": 0.00022363662719726562}, "timestamp": "2025-06-14T10:55:41.555863", "suggestions": []}], "issues": []}
2025-06-14 10:55:41,538 - __main__ - INFO - 检查模板加载状态...
2025-06-14 10:55:41,539 - __main__ - INFO - 找到数据库文件: C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-14 10:55:41,548 - __main__ - INFO - 检查分发状态... (用户Custom ID: 所有用户)
C:\Users\<USER>\Desktop\health-Trea\YUN\backend\scripts\assessment_questionnaire_monitor.py:347: DeprecationWarning: The default datetime adapter is deprecated as of Python 3.12; see the sqlite3 documentation for suggested replacement recipes
  cursor.execute(overdue_query, params)
2025-06-14 10:55:41,551 - __main__ - INFO - 检查移动端获取状态... (用户Custom ID: 所有用户)
2025-06-14 10:55:41,552 - __main__ - INFO - 检查提交状态... (评估ID: None, 问卷ID: None)
C:\Users\<USER>\Desktop\health-Trea\YUN\backend\scripts\assessment_questionnaire_monitor.py:604: DeprecationWarning: The default datetime adapter is deprecated as of Python 3.12; see the sqlite3 documentation for suggested replacement recipes
  cursor.execute("""
C:\Users\<USER>\Desktop\health-Trea\YUN\backend\scripts\assessment_questionnaire_monitor.py:610: DeprecationWarning: The default datetime adapter is deprecated as of Python 3.12; see the sqlite3 documentation for suggested replacement recipes
  cursor.execute("""
2025-06-14 10:55:41,552 - __main__ - INFO - 检查后端计算状态...
2025-06-14 10:55:41,553 - __main__ - INFO - 检查数据库保存状态...
2025-06-14 10:55:41,555 - __main__ - INFO - 检查查询状态...
2025-06-14 10:55:41,555 - __main__ - INFO - 完整检查完成, 成功: True
