#!/usr/bin/env python3
"""
测试移动端API，使用数据库中实际存在的用户ID
"""

import requests
import logging
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'YUN', 'backend'))

from app.db.base_session import SessionLocal
from app.models.user import User

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_first_user_custom_id():
    """从数据库获取第一个用户的custom_id"""
    db = SessionLocal()
    try:
        user = db.query(User).first()
        if user:
            logger.info(f"找到用户: {user.username}, custom_id: {user.custom_id}")
            return user.custom_id
        else:
            logger.error("数据库中没有找到任何用户")
            return None
    except Exception as e:
        logger.error(f"查询用户失败: {e}")
        return None
    finally:
        db.close()

def test_mobile_api_with_user_id(custom_id):
    """测试移动端API，使用有效的用户ID"""
    base_url = "http://localhost:8000/api/mobile"
    
    headers = {
        "X-User-ID": custom_id,
        "Content-Type": "application/json"
    }
    
    # 测试获取assessments
    logger.info(f"测试获取assessments，使用用户ID: {custom_id}")
    try:
        response = requests.get(f"{base_url}/assessments", headers=headers)
        logger.info(f"Assessments API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            logger.info(f"Assessments API响应成功")
            if isinstance(data, list):
                logger.info(f"返回的assessments数量: {len(data)}")
                logger.info(f"响应数据类型: list")
            elif isinstance(data, dict):
                logger.info(f"返回的assessments数量: {data.get('data', {}).get('total', 0)}")
                logger.info(f"响应数据结构: {list(data.keys())}")
            else:
                logger.info(f"响应数据类型: {type(data)}")
        else:
            logger.error(f"Assessments API请求失败: {response.status_code} - {response.text}")
    except Exception as e:
        logger.error(f"Assessments API请求异常: {e}")
    
    # 测试获取questionnaires
    logger.info(f"测试获取questionnaires，使用用户ID: {custom_id}")
    try:
        response = requests.get(f"{base_url}/questionnaires", headers=headers)
        logger.info(f"Questionnaires API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            logger.info(f"Questionnaires API响应成功")
            if isinstance(data, list):
                logger.info(f"返回的questionnaires数量: {len(data)}")
                logger.info(f"响应数据类型: list")
            elif isinstance(data, dict):
                logger.info(f"返回的questionnaires数量: {data.get('data', {}).get('total', 0)}")
                logger.info(f"响应数据结构: {list(data.keys())}")
            else:
                logger.info(f"响应数据类型: {type(data)}")
        else:
            logger.error(f"Questionnaires API请求失败: {response.status_code} - {response.text}")
    except Exception as e:
        logger.error(f"Questionnaires API请求异常: {e}")

if __name__ == "__main__":
    # 获取数据库中的第一个用户ID
    custom_id = get_first_user_custom_id()
    
    if custom_id:
        test_mobile_api_with_user_id(custom_id)
    else:
        logger.error("无法获取有效的用户ID，测试终止")