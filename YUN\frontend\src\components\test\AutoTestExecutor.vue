<template>
  <div class="auto-test-executor">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>自动化测试执行器</h2>
      <p class="page-description">配置和管理自动化测试任务，实现持续集成测试</p>
    </div>

    <!-- 快速操作栏 -->
    <div class="quick-actions">
      <div class="action-buttons">
        <el-button type="primary" size="large" @click="runAllTests" :loading="isRunningAll">
          <el-icon><VideoPlay /></el-icon>
          运行所有测试
        </el-button>
        <el-button type="success" size="large" @click="runSelectedTests" :disabled="selectedTasks.length === 0">
          <el-icon><Select /></el-icon>
          运行选中测试 ({{ selectedTasks.length }})
        </el-button>
        <el-button size="large" @click="stopAllTests" :disabled="!hasRunningTests">
          <el-icon><VideoPause /></el-icon>
          停止所有测试
        </el-button>
        <el-button size="large" @click="showScheduleDialog = true">
          <el-icon><Timer /></el-icon>
          定时任务
        </el-button>
        <el-button size="large" @click="showBatchExecutionDialog = true">
          <el-icon><Operation /></el-icon>
          批量执行
        </el-button>
        <el-button size="large" @click="showExecutionSettings = true">
          <el-icon><Setting /></el-icon>
          执行设置
        </el-button>
      </div>
      
      <div class="status-indicator">
        <div class="status-item">
          <el-icon class="status-icon running"><Loading /></el-icon>
          <span class="status-text">运行中: {{ runningTestsCount }}</span>
        </div>
        <div class="status-item">
          <el-icon class="status-icon queued"><Clock /></el-icon>
          <span class="status-text">队列中: {{ queuedTestsCount }}</span>
        </div>
        <div class="status-item">
          <el-icon class="status-icon completed"><CircleCheck /></el-icon>
          <span class="status-text">已完成: {{ completedTestsCount }}</span>
        </div>
        <div class="status-item">
          <el-icon class="status-icon failed"><CircleClose /></el-icon>
          <span class="status-text">失败: {{ failedTestsCount }}</span>
        </div>
      </div>
    </div>

    <!-- 执行统计面板 -->
    <div class="execution-stats">
      <el-card class="stats-card">
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-icon success">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ executionStats.successRate }}%</div>
              <div class="stat-label">成功率</div>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-icon duration">
              <el-icon><Stopwatch /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ formatDuration(executionStats.avgDuration) }}</div>
              <div class="stat-label">平均耗时</div>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-icon coverage">
              <el-icon><PieChart /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ executionStats.coverage }}%</div>
              <div class="stat-label">代码覆盖率</div>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-icon total">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ executionStats.totalExecutions }}</div>
              <div class="stat-label">总执行次数</div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 测试任务列表 -->
    <div class="test-tasks">
      <div class="section-header">
        <h3>测试任务</h3>
        <div class="task-controls">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索测试任务..."
            prefix-icon="Search"
            style="width: 250px;"
            clearable
          />
          <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px; margin-left: 10px;">
            <el-option label="全部" value="" />
            <el-option label="运行中" value="running" />
            <el-option label="队列中" value="queued" />
            <el-option label="已完成" value="completed" />
            <el-option label="失败" value="failed" />
            <el-option label="已停止" value="stopped" />
          </el-select>
          <el-select v-model="priorityFilter" placeholder="优先级" style="width: 120px; margin-left: 10px;">
            <el-option label="全部" value="" />
            <el-option label="高" value="high" />
            <el-option label="中" value="medium" />
            <el-option label="低" value="low" />
          </el-select>
          <el-button type="primary" @click="showCreateTaskDialog = true" style="margin-left: 10px;">
            <el-icon><Plus /></el-icon>
            新建任务
          </el-button>
        </div>
      </div>
      
      <el-table 
        :data="filteredTasks" 
        style="width: 100%" 
        stripe
        @selection-change="handleSelectionChange"
        v-loading="loading"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="name" label="任务名称" min-width="200">
          <template #default="{ row }">
            <div class="task-name">
              <el-icon class="task-icon"><component :is="getTaskIcon(row.type)" /></el-icon>
              <div class="name-content">
                <div class="name-text">{{ row.name }}</div>
                <div class="name-description">{{ row.description }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="type" label="类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeColor(row.type)" size="small">
              {{ getTypeText(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="priority" label="优先级" width="100">
          <template #default="{ row }">
            <el-tag :type="getPriorityColor(row.priority)" size="small">
              {{ getPriorityText(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="120">
          <template #default="{ row }">
            <div class="status-cell">
              <el-icon class="status-icon" :class="row.status">
                <component :is="getStatusIcon(row.status)" />
              </el-icon>
              <span class="status-text">{{ getStatusText(row.status) }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="progress" label="进度" width="150">
          <template #default="{ row }">
            <div class="progress-cell">
              <el-progress
                :percentage="row.progress"
                :stroke-width="6"
                :color="getProgressColor(row.status)"
                :show-text="false"
              />
              <span class="progress-text">{{ row.progress }}%</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="duration" label="执行时间" width="120">
          <template #default="{ row }">
            <span class="duration-text">{{ formatDuration(row.duration) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="lastRun" label="最后运行" width="180">
          <template #default="{ row }">
            <div class="last-run">
              <el-icon><Clock /></el-icon>
              <span>{{ formatDateTime(row.lastRun) }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="nextRun" label="下次运行" width="180">
          <template #default="{ row }">
            <div class="next-run" v-if="row.nextRun">
              <el-icon><Timer /></el-icon>
              <span>{{ formatDateTime(row.nextRun) }}</span>
            </div>
            <span v-else class="no-schedule">无计划</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button 
                type="text" 
                size="small" 
                @click="runSingleTest(row)" 
                :disabled="row.status === 'running'"
              >
                <el-icon><VideoPlay /></el-icon>
                运行
              </el-button>
              <el-button 
                type="text" 
                size="small" 
                @click="stopSingleTest(row)" 
                :disabled="row.status !== 'running'"
              >
                <el-icon><VideoPause /></el-icon>
                停止
              </el-button>
              <el-button type="text" size="small" @click="viewTaskDetail(row)">
                <el-icon><View /></el-icon>
                详情
              </el-button>
              <el-dropdown trigger="click">
                <el-button type="text" size="small">
                  <el-icon><More /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="editTask(row)">
                      <el-icon><Edit /></el-icon>
                      编辑
                    </el-dropdown-item>
                    <el-dropdown-item @click="cloneTask(row)">
                      <el-icon><CopyDocument /></el-icon>
                      克隆
                    </el-dropdown-item>
                    <el-dropdown-item @click="viewLogs(row)">
                      <el-icon><Document /></el-icon>
                      查看日志
                    </el-dropdown-item>
                    <el-dropdown-item divided @click="deleteTask(row)">
                      <el-icon><Delete /></el-icon>
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 实时监控面板 -->
    <div class="monitoring-panel">
      <div class="section-header">
        <h3>实时监控</h3>
        <div class="monitor-controls">
          <el-switch
            v-model="autoRefresh"
            active-text="自动刷新"
            inactive-text="手动刷新"
          />
          <el-button size="small" @click="refreshMonitoring" style="margin-left: 10px;">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
      
      <div class="monitoring-grid">
        <!-- 执行队列 -->
        <div class="monitor-card">
          <h4>执行队列</h4>
          <div class="queue-list">
            <div 
              v-for="task in executionQueue" 
              :key="task.id" 
              class="queue-item"
              :class="task.status"
            >
              <div class="queue-info">
                <span class="queue-name">{{ task.name }}</span>
                <span class="queue-status">{{ getStatusText(task.status) }}</span>
              </div>
              <div class="queue-progress">
                <el-progress
                  :percentage="task.progress"
                  :stroke-width="4"
                  :show-text="false"
                  :color="getProgressColor(task.status)"
                />
              </div>
            </div>
          </div>
        </div>
        
        <!-- 性能监控 -->
        <div class="monitor-card">
          <h4>性能监控</h4>
          <div class="performance-metrics">
            <div class="metric-item">
              <span class="metric-label">CPU使用率:</span>
              <div class="metric-value">
                <el-progress
                  :percentage="systemMetrics.cpu"
                  :stroke-width="8"
                  :color="getMetricColor(systemMetrics.cpu)"
                />
              </div>
            </div>
            <div class="metric-item">
              <span class="metric-label">内存使用:</span>
              <div class="metric-value">
                <el-progress
                  :percentage="systemMetrics.memory"
                  :stroke-width="8"
                  :color="getMetricColor(systemMetrics.memory)"
                />
              </div>
            </div>
            <div class="metric-item">
              <span class="metric-label">磁盘I/O:</span>
              <div class="metric-value">
                <el-progress
                  :percentage="systemMetrics.disk"
                  :stroke-width="8"
                  :color="getMetricColor(systemMetrics.disk)"
                />
              </div>
            </div>
          </div>
        </div>
        
        <!-- 最近事件 -->
        <div class="monitor-card">
          <h4>最近事件</h4>
          <div class="events-list">
            <div 
              v-for="event in recentEvents" 
              :key="event.id" 
              class="event-item"
              :class="event.type"
            >
              <div class="event-time">{{ formatTime(event.timestamp) }}</div>
              <div class="event-content">
                <el-icon class="event-icon"><component :is="getEventIcon(event.type)" /></el-icon>
                <span class="event-message">{{ event.message }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 统计图表 -->
        <div class="monitor-card chart-card">
          <h4>执行统计</h4>
          <div class="chart-container" ref="statisticsChartRef"></div>
        </div>
      </div>
    </div>

    <!-- 创建任务对话框 -->
    <el-dialog
      v-model="showCreateTaskDialog"
      title="创建测试任务"
      width="800px"
    >
      <el-form :model="newTask" :rules="taskRules" ref="taskFormRef" label-width="120px">
        <el-form-item label="任务名称" prop="name">
          <el-input v-model="newTask.name" placeholder="请输入任务名称" />
        </el-form-item>
        
        <el-form-item label="任务描述" prop="description">
          <el-input 
            v-model="newTask.description" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入任务描述"
          />
        </el-form-item>
        
        <el-form-item label="测试类型" prop="type">
          <el-select v-model="newTask.type" placeholder="选择测试类型" style="width: 100%;">
            <el-option label="单元测试" value="unit" />
            <el-option label="集成测试" value="integration" />
            <el-option label="API测试" value="api" />
            <el-option label="E2E测试" value="e2e" />
            <el-option label="性能测试" value="performance" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="优先级" prop="priority">
          <el-radio-group v-model="newTask.priority">
            <el-radio label="high">高</el-radio>
            <el-radio label="medium">中</el-radio>
            <el-radio label="low">低</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="测试命令" prop="command">
          <el-input v-model="newTask.command" placeholder="例如: npm test" />
        </el-form-item>
        
        <el-form-item label="工作目录" prop="workingDir">
          <el-input v-model="newTask.workingDir" placeholder="测试执行的工作目录" />
        </el-form-item>
        
        <el-form-item label="环境变量">
          <div class="env-vars">
            <div v-for="(env, index) in newTask.envVars" :key="index" class="env-var-item">
              <el-input v-model="env.key" placeholder="变量名" style="width: 40%;" />
              <el-input v-model="env.value" placeholder="变量值" style="width: 40%; margin-left: 10px;" />
              <el-button @click="removeEnvVar(index)" style="margin-left: 10px;">
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
            <el-button @click="addEnvVar" type="text">
              <el-icon><Plus /></el-icon>
              添加环境变量
            </el-button>
          </div>
        </el-form-item>
        
        <el-form-item label="超时时间">
          <el-input-number 
            v-model="newTask.timeout" 
            :min="1" 
            :max="3600" 
            placeholder="秒"
            style="width: 200px;"
          />
          <span style="margin-left: 10px; color: #606266;">秒</span>
        </el-form-item>
        
        <el-form-item label="重试次数">
          <el-input-number 
            v-model="newTask.retryCount" 
            :min="0" 
            :max="5" 
            style="width: 200px;"
          />
        </el-form-item>
        
        <el-form-item label="定时执行">
          <el-switch v-model="newTask.scheduled" />
        </el-form-item>
        
        <el-form-item v-if="newTask.scheduled" label="Cron表达式">
          <el-input 
            v-model="newTask.cronExpression" 
            placeholder="例如: 0 0 2 * * * (每天凌晨2点)"
          />
          <div class="cron-help">
            <el-text size="small" type="info">
              格式: 秒 分 时 日 月 周 年(可选)
            </el-text>
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showCreateTaskDialog = false">取消</el-button>
          <el-button type="primary" @click="createTask">创建任务</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 定时任务管理对话框 -->
    <el-dialog
      v-model="showScheduleDialog"
      title="定时任务管理"
      width="1000px"
    >
      <div class="schedule-management">
        <div class="schedule-header">
          <el-button type="primary" @click="showCreateScheduleDialog = true">
            <el-icon><Plus /></el-icon>
            新建定时任务
          </el-button>
          <el-button @click="refreshSchedules">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
        
        <el-table :data="scheduledTasks" style="width: 100%; margin-top: 20px;">
          <el-table-column prop="name" label="任务名称" min-width="200" />
          <el-table-column prop="cronExpression" label="Cron表达式" width="200" />
          <el-table-column prop="nextRun" label="下次执行" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.nextRun) }}
            </template>
          </el-table-column>
          <el-table-column prop="enabled" label="状态" width="100">
            <template #default="{ row }">
              <el-switch v-model="row.enabled" @change="toggleSchedule(row)" />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150">
            <template #default="{ row }">
              <el-button type="text" size="small" @click="editSchedule(row)">
                编辑
              </el-button>
              <el-button type="text" size="small" @click="deleteSchedule(row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 任务详情对话框 -->
    <el-dialog
      v-model="showTaskDetailDialog"
      :title="`任务详情 - ${selectedTask?.name}`"
      width="1000px"
    >
      <div v-if="selectedTask" class="task-detail">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h4>基本信息</h4>
          <div class="info-grid">
            <div class="info-item">
              <span class="info-label">任务名称:</span>
              <span class="info-value">{{ selectedTask.name }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">任务类型:</span>
              <el-tag :type="getTypeColor(selectedTask.type)">
                {{ getTypeText(selectedTask.type) }}
              </el-tag>
            </div>
            <div class="info-item">
              <span class="info-label">优先级:</span>
              <el-tag :type="getPriorityColor(selectedTask.priority)">
                {{ getPriorityText(selectedTask.priority) }}
              </el-tag>
            </div>
            <div class="info-item">
              <span class="info-label">当前状态:</span>
              <el-tag :type="getStatusColor(selectedTask.status)">
                {{ getStatusText(selectedTask.status) }}
              </el-tag>
            </div>
            <div class="info-item">
              <span class="info-label">执行进度:</span>
              <span class="info-value">{{ selectedTask.progress }}%</span>
            </div>
            <div class="info-item">
              <span class="info-label">执行时间:</span>
              <span class="info-value">{{ formatDuration(selectedTask.duration) }}</span>
            </div>
          </div>
        </div>

        <!-- 执行配置 -->
        <div class="detail-section">
          <h4>执行配置</h4>
          <div class="config-info">
            <div class="config-item">
              <span class="config-label">执行命令:</span>
              <code class="config-value">{{ selectedTask.command }}</code>
            </div>
            <div class="config-item">
              <span class="config-label">工作目录:</span>
              <code class="config-value">{{ selectedTask.workingDir }}</code>
            </div>
            <div class="config-item">
              <span class="config-label">超时时间:</span>
              <span class="config-value">{{ selectedTask.timeout }}秒</span>
            </div>
            <div class="config-item">
              <span class="config-label">重试次数:</span>
              <span class="config-value">{{ selectedTask.retryCount }}次</span>
            </div>
          </div>
        </div>

        <!-- 执行历史 -->
        <div class="detail-section">
          <h4>执行历史</h4>
          <el-table :data="selectedTask.history || []" size="small">
            <el-table-column prop="startTime" label="开始时间" width="180">
              <template #default="{ row }">
                {{ formatDateTime(row.startTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="duration" label="执行时间" width="100">
              <template #default="{ row }">
                {{ formatDuration(row.duration) }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusColor(row.status)" size="small">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="exitCode" label="退出码" width="80" />
            <el-table-column label="操作" width="100">
              <template #default="{ row }">
                <el-button type="text" size="small" @click="viewExecutionLogs(row)">
                  查看日志
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  VideoPlay, Select, VideoPause, Timer, Loading, Clock, CircleCheck, Search, Plus,
  View, More, Edit, CopyDocument, Document, Delete, Refresh, Warning, 
  InfoFilled, SuccessFilled
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// 响应式数据
const loading = ref(false)
const isRunningAll = ref(false)
const searchKeyword = ref('')
const statusFilter = ref('')
const priorityFilter = ref('')
const selectedTasks = ref([])
const autoRefresh = ref(true)
const showCreateTaskDialog = ref(false)
const showScheduleDialog = ref(false)
const showCreateScheduleDialog = ref(false)
const showTaskDetailDialog = ref(false)
const selectedTask = ref(null)

// 图表引用
const statisticsChartRef = ref()
let statisticsChart = null

// 自动刷新定时器
let refreshTimer = null

// 新任务表单
const newTask = reactive({
  name: '',
  description: '',
  type: 'unit',
  priority: 'medium',
  command: '',
  workingDir: '',
  envVars: [],
  timeout: 300,
  retryCount: 0,
  scheduled: false,
  cronExpression: ''
})

// 表单验证规则
const taskRules = {
  name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择测试类型', trigger: 'change' }],
  command: [{ required: true, message: '请输入测试命令', trigger: 'blur' }]
}

const taskFormRef = ref()

// 测试任务数据
const testTasks = ref([
  {
    id: 1,
    name: 'Frontend Unit Tests',
    description: '前端组件单元测试',
    type: 'unit',
    priority: 'high',
    status: 'running',
    progress: 65,
    duration: 120,
    lastRun: new Date('2024-01-15T10:30:00'),
    nextRun: new Date('2024-01-16T02:00:00'),
    command: 'npm run test:unit',
    workingDir: '/frontend',
    timeout: 300,
    retryCount: 2,
    history: [
      {
        startTime: new Date('2024-01-15T10:30:00'),
        duration: 120,
        status: 'completed',
        exitCode: 0
      },
      {
        startTime: new Date('2024-01-14T10:30:00'),
        duration: 135,
        status: 'failed',
        exitCode: 1
      }
    ]
  },
  {
    id: 2,
    name: 'Backend API Tests',
    description: 'API接口测试',
    type: 'api',
    priority: 'high',
    status: 'queued',
    progress: 0,
    duration: 0,
    lastRun: new Date('2024-01-15T09:15:00'),
    nextRun: new Date('2024-01-16T03:00:00'),
    command: 'npm run test:api',
    workingDir: '/backend',
    timeout: 600,
    retryCount: 1
  },
  {
    id: 3,
    name: 'Integration Tests',
    description: '集成测试',
    type: 'integration',
    priority: 'medium',
    status: 'completed',
    progress: 100,
    duration: 245,
    lastRun: new Date('2024-01-15T08:00:00'),
    nextRun: null,
    command: 'npm run test:integration',
    workingDir: '/tests',
    timeout: 900,
    retryCount: 0
  },
  {
    id: 4,
    name: 'E2E Tests',
    description: '端到端测试',
    type: 'e2e',
    priority: 'low',
    status: 'failed',
    progress: 45,
    duration: 180,
    lastRun: new Date('2024-01-14T16:45:00'),
    nextRun: new Date('2024-01-16T04:00:00'),
    command: 'npm run test:e2e',
    workingDir: '/e2e',
    timeout: 1200,
    retryCount: 3
  }
])

// 定时任务数据
const scheduledTasks = ref([
  {
    id: 1,
    name: 'Daily Unit Tests',
    cronExpression: '0 0 2 * * *',
    nextRun: new Date('2024-01-16T02:00:00'),
    enabled: true
  },
  {
    id: 2,
    name: 'Weekly Integration Tests',
    cronExpression: '0 0 3 * * 0',
    nextRun: new Date('2024-01-21T03:00:00'),
    enabled: true
  }
])

// 系统性能指标
const systemMetrics = reactive({
  cpu: 45,
  memory: 67,
  disk: 23
})

// 最近事件
const recentEvents = ref([
  {
    id: 1,
    type: 'success',
    message: 'Frontend Unit Tests 执行完成',
    timestamp: new Date()
  },
  {
    id: 2,
    type: 'error',
    message: 'E2E Tests 执行失败',
    timestamp: new Date(Date.now() - 300000)
  },
  {
    id: 3,
    type: 'info',
    message: 'Backend API Tests 已加入队列',
    timestamp: new Date(Date.now() - 600000)
  }
])

// 计算属性
const filteredTasks = computed(() => {
  return testTasks.value.filter(task => {
    const matchesSearch = !searchKeyword.value || 
      task.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
    
    const matchesStatus = !statusFilter.value || task.status === statusFilter.value
    const matchesPriority = !priorityFilter.value || task.priority === priorityFilter.value
    
    return matchesSearch && matchesStatus && matchesPriority
  })
})

const runningTestsCount = computed(() => {
  return testTasks.value.filter(task => task.status === 'running').length
})

const queuedTestsCount = computed(() => {
  return testTasks.value.filter(task => task.status === 'queued').length
})

const completedTestsCount = computed(() => {
  return testTasks.value.filter(task => task.status === 'completed').length
})

const hasRunningTests = computed(() => {
  return runningTestsCount.value > 0
})

const executionQueue = computed(() => {
  return testTasks.value.filter(task => 
    task.status === 'running' || task.status === 'queued'
  ).slice(0, 5)
})

// 方法
const runAllTests = async () => {
  try {
    isRunningAll.value = true
    ElMessage.info('开始运行所有测试任务...')
    
    // 模拟批量运行
    for (const task of testTasks.value) {
      if (task.status !== 'running') {
        task.status = 'queued'
        task.progress = 0
      }
    }
    
    // 模拟执行过程
    await simulateTestExecution()
    
    ElMessage.success('所有测试任务已完成')
  } catch (error) {
    ElMessage.error('运行失败: ' + error.message)
  } finally {
    isRunningAll.value = false
  }
}

const runSelectedTests = async () => {
  try {
    ElMessage.info(`开始运行选中的 ${selectedTasks.value.length} 个测试任务...`)
    
    for (const task of selectedTasks.value) {
      if (task.status !== 'running') {
        task.status = 'queued'
        task.progress = 0
      }
    }
    
    await simulateTestExecution()
    
    ElMessage.success('选中的测试任务已完成')
  } catch (error) {
    ElMessage.error('运行失败: ' + error.message)
  }
}

const stopAllTests = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要停止所有正在运行的测试吗？',
      '确认停止',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    testTasks.value.forEach(task => {
      if (task.status === 'running' || task.status === 'queued') {
        task.status = 'stopped'
      }
    })
    
    ElMessage.success('所有测试已停止')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('停止失败: ' + error.message)
    }
  }
}

const runSingleTest = async (task) => {
  try {
    task.status = 'running'
    task.progress = 0
    task.lastRun = new Date()
    
    ElMessage.info(`开始运行测试: ${task.name}`)
    
    // 模拟测试执行
    await simulateSingleTestExecution(task)
    
    ElMessage.success(`测试完成: ${task.name}`)
  } catch (error) {
    task.status = 'failed'
    ElMessage.error(`测试失败: ${task.name} - ${error.message}`)
  }
}

const stopSingleTest = (task) => {
  task.status = 'stopped'
  ElMessage.info(`已停止测试: ${task.name}`)
}

const simulateTestExecution = async () => {
  const runningTasks = testTasks.value.filter(task => 
    task.status === 'running' || task.status === 'queued'
  )
  
  for (const task of runningTasks) {
    if (task.status === 'queued') {
      task.status = 'running'
    }
    
    // 模拟进度更新
    const progressInterval = setInterval(() => {
      if (task.progress < 100 && task.status === 'running') {
        task.progress += Math.floor(Math.random() * 10) + 5
        if (task.progress >= 100) {
          task.progress = 100
          task.status = Math.random() > 0.8 ? 'failed' : 'completed'
          task.duration = Math.floor(Math.random() * 300) + 60
          clearInterval(progressInterval)
        }
      }
    }, 500)
    
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
}

const simulateSingleTestExecution = async (task) => {
  return new Promise((resolve, reject) => {
    const progressInterval = setInterval(() => {
      if (task.progress < 100 && task.status === 'running') {
        task.progress += Math.floor(Math.random() * 15) + 10
        if (task.progress >= 100) {
          task.progress = 100
          task.status = Math.random() > 0.8 ? 'failed' : 'completed'
          task.duration = Math.floor(Math.random() * 300) + 60
          clearInterval(progressInterval)
          
          if (task.status === 'completed') {
            resolve()
          } else {
            reject(new Error('测试执行失败'))
          }
        }
      }
    }, 300)
  })
}

const createTask = async () => {
  try {
    await taskFormRef.value.validate()
    
    const task = {
      id: Date.now(),
      ...newTask,
      status: 'stopped',
      progress: 0,
      duration: 0,
      lastRun: null,
      nextRun: newTask.scheduled ? calculateNextRun(newTask.cronExpression) : null
    }
    
    testTasks.value.push(task)
    
    // 重置表单
    Object.assign(newTask, {
      name: '',
      description: '',
      type: 'unit',
      priority: 'medium',
      command: '',
      workingDir: '',
      envVars: [],
      timeout: 300,
      retryCount: 0,
      scheduled: false,
      cronExpression: ''
    })
    
    showCreateTaskDialog.value = false
    ElMessage.success('测试任务创建成功')
  } catch (error) {
    ElMessage.error('创建失败: ' + error.message)
  }
}

const editTask = (task) => {
  // 编辑任务逻辑
  ElMessage.info('编辑功能开发中...')
}

const cloneTask = (task) => {
  const clonedTask = {
    ...task,
    id: Date.now(),
    name: task.name + ' (副本)',
    status: 'stopped',
    progress: 0,
    duration: 0,
    lastRun: null
  }
  
  testTasks.value.push(clonedTask)
  ElMessage.success('任务克隆成功')
}

const deleteTask = async (task) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除任务 "${task.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = testTasks.value.findIndex(t => t.id === task.id)
    if (index > -1) {
      testTasks.value.splice(index, 1)
    }
    
    ElMessage.success('任务删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error.message)
    }
  }
}

const viewTaskDetail = (task) => {
  selectedTask.value = task
  showTaskDetailDialog.value = true
}

const viewLogs = (task) => {
  // 查看日志逻辑
  ElMessage.info('日志查看功能开发中...')
}

const viewExecutionLogs = (execution) => {
  // 查看执行日志
  ElMessage.info('执行日志查看功能开发中...')
}

const handleSelectionChange = (selections) => {
  selectedTasks.value = selections
}

const addEnvVar = () => {
  newTask.envVars.push({ key: '', value: '' })
}

const removeEnvVar = (index) => {
  newTask.envVars.splice(index, 1)
}

const refreshMonitoring = () => {
  // 更新系统指标
  systemMetrics.cpu = Math.floor(Math.random() * 50) + 30
  systemMetrics.memory = Math.floor(Math.random() * 40) + 40
  systemMetrics.disk = Math.floor(Math.random() * 30) + 10
  
  // 添加新事件
  const eventTypes = ['info', 'success', 'warning', 'error']
  const messages = [
    '测试任务已加入队列',
    '测试执行完成',
    '系统资源使用率较高',
    '测试执行失败'
  ]
  
  const newEvent = {
    id: Date.now(),
    type: eventTypes[Math.floor(Math.random() * eventTypes.length)],
    message: messages[Math.floor(Math.random() * messages.length)],
    timestamp: new Date()
  }
  
  recentEvents.value.unshift(newEvent)
  if (recentEvents.value.length > 10) {
    recentEvents.value.pop()
  }
  
  updateStatisticsChart()
}

const refreshSchedules = () => {
  ElMessage.success('定时任务已刷新')
}

const toggleSchedule = (schedule) => {
  const status = schedule.enabled ? '启用' : '禁用'
  ElMessage.success(`定时任务已${status}`)
}

const editSchedule = (schedule) => {
  ElMessage.info('编辑定时任务功能开发中...')
}

const deleteSchedule = async (schedule) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除定时任务 "${schedule.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = scheduledTasks.value.findIndex(s => s.id === schedule.id)
    if (index > -1) {
      scheduledTasks.value.splice(index, 1)
    }
    
    ElMessage.success('定时任务删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error.message)
    }
  }
}

// 工具函数
const getTaskIcon = (type) => {
  const icons = {
    unit: 'Document',
    integration: 'Connection',
    api: 'Link',
    e2e: 'Monitor',
    performance: 'TrendCharts'
  }
  return icons[type] || 'Document'
}

const getTypeColor = (type) => {
  const colors = {
    unit: '',
    integration: 'success',
    api: 'warning',
    e2e: 'info',
    performance: 'danger'
  }
  return colors[type] || ''
}

const getTypeText = (type) => {
  const texts = {
    unit: '单元测试',
    integration: '集成测试',
    api: 'API测试',
    e2e: 'E2E测试',
    performance: '性能测试'
  }
  return texts[type] || type
}

const getPriorityColor = (priority) => {
  const colors = {
    high: 'danger',
    medium: 'warning',
    low: 'info'
  }
  return colors[priority] || ''
}

const getPriorityText = (priority) => {
  const texts = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return texts[priority] || priority
}

const getStatusIcon = (status) => {
  const icons = {
    running: 'Loading',
    queued: 'Clock',
    completed: 'CircleCheck',
    failed: 'CircleClose',
    stopped: 'VideoPause'
  }
  return icons[status] || 'InfoFilled'
}

const getStatusText = (status) => {
  const texts = {
    running: '运行中',
    queued: '队列中',
    completed: '已完成',
    failed: '失败',
    stopped: '已停止'
  }
  return texts[status] || status
}

const getStatusColor = (status) => {
  const colors = {
    running: 'warning',
    queued: 'info',
    completed: 'success',
    failed: 'danger',
    stopped: ''
  }
  return colors[status] || ''
}

const getProgressColor = (status) => {
  const colors = {
    running: '#e6a23c',
    queued: '#909399',
    completed: '#67c23a',
    failed: '#f56c6c',
    stopped: '#909399'
  }
  return colors[status] || '#409eff'
}

const getMetricColor = (value) => {
  if (value >= 80) return '#f56c6c'
  if (value >= 60) return '#e6a23c'
  return '#67c23a'
}

const getEventIcon = (type) => {
  const icons = {
    info: 'InfoFilled',
    success: 'SuccessFilled',
    warning: 'Warning',
    error: 'CircleClose'
  }
  return icons[type] || 'InfoFilled'
}

const formatDuration = (seconds) => {
  if (!seconds) return '0s'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  
  if (hours > 0) {
    return `${hours}h ${minutes}m ${secs}s`
  } else if (minutes > 0) {
    return `${minutes}m ${secs}s`
  } else {
    return `${secs}s`
  }
}

const formatDateTime = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatTime = (date) => {
  return new Date(date).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const calculateNextRun = (cronExpression) => {
  // 简单的下次运行时间计算（实际应使用cron库）
  const now = new Date()
  now.setDate(now.getDate() + 1)
  return now
}

// 图表相关
const initStatisticsChart = () => {
  if (!statisticsChartRef.value) return
  
  statisticsChart = echarts.init(statisticsChartRef.value)
  updateStatisticsChart()
}

const updateStatisticsChart = () => {
  if (!statisticsChart) return
  
  const option = {
    tooltip: {
      trigger: 'item'
    },
    series: [{
      type: 'pie',
      radius: '60%',
      data: [
        { value: runningTestsCount.value, name: '运行中', itemStyle: { color: '#e6a23c' } },
        { value: queuedTestsCount.value, name: '队列中', itemStyle: { color: '#909399' } },
        { value: completedTestsCount.value, name: '已完成', itemStyle: { color: '#67c23a' } },
        { 
          value: testTasks.value.filter(t => t.status === 'failed').length, 
          name: '失败', 
          itemStyle: { color: '#f56c6c' } 
        }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }
  
  statisticsChart.setOption(option)
}

// 生命周期
onMounted(() => {
  nextTick(() => {
    initStatisticsChart()
  })
  
  // 启动自动刷新
  if (autoRefresh.value) {
    refreshTimer = setInterval(() => {
      refreshMonitoring()
    }, 5000)
  }
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    statisticsChart?.resize()
  })
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style scoped>
.auto-test-executor {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.quick-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.status-indicator {
  display: flex;
  gap: 24px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-icon {
  font-size: 16px;
}

.status-icon.running {
  color: #e6a23c;
  animation: spin 1s linear infinite;
}

.status-icon.queued {
  color: #909399;
}

.status-icon.completed {
  color: #67c23a;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.status-text {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.test-tasks,
.monitoring-panel {
  margin-bottom: 32px;
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.task-controls,
.monitor-controls {
  display: flex;
  align-items: center;
}

.task-name {
  display: flex;
  align-items: center;
  gap: 12px;
}

.task-icon {
  color: #409eff;
  font-size: 18px;
}

.name-content {
  flex: 1;
}

.name-text {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.name-description {
  font-size: 12px;
  color: #909399;
}

.status-cell {
  display: flex;
  align-items: center;
  gap: 6px;
}

.progress-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-text {
  font-size: 12px;
  font-weight: 600;
  min-width: 35px;
}

.duration-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 500;
  color: #606266;
}

.last-run,
.next-run {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #606266;
}

.no-schedule {
  color: #c0c4cc;
  font-style: italic;
}

.action-buttons {
  display: flex;
  gap: 4px;
}

.monitoring-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.monitor-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  background: #fafafa;
}

.monitor-card h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #303133;
}

.queue-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.queue-item {
  padding: 12px;
  background: white;
  border-radius: 6px;
  border-left: 4px solid #e4e7ed;
}

.queue-item.running {
  border-left-color: #e6a23c;
}

.queue-item.queued {
  border-left-color: #909399;
}

.queue-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.queue-name {
  font-weight: 500;
  color: #303133;
}

.queue-status {
  font-size: 12px;
  color: #606266;
}

.performance-metrics {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.metric-label {
  min-width: 80px;
  font-size: 14px;
  color: #606266;
}

.metric-value {
  flex: 1;
}

.events-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.event-item {
  padding: 8px 12px;
  background: white;
  border-radius: 6px;
  border-left: 3px solid #e4e7ed;
}

.event-item.info {
  border-left-color: #409eff;
}

.event-item.success {
  border-left-color: #67c23a;
}

.event-item.warning {
  border-left-color: #e6a23c;
}

.event-item.error {
  border-left-color: #f56c6c;
}

.event-time {
  font-size: 11px;
  color: #c0c4cc;
  margin-bottom: 4px;
}

.event-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.event-icon {
  font-size: 14px;
}

.event-message {
  font-size: 13px;
  color: #303133;
}

.chart-card {
  grid-column: span 2;
}

.chart-container {
  height: 200px;
  width: 100%;
}

.env-vars {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.env-var-item {
  display: flex;
  align-items: center;
}

.cron-help {
  margin-top: 8px;
}

.schedule-management {
  padding: 0;
}

.schedule-header {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.task-detail {
  padding: 0;
}

.detail-section {
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.detail-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.detail-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #303133;
  font-weight: 600;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.info-value {
  color: #303133;
}

.config-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.config-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.config-label {
  font-weight: 500;
  color: #606266;
  min-width: 100px;
}

.config-value {
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  color: #303133;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .quick-actions {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .action-buttons {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .status-indicator {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .task-controls {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .monitoring-grid {
    grid-template-columns: 1fr;
  }
  
  .chart-card {
    grid-column: span 1;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .auto-test-executor {
    background-color: #1a1a1a;
    color: #e4e7ed;
  }
  
  .quick-actions,
  .test-tasks,
  .monitoring-panel {
    background: #2d2d2d;
    border: 1px solid #404040;
  }
  
  .monitor-card {
    background: #1a1a1a;
    border-color: #404040;
  }
  
  .queue-item,
  .event-item {
    background: #2d2d2d;
  }
  
  .config-value {
    background: #404040;
    color: #e4e7ed;
  }
}
</style>