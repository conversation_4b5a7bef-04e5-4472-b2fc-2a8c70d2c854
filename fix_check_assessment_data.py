#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复后的assessment数据检查脚本
正确解析回答数据格式
"""

import sqlite3
import json
import os

def check_sm008_moca_responses_fixed():
    """修复后的SM_008用户蒙特利尔认知评估量表回答检查"""
    print("=== 修复后的SM_008蒙特利尔认知评估量表回答检查 ===")
    
    db_path = "c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db"
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 查找蒙特利尔认知评估量表
        print("\n1. 查找蒙特利尔认知评估量表...")
        cursor.execute("""
            SELECT id, name 
            FROM assessments 
            WHERE name LIKE '%蒙特利尔%' OR name LIKE '%MoCA%' OR name LIKE '%Montreal%'
        """)
        moca_assessments = cursor.fetchall()
        
        if not moca_assessments:
            print("未找到蒙特利尔认知评估量表")
            return
        
        for assessment in moca_assessments:
            print(f"  找到评估: ID={assessment[0]}, 名称={assessment[1]}")
        
        moca_assessment_id = moca_assessments[0][0]
        
        # 2. 检查SM_008用户的回答记录
        print(f"\n2. 检查SM_008用户的回答记录...")
        cursor.execute("""
            SELECT id, answers, created_at 
            FROM assessment_responses 
            WHERE custom_id = 'SM_008' AND assessment_id = ?
        """, (moca_assessment_id,))
        responses = cursor.fetchall()
        
        if not responses:
            print("SM_008用户没有蒙特利尔认知评估量表的回答记录")
            return
        
        print(f"找到 {len(responses)} 条回答记录")
        
        # 3. 正确解析回答数据
        for i, response in enumerate(responses):
            print(f"\n  回答记录 {i+1}:")
            print(f"    记录ID: {response[0]}")
            print(f"    创建时间: {response[2]}")
            
            answers_raw = response[1]
            if not answers_raw:
                print("    无回答数据")
                continue
            
            try:
                # 解析JSON数据
                answers_data = json.loads(answers_raw)
                print(f"    回答数据类型: {type(answers_data)}")
                print(f"    回答数据长度: {len(answers_data)}")
                
                if isinstance(answers_data, list):
                    # 正确的格式：列表，每个元素是字典
                    print("\n    各问题回答情况:")
                    
                    answered_count = 0
                    total_score = 0
                    
                    for answer_item in answers_data:
                        if isinstance(answer_item, dict):
                            question_id = answer_item.get('question_id', 'unknown')
                            answer_value = answer_item.get('answer', 'no_answer')
                            score = answer_item.get('score', 0)
                            
                            # 检查是否真正回答了（不是默认值）
                            if answer_value != 'no_answer' and answer_value is not None:
                                answered_count += 1
                                total_score += score
                                status = "✓ 已回答"
                            else:
                                status = "✗ 未回答"
                            
                            print(f"      {question_id}: {status} (答案: {answer_value}, 得分: {score})")
                    
                    print(f"\n    统计结果:")
                    print(f"      总问题数: {len(answers_data)}")
                    print(f"      已回答数: {answered_count}")
                    print(f"      未回答数: {len(answers_data) - answered_count}")
                    print(f"      总得分: {total_score}")
                    print(f"      回答完成率: {answered_count/len(answers_data)*100:.1f}%")
                    
                    if answered_count == 0:
                        print("\n    ⚠️ 所有问题都未回答，可能原因:")
                        print("      1. 用户只是打开了量表但未实际填写")
                        print("      2. 前端页面交互问题")
                        print("      3. 数据提交时出现问题")
                    elif answered_count < len(answers_data):
                        print(f"\n    ⚠️ 部分问题未回答，缺失 {len(answers_data) - answered_count} 个问题")
                        print("      建议检查用户是否完整填写了量表")
                    else:
                        print("\n    ✅ 所有问题都已回答")
                        
                        # 分析得分情况
                        if total_score == 0:
                            print("      但所有问题得分都为0，可能存在评分逻辑问题")
                        else:
                            print(f"      评估得分正常，总分: {total_score}")
                
                elif isinstance(answers_data, dict):
                    # 如果是字典格式
                    print("    回答数据是字典格式")
                    answered_count = len([k for k, v in answers_data.items() if v is not None])
                    print(f"    已回答问题数: {answered_count}/{len(answers_data)}")
                
                else:
                    print(f"    未知的回答数据格式: {type(answers_data)}")
                    
            except json.JSONDecodeError as e:
                print(f"    JSON解析失败: {e}")
                print(f"    原始数据: {answers_raw[:100]}...")
        
        conn.close()
        
    except Exception as e:
        print(f"检查过程中出错: {str(e)}")
    
    print("\n=== 检查完成 ===")

if __name__ == "__main__":
    check_sm008_moca_responses_fixed()