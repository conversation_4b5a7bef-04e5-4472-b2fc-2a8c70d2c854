import requests
import requests
import json

def test_single_api():
    """测试单个API调用"""
    
    base_url = "http://localhost:8000"
    
    print("=== 测试单个API调用 ===")
    
    # 只测试一个API调用
    print("\n使用X-User-ID头部获取SM_008的所有评估:")
    headers = {"X-User-ID": "SM_008"}
    try:
        response = requests.get(f"{base_url}/api/mobile/assessments", headers=headers)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"\n解析后的JSON数据:")
                print(json.dumps(data, ensure_ascii=False, indent=2))
            except Exception as e:
                print(f"JSON解析失败: {e}")
        
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == '__main__':
    test_single_api()