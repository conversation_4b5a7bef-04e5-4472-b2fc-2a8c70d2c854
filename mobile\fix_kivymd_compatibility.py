#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
KivyMD 2.0.1dev0 兼容性修复脚本

这个脚本修复mobile文件夹中与KivyMD 2.0.1dev0相关的兼容性问题，
主要解决密码输入框图标事件绑定和其他UI组件的兼容性问题。
"""

import os
import re
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class KivyMDCompatibilityFixer:
    """KivyMD兼容性修复器"""
    
    def __init__(self, mobile_dir):
        self.mobile_dir = Path(mobile_dir)
        self.fixes_applied = []
        self.errors = []
        
    def fix_password_icon_binding(self):
        """修复密码输入框图标事件绑定问题"""
        login_screen_path = self.mobile_dir / "screens" / "login_screen.py"
        
        if not login_screen_path.exists():
            self.errors.append("login_screen.py 文件不存在")
            return False
            
        try:
            with open(login_screen_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找需要替换的方法
            old_method_pattern = r'def _set_password_icon_callback\(self, dt=None\):.*?(?=def|\Z)'
            
            new_method = '''def _set_password_icon_callback(self, dt=None):
        """设置密码图标的点击事件 - KivyMD 2.0.1兼容版本"""
        if not hasattr(self.ids, 'password'):
            Clock.schedule_once(self._set_password_icon_callback, 0.5)
            return

        password_field = self.ids.password
        
        try:
            # 方法1：直接通过ID访问（最可靠）
            if hasattr(self.ids, 'password_icon'):
                icon = self.ids.password_icon
                # 清除之前的绑定
                icon.unbind(on_release=self._on_password_icon_release)
                # 绑定新的事件
                icon.bind(on_release=self._on_password_icon_release)
                logger.info("密码图标事件绑定成功（方法1）")
                return
                
            # 方法2：遍历查找图标组件
            for child in password_field.children:
                if (hasattr(child, 'icon') and 
                    hasattr(child, 'on_release') and 
                    'eye' in str(getattr(child, 'icon', ''))):
                    # 清除之前的绑定
                    child.unbind(on_release=self._on_password_icon_release)
                    # 绑定新的事件
                    child.bind(on_release=self._on_password_icon_release)
                    logger.info("密码图标事件绑定成功（方法2）")
                    return
                    
        except Exception as e:
            logger.error(f"密码图标事件绑定失败: {e}")
            # 延迟重试
            Clock.schedule_once(self._set_password_icon_callback, 1.0)

    def _on_password_icon_release(self, instance):
        """密码图标点击事件处理器"""
        self.toggle_password_visibility()'''
            
            # 使用正则表达式替换方法
            if re.search(old_method_pattern, content, re.DOTALL):
                new_content = re.sub(old_method_pattern, new_method, content, flags=re.DOTALL)
                
                # 写回文件
                with open(login_screen_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                    
                self.fixes_applied.append("修复了密码输入框图标事件绑定")
                logger.info("成功修复密码输入框图标事件绑定")
                return True
            else:
                self.errors.append("未找到需要修复的密码图标方法")
                return False
                
        except Exception as e:
            self.errors.append(f"修复密码图标绑定时出错: {e}")
            return False
    
    def create_common_components(self):
        """创建统一的基础组件模块"""
        components_path = self.mobile_dir / "utils" / "common_components.py"
        
        component_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一的基础组件模块

提供KivyMD 2.0.1dev0兼容的基础组件，确保整个应用的UI一致性。
"""

from kivymd.uix.textfield import MDTextField
from kivymd.uix.button import MDButton
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
from kivy.metrics import dp
from theme import AppTheme

class BaseFormField(MDTextField):
    """统一的表单输入框基类"""
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.mode = "outlined"
        self.size_hint_y = None
        self.height = dp(56)
        self.font_size = dp(16)

class BaseButton(MDButton):
    """统一的按钮基类"""
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.style = "filled"
        self.height = dp(56)
        self.elevation = 2
        self.md_bg_color = AppTheme.ACCENT_COLOR

class BaseCard(MDCard):
    """统一的卡片基类"""
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.elevation = 2
        self.radius = [dp(16)]
        self.padding = dp(24)
        self.spacing = dp(16)

class BaseLabel(MDLabel):
    """统一的标签基类"""
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.theme_text_color = "Primary"
        self.size_hint_y = None
        self.height = dp(32)

# 兼容性工具函数
def safe_bind_event(widget, event_name, callback):
    """安全的事件绑定，兼容不同版本的KivyMD"""
    try:
        # 先解绑之前的事件
        widget.unbind(**{event_name: callback})
    except:
        pass
    
    try:
        # 绑定新事件
        widget.bind(**{event_name: callback})
        return True
    except Exception as e:
        print(f"事件绑定失败: {e}")
        return False

def get_kivymd_version():
    """获取KivyMD版本信息"""
    try:
        import kivymd
        return getattr(kivymd, '__version__', 'unknown')
    except:
        return 'unknown'

def is_kivymd_2_0_1_or_later():
    """检查是否为KivyMD 2.0.1或更高版本"""
    version = get_kivymd_version()
    if version == 'unknown':
        return False
    
    try:
        # 简单的版本比较
        major, minor, patch = version.split('.')[:3]
        return (int(major) > 2 or 
                (int(major) == 2 and int(minor) > 0) or
                (int(major) == 2 and int(minor) == 0 and int(patch) >= 1))
    except:
        return False
'''
        
        try:
            with open(components_path, 'w', encoding='utf-8') as f:
                f.write(component_content)
            
            self.fixes_applied.append("创建了统一的基础组件模块")
            logger.info("成功创建统一的基础组件模块")
            return True
            
        except Exception as e:
            self.errors.append(f"创建基础组件模块时出错: {e}")
            return False
    
    def fix_logo_manager(self):
        """优化Logo管理器，防止重复实例"""
        logo_manager_path = self.mobile_dir / "widgets" / "logo_manager.py"
        
        if not logo_manager_path.exists():
            self.errors.append("logo_manager.py 文件不存在")
            return False
        
        try:
            with open(logo_manager_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找register_logo方法并优化
            register_method_pattern = r'def register_logo\(self, logo_instance\):.*?(?=def|\Z)'
            
            new_register_method = '''def register_logo(self, logo_instance):
        """注册Logo实例，确保每个屏幕只有一个Logo"""
        # 如果已经注册过这个实例，直接返回
        if logo_instance in self._logo_instances:
            return False

        # 添加到全局实例列表
        self._logo_instances.append(logo_instance)

        # 尝试获取Logo所属的屏幕
        screen = self._find_parent_screen(logo_instance)
        if not screen:
            return True

        screen_name = screen.__class__.__name__
        
        # 检查是否已有Logo
        if screen_name in self._screen_logos:
            existing_logos = self._screen_logos[screen_name]
            if existing_logos:
                # 移除新的Logo，保留第一个
                if logo_instance.parent:
                    logo_instance.parent.remove_widget(logo_instance)
                print(f"警告: 屏幕 {screen_name} 已有Logo，移除重复实例")
                return False
        
        # 注册新Logo
        if screen_name not in self._screen_logos:
            self._screen_logos[screen_name] = []
        self._screen_logos[screen_name].append(logo_instance)
        return True'''
            
            if re.search(register_method_pattern, content, re.DOTALL):
                new_content = re.sub(register_method_pattern, new_register_method, content, flags=re.DOTALL)
                
                with open(logo_manager_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                self.fixes_applied.append("优化了Logo管理器，防止重复实例")
                logger.info("成功优化Logo管理器")
                return True
            else:
                self.errors.append("未找到需要优化的register_logo方法")
                return False
                
        except Exception as e:
            self.errors.append(f"优化Logo管理器时出错: {e}")
            return False
    
    def run_all_fixes(self):
        """运行所有修复"""
        logger.info("开始KivyMD 2.0.1dev0兼容性修复...")
        
        # 执行所有修复
        fixes = [
            ("密码图标事件绑定", self.fix_password_icon_binding),
            ("创建基础组件模块", self.create_common_components),
            ("优化Logo管理器", self.fix_logo_manager),
        ]
        
        success_count = 0
        for fix_name, fix_func in fixes:
            logger.info(f"正在执行: {fix_name}")
            if fix_func():
                success_count += 1
            else:
                logger.error(f"修复失败: {fix_name}")
        
        # 输出结果
        logger.info(f"\n修复完成！成功: {success_count}/{len(fixes)}")
        
        if self.fixes_applied:
            logger.info("\n已应用的修复:")
            for fix in self.fixes_applied:
                logger.info(f"  ✅ {fix}")
        
        if self.errors:
            logger.error("\n遇到的错误:")
            for error in self.errors:
                logger.error(f"  ❌ {error}")
        
        return success_count == len(fixes)

def main():
    """主函数"""
    # 获取mobile目录路径
    current_dir = Path(__file__).parent
    mobile_dir = current_dir
    
    # 创建修复器并运行
    fixer = KivyMDCompatibilityFixer(mobile_dir)
    success = fixer.run_all_fixes()
    
    if success:
        print("\n🎉 所有修复都已成功应用！")
        print("现在可以运行应用测试修复效果。")
    else:
        print("\n⚠️ 部分修复失败，请检查错误信息。")
    
    return success

if __name__ == "__main__":
    main()
