#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API header处理
"""

import requests
import json
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_api_with_different_methods():
    """测试不同的API调用方法"""
    base_url = "http://localhost:8000/api"
    custom_id = "SM_006"
    
    # 方法1: 通过Query参数传递custom_id
    logger.info("=== 方法1: 通过Query参数传递custom_id ===")
    
    url = f"{base_url}/mobile/assessments?custom_id={custom_id}"
    headers = {
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(url, headers=headers)
        logger.info(f"API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            logger.info(f"API返回数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if data.get('status') == 'success' and 'data' in data:
                assessments_data = data['data']
                if isinstance(assessments_data, dict) and 'assessments' in assessments_data:
                    assessments = assessments_data['assessments']
                else:
                    assessments = assessments_data if isinstance(assessments_data, list) else []
                
                logger.info(f"返回的量表总数: {len(assessments)}")
                
                # 统计不同状态的量表
                pending_count = sum(1 for a in assessments if a.get('status') == 'pending')
                completed_count = sum(1 for a in assessments if a.get('status') == 'completed')
                
                logger.info(f"Pending状态量表: {pending_count}")
                logger.info(f"Completed状态量表: {completed_count}")
            else:
                logger.error(f"API返回错误: {data}")
        else:
            logger.error(f"API请求失败: {response.status_code} - {response.text}")
            
    except Exception as e:
        logger.error(f"API请求异常: {str(e)}")
    
    # 方法2: 通过Header传递custom_id
    logger.info("\n=== 方法2: 通过Header传递custom_id ===")
    
    url = f"{base_url}/mobile/assessments"
    headers = {
        'X-User-ID': custom_id,
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(url, headers=headers)
        logger.info(f"API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            logger.info(f"API返回数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if data.get('status') == 'success' and 'data' in data:
                assessments_data = data['data']
                if isinstance(assessments_data, dict) and 'assessments' in assessments_data:
                    assessments = assessments_data['assessments']
                else:
                    assessments = assessments_data if isinstance(assessments_data, list) else []
                
                logger.info(f"返回的量表总数: {len(assessments)}")
                
                # 统计不同状态的量表
                pending_count = sum(1 for a in assessments if a.get('status') == 'pending')
                completed_count = sum(1 for a in assessments if a.get('status') == 'completed')
                
                logger.info(f"Pending状态量表: {pending_count}")
                logger.info(f"Completed状态量表: {completed_count}")
            else:
                logger.error(f"API返回错误: {data}")
        else:
            logger.error(f"API请求失败: {response.status_code} - {response.text}")
            
    except Exception as e:
        logger.error(f"API请求异常: {str(e)}")
    
    # 方法3: 不传递任何用户ID，获取所有数据
    logger.info("\n=== 方法3: 不传递任何用户ID，获取所有数据 ===")
    
    url = f"{base_url}/mobile/assessments"
    headers = {
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(url, headers=headers)
        logger.info(f"API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            logger.info(f"API返回数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if data.get('status') == 'success' and 'data' in data:
                assessments_data = data['data']
                if isinstance(assessments_data, dict) and 'assessments' in assessments_data:
                    assessments = assessments_data['assessments']
                else:
                    assessments = assessments_data if isinstance(assessments_data, list) else []
                
                logger.info(f"返回的量表总数: {len(assessments)}")
                
                # 统计SM_006的量表
                sm_006_assessments = [a for a in assessments if a.get('custom_id') == custom_id]
                logger.info(f"SM_006的量表数量: {len(sm_006_assessments)}")
                
                # 统计不同状态的量表
                pending_count = sum(1 for a in sm_006_assessments if a.get('status') == 'pending')
                completed_count = sum(1 for a in sm_006_assessments if a.get('status') == 'completed')
                
                logger.info(f"SM_006 Pending状态量表: {pending_count}")
                logger.info(f"SM_006 Completed状态量表: {completed_count}")
            else:
                logger.error(f"API返回错误: {data}")
        else:
            logger.error(f"API请求失败: {response.status_code} - {response.text}")
            
    except Exception as e:
        logger.error(f"API请求异常: {str(e)}")

if __name__ == "__main__":
    test_api_with_different_methods()