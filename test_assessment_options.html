<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>评估量表选项显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .question {
            margin: 10px 0;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 3px;
        }
        .question-text {
            font-weight: bold;
            margin-bottom: 8px;
        }
        .options-display {
            margin: 10px 0;
            padding: 10px;
            background-color: #f0f9ff;
            border-radius: 4px;
            border-left: 3px solid #409eff;
        }
        .options-label {
            font-size: 12px;
            color: #909399;
            margin-bottom: 8px;
            font-weight: 500;
        }
        .options-list {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        .option-display {
            font-size: 13px;
            color: #606266;
            padding: 2px 0;
        }
        .option-display .option-score {
            color: #909399;
            font-size: 11px;
            margin-left: 8px;
        }
        .answer-value {
            background-color: #f5f7fa;
            padding: 10px;
            border-radius: 4px;
            margin: 8px 0;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>评估量表选项显示测试</h1>
    
    <div class="test-section">
        <h2>测试场景1: SDS评估量表标准选项格式</h2>
        <div id="test1"></div>
    </div>
    
    <div class="test-section">
        <h2>测试场景2: 模拟前端处理逻辑</h2>
        <div id="test2"></div>
    </div>
    
    <div class="test-section">
        <h2>测试场景3: 问题匹配逻辑</h2>
        <div id="test3"></div>
    </div>

    <script>
        // 模拟SDS评估量表的标准模板数据
        const sdsTemplateData = {
            questions: [
                {
                    question_id: "sds_1",
                    question_text: "我感到情绪沮丧，郁闷",
                    question_type: "single_choice",
                    is_required: true,
                    order: 1,
                    options: [
                        {value: 1, label: "很少", score: 1},
                        {value: 2, label: "有时", score: 2},
                        {value: 3, label: "经常", score: 3},
                        {value: 4, label: "持续", score: 4}
                    ]
                },
                {
                    question_id: "sds_2",
                    question_text: "早晨是我一天中最好的时光",
                    question_type: "single_choice",
                    is_required: true,
                    order: 2,
                    options: [
                        {value: 1, label: "持续", score: 1},
                        {value: 2, label: "经常", score: 2},
                        {value: 3, label: "有时", score: 3},
                        {value: 4, label: "很少", score: 4}
                    ]
                }
            ]
        };
        
        // 模拟回答数据
        const answersData = [
            {
                question_id: "sds_1",
                question: "我感到情绪沮丧，郁闷",
                answer: "有时",
                value: 2,
                score: 2
            },
            {
                question_id: "sds_2",
                question: "早晨是我一天中最好的时光",
                answer: "经常",
                value: 2,
                score: 2
            }
        ];
        
        // 模拟前端的getFormattedOptions函数
        function getFormattedOptions(question) {
            if (!question) return [];
            
            let options = [];
            
            if (question.options?.choices) {
                options = question.options.choices;
            } else if (question.options && Array.isArray(question.options)) {
                options = question.options;
            } else if (question.choices) {
                options = question.choices;
            } else if (question.options && typeof question.options === 'string') {
                try {
                    const parsed = JSON.parse(question.options);
                    if (parsed.choices) {
                        options = parsed.choices;
                    } else if (Array.isArray(parsed)) {
                        options = parsed;
                    }
                } catch (e) {
                    console.warn('Failed to parse options:', question.options);
                    return [];
                }
            }
            
            return options || [];
        }
        
        // 模拟前端的getOptionText函数
        function getOptionText(option) {
            if (typeof option === 'string') {
                return option;
            }
            if (typeof option === 'object' && option !== null) {
                return option.text || option.label || option.value || JSON.stringify(option);
            }
            return String(option);
        }
        
        // 模拟前端的getQuestionOptions函数
        function getQuestionOptions(answer, index, templateData) {
            // 如果有问题ID，优先尝试匹配
            if (answer.question_id && templateData?.questions) {
                const question = templateData.questions.find(q => 
                    q.question_id === answer.question_id || 
                    q.id === answer.question_id ||
                    q.question_id === answer.question_id.toString() ||
                    q.id === answer.question_id.toString()
                );
                if (question) {
                    const options = getFormattedOptions(question);
                    if (options && options.length > 0) {
                        return options;
                    }
                }
            }
            
            // 从模板数据中按索引获取问题选项
            if (templateData?.questions && templateData.questions[index]) {
                const question = templateData.questions[index];
                const options = getFormattedOptions(question);
                if (options && options.length > 0) {
                    return options;
                }
            }
            
            // 如果答案对象中直接包含选项
            if (answer.options && Array.isArray(answer.options)) {
                return answer.options;
            }
            
            return null;
        }
        
        // 渲染测试结果
        function renderTest1() {
            const container = document.getElementById('test1');
            let html = '';
            
            answersData.forEach((answer, index) => {
                const options = getQuestionOptions(answer, index, sdsTemplateData);
                
                html += `
                    <div class="question">
                        <div class="question-text">${answer.question}</div>
                        ${options ? `
                            <div class="options-display">
                                <div class="options-label">选项:</div>
                                <div class="options-list">
                                    ${options.map(option => `
                                        <div class="option-display">
                                            <span class="option-text">${getOptionText(option)}</span>
                                            ${option.score !== undefined || option.value !== undefined ? 
                                                `<span class="option-score">(${option.score || option.value}分)</span>` : ''}
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        ` : '<div style="color: red;">未找到选项数据</div>'}
                        <div class="answer-value">
                            <strong>回答:</strong> ${answer.answer} (${answer.value}分)
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        function renderTest2() {
            const container = document.getElementById('test2');
            
            // 测试getFormattedOptions函数对不同格式的处理
            const testQuestions = [
                {
                    name: "标准格式 - options数组",
                    question: {
                        options: [
                            {value: 1, label: "选项1", score: 1},
                            {value: 2, label: "选项2", score: 2}
                        ]
                    }
                },
                {
                    name: "choices格式",
                    question: {
                        choices: [
                            {value: 1, label: "选项A", score: 1},
                            {value: 2, label: "选项B", score: 2}
                        ]
                    }
                },
                {
                    name: "options.choices格式",
                    question: {
                        options: {
                            choices: [
                                {value: 1, label: "选项X", score: 1},
                                {value: 2, label: "选项Y", score: 2}
                            ]
                        }
                    }
                }
            ];
            
            let html = '';
            testQuestions.forEach(test => {
                const options = getFormattedOptions(test.question);
                html += `
                    <div class="question">
                        <div class="question-text">${test.name}</div>
                        <div class="options-display">
                            <div class="options-label">解析结果:</div>
                            <div class="options-list">
                                ${options.map(option => `
                                    <div class="option-display">
                                        <span class="option-text">${getOptionText(option)}</span>
                                        ${option.score !== undefined ? 
                                            `<span class="option-score">(${option.score}分)</span>` : ''}
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        function renderTest3() {
            const container = document.getElementById('test3');
            
            // 测试问题匹配逻辑
            const testAnswer = {
                question_id: "sds_1",
                question: "我感到情绪沮丧，郁闷",
                answer: "有时",
                value: 2
            };
            
            const options = getQuestionOptions(testAnswer, 0, sdsTemplateData);
            
            let html = `
                <div class="question">
                    <div class="question-text">测试问题ID匹配: ${testAnswer.question_id}</div>
                    <div style="margin: 10px 0; padding: 10px; background: #f0f0f0;">
                        <strong>匹配结果:</strong> ${options ? '成功找到选项' : '未找到选项'}
                    </div>
                    ${options ? `
                        <div class="options-display">
                            <div class="options-label">匹配到的选项:</div>
                            <div class="options-list">
                                ${options.map(option => `
                                    <div class="option-display">
                                        <span class="option-text">${getOptionText(option)}</span>
                                        <span class="option-score">(${option.score}分)</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : '<div style="color: red;">匹配失败</div>'}
                </div>
            `;
            
            container.innerHTML = html;
        }
        
        // 执行测试
        renderTest1();
        renderTest2();
        renderTest3();
        
        // 输出调试信息
        console.log('SDS模板数据:', sdsTemplateData);
        console.log('回答数据:', answersData);
        console.log('第一个问题的选项:', getQuestionOptions(answersData[0], 0, sdsTemplateData));
    </script>
</body>
</html>