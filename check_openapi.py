import requests
import json

def check_mobile_routes():
    try:
        # 获取OpenAPI规范
        response = requests.get('http://localhost:8006/openapi.json')
        if response.status_code == 200:
            openapi_data = response.json()
            
            # 检查所有路径
            paths = openapi_data.get('paths', {})
            print(f"总共有 {len(paths)} 个API路径")
            
            # 查找mobile相关的路径
            mobile_paths = []
            for path in paths.keys():
                if 'mobile' in path.lower():
                    mobile_paths.append(path)
                    print(f"找到mobile路径: {path}")
                    
                    # 显示该路径的方法
                    methods = paths[path].keys()
                    for method in methods:
                        if method in ['get', 'post', 'put', 'delete', 'patch']:
                            summary = paths[path][method].get('summary', '无描述')
                            print(f"  {method.upper()}: {summary}")
            
            if not mobile_paths:
                print("未找到任何mobile相关的路径")
                print("\n所有可用路径:")
                for path in sorted(paths.keys()):
                    print(f"  {path}")
            
            return mobile_paths
        else:
            print(f"获取OpenAPI规范失败: {response.status_code}")
            return []
    except Exception as e:
        print(f"检查OpenAPI规范时出错: {e}")
        return []

if __name__ == "__main__":
    check_mobile_routes()