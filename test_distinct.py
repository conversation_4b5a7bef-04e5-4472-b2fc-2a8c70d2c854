from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine, desc, text

# 创建数据库连接
engine = create_engine('sqlite:///c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db')
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
db = SessionLocal()

try:
    # 测试所有SM_006的量表数据
    print("=== 查询SM_006的所有量表 ===")
    result = db.execute(text("""
        SELECT DISTINCT a.id, a.name, a.status, a.created_at
        FROM assessments a
        LEFT JOIN assessment_distributions ad ON a.id = ad.assessment_id
        WHERE a.custom_id = 'SM_006'
        ORDER BY a.created_at DESC
    """))
    
    rows = result.fetchall()
    print(f'查询结果数量: {len(rows)}')
    
    for row in rows:
        print(f'  ID: {row[0]}, 名称: {row[1]}, 状态: {row[2]}, 创建时间: {row[3]}')
    
    # 测试不带distinct的查询对比
    print("\n=== 查询SM_006的量表（不去重） ===")
    result2 = db.execute(text("""
        SELECT a.id, a.name, a.status, a.created_at
        FROM assessments a
        LEFT JOIN assessment_distributions ad ON a.id = ad.assessment_id
        WHERE a.custom_id = 'SM_006'
        ORDER BY a.created_at DESC
    """))
    
    rows2 = result2.fetchall()
    print(f'查询结果数量: {len(rows2)}')
    
    for row in rows2:
        print(f'  ID: {row[0]}, 名称: {row[1]}, 状态: {row[2]}, 创建时间: {row[3]}')
        
finally:
    db.close()