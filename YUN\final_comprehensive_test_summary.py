#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终综合测试总结报告
汇总所有测试结果，生成完整的系统健康状况报告
包括前端、后端、API、数据库等各个方面的测试结果
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Any
import glob

class ComprehensiveTestSummary:
    def __init__(self):
        self.test_reports = []
        self.summary_data = {
            "frontend_tests": {},
            "backend_tests": {},
            "api_tests": {},
            "integration_tests": {},
            "overall_status": {}
        }
        
    def load_test_reports(self):
        """加载所有测试报告"""
        report_patterns = [
            "*test_report_*.json",
            "*fix_report_*.json",
            "*enhanced_test_report_*.json",
            "*integration_test_report_*.json"
        ]
        
        for pattern in report_patterns:
            for file_path in glob.glob(pattern):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        report_data = json.load(f)
                        self.test_reports.append({
                            "file": file_path,
                            "data": report_data
                        })
                        print(f"加载测试报告: {file_path}")
                except Exception as e:
                    print(f"加载报告失败 {file_path}: {str(e)}")
                    
    def analyze_frontend_tests(self):
        """分析前端测试结果"""
        frontend_reports = [
            r for r in self.test_reports 
            if "frontend" in r["file"].lower() or "integration" in r["file"].lower()
        ]
        
        if not frontend_reports:
            self.summary_data["frontend_tests"] = {
                "status": "未测试",
                "details": "未找到前端测试报告"
            }
            return
            
        # 获取最新的前端测试报告
        latest_report = max(frontend_reports, key=lambda x: os.path.getmtime(x["file"]))
        report_data = latest_report["data"]
        
        if "summary" in report_data:
            summary = report_data["summary"]
            self.summary_data["frontend_tests"] = {
                "status": "已测试",
                "total_tests": summary.get("total_tests", 0),
                "successful": summary.get("successful", 0),
                "failed": summary.get("failed", 0),
                "success_rate": summary.get("success_rate", "0%"),
                "key_findings": self.extract_key_findings(report_data),
                "report_file": latest_report["file"]
            }
        else:
            self.summary_data["frontend_tests"] = {
                "status": "报告格式异常",
                "details": "无法解析测试摘要"
            }
            
    def analyze_backend_tests(self):
        """分析后端测试结果"""
        backend_reports = [
            r for r in self.test_reports 
            if "api" in r["file"].lower() or "comprehensive" in r["file"].lower()
        ]
        
        if not backend_reports:
            self.summary_data["backend_tests"] = {
                "status": "未测试",
                "details": "未找到后端测试报告"
            }
            return
            
        # 获取最新的后端测试报告
        latest_report = max(backend_reports, key=lambda x: os.path.getmtime(x["file"]))
        report_data = latest_report["data"]
        
        if "summary" in report_data:
            summary = report_data["summary"]
            self.summary_data["backend_tests"] = {
                "status": "已测试",
                "total_tests": summary.get("total_tests", 0),
                "successful": summary.get("successful", 0),
                "failed": summary.get("failed", 0),
                "success_rate": summary.get("success_rate", "0%"),
                "key_findings": self.extract_key_findings(report_data),
                "report_file": latest_report["file"]
            }
        else:
            self.summary_data["backend_tests"] = {
                "status": "报告格式异常",
                "details": "无法解析测试摘要"
            }
            
    def analyze_api_tests(self):
        """分析API测试结果"""
        api_reports = [
            r for r in self.test_reports 
            if "api" in r["file"].lower() or "routes" in r["file"].lower()
        ]
        
        if not api_reports:
            self.summary_data["api_tests"] = {
                "status": "未测试",
                "details": "未找到API测试报告"
            }
            return
            
        # 分析API测试结果
        api_status = {
            "working_apis": [],
            "failing_apis": [],
            "missing_apis": []
        }
        
        for report in api_reports:
            report_data = report["data"]
            if "test_results" in report_data:
                for test in report_data["test_results"]:
                    if "API" in test.get("test_name", ""):
                        if test.get("success", False):
                            api_status["working_apis"].append(test["test_name"])
                        else:
                            api_status["failing_apis"].append(test["test_name"])
                            
        self.summary_data["api_tests"] = {
            "status": "已测试",
            "working_count": len(api_status["working_apis"]),
            "failing_count": len(api_status["failing_apis"]),
            "working_apis": api_status["working_apis"][:10],  # 只显示前10个
            "failing_apis": api_status["failing_apis"][:10],
            "details": api_status
        }
        
    def extract_key_findings(self, report_data: Dict) -> List[str]:
        """提取关键发现"""
        findings = []
        
        # 从失败的测试中提取关键问题
        if "failed_tests" in report_data:
            failed_tests = report_data["failed_tests"]
            if failed_tests:
                findings.append(f"发现 {len(failed_tests)} 个失败的测试")
                
                # 分析失败原因
                error_types = {}
                for test in failed_tests:
                    details = test.get("details", "")
                    if "404" in details:
                        error_types["404错误"] = error_types.get("404错误", 0) + 1
                    elif "500" in details:
                        error_types["500错误"] = error_types.get("500错误", 0) + 1
                    elif "403" in details or "401" in details:
                        error_types["认证错误"] = error_types.get("认证错误", 0) + 1
                    elif "422" in details:
                        error_types["参数错误"] = error_types.get("参数错误", 0) + 1
                    elif "连接" in details or "Connection" in details:
                        error_types["连接错误"] = error_types.get("连接错误", 0) + 1
                        
                for error_type, count in error_types.items():
                    findings.append(f"{error_type}: {count} 个")
                    
        # 从成功的测试中提取积极信息
        if "test_results" in report_data:
            successful_tests = [t for t in report_data["test_results"] if t.get("success", False)]
            if successful_tests:
                findings.append(f"成功通过 {len(successful_tests)} 个测试")
                
        # 从建议中提取关键信息
        if "recommendations" in report_data:
            recommendations = report_data["recommendations"]
            if recommendations:
                findings.extend(recommendations[:3])  # 只取前3个建议
                
        return findings
        
    def calculate_overall_status(self):
        """计算总体状态"""
        total_tests = 0
        total_successful = 0
        total_failed = 0
        
        test_categories = ["frontend_tests", "backend_tests"]
        
        for category in test_categories:
            if category in self.summary_data and "total_tests" in self.summary_data[category]:
                total_tests += self.summary_data[category]["total_tests"]
                total_successful += self.summary_data[category]["successful"]
                total_failed += self.summary_data[category]["failed"]
                
        overall_success_rate = (total_successful / total_tests * 100) if total_tests > 0 else 0
        
        # 确定系统健康状态
        if overall_success_rate >= 90:
            health_status = "优秀"
            health_color = "green"
        elif overall_success_rate >= 75:
            health_status = "良好"
            health_color = "yellow"
        elif overall_success_rate >= 50:
            health_status = "一般"
            health_color = "orange"
        else:
            health_status = "需要改进"
            health_color = "red"
            
        self.summary_data["overall_status"] = {
            "total_tests": total_tests,
            "successful": total_successful,
            "failed": total_failed,
            "success_rate": f"{overall_success_rate:.1f}%",
            "health_status": health_status,
            "health_color": health_color,
            "timestamp": datetime.now().isoformat()
        }
        
    def generate_priority_recommendations(self) -> List[Dict]:
        """生成优先级修复建议"""
        recommendations = []
        
        # 基于测试结果生成建议
        if self.summary_data["frontend_tests"].get("failed", 0) > 0:
            recommendations.append({
                "priority": "高",
                "category": "前端",
                "issue": "前端功能测试失败",
                "action": "检查前端页面元素和导航结构",
                "impact": "影响用户体验"
            })
            
        if self.summary_data["backend_tests"].get("failed", 0) > 0:
            recommendations.append({
                "priority": "高",
                "category": "后端",
                "issue": "后端API测试失败",
                "action": "检查API路由配置和数据库连接",
                "impact": "影响系统功能"
            })
            
        if self.summary_data["api_tests"].get("failing_count", 0) > 5:
            recommendations.append({
                "priority": "中",
                "category": "API",
                "issue": "多个API端点失败",
                "action": "系统性检查API路由和认证机制",
                "impact": "影响前后端通信"
            })
            
        # 如果没有具体问题，给出一般性建议
        if not recommendations:
            recommendations.append({
                "priority": "低",
                "category": "维护",
                "issue": "系统运行正常",
                "action": "继续监控系统状态，定期进行测试",
                "impact": "保持系统稳定性"
            })
            
        return recommendations
        
    def generate_summary_report(self):
        """生成总结报告"""
        print("\n" + "="*60)
        print("健康管理系统 - 综合测试总结报告")
        print("="*60)
        print(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"测试报告数量: {len(self.test_reports)}")
        
        # 总体状态
        overall = self.summary_data["overall_status"]
        print(f"\n📊 总体状态: {overall['health_status']} ({overall['success_rate']})")
        print(f"   总测试数: {overall['total_tests']}")
        print(f"   成功: {overall['successful']}")
        print(f"   失败: {overall['failed']}")
        
        # 前端测试结果
        frontend = self.summary_data["frontend_tests"]
        if frontend.get("status") == "已测试":
            print(f"\n🖥️  前端测试: {frontend['success_rate']} 通过率")
            print(f"   测试数: {frontend['total_tests']}, 成功: {frontend['successful']}, 失败: {frontend['failed']}")
            if frontend.get("key_findings"):
                print("   关键发现:")
                for finding in frontend["key_findings"][:3]:
                    print(f"   - {finding}")
        else:
            print(f"\n🖥️  前端测试: {frontend['status']}")
            
        # 后端测试结果
        backend = self.summary_data["backend_tests"]
        if backend.get("status") == "已测试":
            print(f"\n⚙️  后端测试: {backend['success_rate']} 通过率")
            print(f"   测试数: {backend['total_tests']}, 成功: {backend['successful']}, 失败: {backend['failed']}")
            if backend.get("key_findings"):
                print("   关键发现:")
                for finding in backend["key_findings"][:3]:
                    print(f"   - {finding}")
        else:
            print(f"\n⚙️  后端测试: {backend['status']}")
            
        # API测试结果
        api_tests = self.summary_data["api_tests"]
        if api_tests.get("status") == "已测试":
            print(f"\n🔗 API测试: {api_tests['working_count']} 个正常, {api_tests['failing_count']} 个失败")
            if api_tests.get("working_apis"):
                print("   正常API (部分):")
                for api in api_tests["working_apis"][:3]:
                    print(f"   ✓ {api}")
            if api_tests.get("failing_apis"):
                print("   失败API (部分):")
                for api in api_tests["failing_apis"][:3]:
                    print(f"   ✗ {api}")
        else:
            print(f"\n🔗 API测试: {api_tests['status']}")
            
        # 优先级建议
        recommendations = self.generate_priority_recommendations()
        print("\n🔧 优先级修复建议:")
        for i, rec in enumerate(recommendations[:5], 1):
            print(f"   {i}. [{rec['priority']}] {rec['category']}: {rec['issue']}")
            print(f"      建议: {rec['action']}")
            print(f"      影响: {rec['impact']}")
            
        print("\n" + "="*60)
        
        # 保存详细报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"comprehensive_test_summary_{timestamp}.json"
        
        detailed_report = {
            "summary": self.summary_data,
            "recommendations": recommendations,
            "test_reports_analyzed": [r["file"] for r in self.test_reports],
            "generation_time": datetime.now().isoformat()
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(detailed_report, f, ensure_ascii=False, indent=2)
            
        print(f"详细报告已保存到: {report_file}")
        
    def run_analysis(self):
        """运行完整分析"""
        print("开始加载和分析测试报告...")
        
        # 加载所有测试报告
        self.load_test_reports()
        
        if not self.test_reports:
            print("未找到任何测试报告文件")
            return
            
        # 分析各类测试
        self.analyze_frontend_tests()
        self.analyze_backend_tests()
        self.analyze_api_tests()
        
        # 计算总体状态
        self.calculate_overall_status()
        
        # 生成总结报告
        self.generate_summary_report()

def main():
    """主函数"""
    analyzer = ComprehensiveTestSummary()
    analyzer.run_analysis()

if __name__ == "__main__":
    main()