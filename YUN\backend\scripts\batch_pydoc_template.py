import os
import re

SRC_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '../'))
PYDOC_TEMPLATE = '"""\nTODO: 补充模块/函数/类说明\n"""\n'

def insert_pydoc(fpath):
    with open(fpath, 'r', encoding='utf-8') as f:
        code = f.read()
    # 仅在无PyDoc时插入
    if '"""' in code:
        return
    # 在第一个def/class前插入
    code_new = re.sub(r'^(def |class )', PYDOC_TEMPLATE + r'\1', code, flags=re.MULTILINE)
    with open(fpath, 'w', encoding='utf-8') as f:
        f.write(code_new)
    print(f'PyDoc inserted: {fpath}')

def batch_pydoc():
    for root, dirs, files in os.walk(SRC_DIR):
        for fname in files:
            if fname.endswith('.py'):
                insert_pydoc(os.path.join(root, fname))
    print('批量 PyDoc 注释模板插入完成，请人工补全具体说明！')

if __name__ == '__main__':
    batch_pydoc() 