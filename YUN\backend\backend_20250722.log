2025-07-22 21:49:01,458 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-22 21:49:01,469 - auth_service - INFO - 统一认证服务初始化完成
2025-07-22 21:49:01,652 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-22 21:49:01,654 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-22 21:49:02,388 - BackendMockDataManager - INFO - 后端模拟数据模式已启用
2025-07-22 21:49:03,744 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\psutil\__init__.py
2025-07-22 21:49:03,746 - fallback_manager - DEBUG - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-07-22 21:49:03,749 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-07-22 21:49:03,879 - health_monitor - INFO - 健康监控器初始化完成
2025-07-22 21:49:03,895 - app.core.system_monitor - INFO - 已加载 288 个历史数据点
2025-07-22 21:49:03,902 - app.core.alert_detector - INFO - 已加载 6 个告警规则
2025-07-22 21:49:03,904 - app.core.alert_detector - INFO - 已加载 0 个当前告警
2025-07-22 21:49:03,914 - app.core.alert_detector - INFO - 已加载 370 个历史告警
2025-07-22 21:49:03,916 - app.core.alert_detector - INFO - 已加载 1 个通知渠道
2025-07-22 21:49:03,921 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-22 21:49:03,935 - alert_manager - INFO - 已初始化默认告警规则
2025-07-22 21:49:03,936 - alert_manager - INFO - 已初始化默认通知渠道
2025-07-22 21:49:03,940 - alert_manager - INFO - 告警管理器初始化完成
2025-07-22 21:49:05,098 - db_service - INFO - 数据库引擎和会话工厂创建成功
2025-07-22 21:49:05,101 - db_service - INFO - 数据库服务初始化完成
2025-07-22 21:49:05,117 - notification_service - INFO - 通知服务初始化完成
2025-07-22 21:49:05,118 - main - INFO - 错误处理模块导入成功
2025-07-22 21:49:05,188 - main - INFO - 监控模块导入成功
2025-07-22 21:49:05,190 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-07-22 21:49:10,352 - app.services.ocr_service - WARNING - OpenCV未安装，图像预处理功能将不可用
2025-07-22 21:49:11,013 - main - INFO - 应用启动中...
2025-07-22 21:49:11,031 - error_handling - INFO - 错误处理已设置
2025-07-22 21:49:11,032 - main - INFO - 错误处理系统初始化完成
2025-07-22 21:49:11,036 - monitoring - INFO - 添加指标端点成功: /metrics
2025-07-22 21:49:11,039 - monitoring - INFO - 添加健康检查端点成功: /health
2025-07-22 21:49:11,043 - monitoring - INFO - 添加详细健康检查端点成功: /api/health/detailed
2025-07-22 21:49:11,045 - monitoring - INFO - 系统信息初始化完成: Markey, Windows-10-10.0.19045-SP0, Python 3.13.2, CPU核心数: 4
2025-07-22 21:49:11,211 - monitoring - INFO - 启动资源监控线程成功
2025-07-22 21:49:11,224 - monitoring - INFO - 监控系统初始化成功（不使用中间件）
2025-07-22 21:49:11,227 - monitoring - INFO - 监控系统初始化完成
2025-07-22 21:49:11,228 - main - INFO - 监控系统初始化完成
2025-07-22 21:49:11,330 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.5%, CPU使用率 100.0%
2025-07-22 21:49:11,860 - app.db.init_db - INFO - 所有模型导入成功
2025-07-22 21:49:11,862 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-07-22 21:49:12,236 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-22 21:49:12,238 - app.db.init_db - INFO - 所有模型导入成功
2025-07-22 21:49:12,241 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-07-22 21:49:12,243 - app.db.init_db - INFO - 正在运行数据库迁移...
2025-07-22 21:49:12,244 - app.db.init_db - INFO - 正在检查并更新数据库表结构...
2025-07-22 21:49:12,350 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-22 21:49:12,465 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alerts")
2025-07-22 21:49:12,515 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:12,545 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_rules")
2025-07-22 21:49:12,547 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:12,550 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_channels")
2025-07-22 21:49:12,555 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:12,558 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-22 21:49:12,560 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:12,563 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_records")
2025-07-22 21:49:12,565 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:12,571 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_overviews")
2025-07-22 21:49:12,574 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:12,595 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medical_records")
2025-07-22 21:49:12,622 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:12,707 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("inpatient_records")
2025-07-22 21:49:12,818 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:12,943 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("surgery_records")
2025-07-22 21:49:13,040 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:13,065 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_reports")
2025-07-22 21:49:13,071 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:13,160 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_report_items")
2025-07-22 21:49:13,170 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:13,280 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_reports")
2025-07-22 21:49:13,286 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:13,368 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("examination_reports")
2025-07-22 21:49:13,374 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:13,396 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("documents")
2025-07-22 21:49:13,464 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:13,507 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("other_records")
2025-07-22 21:49:13,565 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:13,596 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("operation_logs")
2025-07-22 21:49:13,617 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:13,685 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("follow_up_records")
2025-07-22 21:49:13,756 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:13,835 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_diaries")
2025-07-22 21:49:13,880 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:13,913 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("registration_records")
2025-07-22 21:49:13,920 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:13,937 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("prescription_records")
2025-07-22 21:49:13,943 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:14,159 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("laboratory_records")
2025-07-22 21:49:14,165 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:14,382 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_records")
2025-07-22 21:49:14,398 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:14,665 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medications")
2025-07-22 21:49:14,679 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:14,842 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medication_usages")
2025-07-22 21:49:14,861 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:14,886 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("role_applications")
2025-07-22 21:49:14,895 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:14,928 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessments")
2025-07-22 21:49:14,946 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:14,978 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_items")
2025-07-22 21:49:15,086 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:15,196 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_responses")
2025-07-22 21:49:15,268 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:15,312 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_templates")
2025-07-22 21:49:15,327 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:15,370 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_template_questions")
2025-07-22 21:49:15,405 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:15,469 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaires")
2025-07-22 21:49:15,505 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:15,615 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_items")
2025-07-22 21:49:15,679 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:15,728 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_responses")
2025-07-22 21:49:15,752 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:15,759 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_answers")
2025-07-22 21:49:15,762 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:15,766 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_templates")
2025-07-22 21:49:15,785 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:15,830 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_template_questions")
2025-07-22 21:49:15,910 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:15,985 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_results")
2025-07-22 21:49:16,076 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:16,150 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_results")
2025-07-22 21:49:16,241 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:16,335 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("report_templates")
2025-07-22 21:49:16,413 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:16,526 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_distributions")
2025-07-22 21:49:16,584 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:16,613 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_distributions")
2025-07-22 21:49:16,666 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:16,714 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("service_stats")
2025-07-22 21:49:16,764 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 21:49:16,864 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-22 21:49:16,919 - app.db.init_db - INFO - 数据库表结构检查和更新完成
2025-07-22 21:49:17,074 - app.db.init_db - INFO - 模型关系初始化完成
2025-07-22 21:49:17,097 - app.db.init_db - INFO - 模型关系设置完成
2025-07-22 21:49:17,119 - main - INFO - 数据库初始化完成（强制重建）
2025-07-22 21:49:17,141 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-22 21:49:17,170 - main - INFO - 数据库连接正常
2025-07-22 21:49:17,182 - main - INFO - 开始初始化模板数据
2025-07-22 21:49:17,220 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-22 21:49:18,096 - app.db.init_templates - INFO - 开始初始化评估量表模板...
2025-07-22 21:49:18,202 - app.db.init_templates - INFO - 成功初始化 5 个评估量表模板
2025-07-22 21:49:18,307 - app.db.init_templates - INFO - 开始初始化调查问卷模板...
2025-07-22 21:49:18,405 - app.db.init_templates - INFO - 成功初始化 5 个调查问卷模板
2025-07-22 21:49:18,409 - main - INFO - 模板数据初始化完成
2025-07-22 21:49:18,410 - main - INFO - 数据库表结构已经标准化，不需要迁移
2025-07-22 21:49:18,412 - main - INFO - 应用启动完成
2025-07-22 21:49:26,754 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.8%, CPU使用率 100.0%
2025-07-22 21:49:41,950 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.4%, CPU使用率 100.0%
2025-07-22 21:49:57,066 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.5%, CPU使用率 4.2%
2025-07-22 21:49:58,360 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-22 21:49:58,361 - main - INFO - 请求没有认证头部
2025-07-22 21:49:58,362 - main - INFO - 没有认证头部，设置用户为None
2025-07-22 21:49:58,363 - main - INFO - --- 请求结束: 200 ---

2025-07-22 21:50:00,392 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-22 21:50:00,393 - main - INFO - 请求没有认证头部
2025-07-22 21:50:00,393 - main - INFO - 没有认证头部，设置用户为None
2025-07-22 21:50:00,395 - app.core.db_connection - DEBUG - 当前线程ID: 16928
2025-07-22 21:50:00,396 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-22 21:50:00,399 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-22 21:50:00,400 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-22 21:50:00,401 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-22 21:50:00,402 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-22 21:50:01,345 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-22 21:50:01,351 - main - INFO - --- 请求结束: 200 ---

2025-07-22 21:50:04,777 - health_monitor - DEBUG - 系统指标 - CPU: 30.9%, 内存: 63.1%, 磁盘: 91.4%
2025-07-22 21:50:12,171 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.1%, CPU使用率 36.0%
2025-07-22 22:33:02,184 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-22 22:33:02,192 - auth_service - INFO - 统一认证服务初始化完成
2025-07-22 22:33:02,429 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-22 22:33:02,433 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-22 22:33:03,345 - BackendMockDataManager - INFO - 后端模拟数据模式已启用
2025-07-22 22:33:05,224 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\psutil\__init__.py
2025-07-22 22:33:05,233 - fallback_manager - DEBUG - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-07-22 22:33:05,237 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-07-22 22:33:05,323 - health_monitor - INFO - 健康监控器初始化完成
2025-07-22 22:33:05,341 - app.core.system_monitor - INFO - 已加载 288 个历史数据点
2025-07-22 22:33:05,354 - app.core.alert_detector - INFO - 已加载 6 个告警规则
2025-07-22 22:33:05,358 - app.core.alert_detector - INFO - 已加载 0 个当前告警
2025-07-22 22:33:05,371 - app.core.alert_detector - INFO - 已加载 370 个历史告警
2025-07-22 22:33:05,374 - app.core.alert_detector - INFO - 已加载 1 个通知渠道
2025-07-22 22:33:05,383 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-22 22:33:05,398 - alert_manager - INFO - 已初始化默认告警规则
2025-07-22 22:33:05,400 - alert_manager - INFO - 已初始化默认通知渠道
2025-07-22 22:33:05,405 - alert_manager - INFO - 告警管理器初始化完成
2025-07-22 22:33:07,918 - db_service - INFO - 数据库引擎和会话工厂创建成功
2025-07-22 22:33:07,922 - db_service - INFO - 数据库服务初始化完成
2025-07-22 22:33:07,971 - notification_service - INFO - 通知服务初始化完成
2025-07-22 22:33:08,006 - main - INFO - 错误处理模块导入成功
2025-07-22 22:33:08,127 - main - INFO - 监控模块导入成功
2025-07-22 22:33:08,137 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-07-22 22:33:16,386 - app.services.ocr_service - WARNING - OpenCV未安装，图像预处理功能将不可用
2025-07-22 22:33:16,596 - main - INFO - 应用启动中...
2025-07-22 22:33:16,599 - error_handling - INFO - 错误处理已设置
2025-07-22 22:33:16,602 - main - INFO - 错误处理系统初始化完成
2025-07-22 22:33:16,604 - monitoring - INFO - 添加指标端点成功: /metrics
2025-07-22 22:33:16,612 - monitoring - INFO - 添加健康检查端点成功: /health
2025-07-22 22:33:16,614 - monitoring - INFO - 添加详细健康检查端点成功: /api/health/detailed
2025-07-22 22:33:16,617 - monitoring - INFO - 系统信息初始化完成: Markey, Windows-10-10.0.19045-SP0, Python 3.13.2, CPU核心数: 4
2025-07-22 22:33:16,647 - monitoring - INFO - 启动资源监控线程成功
2025-07-22 22:33:16,649 - monitoring - INFO - 监控系统初始化成功（不使用中间件）
2025-07-22 22:33:16,650 - monitoring - INFO - 监控系统初始化完成
2025-07-22 22:33:16,660 - main - INFO - 监控系统初始化完成
2025-07-22 22:33:16,705 - app.db.init_db - INFO - 所有模型导入成功
2025-07-22 22:33:16,717 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-07-22 22:33:16,750 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-22 22:33:16,753 - app.db.init_db - INFO - 所有模型导入成功
2025-07-22 22:33:16,754 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.6%, CPU使用率 100.0%
2025-07-22 22:33:16,757 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-07-22 22:33:16,768 - app.db.init_db - INFO - 正在运行数据库迁移...
2025-07-22 22:33:16,769 - app.db.init_db - INFO - 正在检查并更新数据库表结构...
2025-07-22 22:33:16,785 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-22 22:33:16,793 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alerts")
2025-07-22 22:33:16,797 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:16,888 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_rules")
2025-07-22 22:33:16,987 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:17,176 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_channels")
2025-07-22 22:33:17,183 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:17,412 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-22 22:33:17,565 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:17,605 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_records")
2025-07-22 22:33:17,650 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:17,745 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_overviews")
2025-07-22 22:33:17,850 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:17,965 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medical_records")
2025-07-22 22:33:18,022 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:18,069 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("inpatient_records")
2025-07-22 22:33:18,079 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:18,115 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("surgery_records")
2025-07-22 22:33:18,121 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:18,182 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_reports")
2025-07-22 22:33:18,194 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:18,205 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_report_items")
2025-07-22 22:33:18,212 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:18,234 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_reports")
2025-07-22 22:33:18,280 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:18,318 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("examination_reports")
2025-07-22 22:33:18,323 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:18,356 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("documents")
2025-07-22 22:33:18,368 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:18,404 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("other_records")
2025-07-22 22:33:18,480 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:18,632 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("operation_logs")
2025-07-22 22:33:18,671 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:18,687 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("follow_up_records")
2025-07-22 22:33:18,700 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:18,719 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_diaries")
2025-07-22 22:33:18,731 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:18,751 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("registration_records")
2025-07-22 22:33:18,852 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:18,945 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("prescription_records")
2025-07-22 22:33:18,965 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:19,017 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("laboratory_records")
2025-07-22 22:33:19,052 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:19,070 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_records")
2025-07-22 22:33:19,118 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:19,138 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medications")
2025-07-22 22:33:19,167 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:19,185 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medication_usages")
2025-07-22 22:33:19,200 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:19,238 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("role_applications")
2025-07-22 22:33:19,301 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:19,405 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessments")
2025-07-22 22:33:19,470 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:19,502 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_items")
2025-07-22 22:33:19,523 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:19,584 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_responses")
2025-07-22 22:33:19,678 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:19,705 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_templates")
2025-07-22 22:33:19,721 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:19,781 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_template_questions")
2025-07-22 22:33:19,801 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:19,979 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaires")
2025-07-22 22:33:20,079 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:20,147 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_items")
2025-07-22 22:33:20,170 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:20,198 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_responses")
2025-07-22 22:33:20,240 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:20,271 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_answers")
2025-07-22 22:33:20,299 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:20,304 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_templates")
2025-07-22 22:33:20,318 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:20,350 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_template_questions")
2025-07-22 22:33:20,368 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:20,382 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_results")
2025-07-22 22:33:20,493 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:20,696 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_results")
2025-07-22 22:33:20,727 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:20,747 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("report_templates")
2025-07-22 22:33:20,752 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:20,763 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_distributions")
2025-07-22 22:33:20,766 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:20,770 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_distributions")
2025-07-22 22:33:20,783 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:20,798 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("service_stats")
2025-07-22 22:33:20,843 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-22 22:33:20,895 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-22 22:33:20,915 - app.db.init_db - INFO - 数据库表结构检查和更新完成
2025-07-22 22:33:20,932 - app.db.init_db - INFO - 模型关系初始化完成
2025-07-22 22:33:20,934 - app.db.init_db - INFO - 模型关系设置完成
2025-07-22 22:33:20,937 - main - INFO - 数据库初始化完成（强制重建）
2025-07-22 22:33:20,945 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-22 22:33:20,950 - main - INFO - 数据库连接正常
2025-07-22 22:33:20,952 - main - INFO - 开始初始化模板数据
2025-07-22 22:33:20,961 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-22 22:33:21,693 - app.db.init_templates - INFO - 开始初始化评估量表模板...
2025-07-22 22:33:21,811 - app.db.init_templates - INFO - 成功初始化 5 个评估量表模板
2025-07-22 22:33:21,934 - app.db.init_templates - INFO - 开始初始化调查问卷模板...
2025-07-22 22:33:22,100 - app.db.init_templates - INFO - 成功初始化 5 个调查问卷模板
2025-07-22 22:33:22,103 - main - INFO - 模板数据初始化完成
2025-07-22 22:33:22,108 - main - INFO - 数据库表结构已经标准化，不需要迁移
2025-07-22 22:33:22,153 - main - INFO - 应用启动完成
2025-07-22 22:33:31,509 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-22 22:33:31,545 - main - INFO - 请求没有认证头部
2025-07-22 22:33:31,546 - main - INFO - 没有认证头部，设置用户为None
2025-07-22 22:33:31,549 - main - INFO - --- 请求结束: 200 ---

2025-07-22 22:33:32,012 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.4%, CPU使用率 100.0%
2025-07-22 22:33:33,893 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-22 22:33:33,895 - main - INFO - 请求没有认证头部
2025-07-22 22:33:33,896 - main - INFO - 没有认证头部，设置用户为None
2025-07-22 22:33:33,899 - app.core.db_connection - DEBUG - 当前线程ID: 16064
2025-07-22 22:33:33,900 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-22 22:33:33,903 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-22 22:33:33,904 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-22 22:33:33,906 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-22 22:33:33,907 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-22 22:33:36,116 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-22 22:33:36,120 - main - INFO - --- 请求结束: 200 ---

2025-07-22 22:33:47,119 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.0%, CPU使用率 71.4%
2025-07-22 22:33:50,037 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-22 22:33:50,046 - main - INFO - 请求没有认证头部
2025-07-22 22:33:50,047 - main - INFO - 没有认证头部，设置用户为None
2025-07-22 22:33:50,052 - main - INFO - --- 请求结束: 200 ---

2025-07-22 22:33:52,409 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-22 22:33:52,411 - main - INFO - 请求没有认证头部
2025-07-22 22:33:52,411 - main - INFO - 没有认证头部，设置用户为None
2025-07-22 22:33:52,414 - app.core.db_connection - DEBUG - 当前线程ID: 16064
2025-07-22 22:33:52,414 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-22 22:33:52,416 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-22 22:33:52,417 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-22 22:33:52,418 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-22 22:33:52,421 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-22 22:33:54,350 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-22 22:33:54,356 - main - INFO - --- 请求结束: 200 ---

2025-07-22 22:34:02,228 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.6%, CPU使用率 96.6%
2025-07-22 22:34:06,363 - health_monitor - DEBUG - 系统指标 - CPU: 90.9%, 内存: 73.0%, 磁盘: 91.1%
2025-07-22 22:34:12,433 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-22 22:34:12,456 - main - INFO - 请求没有认证头部
2025-07-22 22:34:12,457 - main - INFO - 没有认证头部，设置用户为None
2025-07-22 22:34:12,460 - main - INFO - --- 请求结束: 200 ---

2025-07-22 22:34:14,925 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-22 22:34:14,930 - main - INFO - 请求没有认证头部
2025-07-22 22:34:14,933 - main - INFO - 没有认证头部，设置用户为None
2025-07-22 22:34:14,944 - app.core.db_connection - DEBUG - 当前线程ID: 16064
2025-07-22 22:34:15,095 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-22 22:34:15,158 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-22 22:34:15,160 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-22 22:34:15,164 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-22 22:34:15,168 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-22 22:34:17,627 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.6%, CPU使用率 100.0%
2025-07-22 22:34:18,265 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-22 22:34:18,280 - main - INFO - --- 请求结束: 200 ---

2025-07-22 22:34:33,167 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.1%, CPU使用率 100.0%
2025-07-22 22:34:49,020 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.5%, CPU使用率 100.0%
2025-07-22 22:35:04,634 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.0%, CPU使用率 100.0%
2025-07-22 22:35:07,423 - health_monitor - DEBUG - 系统指标 - CPU: 81.2%, 内存: 74.6%, 磁盘: 91.1%
2025-07-22 22:35:19,795 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.2%, CPU使用率 66.7%
2025-07-22 22:35:34,901 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.0%, CPU使用率 42.9%
2025-07-22 22:35:50,042 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.1%, CPU使用率 100.0%
2025-07-22 22:36:05,148 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.5%, CPU使用率 29.6%
2025-07-22 22:36:08,493 - health_monitor - DEBUG - 系统指标 - CPU: 83.0%, 内存: 71.1%, 磁盘: 91.1%
2025-07-22 22:36:20,646 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.7%, CPU使用率 100.0%
2025-07-22 22:36:35,797 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.7%, CPU使用率 78.6%
2025-07-22 22:36:50,922 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.7%, CPU使用率 100.0%
2025-07-22 22:37:06,031 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.9%, CPU使用率 56.0%
2025-07-22 22:37:09,939 - health_monitor - DEBUG - 系统指标 - CPU: 97.8%, 内存: 71.3%, 磁盘: 91.1%
2025-07-22 22:37:21,167 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.5%, CPU使用率 86.7%
2025-07-22 22:37:36,885 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.1%, CPU使用率 100.0%
2025-07-22 22:37:52,244 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.4%, CPU使用率 72.4%
2025-07-22 22:38:08,544 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 100.0%
2025-07-22 22:38:12,156 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 67.1%, 磁盘: 91.1%
2025-07-22 22:38:25,396 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.9%, CPU使用率 100.0%
2025-07-22 22:38:41,073 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 100.0%
2025-07-22 22:38:56,211 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.5%, CPU使用率 79.3%
2025-07-22 22:39:05,658 - alert_manager - WARNING - 触发告警: disk_free_space, 当前值: 0.0, 阈值: 1024
2025-07-22 22:39:11,332 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.5%, CPU使用率 37.5%
2025-07-22 22:39:13,421 - health_monitor - DEBUG - 系统指标 - CPU: 67.4%, 内存: 67.1%, 磁盘: 91.1%
2025-07-22 22:39:26,440 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 100.0%
2025-07-22 22:39:42,711 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 100.0%
2025-07-22 22:39:57,949 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 57.1%
2025-07-22 22:40:05,715 - alert_manager - WARNING - 触发告警: disk_usage, 当前值: 91.1, 阈值: 90
2025-07-22 22:40:13,055 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.1%, CPU使用率 86.2%
2025-07-22 22:40:14,510 - health_monitor - DEBUG - 系统指标 - CPU: 89.8%, 内存: 67.5%, 磁盘: 91.1%
2025-07-22 22:40:28,160 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.7%, CPU使用率 80.0%
2025-07-22 22:40:43,266 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.7%, CPU使用率 58.3%
2025-07-22 22:40:58,371 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.1%, CPU使用率 35.7%
2025-07-22 22:41:13,477 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 57.1%
2025-07-22 22:41:15,536 - health_monitor - DEBUG - 系统指标 - CPU: 63.3%, 内存: 67.6%, 磁盘: 91.1%
2025-07-22 22:41:29,015 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.7%, CPU使用率 100.0%
2025-07-22 22:41:44,381 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.7%, CPU使用率 100.0%
2025-07-22 22:41:59,965 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.7%, CPU使用率 100.0%
2025-07-22 22:42:15,217 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 69.0%
2025-07-22 22:42:16,640 - health_monitor - DEBUG - 系统指标 - CPU: 76.7%, 内存: 67.3%, 磁盘: 91.1%
2025-07-22 22:42:30,776 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 100.0%
2025-07-22 22:42:46,363 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.6%, CPU使用率 100.0%
2025-07-22 22:43:01,607 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.3%, CPU使用率 73.3%
2025-07-22 22:43:16,713 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 24.1%
2025-07-22 22:43:17,669 - health_monitor - DEBUG - 系统指标 - CPU: 31.3%, 内存: 67.4%, 磁盘: 91.1%
2025-07-22 22:43:32,173 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.6%, CPU使用率 100.0%
2025-07-22 22:43:47,845 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.4%, CPU使用率 100.0%
2025-07-22 22:44:03,347 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.1%, CPU使用率 100.0%
2025-07-22 22:44:18,606 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.0%, CPU使用率 92.9%
2025-07-22 22:44:18,699 - health_monitor - DEBUG - 系统指标 - CPU: 78.1%, 内存: 68.0%, 磁盘: 91.1%
2025-07-22 22:44:34,122 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.1%, CPU使用率 100.0%
2025-07-22 22:44:49,340 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.9%, CPU使用率 100.0%
2025-07-22 22:45:01,152 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-22 22:45:01,153 - main - INFO - 请求没有认证头部
2025-07-22 22:45:01,154 - main - INFO - 没有认证头部，设置用户为None
2025-07-22 22:45:01,156 - main - INFO - --- 请求结束: 200 ---

2025-07-22 22:45:03,199 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-22 22:45:03,238 - main - INFO - 请求没有认证头部
2025-07-22 22:45:03,242 - main - INFO - 没有认证头部，设置用户为None
2025-07-22 22:45:03,244 - app.core.db_connection - DEBUG - 当前线程ID: 16064
2025-07-22 22:45:03,246 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-22 22:45:03,491 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-22 22:45:03,543 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-22 22:45:03,595 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-22 22:45:03,629 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-22 22:45:04,546 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.3%, CPU使用率 100.0%
2025-07-22 22:45:06,390 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-22 22:45:06,394 - main - INFO - --- 请求结束: 200 ---

2025-07-22 22:45:19,792 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.6%, CPU使用率 100.0%
2025-07-22 22:45:19,884 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 70.6%, 磁盘: 91.1%
2025-07-22 22:45:35,239 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.0%, CPU使用率 100.0%
2025-07-22 22:45:50,411 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.4%, CPU使用率 100.0%
2025-07-22 22:46:05,517 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.2%, CPU使用率 8.3%
2025-07-22 22:46:20,622 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.2%, CPU使用率 33.3%
2025-07-22 22:46:21,276 - health_monitor - DEBUG - 系统指标 - CPU: 22.4%, 内存: 66.2%, 磁盘: 91.1%
2025-07-22 22:46:35,727 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.2%, CPU使用率 3.7%
2025-07-22 22:46:50,832 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.2%, CPU使用率 50.0%
2025-07-22 22:47:05,938 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.2%, CPU使用率 58.3%
2025-07-22 22:47:21,044 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.7%, CPU使用率 22.2%
2025-07-22 22:47:22,304 - health_monitor - DEBUG - 系统指标 - CPU: 45.5%, 内存: 66.7%, 磁盘: 91.1%
2025-07-22 22:47:36,400 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.1%, CPU使用率 100.0%
2025-07-22 22:47:51,510 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.0%, CPU使用率 41.4%
2025-07-22 22:48:06,615 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 62.5%
2025-07-22 22:48:21,724 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 58.3%
2025-07-22 22:48:23,335 - health_monitor - DEBUG - 系统指标 - CPU: 39.5%, 内存: 67.3%, 磁盘: 91.1%
2025-07-22 22:48:36,946 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.6%, CPU使用率 100.0%
2025-07-22 22:48:52,114 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 100.0%
2025-07-22 22:49:07,253 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.6%, CPU使用率 96.4%
2025-07-22 22:49:22,404 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.3%, CPU使用率 90.9%
2025-07-22 22:49:24,438 - health_monitor - DEBUG - 系统指标 - CPU: 78.3%, 内存: 68.3%, 磁盘: 91.1%
2025-07-22 22:49:31,790 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-22 22:49:31,800 - main - INFO - 请求没有认证头部
2025-07-22 22:49:31,801 - main - INFO - 没有认证头部，设置用户为None
2025-07-22 22:49:31,803 - main - INFO - --- 请求结束: 200 ---

2025-07-22 22:49:33,829 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-22 22:49:33,829 - main - INFO - 请求没有认证头部
2025-07-22 22:49:33,830 - main - INFO - 没有认证头部，设置用户为None
2025-07-22 22:49:33,832 - app.core.db_connection - DEBUG - 当前线程ID: 16064
2025-07-22 22:49:33,832 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-22 22:49:33,834 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-22 22:49:33,834 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-22 22:49:33,835 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-22 22:49:33,836 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-22 22:49:35,836 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-22 22:49:35,862 - main - INFO - --- 请求结束: 200 ---

2025-07-22 22:49:37,806 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.1%, CPU使用率 100.0%
2025-07-22 22:49:52,920 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.8%, CPU使用率 39.1%
2025-07-22 22:50:08,026 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.7%, CPU使用率 55.2%
2025-07-22 22:50:23,425 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 100.0%
2025-07-22 22:50:25,634 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 67.7%, 磁盘: 91.1%
2025-07-22 22:50:38,777 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.9%, CPU使用率 100.0%
2025-07-22 22:50:53,895 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.6%, CPU使用率 12.5%
2025-07-22 22:51:09,004 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 58.3%
2025-07-22 22:51:24,195 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.5%, CPU使用率 63.4%
2025-07-22 22:51:26,678 - health_monitor - DEBUG - 系统指标 - CPU: 38.5%, 内存: 67.5%, 磁盘: 91.1%
2025-07-22 22:51:39,300 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.0%, CPU使用率 25.0%
2025-07-22 22:51:54,405 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 79.2%
2025-07-22 22:52:09,618 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.8%, CPU使用率 100.0%
2025-07-22 22:52:24,732 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.0%, CPU使用率 93.1%
2025-07-22 22:52:25,494 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-22 22:52:25,496 - main - INFO - 请求没有认证头部
2025-07-22 22:52:25,498 - main - INFO - 没有认证头部，设置用户为None
2025-07-22 22:52:25,500 - main - INFO - --- 请求结束: 200 ---

2025-07-22 22:52:27,535 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-22 22:52:27,536 - main - INFO - 请求没有认证头部
2025-07-22 22:52:27,537 - main - INFO - 没有认证头部，设置用户为None
2025-07-22 22:52:27,538 - app.core.db_connection - DEBUG - 当前线程ID: 16064
2025-07-22 22:52:27,541 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-22 22:52:27,543 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-22 22:52:27,544 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-22 22:52:27,545 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-22 22:52:27,546 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-22 22:52:27,794 - health_monitor - DEBUG - 系统指标 - CPU: 83.3%, 内存: 69.2%, 磁盘: 91.1%
2025-07-22 22:52:30,060 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-22 22:52:30,063 - main - INFO - --- 请求结束: 200 ---

2025-07-22 22:52:39,853 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.1%, CPU使用率 96.4%
2025-07-22 22:52:54,963 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.1%, CPU使用率 10.7%
2025-07-22 22:53:10,071 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.9%, CPU使用率 21.4%
2025-07-22 22:53:25,176 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.9%, CPU使用率 37.5%
2025-07-22 22:53:29,056 - health_monitor - DEBUG - 系统指标 - CPU: 99.3%, 内存: 70.6%, 磁盘: 91.1%
2025-07-22 22:53:40,282 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.5%, CPU使用率 20.8%
2025-07-22 22:53:55,390 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.7%, CPU使用率 50.0%
2025-07-22 22:54:10,496 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.8%, CPU使用率 33.3%
2025-07-22 22:54:25,600 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 41.4%
2025-07-22 22:54:30,134 - health_monitor - DEBUG - 系统指标 - CPU: 22.3%, 内存: 67.5%, 磁盘: 91.1%
2025-07-22 22:54:40,706 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 45.8%
2025-07-22 22:54:55,812 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 58.3%
2025-07-22 22:55:10,919 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 39.3%
2025-07-22 22:55:26,025 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.9%, CPU使用率 64.3%
2025-07-22 22:55:31,167 - health_monitor - DEBUG - 系统指标 - CPU: 70.8%, 内存: 68.1%, 磁盘: 91.1%
2025-07-22 22:55:41,131 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.7%, CPU使用率 8.0%
2025-07-22 22:55:56,237 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.1%, CPU使用率 41.7%
2025-07-22 22:56:11,342 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.9%, CPU使用率 58.3%
2025-07-22 22:56:26,448 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.4%, CPU使用率 53.6%
2025-07-22 22:56:32,247 - health_monitor - DEBUG - 系统指标 - CPU: 68.1%, 内存: 68.4%, 磁盘: 91.1%
2025-07-22 22:56:41,554 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.0%, CPU使用率 79.3%
2025-07-22 22:56:56,660 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.9%, CPU使用率 95.8%
2025-07-22 22:57:11,767 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.5%, CPU使用率 45.8%
2025-07-22 22:57:26,874 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.5%, CPU使用率 87.5%
2025-07-22 22:57:33,892 - health_monitor - DEBUG - 系统指标 - CPU: 95.5%, 内存: 68.3%, 磁盘: 91.1%
2025-07-22 22:57:42,085 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.4%, CPU使用率 100.0%
2025-07-22 22:57:57,458 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.2%, CPU使用率 100.0%
2025-07-22 22:58:12,634 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.5%, CPU使用率 57.1%
2025-07-22 22:58:27,752 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.7%, CPU使用率 89.3%
2025-07-22 22:58:34,932 - health_monitor - DEBUG - 系统指标 - CPU: 73.9%, 内存: 68.3%, 磁盘: 91.1%
2025-07-22 22:58:42,864 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.9%, CPU使用率 91.7%
2025-07-22 22:58:51,440 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-22 22:58:51,442 - main - INFO - 请求没有认证头部
2025-07-22 22:58:51,443 - main - INFO - 没有认证头部，设置用户为None
2025-07-22 22:58:51,444 - main - INFO - --- 请求结束: 200 ---

2025-07-22 22:58:53,483 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-22 22:58:53,484 - main - INFO - 请求没有认证头部
2025-07-22 22:58:53,484 - main - INFO - 没有认证头部，设置用户为None
2025-07-22 22:58:53,486 - app.core.db_connection - DEBUG - 当前线程ID: 16064
2025-07-22 22:58:53,487 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-22 22:58:53,491 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-22 22:58:53,492 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-22 22:58:53,493 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-22 22:58:53,494 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-22 22:58:57,445 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-22 22:58:57,447 - main - INFO - --- 请求结束: 200 ---

2025-07-22 22:58:58,314 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.2%, CPU使用率 100.0%
2025-07-22 22:59:13,470 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.8%, CPU使用率 100.0%
2025-07-22 22:59:28,669 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.4%, CPU使用率 86.7%
2025-07-22 22:59:35,968 - health_monitor - DEBUG - 系统指标 - CPU: 67.3%, 内存: 69.0%, 磁盘: 91.1%
2025-07-22 22:59:43,778 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.5%, CPU使用率 71.4%
2025-07-22 22:59:58,884 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.7%, CPU使用率 21.4%
2025-07-22 23:00:13,988 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.2%, CPU使用率 56.0%
2025-07-22 23:00:29,093 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.6%, CPU使用率 50.0%
2025-07-22 23:00:37,000 - health_monitor - DEBUG - 系统指标 - CPU: 37.6%, 内存: 65.6%, 磁盘: 91.1%
2025-07-22 23:00:44,198 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.6%, CPU使用率 17.9%
2025-07-22 23:00:59,304 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.9%, CPU使用率 35.7%
2025-07-22 23:01:14,417 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 71.4%
2025-07-22 23:01:29,528 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.7%, CPU使用率 67.9%
2025-07-22 23:01:38,026 - health_monitor - DEBUG - 系统指标 - CPU: 34.2%, 内存: 68.3%, 磁盘: 91.1%
2025-07-22 23:01:44,634 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.3%, CPU使用率 30.0%
2025-07-22 23:01:59,739 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.4%, CPU使用率 16.7%
2025-07-22 23:02:14,851 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.4%, CPU使用率 55.2%
2025-07-22 23:02:30,040 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.4%, CPU使用率 100.0%
2025-07-22 23:02:39,053 - health_monitor - DEBUG - 系统指标 - CPU: 24.4%, 内存: 68.5%, 磁盘: 91.1%
2025-07-22 23:02:45,170 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.5%, CPU使用率 76.7%
2025-07-22 23:03:00,278 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.5%, CPU使用率 69.0%
2025-07-22 23:03:15,384 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.6%, CPU使用率 82.1%
2025-07-22 23:03:30,491 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.2%, CPU使用率 60.7%
2025-07-22 23:03:40,579 - health_monitor - DEBUG - 系统指标 - CPU: 99.7%, 内存: 71.6%, 磁盘: 91.1%
2025-07-22 23:03:45,595 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.2%, CPU使用率 16.7%
2025-07-22 23:04:00,700 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.8%, CPU使用率 10.7%
2025-07-22 23:04:15,805 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.7%, CPU使用率 20.7%
2025-07-22 23:04:30,915 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.1%, CPU使用率 64.3%
2025-07-22 23:04:41,953 - health_monitor - DEBUG - 系统指标 - CPU: 83.9%, 内存: 68.3%, 磁盘: 91.1%
2025-07-22 23:04:46,022 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.0%, CPU使用率 46.4%
2025-07-22 23:04:48,705 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-22 23:04:48,706 - main - INFO - 请求没有认证头部
2025-07-22 23:04:48,707 - main - INFO - 没有认证头部，设置用户为None
2025-07-22 23:04:48,710 - main - INFO - --- 请求结束: 200 ---

2025-07-22 23:04:50,743 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-22 23:04:50,748 - main - INFO - 请求没有认证头部
2025-07-22 23:04:50,749 - main - INFO - 没有认证头部，设置用户为None
2025-07-22 23:04:50,754 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-07-22 23:04:50,764 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-07-22 23:04:50,767 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-07-22 23:04:50,770 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-07-22 23:04:50,771 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-07-22 23:04:50,776 - app.core.db_connection - DEBUG - 当前线程ID: 16064
2025-07-22 23:04:50,781 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-22 23:04:50,783 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-22 23:04:50,784 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-22 23:04:50,785 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-22 23:04:54,839 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-22 23:04:54,843 - main - INFO - --- 请求结束: 200 ---

2025-07-22 23:05:01,134 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.3%, CPU使用率 89.3%
2025-07-22 23:05:16,246 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.3%, CPU使用率 17.9%
2025-07-22 23:05:31,351 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.2%, CPU使用率 82.8%
2025-07-22 23:05:42,994 - health_monitor - DEBUG - 系统指标 - CPU: 58.2%, 内存: 70.7%, 磁盘: 91.1%
2025-07-22 23:05:46,459 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.4%, CPU使用率 58.3%
2025-07-22 23:06:01,568 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.2%, CPU使用率 87.5%
2025-07-22 23:06:16,680 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.2%, CPU使用率 82.1%
2025-07-22 23:06:31,897 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 100.0%
2025-07-22 23:06:44,035 - health_monitor - DEBUG - 系统指标 - CPU: 78.6%, 内存: 68.3%, 磁盘: 91.1%
2025-07-22 23:06:47,067 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.8%, CPU使用率 91.2%
2025-07-22 23:07:02,174 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.2%, CPU使用率 87.5%
2025-07-22 23:07:17,342 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.6%, CPU使用率 100.0%
2025-07-22 23:07:32,907 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.8%, CPU使用率 100.0%
2025-07-22 23:07:45,071 - health_monitor - DEBUG - 系统指标 - CPU: 82.0%, 内存: 68.2%, 磁盘: 91.1%
2025-07-22 23:07:48,182 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.3%, CPU使用率 100.0%
2025-07-22 23:08:01,031 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-22 23:08:01,033 - main - INFO - 请求没有认证头部
2025-07-22 23:08:01,034 - main - INFO - 没有认证头部，设置用户为None
2025-07-22 23:08:01,036 - main - INFO - --- 请求结束: 200 ---

2025-07-22 23:08:03,069 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-22 23:08:03,070 - main - INFO - 请求没有认证头部
2025-07-22 23:08:03,071 - main - INFO - 没有认证头部，设置用户为None
2025-07-22 23:08:03,072 - app.core.db_connection - DEBUG - 当前线程ID: 16064
2025-07-22 23:08:03,073 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-22 23:08:03,074 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-22 23:08:03,075 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-22 23:08:03,076 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-22 23:08:03,370 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.6%, CPU使用率 65.6%
2025-07-22 23:08:06,321 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-22 23:08:06,323 - main - INFO - --- 请求结束: 200 ---

2025-07-22 23:08:18,554 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.2%, CPU使用率 77.4%
2025-07-22 23:08:34,285 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.9%, CPU使用率 100.0%
2025-07-22 23:08:46,182 - health_monitor - DEBUG - 系统指标 - CPU: 81.1%, 内存: 69.2%, 磁盘: 91.1%
2025-07-22 23:08:49,636 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.6%, CPU使用率 95.0%
2025-07-22 23:09:04,750 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.5%, CPU使用率 96.0%
2025-07-22 23:09:15,649 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-22 23:09:15,649 - main - INFO - 请求没有认证头部
2025-07-22 23:09:15,650 - main - INFO - 没有认证头部，设置用户为None
2025-07-22 23:09:15,651 - main - INFO - --- 请求结束: 200 ---

2025-07-22 23:09:17,687 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-22 23:09:17,689 - main - INFO - 请求没有认证头部
2025-07-22 23:09:17,691 - main - INFO - 没有认证头部，设置用户为None
2025-07-22 23:09:17,694 - app.core.db_connection - DEBUG - 当前线程ID: 16064
2025-07-22 23:09:17,694 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-22 23:09:17,695 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-22 23:09:17,696 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-22 23:09:17,697 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-22 23:09:19,894 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 78.6%
2025-07-22 23:09:21,431 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-22 23:09:21,433 - main - INFO - --- 请求结束: 200 ---

2025-07-22 23:09:35,060 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 67.9%
2025-07-22 23:09:45,859 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-22 23:09:45,860 - main - INFO - 请求没有认证头部
2025-07-22 23:09:45,861 - main - INFO - 没有认证头部，设置用户为None
2025-07-22 23:09:45,862 - main - INFO - --- 请求结束: 200 ---

2025-07-22 23:09:47,214 - health_monitor - DEBUG - 系统指标 - CPU: 44.0%, 内存: 69.6%, 磁盘: 91.1%
2025-07-22 23:09:47,895 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-22 23:09:47,896 - main - INFO - 请求没有认证头部
2025-07-22 23:09:47,897 - main - INFO - 没有认证头部，设置用户为None
2025-07-22 23:09:47,898 - app.core.db_connection - DEBUG - 当前线程ID: 16064
2025-07-22 23:09:47,899 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-22 23:09:47,900 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-22 23:09:47,901 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-22 23:09:47,904 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-22 23:09:50,192 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.5%, CPU使用率 35.7%
2025-07-22 23:09:51,630 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-22 23:09:51,633 - main - INFO - --- 请求结束: 200 ---

2025-07-22 23:10:05,357 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.9%, CPU使用率 33.3%
2025-07-22 23:10:20,756 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.3%, CPU使用率 100.0%
2025-07-22 23:10:36,050 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.7%, CPU使用率 45.8%
2025-07-22 23:10:48,636 - health_monitor - DEBUG - 系统指标 - CPU: 99.7%, 内存: 68.9%, 磁盘: 91.1%
2025-07-22 23:10:51,856 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.8%, CPU使用率 100.0%
2025-07-22 23:11:07,118 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.0%, CPU使用率 57.1%
2025-07-22 23:11:22,548 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.4%, CPU使用率 100.0%
2025-07-22 23:11:38,001 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.0%, CPU使用率 100.0%
2025-07-22 23:11:50,274 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 70.0%, 磁盘: 91.1%
2025-07-22 23:11:53,382 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 100.0%
2025-07-22 23:12:08,660 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.1%, CPU使用率 100.0%
2025-07-22 23:12:23,874 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.9%, CPU使用率 100.0%
2025-07-22 23:12:39,235 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.8%, CPU使用率 100.0%
2025-07-22 23:12:51,501 - health_monitor - DEBUG - 系统指标 - CPU: 99.6%, 内存: 69.8%, 磁盘: 91.1%
2025-07-22 23:12:54,428 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.1%, CPU使用率 100.0%
2025-07-22 23:13:09,611 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.7%, CPU使用率 97.4%
2025-07-22 23:13:24,919 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.0%, CPU使用率 100.0%
2025-07-22 23:13:40,207 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.6%, CPU使用率 98.3%
2025-07-22 23:13:52,832 - health_monitor - DEBUG - 系统指标 - CPU: 95.9%, 内存: 69.4%, 磁盘: 91.1%
2025-07-22 23:13:55,516 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 100.0%
2025-07-22 23:14:10,672 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 97.2%
2025-07-22 23:14:25,977 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.3%, CPU使用率 100.0%
2025-07-22 23:14:41,217 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.1%, CPU使用率 100.0%
2025-07-22 23:14:53,993 - health_monitor - DEBUG - 系统指标 - CPU: 85.7%, 内存: 69.0%, 磁盘: 91.1%
2025-07-22 23:14:56,330 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.2%, CPU使用率 41.7%
2025-07-22 23:15:11,452 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.0%, CPU使用率 100.0%
2025-07-22 23:15:26,599 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.7%, CPU使用率 97.3%
2025-07-22 23:15:41,710 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.6%, CPU使用率 39.3%
2025-07-22 23:15:55,241 - health_monitor - DEBUG - 系统指标 - CPU: 96.4%, 内存: 69.0%, 磁盘: 91.1%
2025-07-22 23:15:56,817 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.8%, CPU使用率 50.0%
2025-07-22 23:16:12,859 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 100.0%
2025-07-22 23:16:28,744 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.0%, CPU使用率 100.0%
2025-07-22 23:16:44,458 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.9%, CPU使用率 100.0%
2025-07-22 23:16:56,272 - health_monitor - DEBUG - 系统指标 - CPU: 47.3%, 内存: 68.3%, 磁盘: 91.1%
2025-07-22 23:16:59,624 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.2%, CPU使用率 100.0%
2025-07-22 23:17:14,787 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.3%, CPU使用率 40.0%
2025-07-22 23:17:29,897 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.3%, CPU使用率 75.0%
2025-07-22 23:17:45,002 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.2%, CPU使用率 33.3%
2025-07-22 23:17:57,297 - health_monitor - DEBUG - 系统指标 - CPU: 23.8%, 内存: 68.1%, 磁盘: 91.1%
2025-07-22 23:18:00,108 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.9%, CPU使用率 46.2%
2025-07-22 23:18:15,213 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.7%, CPU使用率 17.9%
2025-07-22 23:18:30,331 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.8%, CPU使用率 92.0%
2025-07-22 23:18:45,437 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 34.6%
2025-07-22 23:18:58,324 - health_monitor - DEBUG - 系统指标 - CPU: 42.4%, 内存: 67.4%, 磁盘: 91.1%
2025-07-22 23:19:00,542 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 75.0%
2025-07-22 23:19:15,648 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.0%, CPU使用率 33.3%
2025-07-22 23:19:30,761 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.0%, CPU使用率 86.2%
2025-07-22 23:19:45,866 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.0%, CPU使用率 28.6%
2025-07-22 23:19:59,349 - health_monitor - DEBUG - 系统指标 - CPU: 26.5%, 内存: 66.9%, 磁盘: 91.1%
2025-07-22 23:20:00,972 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.9%, CPU使用率 16.7%
2025-07-22 23:20:16,078 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.0%, CPU使用率 16.7%
2025-07-22 23:20:31,185 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.7%, CPU使用率 82.1%
2025-07-22 23:20:46,320 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.8%, CPU使用率 75.9%
2025-07-22 23:21:00,376 - health_monitor - DEBUG - 系统指标 - CPU: 51.0%, 内存: 70.3%, 磁盘: 91.1%
2025-07-22 23:21:01,429 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.7%, CPU使用率 28.6%
2025-07-22 23:21:16,535 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.3%, CPU使用率 33.3%
2025-07-22 23:21:31,641 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.8%, CPU使用率 83.3%
2025-07-22 23:21:46,947 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.6%, CPU使用率 97.5%
2025-07-22 23:22:01,518 - health_monitor - DEBUG - 系统指标 - CPU: 90.9%, 内存: 72.1%, 磁盘: 91.1%
2025-07-22 23:22:02,112 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.1%, CPU使用率 79.4%
2025-07-22 23:22:17,228 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.4%, CPU使用率 60.7%
2025-07-22 23:22:32,359 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.7%, CPU使用率 100.0%
2025-07-22 23:22:47,484 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.2%, CPU使用率 92.0%
2025-07-22 23:23:02,724 - health_monitor - DEBUG - 系统指标 - CPU: 90.4%, 内存: 71.5%, 磁盘: 91.1%
2025-07-22 23:23:02,726 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.5%, CPU使用率 100.0%
2025-07-22 23:23:17,893 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.2%, CPU使用率 100.0%
2025-07-22 23:23:33,017 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.1%, CPU使用率 20.0%
2025-07-22 23:23:48,122 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.0%, CPU使用率 3.6%
2025-07-22 23:24:03,227 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.9%, CPU使用率 39.3%
2025-07-22 23:24:03,786 - health_monitor - DEBUG - 系统指标 - CPU: 39.2%, 内存: 65.8%, 磁盘: 91.1%
2025-07-22 23:24:18,332 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.8%, CPU使用率 16.7%
2025-07-22 23:24:33,442 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.2%, CPU使用率 66.7%
2025-07-22 23:24:48,546 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.8%, CPU使用率 8.3%
2025-07-22 23:25:03,652 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.5%, CPU使用率 41.4%
2025-07-22 23:25:04,820 - health_monitor - DEBUG - 系统指标 - CPU: 76.7%, 内存: 65.5%, 磁盘: 91.1%
2025-07-22 23:25:18,757 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.1%, CPU使用率 25.0%
2025-07-22 23:25:33,863 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.0%, CPU使用率 29.2%
2025-07-22 23:25:48,982 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 66.7%
2025-07-22 23:26:04,259 - monitoring - DEBUG - 资源指标更新: 内存使用率 36.9%, CPU使用率 100.0%
2025-07-22 23:26:05,903 - health_monitor - DEBUG - 系统指标 - CPU: 98.5%, 内存: 34.3%, 磁盘: 91.1%
2025-07-22 23:26:19,685 - monitoring - DEBUG - 资源指标更新: 内存使用率 36.7%, CPU使用率 100.0%
2025-07-22 23:26:35,457 - monitoring - DEBUG - 资源指标更新: 内存使用率 40.5%, CPU使用率 100.0%
2025-07-22 23:26:51,894 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.3%, CPU使用率 100.0%
2025-07-22 23:27:07,103 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 42.5%, 磁盘: 91.1%
2025-07-22 23:27:07,412 - monitoring - DEBUG - 资源指标更新: 内存使用率 42.5%, CPU使用率 100.0%
2025-07-22 23:27:22,623 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.6%, CPU使用率 100.0%
2025-07-22 23:27:38,564 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.8%, CPU使用率 100.0%
2025-07-22 23:27:55,186 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.2%, CPU使用率 100.0%
2025-07-22 23:28:08,553 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 49.4%, 磁盘: 91.1%
2025-07-22 23:28:11,362 - monitoring - DEBUG - 资源指标更新: 内存使用率 50.1%, CPU使用率 100.0%
2025-07-22 23:28:26,791 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.1%, CPU使用率 100.0%
2025-07-22 23:28:41,995 - monitoring - DEBUG - 资源指标更新: 内存使用率 50.5%, CPU使用率 100.0%
2025-07-22 23:28:57,172 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.0%, CPU使用率 100.0%
2025-07-22 23:29:10,080 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 53.0%, 磁盘: 91.1%
2025-07-22 23:29:12,785 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.1%, CPU使用率 100.0%
2025-07-22 23:29:28,292 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.9%, CPU使用率 100.0%
2025-07-22 23:29:43,617 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.7%, CPU使用率 100.0%
2025-07-22 23:29:59,153 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.9%, CPU使用率 100.0%
2025-07-22 23:30:11,491 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 54.2%, 磁盘: 91.0%
2025-07-22 23:30:14,470 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.2%, CPU使用率 100.0%
2025-07-22 23:30:30,057 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.3%, CPU使用率 100.0%
2025-07-22 23:30:45,373 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.9%, CPU使用率 100.0%
2025-07-22 23:31:00,896 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.6%, CPU使用率 100.0%
2025-07-22 23:31:06,470 - alert_manager - WARNING - 触发告警: cpu_usage, 当前值: 100.0, 阈值: 90
2025-07-22 23:31:12,940 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 54.2%, 磁盘: 91.1%
2025-07-22 23:31:16,490 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.1%, CPU使用率 100.0%
2025-07-22 23:31:32,036 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.3%, CPU使用率 100.0%
2025-07-22 23:31:47,309 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.2%, CPU使用率 100.0%
2025-07-22 23:32:02,446 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.8%, CPU使用率 100.0%
2025-07-22 23:32:14,042 - health_monitor - DEBUG - 系统指标 - CPU: 97.4%, 内存: 55.2%, 磁盘: 91.1%
2025-07-22 23:32:17,553 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.2%, CPU使用率 100.0%
2025-07-22 23:32:32,659 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.4%, CPU使用率 100.0%
2025-07-22 23:32:47,792 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.3%, CPU使用率 100.0%
2025-07-22 23:33:02,955 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.3%, CPU使用率 76.9%
2025-07-22 23:33:15,086 - health_monitor - DEBUG - 系统指标 - CPU: 99.6%, 内存: 55.1%, 磁盘: 91.1%
2025-07-22 23:33:18,064 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 64.0%
2025-07-22 23:33:33,170 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.2%, CPU使用率 95.8%
2025-07-22 23:33:48,353 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.7%, CPU使用率 86.2%
2025-07-22 23:34:03,469 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.4%, CPU使用率 75.0%
2025-07-22 23:34:16,122 - health_monitor - DEBUG - 系统指标 - CPU: 84.1%, 内存: 63.3%, 磁盘: 91.1%
2025-07-22 23:34:18,577 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.0%, CPU使用率 100.0%
2025-07-22 23:34:33,695 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.3%, CPU使用率 92.9%
2025-07-22 23:34:48,852 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.2%, CPU使用率 100.0%
2025-07-22 23:35:04,055 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.3%, CPU使用率 100.0%
2025-07-22 23:35:17,157 - health_monitor - DEBUG - 系统指标 - CPU: 95.5%, 内存: 58.2%, 磁盘: 91.1%
2025-07-22 23:35:19,277 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.2%, CPU使用率 95.9%
2025-07-22 23:35:34,392 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.3%, CPU使用率 66.7%
2025-07-22 23:35:49,505 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.2%, CPU使用率 100.0%
2025-07-22 23:36:04,696 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.1%, CPU使用率 100.0%
2025-07-22 23:36:19,463 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 60.3%, 磁盘: 91.1%
2025-07-22 23:36:20,303 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 100.0%
2025-07-22 23:36:35,861 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.2%, CPU使用率 100.0%
2025-07-22 23:36:51,080 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.0%, CPU使用率 100.0%
2025-07-22 23:37:06,197 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.3%, CPU使用率 60.7%
2025-07-22 23:37:20,706 - health_monitor - DEBUG - 系统指标 - CPU: 76.0%, 内存: 61.2%, 磁盘: 91.1%
2025-07-22 23:37:21,310 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.2%, CPU使用率 82.8%
2025-07-22 23:37:36,417 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.9%, CPU使用率 78.6%
2025-07-22 23:37:51,524 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.4%, CPU使用率 42.9%
2025-07-22 23:38:06,672 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.1%, CPU使用率 100.0%
2025-07-22 23:38:21,763 - health_monitor - DEBUG - 系统指标 - CPU: 70.1%, 内存: 60.0%, 磁盘: 91.1%
2025-07-22 23:38:21,785 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.0%, CPU使用率 100.0%
2025-07-22 23:38:37,077 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.2%, CPU使用率 100.0%
2025-07-22 23:38:52,215 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.1%, CPU使用率 85.7%
2025-07-22 23:39:07,568 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.6%, CPU使用率 100.0%
2025-07-22 23:39:23,048 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 65.6%, 磁盘: 91.1%
2025-07-22 23:39:23,121 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.5%, CPU使用率 100.0%
2025-07-22 23:39:40,014 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.5%, CPU使用率 100.0%
2025-07-22 23:39:55,472 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.7%, CPU使用率 66.7%
2025-07-22 23:40:10,607 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.3%, CPU使用率 89.3%
2025-07-22 23:40:24,216 - health_monitor - DEBUG - 系统指标 - CPU: 90.1%, 内存: 64.2%, 磁盘: 91.1%
2025-07-22 23:40:25,762 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.2%, CPU使用率 93.5%
2025-07-22 23:40:40,867 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.6%, CPU使用率 57.1%
2025-07-22 23:40:55,996 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.5%, CPU使用率 56.2%
2025-07-22 23:41:11,108 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.5%, CPU使用率 79.2%
2025-07-22 23:41:25,497 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 62.9%, 磁盘: 91.1%
2025-07-22 23:41:26,589 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 100.0%
2025-07-22 23:41:41,999 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.2%, CPU使用率 100.0%
2025-07-22 23:41:57,231 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.0%, CPU使用率 82.1%
2025-07-22 23:42:12,931 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.9%, CPU使用率 100.0%
2025-07-22 23:42:27,407 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 62.2%, 磁盘: 91.1%
2025-07-22 23:42:28,453 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.3%, CPU使用率 100.0%
2025-07-22 23:42:43,572 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.6%, CPU使用率 70.4%
2025-07-22 23:42:58,901 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.7%, CPU使用率 100.0%
2025-07-22 23:43:14,094 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.2%, CPU使用率 100.0%
2025-07-22 23:43:28,461 - health_monitor - DEBUG - 系统指标 - CPU: 78.8%, 内存: 61.6%, 磁盘: 91.1%
2025-07-22 23:43:29,214 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.7%, CPU使用率 78.6%
2025-07-22 23:43:44,320 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.7%, CPU使用率 59.3%
2025-07-22 23:43:59,905 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.0%, CPU使用率 100.0%
2025-07-22 23:44:15,365 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.7%, CPU使用率 70.8%
2025-07-22 23:44:29,673 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 62.1%, 磁盘: 91.1%
2025-07-22 23:44:30,939 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.2%, CPU使用率 100.0%
2025-07-22 23:44:46,374 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.7%, CPU使用率 88.5%
2025-07-22 23:45:01,701 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.3%, CPU使用率 96.9%
2025-07-22 23:45:17,408 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.3%, CPU使用率 100.0%
2025-07-22 23:45:31,191 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 63.6%, 磁盘: 91.1%
2025-07-22 23:45:32,780 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 100.0%
2025-07-22 23:45:47,939 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.0%, CPU使用率 96.8%
2025-07-22 23:46:03,046 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.6%, CPU使用率 75.0%
2025-07-22 23:46:18,237 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.0%, CPU使用率 100.0%
2025-07-22 23:46:32,232 - health_monitor - DEBUG - 系统指标 - CPU: 82.8%, 内存: 62.5%, 磁盘: 91.1%
2025-07-22 23:46:33,347 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.6%, CPU使用率 79.2%
2025-07-22 23:46:48,557 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.4%, CPU使用率 100.0%
2025-07-22 23:47:03,667 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.5%, CPU使用率 71.4%
2025-07-22 23:47:18,778 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.5%, CPU使用率 100.0%
2025-07-22 23:47:33,254 - health_monitor - DEBUG - 系统指标 - CPU: 34.4%, 内存: 61.4%, 磁盘: 91.1%
2025-07-22 23:47:33,884 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.4%, CPU使用率 46.4%
2025-07-22 23:47:48,988 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.2%, CPU使用率 29.2%
2025-07-22 23:48:04,093 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.2%, CPU使用率 21.4%
2025-07-22 23:48:19,198 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.1%, CPU使用率 24.1%
2025-07-22 23:48:34,275 - health_monitor - DEBUG - 系统指标 - CPU: 28.2%, 内存: 61.1%, 磁盘: 91.1%
2025-07-22 23:48:34,302 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.1%, CPU使用率 33.3%
2025-07-22 23:48:49,406 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.3%, CPU使用率 20.8%
2025-07-22 23:49:04,510 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.2%, CPU使用率 35.7%
2025-07-22 23:49:19,614 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.6%, CPU使用率 66.7%
2025-07-22 23:49:34,719 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.9%, CPU使用率 16.7%
2025-07-22 23:49:35,304 - health_monitor - DEBUG - 系统指标 - CPU: 44.1%, 内存: 61.8%, 磁盘: 91.1%
2025-07-22 23:49:49,825 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 23.1%
2025-07-22 23:50:04,931 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.0%, CPU使用率 55.6%
2025-07-22 23:50:20,035 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.9%, CPU使用率 54.2%
2025-07-22 23:50:35,141 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.7%, CPU使用率 33.3%
2025-07-22 23:50:36,368 - health_monitor - DEBUG - 系统指标 - CPU: 60.4%, 内存: 65.7%, 磁盘: 91.1%
2025-07-22 23:50:50,669 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.1%, CPU使用率 100.0%
2025-07-22 23:51:05,918 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.3%, CPU使用率 44.4%
2025-07-22 23:51:21,024 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.2%, CPU使用率 67.9%
2025-07-22 23:51:36,129 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.3%, CPU使用率 79.2%
2025-07-22 23:51:37,446 - health_monitor - DEBUG - 系统指标 - CPU: 83.3%, 内存: 66.5%, 磁盘: 91.1%
2025-07-22 23:51:51,447 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.7%, CPU使用率 100.0%
2025-07-22 23:52:06,697 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.3%, CPU使用率 75.0%
2025-07-22 23:52:21,801 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.4%, CPU使用率 53.8%
2025-07-22 23:52:36,906 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.4%, CPU使用率 41.7%
2025-07-22 23:52:38,483 - health_monitor - DEBUG - 系统指标 - CPU: 53.1%, 内存: 62.5%, 磁盘: 91.1%
2025-07-22 23:52:52,014 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.7%, CPU使用率 58.3%
2025-07-22 23:53:07,410 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.6%, CPU使用率 100.0%
2025-07-22 23:53:22,521 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 79.2%
2025-07-22 23:53:37,627 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 64.0%
2025-07-22 23:53:39,511 - health_monitor - DEBUG - 系统指标 - CPU: 53.7%, 内存: 60.6%, 磁盘: 91.1%
2025-07-22 23:53:52,732 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.6%, CPU使用率 42.9%
2025-07-22 23:54:07,840 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.2%, CPU使用率 53.6%
2025-07-22 23:54:22,944 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.2%, CPU使用率 34.6%
2025-07-22 23:54:38,050 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.9%, CPU使用率 45.8%
2025-07-22 23:54:40,592 - health_monitor - DEBUG - 系统指标 - CPU: 97.4%, 内存: 61.3%, 磁盘: 91.1%
2025-07-22 23:54:53,156 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 95.8%
2025-07-22 23:55:08,266 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.2%, CPU使用率 66.7%
2025-07-22 23:55:23,373 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.5%, CPU使用率 67.9%
2025-07-22 23:55:38,477 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.3%, CPU使用率 19.2%
2025-07-22 23:55:41,623 - health_monitor - DEBUG - 系统指标 - CPU: 41.2%, 内存: 63.3%, 磁盘: 91.1%
2025-07-22 23:55:53,582 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.2%, CPU使用率 41.7%
2025-07-22 23:56:08,687 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.8%, CPU使用率 12.5%
2025-07-22 23:56:23,791 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.8%, CPU使用率 21.4%
2025-07-22 23:56:38,905 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.6%, CPU使用率 92.6%
2025-07-22 23:56:42,660 - health_monitor - DEBUG - 系统指标 - CPU: 51.0%, 内存: 62.5%, 磁盘: 91.1%
2025-07-22 23:56:54,010 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.6%, CPU使用率 60.7%
2025-07-22 23:57:09,117 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.4%, CPU使用率 42.9%
2025-07-22 23:57:24,232 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.4%, CPU使用率 86.2%
2025-07-22 23:57:39,336 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.4%, CPU使用率 33.3%
2025-07-22 23:57:43,884 - health_monitor - DEBUG - 系统指标 - CPU: 35.6%, 内存: 63.4%, 磁盘: 91.1%
2025-07-22 23:57:54,441 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.3%, CPU使用率 24.0%
2025-07-22 23:58:09,546 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.6%, CPU使用率 38.5%
2025-07-22 23:58:24,662 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.4%, CPU使用率 78.6%
2025-07-22 23:58:39,855 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.8%, CPU使用率 100.0%
2025-07-22 23:58:44,929 - health_monitor - DEBUG - 系统指标 - CPU: 58.1%, 内存: 61.7%, 磁盘: 91.1%
2025-07-22 23:58:55,011 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.8%, CPU使用率 75.0%
2025-07-22 23:59:10,173 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.7%, CPU使用率 82.4%
2025-07-22 23:59:25,279 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.2%, CPU使用率 71.4%
2025-07-22 23:59:40,384 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.3%, CPU使用率 57.1%
2025-07-22 23:59:45,954 - health_monitor - DEBUG - 系统指标 - CPU: 49.2%, 内存: 61.9%, 磁盘: 91.1%
2025-07-22 23:59:55,517 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.6%, CPU使用率 86.7%
2025-07-23 00:00:10,628 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.3%, CPU使用率 54.2%
2025-07-23 00:00:25,737 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.0%, CPU使用率 80.8%
2025-07-23 00:00:40,842 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.1%, CPU使用率 50.0%
2025-07-23 00:00:47,084 - health_monitor - DEBUG - 系统指标 - CPU: 89.7%, 内存: 64.0%, 磁盘: 91.1%
2025-07-23 00:00:55,950 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.9%, CPU使用率 21.4%
2025-07-23 00:01:11,054 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.1%, CPU使用率 53.8%
2025-07-23 00:01:26,159 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.3%, CPU使用率 20.8%
2025-07-23 00:01:41,266 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.0%, CPU使用率 96.0%
2025-07-23 00:01:48,107 - health_monitor - DEBUG - 系统指标 - CPU: 55.5%, 内存: 62.7%, 磁盘: 91.1%
2025-07-23 00:01:56,386 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 64.3%
2025-07-23 00:02:12,133 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.4%, CPU使用率 100.0%
2025-07-23 00:02:27,244 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.2%, CPU使用率 7.7%
2025-07-23 00:02:42,353 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.3%, CPU使用率 35.7%
2025-07-23 00:02:49,180 - health_monitor - DEBUG - 系统指标 - CPU: 79.9%, 内存: 63.6%, 磁盘: 91.1%
2025-07-23 00:02:57,457 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.5%, CPU使用率 62.5%
2025-07-23 00:03:12,653 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.7%, CPU使用率 78.4%
2025-07-23 00:03:27,764 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.8%, CPU使用率 57.7%
2025-07-23 00:03:42,870 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.1%, CPU使用率 67.9%
2025-07-23 00:03:50,337 - health_monitor - DEBUG - 系统指标 - CPU: 91.9%, 内存: 64.0%, 磁盘: 91.1%
2025-07-23 00:03:58,209 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.9%, CPU使用率 100.0%
2025-07-23 00:04:13,331 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.1%, CPU使用率 75.0%
2025-07-23 00:04:28,438 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.2%, CPU使用率 60.0%
2025-07-23 00:04:43,543 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.0%, CPU使用率 22.2%
2025-07-23 00:04:44,089 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-23 00:04:44,090 - main - INFO - 请求没有认证头部
2025-07-23 00:04:44,091 - main - INFO - 没有认证头部，设置用户为None
2025-07-23 00:04:44,093 - main - INFO - --- 请求结束: 200 ---

2025-07-23 00:04:46,136 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-23 00:04:46,137 - main - INFO - 请求没有认证头部
2025-07-23 00:04:46,137 - main - INFO - 没有认证头部，设置用户为None
2025-07-23 00:04:46,139 - app.core.db_connection - DEBUG - 当前线程ID: 16064
2025-07-23 00:04:46,140 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-23 00:04:46,141 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-23 00:04:46,142 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-23 00:04:46,143 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-23 00:04:49,954 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-23 00:04:49,957 - main - INFO - --- 请求结束: 200 ---

2025-07-23 00:04:51,512 - health_monitor - DEBUG - 系统指标 - CPU: 84.7%, 内存: 65.3%, 磁盘: 91.1%
2025-07-23 00:04:59,009 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.1%, CPU使用率 98.8%
2025-07-23 00:05:14,190 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.1%, CPU使用率 59.3%
2025-07-23 00:05:29,295 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.5%, CPU使用率 23.1%
2025-07-23 00:05:44,447 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.5%, CPU使用率 51.9%
2025-07-23 00:05:52,573 - health_monitor - DEBUG - 系统指标 - CPU: 50.0%, 内存: 63.8%, 磁盘: 91.1%
2025-07-23 00:05:59,552 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.8%, CPU使用率 75.0%
2025-07-23 00:06:14,669 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.9%, CPU使用率 60.7%
2025-07-23 00:06:29,792 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.0%, CPU使用率 78.6%
2025-07-23 00:06:45,253 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.5%, CPU使用率 100.0%
2025-07-23 00:06:53,598 - health_monitor - DEBUG - 系统指标 - CPU: 56.2%, 内存: 64.1%, 磁盘: 91.1%
2025-07-23 00:07:00,545 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.3%, CPU使用率 89.3%
2025-07-23 00:07:15,665 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.1%, CPU使用率 91.7%
2025-07-23 00:07:30,774 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.7%, CPU使用率 89.3%
2025-07-23 00:07:34,320 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-23 00:07:34,324 - main - INFO - 请求没有认证头部
2025-07-23 00:07:34,325 - main - INFO - 没有认证头部，设置用户为None
2025-07-23 00:07:34,328 - main - INFO - --- 请求结束: 200 ---

2025-07-23 00:07:36,371 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-23 00:07:36,385 - main - INFO - 请求没有认证头部
2025-07-23 00:07:36,389 - main - INFO - 没有认证头部，设置用户为None
2025-07-23 00:07:36,391 - app.core.db_connection - DEBUG - 当前线程ID: 16064
2025-07-23 00:07:36,392 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-23 00:07:36,397 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-23 00:07:36,399 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-23 00:07:36,400 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-23 00:07:36,400 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-23 00:07:36,406 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-07-23 00:07:36,406 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-07-23 00:07:36,407 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-07-23 00:07:36,408 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-07-23 00:07:36,409 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-07-23 00:07:37,638 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-23 00:07:37,641 - main - INFO - --- 请求结束: 200 ---

2025-07-23 00:07:46,064 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.4%, CPU使用率 100.0%
2025-07-23 00:07:54,627 - health_monitor - DEBUG - 系统指标 - CPU: 55.8%, 内存: 66.0%, 磁盘: 91.1%
2025-07-23 00:08:01,301 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.5%, CPU使用率 68.2%
2025-07-23 00:08:16,405 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.2%, CPU使用率 8.3%
2025-07-23 00:08:31,510 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.2%, CPU使用率 29.6%
2025-07-23 00:08:46,933 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.8%, CPU使用率 100.0%
2025-07-23 00:08:55,881 - health_monitor - DEBUG - 系统指标 - CPU: 79.4%, 内存: 65.7%, 磁盘: 91.1%
2025-07-23 00:09:02,123 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.3%, CPU使用率 78.6%
2025-07-23 00:09:17,239 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.3%, CPU使用率 100.0%
2025-07-23 00:09:32,344 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.5%, CPU使用率 66.7%
2025-07-23 00:09:47,453 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.4%, CPU使用率 71.4%
2025-07-23 00:09:57,523 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 65.9%, 磁盘: 91.1%
2025-07-23 00:10:03,218 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.8%, CPU使用率 100.0%
2025-07-23 00:10:19,283 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.1%, CPU使用率 100.0%
2025-07-23 00:10:34,847 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.2%, CPU使用率 100.0%
2025-07-23 00:10:50,240 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.9%, CPU使用率 93.3%
2025-07-23 00:10:58,898 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 66.3%, 磁盘: 91.1%
2025-07-23 00:11:05,351 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.9%, CPU使用率 64.3%
2025-07-23 00:11:20,479 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.0%, CPU使用率 100.0%
2025-07-23 00:11:35,593 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.1%, CPU使用率 53.8%
2025-07-23 00:11:50,698 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.1%, CPU使用率 75.0%
2025-07-23 00:12:00,318 - health_monitor - DEBUG - 系统指标 - CPU: 98.3%, 内存: 67.3%, 磁盘: 91.1%
2025-07-23 00:12:02,627 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-23 00:12:02,759 - main - INFO - 请求没有认证头部
2025-07-23 00:12:02,940 - main - INFO - 没有认证头部，设置用户为None
2025-07-23 00:12:02,999 - main - INFO - --- 请求结束: 200 ---

2025-07-23 00:12:05,442 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-23 00:12:05,443 - main - INFO - 请求没有认证头部
2025-07-23 00:12:05,443 - main - INFO - 没有认证头部，设置用户为None
2025-07-23 00:12:05,445 - app.core.db_connection - DEBUG - 当前线程ID: 16064
2025-07-23 00:12:05,446 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-23 00:12:05,447 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-23 00:12:05,447 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-23 00:12:05,448 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-23 00:12:05,861 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 97.2%
2025-07-23 00:12:07,290 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-23 00:12:07,295 - main - INFO - --- 请求结束: 200 ---

2025-07-23 00:12:21,400 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.1%, CPU使用率 42.9%
2025-07-23 00:12:36,509 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.3%, CPU使用率 50.0%
2025-07-23 00:12:51,633 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.4%, CPU使用率 100.0%
2025-07-23 00:13:01,362 - health_monitor - DEBUG - 系统指标 - CPU: 97.7%, 内存: 67.2%, 磁盘: 91.1%
2025-07-23 00:13:06,777 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.7%, CPU使用率 92.9%
2025-07-23 00:13:13,272 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-23 00:13:13,273 - main - INFO - 请求没有认证头部
2025-07-23 00:13:13,274 - main - INFO - 没有认证头部，设置用户为None
2025-07-23 00:13:13,275 - main - INFO - --- 请求结束: 200 ---

2025-07-23 00:13:15,619 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-23 00:13:15,620 - main - INFO - 请求没有认证头部
2025-07-23 00:13:15,621 - main - INFO - 没有认证头部，设置用户为None
2025-07-23 00:13:15,626 - app.core.db_connection - DEBUG - 当前线程ID: 16064
2025-07-23 00:13:15,628 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-23 00:13:15,631 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-23 00:13:15,634 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-23 00:13:15,635 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-23 00:13:17,346 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-23 00:13:17,426 - main - INFO - --- 请求结束: 200 ---

2025-07-23 00:13:22,083 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.0%, CPU使用率 100.0%
2025-07-23 00:13:37,189 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.9%, CPU使用率 83.3%
2025-07-23 00:13:52,319 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 85.2%
2025-07-23 00:14:02,399 - health_monitor - DEBUG - 系统指标 - CPU: 53.1%, 内存: 66.4%, 磁盘: 91.1%
2025-07-23 00:14:07,432 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.1%, CPU使用率 78.6%
2025-07-23 00:14:22,826 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.6%, CPU使用率 91.9%
2025-07-23 00:14:38,194 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 100.0%
2025-07-23 00:14:45,883 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-23 00:14:46,129 - main - INFO - 请求没有认证头部
2025-07-23 00:14:46,340 - main - INFO - 没有认证头部，设置用户为None
2025-07-23 00:14:46,510 - main - INFO - --- 请求结束: 200 ---

2025-07-23 00:14:48,849 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-23 00:14:48,958 - main - INFO - 请求没有认证头部
2025-07-23 00:14:49,006 - main - INFO - 没有认证头部，设置用户为None
2025-07-23 00:14:49,009 - app.core.db_connection - DEBUG - 当前线程ID: 16064
2025-07-23 00:14:49,009 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-23 00:14:49,010 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-23 00:14:49,011 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-23 00:14:49,012 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-23 00:14:51,661 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-23 00:14:51,771 - main - INFO - --- 请求结束: 200 ---

2025-07-23 00:14:53,595 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.7%, CPU使用率 100.0%
2025-07-23 00:15:03,495 - health_monitor - DEBUG - 系统指标 - CPU: 83.6%, 内存: 65.8%, 磁盘: 91.1%
2025-07-23 00:15:08,774 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 82.1%
2025-07-23 00:15:14,476 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-23 00:15:14,477 - main - INFO - 请求没有认证头部
2025-07-23 00:15:14,477 - main - INFO - 没有认证头部，设置用户为None
2025-07-23 00:15:14,479 - main - INFO - --- 请求结束: 200 ---

2025-07-23 00:15:16,513 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-23 00:15:16,590 - main - INFO - 请求没有认证头部
2025-07-23 00:15:16,607 - main - INFO - 没有认证头部，设置用户为None
2025-07-23 00:15:16,617 - app.core.db_connection - DEBUG - 当前线程ID: 16064
2025-07-23 00:15:16,620 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-23 00:15:16,622 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-23 00:15:16,623 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-23 00:15:16,624 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-23 00:15:18,745 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-23 00:15:18,748 - main - INFO - --- 请求结束: 200 ---

2025-07-23 00:15:23,884 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.5%, CPU使用率 100.0%
2025-07-23 00:15:38,993 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.0%, CPU使用率 75.0%
2025-07-23 00:15:54,101 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.4%, CPU使用率 41.7%
2025-07-23 00:16:04,625 - health_monitor - DEBUG - 系统指标 - CPU: 68.2%, 内存: 66.2%, 磁盘: 91.1%
2025-07-23 00:16:09,205 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.4%, CPU使用率 40.9%
2025-07-23 00:16:24,398 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.9%, CPU使用率 87.8%
2025-07-23 00:16:39,594 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.9%, CPU使用率 100.0%
2025-07-23 00:16:54,725 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.0%, CPU使用率 71.4%
2025-07-23 00:16:58,124 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-23 00:16:58,125 - main - INFO - 请求没有认证头部
2025-07-23 00:16:58,125 - main - INFO - 没有认证头部，设置用户为None
2025-07-23 00:16:58,126 - main - INFO - --- 请求结束: 200 ---

2025-07-23 00:17:00,168 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-23 00:17:00,173 - main - INFO - 请求没有认证头部
2025-07-23 00:17:00,174 - main - INFO - 没有认证头部，设置用户为None
2025-07-23 00:17:00,176 - app.core.db_connection - DEBUG - 当前线程ID: 16064
2025-07-23 00:17:00,177 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-23 00:17:00,178 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-23 00:17:00,179 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-23 00:17:00,179 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-23 00:17:01,458 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-23 00:17:01,505 - main - INFO - --- 请求结束: 200 ---

2025-07-23 00:17:05,665 - health_monitor - DEBUG - 系统指标 - CPU: 80.2%, 内存: 68.3%, 磁盘: 91.1%
2025-07-23 00:17:09,841 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.0%, CPU使用率 93.3%
2025-07-23 00:17:13,363 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-23 00:17:13,364 - main - INFO - 请求没有认证头部
2025-07-23 00:17:13,364 - main - INFO - 没有认证头部，设置用户为None
2025-07-23 00:17:13,366 - main - INFO - --- 请求结束: 200 ---

2025-07-23 00:17:15,405 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-23 00:17:15,406 - main - INFO - 请求没有认证头部
2025-07-23 00:17:15,406 - main - INFO - 没有认证头部，设置用户为None
2025-07-23 00:17:15,408 - app.core.db_connection - DEBUG - 当前线程ID: 16064
2025-07-23 00:17:15,408 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-23 00:17:15,410 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-23 00:17:15,410 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-23 00:17:15,411 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-23 00:17:15,412 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-23 00:17:16,276 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-23 00:17:16,279 - main - INFO - --- 请求结束: 200 ---

2025-07-23 00:17:24,947 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.2%, CPU使用率 39.3%
2025-07-23 00:17:40,055 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 100.0%
2025-07-23 00:17:55,175 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.6%, CPU使用率 62.5%
2025-07-23 00:18:06,756 - health_monitor - DEBUG - 系统指标 - CPU: 58.2%, 内存: 68.1%, 磁盘: 91.1%
2025-07-23 00:18:10,281 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 70.8%
2025-07-23 00:18:25,386 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.8%, CPU使用率 60.7%
2025-07-23 00:18:40,745 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.2%, CPU使用率 96.6%
2025-07-23 00:18:56,030 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.5%, CPU使用率 100.0%
2025-07-23 00:19:07,988 - health_monitor - DEBUG - 系统指标 - CPU: 72.2%, 内存: 70.2%, 磁盘: 91.1%
2025-07-23 00:19:11,410 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.3%, CPU使用率 100.0%
2025-07-23 00:19:26,814 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.8%, CPU使用率 91.4%
2025-07-23 00:19:39,196 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-23 00:19:39,197 - main - INFO - 请求没有认证头部
2025-07-23 00:19:39,197 - main - INFO - 没有认证头部，设置用户为None
2025-07-23 00:19:39,200 - main - INFO - --- 请求结束: 200 ---

2025-07-23 00:19:41,235 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-23 00:19:41,236 - main - INFO - 请求没有认证头部
2025-07-23 00:19:41,237 - main - INFO - 没有认证头部，设置用户为None
2025-07-23 00:19:41,238 - app.core.db_connection - DEBUG - 当前线程ID: 16064
2025-07-23 00:19:41,239 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-23 00:19:41,241 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-23 00:19:41,242 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-23 00:19:41,243 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-23 00:19:41,244 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-23 00:19:41,949 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.9%, CPU使用率 82.1%
2025-07-23 00:19:42,585 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-23 00:19:42,588 - main - INFO - --- 请求结束: 200 ---

2025-07-23 00:19:57,145 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.1%, CPU使用率 40.0%
2025-07-23 00:20:08,788 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-23 00:20:08,789 - main - INFO - 请求没有认证头部
2025-07-23 00:20:08,790 - main - INFO - 没有认证头部，设置用户为None
2025-07-23 00:20:08,792 - main - INFO - --- 请求结束: 200 ---

2025-07-23 00:20:09,024 - health_monitor - DEBUG - 系统指标 - CPU: 56.8%, 内存: 72.3%, 磁盘: 91.1%
2025-07-23 00:20:10,841 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-23 00:20:10,914 - main - INFO - 请求没有认证头部
2025-07-23 00:20:10,915 - main - INFO - 没有认证头部，设置用户为None
2025-07-23 00:20:10,918 - app.core.db_connection - DEBUG - 当前线程ID: 16064
2025-07-23 00:20:10,919 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-23 00:20:10,921 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-23 00:20:10,922 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-23 00:20:10,924 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-23 00:20:10,925 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-23 00:20:12,169 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-23 00:20:12,248 - main - INFO - --- 请求结束: 200 ---

2025-07-23 00:20:12,289 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.1%, CPU使用率 100.0%
2025-07-23 00:20:16,280 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-23 00:20:16,295 - main - INFO - 请求没有认证头部
2025-07-23 00:20:16,296 - main - INFO - 没有认证头部，设置用户为None
2025-07-23 00:20:16,298 - main - INFO - --- 请求结束: 200 ---

2025-07-23 00:20:18,325 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-23 00:20:18,336 - main - INFO - 请求没有认证头部
2025-07-23 00:20:18,336 - main - INFO - 没有认证头部，设置用户为None
2025-07-23 00:20:18,338 - app.core.db_connection - DEBUG - 当前线程ID: 16064
2025-07-23 00:20:18,339 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-23 00:20:18,341 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-23 00:20:18,342 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-23 00:20:18,346 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-23 00:20:18,347 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-23 00:20:20,551 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-23 00:20:20,847 - main - INFO - --- 请求结束: 200 ---

2025-07-23 00:20:27,648 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.3%, CPU使用率 92.3%
2025-07-23 00:20:42,844 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.2%, CPU使用率 89.5%
2025-07-23 00:20:58,180 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.5%, CPU使用率 93.8%
2025-07-23 00:21:10,047 - health_monitor - DEBUG - 系统指标 - CPU: 52.9%, 内存: 65.9%, 磁盘: 91.1%
2025-07-23 00:21:13,284 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.9%, CPU使用率 41.7%
2025-07-23 00:21:28,392 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.8%, CPU使用率 29.2%
2025-07-23 00:21:33,898 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-23 00:21:33,902 - main - INFO - 请求没有认证头部
2025-07-23 00:21:33,904 - main - INFO - 没有认证头部，设置用户为None
2025-07-23 00:21:33,906 - main - INFO - --- 请求结束: 200 ---

2025-07-23 00:21:35,960 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-23 00:21:35,968 - main - INFO - 请求没有认证头部
2025-07-23 00:21:35,969 - main - INFO - 没有认证头部，设置用户为None
2025-07-23 00:21:35,971 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-07-23 00:21:35,972 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-07-23 00:21:35,974 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-07-23 00:21:35,974 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-07-23 00:21:35,976 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-07-23 00:21:35,977 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-07-23 00:21:35,978 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-07-23 00:21:35,980 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-07-23 00:21:35,991 - app.core.db_connection - DEBUG - 当前线程ID: 16064
2025-07-23 00:21:35,993 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-23 00:21:35,995 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-23 00:21:35,995 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-23 00:21:35,996 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-23 00:21:37,889 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-23 00:21:37,891 - main - INFO - --- 请求结束: 200 ---

2025-07-23 00:21:43,524 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 100.0%
2025-07-23 00:21:58,789 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 100.0%
2025-07-23 00:22:11,186 - health_monitor - DEBUG - 系统指标 - CPU: 96.3%, 内存: 65.4%, 磁盘: 91.1%
2025-07-23 00:22:13,976 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.2%, CPU使用率 100.0%
