#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
带认证的API测试脚本
"""

import requests
import json
import sys
import os

# 添加后端路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'YUN', 'backend'))

def login_and_get_token(base_url):
    """登录并获取token"""
    login_url = f"{base_url}/api/auth/frontend_login"
    
    # 尝试使用admin账户登录
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        print(f"尝试登录: {login_url}")
        response = requests.post(login_url, json=login_data, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if 'access_token' in data:
                print(f"登录成功，获取到token: {data['access_token'][:20]}...")
                return data['access_token']
            else:
                print(f"登录响应中没有access_token: {data}")
        else:
            print(f"登录失败，状态码: {response.status_code}, 响应: {response.text}")
            
    except Exception as e:
        print(f"登录异常: {e}")
    
    return None

def test_api_with_token(url, token, description):
    """使用token测试API端点"""
    print(f"\n=== 测试 {description} ===")
    print(f"URL: {url}")
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"响应数据类型: {type(data)}")
                
                if isinstance(data, list):
                    print(f"数据数量: {len(data)}")
                    if len(data) > 0:
                        print(f"第一条数据: {json.dumps(data[0], ensure_ascii=False, indent=2)}")
                        if len(data) > 1:
                            print(f"第二条数据: {json.dumps(data[1], ensure_ascii=False, indent=2)}")
                elif isinstance(data, dict):
                    print(f"响应数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
                else:
                    print(f"响应数据: {data}")
                    
            except json.JSONDecodeError:
                print(f"响应内容: {response.text[:500]}")
        else:
            print(f"错误响应: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("连接失败 - 服务器可能未运行")
    except requests.exceptions.Timeout:
        print("请求超时")
    except Exception as e:
        print(f"请求异常: {e}")

def test_database_with_sqlite():
    """直接使用sqlite3测试数据库"""
    print("\n=== 直接测试SQLite数据库 ===")
    
    import sqlite3
    
    # 尝试不同的数据库路径
    db_paths = [
        os.path.join(os.path.dirname(__file__), 'YUN', 'backend', 'app.db'),
        os.path.join(os.path.dirname(__file__), 'YUN', 'backend', 'health_management.db'),
        os.path.join(os.path.dirname(__file__), 'backend', 'app.db')
    ]
    
    for db_path in db_paths:
        if os.path.exists(db_path):
            print(f"\n找到数据库: {db_path}")
            
            try:
                conn = sqlite3.connect(db_path)
                conn.row_factory = sqlite3.Row
                
                # 查看表结构
                cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                print(f"数据库表: {tables}")
                
                # 查看用户数据
                if 'users' in tables:
                    cursor = conn.execute("SELECT * FROM users LIMIT 5")
                    users = cursor.fetchall()
                    print(f"\n用户数量: {len(users)}")
                    for user in users:
                        print(f"  用户: {dict(user)}")
                
                # 查看评估数据
                if 'assessments' in tables:
                    cursor = conn.execute("SELECT * FROM assessments LIMIT 5")
                    assessments = cursor.fetchall()
                    print(f"\n评估记录数量: {len(assessments)}")
                    for assessment in assessments:
                        print(f"  评估: {dict(assessment)}")
                
                # 查看SM_008的数据
                if 'assessments' in tables:
                    cursor = conn.execute("SELECT * FROM assessments WHERE custom_id = 'SM_008'")
                    sm008_assessments = cursor.fetchall()
                    print(f"\nSM_008的评估记录数量: {len(sm008_assessments)}")
                    for assessment in sm008_assessments:
                        print(f"  SM_008评估: {dict(assessment)}")
                
                conn.close()
                break
                
            except Exception as e:
                print(f"数据库操作失败: {e}")
                conn.close()
        else:
            print(f"数据库不存在: {db_path}")

def main():
    """主函数"""
    print("开始带认证的API测试...")
    
    # 测试不同端口
    ports = [8006, 8080, 8000]
    
    for port in ports:
        base_url = f"http://localhost:{port}"
        print(f"\n=== 测试端口 {port} ===")
        
        # 尝试登录获取token
        token = login_and_get_token(base_url)
        
        if token:
            # 测试API端点
            test_api_with_token(f"{base_url}/api/assessments", token, f"评估量表API (端口{port})")
            test_api_with_token(f"{base_url}/api/assessments/records", token, f"评估记录API (端口{port})")
            test_api_with_token(f"{base_url}/api/questionnaires", token, f"问卷API (端口{port})")
            test_api_with_token(f"{base_url}/api/users", token, f"用户API (端口{port})")
            
            # 如果找到有效的API，就不再测试其他端口
            break
        else:
            print(f"端口 {port} 登录失败")
    
    # 直接测试数据库
    test_database_with_sqlite()
    
    print("\n测试完成!")

if __name__ == "__main__":
    main()