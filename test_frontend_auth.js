// 测试前端认证状态和API调用

// 检查localStorage中的token
function checkAuthStatus() {
    const token = localStorage.getItem('token');
    const customId = localStorage.getItem('custom_id');
    
    console.log('=== 认证状态检查 ===');
    console.log('Token:', token ? '存在' : '不存在');
    console.log('Custom ID:', customId);
    
    if (token) {
        try {
            // 尝试解析JWT token
            const payload = JSON.parse(atob(token.split('.')[1]));
            console.log('Token payload:', payload);
            console.log('Token过期时间:', new Date(payload.exp * 1000));
            console.log('Token是否过期:', Date.now() > payload.exp * 1000);
        } catch (e) {
            console.log('Token解析失败:', e.message);
        }
    }
    
    return { token, customId };
}

// 测试API调用
async function testApiCalls() {
    const { token } = checkAuthStatus();
    
    const headers = {
        'Content-Type': 'application/json'
    };
    
    if (token) {
        headers['Authorization'] = `Bearer ${token}`;
    }
    
    console.log('\n=== API调用测试 ===');
    
    // 测试评估记录API
    try {
        console.log('测试 /api/assessments/records...');
        const response = await fetch('/api/assessments/records', {
            method: 'GET',
            headers: headers
        });
        
        console.log('状态码:', response.status);
        console.log('状态文本:', response.statusText);
        
        if (response.ok) {
            const data = await response.json();
            console.log('返回数据:', data);
            console.log('数据类型:', Array.isArray(data) ? '数组' : typeof data);
            if (Array.isArray(data)) {
                console.log('记录数量:', data.length);
            } else if (data && typeof data === 'object') {
                console.log('对象键:', Object.keys(data));
            }
        } else {
            const errorText = await response.text();
            console.log('错误响应:', errorText);
        }
    } catch (error) {
        console.error('API调用失败:', error);
    }
    
    // 测试评估列表API
    try {
        console.log('\n测试 /api/assessments...');
        const response = await fetch('/api/assessments', {
            method: 'GET',
            headers: headers
        });
        
        console.log('状态码:', response.status);
        console.log('状态文本:', response.statusText);
        
        if (response.ok) {
            const data = await response.json();
            console.log('返回数据类型:', Array.isArray(data) ? '数组' : typeof data);
            if (Array.isArray(data)) {
                console.log('评估数量:', data.length);
            }
        } else {
            const errorText = await response.text();
            console.log('错误响应:', errorText);
        }
    } catch (error) {
        console.error('API调用失败:', error);
    }
}

// 模拟登录
async function testLogin() {
    console.log('\n=== 登录测试 ===');
    
    try {
        const response = await fetch('/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: 'admin',
                password: 'admin123'
            })
        });
        
        console.log('登录状态码:', response.status);
        
        if (response.ok) {
            const data = await response.json();
            console.log('登录响应:', data);
            
            if (data.token) {
                localStorage.setItem('token', data.token);
                if (data.custom_id) {
                    localStorage.setItem('custom_id', data.custom_id);
                }
                console.log('Token已保存到localStorage');
                
                // 重新测试API
                await testApiCalls();
            }
        } else {
            const errorText = await response.text();
            console.log('登录失败:', errorText);
        }
    } catch (error) {
        console.error('登录请求失败:', error);
    }
}

// 清除认证信息
function clearAuth() {
    localStorage.removeItem('token');
    localStorage.removeItem('custom_id');
    console.log('认证信息已清除');
}

// 主函数
async function main() {
    console.log('开始前端认证和API测试...');
    
    // 首先检查当前认证状态
    await testApiCalls();
    
    // 如果没有token或API调用失败，尝试登录
    const { token } = checkAuthStatus();
    if (!token) {
        console.log('\n没有找到token，尝试登录...');
        await testLogin();
    }
}

// 导出函数供控制台使用
window.testAuth = {
    checkAuthStatus,
    testApiCalls,
    testLogin,
    clearAuth,
    main
};

// 自动运行
main();