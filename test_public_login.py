#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试公网服务器登录功能
"""

import requests
import json

def test_public_server_login():
    """测试公网服务器登录"""
    url = "http://8.138.188.26/api/direct-login"
    
    # 测试数据
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print(f"正在测试登录: {url}")
        print(f"登录数据: {login_data}")
        
        response = requests.post(
            url,
            json=login_data,
            headers=headers,
            timeout=10
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            print(f"响应数据: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
            
            if response_data.get('access_token'):
                print("✓ 登录成功，获取到访问令牌")
                return True
            else:
                print("✗ 登录失败，未获取到访问令牌")
                return False
                
        except json.JSONDecodeError:
            print(f"响应文本: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return False

def test_local_server_login():
    """测试本地服务器登录作为对比"""
    url = "http://localhost:8006/api/direct-login"
    
    # 测试数据
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print(f"\n正在测试本地服务器登录: {url}")
        print(f"登录数据: {login_data}")
        
        response = requests.post(
            url,
            json=login_data,
            headers=headers,
            timeout=10
        )
        
        print(f"响应状态码: {response.status_code}")
        
        try:
            response_data = response.json()
            print(f"响应数据: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
            
            if response_data.get('access_token'):
                print("✓ 本地服务器登录成功")
                return True
            else:
                print("✗ 本地服务器登录失败")
                return False
                
        except json.JSONDecodeError:
            print(f"响应文本: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return False

if __name__ == "__main__":
    print("=== 测试服务器登录功能 ===")
    
    # 测试公网服务器
    public_result = test_public_server_login()
    
    # 测试本地服务器
    local_result = test_local_server_login()
    
    print("\n=== 测试结果汇总 ===")
    print(f"公网服务器登录: {'✓ 成功' if public_result else '✗ 失败'}")
    print(f"本地服务器登录: {'✓ 成功' if local_result else '✗ 失败'}")