#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整检查assessment相关数据
包括assessment_responses和assessment_results
"""

import sqlite3
import json
import os

def check_complete_assessment_data():
    """完整检查assessment数据"""
    print("=== 完整Assessment数据检查 ===")
    
    db_path = "c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db"
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 检查SM_008用户的所有assessment_responses记录
        print("\n1. SM_008用户的所有assessment_responses记录:")
        cursor.execute("""
            SELECT ar.id, ar.assessment_id, ar.answers, ar.created_at, a.name
            FROM assessment_responses ar
            LEFT JOIN assessments a ON ar.assessment_id = a.id
            WHERE ar.custom_id = 'SM_008'
            ORDER BY ar.created_at
        """)
        responses = cursor.fetchall()
        
        print(f"找到 {len(responses)} 条assessment_responses记录")
        
        for i, response in enumerate(responses):
            print(f"\n  记录 {i+1}:")
            print(f"    ID: {response[0]}")
            print(f"    Assessment ID: {response[1]}")
            print(f"    Assessment名称: {response[4]}")
            print(f"    创建时间: {response[3]}")
            
            answers_raw = response[2]
            if answers_raw:
                try:
                    answers_data = json.loads(answers_raw)
                    if isinstance(answers_data, list):
                        print(f"    回答问题数: {len(answers_data)}")
                        # 显示前3个问题的详情
                        for j, answer in enumerate(answers_data[:3]):
                            print(f"      问题{j+1}: {answer}")
                        if len(answers_data) > 3:
                            print(f"      ... 还有 {len(answers_data)-3} 个问题")
                    else:
                        print(f"    回答数据格式: {type(answers_data)}")
                except Exception as e:
                    print(f"    解析回答数据失败: {e}")
            else:
                print("    无回答数据")
        
        # 2. 检查assessment_results表结构
        print("\n\n2. 检查assessment_results表结构:")
        cursor.execute("PRAGMA table_info(assessment_results)")
        columns = cursor.fetchall()
        print("assessment_results表的列:")
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
        
        # 3. 检查SM_008用户的所有assessment_results记录
        print("\n\n3. SM_008用户的所有assessment_results记录:")
        cursor.execute("""
            SELECT ar.id, ar.assessment_id, ar.total_score, ar.result_category, 
                   ar.interpretation, ar.created_at, a.name, ar.raw_answers
            FROM assessment_results ar
            LEFT JOIN assessments a ON ar.assessment_id = a.id
            WHERE ar.custom_id = 'SM_008'
            ORDER BY ar.created_at
        """)
        results = cursor.fetchall()
        
        print(f"找到 {len(results)} 条assessment_results记录")
        
        for i, result in enumerate(results):
            print(f"\n  结果记录 {i+1}:")
            print(f"    ID: {result[0]}")
            print(f"    Assessment ID: {result[1]}")
            print(f"    Assessment名称: {result[6]}")
            print(f"    总分: {result[2]}")
            print(f"    结果分类: {result[3]}")
            print(f"    解释: {result[4][:100] if result[4] else 'None'}...")
            print(f"    创建时间: {result[5]}")
            
            # 检查raw_answers字段
            raw_answers = result[7]
            if raw_answers:
                try:
                    answers_data = json.loads(raw_answers)
                    if isinstance(answers_data, list):
                        print(f"    原始回答问题数: {len(answers_data)}")
                        # 显示前3个问题的详情
                        for j, answer in enumerate(answers_data[:3]):
                            if isinstance(answer, dict):
                                qid = answer.get('question_id', 'unknown')
                                ans = answer.get('answer', 'no_answer')
                                scr = answer.get('score', 0)
                                print(f"      {qid}: 答案={ans}, 得分={scr}")
                        if len(answers_data) > 3:
                            print(f"      ... 还有 {len(answers_data)-3} 个问题")
                    else:
                        print(f"    原始回答数据格式: {type(answers_data)}")
                except Exception as e:
                    print(f"    解析原始回答数据失败: {e}")
            else:
                print("    无原始回答数据")
        
        # 4. 检查蒙特利尔认知评估量表的问题模板数量
        print("\n\n4. 蒙特利尔认知评估量表问题模板检查:")
        cursor.execute("""
            SELECT COUNT(*) 
            FROM assessment_template_questions atq
            JOIN assessment_templates at ON atq.template_id = at.id
            WHERE at.name LIKE '%蒙特利尔%'
        """)
        template_question_count = cursor.fetchone()[0]
        print(f"模板中的问题总数: {template_question_count}")
        
        if template_question_count > 0:
            cursor.execute("""
                SELECT atq.question_id, atq.question_text, atq.question_type
                FROM assessment_template_questions atq
                JOIN assessment_templates at ON atq.template_id = at.id
                WHERE at.name LIKE '%蒙特利尔%'
                ORDER BY atq.order_num
                LIMIT 5
            """)
            sample_questions = cursor.fetchall()
            print("\n前5个问题模板:")
            for q in sample_questions:
                print(f"  {q[0]}: {q[1][:50]}... (类型: {q[2]})")
        
        # 5. 数据一致性检查
        print("\n\n5. 数据一致性分析:")
        print(f"  assessment_responses记录数: {len(responses)}")
        print(f"  assessment_results记录数: {len(results)}")
        print(f"  问题模板数: {template_question_count}")
        
        if len(responses) != len(results):
            print("  ⚠️ responses和results记录数不匹配")
        
        if template_question_count == 22:
            print("  ✅ 问题模板数量正确（22个MoCA问题）")
        else:
            print(f"  ⚠️ 问题模板数量异常，期望22个，实际{template_question_count}个")
        
        # 检查最新的assessment_responses记录是否包含完整的22个问题
        if responses:
            latest_response = responses[-1]  # 最新的记录
            answers_raw = latest_response[2]
            if answers_raw:
                try:
                    answers_data = json.loads(answers_raw)
                    if isinstance(answers_data, list) and len(answers_data) == 22:
                        print("  ✅ 最新responses记录包含完整的22个问题回答")
                    else:
                        print(f"  ⚠️ 最新responses记录只包含{len(answers_data)}个问题回答")
                except:
                    print("  ❌ 最新responses记录的回答数据解析失败")
        
        conn.close()
        
    except Exception as e:
        print(f"检查过程中出错: {str(e)}")
    
    print("\n=== 完整检查完成 ===")

if __name__ == "__main__":
    check_complete_assessment_data()