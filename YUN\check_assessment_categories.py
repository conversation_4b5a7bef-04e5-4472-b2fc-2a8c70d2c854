# -*- coding: utf-8 -*-
import sqlite3
import os

# 数据库路径
db_path = 'c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db'

try:
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    print("=== 检查评估模板分类数据 ===")
    cursor.execute('SELECT template_key, sub_type, assessment_type FROM assessment_templates LIMIT 10')
    templates = cursor.fetchall()
    
    print(f"找到 {len(templates)} 个评估模板:")
    for template in templates:
        print(f"  模板: {template[0]}, 子类型: {template[1]}, 评估类型: {template[2]}")
    
    print("\n=== 检查评估记录关联的模板 ===")
    cursor.execute('''
        SELECT a.id, a.template_id, at.template_key, at.sub_type, at.assessment_type 
        FROM assessments a 
        LEFT JOIN assessment_templates at ON a.template_id = at.id 
        LIMIT 10
    ''')
    assessments = cursor.fetchall()
    
    print(f"找到 {len(assessments)} 个评估记录:")
    for assessment in assessments:
        print(f"  评估ID: {assessment[0]}, 模板ID: {assessment[1]}, 模板: {assessment[2]}, 子类型: {assessment[3]}, 评估类型: {assessment[4]}")
    
    conn.close()
    print("\n检查完成!")
    
except Exception as e:
    print(f"检查失败: {e}")