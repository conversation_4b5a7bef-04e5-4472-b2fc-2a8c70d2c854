<template>
  <div class="follow-up-tab">
    <div class="tab-header">
      <h3>随访记录</h3>
      <div class="action-buttons">
        <el-button type="primary" size="small" @click="openAddDialog" v-if="editable">添加随访记录</el-button>
        <el-button type="primary" size="small" @click="refreshData">刷新数据</el-button>
      </div>
    </div>

    <el-table
      :data="followUps"
      style="width: 100%"
      v-loading="loading"
      :empty-text="emptyText"
    >
      <el-table-column prop="follow_up_date" label="随访日期" width="120">
        <template #default="scope">
          {{ formatDate(scope.row.follow_up_date) }}
        </template>
      </el-table-column>
      <el-table-column prop="follow_up_type" label="随访类型" width="120">
        <template #default="scope">
          {{ getFollowUpTypeLabel(scope.row.follow_up_type) }}
        </template>
      </el-table-column>
      <el-table-column prop="doctor_name" label="随访医生" width="120" />
      <el-table-column prop="hospital_name" label="医院" width="180" />
      <el-table-column prop="department" label="科室" width="120" />
      <el-table-column prop="main_complaint" label="主诉" show-overflow-tooltip />
      <el-table-column prop="next_follow_up_date" label="下次随访日期" width="120">
        <template #default="scope">
          {{ formatDate(scope.row.next_follow_up_date) || '未安排' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180" fixed="right">
        <template #default="scope">
          <el-button type="primary" link @click="viewFollowUp(scope.row)">查看</el-button>
          <el-button type="success" link @click="editFollowUp(scope.row)" v-if="editable">编辑</el-button>
          <el-button type="danger" link @click="deleteFollowUp(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 随访记录详情对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="随访记录详情"
      width="70%"
    >
      <div v-if="currentFollowUp" class="follow-up-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="随访日期">{{ formatDate(currentFollowUp.follow_up_date) }}</el-descriptions-item>
          <el-descriptions-item label="随访类型">{{ getFollowUpTypeLabel(currentFollowUp.follow_up_type) }}</el-descriptions-item>
          <el-descriptions-item label="随访医生">{{ currentFollowUp.doctor_name }}</el-descriptions-item>
          <el-descriptions-item label="医院">{{ currentFollowUp.hospital_name }}</el-descriptions-item>
          <el-descriptions-item label="科室">{{ currentFollowUp.department }}</el-descriptions-item>
          <el-descriptions-item label="下次随访日期">{{ formatDate(currentFollowUp.next_follow_up_date) || '未安排' }}</el-descriptions-item>
          <el-descriptions-item label="主诉" :span="2">{{ currentFollowUp.main_complaint }}</el-descriptions-item>
          <el-descriptions-item label="体格检查" :span="2">{{ currentFollowUp.physical_examination }}</el-descriptions-item>
          <el-descriptions-item label="诊断" :span="2">{{ currentFollowUp.diagnosis }}</el-descriptions-item>
          <el-descriptions-item label="治疗方案" :span="2">{{ currentFollowUp.treatment_plan }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ currentFollowUp.notes }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 添加/编辑随访记录对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      :title="isEditing ? '编辑随访记录' : '添加随访记录'"
      width="70%"
    >
      <el-form :model="followUpForm" label-width="120px" :rules="rules" ref="followUpFormRef">
        <el-form-item label="随访日期" prop="follow_up_date">
          <el-date-picker v-model="followUpForm.follow_up_date" type="date" placeholder="选择随访日期" style="width: 100%" />
        </el-form-item>
        <el-form-item label="随访类型" prop="follow_up_type">
          <el-select v-model="followUpForm.follow_up_type" placeholder="请选择随访类型" style="width: 100%">
            <el-option label="门诊随访" value="outpatient" />
            <el-option label="电话随访" value="telephone" />
            <el-option label="家庭随访" value="home_visit" />
            <el-option label="网络随访" value="online" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="随访医生" prop="doctor_name">
          <el-input v-model="followUpForm.doctor_name" placeholder="请输入随访医生姓名" />
        </el-form-item>
        <el-form-item label="医院" prop="hospital_name">
          <el-input v-model="followUpForm.hospital_name" placeholder="请输入医院名称" />
        </el-form-item>
        <el-form-item label="科室" prop="department">
          <el-input v-model="followUpForm.department" placeholder="请输入科室名称" />
        </el-form-item>
        <el-form-item label="主诉" prop="main_complaint">
          <el-input v-model="followUpForm.main_complaint" type="textarea" rows="2" placeholder="请输入主诉" />
        </el-form-item>
        <el-form-item label="体格检查" prop="physical_examination">
          <el-input v-model="followUpForm.physical_examination" type="textarea" rows="3" placeholder="请输入体格检查结果" />
        </el-form-item>
        <el-form-item label="诊断" prop="diagnosis">
          <el-input v-model="followUpForm.diagnosis" type="textarea" rows="2" placeholder="请输入诊断结果" />
        </el-form-item>
        <el-form-item label="治疗方案" prop="treatment_plan">
          <el-input v-model="followUpForm.treatment_plan" type="textarea" rows="3" placeholder="请输入治疗方案" />
        </el-form-item>
        <el-form-item label="下次随访日期">
          <el-date-picker v-model="followUpForm.next_follow_up_date" type="date" placeholder="选择下次随访日期（可选）" style="width: 100%" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="followUpForm.notes" type="textarea" rows="2" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveFollowUp">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import axios from 'axios';

const props = defineProps({
  customId: {
    type: [Number, String],
    required: true
  },
  editable: {
    type: Boolean,
    default: false
  }
});

const loading = ref(false);
const followUps = ref([]);
const viewDialogVisible = ref(false);
const editDialogVisible = ref(false);
const currentFollowUp = ref(null);
const isEditing = ref(false);
const followUpFormRef = ref(null);

const followUpForm = ref({
  follow_up_date: '',
  follow_up_type: '',
  doctor_name: '',
  hospital_name: '',
  department: '',
  main_complaint: '',
  physical_examination: '',
  diagnosis: '',
  treatment_plan: '',
  next_follow_up_date: '',
  notes: ''
});

const rules = {
  follow_up_date: [
    { required: true, message: '请选择随访日期', trigger: 'change' }
  ],
  follow_up_type: [
    { required: true, message: '请选择随访类型', trigger: 'change' }
  ],
  doctor_name: [
    { required: true, message: '请输入随访医生姓名', trigger: 'blur' }
  ],
  hospital_name: [
    { required: true, message: '请输入医院名称', trigger: 'blur' }
  ],
  department: [
    { required: true, message: '请输入科室名称', trigger: 'blur' }
  ],
  main_complaint: [
    { required: true, message: '请输入主诉', trigger: 'blur' }
  ],
  diagnosis: [
    { required: true, message: '请输入诊断结果', trigger: 'blur' }
  ],
  treatment_plan: [
    { required: true, message: '请输入治疗方案', trigger: 'blur' }
  ]
};

const emptyText = computed(() => {
  return loading.value ? '加载中...' : '暂无随访记录数据';
});

// 统一聚合接口请求
const fetchUserHealthRecords = async () => {
  if (!props.customId) return;
  loading.value = true;
  try {
    const response = await axios.get(`/api/v1/aggregated/health-profile/${props.customId}`, {
      params: {
        include_follow_ups: true
      }
    });
    const profileData = response.data.profile_data || {};
    followUps.value = profileData.follow_ups || [];
  } catch (error) {
    ElMessage.error('获取健康资料失败');
    followUps.value = [];
  } finally {
    loading.value = false;
  }
};



onMounted(() => {
  if (props.customId) {
    fetchUserHealthRecords();
  }
});

watch(() => props.customId, (newVal) => {
  if (newVal) {
    fetchUserHealthRecords();
  } else {
    followUps.value = [];
  }
}, { immediate: true });

const refreshData = () => {
  fetchUserHealthRecords();
};

const viewFollowUp = (followUp) => {
  currentFollowUp.value = followUp;
  viewDialogVisible.value = true;
};

const openAddDialog = () => {
  isEditing.value = false;
  followUpForm.value = {
    follow_up_date: new Date(),
    follow_up_type: '',
    doctor_name: '',
    hospital_name: '',
    department: '',
    main_complaint: '',
    physical_examination: '',
    diagnosis: '',
    treatment_plan: '',
    next_follow_up_date: '',
    notes: ''
  };
  editDialogVisible.value = true;
};

const editFollowUp = (followUp) => {
  isEditing.value = true;
  followUpForm.value = { ...followUp };
  
  if (followUpForm.value.follow_up_date) {
    followUpForm.value.follow_up_date = new Date(followUpForm.value.follow_up_date);
  }
  if (followUpForm.value.next_follow_up_date) {
    followUpForm.value.next_follow_up_date = new Date(followUpForm.value.next_follow_up_date);
  }
  
  editDialogVisible.value = true;
};

const saveFollowUp = async () => {
  if (!followUpFormRef.value) return;
  
  await followUpFormRef.value.validate(async (valid) => {
    if (!valid) {
      ElMessage.warning('请完善表单信息');
      return;
    }
    
    try {
      const formData = { ...followUpForm.value };
      formData.custom_id = props.customId;
      
      if (formData.follow_up_date instanceof Date) {
        formData.follow_up_date = formData.follow_up_date.toISOString().split('T')[0];
      }
      if (formData.next_follow_up_date instanceof Date) {
        formData.next_follow_up_date = formData.next_follow_up_date.toISOString().split('T')[0];
      }
      
      if (isEditing.value) {
        await axios.put(`/api/follow-ups/${formData.id}`, formData);
        ElMessage.success('随访记录更新成功');
      } else {
        await axios.post('/api/follow-ups', formData);
        ElMessage.success('随访记录添加成功');
      }
      
      editDialogVisible.value = false;
      fetchUserHealthRecords();
    } catch (error) {
      ElMessage.error('保存随访记录失败，请稍后重试');
    }
  });
};

const deleteFollowUp = (followUp) => {
  ElMessageBox.confirm(
    `确定要删除 ${formatDate(followUp.follow_up_date)} 的随访记录吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await axios.delete(`/api/follow-ups/${followUp.id}`);
      ElMessage.success('删除成功');
      fetchUserHealthRecords();
    } catch (error) {
      ElMessage.error('删除随访记录失败，请稍后重试');
    }
  }).catch(() => {});
};

const formatDate = (dateString) => {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  return date.toLocaleDateString();
};

const getFollowUpTypeLabel = (type) => {
  const typeMap = {
    'outpatient': '门诊随访',
    'telephone': '电话随访',
    'home_visit': '家庭随访',
    'online': '网络随访',
    'other': '其他'
  };
  
  return typeMap[type] || type;
};
</script>

<style scoped>
.follow-up-tab {
  padding: 10px;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.tab-header h3 {
  margin: 0;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.follow-up-detail {
  padding: 10px;
}
</style>
