2025-07-25 10:02:56,050 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-25 10:02:56,054 - auth_service - INFO - 统一认证服务初始化完成
2025-07-25 10:02:56,169 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-25 10:02:56,170 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-25 10:02:56,671 - BackendMockDataManager - INFO - 后端模拟数据模式已启用
2025-07-25 10:03:00,301 - DataExportService - DEBUG - 已注册表模型: users -> User
2025-07-25 10:03:00,304 - DataExportService - DEBUG - 已注册表模型: health_records -> HealthRecord
2025-07-25 10:03:00,312 - DataExportService - DEBUG - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-25 10:03:00,317 - DataExportService - DEBUG - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-25 10:03:00,319 - DataExportService - DEBUG - 已注册表模型: assessment_results -> AssessmentResult
2025-07-25 10:03:00,321 - DataExportService - DEBUG - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-25 10:03:00,323 - DataExportService - DEBUG - 已注册表模型: health_diaries -> HealthDiary
2025-07-25 10:03:00,325 - DataExportService - DEBUG - 已注册表模型: medical_records -> MedicalRecord
2025-07-25 10:03:00,327 - DataExportService - DEBUG - 已注册表模型: lab_reports -> LabReport
2025-07-25 10:03:00,335 - DataExportService - DEBUG - 已注册表模型: examination_reports -> ExaminationReport
2025-07-25 10:03:00,336 - DataExportService - DEBUG - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-25 10:03:00,337 - DataExportService - DEBUG - 已注册表模型: medications -> Medication
2025-07-25 10:03:00,338 - DataExportService - DEBUG - 已注册表模型: imaging_reports -> ImagingReport
2025-07-25 10:03:00,342 - DataExportService - DEBUG - 已注册表模型: other_records -> OtherRecord
2025-07-25 10:03:00,343 - DataExportService - DEBUG - 已注册表模型: user_health_records -> HealthRecord
2025-07-25 10:03:00,345 - DataExportService - DEBUG - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-25 10:03:00,349 - DataExportService - DEBUG - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-25 10:03:00,351 - DataExportService - DEBUG - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-25 10:03:00,352 - DataExportService - DEBUG - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-25 10:03:00,354 - DataExportService - INFO - 已初始化 19 个表模型
2025-07-25 10:03:00,361 - DataExportService - DEBUG - 已注册表模型: users -> User
2025-07-25 10:03:00,367 - DataExportService - DEBUG - 已注册表模型: health_records -> HealthRecord
2025-07-25 10:03:00,369 - DataExportService - DEBUG - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-25 10:03:00,372 - DataExportService - DEBUG - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-25 10:03:00,374 - DataExportService - DEBUG - 已注册表模型: assessment_results -> AssessmentResult
2025-07-25 10:03:00,376 - DataExportService - DEBUG - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-25 10:03:00,381 - DataExportService - DEBUG - 已注册表模型: health_diaries -> HealthDiary
2025-07-25 10:03:00,383 - DataExportService - DEBUG - 已注册表模型: medical_records -> MedicalRecord
2025-07-25 10:03:00,384 - DataExportService - DEBUG - 已注册表模型: lab_reports -> LabReport
2025-07-25 10:03:00,385 - DataExportService - DEBUG - 已注册表模型: examination_reports -> ExaminationReport
2025-07-25 10:03:00,387 - DataExportService - DEBUG - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-25 10:03:00,389 - DataExportService - DEBUG - 已注册表模型: medications -> Medication
2025-07-25 10:03:00,390 - DataExportService - DEBUG - 已注册表模型: imaging_reports -> ImagingReport
2025-07-25 10:03:00,391 - DataExportService - DEBUG - 已注册表模型: other_records -> OtherRecord
2025-07-25 10:03:00,393 - DataExportService - DEBUG - 已注册表模型: user_health_records -> HealthRecord
2025-07-25 10:03:00,394 - DataExportService - DEBUG - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-25 10:03:00,400 - DataExportService - DEBUG - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-25 10:03:00,433 - DataExportService - DEBUG - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-25 10:03:00,459 - DataExportService - DEBUG - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-25 10:03:00,462 - DataExportService - INFO - 已初始化 19 个表模型
2025-07-25 10:03:00,467 - DataExportService - DEBUG - 已注册表模型: users -> User
2025-07-25 10:03:00,471 - DataExportService - DEBUG - 已注册表模型: health_records -> HealthRecord
2025-07-25 10:03:00,474 - DataExportService - DEBUG - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-25 10:03:00,482 - DataExportService - DEBUG - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-25 10:03:00,485 - DataExportService - DEBUG - 已注册表模型: assessment_results -> AssessmentResult
2025-07-25 10:03:00,486 - DataExportService - DEBUG - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-25 10:03:00,489 - DataExportService - DEBUG - 已注册表模型: health_diaries -> HealthDiary
2025-07-25 10:03:00,491 - DataExportService - DEBUG - 已注册表模型: medical_records -> MedicalRecord
2025-07-25 10:03:00,492 - DataExportService - DEBUG - 已注册表模型: lab_reports -> LabReport
2025-07-25 10:03:00,497 - DataExportService - DEBUG - 已注册表模型: examination_reports -> ExaminationReport
2025-07-25 10:03:00,499 - DataExportService - DEBUG - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-25 10:03:00,500 - DataExportService - DEBUG - 已注册表模型: medications -> Medication
2025-07-25 10:03:00,501 - DataExportService - DEBUG - 已注册表模型: imaging_reports -> ImagingReport
2025-07-25 10:03:00,503 - DataExportService - DEBUG - 已注册表模型: other_records -> OtherRecord
2025-07-25 10:03:00,504 - DataExportService - DEBUG - 已注册表模型: user_health_records -> HealthRecord
2025-07-25 10:03:00,506 - DataExportService - DEBUG - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-25 10:03:00,507 - DataExportService - DEBUG - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-25 10:03:00,509 - DataExportService - DEBUG - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-25 10:03:00,510 - DataExportService - DEBUG - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-25 10:03:00,551 - DataExportService - INFO - 已初始化 19 个表模型
2025-07-25 10:03:00,555 - DataExportService - DEBUG - 已注册表模型: users -> User
2025-07-25 10:03:00,560 - DataExportService - DEBUG - 已注册表模型: health_records -> HealthRecord
2025-07-25 10:03:00,637 - DataExportService - DEBUG - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-25 10:03:00,723 - DataExportService - DEBUG - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-25 10:03:00,769 - DataExportService - DEBUG - 已注册表模型: assessment_results -> AssessmentResult
2025-07-25 10:03:00,774 - DataExportService - DEBUG - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-25 10:03:00,797 - DataExportService - DEBUG - 已注册表模型: health_diaries -> HealthDiary
2025-07-25 10:03:00,830 - DataExportService - DEBUG - 已注册表模型: medical_records -> MedicalRecord
2025-07-25 10:03:00,841 - DataExportService - DEBUG - 已注册表模型: lab_reports -> LabReport
2025-07-25 10:03:00,903 - DataExportService - DEBUG - 已注册表模型: examination_reports -> ExaminationReport
2025-07-25 10:03:00,921 - DataExportService - DEBUG - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-25 10:03:00,926 - DataExportService - DEBUG - 已注册表模型: medications -> Medication
2025-07-25 10:03:00,937 - DataExportService - DEBUG - 已注册表模型: imaging_reports -> ImagingReport
2025-07-25 10:03:00,958 - DataExportService - DEBUG - 已注册表模型: other_records -> OtherRecord
2025-07-25 10:03:00,965 - DataExportService - DEBUG - 已注册表模型: user_health_records -> HealthRecord
2025-07-25 10:03:00,967 - DataExportService - DEBUG - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-25 10:03:00,969 - DataExportService - DEBUG - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-25 10:03:00,970 - DataExportService - DEBUG - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-25 10:03:00,982 - DataExportService - DEBUG - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-25 10:03:00,986 - DataExportService - INFO - 已初始化 19 个表模型
2025-07-25 10:03:01,134 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-07-25 10:03:01,141 - fallback_manager - DEBUG - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-07-25 10:03:01,215 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-07-25 10:03:01,465 - health_monitor - INFO - 健康监控器初始化完成
2025-07-25 10:03:01,468 - app.core.system_monitor - INFO - 已加载 16 个历史数据点
2025-07-25 10:03:01,471 - app.core.alert_detector - INFO - 已加载 6 个告警规则
2025-07-25 10:03:01,472 - app.core.alert_detector - INFO - 已加载 0 个当前告警
2025-07-25 10:03:01,474 - app.core.alert_detector - INFO - 已加载 3 个历史告警
2025-07-25 10:03:01,475 - app.core.alert_detector - INFO - 已加载 1 个通知渠道
2025-07-25 10:03:01,478 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-25 10:03:01,487 - alert_manager - INFO - 已初始化默认告警规则
2025-07-25 10:03:01,488 - alert_manager - INFO - 已初始化默认通知渠道
2025-07-25 10:03:01,489 - alert_manager - INFO - 告警管理器初始化完成
2025-07-25 10:03:02,807 - db_service - INFO - 数据库引擎和会话工厂创建成功
2025-07-25 10:03:02,823 - db_service - INFO - 数据库服务初始化完成
2025-07-25 10:03:02,838 - notification_service - INFO - 通知服务初始化完成
2025-07-25 10:03:02,842 - main - INFO - 错误处理模块导入成功
2025-07-25 10:03:02,958 - main - INFO - 监控模块导入成功
2025-07-25 10:03:02,961 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-07-25 10:03:19,733 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-25 10:03:19,831 - auth_service - INFO - 统一认证服务初始化完成
2025-07-25 10:03:20,454 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-25 10:03:20,481 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-25 10:03:21,408 - BackendMockDataManager - INFO - 后端模拟数据模式已启用
2025-07-25 10:03:25,038 - DataExportService - DEBUG - 已注册表模型: users -> User
2025-07-25 10:03:25,040 - DataExportService - DEBUG - 已注册表模型: health_records -> HealthRecord
2025-07-25 10:03:25,042 - DataExportService - DEBUG - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-25 10:03:25,047 - DataExportService - DEBUG - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-25 10:03:25,049 - DataExportService - DEBUG - 已注册表模型: assessment_results -> AssessmentResult
2025-07-25 10:03:25,051 - DataExportService - DEBUG - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-25 10:03:25,053 - DataExportService - DEBUG - 已注册表模型: health_diaries -> HealthDiary
2025-07-25 10:03:25,055 - DataExportService - DEBUG - 已注册表模型: medical_records -> MedicalRecord
2025-07-25 10:03:25,057 - DataExportService - DEBUG - 已注册表模型: lab_reports -> LabReport
2025-07-25 10:03:25,058 - DataExportService - DEBUG - 已注册表模型: examination_reports -> ExaminationReport
2025-07-25 10:03:25,061 - DataExportService - DEBUG - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-25 10:03:25,063 - DataExportService - DEBUG - 已注册表模型: medications -> Medication
2025-07-25 10:03:25,065 - DataExportService - DEBUG - 已注册表模型: imaging_reports -> ImagingReport
2025-07-25 10:03:25,066 - DataExportService - DEBUG - 已注册表模型: other_records -> OtherRecord
2025-07-25 10:03:25,070 - DataExportService - DEBUG - 已注册表模型: user_health_records -> HealthRecord
2025-07-25 10:03:25,073 - DataExportService - DEBUG - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-25 10:03:25,075 - DataExportService - DEBUG - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-25 10:03:25,081 - DataExportService - DEBUG - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-25 10:03:25,083 - DataExportService - DEBUG - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-25 10:03:25,096 - DataExportService - INFO - 已初始化 19 个表模型
2025-07-25 10:03:25,100 - DataExportService - DEBUG - 已注册表模型: users -> User
2025-07-25 10:03:25,107 - DataExportService - DEBUG - 已注册表模型: health_records -> HealthRecord
2025-07-25 10:03:25,120 - DataExportService - DEBUG - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-25 10:03:25,122 - DataExportService - DEBUG - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-25 10:03:25,125 - DataExportService - DEBUG - 已注册表模型: assessment_results -> AssessmentResult
2025-07-25 10:03:25,130 - DataExportService - DEBUG - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-25 10:03:25,132 - DataExportService - DEBUG - 已注册表模型: health_diaries -> HealthDiary
2025-07-25 10:03:25,134 - DataExportService - DEBUG - 已注册表模型: medical_records -> MedicalRecord
2025-07-25 10:03:25,136 - DataExportService - DEBUG - 已注册表模型: lab_reports -> LabReport
2025-07-25 10:03:25,138 - DataExportService - DEBUG - 已注册表模型: examination_reports -> ExaminationReport
2025-07-25 10:03:25,152 - DataExportService - DEBUG - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-25 10:03:25,155 - DataExportService - DEBUG - 已注册表模型: medications -> Medication
2025-07-25 10:03:25,157 - DataExportService - DEBUG - 已注册表模型: imaging_reports -> ImagingReport
2025-07-25 10:03:25,164 - DataExportService - DEBUG - 已注册表模型: other_records -> OtherRecord
2025-07-25 10:03:25,166 - DataExportService - DEBUG - 已注册表模型: user_health_records -> HealthRecord
2025-07-25 10:03:25,168 - DataExportService - DEBUG - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-25 10:03:25,170 - DataExportService - DEBUG - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-25 10:03:25,172 - DataExportService - DEBUG - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-25 10:03:25,175 - DataExportService - DEBUG - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-25 10:03:25,181 - DataExportService - INFO - 已初始化 19 个表模型
2025-07-25 10:03:25,183 - DataExportService - DEBUG - 已注册表模型: users -> User
2025-07-25 10:03:25,186 - DataExportService - DEBUG - 已注册表模型: health_records -> HealthRecord
2025-07-25 10:03:25,195 - DataExportService - DEBUG - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-25 10:03:25,197 - DataExportService - DEBUG - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-25 10:03:25,200 - DataExportService - DEBUG - 已注册表模型: assessment_results -> AssessmentResult
2025-07-25 10:03:25,202 - DataExportService - DEBUG - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-25 10:03:25,207 - DataExportService - DEBUG - 已注册表模型: health_diaries -> HealthDiary
2025-07-25 10:03:25,214 - DataExportService - DEBUG - 已注册表模型: medical_records -> MedicalRecord
2025-07-25 10:03:25,218 - DataExportService - DEBUG - 已注册表模型: lab_reports -> LabReport
2025-07-25 10:03:25,220 - DataExportService - DEBUG - 已注册表模型: examination_reports -> ExaminationReport
2025-07-25 10:03:25,222 - DataExportService - DEBUG - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-25 10:03:25,229 - DataExportService - DEBUG - 已注册表模型: medications -> Medication
2025-07-25 10:03:25,232 - DataExportService - DEBUG - 已注册表模型: imaging_reports -> ImagingReport
2025-07-25 10:03:25,234 - DataExportService - DEBUG - 已注册表模型: other_records -> OtherRecord
2025-07-25 10:03:25,237 - DataExportService - DEBUG - 已注册表模型: user_health_records -> HealthRecord
2025-07-25 10:03:25,239 - DataExportService - DEBUG - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-25 10:03:25,250 - DataExportService - DEBUG - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-25 10:03:25,382 - DataExportService - DEBUG - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-25 10:03:25,434 - DataExportService - DEBUG - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-25 10:03:25,602 - DataExportService - INFO - 已初始化 19 个表模型
2025-07-25 10:03:25,631 - DataExportService - DEBUG - 已注册表模型: users -> User
2025-07-25 10:03:25,668 - DataExportService - DEBUG - 已注册表模型: health_records -> HealthRecord
2025-07-25 10:03:25,735 - DataExportService - DEBUG - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-25 10:03:25,778 - DataExportService - DEBUG - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-25 10:03:26,050 - DataExportService - DEBUG - 已注册表模型: assessment_results -> AssessmentResult
2025-07-25 10:03:26,163 - DataExportService - DEBUG - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-25 10:03:26,280 - DataExportService - DEBUG - 已注册表模型: health_diaries -> HealthDiary
2025-07-25 10:03:26,346 - DataExportService - DEBUG - 已注册表模型: medical_records -> MedicalRecord
2025-07-25 10:03:26,355 - DataExportService - DEBUG - 已注册表模型: lab_reports -> LabReport
2025-07-25 10:03:26,358 - DataExportService - DEBUG - 已注册表模型: examination_reports -> ExaminationReport
2025-07-25 10:03:26,363 - DataExportService - DEBUG - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-25 10:03:26,370 - DataExportService - DEBUG - 已注册表模型: medications -> Medication
2025-07-25 10:03:26,475 - DataExportService - DEBUG - 已注册表模型: imaging_reports -> ImagingReport
2025-07-25 10:03:26,573 - DataExportService - DEBUG - 已注册表模型: other_records -> OtherRecord
2025-07-25 10:03:26,637 - DataExportService - DEBUG - 已注册表模型: user_health_records -> HealthRecord
2025-07-25 10:03:26,775 - DataExportService - DEBUG - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-25 10:03:26,817 - DataExportService - DEBUG - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-25 10:03:26,852 - DataExportService - DEBUG - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-25 10:03:26,874 - DataExportService - DEBUG - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-25 10:03:26,965 - DataExportService - INFO - 已初始化 19 个表模型
2025-07-25 10:03:27,139 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-07-25 10:03:27,148 - fallback_manager - DEBUG - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-07-25 10:03:27,199 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-07-25 10:03:27,450 - health_monitor - INFO - 健康监控器初始化完成
2025-07-25 10:03:27,452 - app.core.system_monitor - INFO - 已加载 16 个历史数据点
2025-07-25 10:03:27,456 - app.core.alert_detector - INFO - 已加载 6 个告警规则
2025-07-25 10:03:27,460 - app.core.alert_detector - INFO - 已加载 0 个当前告警
2025-07-25 10:03:27,464 - app.core.alert_detector - INFO - 已加载 3 个历史告警
2025-07-25 10:03:27,467 - app.core.alert_detector - INFO - 已加载 1 个通知渠道
2025-07-25 10:03:27,471 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-25 10:03:27,482 - alert_manager - INFO - 已初始化默认告警规则
2025-07-25 10:03:27,484 - alert_manager - INFO - 已初始化默认通知渠道
2025-07-25 10:03:27,485 - alert_manager - INFO - 告警管理器初始化完成
2025-07-25 10:03:30,214 - db_service - INFO - 数据库引擎和会话工厂创建成功
2025-07-25 10:03:30,220 - db_service - INFO - 数据库服务初始化完成
2025-07-25 10:03:30,239 - notification_service - INFO - 通知服务初始化完成
2025-07-25 10:03:30,241 - main - INFO - 错误处理模块导入成功
2025-07-25 10:03:30,417 - main - INFO - 监控模块导入成功
2025-07-25 10:03:30,496 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-07-25 10:03:38,929 - asyncio - DEBUG - Using selector: SelectSelector
2025-07-25 10:03:39,444 - main - INFO - 错误处理模块导入成功
2025-07-25 10:03:39,785 - main - INFO - 监控模块导入成功
2025-07-25 10:03:40,019 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-07-25 10:03:43,912 - main - INFO - 应用启动中...
2025-07-25 10:03:43,949 - error_handling - INFO - 错误处理已设置
2025-07-25 10:03:44,060 - main - INFO - 错误处理系统初始化完成
2025-07-25 10:03:44,085 - monitoring - INFO - 添加指标端点成功: /metrics
2025-07-25 10:03:44,113 - monitoring - INFO - 添加健康检查端点成功: /health
2025-07-25 10:03:44,122 - monitoring - INFO - 添加详细健康检查端点成功: /api/health/detailed
2025-07-25 10:03:44,130 - monitoring - INFO - 系统信息初始化完成: Markey, Windows-10-10.0.19045-SP0, Python 3.13.2, CPU核心数: 4
2025-07-25 10:03:44,138 - monitoring - INFO - 启动资源监控线程成功
2025-07-25 10:03:44,146 - monitoring - INFO - 监控系统初始化成功（不使用中间件）
2025-07-25 10:03:44,149 - monitoring - INFO - 监控系统初始化完成
2025-07-25 10:03:44,151 - main - INFO - 监控系统初始化完成
2025-07-25 10:03:44,154 - app.db.init_db - INFO - 所有模型导入成功
2025-07-25 10:03:44,162 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-07-25 10:03:44,167 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-25 10:03:44,170 - app.db.init_db - INFO - 所有模型导入成功
2025-07-25 10:03:44,179 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-07-25 10:03:44,181 - app.db.init_db - INFO - 正在运行数据库迁移...
2025-07-25 10:03:44,183 - app.db.init_db - INFO - 正在检查并更新数据库表结构...
2025-07-25 10:03:44,265 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-25 10:03:44,570 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.7%, CPU使用率 98.4%
2025-07-25 10:03:44,580 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alerts")
2025-07-25 10:03:44,585 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:44,593 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_rules")
2025-07-25 10:03:44,654 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:44,659 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_channels")
2025-07-25 10:03:44,663 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:44,666 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-25 10:03:44,669 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:44,672 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_records")
2025-07-25 10:03:44,680 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:44,684 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_overviews")
2025-07-25 10:03:44,687 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:44,693 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medical_records")
2025-07-25 10:03:44,696 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:44,700 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("inpatient_records")
2025-07-25 10:03:44,703 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:44,717 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("surgery_records")
2025-07-25 10:03:44,721 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:44,735 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_reports")
2025-07-25 10:03:44,851 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:44,866 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_report_items")
2025-07-25 10:03:44,869 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:44,872 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_reports")
2025-07-25 10:03:44,898 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:44,902 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("examination_reports")
2025-07-25 10:03:44,925 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:44,930 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("documents")
2025-07-25 10:03:44,934 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:44,937 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("other_records")
2025-07-25 10:03:44,945 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:44,948 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("operation_logs")
2025-07-25 10:03:44,952 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:44,957 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("follow_up_records")
2025-07-25 10:03:44,961 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:44,964 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_diaries")
2025-07-25 10:03:44,967 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:44,970 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("registration_records")
2025-07-25 10:03:44,983 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:44,987 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("prescription_records")
2025-07-25 10:03:44,993 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:44,996 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("laboratory_records")
2025-07-25 10:03:44,999 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:45,002 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_records")
2025-07-25 10:03:45,006 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:45,014 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medications")
2025-07-25 10:03:45,018 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:45,021 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medication_usages")
2025-07-25 10:03:45,028 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:45,033 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("role_applications")
2025-07-25 10:03:45,036 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:45,039 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessments")
2025-07-25 10:03:45,050 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:45,053 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_items")
2025-07-25 10:03:45,059 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:45,071 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_responses")
2025-07-25 10:03:45,081 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:45,087 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_templates")
2025-07-25 10:03:45,095 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:45,099 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_template_questions")
2025-07-25 10:03:45,103 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:45,111 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaires")
2025-07-25 10:03:45,115 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:45,118 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_items")
2025-07-25 10:03:45,121 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:45,135 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_responses")
2025-07-25 10:03:45,139 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:45,149 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_answers")
2025-07-25 10:03:45,156 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:45,161 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_templates")
2025-07-25 10:03:45,168 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:45,179 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_template_questions")
2025-07-25 10:03:45,182 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:45,187 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_results")
2025-07-25 10:03:45,192 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:45,195 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_results")
2025-07-25 10:03:45,200 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:45,204 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("report_templates")
2025-07-25 10:03:45,212 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:45,220 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_distributions")
2025-07-25 10:03:45,266 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:45,270 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_distributions")
2025-07-25 10:03:45,276 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:45,279 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("service_stats")
2025-07-25 10:03:45,283 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 10:03:45,289 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-25 10:03:45,296 - app.db.init_db - INFO - 数据库表结构检查和更新完成
2025-07-25 10:03:45,303 - app.db.init_db - INFO - 模型关系初始化完成
2025-07-25 10:03:45,305 - app.db.init_db - INFO - 模型关系设置完成
2025-07-25 10:03:45,310 - main - INFO - 数据库初始化完成（强制重建）
2025-07-25 10:03:45,312 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-25 10:03:45,315 - main - INFO - 数据库连接正常
2025-07-25 10:03:45,318 - main - INFO - 开始初始化模板数据
2025-07-25 10:03:45,321 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-25 10:03:46,144 - app.db.init_templates - INFO - 开始初始化评估量表模板...
2025-07-25 10:03:46,223 - app.db.init_templates - INFO - 成功初始化 5 个评估量表模板
2025-07-25 10:03:46,268 - app.db.init_templates - INFO - 开始初始化调查问卷模板...
2025-07-25 10:03:46,444 - app.db.init_templates - INFO - 成功初始化 5 个调查问卷模板
2025-07-25 10:03:46,468 - main - INFO - 模板数据初始化完成
2025-07-25 10:03:46,471 - main - INFO - 数据库表结构已经标准化，不需要迁移
2025-07-25 10:03:46,498 - main - INFO - 应用启动完成
2025-07-25 10:04:00,115 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.4%, CPU使用率 100.0%
2025-07-25 10:04:02,507 - health_monitor - DEBUG - 系统指标 - CPU: 88.6%, 内存: 69.9%, 磁盘: 95.1%
2025-07-25 10:04:15,513 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.9%, CPU使用率 100.0%
2025-07-25 10:04:28,549 - health_monitor - DEBUG - 系统指标 - CPU: 96.2%, 内存: 68.7%, 磁盘: 95.1%
2025-07-25 10:04:30,750 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.1%, CPU使用率 91.4%
2025-07-25 10:04:45,859 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.3%, CPU使用率 75.0%
2025-07-25 10:05:00,967 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.3%, CPU使用率 62.5%
2025-07-25 10:05:03,570 - health_monitor - DEBUG - 系统指标 - CPU: 67.3%, 内存: 68.2%, 磁盘: 95.1%
2025-07-25 10:05:16,073 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.4%, CPU使用率 54.2%
2025-07-25 10:05:29,610 - health_monitor - DEBUG - 系统指标 - CPU: 62.3%, 内存: 68.5%, 磁盘: 95.1%
2025-07-25 10:05:31,178 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.5%, CPU使用率 57.1%
2025-07-25 10:05:46,283 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.6%, CPU使用率 42.3%
2025-07-25 10:06:01,387 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.8%, CPU使用率 58.3%
2025-07-25 10:06:04,595 - health_monitor - DEBUG - 系统指标 - CPU: 71.5%, 内存: 69.0%, 磁盘: 95.1%
2025-07-25 10:06:16,494 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.1%, CPU使用率 64.0%
2025-07-25 10:06:30,637 - health_monitor - DEBUG - 系统指标 - CPU: 62.8%, 内存: 68.1%, 磁盘: 95.1%
2025-07-25 10:06:31,598 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.2%, CPU使用率 50.0%
2025-07-25 10:06:46,712 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.4%, CPU使用率 69.2%
2025-07-25 10:07:01,818 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.5%, CPU使用率 67.9%
2025-07-25 10:07:05,622 - health_monitor - DEBUG - 系统指标 - CPU: 66.1%, 内存: 65.7%, 磁盘: 95.1%
2025-07-25 10:07:16,923 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.9%, CPU使用率 36.0%
2025-07-25 10:07:31,663 - health_monitor - DEBUG - 系统指标 - CPU: 63.7%, 内存: 66.1%, 磁盘: 95.1%
2025-07-25 10:07:32,162 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.1%, CPU使用率 100.0%
2025-07-25 10:07:47,346 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.1%, CPU使用率 65.4%
2025-07-25 10:08:02,454 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.1%, CPU使用率 82.1%
2025-07-25 10:08:06,649 - health_monitor - DEBUG - 系统指标 - CPU: 66.8%, 内存: 66.2%, 磁盘: 95.1%
2025-07-25 10:08:17,568 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.2%, CPU使用率 64.3%
2025-07-25 10:08:32,686 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.3%, CPU使用率 53.6%
2025-07-25 10:08:32,687 - health_monitor - DEBUG - 系统指标 - CPU: 65.8%, 内存: 66.3%, 磁盘: 95.1%
2025-07-25 10:08:47,794 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.5%, CPU使用率 83.3%
2025-07-25 10:09:01,494 - alert_manager - WARNING - 触发告警: disk_free_space, 当前值: 0.0, 阈值: 1024
2025-07-25 10:09:02,902 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.7%, CPU使用率 70.8%
2025-07-25 10:09:07,672 - health_monitor - DEBUG - 系统指标 - CPU: 66.9%, 内存: 66.8%, 磁盘: 95.1%
2025-07-25 10:09:18,007 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.4%, CPU使用率 64.3%
2025-07-25 10:09:27,491 - alert_manager - WARNING - 触发告警: disk_free_space, 当前值: 0.0, 阈值: 1024
2025-07-25 10:09:33,117 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.6%, CPU使用率 68.0%
2025-07-25 10:09:33,720 - health_monitor - DEBUG - 系统指标 - CPU: 68.8%, 内存: 65.6%, 磁盘: 95.1%
2025-07-25 10:09:48,223 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.8%, CPU使用率 66.7%
2025-07-25 10:10:01,496 - alert_manager - WARNING - 触发告警: disk_usage, 当前值: 95.1, 阈值: 90
2025-07-25 10:10:03,330 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.1%, CPU使用率 50.0%
2025-07-25 10:10:08,695 - health_monitor - DEBUG - 系统指标 - CPU: 66.7%, 内存: 66.0%, 磁盘: 95.1%
2025-07-25 10:10:18,435 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.0%, CPU使用率 83.3%
2025-07-25 10:10:27,493 - alert_manager - WARNING - 触发告警: disk_usage, 当前值: 95.1, 阈值: 90
2025-07-25 10:10:33,543 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.0%, CPU使用率 62.5%
2025-07-25 10:10:34,744 - health_monitor - DEBUG - 系统指标 - CPU: 67.8%, 内存: 66.0%, 磁盘: 95.1%
2025-07-25 10:10:48,648 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.1%, CPU使用率 88.9%
2025-07-25 10:11:03,777 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.3%, CPU使用率 100.0%
2025-07-25 10:11:09,717 - health_monitor - DEBUG - 系统指标 - CPU: 68.0%, 内存: 66.3%, 磁盘: 95.1%
2025-07-25 10:11:18,884 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 66.7%
2025-07-25 10:11:33,994 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.7%, CPU使用率 85.7%
2025-07-25 10:11:35,769 - health_monitor - DEBUG - 系统指标 - CPU: 70.8%, 内存: 66.7%, 磁盘: 95.1%
2025-07-25 10:11:49,107 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 70.8%
2025-07-25 10:12:04,213 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 58.3%
2025-07-25 10:12:10,794 - health_monitor - DEBUG - 系统指标 - CPU: 63.0%, 内存: 66.7%, 磁盘: 95.1%
2025-07-25 10:12:19,320 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.7%, CPU使用率 57.7%
2025-07-25 10:12:34,427 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.7%, CPU使用率 53.6%
2025-07-25 10:12:36,792 - health_monitor - DEBUG - 系统指标 - CPU: 70.2%, 内存: 66.7%, 磁盘: 95.1%
2025-07-25 10:12:49,532 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.7%, CPU使用率 77.8%
2025-07-25 10:13:04,638 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.7%, CPU使用率 50.0%
2025-07-25 10:13:11,820 - health_monitor - DEBUG - 系统指标 - CPU: 60.7%, 内存: 66.8%, 磁盘: 95.1%
2025-07-25 10:13:19,743 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.7%, CPU使用率 67.9%
2025-07-25 10:13:34,850 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 50.0%
2025-07-25 10:13:37,824 - health_monitor - DEBUG - 系统指标 - CPU: 69.6%, 内存: 66.7%, 磁盘: 95.1%
2025-07-25 10:13:49,955 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 74.1%
2025-07-25 10:14:05,063 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 53.6%
2025-07-25 10:14:12,844 - health_monitor - DEBUG - 系统指标 - CPU: 68.1%, 内存: 66.7%, 磁盘: 95.1%
2025-07-25 10:14:20,168 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.7%, CPU使用率 45.8%
2025-07-25 10:14:35,274 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.8%, CPU使用率 62.5%
2025-07-25 10:14:38,847 - health_monitor - DEBUG - 系统指标 - CPU: 59.8%, 内存: 66.8%, 磁盘: 95.1%
2025-07-25 10:14:50,381 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.7%, CPU使用率 67.9%
2025-07-25 10:15:05,488 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 75.0%
2025-07-25 10:15:13,869 - health_monitor - DEBUG - 系统指标 - CPU: 65.4%, 内存: 66.7%, 磁盘: 95.1%
2025-07-25 10:15:20,594 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 66.7%
2025-07-25 10:15:35,703 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.7%, CPU使用率 57.1%
2025-07-25 10:15:39,879 - health_monitor - DEBUG - 系统指标 - CPU: 43.8%, 内存: 66.7%, 磁盘: 95.1%
2025-07-25 10:15:50,811 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 29.2%
2025-07-25 10:16:05,917 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.7%, CPU使用率 70.8%
2025-07-25 10:16:14,894 - health_monitor - DEBUG - 系统指标 - CPU: 52.7%, 内存: 66.8%, 磁盘: 95.1%
2025-07-25 10:16:21,025 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.9%, CPU使用率 66.7%
2025-07-25 10:16:36,131 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.7%, CPU使用率 31.0%
2025-07-25 10:16:40,903 - health_monitor - DEBUG - 系统指标 - CPU: 42.6%, 内存: 66.7%, 磁盘: 95.1%
2025-07-25 10:16:51,236 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.7%, CPU使用率 42.3%
2025-07-25 10:17:06,346 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.7%, CPU使用率 71.4%
2025-07-25 10:17:15,927 - health_monitor - DEBUG - 系统指标 - CPU: 76.7%, 内存: 67.7%, 磁盘: 95.1%
2025-07-25 10:17:21,452 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.8%, CPU使用率 100.0%
2025-07-25 10:17:36,565 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.6%, CPU使用率 35.7%
2025-07-25 10:17:41,959 - health_monitor - DEBUG - 系统指标 - CPU: 82.6%, 内存: 67.6%, 磁盘: 95.1%
2025-07-25 10:17:51,725 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.7%, CPU使用率 100.0%
2025-07-25 17:13:56,886 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-25 17:13:56,890 - auth_service - INFO - 统一认证服务初始化完成
2025-07-25 17:13:56,994 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-25 17:13:56,996 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-25 17:13:57,415 - BackendMockDataManager - INFO - 后端模拟数据模式已启用
2025-07-25 17:13:59,020 - DataExportService - DEBUG - 已注册表模型: users -> User
2025-07-25 17:13:59,021 - DataExportService - DEBUG - 已注册表模型: health_records -> HealthRecord
2025-07-25 17:13:59,022 - DataExportService - DEBUG - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-25 17:13:59,022 - DataExportService - DEBUG - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-25 17:13:59,024 - DataExportService - DEBUG - 已注册表模型: assessment_results -> AssessmentResult
2025-07-25 17:13:59,025 - DataExportService - DEBUG - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-25 17:13:59,026 - DataExportService - DEBUG - 已注册表模型: health_diaries -> HealthDiary
2025-07-25 17:13:59,027 - DataExportService - DEBUG - 已注册表模型: medical_records -> MedicalRecord
2025-07-25 17:13:59,028 - DataExportService - DEBUG - 已注册表模型: lab_reports -> LabReport
2025-07-25 17:13:59,028 - DataExportService - DEBUG - 已注册表模型: examination_reports -> ExaminationReport
2025-07-25 17:13:59,029 - DataExportService - DEBUG - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-25 17:13:59,030 - DataExportService - DEBUG - 已注册表模型: medications -> Medication
2025-07-25 17:13:59,031 - DataExportService - DEBUG - 已注册表模型: imaging_reports -> ImagingReport
2025-07-25 17:13:59,032 - DataExportService - DEBUG - 已注册表模型: other_records -> OtherRecord
2025-07-25 17:13:59,033 - DataExportService - DEBUG - 已注册表模型: user_health_records -> HealthRecord
2025-07-25 17:13:59,034 - DataExportService - DEBUG - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-25 17:13:59,035 - DataExportService - DEBUG - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-25 17:13:59,036 - DataExportService - DEBUG - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-25 17:13:59,037 - DataExportService - DEBUG - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-25 17:13:59,037 - DataExportService - INFO - 已初始化 19 个表模型
2025-07-25 17:13:59,040 - DataExportService - DEBUG - 已注册表模型: users -> User
2025-07-25 17:13:59,041 - DataExportService - DEBUG - 已注册表模型: health_records -> HealthRecord
2025-07-25 17:13:59,042 - DataExportService - DEBUG - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-25 17:13:59,044 - DataExportService - DEBUG - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-25 17:13:59,047 - DataExportService - DEBUG - 已注册表模型: assessment_results -> AssessmentResult
2025-07-25 17:13:59,048 - DataExportService - DEBUG - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-25 17:13:59,049 - DataExportService - DEBUG - 已注册表模型: health_diaries -> HealthDiary
2025-07-25 17:13:59,050 - DataExportService - DEBUG - 已注册表模型: medical_records -> MedicalRecord
2025-07-25 17:13:59,051 - DataExportService - DEBUG - 已注册表模型: lab_reports -> LabReport
2025-07-25 17:13:59,052 - DataExportService - DEBUG - 已注册表模型: examination_reports -> ExaminationReport
2025-07-25 17:13:59,053 - DataExportService - DEBUG - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-25 17:13:59,053 - DataExportService - DEBUG - 已注册表模型: medications -> Medication
2025-07-25 17:13:59,054 - DataExportService - DEBUG - 已注册表模型: imaging_reports -> ImagingReport
2025-07-25 17:13:59,055 - DataExportService - DEBUG - 已注册表模型: other_records -> OtherRecord
2025-07-25 17:13:59,056 - DataExportService - DEBUG - 已注册表模型: user_health_records -> HealthRecord
2025-07-25 17:13:59,059 - DataExportService - DEBUG - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-25 17:13:59,060 - DataExportService - DEBUG - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-25 17:13:59,061 - DataExportService - DEBUG - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-25 17:13:59,061 - DataExportService - DEBUG - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-25 17:13:59,062 - DataExportService - INFO - 已初始化 19 个表模型
2025-07-25 17:13:59,064 - DataExportService - DEBUG - 已注册表模型: users -> User
2025-07-25 17:13:59,064 - DataExportService - DEBUG - 已注册表模型: health_records -> HealthRecord
2025-07-25 17:13:59,065 - DataExportService - DEBUG - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-25 17:13:59,066 - DataExportService - DEBUG - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-25 17:13:59,067 - DataExportService - DEBUG - 已注册表模型: assessment_results -> AssessmentResult
2025-07-25 17:13:59,067 - DataExportService - DEBUG - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-25 17:13:59,068 - DataExportService - DEBUG - 已注册表模型: health_diaries -> HealthDiary
2025-07-25 17:13:59,069 - DataExportService - DEBUG - 已注册表模型: medical_records -> MedicalRecord
2025-07-25 17:13:59,069 - DataExportService - DEBUG - 已注册表模型: lab_reports -> LabReport
2025-07-25 17:13:59,070 - DataExportService - DEBUG - 已注册表模型: examination_reports -> ExaminationReport
2025-07-25 17:13:59,071 - DataExportService - DEBUG - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-25 17:13:59,072 - DataExportService - DEBUG - 已注册表模型: medications -> Medication
2025-07-25 17:13:59,075 - DataExportService - DEBUG - 已注册表模型: imaging_reports -> ImagingReport
2025-07-25 17:13:59,078 - DataExportService - DEBUG - 已注册表模型: other_records -> OtherRecord
2025-07-25 17:13:59,082 - DataExportService - DEBUG - 已注册表模型: user_health_records -> HealthRecord
2025-07-25 17:13:59,083 - DataExportService - DEBUG - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-25 17:13:59,086 - DataExportService - DEBUG - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-25 17:13:59,087 - DataExportService - DEBUG - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-25 17:13:59,089 - DataExportService - DEBUG - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-25 17:13:59,094 - DataExportService - INFO - 已初始化 19 个表模型
2025-07-25 17:13:59,096 - DataExportService - DEBUG - 已注册表模型: users -> User
2025-07-25 17:13:59,097 - DataExportService - DEBUG - 已注册表模型: health_records -> HealthRecord
2025-07-25 17:13:59,098 - DataExportService - DEBUG - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-25 17:13:59,099 - DataExportService - DEBUG - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-25 17:13:59,100 - DataExportService - DEBUG - 已注册表模型: assessment_results -> AssessmentResult
2025-07-25 17:13:59,101 - DataExportService - DEBUG - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-25 17:13:59,102 - DataExportService - DEBUG - 已注册表模型: health_diaries -> HealthDiary
2025-07-25 17:13:59,102 - DataExportService - DEBUG - 已注册表模型: medical_records -> MedicalRecord
2025-07-25 17:13:59,103 - DataExportService - DEBUG - 已注册表模型: lab_reports -> LabReport
2025-07-25 17:13:59,104 - DataExportService - DEBUG - 已注册表模型: examination_reports -> ExaminationReport
2025-07-25 17:13:59,104 - DataExportService - DEBUG - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-25 17:13:59,105 - DataExportService - DEBUG - 已注册表模型: medications -> Medication
2025-07-25 17:13:59,107 - DataExportService - DEBUG - 已注册表模型: imaging_reports -> ImagingReport
2025-07-25 17:13:59,108 - DataExportService - DEBUG - 已注册表模型: other_records -> OtherRecord
2025-07-25 17:13:59,109 - DataExportService - DEBUG - 已注册表模型: user_health_records -> HealthRecord
2025-07-25 17:13:59,112 - DataExportService - DEBUG - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-25 17:13:59,114 - DataExportService - DEBUG - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-25 17:13:59,115 - DataExportService - DEBUG - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-25 17:13:59,116 - DataExportService - DEBUG - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-25 17:13:59,117 - DataExportService - INFO - 已初始化 19 个表模型
2025-07-25 17:13:59,185 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-07-25 17:13:59,186 - fallback_manager - DEBUG - 当前Python路径: ['c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\Pythonwin', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-07-25 17:13:59,196 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-07-25 17:13:59,278 - health_monitor - INFO - 健康监控器初始化完成
2025-07-25 17:13:59,280 - app.core.system_monitor - INFO - 已加载 16 个历史数据点
2025-07-25 17:13:59,283 - app.core.alert_detector - INFO - 已加载 6 个告警规则
2025-07-25 17:13:59,288 - app.core.alert_detector - INFO - 已加载 0 个当前告警
2025-07-25 17:13:59,292 - app.core.alert_detector - INFO - 已加载 3 个历史告警
2025-07-25 17:13:59,294 - app.core.alert_detector - INFO - 已加载 1 个通知渠道
2025-07-25 17:13:59,295 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-25 17:13:59,304 - alert_manager - INFO - 已初始化默认告警规则
2025-07-25 17:13:59,308 - alert_manager - INFO - 已初始化默认通知渠道
2025-07-25 17:13:59,313 - alert_manager - INFO - 告警管理器初始化完成
2025-07-25 17:14:00,046 - db_service - INFO - 数据库引擎和会话工厂创建成功
2025-07-25 17:14:00,046 - db_service - INFO - 数据库服务初始化完成
2025-07-25 17:14:00,051 - notification_service - INFO - 通知服务初始化完成
2025-07-25 17:14:00,053 - main - INFO - 错误处理模块导入成功
2025-07-25 17:14:00,097 - main - INFO - 监控模块导入成功
2025-07-25 17:14:00,098 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-07-25 17:14:06,717 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-25 17:14:06,722 - auth_service - INFO - 统一认证服务初始化完成
2025-07-25 17:14:06,857 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-25 17:14:06,858 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-25 17:14:07,307 - BackendMockDataManager - INFO - 后端模拟数据模式已启用
2025-07-25 17:14:09,171 - DataExportService - DEBUG - 已注册表模型: users -> User
2025-07-25 17:14:09,173 - DataExportService - DEBUG - 已注册表模型: health_records -> HealthRecord
2025-07-25 17:14:09,174 - DataExportService - DEBUG - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-25 17:14:09,175 - DataExportService - DEBUG - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-25 17:14:09,176 - DataExportService - DEBUG - 已注册表模型: assessment_results -> AssessmentResult
2025-07-25 17:14:09,177 - DataExportService - DEBUG - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-25 17:14:09,179 - DataExportService - DEBUG - 已注册表模型: health_diaries -> HealthDiary
2025-07-25 17:14:09,180 - DataExportService - DEBUG - 已注册表模型: medical_records -> MedicalRecord
2025-07-25 17:14:09,181 - DataExportService - DEBUG - 已注册表模型: lab_reports -> LabReport
2025-07-25 17:14:09,182 - DataExportService - DEBUG - 已注册表模型: examination_reports -> ExaminationReport
2025-07-25 17:14:09,183 - DataExportService - DEBUG - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-25 17:14:09,184 - DataExportService - DEBUG - 已注册表模型: medications -> Medication
2025-07-25 17:14:09,185 - DataExportService - DEBUG - 已注册表模型: imaging_reports -> ImagingReport
2025-07-25 17:14:09,186 - DataExportService - DEBUG - 已注册表模型: other_records -> OtherRecord
2025-07-25 17:14:09,187 - DataExportService - DEBUG - 已注册表模型: user_health_records -> HealthRecord
2025-07-25 17:14:09,189 - DataExportService - DEBUG - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-25 17:14:09,190 - DataExportService - DEBUG - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-25 17:14:09,191 - DataExportService - DEBUG - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-25 17:14:09,192 - DataExportService - DEBUG - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-25 17:14:09,194 - DataExportService - INFO - 已初始化 19 个表模型
2025-07-25 17:14:09,197 - DataExportService - DEBUG - 已注册表模型: users -> User
2025-07-25 17:14:09,199 - DataExportService - DEBUG - 已注册表模型: health_records -> HealthRecord
2025-07-25 17:14:09,202 - DataExportService - DEBUG - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-25 17:14:09,204 - DataExportService - DEBUG - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-25 17:14:09,208 - DataExportService - DEBUG - 已注册表模型: assessment_results -> AssessmentResult
2025-07-25 17:14:09,209 - DataExportService - DEBUG - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-25 17:14:09,210 - DataExportService - DEBUG - 已注册表模型: health_diaries -> HealthDiary
2025-07-25 17:14:09,212 - DataExportService - DEBUG - 已注册表模型: medical_records -> MedicalRecord
2025-07-25 17:14:09,213 - DataExportService - DEBUG - 已注册表模型: lab_reports -> LabReport
2025-07-25 17:14:09,214 - DataExportService - DEBUG - 已注册表模型: examination_reports -> ExaminationReport
2025-07-25 17:14:09,215 - DataExportService - DEBUG - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-25 17:14:09,216 - DataExportService - DEBUG - 已注册表模型: medications -> Medication
2025-07-25 17:14:09,217 - DataExportService - DEBUG - 已注册表模型: imaging_reports -> ImagingReport
2025-07-25 17:14:09,218 - DataExportService - DEBUG - 已注册表模型: other_records -> OtherRecord
2025-07-25 17:14:09,219 - DataExportService - DEBUG - 已注册表模型: user_health_records -> HealthRecord
2025-07-25 17:14:09,220 - DataExportService - DEBUG - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-25 17:14:09,222 - DataExportService - DEBUG - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-25 17:14:09,224 - DataExportService - DEBUG - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-25 17:14:09,225 - DataExportService - DEBUG - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-25 17:14:09,233 - DataExportService - INFO - 已初始化 19 个表模型
2025-07-25 17:14:09,237 - DataExportService - DEBUG - 已注册表模型: users -> User
2025-07-25 17:14:09,246 - DataExportService - DEBUG - 已注册表模型: health_records -> HealthRecord
2025-07-25 17:14:09,247 - DataExportService - DEBUG - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-25 17:14:09,248 - DataExportService - DEBUG - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-25 17:14:09,249 - DataExportService - DEBUG - 已注册表模型: assessment_results -> AssessmentResult
2025-07-25 17:14:09,250 - DataExportService - DEBUG - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-25 17:14:09,252 - DataExportService - DEBUG - 已注册表模型: health_diaries -> HealthDiary
2025-07-25 17:14:09,253 - DataExportService - DEBUG - 已注册表模型: medical_records -> MedicalRecord
2025-07-25 17:14:09,259 - DataExportService - DEBUG - 已注册表模型: lab_reports -> LabReport
2025-07-25 17:14:09,262 - DataExportService - DEBUG - 已注册表模型: examination_reports -> ExaminationReport
2025-07-25 17:14:09,263 - DataExportService - DEBUG - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-25 17:14:09,264 - DataExportService - DEBUG - 已注册表模型: medications -> Medication
2025-07-25 17:14:09,265 - DataExportService - DEBUG - 已注册表模型: imaging_reports -> ImagingReport
2025-07-25 17:14:09,267 - DataExportService - DEBUG - 已注册表模型: other_records -> OtherRecord
2025-07-25 17:14:09,268 - DataExportService - DEBUG - 已注册表模型: user_health_records -> HealthRecord
2025-07-25 17:14:09,269 - DataExportService - DEBUG - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-25 17:14:09,271 - DataExportService - DEBUG - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-25 17:14:09,280 - DataExportService - DEBUG - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-25 17:14:09,281 - DataExportService - DEBUG - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-25 17:14:09,282 - DataExportService - INFO - 已初始化 19 个表模型
2025-07-25 17:14:09,285 - DataExportService - DEBUG - 已注册表模型: users -> User
2025-07-25 17:14:09,286 - DataExportService - DEBUG - 已注册表模型: health_records -> HealthRecord
2025-07-25 17:14:09,287 - DataExportService - DEBUG - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-25 17:14:09,289 - DataExportService - DEBUG - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-25 17:14:09,291 - DataExportService - DEBUG - 已注册表模型: assessment_results -> AssessmentResult
2025-07-25 17:14:09,293 - DataExportService - DEBUG - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-25 17:14:09,294 - DataExportService - DEBUG - 已注册表模型: health_diaries -> HealthDiary
2025-07-25 17:14:09,296 - DataExportService - DEBUG - 已注册表模型: medical_records -> MedicalRecord
2025-07-25 17:14:09,297 - DataExportService - DEBUG - 已注册表模型: lab_reports -> LabReport
2025-07-25 17:14:09,299 - DataExportService - DEBUG - 已注册表模型: examination_reports -> ExaminationReport
2025-07-25 17:14:09,300 - DataExportService - DEBUG - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-25 17:14:09,302 - DataExportService - DEBUG - 已注册表模型: medications -> Medication
2025-07-25 17:14:09,303 - DataExportService - DEBUG - 已注册表模型: imaging_reports -> ImagingReport
2025-07-25 17:14:09,309 - DataExportService - DEBUG - 已注册表模型: other_records -> OtherRecord
2025-07-25 17:14:09,310 - DataExportService - DEBUG - 已注册表模型: user_health_records -> HealthRecord
2025-07-25 17:14:09,315 - DataExportService - DEBUG - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-25 17:14:09,317 - DataExportService - DEBUG - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-25 17:14:09,319 - DataExportService - DEBUG - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-25 17:14:09,320 - DataExportService - DEBUG - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-25 17:14:09,323 - DataExportService - INFO - 已初始化 19 个表模型
2025-07-25 17:14:09,408 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-07-25 17:14:09,410 - fallback_manager - DEBUG - 当前Python路径: ['c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\Pythonwin', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-07-25 17:14:09,416 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-07-25 17:14:09,505 - health_monitor - INFO - 健康监控器初始化完成
2025-07-25 17:14:09,508 - app.core.system_monitor - INFO - 已加载 16 个历史数据点
2025-07-25 17:14:09,511 - app.core.alert_detector - INFO - 已加载 6 个告警规则
2025-07-25 17:14:09,513 - app.core.alert_detector - INFO - 已加载 0 个当前告警
2025-07-25 17:14:09,514 - app.core.alert_detector - INFO - 已加载 3 个历史告警
2025-07-25 17:14:09,515 - app.core.alert_detector - INFO - 已加载 1 个通知渠道
2025-07-25 17:14:09,517 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-25 17:14:09,524 - alert_manager - INFO - 已初始化默认告警规则
2025-07-25 17:14:09,526 - alert_manager - INFO - 已初始化默认通知渠道
2025-07-25 17:14:09,527 - alert_manager - INFO - 告警管理器初始化完成
2025-07-25 17:14:10,487 - db_service - INFO - 数据库引擎和会话工厂创建成功
2025-07-25 17:14:10,490 - db_service - INFO - 数据库服务初始化完成
2025-07-25 17:14:10,496 - notification_service - INFO - 通知服务初始化完成
2025-07-25 17:14:10,497 - main - INFO - 错误处理模块导入成功
2025-07-25 17:14:10,556 - main - INFO - 监控模块导入成功
2025-07-25 17:14:10,562 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-07-25 17:14:13,681 - asyncio - DEBUG - Using selector: SelectSelector
2025-07-25 17:14:14,038 - main - INFO - 错误处理模块导入成功
2025-07-25 17:14:14,094 - main - INFO - 监控模块导入成功
2025-07-25 17:14:14,166 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-07-25 17:14:15,461 - main - INFO - 应用启动中...
2025-07-25 17:14:15,462 - error_handling - INFO - 错误处理已设置
2025-07-25 17:14:15,464 - main - INFO - 错误处理系统初始化完成
2025-07-25 17:14:15,465 - monitoring - INFO - 添加指标端点成功: /metrics
2025-07-25 17:14:15,482 - monitoring - INFO - 添加健康检查端点成功: /health
2025-07-25 17:14:15,484 - monitoring - INFO - 添加详细健康检查端点成功: /api/health/detailed
2025-07-25 17:14:15,486 - monitoring - INFO - 系统信息初始化完成: Markey, Windows-10-10.0.19045-SP0, Python 3.13.2, CPU核心数: 4
2025-07-25 17:14:15,499 - monitoring - INFO - 启动资源监控线程成功
2025-07-25 17:14:15,500 - monitoring - INFO - 监控系统初始化成功（不使用中间件）
2025-07-25 17:14:15,502 - monitoring - INFO - 监控系统初始化完成
2025-07-25 17:14:15,503 - main - INFO - 监控系统初始化完成
2025-07-25 17:14:15,508 - app.db.init_db - INFO - 所有模型导入成功
2025-07-25 17:14:15,510 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-07-25 17:14:15,517 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-25 17:14:15,519 - app.db.init_db - INFO - 所有模型导入成功
2025-07-25 17:14:15,525 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-07-25 17:14:15,527 - app.db.init_db - INFO - 正在运行数据库迁移...
2025-07-25 17:14:15,529 - app.db.init_db - INFO - 正在检查并更新数据库表结构...
2025-07-25 17:14:15,531 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-25 17:14:15,533 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alerts")
2025-07-25 17:14:15,535 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,542 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_rules")
2025-07-25 17:14:15,545 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,548 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_channels")
2025-07-25 17:14:15,549 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,552 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-25 17:14:15,558 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,562 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_records")
2025-07-25 17:14:15,565 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,567 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_overviews")
2025-07-25 17:14:15,570 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,576 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medical_records")
2025-07-25 17:14:15,579 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,582 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("inpatient_records")
2025-07-25 17:14:15,584 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,590 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("surgery_records")
2025-07-25 17:14:15,600 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.9%, CPU使用率 89.3%
2025-07-25 17:14:15,601 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,610 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_reports")
2025-07-25 17:14:15,615 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,617 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_report_items")
2025-07-25 17:14:15,620 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,634 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_reports")
2025-07-25 17:14:15,638 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,641 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("examination_reports")
2025-07-25 17:14:15,645 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,648 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("documents")
2025-07-25 17:14:15,651 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,655 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("other_records")
2025-07-25 17:14:15,659 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,662 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("operation_logs")
2025-07-25 17:14:15,664 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,668 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("follow_up_records")
2025-07-25 17:14:15,677 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,681 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_diaries")
2025-07-25 17:14:15,685 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,691 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("registration_records")
2025-07-25 17:14:15,695 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,699 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("prescription_records")
2025-07-25 17:14:15,703 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,708 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("laboratory_records")
2025-07-25 17:14:15,717 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,720 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_records")
2025-07-25 17:14:15,731 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,734 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medications")
2025-07-25 17:14:15,739 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,745 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medication_usages")
2025-07-25 17:14:15,749 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,752 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("role_applications")
2025-07-25 17:14:15,762 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,765 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessments")
2025-07-25 17:14:15,770 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,776 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_items")
2025-07-25 17:14:15,781 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,792 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_responses")
2025-07-25 17:14:15,795 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,800 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_templates")
2025-07-25 17:14:15,802 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,806 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_template_questions")
2025-07-25 17:14:15,809 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,813 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaires")
2025-07-25 17:14:15,815 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,817 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_items")
2025-07-25 17:14:15,819 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,823 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_responses")
2025-07-25 17:14:15,825 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,830 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_answers")
2025-07-25 17:14:15,834 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,836 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_templates")
2025-07-25 17:14:15,841 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,844 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_template_questions")
2025-07-25 17:14:15,846 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,849 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_results")
2025-07-25 17:14:15,851 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,853 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_results")
2025-07-25 17:14:15,859 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,863 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("report_templates")
2025-07-25 17:14:15,866 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,868 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_distributions")
2025-07-25 17:14:15,870 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,873 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_distributions")
2025-07-25 17:14:15,877 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,879 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("service_stats")
2025-07-25 17:14:15,882 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:14:15,886 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-25 17:14:15,891 - app.db.init_db - INFO - 数据库表结构检查和更新完成
2025-07-25 17:14:15,899 - app.db.init_db - INFO - 模型关系初始化完成
2025-07-25 17:14:15,901 - app.db.init_db - INFO - 模型关系设置完成
2025-07-25 17:14:15,902 - main - INFO - 数据库初始化完成（强制重建）
2025-07-25 17:14:15,905 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-25 17:14:15,907 - main - INFO - 数据库连接正常
2025-07-25 17:14:15,909 - main - INFO - 开始初始化模板数据
2025-07-25 17:14:15,911 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-25 17:14:16,484 - app.db.init_templates - INFO - 开始初始化评估量表模板...
2025-07-25 17:14:16,550 - app.db.init_templates - INFO - 成功初始化 5 个评估量表模板
2025-07-25 17:14:16,597 - app.db.init_templates - INFO - 开始初始化调查问卷模板...
2025-07-25 17:14:16,695 - app.db.init_templates - INFO - 成功初始化 5 个调查问卷模板
2025-07-25 17:14:16,698 - main - INFO - 模板数据初始化完成
2025-07-25 17:14:16,700 - main - INFO - 数据库表结构已经标准化，不需要迁移
2025-07-25 17:14:16,702 - main - INFO - 应用启动完成
2025-07-25 17:14:30,713 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.8%, CPU使用率 56.0%
2025-07-25 17:14:45,893 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.4%, CPU使用率 100.0%
2025-07-25 17:15:00,229 - health_monitor - DEBUG - 系统指标 - CPU: 88.3%, 内存: 75.5%, 磁盘: 94.6%
2025-07-25 17:15:01,013 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.6%, CPU使用率 79.2%
2025-07-25 17:15:10,440 - health_monitor - DEBUG - 系统指标 - CPU: 63.2%, 内存: 75.6%, 磁盘: 94.6%
2025-07-25 17:15:16,145 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.7%, CPU使用率 70.0%
2025-07-25 17:15:31,415 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.1%, CPU使用率 100.0%
2025-07-25 17:15:46,522 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.9%, CPU使用率 39.3%
2025-07-25 17:16:01,397 - health_monitor - DEBUG - 系统指标 - CPU: 80.1%, 内存: 75.5%, 磁盘: 94.6%
2025-07-25 17:16:01,697 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.7%, CPU使用率 96.7%
2025-07-25 17:16:11,561 - health_monitor - DEBUG - 系统指标 - CPU: 87.5%, 内存: 75.0%, 磁盘: 94.6%
2025-07-25 17:16:16,848 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.9%, CPU使用率 66.7%
2025-07-25 17:16:31,957 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.1%, CPU使用率 60.7%
2025-07-25 17:16:47,094 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.5%, CPU使用率 93.5%
2025-07-25 17:17:02,329 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.9%, CPU使用率 97.8%
2025-07-25 17:17:02,437 - health_monitor - DEBUG - 系统指标 - CPU: 87.9%, 内存: 75.8%, 磁盘: 94.6%
2025-07-25 17:17:13,061 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 75.6%, 磁盘: 94.6%
2025-07-25 17:17:17,501 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.8%, CPU使用率 16.0%
2025-07-25 17:17:26,882 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-25 17:17:26,888 - auth_service - INFO - 统一认证服务初始化完成
2025-07-25 17:17:27,074 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-25 17:17:27,126 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-25 17:17:28,138 - BackendMockDataManager - INFO - 后端模拟数据模式已启用
2025-07-25 17:17:31,138 - DataExportService - DEBUG - 已注册表模型: users -> User
2025-07-25 17:17:31,140 - DataExportService - DEBUG - 已注册表模型: health_records -> HealthRecord
2025-07-25 17:17:31,141 - DataExportService - DEBUG - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-25 17:17:31,142 - DataExportService - DEBUG - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-25 17:17:31,144 - DataExportService - DEBUG - 已注册表模型: assessment_results -> AssessmentResult
2025-07-25 17:17:31,149 - DataExportService - DEBUG - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-25 17:17:31,151 - DataExportService - DEBUG - 已注册表模型: health_diaries -> HealthDiary
2025-07-25 17:17:31,152 - DataExportService - DEBUG - 已注册表模型: medical_records -> MedicalRecord
2025-07-25 17:17:31,154 - DataExportService - DEBUG - 已注册表模型: lab_reports -> LabReport
2025-07-25 17:17:31,156 - DataExportService - DEBUG - 已注册表模型: examination_reports -> ExaminationReport
2025-07-25 17:17:31,158 - DataExportService - DEBUG - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-25 17:17:31,159 - DataExportService - DEBUG - 已注册表模型: medications -> Medication
2025-07-25 17:17:31,161 - DataExportService - DEBUG - 已注册表模型: imaging_reports -> ImagingReport
2025-07-25 17:17:31,164 - DataExportService - DEBUG - 已注册表模型: other_records -> OtherRecord
2025-07-25 17:17:31,169 - DataExportService - DEBUG - 已注册表模型: user_health_records -> HealthRecord
2025-07-25 17:17:31,173 - DataExportService - DEBUG - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-25 17:17:31,174 - DataExportService - DEBUG - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-25 17:17:31,175 - DataExportService - DEBUG - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-25 17:17:31,177 - DataExportService - DEBUG - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-25 17:17:31,184 - DataExportService - INFO - 已初始化 19 个表模型
2025-07-25 17:17:31,187 - DataExportService - DEBUG - 已注册表模型: users -> User
2025-07-25 17:17:31,190 - DataExportService - DEBUG - 已注册表模型: health_records -> HealthRecord
2025-07-25 17:17:31,191 - DataExportService - DEBUG - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-25 17:17:31,192 - DataExportService - DEBUG - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-25 17:17:31,193 - DataExportService - DEBUG - 已注册表模型: assessment_results -> AssessmentResult
2025-07-25 17:17:31,198 - DataExportService - DEBUG - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-25 17:17:31,200 - DataExportService - DEBUG - 已注册表模型: health_diaries -> HealthDiary
2025-07-25 17:17:31,203 - DataExportService - DEBUG - 已注册表模型: medical_records -> MedicalRecord
2025-07-25 17:17:31,204 - DataExportService - DEBUG - 已注册表模型: lab_reports -> LabReport
2025-07-25 17:17:31,206 - DataExportService - DEBUG - 已注册表模型: examination_reports -> ExaminationReport
2025-07-25 17:17:31,207 - DataExportService - DEBUG - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-25 17:17:31,208 - DataExportService - DEBUG - 已注册表模型: medications -> Medication
2025-07-25 17:17:31,210 - DataExportService - DEBUG - 已注册表模型: imaging_reports -> ImagingReport
2025-07-25 17:17:31,211 - DataExportService - DEBUG - 已注册表模型: other_records -> OtherRecord
2025-07-25 17:17:31,217 - DataExportService - DEBUG - 已注册表模型: user_health_records -> HealthRecord
2025-07-25 17:17:31,219 - DataExportService - DEBUG - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-25 17:17:31,221 - DataExportService - DEBUG - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-25 17:17:31,222 - DataExportService - DEBUG - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-25 17:17:31,223 - DataExportService - DEBUG - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-25 17:17:31,225 - DataExportService - INFO - 已初始化 19 个表模型
2025-07-25 17:17:31,227 - DataExportService - DEBUG - 已注册表模型: users -> User
2025-07-25 17:17:31,231 - DataExportService - DEBUG - 已注册表模型: health_records -> HealthRecord
2025-07-25 17:17:31,235 - DataExportService - DEBUG - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-25 17:17:31,236 - DataExportService - DEBUG - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-25 17:17:31,238 - DataExportService - DEBUG - 已注册表模型: assessment_results -> AssessmentResult
2025-07-25 17:17:31,239 - DataExportService - DEBUG - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-25 17:17:31,240 - DataExportService - DEBUG - 已注册表模型: health_diaries -> HealthDiary
2025-07-25 17:17:31,241 - DataExportService - DEBUG - 已注册表模型: medical_records -> MedicalRecord
2025-07-25 17:17:31,243 - DataExportService - DEBUG - 已注册表模型: lab_reports -> LabReport
2025-07-25 17:17:31,248 - DataExportService - DEBUG - 已注册表模型: examination_reports -> ExaminationReport
2025-07-25 17:17:31,250 - DataExportService - DEBUG - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-25 17:17:31,252 - DataExportService - DEBUG - 已注册表模型: medications -> Medication
2025-07-25 17:17:31,254 - DataExportService - DEBUG - 已注册表模型: imaging_reports -> ImagingReport
2025-07-25 17:17:31,255 - DataExportService - DEBUG - 已注册表模型: other_records -> OtherRecord
2025-07-25 17:17:31,256 - DataExportService - DEBUG - 已注册表模型: user_health_records -> HealthRecord
2025-07-25 17:17:31,258 - DataExportService - DEBUG - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-25 17:17:31,260 - DataExportService - DEBUG - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-25 17:17:31,261 - DataExportService - DEBUG - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-25 17:17:31,266 - DataExportService - DEBUG - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-25 17:17:31,268 - DataExportService - INFO - 已初始化 19 个表模型
2025-07-25 17:17:31,271 - DataExportService - DEBUG - 已注册表模型: users -> User
2025-07-25 17:17:31,272 - DataExportService - DEBUG - 已注册表模型: health_records -> HealthRecord
2025-07-25 17:17:31,273 - DataExportService - DEBUG - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-25 17:17:31,274 - DataExportService - DEBUG - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-25 17:17:31,276 - DataExportService - DEBUG - 已注册表模型: assessment_results -> AssessmentResult
2025-07-25 17:17:31,277 - DataExportService - DEBUG - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-25 17:17:31,282 - DataExportService - DEBUG - 已注册表模型: health_diaries -> HealthDiary
2025-07-25 17:17:31,284 - DataExportService - DEBUG - 已注册表模型: medical_records -> MedicalRecord
2025-07-25 17:17:31,286 - DataExportService - DEBUG - 已注册表模型: lab_reports -> LabReport
2025-07-25 17:17:31,288 - DataExportService - DEBUG - 已注册表模型: examination_reports -> ExaminationReport
2025-07-25 17:17:31,289 - DataExportService - DEBUG - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-25 17:17:31,290 - DataExportService - DEBUG - 已注册表模型: medications -> Medication
2025-07-25 17:17:31,292 - DataExportService - DEBUG - 已注册表模型: imaging_reports -> ImagingReport
2025-07-25 17:17:31,294 - DataExportService - DEBUG - 已注册表模型: other_records -> OtherRecord
2025-07-25 17:17:31,298 - DataExportService - DEBUG - 已注册表模型: user_health_records -> HealthRecord
2025-07-25 17:17:31,299 - DataExportService - DEBUG - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-25 17:17:31,302 - DataExportService - DEBUG - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-25 17:17:31,304 - DataExportService - DEBUG - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-25 17:17:31,305 - DataExportService - DEBUG - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-25 17:17:31,306 - DataExportService - INFO - 已初始化 19 个表模型
2025-07-25 17:17:31,407 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-07-25 17:17:31,408 - fallback_manager - DEBUG - 当前Python路径: ['c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\Pythonwin', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-07-25 17:17:31,420 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-07-25 17:17:31,525 - health_monitor - INFO - 健康监控器初始化完成
2025-07-25 17:17:31,527 - app.core.system_monitor - INFO - 已加载 16 个历史数据点
2025-07-25 17:17:31,533 - app.core.alert_detector - INFO - 已加载 6 个告警规则
2025-07-25 17:17:31,534 - app.core.alert_detector - INFO - 已加载 0 个当前告警
2025-07-25 17:17:31,535 - app.core.alert_detector - INFO - 已加载 3 个历史告警
2025-07-25 17:17:31,536 - app.core.alert_detector - INFO - 已加载 1 个通知渠道
2025-07-25 17:17:31,538 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-25 17:17:31,544 - alert_manager - INFO - 已初始化默认告警规则
2025-07-25 17:17:31,548 - alert_manager - INFO - 已初始化默认通知渠道
2025-07-25 17:17:31,550 - alert_manager - INFO - 告警管理器初始化完成
2025-07-25 17:17:32,933 - db_service - INFO - 数据库引擎和会话工厂创建成功
2025-07-25 17:17:32,974 - db_service - INFO - 数据库服务初始化完成
2025-07-25 17:17:32,988 - notification_service - INFO - 通知服务初始化完成
2025-07-25 17:17:32,991 - main - INFO - 错误处理模块导入成功
2025-07-25 17:17:33,058 - main - INFO - 监控模块导入成功
2025-07-25 17:17:33,060 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-07-25 17:17:36,785 - asyncio - DEBUG - Using selector: SelectSelector
2025-07-25 17:17:37,059 - main - INFO - 错误处理模块导入成功
2025-07-25 17:17:37,072 - main - INFO - 监控模块导入成功
2025-07-25 17:17:37,076 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-07-25 17:17:39,183 - main - INFO - 应用启动中...
2025-07-25 17:17:39,209 - error_handling - INFO - 错误处理已设置
2025-07-25 17:17:39,232 - main - INFO - 错误处理系统初始化完成
2025-07-25 17:17:39,237 - monitoring - INFO - 添加指标端点成功: /metrics
2025-07-25 17:17:39,240 - monitoring - INFO - 添加健康检查端点成功: /health
2025-07-25 17:17:39,243 - monitoring - INFO - 添加详细健康检查端点成功: /api/health/detailed
2025-07-25 17:17:39,249 - monitoring - INFO - 系统信息初始化完成: Markey, Windows-10-10.0.19045-SP0, Python 3.13.2, CPU核心数: 4
2025-07-25 17:17:39,259 - monitoring - INFO - 启动资源监控线程成功
2025-07-25 17:17:39,264 - monitoring - INFO - 监控系统初始化成功（不使用中间件）
2025-07-25 17:17:39,268 - monitoring - INFO - 监控系统初始化完成
2025-07-25 17:17:39,269 - main - INFO - 监控系统初始化完成
2025-07-25 17:17:39,272 - app.db.init_db - INFO - 所有模型导入成功
2025-07-25 17:17:39,274 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-07-25 17:17:39,281 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-25 17:17:39,286 - app.db.init_db - INFO - 所有模型导入成功
2025-07-25 17:17:39,288 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-07-25 17:17:39,289 - app.db.init_db - INFO - 正在运行数据库迁移...
2025-07-25 17:17:39,293 - app.db.init_db - INFO - 正在检查并更新数据库表结构...
2025-07-25 17:17:39,300 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-25 17:17:39,305 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alerts")
2025-07-25 17:17:39,307 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,318 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_rules")
2025-07-25 17:17:39,321 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,323 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_channels")
2025-07-25 17:17:39,326 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,332 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-25 17:17:39,336 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,338 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_records")
2025-07-25 17:17:39,339 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,341 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_overviews")
2025-07-25 17:17:39,343 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,349 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medical_records")
2025-07-25 17:17:39,351 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,354 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("inpatient_records")
2025-07-25 17:17:39,355 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,357 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("surgery_records")
2025-07-25 17:17:39,359 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.5%, CPU使用率 96.4%
2025-07-25 17:17:39,359 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,365 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_reports")
2025-07-25 17:17:39,368 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,370 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_report_items")
2025-07-25 17:17:39,372 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,375 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_reports")
2025-07-25 17:17:39,382 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,387 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("examination_reports")
2025-07-25 17:17:39,389 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,391 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("documents")
2025-07-25 17:17:39,396 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,400 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("other_records")
2025-07-25 17:17:39,402 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,406 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("operation_logs")
2025-07-25 17:17:39,408 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,425 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("follow_up_records")
2025-07-25 17:17:39,439 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,453 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_diaries")
2025-07-25 17:17:39,456 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,459 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("registration_records")
2025-07-25 17:17:39,466 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,471 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("prescription_records")
2025-07-25 17:17:39,473 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,475 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("laboratory_records")
2025-07-25 17:17:39,503 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,507 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_records")
2025-07-25 17:17:39,519 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,521 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medications")
2025-07-25 17:17:39,526 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,538 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medication_usages")
2025-07-25 17:17:39,543 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,554 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("role_applications")
2025-07-25 17:17:39,567 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,571 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessments")
2025-07-25 17:17:39,576 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,586 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_items")
2025-07-25 17:17:39,592 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,607 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_responses")
2025-07-25 17:17:39,650 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,657 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_templates")
2025-07-25 17:17:39,702 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,708 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_template_questions")
2025-07-25 17:17:39,719 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,722 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaires")
2025-07-25 17:17:39,730 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,733 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_items")
2025-07-25 17:17:39,739 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,742 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_responses")
2025-07-25 17:17:39,756 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,759 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_answers")
2025-07-25 17:17:39,768 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,770 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_templates")
2025-07-25 17:17:39,772 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,774 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_template_questions")
2025-07-25 17:17:39,776 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,782 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_results")
2025-07-25 17:17:39,784 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,786 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_results")
2025-07-25 17:17:39,788 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,790 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("report_templates")
2025-07-25 17:17:39,792 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,798 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_distributions")
2025-07-25 17:17:39,801 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,803 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_distributions")
2025-07-25 17:17:39,805 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,806 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("service_stats")
2025-07-25 17:17:39,812 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:17:39,817 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-25 17:17:39,819 - app.db.init_db - INFO - 数据库表结构检查和更新完成
2025-07-25 17:17:39,823 - app.db.init_db - INFO - 模型关系初始化完成
2025-07-25 17:17:39,825 - app.db.init_db - INFO - 模型关系设置完成
2025-07-25 17:17:39,827 - main - INFO - 数据库初始化完成（强制重建）
2025-07-25 17:17:39,833 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-25 17:17:39,835 - main - INFO - 数据库连接正常
2025-07-25 17:17:39,836 - main - INFO - 开始初始化模板数据
2025-07-25 17:17:39,838 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-25 17:17:40,754 - app.db.init_templates - INFO - 开始初始化评估量表模板...
2025-07-25 17:17:40,938 - app.db.init_templates - INFO - 成功初始化 5 个评估量表模板
2025-07-25 17:17:40,974 - app.db.init_templates - INFO - 开始初始化调查问卷模板...
2025-07-25 17:17:41,072 - app.db.init_templates - INFO - 成功初始化 5 个调查问卷模板
2025-07-25 17:17:41,074 - main - INFO - 模板数据初始化完成
2025-07-25 17:17:41,076 - main - INFO - 数据库表结构已经标准化，不需要迁移
2025-07-25 17:17:41,079 - main - INFO - 应用启动完成
2025-07-25 17:17:54,474 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.2%, CPU使用率 96.2%
2025-07-25 17:18:03,496 - health_monitor - DEBUG - 系统指标 - CPU: 85.6%, 内存: 75.5%, 磁盘: 94.6%
2025-07-25 17:18:09,605 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.4%, CPU使用率 91.7%
2025-07-25 17:18:24,718 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.5%, CPU使用率 100.0%
2025-07-25 17:18:32,898 - health_monitor - DEBUG - 系统指标 - CPU: 99.3%, 内存: 74.9%, 磁盘: 94.6%
2025-07-25 17:18:40,046 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.4%, CPU使用率 98.2%
2025-07-25 17:18:53,424 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-25 17:18:53,526 - auth_service - INFO - 统一认证服务初始化完成
2025-07-25 17:18:53,761 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-25 17:18:53,781 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-25 17:18:54,470 - BackendMockDataManager - INFO - 后端模拟数据模式已启用
2025-07-25 17:18:57,423 - DataExportService - DEBUG - 已注册表模型: users -> User
2025-07-25 17:18:57,425 - DataExportService - DEBUG - 已注册表模型: health_records -> HealthRecord
2025-07-25 17:18:57,427 - DataExportService - DEBUG - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-25 17:18:57,428 - DataExportService - DEBUG - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-25 17:18:57,430 - DataExportService - DEBUG - 已注册表模型: assessment_results -> AssessmentResult
2025-07-25 17:18:57,431 - DataExportService - DEBUG - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-25 17:18:57,438 - DataExportService - DEBUG - 已注册表模型: health_diaries -> HealthDiary
2025-07-25 17:18:57,439 - DataExportService - DEBUG - 已注册表模型: medical_records -> MedicalRecord
2025-07-25 17:18:57,440 - DataExportService - DEBUG - 已注册表模型: lab_reports -> LabReport
2025-07-25 17:18:57,442 - DataExportService - DEBUG - 已注册表模型: examination_reports -> ExaminationReport
2025-07-25 17:18:57,444 - DataExportService - DEBUG - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-25 17:18:57,445 - DataExportService - DEBUG - 已注册表模型: medications -> Medication
2025-07-25 17:18:57,447 - DataExportService - DEBUG - 已注册表模型: imaging_reports -> ImagingReport
2025-07-25 17:18:57,450 - DataExportService - DEBUG - 已注册表模型: other_records -> OtherRecord
2025-07-25 17:18:57,453 - DataExportService - DEBUG - 已注册表模型: user_health_records -> HealthRecord
2025-07-25 17:18:57,455 - DataExportService - DEBUG - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-25 17:18:57,456 - DataExportService - DEBUG - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-25 17:18:57,458 - DataExportService - DEBUG - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-25 17:18:57,460 - DataExportService - DEBUG - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-25 17:18:57,461 - DataExportService - INFO - 已初始化 19 个表模型
2025-07-25 17:18:57,464 - DataExportService - DEBUG - 已注册表模型: users -> User
2025-07-25 17:18:57,471 - DataExportService - DEBUG - 已注册表模型: health_records -> HealthRecord
2025-07-25 17:18:57,472 - DataExportService - DEBUG - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-25 17:18:57,474 - DataExportService - DEBUG - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-25 17:18:57,475 - DataExportService - DEBUG - 已注册表模型: assessment_results -> AssessmentResult
2025-07-25 17:18:57,477 - DataExportService - DEBUG - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-25 17:18:57,479 - DataExportService - DEBUG - 已注册表模型: health_diaries -> HealthDiary
2025-07-25 17:18:57,481 - DataExportService - DEBUG - 已注册表模型: medical_records -> MedicalRecord
2025-07-25 17:18:57,485 - DataExportService - DEBUG - 已注册表模型: lab_reports -> LabReport
2025-07-25 17:18:57,487 - DataExportService - DEBUG - 已注册表模型: examination_reports -> ExaminationReport
2025-07-25 17:18:57,488 - DataExportService - DEBUG - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-25 17:18:57,490 - DataExportService - DEBUG - 已注册表模型: medications -> Medication
2025-07-25 17:18:57,491 - DataExportService - DEBUG - 已注册表模型: imaging_reports -> ImagingReport
2025-07-25 17:18:57,493 - DataExportService - DEBUG - 已注册表模型: other_records -> OtherRecord
2025-07-25 17:18:57,495 - DataExportService - DEBUG - 已注册表模型: user_health_records -> HealthRecord
2025-07-25 17:18:57,496 - DataExportService - DEBUG - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-25 17:18:57,498 - DataExportService - DEBUG - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-25 17:18:57,503 - DataExportService - DEBUG - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-25 17:18:57,505 - DataExportService - DEBUG - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-25 17:18:57,506 - DataExportService - INFO - 已初始化 19 个表模型
2025-07-25 17:18:57,508 - DataExportService - DEBUG - 已注册表模型: users -> User
2025-07-25 17:18:57,510 - DataExportService - DEBUG - 已注册表模型: health_records -> HealthRecord
2025-07-25 17:18:57,512 - DataExportService - DEBUG - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-25 17:18:57,513 - DataExportService - DEBUG - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-25 17:18:57,515 - DataExportService - DEBUG - 已注册表模型: assessment_results -> AssessmentResult
2025-07-25 17:18:57,519 - DataExportService - DEBUG - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-25 17:18:57,520 - DataExportService - DEBUG - 已注册表模型: health_diaries -> HealthDiary
2025-07-25 17:18:57,522 - DataExportService - DEBUG - 已注册表模型: medical_records -> MedicalRecord
2025-07-25 17:18:57,524 - DataExportService - DEBUG - 已注册表模型: lab_reports -> LabReport
2025-07-25 17:18:57,526 - DataExportService - DEBUG - 已注册表模型: examination_reports -> ExaminationReport
2025-07-25 17:18:57,527 - DataExportService - DEBUG - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-25 17:18:57,529 - DataExportService - DEBUG - 已注册表模型: medications -> Medication
2025-07-25 17:18:57,531 - DataExportService - DEBUG - 已注册表模型: imaging_reports -> ImagingReport
2025-07-25 17:18:57,535 - DataExportService - DEBUG - 已注册表模型: other_records -> OtherRecord
2025-07-25 17:18:57,537 - DataExportService - DEBUG - 已注册表模型: user_health_records -> HealthRecord
2025-07-25 17:18:57,539 - DataExportService - DEBUG - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-25 17:18:57,540 - DataExportService - DEBUG - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-25 17:18:57,542 - DataExportService - DEBUG - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-25 17:18:57,543 - DataExportService - DEBUG - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-25 17:18:57,544 - DataExportService - INFO - 已初始化 19 个表模型
2025-07-25 17:18:57,546 - DataExportService - DEBUG - 已注册表模型: users -> User
2025-07-25 17:18:57,548 - DataExportService - DEBUG - 已注册表模型: health_records -> HealthRecord
2025-07-25 17:18:57,549 - DataExportService - DEBUG - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-25 17:18:57,552 - DataExportService - DEBUG - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-25 17:18:57,554 - DataExportService - DEBUG - 已注册表模型: assessment_results -> AssessmentResult
2025-07-25 17:18:57,555 - DataExportService - DEBUG - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-25 17:18:57,556 - DataExportService - DEBUG - 已注册表模型: health_diaries -> HealthDiary
2025-07-25 17:18:57,558 - DataExportService - DEBUG - 已注册表模型: medical_records -> MedicalRecord
2025-07-25 17:18:57,559 - DataExportService - DEBUG - 已注册表模型: lab_reports -> LabReport
2025-07-25 17:18:57,560 - DataExportService - DEBUG - 已注册表模型: examination_reports -> ExaminationReport
2025-07-25 17:18:57,561 - DataExportService - DEBUG - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-25 17:18:57,563 - DataExportService - DEBUG - 已注册表模型: medications -> Medication
2025-07-25 17:18:57,564 - DataExportService - DEBUG - 已注册表模型: imaging_reports -> ImagingReport
2025-07-25 17:18:57,565 - DataExportService - DEBUG - 已注册表模型: other_records -> OtherRecord
2025-07-25 17:18:57,566 - DataExportService - DEBUG - 已注册表模型: user_health_records -> HealthRecord
2025-07-25 17:18:57,607 - DataExportService - DEBUG - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-25 17:18:57,610 - DataExportService - DEBUG - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-25 17:18:57,611 - DataExportService - DEBUG - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-25 17:18:57,614 - DataExportService - DEBUG - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-25 17:18:57,620 - DataExportService - INFO - 已初始化 19 个表模型
2025-07-25 17:18:57,720 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-07-25 17:18:57,738 - fallback_manager - DEBUG - 当前Python路径: ['c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\Pythonwin', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-07-25 17:18:57,748 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-07-25 17:19:04,652 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 75.8%, 磁盘: 94.6%
2025-07-25 17:19:08,021 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-25 17:19:08,027 - auth_service - INFO - 统一认证服务初始化完成
2025-07-25 17:19:08,157 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-25 17:19:08,159 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-25 17:19:09,189 - BackendMockDataManager - INFO - 后端模拟数据模式已启用
2025-07-25 17:19:12,615 - DataExportService - DEBUG - 已注册表模型: users -> User
2025-07-25 17:19:12,618 - DataExportService - DEBUG - 已注册表模型: health_records -> HealthRecord
2025-07-25 17:19:12,619 - DataExportService - DEBUG - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-25 17:19:12,620 - DataExportService - DEBUG - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-25 17:19:12,622 - DataExportService - DEBUG - 已注册表模型: assessment_results -> AssessmentResult
2025-07-25 17:19:12,624 - DataExportService - DEBUG - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-25 17:19:12,626 - DataExportService - DEBUG - 已注册表模型: health_diaries -> HealthDiary
2025-07-25 17:19:12,628 - DataExportService - DEBUG - 已注册表模型: medical_records -> MedicalRecord
2025-07-25 17:19:12,629 - DataExportService - DEBUG - 已注册表模型: lab_reports -> LabReport
2025-07-25 17:19:12,636 - DataExportService - DEBUG - 已注册表模型: examination_reports -> ExaminationReport
2025-07-25 17:19:12,638 - DataExportService - DEBUG - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-25 17:19:12,639 - DataExportService - DEBUG - 已注册表模型: medications -> Medication
2025-07-25 17:19:12,641 - DataExportService - DEBUG - 已注册表模型: imaging_reports -> ImagingReport
2025-07-25 17:19:12,643 - DataExportService - DEBUG - 已注册表模型: other_records -> OtherRecord
2025-07-25 17:19:12,645 - DataExportService - DEBUG - 已注册表模型: user_health_records -> HealthRecord
2025-07-25 17:19:12,649 - DataExportService - DEBUG - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-25 17:19:12,653 - DataExportService - DEBUG - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-25 17:19:12,655 - DataExportService - DEBUG - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-25 17:19:12,659 - DataExportService - DEBUG - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-25 17:19:12,660 - DataExportService - INFO - 已初始化 19 个表模型
2025-07-25 17:19:12,664 - DataExportService - DEBUG - 已注册表模型: users -> User
2025-07-25 17:19:12,669 - DataExportService - DEBUG - 已注册表模型: health_records -> HealthRecord
2025-07-25 17:19:12,671 - DataExportService - DEBUG - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-25 17:19:12,673 - DataExportService - DEBUG - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-25 17:19:12,674 - DataExportService - DEBUG - 已注册表模型: assessment_results -> AssessmentResult
2025-07-25 17:19:12,675 - DataExportService - DEBUG - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-25 17:19:12,676 - DataExportService - DEBUG - 已注册表模型: health_diaries -> HealthDiary
2025-07-25 17:19:12,678 - DataExportService - DEBUG - 已注册表模型: medical_records -> MedicalRecord
2025-07-25 17:19:12,679 - DataExportService - DEBUG - 已注册表模型: lab_reports -> LabReport
2025-07-25 17:19:12,684 - DataExportService - DEBUG - 已注册表模型: examination_reports -> ExaminationReport
2025-07-25 17:19:12,686 - DataExportService - DEBUG - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-25 17:19:12,687 - DataExportService - DEBUG - 已注册表模型: medications -> Medication
2025-07-25 17:19:12,688 - DataExportService - DEBUG - 已注册表模型: imaging_reports -> ImagingReport
2025-07-25 17:19:12,689 - DataExportService - DEBUG - 已注册表模型: other_records -> OtherRecord
2025-07-25 17:19:12,691 - DataExportService - DEBUG - 已注册表模型: user_health_records -> HealthRecord
2025-07-25 17:19:12,692 - DataExportService - DEBUG - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-25 17:19:12,693 - DataExportService - DEBUG - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-25 17:19:12,695 - DataExportService - DEBUG - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-25 17:19:12,697 - DataExportService - DEBUG - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-25 17:19:12,701 - DataExportService - INFO - 已初始化 19 个表模型
2025-07-25 17:19:12,703 - DataExportService - DEBUG - 已注册表模型: users -> User
2025-07-25 17:19:12,704 - DataExportService - DEBUG - 已注册表模型: health_records -> HealthRecord
2025-07-25 17:19:12,705 - DataExportService - DEBUG - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-25 17:19:12,707 - DataExportService - DEBUG - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-25 17:19:12,708 - DataExportService - DEBUG - 已注册表模型: assessment_results -> AssessmentResult
2025-07-25 17:19:12,709 - DataExportService - DEBUG - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-25 17:19:12,710 - DataExportService - DEBUG - 已注册表模型: health_diaries -> HealthDiary
2025-07-25 17:19:12,712 - DataExportService - DEBUG - 已注册表模型: medical_records -> MedicalRecord
2025-07-25 17:19:12,713 - DataExportService - DEBUG - 已注册表模型: lab_reports -> LabReport
2025-07-25 17:19:12,718 - DataExportService - DEBUG - 已注册表模型: examination_reports -> ExaminationReport
2025-07-25 17:19:12,719 - DataExportService - DEBUG - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-25 17:19:12,721 - DataExportService - DEBUG - 已注册表模型: medications -> Medication
2025-07-25 17:19:12,722 - DataExportService - DEBUG - 已注册表模型: imaging_reports -> ImagingReport
2025-07-25 17:19:12,723 - DataExportService - DEBUG - 已注册表模型: other_records -> OtherRecord
2025-07-25 17:19:12,726 - DataExportService - DEBUG - 已注册表模型: user_health_records -> HealthRecord
2025-07-25 17:19:12,729 - DataExportService - DEBUG - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-25 17:19:12,730 - DataExportService - DEBUG - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-25 17:19:12,731 - DataExportService - DEBUG - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-25 17:19:12,738 - DataExportService - DEBUG - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-25 17:19:12,739 - DataExportService - INFO - 已初始化 19 个表模型
2025-07-25 17:19:12,742 - DataExportService - DEBUG - 已注册表模型: users -> User
2025-07-25 17:19:12,743 - DataExportService - DEBUG - 已注册表模型: health_records -> HealthRecord
2025-07-25 17:19:12,744 - DataExportService - DEBUG - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-25 17:19:12,746 - DataExportService - DEBUG - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-25 17:19:12,747 - DataExportService - DEBUG - 已注册表模型: assessment_results -> AssessmentResult
2025-07-25 17:19:12,751 - DataExportService - DEBUG - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-25 17:19:12,752 - DataExportService - DEBUG - 已注册表模型: health_diaries -> HealthDiary
2025-07-25 17:19:12,753 - DataExportService - DEBUG - 已注册表模型: medical_records -> MedicalRecord
2025-07-25 17:19:12,754 - DataExportService - DEBUG - 已注册表模型: lab_reports -> LabReport
2025-07-25 17:19:12,756 - DataExportService - DEBUG - 已注册表模型: examination_reports -> ExaminationReport
2025-07-25 17:19:12,757 - DataExportService - DEBUG - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-25 17:19:12,758 - DataExportService - DEBUG - 已注册表模型: medications -> Medication
2025-07-25 17:19:12,759 - DataExportService - DEBUG - 已注册表模型: imaging_reports -> ImagingReport
2025-07-25 17:19:12,760 - DataExportService - DEBUG - 已注册表模型: other_records -> OtherRecord
2025-07-25 17:19:12,761 - DataExportService - DEBUG - 已注册表模型: user_health_records -> HealthRecord
2025-07-25 17:19:12,763 - DataExportService - DEBUG - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-25 17:19:12,764 - DataExportService - DEBUG - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-25 17:19:12,768 - DataExportService - DEBUG - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-25 17:19:12,769 - DataExportService - DEBUG - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-25 17:19:12,771 - DataExportService - INFO - 已初始化 19 个表模型
2025-07-25 17:19:12,857 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-07-25 17:19:12,859 - fallback_manager - DEBUG - 当前Python路径: ['c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\Pythonwin', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-07-25 17:19:12,870 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-07-25 17:19:12,920 - health_monitor - INFO - 健康监控器初始化完成
2025-07-25 17:19:12,924 - app.core.system_monitor - INFO - 已加载 16 个历史数据点
2025-07-25 17:19:12,936 - app.core.alert_detector - INFO - 已加载 6 个告警规则
2025-07-25 17:19:12,940 - app.core.alert_detector - INFO - 已加载 0 个当前告警
2025-07-25 17:19:12,945 - app.core.alert_detector - INFO - 已加载 3 个历史告警
2025-07-25 17:19:12,949 - app.core.alert_detector - INFO - 已加载 1 个通知渠道
2025-07-25 17:19:12,952 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-25 17:19:12,961 - alert_manager - INFO - 已初始化默认告警规则
2025-07-25 17:19:12,963 - alert_manager - INFO - 已初始化默认通知渠道
2025-07-25 17:19:12,968 - alert_manager - INFO - 告警管理器初始化完成
2025-07-25 17:19:14,616 - db_service - INFO - 数据库引擎和会话工厂创建成功
2025-07-25 17:19:14,646 - db_service - INFO - 数据库服务初始化完成
2025-07-25 17:19:14,658 - notification_service - INFO - 通知服务初始化完成
2025-07-25 17:19:14,660 - main - INFO - 错误处理模块导入成功
2025-07-25 17:19:14,724 - main - INFO - 监控模块导入成功
2025-07-25 17:19:14,739 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-07-25 17:19:19,385 - asyncio - DEBUG - Using selector: SelectSelector
2025-07-25 17:19:19,485 - main - INFO - 错误处理模块导入成功
2025-07-25 17:19:19,486 - main - INFO - 监控模块导入成功
2025-07-25 17:19:19,487 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-07-25 17:19:20,569 - main - INFO - 应用启动中...
2025-07-25 17:19:20,571 - error_handling - INFO - 错误处理已设置
2025-07-25 17:19:20,572 - main - INFO - 错误处理系统初始化完成
2025-07-25 17:19:20,574 - monitoring - INFO - 添加指标端点成功: /metrics
2025-07-25 17:19:20,577 - monitoring - INFO - 添加健康检查端点成功: /health
2025-07-25 17:19:20,580 - monitoring - INFO - 添加详细健康检查端点成功: /api/health/detailed
2025-07-25 17:19:20,588 - monitoring - INFO - 系统信息初始化完成: Markey, Windows-10-10.0.19045-SP0, Python 3.13.2, CPU核心数: 4
2025-07-25 17:19:20,596 - monitoring - INFO - 启动资源监控线程成功
2025-07-25 17:19:20,600 - monitoring - INFO - 监控系统初始化成功（不使用中间件）
2025-07-25 17:19:20,602 - monitoring - INFO - 监控系统初始化完成
2025-07-25 17:19:20,603 - main - INFO - 监控系统初始化完成
2025-07-25 17:19:20,606 - app.db.init_db - INFO - 所有模型导入成功
2025-07-25 17:19:20,609 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-07-25 17:19:20,616 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-25 17:19:20,618 - app.db.init_db - INFO - 所有模型导入成功
2025-07-25 17:19:20,620 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-07-25 17:19:20,621 - app.db.init_db - INFO - 正在运行数据库迁移...
2025-07-25 17:19:20,622 - app.db.init_db - INFO - 正在检查并更新数据库表结构...
2025-07-25 17:19:20,625 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-25 17:19:20,626 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alerts")
2025-07-25 17:19:20,628 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,635 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_rules")
2025-07-25 17:19:20,637 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,639 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_channels")
2025-07-25 17:19:20,641 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,644 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-25 17:19:20,645 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,652 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_records")
2025-07-25 17:19:20,654 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,657 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_overviews")
2025-07-25 17:19:20,659 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,662 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medical_records")
2025-07-25 17:19:20,668 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,671 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("inpatient_records")
2025-07-25 17:19:20,673 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,675 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("surgery_records")
2025-07-25 17:19:20,677 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,679 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_reports")
2025-07-25 17:19:20,685 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,687 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_report_items")
2025-07-25 17:19:20,688 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,691 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_reports")
2025-07-25 17:19:20,692 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,694 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("examination_reports")
2025-07-25 17:19:20,696 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,699 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.6%, CPU使用率 79.2%
2025-07-25 17:19:20,702 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("documents")
2025-07-25 17:19:20,703 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,706 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("other_records")
2025-07-25 17:19:20,707 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,710 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("operation_logs")
2025-07-25 17:19:20,711 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,713 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("follow_up_records")
2025-07-25 17:19:20,719 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,720 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_diaries")
2025-07-25 17:19:20,722 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,724 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("registration_records")
2025-07-25 17:19:20,726 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,728 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("prescription_records")
2025-07-25 17:19:20,731 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,734 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("laboratory_records")
2025-07-25 17:19:20,736 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,738 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_records")
2025-07-25 17:19:20,740 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,742 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medications")
2025-07-25 17:19:20,744 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,746 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medication_usages")
2025-07-25 17:19:20,751 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,754 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("role_applications")
2025-07-25 17:19:20,755 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,757 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessments")
2025-07-25 17:19:20,759 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,761 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_items")
2025-07-25 17:19:20,763 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,766 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_responses")
2025-07-25 17:19:20,768 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,771 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_templates")
2025-07-25 17:19:20,773 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,775 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_template_questions")
2025-07-25 17:19:20,776 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,779 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaires")
2025-07-25 17:19:20,784 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,786 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_items")
2025-07-25 17:19:20,787 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,789 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_responses")
2025-07-25 17:19:20,792 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,794 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_answers")
2025-07-25 17:19:20,796 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,800 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_templates")
2025-07-25 17:19:20,802 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,805 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_template_questions")
2025-07-25 17:19:20,806 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,808 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_results")
2025-07-25 17:19:20,810 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,812 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_results")
2025-07-25 17:19:20,816 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,819 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("report_templates")
2025-07-25 17:19:20,821 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,825 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_distributions")
2025-07-25 17:19:20,828 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,835 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_distributions")
2025-07-25 17:19:20,838 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,840 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("service_stats")
2025-07-25 17:19:20,843 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-25 17:19:20,870 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-25 17:19:20,939 - app.db.init_db - INFO - 数据库表结构检查和更新完成
2025-07-25 17:19:20,998 - app.db.init_db - INFO - 模型关系初始化完成
2025-07-25 17:19:21,050 - app.db.init_db - INFO - 模型关系设置完成
2025-07-25 17:19:21,060 - main - INFO - 数据库初始化完成（强制重建）
2025-07-25 17:19:21,065 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-25 17:19:21,072 - main - INFO - 数据库连接正常
2025-07-25 17:19:21,076 - main - INFO - 开始初始化模板数据
2025-07-25 17:19:21,096 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-25 17:19:22,000 - app.db.init_templates - INFO - 开始初始化评估量表模板...
2025-07-25 17:19:22,075 - app.db.init_templates - INFO - 成功初始化 5 个评估量表模板
2025-07-25 17:19:22,204 - app.db.init_templates - INFO - 开始初始化调查问卷模板...
2025-07-25 17:19:22,335 - app.db.init_templates - INFO - 成功初始化 5 个调查问卷模板
2025-07-25 17:19:22,339 - main - INFO - 模板数据初始化完成
2025-07-25 17:19:22,342 - main - INFO - 数据库表结构已经标准化，不需要迁移
2025-07-25 17:19:22,345 - main - INFO - 应用启动完成
2025-07-25 17:19:35,807 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.0%, CPU使用率 95.8%
2025-07-25 17:19:50,916 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.2%, CPU使用率 87.5%
2025-07-25 17:19:59,899 - alert_manager - WARNING - 触发告警: disk_free_space, 当前值: 0.0, 阈值: 1024
2025-07-25 17:20:05,909 - health_monitor - DEBUG - 系统指标 - CPU: 86.2%, 内存: 76.9%, 磁盘: 94.6%
2025-07-25 17:20:06,070 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.9%, CPU使用率 96.6%
2025-07-25 17:20:14,315 - health_monitor - DEBUG - 系统指标 - CPU: 94.7%, 内存: 76.4%, 磁盘: 94.6%
2025-07-25 17:20:21,193 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.2%, CPU使用率 92.9%
2025-07-25 17:20:36,351 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.5%, CPU使用率 87.5%
2025-07-25 17:20:51,458 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.0%, CPU使用率 85.7%
2025-07-25 17:20:59,936 - alert_manager - WARNING - 触发告警: disk_usage, 当前值: 94.6, 阈值: 90
2025-07-25 17:21:06,564 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.7%, CPU使用率 85.7%
2025-07-25 17:21:06,940 - health_monitor - DEBUG - 系统指标 - CPU: 75.5%, 内存: 73.0%, 磁盘: 94.6%
2025-07-25 17:21:15,434 - health_monitor - DEBUG - 系统指标 - CPU: 90.3%, 内存: 72.4%, 磁盘: 94.6%
2025-07-25 17:21:21,680 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.6%, CPU使用率 88.0%
2025-07-25 17:21:36,948 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.6%, CPU使用率 100.0%
2025-07-25 17:21:52,388 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.6%, CPU使用率 100.0%
2025-07-25 17:22:07,601 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.5%, CPU使用率 100.0%
2025-07-25 17:22:07,972 - health_monitor - DEBUG - 系统指标 - CPU: 92.3%, 内存: 74.3%, 磁盘: 94.6%
2025-07-25 17:22:16,463 - health_monitor - DEBUG - 系统指标 - CPU: 66.5%, 内存: 73.8%, 磁盘: 94.6%
