#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整的移动端文档上传和查询流程
确保使用相同用户进行上传和查询
"""

import requests
import json
import os
import tempfile
from datetime import datetime

# 测试配置
BASE_URL = "http://localhost:8006"
TEST_USER = {
    "username": "admin",
    "password": "admin123"
}

def create_test_file(filename, content):
    """创建测试文件"""
    temp_dir = tempfile.gettempdir()
    file_path = os.path.join(temp_dir, filename)
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    return file_path

def test_complete_mobile_flow():
    """测试完整的移动端文档流程"""
    print("=== 测试完整的移动端文档上传和查询流程 ===")
    
    # 1. 用户登录
    print("\n[1] 用户登录...")
    login_url = f"{BASE_URL}/api/auth/register/login"
    login_data = {
        "username": TEST_USER["username"],
        "password": TEST_USER["password"]
    }
    
    try:
        login_response = requests.post(login_url, json=login_data)
        print(f"登录状态码: {login_response.status_code}")
        
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.text}")
            return False
        
        login_result = login_response.json()
        if login_result.get("status") != "success":
            print(f"❌ 登录失败: {login_result.get('message')}")
            return False
        
        access_token = login_result.get("access_token")
        user_info = login_result.get("user", {})
        custom_id = user_info.get("custom_id")
        
        print(f"✅ 登录成功，获取到token: {access_token[:50]}...")
        print(f"✅ 用户custom_id: {custom_id}")
        
    except Exception as e:
        print(f"❌ 登录异常: {str(e)}")
        return False
    
    # 2. 上传文档
    print(f"\n[2] 上传文档（用户: {custom_id}）...")
    upload_url = f"{BASE_URL}/api/mobile/upload"
    
    # 创建测试文件
    test_content = f"完整流程测试文档 - 用户{custom_id} - {datetime.now().isoformat()}"
    test_file_path = create_test_file("complete_flow_test.txt", test_content)
    
    try:
        # 准备上传数据
        with open(test_file_path, 'rb') as f:
            files = {
                'file': ('complete_flow_test.txt', f, 'text/plain')
            }
            
            data = {
                'title': '完整流程测试文档',
                'document_type': 'test',
                'description': f'测试用户{custom_id}的完整上传查询流程',
                'category': 'medical'
            }
            
            headers = {
                'Authorization': f'Bearer {access_token}'
            }
            
            upload_response = requests.post(upload_url, files=files, data=data, headers=headers)
            print(f"上传状态码: {upload_response.status_code}")
            
            if upload_response.status_code == 200:
                upload_result = upload_response.json()
                if upload_result.get("status") == "success":
                    document_id = upload_result.get('data', {}).get('document_id')
                    print("✅ 文件上传成功！")
                    print(f"   文档ID: {document_id}")
                    print(f"   文件名: {upload_result.get('data', {}).get('filename')}")
                    print(f"   状态: {upload_result.get('data', {}).get('status')}")
                else:
                    print(f"❌ 上传失败: {upload_result.get('message')}")
                    return False
            else:
                print(f"❌ 上传请求失败: {upload_response.text}")
                return False
                
    except Exception as e:
        print(f"❌ 上传异常: {str(e)}")
        return False
    finally:
        # 清理测试文件
        if os.path.exists(test_file_path):
            os.remove(test_file_path)
    
    # 3. 等待一下让数据库操作完成
    import time
    print("\n[3] 等待数据库操作完成...")
    time.sleep(2)
    
    # 4. 查询健康记录
    print(f"\n[4] 查询健康记录（用户: {custom_id}）...")
    query_url = f"{BASE_URL}/api/user-health-records/user/{custom_id}"
    
    headers = {
        'Authorization': f'Bearer {access_token}',
        'X-User-ID': str(custom_id)
    }
    
    params = {
        'page': 1,
        'page_size': 20,
        'custom_id': custom_id
    }
    
    try:
        query_response = requests.get(query_url, headers=headers, params=params)
        print(f"查询状态码: {query_response.status_code}")
        
        if query_response.status_code == 200:
            query_result = query_response.json()
            print(f"查询响应: {json.dumps(query_result, indent=2, ensure_ascii=False)[:800]}...")
            
            if query_result.get("status") == "success":
                data = query_result.get("data", {})
                records = data.get("records", [])
                total = data.get("total", 0)
                
                print(f"✅ 查询成功，共找到 {total} 条健康记录")
                
                # 查找文档类型的记录
                document_records = [r for r in records if r.get("record_type") == "document"]
                print(f"✅ 其中文档类型记录: {len(document_records)} 条")
                
                if document_records:
                    print("\n📄 文档记录详情:")
                    for i, record in enumerate(document_records, 1):
                        print(f"  [{i}] ID: {record.get('id')}")
                        print(f"      标题: {record.get('title')}")
                        print(f"      类型: {record.get('record_type')}")
                        print(f"      状态: {record.get('status')}")
                        print(f"      创建时间: {record.get('created_at')}")
                        
                        # 解析content中的文档信息
                        content = record.get('content')
                        if content:
                            try:
                                content_data = json.loads(content)
                                print(f"      关联文档ID: {content_data.get('document_id')}")
                                print(f"      文件名: {content_data.get('filename')}")
                                print(f"      来源: {content_data.get('source')}")
                            except:
                                print(f"      内容: {content[:100]}...")
                        print()
                    
                    return True
                else:
                    print("❌ 未找到文档类型的健康记录")
                    
                    # 显示所有记录类型用于调试
                    if records:
                        print("\n🔍 所有记录类型:")
                        for record in records:
                            print(f"  - {record.get('record_type')} (ID: {record.get('id')})")
                    
                    return False
            else:
                print(f"❌ 查询失败: {query_result.get('message')}")
                return False
        else:
            print(f"❌ 查询请求失败: {query_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 查询异常: {str(e)}")
        return False
    
    # 5. 查询Documents表（对比验证）
    print(f"\n[5] 查询Documents表（对比验证）...")
    docs_url = f"{BASE_URL}/api/documents"
    
    try:
        docs_response = requests.get(docs_url, headers=headers, params={'custom_id': custom_id})
        print(f"Documents查询状态码: {docs_response.status_code}")
        
        if docs_response.status_code == 200:
            docs_result = docs_response.json()
            if docs_result.get("status") == "success":
                data = docs_result.get("data", {})
                documents = data.get("documents", [])
                total = data.get("total", 0)
                
                print(f"✅ Documents表查询成功，共找到 {total} 个文档")
                
                if documents:
                    print("\n📄 Documents表中的文档:")
                    for i, doc in enumerate(documents, 1):
                        print(f"  [{i}] ID: {doc.get('id')}")
                        print(f"      标题: {doc.get('title')}")
                        print(f"      文件名: {doc.get('filename')}")
                        print(f"      状态: {doc.get('status')}")
                        print(f"      来源: {doc.get('source')}")
                        print(f"      用户ID: {doc.get('custom_id')}")
                        print(f"      创建时间: {doc.get('created_at')}")
                        print()
            else:
                print(f"❌ Documents查询失败: {docs_result.get('message')}")
        else:
            print(f"❌ Documents查询请求失败: {docs_response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ Documents查询异常: {str(e)}")

if __name__ == "__main__":
    success = test_complete_mobile_flow()
    
    if success:
        print("\n🎉 完整移动端文档流程测试成功！")
        print("✅ 上传的文档现在可以在移动端查询中正常显示")
    else:
        print("\n❌ 移动端文档流程仍有问题，需要进一步调试。")