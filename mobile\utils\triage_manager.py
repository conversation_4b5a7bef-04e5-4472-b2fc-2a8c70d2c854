"""
语音分诊管理模块 - 处理语音分诊会话、消息和总结
"""
import os
import json
import logging
import threading
import time
import uuid
from datetime import datetime
from pathlib import Path
from utils.user_manager import get_user_manager

# 设置日志
logger = logging.getLogger(__name__)

# 单例模式的TriageManager
_triage_manager_instance = None

def get_triage_manager():
    """获取TriageManager单例"""
    global _triage_manager_instance
    if _triage_manager_instance is None:
        _triage_manager_instance = TriageManager()
    return _triage_manager_instance

class TriageManager:
    """语音分诊管理器，负责管理分诊会话和消息，与云端同步"""
    
    def __init__(self):
        """初始化分诊管理器"""
        # 获取数据目录
        self.base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.data_dir = os.path.join(self.base_dir, 'data', 'triage')
        
        # 确保目录存在
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 数据文件路径
        self.sessions_file = os.path.join(self.data_dir, 'sessions.json')
        self.messages_dir = os.path.join(self.data_dir, 'messages')
        self.audio_dir = os.path.join(self.data_dir, 'audio')
        self.sync_queue_file = os.path.join(self.data_dir, 'sync_queue.json')
        
        # 确保目录存在
        os.makedirs(self.messages_dir, exist_ok=True)
        os.makedirs(self.audio_dir, exist_ok=True)
        
        # 初始化数据
        self._init_data_files()
        
        # 启动同步队列处理
        self._start_sync_queue_processor()
    
    def _init_data_files(self):
        """初始化数据文件"""
        # 会话文件
        if not os.path.exists(self.sessions_file):
            with open(self.sessions_file, 'w', encoding='utf-8') as f:
                json.dump({"sessions": []}, f)
        
        # 同步队列文件
        if not os.path.exists(self.sync_queue_file):
            with open(self.sync_queue_file, 'w', encoding='utf-8') as f:
                json.dump({"queue": []}, f)
    
    def _start_sync_queue_processor(self):
        """启动同步队列处理线程"""
        def process_queue():
            while True:
                try:
                    # 每60秒处理一次队列
                    time.sleep(60)
                    
                    # 处理队列中的待同步项目
                    self._process_sync_queue()
                    
                except Exception as e:
                    logger.error(f"处理同步队列时出错: {str(e)}")
                    # 出错后等待较长时间再重试
                    time.sleep(300)
        
        # 创建并启动后台线程
        sync_thread = threading.Thread(target=process_queue, daemon=True)
        sync_thread.start()
    
    def _process_sync_queue(self):
        """处理同步队列中的项目"""
        # 检查队列是否为空
        queue = self._load_sync_queue()
        if not queue:
            return
        
        # 获取API客户端
        from api.api_client import APIClient
        api_client = APIClient()
        
        # 检查是否已登录
        if not api_client.cloud_api.is_authenticated():
            logger.info("未登录，暂不处理同步队列")
            return
        
        # 处理队列中的项目
        remaining_queue = []
        synced_count = 0
        
        for item in queue:
            item_type = item.get('type')
            
            if item_type == 'feedback':
                # 同步反馈
                session_id = item.get('session_id')
                feedback = item.get('feedback')
                
                try:
                    result = api_client.submit_triage_feedback(session_id, feedback)
                    if result and result.get('success'):
                        synced_count += 1
                        continue
                except Exception as e:
                    logger.error(f"同步反馈时出错: {str(e)}")
            
            elif item_type == 'audio_message':
                # 同步音频消息
                session_id = item.get('session_id')
                message_id = item.get('message_id')
                audio_path = item.get('audio_path')
                
                try:
                    if os.path.exists(audio_path):
                        result = api_client.upload_triage_audio(session_id, message_id, audio_path)
                        if result and result.get('success'):
                            synced_count += 1
                            continue
                except Exception as e:
                    logger.error(f"同步音频消息时出错: {str(e)}")
            
            # 如果同步失败，保留在队列中
            remaining_queue.append(item)
        
        # 更新同步队列
        self._save_sync_queue(remaining_queue)
        
        if synced_count > 0:
            logger.info(f"成功同步 {synced_count} 个分诊项目")
    
    def _load_sync_queue(self):
        """加载同步队列"""
        try:
            with open(self.sync_queue_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('queue', [])
        except (json.JSONDecodeError, FileNotFoundError):
            return []
    
    def _save_sync_queue(self, queue):
        """保存同步队列"""
        with open(self.sync_queue_file, 'w', encoding='utf-8') as f:
            json.dump({"queue": queue}, f, ensure_ascii=False)
    
    def _add_to_sync_queue(self, item):
        """添加项目到同步队列"""
        queue = self._load_sync_queue()
        queue.append(item)
        self._save_sync_queue(queue)
    
    def _get_user_identity(self):
        """获取当前用户身份"""
        user = get_user_manager().get_current_user()
        if user:
            return {'user_id': getattr(user, 'user_id', None), 'token': getattr(user, 'token', None)}
        return {}

    def create_session(self, user_id=None):
        """创建新的分诊会话
        
        Args:
            user_id: 用户ID，可选
            
        Returns:
            str: 会话ID
        """
        session_id = str(uuid.uuid4())
        self.save_session(session_id, status="active", user_id=user_id)
        return session_id
    
    def save_session(self, session_id, status="active", created_at=None, user_id=None):
        """保存分诊会话
        
        Args:
            session_id: 会话ID
            status: 会话状态
            created_at: 创建时间
            user_id: 用户ID
            
        Returns:
            bool: 是否成功
        """
        if not created_at:
            created_at = datetime.now().isoformat()
            
        try:
            # 加载现有会话
            with open(self.sessions_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            sessions = data.get('sessions', [])
            
            # 检查会话是否已存在
            for session in sessions:
                if session.get('session_id') == session_id:
                    # 更新现有会话
                    session['status'] = status
                    session['updated_at'] = datetime.now().isoformat()
                    if user_id:
                        session['user_id'] = user_id
                    break
            else:
                # 添加新会话
                new_session = {
                    'session_id': session_id,
                    'status': status,
                    'created_at': created_at,
                    'updated_at': datetime.now().isoformat()
                }
                if user_id:
                    new_session['user_id'] = user_id
                sessions.append(new_session)
            
            # 保存会话
            with open(self.sessions_file, 'w', encoding='utf-8') as f:
                json.dump({"sessions": sessions}, f, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            logger.error(f"保存会话时出错: {str(e)}")
            return False
    
    def get_session(self, session_id):
        """获取会话信息
        
        Args:
            session_id: 会话ID
            
        Returns:
            dict: 会话信息
        """
        try:
            with open(self.sessions_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            for session in data.get('sessions', []):
                if session.get('session_id') == session_id:
                    return session
            
            return None
            
        except Exception as e:
            logger.error(f"获取会话时出错: {str(e)}")
            return None
    
    def get_user_sessions(self, user_id):
        """获取用户的所有会话
        
        Args:
            user_id: 用户ID
            
        Returns:
            list: 会话列表
        """
        try:
            with open(self.sessions_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            return [s for s in data.get('sessions', []) if s.get('user_id') == user_id]
            
        except Exception as e:
            logger.error(f"获取用户会话时出错: {str(e)}")
            return []
    
    def get_active_sessions(self):
        """获取所有活跃会话
        
        Returns:
            list: 活跃会话列表
        """
        try:
            with open(self.sessions_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            return [s for s in data.get('sessions', []) if s.get('status') == 'active']
            
        except Exception as e:
            logger.error(f"获取活跃会话时出错: {str(e)}")
            return []
    
    def end_session(self, session_id):
        """结束会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            bool: 是否成功
        """
        return self.save_session(session_id, status="ended")
    
    def save_message(self, session_id, message_id=None, direction="outgoing", message_type="text", content="", audio_path=None):
        """保存消息
        
        Args:
            session_id: 会话ID
            message_id: 消息ID，如不提供则自动生成
            direction: 消息方向(outgoing/incoming)
            message_type: 消息类型(text/audio/image)
            content: 消息内容
            audio_path: 音频文件路径，仅当message_type为audio时有效
            
        Returns:
            str: 消息ID
        """
        if not message_id:
            message_id = str(uuid.uuid4())
            
        timestamp = datetime.now().isoformat()
            
        try:
            messages_file = os.path.join(self.messages_dir, f"{session_id}.json")
            
            # 加载现有消息
            if os.path.exists(messages_file):
                with open(messages_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            else:
                data = {"messages": []}
            
            messages = data.get('messages', [])
            
            # 检查消息是否已存在
            message_exists = False
            for msg in messages:
                if msg.get('message_id') == message_id:
                    # 更新现有消息
                    msg['direction'] = direction
                    msg['type'] = message_type
                    msg['content'] = content
                    msg['updated_at'] = datetime.now().isoformat()
                    if audio_path and message_type == 'audio':
                        msg['audio_path'] = audio_path
                    message_exists = True
                    break
            
            if not message_exists:
                # 添加新消息
                new_message = {
                    'message_id': message_id,
                    'session_id': session_id,
                    'direction': direction,
                    'type': message_type,
                    'content': content,
                    'timestamp': timestamp,
                    'updated_at': datetime.now().isoformat()
                }
                
                if audio_path and message_type == 'audio':
                    new_message['audio_path'] = audio_path
                    
                    # 添加到同步队列
                    if direction == 'outgoing':
                        self._add_to_sync_queue({
                            'type': 'audio_message',
                            'session_id': session_id,
                            'message_id': message_id,
                            'audio_path': audio_path,
                            'timestamp': timestamp
                        })
                
                messages.append(new_message)
            
            # 保存消息
            with open(messages_file, 'w', encoding='utf-8') as f:
                json.dump({"messages": messages}, f, ensure_ascii=False)
            
            # 更新会话最后活动时间
            self.save_session(session_id)
            
            return message_id
            
        except Exception as e:
            logger.error(f"保存消息时出错: {str(e)}")
            return None
    
    def get_session_messages(self, session_id):
        """获取会话的所有消息
        
        Args:
            session_id: 会话ID
            
        Returns:
            list: 消息列表
        """
        try:
            messages_file = os.path.join(self.messages_dir, f"{session_id}.json")
            
            if not os.path.exists(messages_file):
                return []
            
            with open(messages_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 按时间戳排序
            messages = data.get('messages', [])
            return sorted(messages, key=lambda m: m.get('timestamp', ''))
            
        except Exception as e:
            logger.error(f"获取会话消息时出错: {str(e)}")
            return []
    
    def save_audio_file(self, session_id, audio_data, file_extension=".wav"):
        """保存音频文件
        
        Args:
            session_id: 会话ID
            audio_data: 音频数据
            file_extension: 文件扩展名
            
        Returns:
            str: 文件路径
        """
        try:
            # 创建会话音频目录
            session_audio_dir = os.path.join(self.audio_dir, session_id)
            os.makedirs(session_audio_dir, exist_ok=True)
            
            # 生成文件名
            file_name = f"{uuid.uuid4()}{file_extension}"
            file_path = os.path.join(session_audio_dir, file_name)
            
            # 写入文件
            with open(file_path, 'wb') as f:
                f.write(audio_data)
            
            return file_path
            
        except Exception as e:
            logger.error(f"保存音频文件时出错: {str(e)}")
            return None
    
    def save_text_message(self, session_id, content, direction="outgoing"):
        """保存文本消息
        
        Args:
            session_id: 会话ID
            content: 消息内容
            direction: 消息方向(outgoing/incoming)
            
        Returns:
            str: 消息ID
        """
        return self.save_message(
            session_id=session_id,
            direction=direction,
            message_type="text",
            content=content
        )
    
    def save_audio_message(self, session_id, audio_data, content="", direction="outgoing", file_extension=".wav"):
        """保存音频消息
        
        Args:
            session_id: 会话ID
            audio_data: 音频数据
            content: 文本内容（转写结果）
            direction: 消息方向(outgoing/incoming)
            file_extension: 文件扩展名
            
        Returns:
            str: 消息ID
        """
        # 保存音频文件
        audio_path = self.save_audio_file(session_id, audio_data, file_extension)
        if not audio_path:
            return None
        
        # 保存消息
        return self.save_message(
            session_id=session_id,
            direction=direction,
            message_type="audio",
            content=content,
            audio_path=audio_path
        )
    
    def save_session_summary(self, session_id, summary):
        """保存会话总结
        
        Args:
            session_id: 会话ID
            summary: 总结内容
            
        Returns:
            bool: 是否成功
        """
        try:
            # 加载现有会话
            with open(self.sessions_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            sessions = data.get('sessions', [])
            
            # 更新会话信息
            for session in sessions:
                if session.get('session_id') == session_id:
                    session['summary'] = summary
                    session['has_summary'] = True
                    session['updated_at'] = datetime.now().isoformat()
                    break
            else:
                # 如果找不到会话，添加新会话
                sessions.append({
                    'session_id': session_id,
                    'status': 'ended',
                    'summary': summary,
                    'has_summary': True,
                    'created_at': datetime.now().isoformat(),
                    'updated_at': datetime.now().isoformat()
                })
            
            # 保存会话
            with open(self.sessions_file, 'w', encoding='utf-8') as f:
                json.dump({"sessions": sessions}, f, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            logger.error(f"保存会话总结时出错: {str(e)}")
            return False
    
    def get_session_summary(self, session_id):
        """获取会话总结
        
        Args:
            session_id: 会话ID
            
        Returns:
            str: 总结内容
        """
        session = self.get_session(session_id)
        return session.get('summary') if session else None
    
    def save_feedback(self, session_id, feedback):
        """保存用户反馈
        
        Args:
            session_id: 会话ID
            feedback: 反馈内容
            
        Returns:
            bool: 是否成功
        """
        try:
            # 加载现有会话
            with open(self.sessions_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            sessions = data.get('sessions', [])
            
            # 更新会话信息
            found = False
            for session in sessions:
                if session.get('session_id') == session_id:
                    session['feedback'] = feedback
                    session['feedback_time'] = datetime.now().isoformat()
                    session['updated_at'] = datetime.now().isoformat()
                    found = True
                    break
            
            if found:
                # 保存会话
                with open(self.sessions_file, 'w', encoding='utf-8') as f:
                    json.dump({"sessions": sessions}, f, ensure_ascii=False)
                
                # 尝试同步到云端
                from api.api_client import APIClient
                api_client = APIClient()
                
                try:
                    identity = self._get_user_identity()
                    result = api_client.submit_triage_feedback(session_id, feedback, **identity)
                    if not result or not result.get('success'):
                        # 添加到同步队列
                        self._add_to_sync_queue({
                            'type': 'feedback',
                            'session_id': session_id,
                            'feedback': feedback,
                            'timestamp': datetime.now().isoformat()
                        })
                except Exception as e:
                    logger.error(f"提交反馈到云端时出错: {str(e)}")
                    # 添加到同步队列
                    self._add_to_sync_queue({
                        'type': 'feedback',
                        'session_id': session_id,
                        'feedback': feedback,
                        'timestamp': datetime.now().isoformat()
                    })
                
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"保存反馈时出错: {str(e)}")
            return False
    
    def get_feedback(self, session_id):
        """获取用户反馈
        
        Args:
            session_id: 会话ID
            
        Returns:
            dict: 反馈内容
        """
        session = self.get_session(session_id)
        return session.get('feedback') if session else None
        
    def request_transcribe(self, session_id, message_id, audio_path):
        """请求音频转写
        
        Args:
            session_id: 会话ID
            message_id: 消息ID
            audio_path: 音频文件路径
            
        Returns:
            bool: 是否成功提交
        """
        try:
            # 获取API客户端
            from api.api_client import APIClient
            api_client = APIClient()
            
            # 请求转写
            result = api_client.request_audio_transcribe(session_id, message_id, audio_path)
            
            return result and result.get('success', False)
            
        except Exception as e:
            logger.error(f"请求音频转写时出错: {str(e)}")
            return False
    
    def update_message_transcription(self, session_id, message_id, transcription):
        """更新消息的转写结果
        
        Args:
            session_id: 会话ID
            message_id: 消息ID
            transcription: 转写结果
            
        Returns:
            bool: 是否成功
        """
        try:
            messages_file = os.path.join(self.messages_dir, f"{session_id}.json")
            
            if not os.path.exists(messages_file):
                return False
            
            # 加载消息
            with open(messages_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            messages = data.get('messages', [])
            
            # 更新消息
            found = False
            for msg in messages:
                if msg.get('message_id') == message_id:
                    msg['content'] = transcription
                    msg['transcribed'] = True
                    msg['updated_at'] = datetime.now().isoformat()
                    found = True
                    break
            
            if found:
                # 保存消息
                with open(messages_file, 'w', encoding='utf-8') as f:
                    json.dump({"messages": messages}, f, ensure_ascii=False)
                
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"更新消息转写结果时出错: {str(e)}")
            return False

# 全局获取函数
def get_triage_manager():
    """获取语音分诊管理器实例"""
    return TriageManager()