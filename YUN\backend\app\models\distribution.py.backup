"""
分发记录相关模型
"""
from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Boolean, Float
from sqlalchemy.orm import relationship
from app.db.base_session import Base

# 使用字符串引用避免循环导入
# from app.models.user import User
# from app.models.assessment import Assessment
# from app.models.questionnaire import Questionnaire

class AssessmentDistribution(Base):
    """
    评估量表分发记录模型
    """
    __tablename__ = "assessment_distributions"
    __table_args__ = {"extend_existing": True}
    
    id = Column(Integer, primary_key=True, index=True)
    assessment_id = Column(Integer, ForeignKey("assessments.id", ondelete="CASCADE"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    distributor_id = Column(Integer, ForeignKey("users.id", ondelete="SET NULL"), nullable=True)
    
    status = Column(String(50), default="pending")  # pending, completed, expired
    due_date = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    message = Column(Text, nullable=True)
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联 - 使用字符串引用避免循环导入
    # 这些关系将由app.models.relationships模块管理
    assessment = relationship("Assessment", back_populates="distributions")
    user = relationship("User", foreign_keys=[user_id], back_populates="received_assessments")
    distributor = relationship("User", foreign_keys=[distributor_id], back_populates="distributed_assessments")

class QuestionnaireDistribution(Base):
    """
    调查问卷分发记录模型
    """
    __tablename__ = "questionnaire_distributions"
    __table_args__ = {"extend_existing": True}
    
    id = Column(Integer, primary_key=True, index=True)
    questionnaire_id = Column(Integer, ForeignKey("questionnaires.id", ondelete="CASCADE"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    distributor_id = Column(Integer, ForeignKey("users.id", ondelete="SET NULL"), nullable=True)
    
    status = Column(String(50), default="pending")  # pending, completed, expired
    due_date = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    message = Column(Text, nullable=True)
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联 - 使用字符串引用避免循环导入
    # 这些关系将由app.models.relationships模块管理
    questionnaire = relationship("Questionnaire", back_populates="distributions")
    user = relationship("User", foreign_keys=[user_id], back_populates="received_questionnaires")
    distributor = relationship("User", foreign_keys=[distributor_id], back_populates="distributed_questionnaires")

# 在所有模型定义完成后再添加关系
def setup_relationships():
    # 导入模型类，避免循环导入
    from app.models.user import User
    from app.models.assessment import Assessment
    from app.models.questionnaire import Questionnaire
    
    # 添加评估量表分发关系
    AssessmentDistribution.assessment = relationship("Assessment", back_populates="distributions")
    AssessmentDistribution.user = relationship("User", foreign_keys=[AssessmentDistribution.user_id], back_populates="received_assessments")
    AssessmentDistribution.distributor = relationship("User", foreign_keys=[AssessmentDistribution.distributor_id], back_populates="distributed_assessments")
    
    # 添加问卷分发关系
    QuestionnaireDistribution.questionnaire = relationship("Questionnaire", back_populates="distributions")
    QuestionnaireDistribution.user = relationship("User", foreign_keys=[QuestionnaireDistribution.user_id], back_populates="received_questionnaires")
    QuestionnaireDistribution.distributor = relationship("User", foreign_keys=[QuestionnaireDistribution.distributor_id], back_populates="distributed_questionnaires")
    
    # 添加User模型关系
    User.received_assessments = relationship("AssessmentDistribution", back_populates="user", foreign_keys="AssessmentDistribution.user_id")
    User.distributed_assessments = relationship("AssessmentDistribution", back_populates="distributor", foreign_keys="AssessmentDistribution.distributor_id")
    User.received_questionnaires = relationship("QuestionnaireDistribution", back_populates="user", foreign_keys="QuestionnaireDistribution.user_id")
    User.distributed_questionnaires = relationship("QuestionnaireDistribution", back_populates="distributor", foreign_keys="QuestionnaireDistribution.distributor_id")
    
    # 添加Assessment和Questionnaire模型关系
    Assessment.distributions = relationship("AssessmentDistribution", back_populates="assessment", cascade="all, delete-orphan")
    Questionnaire.distributions = relationship("QuestionnaireDistribution", back_populates="questionnaire", cascade="all, delete-orphan")