<template>
  <div class="permission-management-container">
    <h1>权限管理</h1>

    <el-tabs v-model="activeTab">
      <el-tab-pane label="角色权限设置" name="role-permissions">
        <el-card class="permission-card">
          <template #header>
            <div class="card-header">
              <span>角色权限配置</span>
            </div>
          </template>

          <el-table :data="roles" v-loading="loading" border style="width: 100%">
            <el-table-column prop="name" label="角色名称" width="180">
              <template #default="scope">
                {{ getRoleLabel(scope.row.name) }}
              </template>
            </el-table-column>
            <el-table-column prop="description" label="角色描述" width="220" />
            <el-table-column label="移动端权限">
              <template #default="scope">
                <el-checkbox-group v-model="scope.row.mobilePermissions" @change="handlePermissionChange(scope.row)">
                  <el-checkbox v-for="perm in mobilePermissions" :key="perm.id" :value="perm.id">
                    {{ perm.name }}
                  </el-checkbox>
                </el-checkbox-group>
              </template>
            </el-table-column>
            <el-table-column label="后端权限">
              <template #default="scope">
                <el-checkbox-group v-model="scope.row.backendPermissions" @change="handlePermissionChange(scope.row)">
                  <el-checkbox v-for="perm in backendPermissions" :key="perm.id" :value="perm.id">
                    {{ perm.name }}
                  </el-checkbox>
                </el-checkbox-group>
              </template>
            </el-table-column>
            <el-table-column label="前端权限">
              <template #default="scope">
                <el-checkbox-group v-model="scope.row.frontendPermissions" @change="handlePermissionChange(scope.row)">
                  <el-checkbox v-for="perm in frontendPermissions" :key="perm.id" :value="perm.id">
                    {{ perm.name }}
                  </el-checkbox>
                </el-checkbox-group>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>

      <el-tab-pane label="角色审批" name="role-approval">
        <el-card class="approval-card">
          <template #header>
            <div class="card-header">
              <span>角色审批</span>
              <div class="card-actions">
                <el-select v-model="approvalFilter" placeholder="筛选状态" style="width: 120px">
                  <el-option label="全部" value="" />
                  <el-option label="待审批" value="pending" />
                  <el-option label="已批准" value="approved" />
                  <el-option label="已拒绝" value="rejected" />
                </el-select>
              </div>
            </div>
          </template>

          <el-table :data="filteredApprovals" v-loading="loadingApprovals" border style="width: 100%">
            <el-table-column prop="user.username" label="用户名" width="120" />
            <el-table-column prop="user.full_name" label="姓名" width="120" />
            <el-table-column prop="user.custom_id" label="用户ID" width="120" />
            <el-table-column prop="current_role" label="当前角色" width="120">
              <template #default="scope">
                {{ getRoleLabel(scope.row.current_role) }}
              </template>
            </el-table-column>
            <el-table-column prop="requested_role" label="申请角色" width="120">
              <template #default="scope">
                {{ getRoleLabel(scope.row.requested_role) }}
              </template>
            </el-table-column>
            <el-table-column prop="request_date" label="申请日期" width="150" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ getStatusLabel(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="申请理由" width="150">
              <template #default="scope">
                <span v-if="scope.row.reason">{{ scope.row.reason }}</span>
                <span v-else class="text-muted">无</span>
              </template>
            </el-table-column>
            <el-table-column label="证书" width="150">
              <template #default="scope">
                <div v-if="scope.row.documents && scope.row.documents.length > 0">
                  <el-button type="primary" size="small" @click="viewCertificates(scope.row)">
                    查看证书 ({{ scope.row.documents.length }})
                  </el-button>
                </div>
                <span v-else class="text-muted">无</span>
              </template>
            </el-table-column>
            <el-table-column label="处理信息" width="200">
              <template #default="scope">
                <div v-if="scope.row.status !== 'pending'">
                  <div v-if="scope.row.processed_by">处理人: {{ scope.row.processed_by }}</div>
                  <div v-if="scope.row.processed_at">处理时间: {{ scope.row.processed_at }}</div>
                </div>
                <span v-else class="text-muted">未处理</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="scope">
                <el-button v-if="scope.row.status === 'pending'" type="success" size="small"
                  @click="handleApprove(scope.row)">
                  批准
                </el-button>
                <el-button v-if="scope.row.status === 'pending'" type="danger" size="small"
                  @click="handleReject(scope.row)">
                  拒绝
                </el-button>
                <el-button v-if="scope.row.status !== 'pending'" type="primary" size="small"
                  @click="viewApprovalDetails(scope.row)">
                  查看详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 证书查看对话框 -->
          <el-dialog v-model="certificateDialogVisible" title="申请证书" width="70%"
            :before-close="handleCertificateDialogClose">
            <div v-if="selectedApproval">
              <h3>{{ selectedApproval.user.full_name }} 的证书</h3>
              <el-tabs v-model="certificateActiveTab">
                <el-tab-pane label="证书列表" name="list">
                  <el-table :data="certificateList" border style="width: 100%">
                    <el-table-column prop="id" label="ID" width="80" />
                    <el-table-column prop="filename" label="文件名" width="200" />
                    <el-table-column prop="document_category" label="类型" width="150">
                      <template #default="scope">
                        {{ getDocumentCategoryLabel(scope.row.document_category) }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="created_at" label="上传时间" width="180" />
                    <el-table-column label="操作" width="200">
                      <template #default="scope">
                        <el-button type="primary" size="small" @click="previewDocument(scope.row)">
                          预览
                        </el-button>
                        <el-button type="success" size="small" @click="downloadDocument(scope.row)">
                          下载
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <div v-if="certificateList.length === 0" class="empty-data">
                    <el-empty description="暂无证书数据" />
                  </div>
                </el-tab-pane>
                <el-tab-pane label="证书预览" name="preview" v-if="previewUrl">
                  <div class="document-preview">
                    <!-- 文档操作按钮 -->
                    <div class="document-actions" style="margin-bottom: 10px; text-align: center;">
                      <el-button type="primary" @click="downloadCurrentDocument">下载文件</el-button>
                      <el-button type="success" @click="openInNewWindow">在新窗口中打开</el-button>
                    </div>

                    <!-- 使用object标签替代iframe，更好地支持各种文件类型 -->
                    <object :data="previewUrl" type="application/pdf" width="100%" height="600" v-if="isPreviewPdf">
                      <div class="preview-fallback">
                        <p>无法在浏览器中预览此PDF文件，请<a :href="previewUrl" target="_blank">点击此处</a>在新窗口中打开，或下载后查看。</p>
                      </div>
                    </object>

                    <!-- 图片预览 -->
                    <img :src="previewUrl" v-if="isPreviewImage"
                      style="max-width: 100%; max-height: 600px; object-fit: contain;" @load="imageLoaded"
                      @error="handleIframeError" />

                    <!-- 其他文件类型使用iframe -->
                    <iframe :src="previewUrl" width="100%" height="600" frameborder="0" @load="handleIframeLoad"
                      @error="handleIframeError" v-if="!isPreviewPdf && !isPreviewImage"></iframe>

                    <!-- 错误信息显示 -->
                    <div v-if="previewError" class="preview-error">
                      <el-alert type="error" :title="previewError" :closable="false" show-icon />
                      <el-button type="primary" size="small" style="margin-top: 10px"
                        @click="retryPreview">重试</el-button>
                      <el-button type="success" size="small" style="margin-top: 10px; margin-left: 10px"
                        @click="downloadCurrentDocument">下载文件</el-button>
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </el-dialog>

          <!-- 申请详情对话框 -->
          <el-dialog v-model="approvalDetailsDialogVisible" title="申请详情" width="50%">
            <div v-if="selectedApproval" class="approval-details">
              <el-descriptions :column="1" border>
                <el-descriptions-item label="用户名">{{ selectedApproval.user.username }}</el-descriptions-item>
                <el-descriptions-item label="姓名">{{ selectedApproval.user.full_name }}</el-descriptions-item>
                <el-descriptions-item label="用户ID">{{ selectedApproval.user.custom_id }}</el-descriptions-item>
                <el-descriptions-item label="当前角色">{{ getRoleLabel(selectedApproval.current_role)
                  }}</el-descriptions-item>
                <el-descriptions-item label="申请角色">{{ getRoleLabel(selectedApproval.requested_role)
                  }}</el-descriptions-item>
                <el-descriptions-item label="申请日期">{{ selectedApproval.request_date }}</el-descriptions-item>
                <el-descriptions-item label="状态">
                  <el-tag :type="getStatusType(selectedApproval.status)">
                    {{ getStatusLabel(selectedApproval.status) }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="申请理由">{{ selectedApproval.reason || '无' }}</el-descriptions-item>
                <el-descriptions-item label="处理人">{{ selectedApproval.processed_by || '无' }}</el-descriptions-item>
                <el-descriptions-item label="处理时间">{{ selectedApproval.processed_at || '无' }}</el-descriptions-item>
                <el-descriptions-item label="证书">
                  <el-button v-if="selectedApproval.documents && selectedApproval.documents.length > 0" type="primary"
                    size="small" @click="viewCertificates(selectedApproval)">
                    查看证书 ({{ selectedApproval.documents.length }})
                  </el-button>
                  <span v-else>无</span>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-dialog>
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from 'axios'
import { useUserStore } from '../store/user'

// 用户状态
const userStore = useUserStore()

// 标签页
const activeTab = ref('role-permissions')

// 加载状态
const loading = ref(false)
const loadingApprovals = ref(false)

// 角色列表
const roles = ref([])

// 权限列表
const mobilePermissions = ref([
  { id: 'mobile_upload_document', name: '上传文档' },
  { id: 'mobile_view_document', name: '查看文档' },
  { id: 'mobile_ocr_process', name: 'OCR处理' },
  { id: 'mobile_health_data', name: '健康数据' },
  { id: 'mobile_questionnaire', name: '问卷调查' }
])

const backendPermissions = ref([
  { id: 'backend_user_management', name: '用户管理' },
  { id: 'backend_document_management', name: '文档管理' },
  { id: 'backend_health_data', name: '健康数据' },
  { id: 'backend_system_settings', name: '系统设置' },
  { id: 'backend_alerts', name: '系统告警' }
])

const frontendPermissions = ref([
  { id: 'frontend_dashboard', name: '仪表盘' },
  { id: 'frontend_user_management', name: '用户管理' },
  { id: 'frontend_document_management', name: '文档管理' },
  { id: 'frontend_health_data', name: '健康数据' },
  { id: 'frontend_system_settings', name: '系统设置' },
  { id: 'frontend_performance', name: '性能监控' },
  { id: 'frontend_alerts', name: '系统告警' }
])

// 角色审批列表
const approvals = ref([])
const approvalFilter = ref('')

// 证书查看对话框
const certificateDialogVisible = ref(false)
const certificateActiveTab = ref('list')
const selectedApproval = ref(null)
const certificateList = ref([])
const previewUrl = ref('')

// 申请详情对话框
const approvalDetailsDialogVisible = ref(false)

// 过滤后的审批列表
const filteredApprovals = computed(() => {
  if (!approvalFilter.value) {
    return approvals.value
  }
  return approvals.value.filter(item => item.status === approvalFilter.value)
})

// 获取角色标签
const getRoleLabel = (role) => {
  const roleMap = {
    'personal': '个人用户',
    'family': '家庭用户',
    'doctor': '医生',
    'consultant': '健康顾问',
    'health_advisor': '健康顾问',
    'unit_admin': '单位管理员',
    'family_admin': '家庭管理员',
    'admin': '管理员',
    'super_admin': '超级管理员'
  }
  return roleMap[role] || role
}

// 获取状态标签
const getStatusLabel = (status) => {
  const statusMap = {
    'pending': '待审批',
    'approved': '已批准',
    'rejected': '已拒绝'
  }
  return statusMap[status] || status
}

// 获取状态类型
const getStatusType = (status) => {
  const typeMap = {
    'pending': 'warning',
    'approved': 'success',
    'rejected': 'danger'
  }
  return typeMap[status] || 'info'
}

// 处理权限变更
const handlePermissionChange = async (role) => {
  try {
    loading.value = true

    // 构建权限数据
    const permissionData = {
      role: role.name,
      permissions: {
        mobile: role.mobilePermissions,
        backend: role.backendPermissions,
        frontend: role.frontendPermissions
      }
    }

    // 发送请求
    const response = await axios.post('/api/permissions/update', permissionData)

    if (response.data.status === 'success') {
      ElMessage.success('权限更新成功')
    } else {
      ElMessage.error(response.data.message || '权限更新失败')
    }
  } catch (error) {
    console.error('更新权限失败:', error)
    ElMessage.error('更新权限失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 处理批准
const handleApprove = async (approval) => {
  try {
    loadingApprovals.value = true

    // 确认操作
    await ElMessageBox.confirm(
      `确定要批准 ${approval.user.full_name} 的 ${getRoleLabel(approval.requested_role)} 角色申请吗？

注意：角色审批将逐级进行，不会超过申请的最高级别。`,
      '确认批准角色申请',
      {
        confirmButtonText: '确定批准',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 发送请求
    const response = await axios.post(`/api/role-applications/applications/${approval.id}/process`, { 
      status: 'approved',
      comment: '批准申请' 
    })

    if (response.data.status === 'success') {
      ElMessage.success(response.data.message || '已批准角色申请')

      // 立即更新本地状态，以便UI立即反应
      approval.status = 'approved'
      approval.processed_by = userStore.username
      approval.processed_at = new Date().toLocaleString()

      // 然后重新加载审批列表，确保显示最新的处理状态和处理人信息
      setTimeout(() => {
        loadApprovals()
      }, 500)
    } else {
      ElMessage.error(response.data.message || '操作失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批准角色失败:', error)
      ElMessage.error('批准角色失败: ' + (error.message || '未知错误'))
    }
  } finally {
    loadingApprovals.value = false
  }
}

// 处理拒绝
const handleReject = async (approval) => {
  try {
    loadingApprovals.value = true

    // 确认操作
    await ElMessageBox.confirm(
      `确定要拒绝 ${approval.user.full_name} 的 ${getRoleLabel(approval.requested_role)} 角色申请吗？

拒绝后，用户将保持当前角色 ${getRoleLabel(approval.current_role)}。`,
      '确认拒绝角色申请',
      {
        confirmButtonText: '确定拒绝',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 发送请求
    const response = await axios.post(`/api/role-applications/applications/${approval.id}/process`, { 
      status: 'rejected',
      comment: '拒绝申请' 
    })

    if (response.data.status === 'success') {
      ElMessage.success(response.data.message || '已拒绝角色申请')

      // 立即更新本地状态，以便UI立即反应
      approval.status = 'rejected'
      approval.processed_by = userStore.username
      approval.processed_at = new Date().toLocaleString()

      // 然后重新加载审批列表，确保显示最新的处理状态和处理人信息
      setTimeout(() => {
        loadApprovals()
      }, 500)
    } else {
      ElMessage.error(response.data.message || '操作失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('拒绝角色失败:', error)
      ElMessage.error('拒绝角色失败: ' + (error.message || '未知错误'))
    }
  } finally {
    loadingApprovals.value = false
  }
}

// 加载角色和权限数据
const loadRolesAndPermissions = async () => {
  try {
    loading.value = true

    // 从API获取真实数据
    const response = await axios.get('/api/permissions/permissions/roles')
    
    // 验证返回数据格式
    if (response.data && Array.isArray(response.data)) {
      roles.value = response.data
      console.log('获取到的角色权限数据:', roles.value)
    } else {
      console.warn('角色权限数据格式不正确:', response.data)
      throw new Error('数据格式不正确');
    }
  } catch (error) {
    console.error('加载角色和权限失败:', error)
    const errorMsg = error.response?.data?.message || error.message || '未知错误';
    ElMessage.error(`加载角色和权限失败: ${errorMsg}`)

    // 如果API请求失败，使用模拟数据进行测试
    roles.value = [
      {
        name: 'personal',
        description: '个人用户',
        mobilePermissions: ['mobile_upload_document', 'mobile_view_document', 'mobile_health_data'],
        backendPermissions: [],
        frontendPermissions: []
      },
      {
        name: 'doctor',
        description: '医生',
        mobilePermissions: ['mobile_upload_document', 'mobile_view_document', 'mobile_ocr_process', 'mobile_health_data'],
        backendPermissions: ['backend_document_management', 'backend_health_data'],
        frontendPermissions: ['frontend_dashboard', 'frontend_document_management', 'frontend_health_data']
      },
      {
        name: 'consultant',
        description: '健康顾问',
        mobilePermissions: ['mobile_upload_document', 'mobile_view_document', 'mobile_ocr_process', 'mobile_health_data', 'mobile_questionnaire'],
        backendPermissions: ['backend_document_management', 'backend_health_data'],
        frontendPermissions: ['frontend_dashboard', 'frontend_document_management', 'frontend_health_data']
      },
      {
        name: 'unit_admin',
        description: '单位管理员',
        mobilePermissions: ['mobile_upload_document', 'mobile_view_document', 'mobile_ocr_process', 'mobile_health_data', 'mobile_questionnaire'],
        backendPermissions: ['backend_user_management', 'backend_document_management', 'backend_health_data', 'backend_alerts'],
        frontendPermissions: ['frontend_dashboard', 'frontend_user_management', 'frontend_document_management', 'frontend_health_data', 'frontend_alerts']
      },
      {
        name: 'super_admin',
        description: '超级管理员',
        mobilePermissions: ['mobile_upload_document', 'mobile_view_document', 'mobile_ocr_process', 'mobile_health_data', 'mobile_questionnaire'],
        backendPermissions: ['backend_user_management', 'backend_document_management', 'backend_health_data', 'backend_system_settings', 'backend_alerts'],
        frontendPermissions: ['frontend_dashboard', 'frontend_user_management', 'frontend_document_management', 'frontend_health_data', 'frontend_system_settings', 'frontend_performance', 'frontend_alerts']
      }
    ]
  } finally {
    loading.value = false
  }
}

// 获取文档类型标签
const getDocumentCategoryLabel = (category) => {
  const categoryMap = {
    'medical_record': '病历',
    'lab_report': '检验报告',
    'imaging_report': '影像报告',
    'prescription': '处方',
    'discharge_summary': '出院小结',
    'referral': '转诊单',
    'consent_form': '知情同意书',
    'insurance': '保险文件',
    'vaccination': '疫苗接种记录',
    'allergy': '过敏记录',
    'health_certificate': '健康证明',
    'other': '其他'
  }
  return categoryMap[category] || category
}

// 查看证书
const viewCertificates = async (approval) => {
  try {
    selectedApproval.value = approval
    certificateDialogVisible.value = true
    certificateActiveTab.value = 'list'
    certificateList.value = []
    previewUrl.value = ''

    // 检查是否有证书
    if (!approval.documents || approval.documents.length === 0) {
      ElMessage.warning('该申请没有上传证书')
      return
    }

    // 如果documents已经是完整对象数组，直接使用
    if (typeof approval.documents[0] === 'object' && approval.documents[0].id) {
      certificateList.value = approval.documents
      console.log('使用已有的证书列表:', certificateList.value)
      return
    }

    // 加载证书列表
    const documentIds = approval.documents
    const promises = documentIds.map(async (docId) => {
      try {
        // 使用相对路径，让axios处理基础URL
        const response = await axios.get(`/api/documents/${docId}`)
        return response.data
      } catch (error) {
        console.error(`获取文档 ${docId} 失败:`, error)
        ElMessage.error(`获取文档失败: ${error.message || '未知错误'}`)
        return null
      }
    })

    const results = await Promise.all(promises)
    certificateList.value = results.filter(doc => doc !== null)

    console.log('获取到的证书列表:', certificateList.value)
  } catch (error) {
    console.error('查看证书失败:', error)
    ElMessage.error('查看证书失败: ' + (error.message || '未知错误'))
  }
}

// 预览相关状态
const previewError = ref('')
const currentPreviewDocument = ref(null)
const isPreviewPdf = ref(false)
const isPreviewImage = ref(false)

// 预览文档
const previewDocument = (document) => {
  try {
    // 保存当前预览的文档，用于重试
    currentPreviewDocument.value = document
    previewError.value = ''

    // 根据文件类型设置预览方式
    const filename = document.filename.toLowerCase()
    isPreviewPdf.value = filename.endsWith('.pdf')
    isPreviewImage.value = filename.endsWith('.jpg') || filename.endsWith('.jpeg') ||
      filename.endsWith('.png') || filename.endsWith('.gif') ||
      filename.endsWith('.bmp') || filename.endsWith('.webp')

    // 设置预览URL，添加更强的随机参数防止缓存
    const randomParam = Date.now() + '-' + Math.random().toString(36).substring(2, 15)
    previewUrl.value = `/api/documents/${document.id}/preview?nocache=${randomParam}`
    certificateActiveTab.value = 'preview'

    console.log('预览文档:', document.filename, '预览URL:', previewUrl.value, '文件类型:', isPreviewPdf.value ? 'PDF' : isPreviewImage.value ? '图片' : '其他')

    // 如果是PDF，尝试直接在新窗口打开，但也保留在当前页面的预览
    if (isPreviewPdf.value) {
      // 使用fetch先检查文件是否可访问
      fetch(previewUrl.value, { method: 'HEAD' })
        .then(response => {
          if (response.ok) {
            // 文件可访问，在新窗口打开
            window.open(previewUrl.value, '_blank')
          } else {
            throw new Error(`文件访问失败: ${response.status} ${response.statusText}`)
          }
        })
        .catch(error => {
          console.error('PDF文件访问检查失败:', error)
          previewError.value = '无法访问PDF文件，请检查网络连接或文件是否存在'
          ElMessage.error('PDF文件访问失败: ' + (error.message || '未知错误'))
        })
    }
  } catch (error) {
    console.error('预览文档失败:', error)
    previewError.value = '预览文档失败: ' + (error.message || '未知错误')
    ElMessage.error('预览文档失败: ' + (error.message || '未知错误'))
  }
}

// 图片加载成功
const imageLoaded = () => {
  previewError.value = ''
  console.log('图片加载成功')
}

// 处理iframe加载事件
const handleIframeLoad = (event) => {
  try {
    // 检查iframe是否加载了有效内容
    const iframe = event.target
    // 尝试访问iframe内容，如果出错说明跨域或内容为空
    if (iframe.contentDocument && iframe.contentDocument.body) {
      const content = iframe.contentDocument.body.innerHTML
      if (content.includes('error') || content.includes('Error') || content.includes('失败') || content === '') {
        previewError.value = '文档预览失败，可能是格式不支持或文件已损坏'
      } else {
        previewError.value = ''
      }
    }
  } catch (error) {
    // 跨域错误会在这里捕获
    console.log('iframe加载完成，但无法检查内容（可能是跨域限制）')
  }
}

// 处理iframe错误事件
const handleIframeError = () => {
  previewError.value = '文档加载失败，请检查网络连接或文件是否存在'
}

// 重试预览
const retryPreview = () => {
  if (currentPreviewDocument.value) {
    previewDocument(currentPreviewDocument.value)
  }
}


// 下载当前预览的文档
const downloadCurrentDocument = () => {
  if (currentPreviewDocument.value) {
    downloadDocument(currentPreviewDocument.value)
  } else {
    ElMessage.warning('没有可下载的文档')
  }
}

// 下载文档
const downloadDocument = (document) => {
  try {
    // 构建下载URL，使用相对路径，添加更强的随机参数防止缓存
    const randomParam = Date.now() + '-' + Math.random().toString(36).substring(2, 15)
    const downloadUrl = `/api/documents/${document.id}/download?nocache=${randomParam}`

    ElMessage.info('正在准备下载文件...')

    // 先使用fetch检查文件是否可访问
    fetch(downloadUrl, { method: 'HEAD' })
      .then(response => {
        if (response.ok) {
          // 文件可访问，创建一个隐藏的iframe用于下载
          const downloadFrame = document.createElement('iframe')
          downloadFrame.style.display = 'none'
          downloadFrame.src = downloadUrl
          document.body.appendChild(downloadFrame)

          // 设置超时检查下载是否开始
          setTimeout(() => {
            document.body.removeChild(downloadFrame)
            ElMessage.success(`文件"${document.filename}"下载已开始，如果没有自动下载，请点击重试`)
          }, 2000)

          console.log('下载文档:', document.filename, '下载URL:', downloadUrl)
        } else {
          throw new Error(`文件访问失败: ${response.status} ${response.statusText}`)
        }
      })
      .catch(error => {
        console.error('文件访问检查失败:', error)
        ElMessage.error('文件访问失败: ' + (error.message || '未知错误'))
      })
  } catch (error) {
    console.error('下载文档失败:', error)
    ElMessage.error('下载文档失败: ' + (error.message || '未知错误'))
  }
}

// 在新窗口中打开
const openInNewWindow = () => {
  if (previewUrl.value) {
    window.open(previewUrl.value, '_blank')
  }
}

// 关闭证书对话框
const handleCertificateDialogClose = () => {
  certificateDialogVisible.value = false
  selectedApproval.value = null
  certificateList.value = []
  previewUrl.value = ''
}

// 查看申请详情
const viewApprovalDetails = (approval) => {
  selectedApproval.value = approval
  approvalDetailsDialogVisible.value = true
}

// 加载审批列表
const loadApprovals = async () => {
  try {
    loadingApprovals.value = true

    // 从API获取真实数据
    const response = await axios.get('/api/role-applications/applications')
    
    // 验证返回数据格式
    if (response.data && Array.isArray(response.data)) {
      approvals.value = response.data
      console.log('获取到的角色审批数据:', approvals.value)
    } else {
      console.warn('角色审批数据格式不正确:', response.data)
      approvals.value = []
      ElMessage.warning('角色审批数据格式异常，已重置为空列表')
    }
  } catch (error) {
    console.error('加载角色审批数据失败:', error)
    const errorMsg = error.response?.data?.message || error.message || '未知错误';
    ElMessage.error(`加载角色审批数据失败: ${errorMsg}`)
    approvals.value = []
  } finally {
    loadingApprovals.value = false
  }
}

// 生命周期钩子
onMounted(async () => {
  await loadRolesAndPermissions()
  await loadApprovals()
})
</script>

<style scoped>
.permission-management-container {
  padding: 20px;
}

.permission-card,
.approval-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-actions {
  display: flex;
  gap: 10px;
}

.el-checkbox-group {
  display: flex;
  flex-direction: column;
}

.preview-fallback {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 20px;
  text-align: center;
  height: 300px;
}

.preview-fallback a {
  color: #409eff;
  text-decoration: underline;
  margin: 10px 0;
}

.text-muted {
  color: #909399;
}

.approval-details {
  padding: 10px;
}

.document-preview {
  position: relative;
}

.preview-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(255, 255, 255, 0.9);
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  text-align: center;
  min-width: 300px;
}

.preview-fallback {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 600px;
  background-color: #f5f7fa;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  text-align: center;
  padding: 20px;
}

.preview-fallback a {
  color: #409eff;
  text-decoration: underline;
  margin: 0 5px;
}
</style>
