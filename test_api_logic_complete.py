#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整模拟API查询逻辑
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'YUN', 'backend'))

from sqlalchemy import create_engine, desc
from sqlalchemy.orm import sessionmaker
from app.models.assessment import Assessment, AssessmentTemplate
from app.models.distribution import AssessmentDistribution

def test_api_logic():
    """完整模拟API查询逻辑"""
    print("完整模拟API查询逻辑...")
    
    # 创建数据库连接
    engine = create_engine('sqlite:///YUN/backend/app.db')
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # 模拟API参数
        custom_id = "SM_006"
        status = None  # 不过滤状态
        skip = 0
        limit = 20
        
        print(f"查询参数: custom_id={custom_id}, status={status}, skip={skip}, limit={limit}")
        
        # 完全模拟API逻辑
        print("\n=== 模拟API查询逻辑 ===")
        
        # 1. 构建查询
        query = db.query(Assessment).outerjoin(AssessmentDistribution)
        print(f"初始查询构建完成")
        
        # 2. 添加custom_id过滤
        if custom_id:
            query = query.filter(Assessment.custom_id == custom_id)
            print(f"添加custom_id过滤: {custom_id}")
        
        # 3. 添加status过滤
        if status:
            query = query.filter(Assessment.status == status)
            print(f"添加status过滤: {status}")
        else:
            print("未添加status过滤")
        
        # 4. 添加排序
        query = query.order_by(desc(Assessment.created_at))
        print("添加排序: created_at DESC")
        
        # 5. 计算总数
        total = query.count()
        print(f"查询总数: {total}")
        
        # 6. 应用分页
        assessments = query.offset(skip).limit(limit).all()
        print(f"分页后结果数量: {len(assessments)}")
        
        # 7. 显示结果
        print("\n=== 查询结果 ===")
        for i, assessment in enumerate(assessments):
            print(f"  {i+1}. ID: {assessment.id}, 名称: {assessment.name}, 状态: {assessment.status}, 创建时间: {assessment.created_at}")
        
        # 8. 检查是否有重复记录问题
        print("\n=== 检查重复记录 ===")
        distinct_query = db.query(Assessment).outerjoin(AssessmentDistribution)
        if custom_id:
            distinct_query = distinct_query.filter(Assessment.custom_id == custom_id)
        distinct_query = distinct_query.distinct(Assessment.id).order_by(desc(Assessment.created_at))
        distinct_assessments = distinct_query.offset(skip).limit(limit).all()
        print(f"去重后结果数量: {len(distinct_assessments)}")
        
        for i, assessment in enumerate(distinct_assessments):
            print(f"  {i+1}. ID: {assessment.id}, 名称: {assessment.name}, 状态: {assessment.status}, 创建时间: {assessment.created_at}")
        
        # 9. 检查join是否产生重复
        print("\n=== 检查join重复问题 ===")
        # 查看每个assessment对应的distribution记录数
        for assessment in assessments:
            dist_count = db.query(AssessmentDistribution).filter(AssessmentDistribution.assessment_id == assessment.id).count()
            print(f"  Assessment ID {assessment.id} ({assessment.name}) 对应的分发记录数: {dist_count}")
        
        # 10. 测试不同的查询方式
        print("\n=== 测试不使用join的查询 ===")
        simple_query = db.query(Assessment)
        if custom_id:
            simple_query = simple_query.filter(Assessment.custom_id == custom_id)
        if status:
            simple_query = simple_query.filter(Assessment.status == status)
        simple_query = simple_query.order_by(desc(Assessment.created_at))
        simple_assessments = simple_query.offset(skip).limit(limit).all()
        print(f"不使用join的结果数量: {len(simple_assessments)}")
        
        for i, assessment in enumerate(simple_assessments):
            print(f"  {i+1}. ID: {assessment.id}, 名称: {assessment.name}, 状态: {assessment.status}, 创建时间: {assessment.created_at}")
            
    except Exception as e:
        print(f"查询出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    test_api_logic()