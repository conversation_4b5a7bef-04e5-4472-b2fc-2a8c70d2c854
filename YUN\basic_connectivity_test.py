#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基本连接测试
测试前端、后端、数据库之间的连通性
"""

import requests
import sqlite3
import os
import json
from datetime import datetime

def test_backend_api():
    """测试后端API连接"""
    print("\n=== 后端API测试 ===")
    
    endpoints = [
        {
            "name": "API文档",
            "url": "http://localhost:8006/docs",
            "expect_html": True
        },
        {
            "name": "健康检查",
            "url": "http://localhost:8006/api/health",
            "expect_json": True
        },
        {
            "name": "聚合API健康检查",
            "url": "http://localhost:8006/api/v1/aggregated/health",
            "expect_json": True
        },
        {
            "name": "数据管理API健康检查",
            "url": "http://localhost:8006/api/data-management/health",
            "expect_json": True
        }
    ]
    
    results = []
    
    for endpoint in endpoints:
        try:
            print(f"\n测试: {endpoint['name']}")
            print(f"URL: {endpoint['url']}")
            
            response = requests.get(endpoint['url'], timeout=10)
            
            if response.status_code == 200:
                print(f"✅ 状态码: {response.status_code}")
                
                if endpoint.get('expect_json'):
                    try:
                        data = response.json()
                        print(f"响应数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
                        results.append({"endpoint": endpoint['name'], "status": "success", "data": data})
                    except:
                        print(f"响应文本: {response.text[:200]}...")
                        results.append({"endpoint": endpoint['name'], "status": "success", "note": "非JSON响应"})
                else:
                    print(f"响应长度: {len(response.text)} 字符")
                    results.append({"endpoint": endpoint['name'], "status": "success"})
            else:
                print(f"❌ 状态码: {response.status_code}")
                results.append({"endpoint": endpoint['name'], "status": "failed", "status_code": response.status_code})
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求异常: {e}")
            results.append({"endpoint": endpoint['name'], "status": "error", "error": str(e)})
    
    return results

def test_frontend():
    """测试前端连接"""
    print("\n=== 前端测试 ===")
    
    try:
        print("测试前端页面访问...")
        response = requests.get("http://localhost:8080/", timeout=10)
        
        if response.status_code == 200:
            print(f"✅ 前端页面访问成功，状态码: {response.status_code}")
            print(f"页面大小: {len(response.text)} 字符")
            
            # 检查页面内容
            if "健康管理" in response.text or "登录" in response.text:
                print("✅ 页面内容正常")
                return {"status": "success", "note": "页面访问正常"}
            else:
                print("⚠️ 页面内容可能异常")
                return {"status": "warning", "note": "页面内容可能异常"}
        else:
            print(f"❌ 前端页面访问失败，状态码: {response.status_code}")
            return {"status": "failed", "status_code": response.status_code}
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 前端连接异常: {e}")
        return {"status": "error", "error": str(e)}

def test_database():
    """测试数据库连接"""
    print("\n=== 数据库测试 ===")
    
    db_path = "backend/app.db"
    
    try:
        if not os.path.exists(db_path):
            print(f"❌ 数据库文件不存在: {db_path}")
            return {"status": "error", "error": "数据库文件不存在"}
        
        print(f"数据库文件存在: {db_path}")
        print(f"文件大小: {os.path.getsize(db_path)} 字节")
        
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取表列表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"✅ 数据库连接成功")
        print(f"表数量: {len(tables)}")
        print(f"表列表: {[table[0] for table in tables[:10]]}")
        
        # 测试一些关键表
        key_tables = ['users', 'questionnaire_templates', 'assessment_templates']
        table_info = {}
        
        for table in key_tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                table_info[table] = count
                print(f"表 {table}: {count} 条记录")
            except sqlite3.OperationalError:
                print(f"表 {table}: 不存在")
                table_info[table] = "不存在"
        
        conn.close()
        
        return {
            "status": "success", 
            "tables_count": len(tables),
            "key_tables": table_info
        }
        
    except Exception as e:
        print(f"❌ 数据库连接异常: {e}")
        return {"status": "error", "error": str(e)}

def main():
    """主测试函数"""
    print("开始基本连接测试...")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 运行所有测试
    backend_results = test_backend_api()
    frontend_result = test_frontend()
    database_result = test_database()
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print("=" * 60)
    
    print(f"\n前端状态: {frontend_result['status']}")
    print(f"数据库状态: {database_result['status']}")
    
    print("\n后端API状态:")
    for result in backend_results:
        print(f"  - {result['endpoint']}: {result['status']}")
    
    # 生成测试报告
    report = {
        "test_time": datetime.now().isoformat(),
        "frontend": frontend_result,
        "backend": backend_results,
        "database": database_result
    }
    
    with open("connectivity_test_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print("\n测试报告已保存到: connectivity_test_report.json")
    print("测试完成!")

if __name__ == "__main__":
    main()