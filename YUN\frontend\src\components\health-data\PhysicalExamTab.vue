<template>
  <div class="physical-exam-tab">
    <div class="tab-header">
      <h3>体检报告</h3>
      <div class="filter-container">
        <el-select v-model="filterYear" placeholder="年份" clearable @change="filterReports">
          <el-option label="全部" value="" />
          <el-option v-for="year in availableYears" :key="year" :label="year" :value="year" />
        </el-select>
        <el-select v-model="filterHospital" placeholder="体检机构" clearable @change="filterReports">
          <el-option label="全部" value="" />
          <el-option v-for="hospital in availableHospitals" :key="hospital" :label="hospital" :value="hospital" />
        </el-select>
        <el-button type="primary" size="small" @click="refreshData">刷新数据</el-button>
      </div>
    </div>

    <el-table
      :data="filteredReports"
      style="width: 100%"
      v-loading="loading"
      :empty-text="emptyText"
    >
      <el-table-column prop="exam_date" label="体检日期" width="120">
        <template #default="scope">
          {{ formatDate(scope.row.exam_date) }}
        </template>
      </el-table-column>
      <el-table-column prop="hospital_name" label="体检机构" width="180" />
      <el-table-column prop="exam_type" label="体检类型" width="120">
        <template #default="scope">
          {{ getExamTypeLabel(scope.row.exam_type) }}
        </template>
      </el-table-column>
      <el-table-column prop="has_abnormal" label="异常项目" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.has_abnormal ? 'danger' : 'success'">
            {{ scope.row.has_abnormal ? '有' : '无' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="doctor_name" label="体检医生" width="120" />
      <el-table-column prop="summary" label="体检小结" show-overflow-tooltip />
      <el-table-column label="操作" width="120" fixed="right">
        <template #default="scope">
          <el-button type="primary" link @click="viewReport(scope.row)">查看</el-button>
          <el-button type="danger" link @click="deleteReport(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 报告详情对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="80%"
      fullscreen
    >
      <div v-if="currentReport" class="report-detail">
        <div class="report-header">
          <h2>{{ currentReport.hospital_name }} 体检报告</h2>
          <div class="report-meta">
            <div class="meta-item">
              <span class="label">体检日期：</span>
              <span class="value">{{ formatDate(currentReport.exam_date) }}</span>
            </div>
            <div class="meta-item">
              <span class="label">体检类型：</span>
              <span class="value">{{ getExamTypeLabel(currentReport.exam_type) }}</span>
            </div>
            <div class="meta-item">
              <span class="label">体检医生：</span>
              <span class="value">{{ currentReport.doctor_name }}</span>
            </div>
          </div>
        </div>

        <el-divider />

        <div class="report-sections">
          <el-tabs type="border-card">
            <el-tab-pane label="体检总结">
              <div class="summary-section">
                <h4>体检小结</h4>
                <div class="summary-content">{{ currentReport.summary }}</div>
                
                <h4>健康建议</h4>
                <div class="advice-content">{{ currentReport.health_advice }}</div>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="体格检查">
              <el-table :data="currentReport.physical_items || []" border style="width: 100%">
                <el-table-column prop="item_name" label="检查项目" width="180" />
                <el-table-column prop="item_value" label="检查结果" width="180" />
                <el-table-column prop="reference_range" label="参考范围" width="180" />
                <el-table-column prop="is_abnormal" label="是否异常" width="100">
                  <template #default="scope">
                    <el-tag :type="scope.row.is_abnormal ? 'danger' : 'success'">
                      {{ scope.row.is_abnormal ? '异常' : '正常' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="notes" label="备注" />
              </el-table>
            </el-tab-pane>
            
            <el-tab-pane label="实验室检查">
              <el-table :data="currentReport.lab_items || []" border style="width: 100%">
                <el-table-column prop="item_name" label="检查项目" width="180" />
                <el-table-column prop="item_value" label="检查结果" width="120" />
                <el-table-column prop="unit" label="单位" width="80" />
                <el-table-column prop="reference_range" label="参考范围" width="180" />
                <el-table-column prop="is_abnormal" label="是否异常" width="100">
                  <template #default="scope">
                    <el-tag :type="scope.row.is_abnormal ? 'danger' : 'success'">
                      {{ scope.row.is_abnormal ? '异常' : '正常' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="notes" label="备注" />
              </el-table>
            </el-tab-pane>
            
            <el-tab-pane label="影像学检查">
              <el-table :data="currentReport.imaging_items || []" border style="width: 100%">
                <el-table-column prop="item_name" label="检查项目" width="180" />
                <el-table-column prop="item_result" label="检查结果" show-overflow-tooltip />
                <el-table-column prop="is_abnormal" label="是否异常" width="100">
                  <template #default="scope">
                    <el-tag :type="scope.row.is_abnormal ? 'danger' : 'success'">
                      {{ scope.row.is_abnormal ? '异常' : '正常' }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            
            <el-tab-pane label="功能检查">
              <el-table :data="currentReport.function_items || []" border style="width: 100%">
                <el-table-column prop="item_name" label="检查项目" width="180" />
                <el-table-column prop="item_result" label="检查结果" show-overflow-tooltip />
                <el-table-column prop="is_abnormal" label="是否异常" width="100">
                  <template #default="scope">
                    <el-tag :type="scope.row.is_abnormal ? 'danger' : 'success'">
                      {{ scope.row.is_abnormal ? '异常' : '正常' }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import axios from 'axios';

const props = defineProps({
  customId: {
    type: [Number, String],
    required: true
  }
});

const loading = ref(false);
const reports = ref([]);
const filterYear = ref('');
const filterHospital = ref('');
const dialogVisible = ref(false);
const currentReport = ref(null);

const dialogTitle = computed(() => {
  if (!currentReport.value) return '体检报告详情';
  const dateStr = formatDate(currentReport.value.exam_date);
  const hospitalName = currentReport.value.hospital_name;
  return `${hospitalName} 体检报告 (${dateStr})`;
});

const emptyText = computed(() => {
  return loading.value ? '加载中...' : '暂无体检报告数据';
});

const filteredReports = computed(() => {
  let result = reports.value;
  if (filterYear.value) {
    result = result.filter(report => {
      const year = new Date(report.exam_date).getFullYear().toString();
      return year === filterYear.value;
    });
  }
  if (filterHospital.value) {
    result = result.filter(report => report.hospital_name === filterHospital.value);
  }
  return result;
});

const availableYears = computed(() => {
  const years = new Set();
  reports.value.forEach(report => {
    if (report.exam_date) {
      const year = new Date(report.exam_date).getFullYear().toString();
      years.add(year);
    }
  });
  return Array.from(years).sort((a, b) => b - a);
});

const availableHospitals = computed(() => {
  const hospitals = new Set();
  reports.value.forEach(report => {
    if (report.hospital_name) {
      hospitals.add(report.hospital_name);
    }
  });
  return Array.from(hospitals).sort();
});

// 统一聚合接口请求
const fetchUserHealthRecords = async () => {
  if (!props.customId) return;
  loading.value = true;
  try {
    const response = await axios.get(`/api/v1/aggregated/health-profile/${props.customId}`, {
      params: {
        include_physical_exams: true
      }
    });
    const profileData = response.data.profile_data || {};
    reports.value = profileData.physical_exams || [];
  } catch (error) {
    console.error('获取体检报告失败:', error);
    reports.value = [];
  } finally {
    loading.value = false;
  }
};

watch(() => props.customId, (newVal) => {
  if (newVal) {
    fetchUserHealthRecords();
  } else {
    reports.value = [];
  }
}, { immediate: true });

onMounted(() => {
  if (props.customId) {
    fetchUserHealthRecords();
  }
});



// 刷新数据
const refreshData = () => {
  fetchUserHealthRecords();
};

// 筛选报告
const filterReports = () => {
  // 筛选逻辑已通过计算属性实现
};

// 查看报告详情
const viewReport = (report) => {
  currentReport.value = report;
  dialogVisible.value = true;
};

// 删除报告
const deleteReport = (report) => {
  ElMessageBox.confirm(
    `确定要删除 ${formatDate(report.exam_date)} 的体检报告吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await axios.delete(`/api/physical-exams/${report.id}`);
      ElMessage.success('删除成功');
      fetchUserHealthRecords(); // 刷新数据
    } catch (error) {
      console.error('删除体检报告失败:', error);
      ElMessage.error('删除体检报告失败，请稍后重试');
    }
  }).catch(() => {
    // 用户取消删除
  });
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  return date.toLocaleDateString();
};

// 获取体检类型标签
const getExamTypeLabel = (type) => {
  const typeMap = {
    'annual': '年度体检',
    'employment': '入职体检',
    'retirement': '退休体检',
    'special': '专项体检',
    'other': '其他体检'
  };
  
  return typeMap[type] || type;
};
</script>

<style scoped>
.physical-exam-tab {
  padding: 10px;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.tab-header h3 {
  margin: 0;
}

.filter-container {
  display: flex;
  gap: 10px;
}

.report-detail {
  padding: 10px;
}

.report-header {
  margin-bottom: 20px;
}

.report-header h2 {
  margin-top: 0;
  margin-bottom: 15px;
  text-align: center;
  color: #303133;
}

.report-meta {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
}

.meta-item {
  margin: 5px 15px;
}

.meta-item .label {
  font-weight: bold;
  color: #606266;
}

.summary-section {
  padding: 15px;
}

.summary-section h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #303133;
}

.summary-content,
.advice-content {
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 20px;
  white-space: pre-line;
}
</style>
