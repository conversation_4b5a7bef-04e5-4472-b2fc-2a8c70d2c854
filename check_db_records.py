#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

# 数据库路径
db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'YUN', 'backend', 'app.db')

def check_records():
    """检查数据库中的记录"""
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查量表记录
        print("=== 检查量表记录 ===")
        cursor.execute("SELECT id, custom_id, name, status, created_at FROM assessments WHERE custom_id = 'SM_006' ORDER BY id")
        assessments = cursor.fetchall()
        print(f"找到 {len(assessments)} 条量表记录:")
        for i, assessment in enumerate(assessments):
            print(f"  {i+1}. ID: {assessment[0]}, custom_id: {assessment[1]}, 名称: {assessment[2]}, 状态: {assessment[3]}, 创建时间: {assessment[4]}")
        
        # 检查问卷记录
        print("\n=== 检查问卷记录 ===")
        cursor.execute("SELECT id, custom_id, name, status, created_at FROM questionnaires WHERE custom_id = 'SM_006' ORDER BY id")
        questionnaires = cursor.fetchall()
        print(f"找到 {len(questionnaires)} 条问卷记录:")
        for i, questionnaire in enumerate(questionnaires):
            print(f"  {i+1}. ID: {questionnaire[0]}, custom_id: {questionnaire[1]}, 名称: {questionnaire[2]}, 状态: {questionnaire[3]}, 创建时间: {questionnaire[4]}")
        
        # 关闭连接
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"查询数据库时出错: {e}")

if __name__ == "__main__":
    check_records()