<template>
  <div class="health-diary-container">
    <h1>健康日记管理</h1>
    
    <el-card class="filter-card">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="用户">
          <el-select v-model="filterForm.customId" placeholder="选择用户" clearable>
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="user.username"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="类型">
          <el-select v-model="filterForm.diaryType" placeholder="日记类型" clearable>
            <el-option label="一般记录" value="general" />
            <el-option label="症状记录" value="symptom" />
            <el-option label="用药记录" value="medication" />
            <el-option label="饮食记录" value="diet" />
            <el-option label="运动记录" value="exercise" />
            <el-option label="睡眠记录" value="sleep" />
            <el-option label="情绪记录" value="mood" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="重要标记">
          <el-select v-model="filterForm.isImportant" placeholder="是否重要" clearable>
            <el-option label="是" :value="true" />
            <el-option label="否" :value="false" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleFilter">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <el-card class="diaries-card">
      <template #header>
        <div class="card-header">
          <span>健康日记列表</span>
          <el-button type="primary" @click="handleCreate">新增日记</el-button>
        </div>
      </template>
      
      <el-table
        v-loading="loading"
        :data="diaries"
        style="width: 100%"
        border
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="diary_type" label="类型" width="120">
          <template #default="scope">
            <el-tag :type="getDiaryTypeTag(scope.row.diary_type)">
              {{ getDiaryTypeLabel(scope.row.diary_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="标题" width="180" />
        <el-table-column prop="diary_date" label="日期" width="180" />
        <el-table-column prop="mood_level" label="情绪" width="100">
          <template #default="scope">
            <el-rate
              v-model="scope.row.mood_level"
              disabled
              :max="10"
              :colors="['#99A9BF', '#F7BA2A', '#FF9900']"
              text-color="#ff9900"
              score-template="{value}"
            />
          </template>
        </el-table-column>
        <el-table-column prop="energy_level" label="能量" width="100">
          <template #default="scope">
            <el-progress
              :percentage="scope.row.energy_level * 10"
              :color="getEnergyLevelColor(scope.row.energy_level)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="is_important" label="重要" width="80">
          <template #default="scope">
            <el-tag v-if="scope.row.is_important" type="danger">重要</el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="handleView(scope.row)">查看</el-button>
            <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 查看/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'view' ? '查看健康日记' : (dialogType === 'create' ? '新增健康日记' : '编辑健康日记')"
      width="60%"
    >
      <el-form
        ref="diaryForm"
        :model="currentDiary"
        :rules="formRules"
        label-width="100px"
        :disabled="dialogType === 'view'"
      >
        <el-form-item label="日记类型" prop="diary_type">
          <el-select v-model="currentDiary.diary_type" placeholder="请选择日记类型">
            <el-option label="一般记录" value="general" />
            <el-option label="症状记录" value="symptom" />
            <el-option label="用药记录" value="medication" />
            <el-option label="饮食记录" value="diet" />
            <el-option label="运动记录" value="exercise" />
            <el-option label="睡眠记录" value="sleep" />
            <el-option label="情绪记录" value="mood" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="标题" prop="title">
          <el-input v-model="currentDiary.title" placeholder="请输入标题" />
        </el-form-item>
        
        <el-form-item label="日期" prop="diary_date">
          <el-date-picker
            v-model="currentDiary.diary_date"
            type="datetime"
            placeholder="请选择日期"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        
        <el-form-item label="内容">
          <el-input
            v-model="currentDiary.content"
            type="textarea"
            :rows="6"
            placeholder="请输入日记内容"
          />
        </el-form-item>
        
        <el-form-item label="情绪等级">
          <el-rate
            v-model="currentDiary.mood_level"
            :max="10"
            :colors="['#99A9BF', '#F7BA2A', '#FF9900']"
            text-color="#ff9900"
            score-template="{value}"
          />
        </el-form-item>
        
        <el-form-item label="能量等级">
          <el-slider
            v-model="currentDiary.energy_level"
            :min="1"
            :max="10"
            :step="1"
            show-stops
          />
        </el-form-item>
        
        <el-form-item label="疼痛等级">
          <el-slider
            v-model="currentDiary.pain_level"
            :min="0"
            :max="10"
            :step="1"
            show-stops
          />
        </el-form-item>
        
        <el-form-item label="重要标记">
          <el-switch v-model="currentDiary.is_important" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveDiary" v-if="dialogType !== 'view'">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import axios from 'axios'

const users = ref([])
const diaries = ref([])
const loading = ref(false)
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})
const filterForm = reactive({
  customId: '',
  diaryType: '',
  isImportant: null,
  dateRange: []
})

// 统一聚合接口请求
const fetchUserHealthRecords = async (customId) => {
  loading.value = true
  try {
    const response = await axios.get(`/api/user-health-records/user/${customId}`)
    // 分类处理
    const allRecords = response.data || []
    // 这里只筛选 type 为 health_diary 的记录
    let diaryRecords = allRecords.filter(r => r.type === 'health_diary')
    if (filterForm.diaryType) {
      diaryRecords = diaryRecords.filter(r => r.diary_type === filterForm.diaryType)
    }
    if (filterForm.isImportant !== null) {
      diaryRecords = diaryRecords.filter(r => r.is_important === filterForm.isImportant)
    }
    if (filterForm.dateRange && filterForm.dateRange.length === 2) {
      diaryRecords = diaryRecords.filter(r => r.diary_date >= filterForm.dateRange[0] && r.diary_date <= filterForm.dateRange[1])
    }
    pagination.total = diaryRecords.length
    const start = (pagination.currentPage - 1) * pagination.pageSize
    const end = start + pagination.pageSize
    diaries.value = diaryRecords.slice(start, end)
  } catch (error) {
    ElMessage.error('获取健康日记失败')
  } finally {
    loading.value = false
  }
}

const fetchUsers = async () => {
  try {
    const response = await axios.get('/api/users')
    users.value = response.data
  } catch (error) {
    ElMessage.error('获取用户列表失败')
  }
}

const handleFilter = () => {
  pagination.currentPage = 1
  if (filterForm.customId) fetchUserHealthRecords(filterForm.customId)
}
const resetFilter = () => {
  filterForm.diaryType = ''
  filterForm.isImportant = null
  filterForm.dateRange = []
  pagination.currentPage = 1
  if (filterForm.customId) fetchUserHealthRecords(filterForm.customId)
}
const handleSizeChange = (size) => {
  pagination.pageSize = size
  if (filterForm.customId) fetchUserHealthRecords(filterForm.customId)
}
const handleCurrentChange = (page) => {
  pagination.currentPage = page
  if (filterForm.customId) fetchUserHealthRecords(filterForm.customId)
}

onMounted(() => {
  fetchUsers()
})
</script>

<style scoped>
.health-diary-container {
  padding: 20px;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
