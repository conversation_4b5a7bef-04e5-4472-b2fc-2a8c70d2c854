"""\n问卷调查API\n"""
from typing import List, Optional, Dict, Any, Union
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Request, Body, status as http_status
from sqlalchemy.orm import Session
from datetime import datetime
from pydantic import BaseModel

from app.api.deps import get_db
from app.core.auth import get_current_active_user_custom
from app.models.user import User
from app.models.questionnaire import Questionnaire, QuestionnaireItem, QuestionnaireResponse
from app.models.questionnaire import QuestionnaireTemplate, QuestionnaireTemplateQuestion
from app.utils.field_compatibility import ensure_field_compatibility, ensure_list_field_compatibility

router = APIRouter()

# 添加模板相关函数
def get_questionnaire_templates(db: Session) -> List[Dict[str, Any]]:
    """获取问卷模板列表"""
    templates = db.query(QuestionnaireTemplate).filter(QuestionnaireTemplate.is_active == True).all()
    result = []
    for template in templates:
        # 获取模板题目
        questions = db.query(QuestionnaireTemplateQuestion).filter(
            QuestionnaireTemplateQuestion.template_id == template.id
        ).order_by(QuestionnaireTemplateQuestion.order).all()
        
        # 转换为API格式
        result.append({
            "id": template.id,
            "template_key": getattr(template, 'template_key', None),
            "name": template.name,
            "name_en": getattr(template, 'name_en', ''),
            "description": template.description,
            "instructions": template.instructions,
            "category": getattr(template, 'category', ''),
            "type": getattr(template, 'type', 'questionnaire'),
            "status": getattr(template, 'status', 'pending'),
            "version": template.version,
            "questions": [
                {
                    "id": q.id,
                    "question_id": q.question_id,
                    "question_text": q.question_text,
                    "question_type": q.question_type,
                    "options": q.options,
                    "order": q.order,
                    "is_required": q.is_required,
                    "jump_logic": q.jump_logic
                } for q in questions
            ],
            "question_count": len(questions)
        })
    return result

def get_user_questionnaires(db: Session, custom_id: str) -> List[Dict[str, Any]]:
    """获取用户的问卷列表"""
    questionnaires = db.query(Questionnaire).filter(Questionnaire.custom_id == custom_id).all()
    result = []
    for questionnaire in questionnaires:
        # 获取题目
        items = db.query(QuestionnaireItem).filter(QuestionnaireItem.questionnaire_id == questionnaire.id).all()
        # 获取最新的已完成回复的完成时间
        completed_response = db.query(QuestionnaireResponse) \
            .filter(QuestionnaireResponse.questionnaire_id == questionnaire.id, QuestionnaireResponse.status == 'completed') \
            .order_by(QuestionnaireResponse.updated_at.desc()) \
            .first()
        completed_at = completed_response.updated_at.isoformat() if completed_response and completed_response.updated_at else None
        result.append({
            "id": questionnaire.id,
            "title": questionnaire.title,
            "description": questionnaire.notes,
            "questionnaire_type": questionnaire.questionnaire_type.name if hasattr(questionnaire.questionnaire_type, 'name') else str(questionnaire.questionnaire_type),
            "version": questionnaire.template.version if questionnaire.template else "1.0",
            "created_at": questionnaire.created_at.isoformat() if questionnaire.created_at else None,
            "updated_at": questionnaire.updated_at.isoformat() if questionnaire.updated_at else None,
            "is_system": questionnaire.custom_id == 1,
            "status": questionnaire.status,
            "category": getattr(questionnaire, 'category', None) if hasattr(questionnaire, 'category') else None,
            "question_count": len(items),
            "response_count": db.query(QuestionnaireResponse).filter(QuestionnaireResponse.questionnaire_id == questionnaire.id).count(),
            "completed_at": completed_at,
        })
    return result

class QuestionnaireQuestion(BaseModel):
    question_id: str
    question_text: str
    question_type: Optional[str] = None
    options: Optional[List[Dict[str, Any]]] = None
    order: Optional[int] = None
    is_required: Optional[bool] = True
    jump_logic: Optional[Dict[str, Any]] = None  # 题目逻辑跳转

class QuestionnaireCreate(BaseModel):
    questionnaire_type: str
    title: str
    version: Optional[str] = ""
    description: Optional[str] = ""
    max_score: Optional[float] = 0
    questions: List[QuestionnaireQuestion]

@router.get("", response_model=dict)
def get_questionnaires(
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
    status: Optional[str] = Query(None, description="状态过滤：pending/completed"),
):
    """
    获取问卷列表（系统预置+当前用户自定义）
    """
    try:
        # 查询包括标准问卷和用户自定义问卷
        query = db.query(Questionnaire).filter(
            Questionnaire.custom_id.in_([1, getattr(current_user, 'custom_id', 0)])
        )
        
        # 状态过滤
        if status:
            query = query.filter(Questionnaire.status == status)
        
        total = query.count()
        # 支持 page_size=-1 返回全部问卷
        if page_size == -1:
            questionnaires = query.order_by(Questionnaire.created_at.desc()).all()
        else:
            questionnaires = query.order_by(Questionnaire.created_at.desc()) \
                .offset((page - 1) * page_size).limit(page_size).all()

        result = []
        for q in questionnaires:
            # 分类字段（如有）
            category = getattr(q, 'category', None) if hasattr(q, 'category') else None
            # 问题数量
            question_count = len(q.items) if hasattr(q, 'items') else 0
            # 回复数量
            response_count = db.query(QuestionnaireResponse).filter(QuestionnaireResponse.questionnaire_id == q.id).count()
            result.append({
                "id": q.id,
                "title": q.title,
                "description": q.description,
                "questionnaire_type": q.questionnaire_type,  # 直接用字段
                "version": q.version,
                "created_at": q.created_at.isoformat() if q.created_at else None,
                "updated_at": q.updated_at.isoformat() if q.updated_at else None,
                "is_system": q.custom_id == 1,
                "status": q.status,
                "category": category,
                "question_count": question_count,
                "response_count": response_count,
                "score": q.score if hasattr(q, 'score') else None,
                "max_score": q.max_score if hasattr(q, 'max_score') else None,
                "completed_at": q.completed_at.isoformat() if hasattr(q, 'completed_at') and q.completed_at else None
            })
        
        return {
            "status": "success",
            "data": result,
            "total": total
        }
    except Exception as e:
        print(f"获取问卷列表出错: {str(e)}")
        return {
            "status": "error",
            "message": f"获取问卷列表出错: {str(e)}",
            "data": []
        }

@router.get("/{questionnaire_id}", response_model=dict)
def get_questionnaire(
    questionnaire_id: int,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
):
    """
    获取问卷详情
    """
    questionnaire = db.query(Questionnaire).filter(Questionnaire.id == questionnaire_id).first()
    if not questionnaire:
        raise HTTPException(status_code=http_status.HTTP_404_NOT_FOUND, detail="问卷不存在")
    
    # 获取题目
    items = db.query(QuestionnaireItem).filter(QuestionnaireItem.questionnaire_id == questionnaire.id).all()
    questions = [
        {
            "question_id": item.question_id,
            "question_text": item.question_text,
            "question_type": getattr(item, 'question_type', 'radio'),
            "options": getattr(item, 'options', None),
            "order": getattr(item, 'order', None),
            "is_required": getattr(item, 'is_required', True),
            "jump_logic": getattr(item, 'jump_logic', None),
            "score": getattr(item, 'score', None),
            "answer": getattr(item, 'answer', None)
        } for item in items
    ]
    
    return {
        "status": "success",
        "data": {
            "id": questionnaire.id,
            "title": questionnaire.title,
            "description": questionnaire.notes,
            "questionnaire_type": questionnaire.questionnaire_type.name if hasattr(questionnaire.questionnaire_type, 'name') else str(questionnaire.questionnaire_type),
            "version": questionnaire.template.version if questionnaire.template else "1.0",
            "created_at": questionnaire.created_at.isoformat() if questionnaire.created_at else None,
            "updated_at": questionnaire.updated_at.isoformat() if questionnaire.updated_at else None,
            "is_system": questionnaire.custom_id == 1,
            "status": questionnaire.status,
            "score": questionnaire.score if hasattr(questionnaire, 'score') else None,
            "max_score": questionnaire.max_score if hasattr(questionnaire, 'max_score') else None,
            "completed_at": questionnaire.completed_at.isoformat() if hasattr(questionnaire, 'completed_at') and questionnaire.completed_at else None,
            "questions": questions
        }
    }

@router.get("/{questionnaire_id}/questions", response_model=dict)
def get_questionnaire_questions(
    questionnaire_id: int,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
):
    """
    获取问卷调查的问题列表
    """
    questionnaire = db.query(Questionnaire).filter(Questionnaire.id == questionnaire_id).first()
    if not questionnaire:
        raise HTTPException(status_code=http_status.HTTP_404_NOT_FOUND, detail="问卷不存在")
    
    # 获取题目
    items = db.query(QuestionnaireItem).filter(QuestionnaireItem.questionnaire_id == questionnaire.id).all()
    questions = [
        {
            "id": item.id,
            "question_id": item.question_id,
            "text": item.question_text,
            "question_text": item.question_text,
            "type": getattr(item, 'question_type', 'radio'),
            "question_type": getattr(item, 'question_type', 'radio'),
            "options": getattr(item, 'options', [
                {'value': '1', 'text': '完全不符合'},
                {'value': '2', 'text': '基本不符合'},
                {'value': '3', 'text': '部分符合'},
                {'value': '4', 'text': '基本符合'},
                {'value': '5', 'text': '完全符合'}
            ]),
            "order": getattr(item, 'order', None),
            "is_required": getattr(item, 'is_required', True),
            "jump_logic": getattr(item, 'jump_logic', None),
        } for item in items
    ]
    
    return {
        "status": "success",
        "questions": questions
    }

@router.post("", response_model=dict)
def create_questionnaire(
    data: QuestionnaireCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
):
    """
    创建问卷调查（支持自定义字段，写入正式表）
    """
    questionnaire = Questionnaire(
        custom_id=getattr(current_user, 'custom_id', 1),
        questionnaire_type=data.questionnaire_type,
        name=data.title,
        version=data.version,
        notes=data.description,
        max_score=data.max_score,
        template_id=getattr(data, 'template_id', None),
    )
    db.add(questionnaire)
    db.flush()
    for q in data.questions:
        item = QuestionnaireItem(
            questionnaire_id=questionnaire.id,
            question_id=q.question_id,
            question_text=q.question_text,
            answer='',
            score=None,
            notes='',
            question_type=getattr(q, 'question_type', None),
            options=getattr(q, 'options', None),
            order=getattr(q, 'order', None),
            is_required=getattr(q, 'is_required', True),
            jump_logic=getattr(q, 'jump_logic', None),
        )
        db.add(item)
    db.commit()
    db.refresh(questionnaire)
    return {
        "status": "success",
        "message": "问卷调查创建成功",
        "data": {
            "id": questionnaire.id,
            "title": questionnaire.title,
            "description": questionnaire.notes,
            "questions": [q.dict() for q in data.questions]
        }
    }

@router.put("/{questionnaire_id}", response_model=dict)
def update_questionnaire(
    questionnaire_id: int,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
):
    """
    更新问卷调查（可根据需要完善）
    """
    # 这里只做演示，实际应实现字段更新和题目同步
    return {
        "status": "success",
        "message": "问卷调查更新成功",
        "data": {
            "id": questionnaire_id,
            "title": "更新后的问卷调查",
            "description": "更新后的问卷调查描述",
            "questions": []
        }
    }

@router.delete("/{questionnaire_id}", response_model=dict)
def delete_questionnaire(
    questionnaire_id: int,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
):
    """
    删除问卷（系统预置问卷禁止删除）
    """
    questionnaire = db.query(Questionnaire).filter(Questionnaire.id == questionnaire_id).first()
    if not questionnaire:
        raise HTTPException(status_code=http_status.HTTP_404_NOT_FOUND, detail="问卷不存在")
    if questionnaire.custom_id == 1:
        raise HTTPException(status_code=http_status.HTTP_403_FORBIDDEN, detail="系统预置问卷禁止删除")
    db.delete(questionnaire)
    db.commit()
    return {
        "status": "success",
        "message": "问卷删除成功"
    }

class ReviewRequest(BaseModel):
    status: Optional[str] = "published"
    revision_reason: Optional[str] = None

@router.post("/{questionnaire_id}/review", response_model=dict)
def review_questionnaire(
    questionnaire_id: int,
    review_data: Optional[ReviewRequest] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
):
    """
    审核问卷（仅管理员可操作）
    审核通过后状态变更为已发布(published)
    返修时状态变更为草稿(draft)
    """
    if getattr(current_user, 'role', '') not in ["admin", "super_admin", "consultant"]:
        raise HTTPException(status_code=http_status.HTTP_403_FORBIDDEN, detail="无权限审核")
    questionnaire = db.query(Questionnaire).filter(Questionnaire.id == questionnaire_id).first()
    if not questionnaire:
        raise HTTPException(status_code=http_status.HTTP_404_NOT_FOUND, detail="问卷不存在")
    
    # 默认为审核通过
    status = "published"
    message = "审核通过，问卷已发布"
    
    # 如果提供了审核数据，则使用提供的状态
    if review_data:
        status = review_data.status
        if status == "draft":
            message = "已退回返修"
            # 如果有返修原因，可以记录到数据库中
            if review_data.revision_reason:
                questionnaire.revision_reason = review_data.revision_reason
    
    questionnaire.status = status
    db.commit()
    return {"status": "success", "message": message}

@router.post("/{questionnaire_id}/push", response_model=dict)
def push_questionnaire(
    questionnaire_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
):
    """
    推送问卷（仅管理员可操作）
    """
    if getattr(current_user, 'role', '') not in ["admin", "super_admin", "consultant"]:
        raise HTTPException(status_code=http_status.HTTP_403_FORBIDDEN, detail="无权限推送")
    questionnaire = db.query(Questionnaire).filter(Questionnaire.id == questionnaire_id).first()
    if not questionnaire:
        raise HTTPException(status_code=http_status.HTTP_404_NOT_FOUND, detail="问卷不存在")
    questionnaire.status = 'pushed'
    db.commit()
    return {"status": "success", "message": "推送成功"}

@router.post("/{questionnaire_id}/import-questions", response_model=dict)
def import_questionnaire_questions(
    questionnaire_id: int,
    questions: List[QuestionnaireQuestion],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
):
    """
    批量导入题目（仅管理员可操作）
    """
    if getattr(current_user, 'role', '') not in ["admin", "super_admin", "consultant"]:
        raise HTTPException(status_code=http_status.HTTP_403_FORBIDDEN, detail="无权限批量导入题目")
    questionnaire = db.query(Questionnaire).filter(Questionnaire.id == questionnaire_id).first()
    if not questionnaire:
        raise HTTPException(status_code=http_status.HTTP_404_NOT_FOUND, detail="问卷不存在")
    # 先删除原有题目
    db.query(QuestionnaireItem).filter(QuestionnaireItem.questionnaire_id == questionnaire_id).delete()
    # 批量插入新题目
    for q in questions:
        item = QuestionnaireItem(
            questionnaire_id=questionnaire_id,
            question_id=q.question_id,
            question_text=q.question_text,
            answer='',
            score=None,
            notes='',
            question_type=getattr(q, 'question_type', None),
            options=getattr(q, 'options', None),
            order=getattr(q, 'order', None),
            is_required=getattr(q, 'is_required', True),
            jump_logic=getattr(q, 'jump_logic', None),
        )
        db.add(item)
    db.commit()
    return {"status": "success", "message": "批量导入题目成功"}

@router.post("/{questionnaire_id}/distribute", response_model=dict)
async def distribute_questionnaire(
    questionnaire_id: Union[int, str],
    distributionData: dict = Body(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
):
    """
    分发问卷 - 支持按角色分发和指定用户分发
    支持标准问卷（standard_xxx格式）和自定义问卷（整数ID或custom_id）
    """
    # 检查是否为标准问卷
    if isinstance(questionnaire_id, str) and questionnaire_id.startswith("standard_"):
        # 处理标准问卷，重定向到问卷模板分发
        from app.clinical_scales.questionnaire import ALL_QUESTIONNAIRE_TEMPLATES as STANDARD_QUESTIONNAIRE_TEMPLATES
        
        # 直接使用standard_xxx格式，与量表保持一致
        template_key = questionnaire_id.replace("standard_", "")
        template_id = questionnaire_id  # 保持原格式
        
        # 检查标准问卷是否存在
        found_template = None
        # STANDARD_QUESTIONNAIRE_TEMPLATES是字典，直接通过key查找
        if template_key in STANDARD_QUESTIONNAIRE_TEMPLATES:
            found_template = STANDARD_QUESTIONNAIRE_TEMPLATES[template_key]
        
        if not found_template:
            raise HTTPException(status_code=http_status.HTTP_404_NOT_FOUND, detail="标准问卷不存在")
        
        # 调用问卷模板分发函数
        from app.api.endpoints.templates import distribute_questionnaire_template
        return await distribute_questionnaire_template(template_id, distributionData, db, current_user)
    
    # 检查自定义问卷是否存在
    questionnaire = None
    if isinstance(questionnaire_id, int):
        questionnaire = db.query(Questionnaire).filter(Questionnaire.id == questionnaire_id).first()
    else:
        # 处理字符串ID，可能是custom_id
        questionnaire = db.query(Questionnaire).filter(Questionnaire.custom_id == questionnaire_id).first()
        if not questionnaire and questionnaire_id.isdigit():
            questionnaire = db.query(Questionnaire).filter(Questionnaire.id == int(questionnaire_id)).first()
    if not questionnaire:
        raise HTTPException(status_code=http_status.HTTP_404_NOT_FOUND, detail="问卷不存在")
    
    # 获取分发类型
    distribution_type = distributionData.get("distribution_type")
    if not distribution_type:
        raise HTTPException(status_code=http_status.HTTP_400_BAD_REQUEST, detail="分发类型不能为空")
    
    # 根据分发类型处理
    try:
        from app.models.distribution import QuestionnaireDistribution
        from datetime import datetime, timedelta
        
        # 截止日期处理
        due_date = None
        if "due_date" in distributionData and distributionData["due_date"]:
            try:
                due_date = datetime.fromisoformat(distributionData["due_date"].replace("Z", "+00:00"))
            except Exception as e:
                print(f"处理截止日期时出错: {str(e)}")
                due_date = datetime.now() + timedelta(days=7)  # 默认7天后
        
        # 分发给所有用户
        if distribution_type == "all":
            # 获取所有激活用户
            users = db.query(User).filter(User.is_active == True).all()
            for user in users:
                # 创建分发记录
                distribution = QuestionnaireDistribution(
                    questionnaire_id=questionnaire.id,
                    custom_id=user.custom_id,
                    distributor_custom_id=current_user.custom_id,
                    status="pending",
                    due_date=due_date,
                    message=distributionData.get("message", "")
                )
                db.add(distribution)
        
        # 分发给特定角色
        elif distribution_type == "role":
            roles = distributionData.get("roles", [])
            if not roles:
                raise HTTPException(status_code=http_status.HTTP_400_BAD_REQUEST, detail="角色列表不能为空")
            
            # 获取指定角色的用户
            users = db.query(User).filter(User.is_active == True).all()
            target_users = []
            
            for user in users:
                user_role = user.role
                if isinstance(user_role, str):
                    user_role_str = user_role.lower()
                elif hasattr(user_role, 'name'):
                    user_role_str = user_role.name.lower()
                elif hasattr(user_role, 'value'):
                    user_role_str = user_role.value.lower()
                else:
                    user_role_str = str(user_role).lower()
                
                # 检查用户角色是否在目标角色列表中
                if any(role.lower() == user_role_str for role in roles):
                    target_users.append(user)
            
            # 创建分发记录
            for user in target_users:
                distribution = QuestionnaireDistribution(
                    questionnaire_id=questionnaire.id,
                    custom_id=user.custom_id,
                    distributor_custom_id=current_user.custom_id,
                    status="pending",
                    due_date=due_date,
                    message=distributionData.get("message", "")
                )
                db.add(distribution)
        
        # 分发给特定用户
        elif distribution_type == "specific":
            custom_ids = distributionData.get("custom_ids", [])
            if not custom_ids:
                raise HTTPException(status_code=http_status.HTTP_400_BAD_REQUEST, detail="用户ID列表不能为空")
            
            # 处理用户ID
            for user_id in custom_ids:
                # 尝试查找用户
                user = None
                if isinstance(user_id, int):
                    user = db.query(User).filter(User.id == user_id).first()
                else:
                    # 可能是custom_id
                    user = db.query(User).filter(User.custom_id == user_id).first()
                    if not user and str(user_id).isdigit():
                        # 尝试将字符串解析为int
                        user = db.query(User).filter(User.id == int(user_id)).first()
                
                if user:
                    # 创建分发记录
                    distribution = QuestionnaireDistribution(
                        questionnaire_id=questionnaire.id,
                        custom_id=user.custom_id,
                        distributor_custom_id=current_user.custom_id,
                        status="pending",
                        due_date=due_date,
                        message=distributionData.get("message", "")
                    )
                    db.add(distribution)
        
        # 不支持的分发类型
        else:
            raise HTTPException(
                status_code=http_status.HTTP_400_BAD_REQUEST, 
                detail=f"不支持的分发类型: {distribution_type}"
            )
        
        # 提交事务
        db.commit()
        
        return {"status": "success", "message": "问卷分发成功"}
    
    except HTTPException as he:
        # 重新抛出HTTP异常
        raise he
    except Exception as e:
        # 记录日志并回滚事务
        print(f"分发问卷时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        db.rollback()
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"分发问卷失败: {str(e)}"
        )

@router.get("/user/{custom_id}", response_model=Dict[str, Any])
def read_user_questionnaires(
    *,
    db: Session = Depends(get_db),
    custom_id: str = Path(..., description="用户ID"),
    questionnaire_type: Optional[str] = Query(None, description="问卷类型"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    current_user: User = Depends(get_current_active_user_custom)
) -> Any:
    """
    获取指定用户的问卷调查记录列表
    """
    print(f"questionnaires.py - 获取用户问卷调查记录 - 用户ID: {custom_id}")

    # 支持数字ID和自定义ID查找用户
    user = None
    if custom_id.isdigit():
        user = db.query(User).filter(User.id == int(custom_id)).first()
    else:
        user = db.query(User).filter(User.custom_id == custom_id).first()
    
    if not user:
        print(f"用户{custom_id}不存在，返回404异常")
        raise HTTPException(status_code=http_status.HTTP_404_NOT_FOUND, detail="用户不存在")
    
    try:
        # 修正：确保 start_date 和 end_date 为 datetime 类型
        from datetime import datetime
        def parse_date_safe(val):
            if isinstance(val, datetime) or val is None:
                return val
            try:
                return datetime.fromisoformat(val)
            except Exception:
                try:
                    return datetime.strptime(val, "%Y-%m-%d")
                except Exception:
                    return None
        if start_date is not None:
            start_date = parse_date_safe(start_date)
        if end_date is not None:
            end_date = parse_date_safe(end_date)
        # 构建查询
        query = db.query(Questionnaire).filter(Questionnaire.custom_id == user.custom_id)
        
        # 应用筛选条件
        if questionnaire_type:
            query = query.filter(Questionnaire.questionnaire_type == questionnaire_type)
        
        if start_date:
            query = query.filter(Questionnaire.created_at >= start_date)
        
        if end_date:
            query = query.filter(Questionnaire.created_at <= end_date)
        
        # 获取总数
        total = query.count()
        
        # 分页
        questionnaires = query.order_by(Questionnaire.created_at.desc()).offset(skip).limit(limit).all()
        
        # 转换为API响应格式
        result = []
        for q in questionnaires:
            # 获取问卷题目
            items = db.query(QuestionnaireItem).filter(QuestionnaireItem.questionnaire_id == q.id).all()
            
            # 获取最新的已完成回复的完成时间
            completed_response = db.query(QuestionnaireResponse) \
                .filter(QuestionnaireResponse.questionnaire_id == q.id, QuestionnaireResponse.status == 'completed') \
                .order_by(QuestionnaireResponse.updated_at.desc()) \
                .first()
            completed_at = completed_response.updated_at.isoformat() if completed_response and completed_response.updated_at else None

            # 构建问卷数据
            questionnaire_data = {
                "id": q.id,
                "title": q.title or "未命名问卷",
                "description": q.notes,
                "questionnaire_type": q.questionnaire_type.name if hasattr(q.questionnaire_type, 'name') else str(q.questionnaire_type),
                "version": q.version,
                "created_at": q.created_at.isoformat() if q.created_at else None,
                "updated_at": q.updated_at.isoformat() if q.updated_at else None,
                "status": q.status,
                "completed_at": completed_at,
                "questions": [
                    {
                        "question_id": item.question_id,
                        "question_text": item.question_text,
                        "answer": item.answer,
                        "score": item.score,
                        "notes": item.notes,
                        "question_type": getattr(item, 'question_type', None),
                        "options": getattr(item, 'options', None),
                    } for item in items
                ]
            }
            
            # 添加到结果列表
            result.append(questionnaire_data)
        
        return {
            "status": "success",
            "data": {
                "total": total,
                "items": result
            }
        }
    
    except Exception as e:
        print(f"获取用户问卷调查记录时出错: {str(e)}")
        raise HTTPException(status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"获取用户问卷调查记录时出错: {str(e)}")

@router.get("/history", response_model=dict)
def get_questionnaire_history(
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
):
    """
    获取问卷历史记录
    """
    # 查询用户的问卷回复记录
    query = db.query(QuestionnaireResponse).filter(
        QuestionnaireResponse.custom_id == current_user.custom_id
    )
    
    total = query.count()
    responses = query.order_by(QuestionnaireResponse.created_at.desc()) \
        .offset((page - 1) * page_size).limit(page_size).all()
    
    result = []
    for response in responses:
        questionnaire = db.query(Questionnaire).filter(Questionnaire.id == response.questionnaire_id).first()
        if questionnaire:
            result.append({
                "id": response.id,
                "questionnaire_id": response.questionnaire_id,
                "title": questionnaire.title,
                "description": questionnaire.notes,
                "questionnaire_type": questionnaire.questionnaire_type.name if hasattr(questionnaire.questionnaire_type, 'name') else str(questionnaire.questionnaire_type),
                "status": "completed",
                "total_score": response.total_score,
                "report": response.report,
                "notes": response.notes,
                "created_at": response.created_at.isoformat() if response.created_at else None,
                "completed_at": response.completed_at.isoformat() if response.completed_at else None,
                "answers": response.answers
            })
    
    return {
        "status": "success",
        "data": {
            "items": result,
            "total": total,
            "page": page,
            "page_size": page_size
        }
    }

@router.post("/{questionnaire_id}/submit", response_model=dict)
def submit_questionnaire(
    questionnaire_id: int,
    data: Dict[str, Any] = Body(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
):
    """
    提交问卷答案
    """
    try:
        questionnaire = db.query(Questionnaire).filter(Questionnaire.id == questionnaire_id).first()
        if not questionnaire:
            raise HTTPException(status_code=http_status.HTTP_404_NOT_FOUND, detail="问卷不存在")
        
        # 验证答案
        answers = data.get("answers", [])
        if not answers:
            raise HTTPException(status_code=http_status.HTTP_400_BAD_REQUEST, detail="答案不能为空")
        
        # 计算得分
        total_score = 0
        for answer in answers:
            item = db.query(QuestionnaireItem).filter(
                QuestionnaireItem.questionnaire_id == questionnaire_id,
                QuestionnaireItem.question_id == answer.get("question_id")
            ).first()
            if item:
                score = answer.get("score", 0)
                total_score += score
                item.answer = str(answer.get("answer", ""))
                item.score = score
        
        # 更新问卷状态
        questionnaire.status = "completed"
        questionnaire.score = total_score
        questionnaire.completed_at = datetime.now()
        
        # 创建回复记录
        response = QuestionnaireResponse(
            questionnaire_id=questionnaire_id,
            custom_id=current_user.custom_id,
            answers={"answers": answers},
            score=total_score,
            result=None,  # 问卷可能不需要结果分类
            notes=data.get("notes", "")
        )
        db.add(response)
        db.commit()
        
        return {
            "status": "success",
            "message": "问卷提交成功",
            "data": {
                "questionnaire_id": questionnaire_id,
                "total_score": total_score,
                "max_score": questionnaire.max_score if hasattr(questionnaire, 'max_score') else None,
                "completed_at": questionnaire.completed_at.isoformat(),
                "answers_count": len(answers),
                "response_id": response.id
            }
        }
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))