# -*- coding: utf-8 -*-
"""
健康管理系统主管理器
统一入口，集成项目管理、配置管理、测试管理、服务管理和部署管理

版本: 1.0
作者: Health Management System
创建时间: 2024-12-30
"""

import os
import sys
import json
import asyncio
import argparse
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入所有管理器
try:
    from project_manager import ProjectManager
    from config_manager import ConfigManager
    from test_manager import TestManager
    from service_manager import ServiceManager
    from deployment_manager import DeploymentManager
except ImportError as e:
    print(f"警告：无法导入管理器模块: {e}")
    print("请确保所有管理器文件都存在")
    sys.exit(1)

# 导入后端核心模块
try:
    from backend.app.core.logging_utils import get_logger
except ImportError:
    import logging
    def get_logger(name):
        logging.basicConfig(level=logging.INFO)
        return logging.getLogger(name)

logger = get_logger(__name__)

class MainManager:
    """主管理器"""
    
    def __init__(self):
        """初始化主管理器"""
        self.project_root = project_root
        
        # 初始化各个管理器
        self.project_manager = None
        self.config_manager = None
        self.test_manager = None
        self.service_manager = None
        self.deployment_manager = None
        
        logger.info(f"主管理器初始化完成，项目根目录: {self.project_root}")
    
    def _initialize_managers(self):
        """延迟初始化管理器"""
        try:
            if not self.project_manager:
                self.project_manager = ProjectManager()
            if not self.config_manager:
                self.config_manager = ConfigManager()
            if not self.test_manager:
                self.test_manager = TestManager()
            if not self.service_manager:
                self.service_manager = ServiceManager()
            if not self.deployment_manager:
                self.deployment_manager = DeploymentManager()
            
            logger.info("所有管理器初始化完成")
            
        except Exception as e:
            logger.error(f"初始化管理器失败: {str(e)}")
            raise
    
    def show_main_menu(self):
        """显示主菜单"""
        print("\n" + "="*60)
        print("🏥 健康管理系统 - 统一管理平台")
        print("="*60)
        print("请选择管理模块:")
        print("\n📋 1. 项目管理器 - 组件管理、状态监控")
        print("⚙️  2. 配置管理器 - 环境配置、参数设置")
        print("🧪 3. 测试管理器 - 自动化测试、质量保证")
        print("🚀 4. 服务管理器 - 服务启停、运行监控")
        print("📦 5. 部署管理器 - 构建部署、版本管理")
        print("\n🔧 6. 系统初始化 - 一键环境搭建")
        print("📊 7. 系统状态 - 全局状态概览")
        print("🛠️  8. 快速操作 - 常用操作集合")
        print("\n❓ 9. 帮助信息")
        print("🚪 0. 退出系统")
    
    async def run_project_manager(self):
        """运行项目管理器"""
        try:
            if not self.project_manager:
                self.project_manager = ProjectManager()
            
            print("\n🔄 启动项目管理器...")
            await self.project_manager.interactive_mode()
            
        except Exception as e:
            print(f"❌ 项目管理器运行错误: {str(e)}")
            logger.error(f"项目管理器运行错误: {str(e)}")
    
    async def run_config_manager(self):
        """运行配置管理器"""
        try:
            if not self.config_manager:
                self.config_manager = ConfigManager()
            
            print("\n🔄 启动配置管理器...")
            await self.config_manager.interactive_mode()
            
        except Exception as e:
            print(f"❌ 配置管理器运行错误: {str(e)}")
            logger.error(f"配置管理器运行错误: {str(e)}")
    
    async def run_test_manager(self):
        """运行测试管理器"""
        try:
            if not self.test_manager:
                self.test_manager = TestManager()
            
            print("\n🔄 启动测试管理器...")
            # 导入测试管理器的main函数
            from test_manager import main as test_main
            await test_main()
            
        except Exception as e:
            print(f"❌ 测试管理器运行错误: {str(e)}")
            logger.error(f"测试管理器运行错误: {str(e)}")
    
    async def run_service_manager(self):
        """运行服务管理器"""
        try:
            if not self.service_manager:
                self.service_manager = ServiceManager()
            
            print("\n🔄 启动服务管理器...")
            # 导入服务管理器的main函数
            from service_manager import main as service_main
            await service_main()
            
        except Exception as e:
            print(f"❌ 服务管理器运行错误: {str(e)}")
            logger.error(f"服务管理器运行错误: {str(e)}")
    
    async def run_deployment_manager(self):
        """运行部署管理器"""
        try:
            if not self.deployment_manager:
                self.deployment_manager = DeploymentManager()
            
            print("\n🔄 启动部署管理器...")
            # 导入部署管理器的main函数
            from deployment_manager import main as deploy_main
            await deploy_main()
            
        except Exception as e:
            print(f"❌ 部署管理器运行错误: {str(e)}")
            logger.error(f"部署管理器运行错误: {str(e)}")
    
    async def system_initialization(self):
        """系统初始化"""
        try:
            print("\n🔧 开始系统初始化...")
            
            # 初始化所有管理器
            self._initialize_managers()
            
            # 检查项目结构
            print("\n📁 检查项目结构...")
            required_dirs = [
                self.project_root / "backend",
                self.project_root / "frontend",
                self.project_root / "backend" / "app",
                self.project_root / "frontend" / "src"
            ]
            
            for dir_path in required_dirs:
                if dir_path.exists():
                    print(f"  ✅ {dir_path.name} 目录存在")
                else:
                    print(f"  ❌ {dir_path.name} 目录不存在")
            
            # 检查配置文件
            print("\n⚙️ 检查配置文件...")
            config_files = [
                self.project_root / "backend" / "requirements.txt",
                self.project_root / "frontend" / "package.json",
                self.project_root / "backend" / "app" / "main.py"
            ]
            
            for file_path in config_files:
                if file_path.exists():
                    print(f"  ✅ {file_path.name} 文件存在")
                else:
                    print(f"  ❌ {file_path.name} 文件不存在")
            
            # 安装依赖
            print("\n📦 检查依赖安装...")
            
            # 检查Python依赖
            backend_requirements = self.project_root / "backend" / "requirements.txt"
            if backend_requirements.exists():
                print("  🐍 检查Python依赖...")
                try:
                    import subprocess
                    result = subprocess.run(
                        ["pip", "check"],
                        capture_output=True,
                        text=True,
                        cwd=self.project_root / "backend"
                    )
                    if result.returncode == 0:
                        print("    ✅ Python依赖检查通过")
                    else:
                        print("    ⚠️ Python依赖存在问题")
                        print(f"    {result.stdout}")
                except Exception as e:
                    print(f"    ❌ Python依赖检查失败: {str(e)}")
            
            # 检查Node.js依赖
            frontend_package = self.project_root / "frontend" / "package.json"
            if frontend_package.exists():
                print("  📦 检查Node.js依赖...")
                node_modules = self.project_root / "frontend" / "node_modules"
                if node_modules.exists():
                    print("    ✅ Node.js依赖已安装")
                else:
                    print("    ⚠️ Node.js依赖未安装")
            
            # 创建必要的目录
            print("\n📁 创建必要目录...")
            dirs_to_create = [
                self.project_root / "logs",
                self.project_root / "temp",
                self.project_root / "build",
                self.project_root / "dist",
                self.project_root / "backups"
            ]
            
            for dir_path in dirs_to_create:
                dir_path.mkdir(exist_ok=True)
                print(f"  ✅ 创建目录: {dir_path.name}")
            
            print("\n✅ 系统初始化完成！")
            
        except Exception as e:
            print(f"❌ 系统初始化失败: {str(e)}")
            logger.error(f"系统初始化失败: {str(e)}")
    
    async def show_system_status(self):
        """显示系统状态"""
        try:
            print("\n📊 系统状态概览")
            print("="*50)
            
            # 初始化管理器
            self._initialize_managers()
            
            # 项目状态
            print("\n📋 项目状态:")
            if self.project_manager:
                # 这里可以添加项目状态检查
                print("  ✅ 项目管理器正常")
            
            # 配置状态
            print("\n⚙️ 配置状态:")
            if self.config_manager:
                print("  ✅ 配置管理器正常")
            
            # 服务状态
            print("\n🚀 服务状态:")
            if self.service_manager:
                status = self.service_manager.get_all_services_status()
                print(f"  总服务数: {status['summary']['total']}")
                print(f"  运行中: {status['summary']['running']}")
                print(f"  已停止: {status['summary']['stopped']}")
                print(f"  错误: {status['summary']['error']}")
                
                for name, info in status['services'].items():
                    status_icon = {
                        'running': '🟢',
                        'stopped': '🔴',
                        'error': '🟡'
                    }.get(info['status'], '⚪')
                    print(f"    {status_icon} {name}: {info['status']}")
            
            # 测试状态
            print("\n🧪 测试状态:")
            if self.test_manager:
                print("  ✅ 测试管理器正常")
            
            # 部署状态
            print("\n📦 部署状态:")
            if self.deployment_manager:
                build_history = self.deployment_manager.get_build_history(3)
                deploy_history = self.deployment_manager.get_deployment_history(3)
                
                print(f"  最近构建: {len(build_history)} 次")
                print(f"  最近部署: {len(deploy_history)} 次")
                
                if build_history:
                    latest_build = build_history[-1]
                    status_icon = {
                        'success': '✅',
                        'failed': '❌',
                        'building': '🔨'
                    }.get(latest_build['status'], '❓')
                    print(f"    最新构建: {status_icon} {latest_build['name']} ({latest_build['status']})")
                
                if deploy_history:
                    latest_deploy = deploy_history[-1]
                    status_icon = {
                        'success': '✅',
                        'failed': '❌',
                        'deploying': '🚀'
                    }.get(latest_deploy['status'], '❓')
                    print(f"    最新部署: {status_icon} {latest_deploy['name']} ({latest_deploy['status']})")
            
            # 系统资源
            print("\n💻 系统资源:")
            try:
                import psutil
                cpu_percent = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()
                disk = psutil.disk_usage('/')
                
                print(f"  CPU使用率: {cpu_percent:.1f}%")
                print(f"  内存使用率: {memory.percent:.1f}%")
                print(f"  磁盘使用率: {disk.percent:.1f}%")
            except ImportError:
                print("  ⚠️ 无法获取系统资源信息 (需要安装psutil)")
            
        except Exception as e:
            print(f"❌ 获取系统状态失败: {str(e)}")
            logger.error(f"获取系统状态失败: {str(e)}")
    
    async def quick_operations(self):
        """快速操作"""
        try:
            while True:
                print("\n🛠️ 快速操作")
                print("-"*30)
                print("1. 启动所有服务")
                print("2. 停止所有服务")
                print("3. 重启所有服务")
                print("4. 运行所有测试")
                print("5. 构建并部署")
                print("6. 系统健康检查")
                print("7. 清理临时文件")
                print("0. 返回主菜单")
                
                choice = input("\n请选择操作 (0-7): ").strip()
                
                if choice == '1':
                    print("\n🚀 启动所有服务...")
                    if not self.service_manager:
                        self.service_manager = ServiceManager()
                    success = await self.service_manager.start_all_services()
                    print(f"结果: {'全部成功' if success else '部分失败'}")
                    
                elif choice == '2':
                    print("\n🛑 停止所有服务...")
                    if not self.service_manager:
                        self.service_manager = ServiceManager()
                    success = await self.service_manager.stop_all_services()
                    print(f"结果: {'全部成功' if success else '部分失败'}")
                    
                elif choice == '3':
                    print("\n🔄 重启所有服务...")
                    if not self.service_manager:
                        self.service_manager = ServiceManager()
                    # 先停止再启动
                    await self.service_manager.stop_all_services()
                    await asyncio.sleep(2)
                    success = await self.service_manager.start_all_services()
                    print(f"结果: {'全部成功' if success else '部分失败'}")
                    
                elif choice == '4':
                    print("\n🧪 运行所有测试...")
                    if not self.test_manager:
                        self.test_manager = TestManager()
                    success = await self.test_manager.run_all_tests()
                    print(f"结果: {'全部通过' if success else '存在失败'}")
                    
                elif choice == '5':
                    print("\n🔨🚀 构建并部署...")
                    if not self.deployment_manager:
                        self.deployment_manager = DeploymentManager()
                    
                    # 构建并部署后端
                    print("构建并部署后端...")
                    build_result, deploy_result = await self.deployment_manager.build_and_deploy('backend', 'local')
                    print(f"后端构建: {build_result.status.value}")
                    if deploy_result:
                        print(f"后端部署: {deploy_result.status.value}")
                    
                    # 构建并部署前端
                    print("构建并部署前端...")
                    build_result, deploy_result = await self.deployment_manager.build_and_deploy('frontend', 'local')
                    print(f"前端构建: {build_result.status.value}")
                    if deploy_result:
                        print(f"前端部署: {deploy_result.status.value}")
                    
                elif choice == '6':
                    print("\n🏥 系统健康检查...")
                    await self.show_system_status()
                    
                elif choice == '7':
                    print("\n🧹 清理临时文件...")
                    if not self.deployment_manager:
                        self.deployment_manager = DeploymentManager()
                    self.deployment_manager.cleanup_old_builds()
                    
                    # 清理测试临时文件
                    if self.test_manager:
                        self.test_manager.cleanup()
                    
                    print("清理完成")
                    
                elif choice == '0':
                    break
                    
                else:
                    print("❌ 无效选择")
                    
        except Exception as e:
            print(f"❌ 快速操作失败: {str(e)}")
            logger.error(f"快速操作失败: {str(e)}")
    
    def show_help(self):
        """显示帮助信息"""
        print("\n❓ 帮助信息")
        print("="*50)
        print("\n🏥 健康管理系统统一管理平台")
        print("\n功能模块说明:")
        print("\n📋 项目管理器:")
        print("  - 组件状态监控")
        print("  - 系统资源管理")
        print("  - 操作日志记录")
        
        print("\n⚙️ 配置管理器:")
        print("  - 环境配置管理")
        print("  - 数据库配置")
        print("  - 服务器配置")
        print("  - 安全配置")
        
        print("\n🧪 测试管理器:")
        print("  - 数据导出测试")
        print("  - API接口测试")
        print("  - 前端组件测试")
        print("  - 测试报告生成")
        
        print("\n🚀 服务管理器:")
        print("  - 后端服务管理")
        print("  - 前端服务管理")
        print("  - 服务监控")
        print("  - 自动重启")
        
        print("\n📦 部署管理器:")
        print("  - 项目构建")
        print("  - 自动部署")
        print("  - 版本管理")
        print("  - 回滚功能")
        
        print("\n🛠️ 快速操作:")
        print("  - 一键启动/停止服务")
        print("  - 一键测试")
        print("  - 一键构建部署")
        print("  - 系统健康检查")
        
        print("\n📞 技术支持:")
        print("  - 项目地址: c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN")
        print("  - 日志目录: logs/")
        print("  - 配置目录: config/")
        
        input("\n按回车键返回主菜单...")
    
    async def run(self):
        """运行主管理器"""
        try:
            while True:
                self.show_main_menu()
                choice = input("\n请选择操作 (0-9): ").strip()
                
                if choice == '1':
                    await self.run_project_manager()
                    
                elif choice == '2':
                    await self.run_config_manager()
                    
                elif choice == '3':
                    await self.run_test_manager()
                    
                elif choice == '4':
                    await self.run_service_manager()
                    
                elif choice == '5':
                    await self.run_deployment_manager()
                    
                elif choice == '6':
                    await self.system_initialization()
                    
                elif choice == '7':
                    await self.show_system_status()
                    
                elif choice == '8':
                    await self.quick_operations()
                    
                elif choice == '9':
                    self.show_help()
                    
                elif choice == '0':
                    print("\n👋 感谢使用健康管理系统统一管理平台！")
                    break
                    
                else:
                    print("\n❌ 无效选择，请重新输入")
                    
        except KeyboardInterrupt:
            print("\n\n⚠️ 用户中断操作")
        except Exception as e:
            print(f"\n❌ 系统运行错误: {str(e)}")
            logger.error(f"系统运行错误: {str(e)}")
        finally:
            print("\n🧹 系统清理完成")

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="健康管理系统统一管理平台",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python main_manager.py                    # 交互式模式
  python main_manager.py --init             # 系统初始化
  python main_manager.py --status           # 显示系统状态
  python main_manager.py --start-services   # 启动所有服务
  python main_manager.py --stop-services    # 停止所有服务
  python main_manager.py --run-tests        # 运行所有测试
        """
    )
    
    parser.add_argument('--init', action='store_true', help='系统初始化')
    parser.add_argument('--status', action='store_true', help='显示系统状态')
    parser.add_argument('--start-services', action='store_true', help='启动所有服务')
    parser.add_argument('--stop-services', action='store_true', help='停止所有服务')
    parser.add_argument('--run-tests', action='store_true', help='运行所有测试')
    parser.add_argument('--build-deploy', action='store_true', help='构建并部署')
    parser.add_argument('--version', action='version', version='健康管理系统统一管理平台 v1.0')
    
    return parser.parse_args()

async def main():
    """主函数"""
    args = parse_arguments()
    manager = MainManager()
    
    try:
        # 命令行模式
        if args.init:
            await manager.system_initialization()
        elif args.status:
            await manager.show_system_status()
        elif args.start_services:
            manager._initialize_managers()
            success = await manager.service_manager.start_all_services()
            print(f"启动服务结果: {'成功' if success else '失败'}")
        elif args.stop_services:
            manager._initialize_managers()
            success = await manager.service_manager.stop_all_services()
            print(f"停止服务结果: {'成功' if success else '失败'}")
        elif args.run_tests:
            manager._initialize_managers()
            success = await manager.test_manager.run_all_tests()
            print(f"测试结果: {'全部通过' if success else '存在失败'}")
        elif args.build_deploy:
            manager._initialize_managers()
            # 构建并部署后端和前端
            build_result, deploy_result = await manager.deployment_manager.build_and_deploy('backend', 'local')
            print(f"后端构建部署: {build_result.status.value} -> {deploy_result.status.value if deploy_result else 'N/A'}")
            
            build_result, deploy_result = await manager.deployment_manager.build_and_deploy('frontend', 'local')
            print(f"前端构建部署: {build_result.status.value} -> {deploy_result.status.value if deploy_result else 'N/A'}")
        else:
            # 交互式模式
            await manager.run()
            
    except Exception as e:
        print(f"❌ 程序运行错误: {str(e)}")
        logger.error(f"程序运行错误: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)