#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析assessment_responses表中的回答数据格式问题
"""

import sqlite3
import json
import os

def analyze_assessment_answers():
    """分析回答数据格式"""
    print("=== 分析Assessment回答数据格式 ===")
    
    db_path = "c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db"
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 查看assessment_responses表结构
        print("\n1. assessment_responses表结构:")
        cursor.execute("PRAGMA table_info(assessment_responses)")
        columns = cursor.fetchall()
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
        
        # 2. 查看SM_008用户的原始回答数据
        print("\n2. SM_008用户的原始回答数据:")
        cursor.execute("""
            SELECT id, assessment_id, answers, created_at 
            FROM assessment_responses 
            WHERE custom_id = 'SM_008'
        """)
        responses = cursor.fetchall()
        
        for i, response in enumerate(responses):
            print(f"\n  回答记录 {i+1}:")
            print(f"    ID: {response[0]}")
            print(f"    Assessment ID: {response[1]}")
            print(f"    创建时间: {response[3]}")
            
            answers_raw = response[2]
            print(f"    原始answers数据类型: {type(answers_raw)}")
            print(f"    原始answers数据长度: {len(answers_raw) if answers_raw else 0}")
            
            if answers_raw:
                print(f"    原始answers前100字符: {str(answers_raw)[:100]}...")
                
                try:
                    # 尝试解析JSON
                    answers_data = json.loads(answers_raw)
                    print(f"    JSON解析成功，数据类型: {type(answers_data)}")
                    
                    if isinstance(answers_data, list):
                        print(f"    答案列表长度: {len(answers_data)}")
                        
                        # 分析前几个答案的格式
                        for j, answer in enumerate(answers_data[:3]):
                            print(f"      答案 {j+1}: {answer}")
                            if isinstance(answer, dict):
                                for key, value in answer.items():
                                    print(f"        {key}: {value} (类型: {type(value)})")
                    
                    elif isinstance(answers_data, dict):
                        print(f"    答案字典键: {list(answers_data.keys())}")
                        
                except json.JSONDecodeError as e:
                    print(f"    JSON解析失败: {e}")
                    # 尝试其他解析方法
                    print(f"    尝试直接eval解析...")
                    try:
                        answers_data = eval(answers_raw)
                        print(f"    eval解析成功: {type(answers_data)}")
                    except Exception as e2:
                        print(f"    eval解析也失败: {e2}")
        
        # 3. 查看蒙特利尔认知评估量表的问题模板
        print("\n3. 蒙特利尔认知评估量表的问题模板:")
        cursor.execute("""
            SELECT at.id, at.name, atq.question_id, atq.question_text, atq.question_type, atq.options
            FROM assessment_templates at
            JOIN assessment_template_questions atq ON at.id = atq.template_id
            WHERE at.name LIKE '%蒙特利尔%'
            ORDER BY atq.order
            LIMIT 5
        """)
        questions = cursor.fetchall()
        
        for question in questions:
            print(f"  问题ID: {question[2]}, 类型: {question[4]}")
            print(f"  问题文本: {question[3][:50]}...")
            if question[5]:  # options
                try:
                    options = json.loads(question[5])
                    print(f"  选项: {options}")
                except:
                    print(f"  选项(原始): {question[5]}")
            print()
        
        # 4. 检查数据匹配问题
        print("\n4. 数据匹配分析:")
        
        # 获取问题ID列表
        cursor.execute("""
            SELECT question_id 
            FROM assessment_template_questions atq
            JOIN assessment_templates at ON atq.template_id = at.id
            WHERE at.name LIKE '%蒙特利尔%'
            ORDER BY atq.order
        """)
        expected_question_ids = [row[0] for row in cursor.fetchall()]
        print(f"  期望的问题ID列表: {expected_question_ids[:10]}...")  # 只显示前10个
        
        # 分析实际回答中的问题ID
        if responses:
            answers_raw = responses[0][2]  # 取第一条记录
            if answers_raw:
                try:
                    answers_data = json.loads(answers_raw)
                    if isinstance(answers_data, list):
                        actual_question_ids = []
                        for answer in answers_data:
                            if isinstance(answer, dict) and 'question_id' in answer:
                                actual_question_ids.append(answer['question_id'])
                        
                        print(f"  实际回答的问题ID列表: {actual_question_ids[:10]}...")  # 只显示前10个
                        
                        # 检查匹配情况
                        matched = set(expected_question_ids) & set(actual_question_ids)
                        print(f"  匹配的问题ID数量: {len(matched)}/{len(expected_question_ids)}")
                        
                        if len(matched) < len(expected_question_ids):
                            missing = set(expected_question_ids) - set(actual_question_ids)
                            print(f"  缺失的问题ID: {list(missing)[:5]}...")  # 只显示前5个
                        
                except Exception as e:
                    print(f"  分析失败: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"分析过程中出错: {str(e)}")
    
    print("\n=== 分析完成 ===")

if __name__ == "__main__":
    analyze_assessment_answers()