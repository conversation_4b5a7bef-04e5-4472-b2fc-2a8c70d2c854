#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
添加dimension_scores字段到数据库表
"""

import sqlite3
import os

def add_dimension_scores_field():
    """添加dimension_scores字段"""
    db_path = 'app.db'
    
    if not os.path.exists(db_path):
        print(f"数据库文件 {db_path} 不存在")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查questionnaire_responses表是否已有dimension_scores字段
        cursor.execute('PRAGMA table_info(questionnaire_responses)')
        columns = [col[1] for col in cursor.fetchall()]
        
        if 'dimension_scores' not in columns:
            print("为questionnaire_responses表添加dimension_scores字段...")
            cursor.execute('ALTER TABLE questionnaire_responses ADD COLUMN dimension_scores TEXT')
            print("✓ questionnaire_responses表添加dimension_scores字段成功")
        else:
            print("questionnaire_responses表已有dimension_scores字段")
        
        # 检查assessment_responses表是否已有dimension_scores字段
        cursor.execute('PRAGMA table_info(assessment_responses)')
        columns = [col[1] for col in cursor.fetchall()]
        
        if 'dimension_scores' not in columns:
            print("为assessment_responses表添加dimension_scores字段...")
            cursor.execute('ALTER TABLE assessment_responses ADD COLUMN dimension_scores TEXT')
            print("✓ assessment_responses表添加dimension_scores字段成功")
        else:
            print("assessment_responses表已有dimension_scores字段")
        
        conn.commit()
        conn.close()
        
        print("\n字段添加完成！")
        
    except Exception as e:
        print(f"添加字段过程中发生错误: {e}")
        if conn:
            conn.rollback()
            conn.close()

if __name__ == "__main__":
    add_dimension_scores_field()