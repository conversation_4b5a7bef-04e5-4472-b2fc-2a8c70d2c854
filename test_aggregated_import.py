#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试聚合API模块导入
"""

import sys
import os

# 添加后端目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'YUN', 'backend'))

print("测试聚合API模块导入...")

try:
    from app.api.v1 import aggregated
    print("✅ 聚合API模块导入成功")
    print(f"聚合API路由器: {aggregated.router}")
    print(f"路由器前缀: {aggregated.router.prefix}")
    print(f"路由器标签: {aggregated.router.tags}")
    
    # 检查路由
    routes = aggregated.router.routes
    print(f"\n路由数量: {len(routes)}")
    for route in routes:
        if hasattr(route, 'path') and hasattr(route, 'methods'):
            print(f"  - {route.methods} {route.path}")
        
except ImportError as e:
    print(f"❌ 聚合API模块导入失败: {e}")
    import traceback
    traceback.print_exc()
except Exception as e:
    print(f"❌ 其他错误: {e}")
    import traceback
    traceback.print_exc()