import requests
import json

# API基础URL
base_url = "http://localhost:8000"

# 测试用的认证头部
headers = {
    "X-User-ID": "SM_006",
    "Content-Type": "application/json"
}

print("=== 测试不带status参数的API调用 ===")

# 1. 测试不带status参数，获取所有量表
print("\n1. 测试获取所有量表（不带status参数）:")
try:
    url = f"{base_url}/api/mobile/assessments"
    params = {
        "custom_id": "SM_006",
        "limit": 20
    }
    
    print(f"请求URL: {url}")
    print(f"请求参数: {params}")
    print(f"请求头: {headers}")
    
    response = requests.get(url, params=params, headers=headers)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        
        # 检查响应格式
        if 'data' in data:
            assessments = data['data']
            total = data.get('total', len(assessments))
        else:
            # 如果响应格式不同，直接使用data作为assessments
            assessments = data if isinstance(data, list) else []
            total = len(assessments)
        
        print(f"返回的量表数量: {len(assessments)}")
        print(f"API返回的total: {total}")
        
        for i, assessment in enumerate(assessments, 1):
            print(f"  量表{i}: ID={assessment['id']}, 名称={assessment['name']}, 状态={assessment['status']}")
    else:
        print(f"请求失败: {response.text}")
        
except Exception as e:
    print(f"请求出错: {e}")

# 2. 测试带status=pending参数
print("\n2. 测试获取pending状态的量表:")
try:
    url = f"{base_url}/api/mobile/assessments"
    params = {
        "custom_id": "SM_006",
        "status": "pending",
        "limit": 20
    }
    
    print(f"请求URL: {url}")
    print(f"请求参数: {params}")
    
    response = requests.get(url, params=params, headers=headers)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        
        # 检查响应格式
        if 'data' in data:
            assessments = data['data']
            total = data.get('total', len(assessments))
        else:
            # 如果响应格式不同，直接使用data作为assessments
            assessments = data if isinstance(data, list) else []
            total = len(assessments)
        
        print(f"返回的量表数量: {len(assessments)}")
        print(f"API返回的total: {total}")
        
        for i, assessment in enumerate(assessments, 1):
            print(f"  量表{i}: ID={assessment['id']}, 名称={assessment['name']}, 状态={assessment['status']}")
    else:
        print(f"请求失败: {response.text}")
        
except Exception as e:
    print(f"请求出错: {e}")