#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为评估模板添加计分规则
"""

import sqlite3
import json
import sys
import os

# 数据库路径
DB_PATH = 'c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db'

def add_scoring_rules():
    """为评估模板添加计分规则"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        print("开始为评估模板添加计分规则...")
        
        # 获取所有模板问题
        cursor.execute("""
            SELECT atq.id, atq.template_id, atq.question_text, atq.question_type, 
                   atq.options, atq.scoring, at.name as template_name
            FROM assessment_template_questions atq
            JOIN assessment_templates at ON atq.template_id = at.id
            ORDER BY atq.template_id, atq.id
        """)
        
        questions = cursor.fetchall()
        
        print(f"找到 {len(questions)} 个问题")
        
        updated_count = 0
        
        for question in questions:
            question_id, template_id, question_text, question_type, options_str, scoring, template_name = question
            
            print(f"\n处理问题 {question_id}: {question_text[:50]}...")
            print(f"模板: {template_name}")
            print(f"当前scoring: {scoring}")
            
            # 如果已经有scoring规则，跳过
            if scoring and scoring.strip() and scoring != 'null':
                print("已有计分规则，跳过")
                continue
            
            # 解析选项
            try:
                if options_str:
                    options = json.loads(options_str)
                else:
                    options = []
            except:
                options = []
            
            # 根据模板类型和问题类型生成计分规则
            new_scoring = None
            
            if template_name == "简易精神状态检查量表" or "MMSE" in template_name.upper():
                # MMSE量表：大部分题目正确答案得1分
                if question_type == "single_choice" and options:
                    # 为单选题创建计分规则
                    scoring_rules = {}
                    for i, option in enumerate(options):
                        # 通常第一个选项是正确答案，得1分
                        if i == 0:
                            scoring_rules[str(i)] = 1
                        else:
                            scoring_rules[str(i)] = 0
                    new_scoring = json.dumps(scoring_rules)
                elif question_type == "text_input":
                    # 文本输入题：正确答案得分
                    new_scoring = json.dumps({"correct": 1, "incorrect": 0})
                    
            elif template_name == "蒙特利尔认知评估量表" or "MOCA" in template_name.upper():
                # MoCA量表：根据题目类型设置计分
                if question_type == "single_choice" and options:
                    scoring_rules = {}
                    for i, option in enumerate(options):
                        # 第一个选项通常是正确答案
                        if i == 0:
                            scoring_rules[str(i)] = 1
                        else:
                            scoring_rules[str(i)] = 0
                    new_scoring = json.dumps(scoring_rules)
                elif question_type == "text_input":
                    new_scoring = json.dumps({"correct": 1, "incorrect": 0})
                elif question_type == "drawing":
                    # 绘图题
                    new_scoring = json.dumps({"correct": 1, "partial": 0.5, "incorrect": 0})
                    
            elif "汉密尔顿" in template_name or "Hamilton" in template_name:
                # 汉密尔顿抑郁量表：0-4分制
                if question_type == "single_choice" and options:
                    scoring_rules = {}
                    for i, option in enumerate(options):
                        scoring_rules[str(i)] = i  # 选项索引即为分数
                    new_scoring = json.dumps(scoring_rules)
                    
            elif "焦虑" in template_name or "Anxiety" in template_name:
                # 焦虑量表：通常0-3分制
                if question_type == "single_choice" and options:
                    scoring_rules = {}
                    for i, option in enumerate(options):
                        scoring_rules[str(i)] = i
                    new_scoring = json.dumps(scoring_rules)
            
            # 如果没有特定规则，使用默认规则
            if not new_scoring and question_type == "single_choice" and options:
                scoring_rules = {}
                for i, option in enumerate(options):
                    scoring_rules[str(i)] = i  # 默认：选项索引为分数
                new_scoring = json.dumps(scoring_rules)
            
            # 更新数据库
            if new_scoring:
                cursor.execute("""
                    UPDATE assessment_template_questions 
                    SET scoring = ? 
                    WHERE id = ?
                """, (new_scoring, question_id))
                
                updated_count += 1
                print(f"更新计分规则: {new_scoring}")
            else:
                print("无法生成计分规则")
        
        # 提交更改
        conn.commit()
        print(f"\n成功更新了 {updated_count} 个问题的计分规则")
        
        # 验证更新结果
        print("\n验证更新结果:")
        cursor.execute("""
            SELECT COUNT(*) as total,
                   SUM(CASE WHEN scoring IS NULL OR scoring = '' OR scoring = 'null' THEN 1 ELSE 0 END) as no_scoring
            FROM assessment_template_questions
        """)
        
        result = cursor.fetchone()
        total, no_scoring = result
        print(f"总问题数: {total}")
        print(f"无计分规则的问题数: {no_scoring}")
        print(f"有计分规则的问题数: {total - no_scoring}")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    add_scoring_rules()