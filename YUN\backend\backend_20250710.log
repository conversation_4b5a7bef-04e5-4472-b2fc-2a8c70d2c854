2025-07-10 12:33:47,562 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-10 12:33:47,570 - auth_service - INFO - 统一认证服务初始化完成
2025-07-10 12:33:47,712 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-10 12:33:47,715 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-10 12:33:48,288 - BackendMockDataManager - INFO - 后端模拟数据模式已启用
2025-07-10 12:33:49,469 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\psutil\__init__.py
2025-07-10 12:33:49,472 - fallback_manager - DEBUG - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-07-10 12:33:49,473 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-07-10 12:33:49,567 - health_monitor - INFO - 健康监控器初始化完成
2025-07-10 12:33:49,603 - app.core.system_monitor - INFO - 已加载 288 个历史数据点
2025-07-10 12:33:49,630 - app.core.alert_detector - INFO - 已加载 6 个告警规则
2025-07-10 12:33:49,631 - app.core.alert_detector - INFO - 已加载 0 个当前告警
2025-07-10 12:33:49,659 - app.core.alert_detector - INFO - 已加载 370 个历史告警
2025-07-10 12:33:49,677 - app.core.alert_detector - INFO - 已加载 1 个通知渠道
2025-07-10 12:33:49,680 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-10 12:33:49,693 - alert_manager - INFO - 已初始化默认告警规则
2025-07-10 12:33:49,695 - alert_manager - INFO - 已初始化默认通知渠道
2025-07-10 12:33:49,697 - alert_manager - INFO - 告警管理器初始化完成
2025-07-10 12:33:50,687 - db_service - INFO - 数据库引擎和会话工厂创建成功
2025-07-10 12:33:50,690 - db_service - INFO - 数据库服务初始化完成
2025-07-10 12:33:50,701 - notification_service - INFO - 通知服务初始化完成
2025-07-10 12:33:50,703 - main - INFO - 错误处理模块导入成功
2025-07-10 12:33:50,770 - main - INFO - 监控模块导入成功
2025-07-10 12:33:50,772 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-07-10 12:33:55,492 - main - INFO - 应用启动中...
2025-07-10 12:33:55,493 - error_handling - INFO - 错误处理已设置
2025-07-10 12:33:55,494 - main - INFO - 错误处理系统初始化完成
2025-07-10 12:33:55,496 - monitoring - INFO - 添加指标端点成功: /metrics
2025-07-10 12:33:55,521 - monitoring - INFO - 添加健康检查端点成功: /health
2025-07-10 12:33:55,534 - monitoring - INFO - 添加详细健康检查端点成功: /api/health/detailed
2025-07-10 12:33:55,583 - monitoring - INFO - 系统信息初始化完成: Markey, Windows-10-10.0.19045-SP0, Python 3.13.2, CPU核心数: 4
2025-07-10 12:33:55,825 - monitoring - INFO - 启动资源监控线程成功
2025-07-10 12:33:55,830 - monitoring - INFO - 监控系统初始化成功（不使用中间件）
2025-07-10 12:33:55,833 - monitoring - INFO - 监控系统初始化完成
2025-07-10 12:33:55,834 - main - INFO - 监控系统初始化完成
2025-07-10 12:33:55,838 - app.db.init_db - INFO - 所有模型导入成功
2025-07-10 12:33:55,840 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-07-10 12:33:55,856 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-10 12:33:55,857 - app.db.init_db - INFO - 所有模型导入成功
2025-07-10 12:33:55,858 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-07-10 12:33:55,860 - app.db.init_db - INFO - 正在运行数据库迁移...
2025-07-10 12:33:55,861 - app.db.init_db - INFO - 正在检查并更新数据库表结构...
2025-07-10 12:33:55,863 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-10 12:33:55,865 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alerts")
2025-07-10 12:33:55,867 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:55,877 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_rules")
2025-07-10 12:33:55,880 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:55,884 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_channels")
2025-07-10 12:33:55,886 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:55,889 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-10 12:33:55,891 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:55,893 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_records")
2025-07-10 12:33:55,895 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:55,898 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_overviews")
2025-07-10 12:33:55,899 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:55,928 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medical_records")
2025-07-10 12:33:55,979 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.3%, CPU使用率 100.0%
2025-07-10 12:33:56,006 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:56,097 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("inpatient_records")
2025-07-10 12:33:56,210 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:56,258 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("surgery_records")
2025-07-10 12:33:56,305 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:56,339 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_reports")
2025-07-10 12:33:56,357 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:56,376 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_report_items")
2025-07-10 12:33:56,387 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:56,396 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_reports")
2025-07-10 12:33:56,407 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:56,425 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("examination_reports")
2025-07-10 12:33:56,447 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:56,459 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("documents")
2025-07-10 12:33:56,502 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:56,557 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("other_records")
2025-07-10 12:33:56,728 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:56,847 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("operation_logs")
2025-07-10 12:33:56,971 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:57,030 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("follow_up_records")
2025-07-10 12:33:57,037 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:57,074 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_diaries")
2025-07-10 12:33:57,156 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:57,200 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("registration_records")
2025-07-10 12:33:57,261 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:57,312 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("prescription_records")
2025-07-10 12:33:57,361 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:57,431 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("laboratory_records")
2025-07-10 12:33:57,472 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:57,509 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_records")
2025-07-10 12:33:57,538 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:57,561 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medications")
2025-07-10 12:33:57,580 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:57,609 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medication_usages")
2025-07-10 12:33:57,644 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:57,667 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("role_applications")
2025-07-10 12:33:57,694 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:57,737 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessments")
2025-07-10 12:33:57,773 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:57,814 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_items")
2025-07-10 12:33:57,858 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:57,894 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_responses")
2025-07-10 12:33:57,919 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:57,950 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_templates")
2025-07-10 12:33:57,958 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:57,991 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_template_questions")
2025-07-10 12:33:58,024 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:58,040 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaires")
2025-07-10 12:33:58,048 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:58,053 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_items")
2025-07-10 12:33:58,056 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:58,085 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_responses")
2025-07-10 12:33:58,139 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:58,208 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_answers")
2025-07-10 12:33:58,276 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:58,352 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_templates")
2025-07-10 12:33:58,418 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:58,471 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_template_questions")
2025-07-10 12:33:58,551 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:58,652 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_results")
2025-07-10 12:33:58,722 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:58,763 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_results")
2025-07-10 12:33:58,792 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:58,811 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("report_templates")
2025-07-10 12:33:58,833 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:58,872 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_distributions")
2025-07-10 12:33:58,917 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:58,951 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_distributions")
2025-07-10 12:33:59,008 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:59,038 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("service_stats")
2025-07-10 12:33:59,071 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-10 12:33:59,107 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-10 12:33:59,137 - app.db.init_db - INFO - 数据库表结构检查和更新完成
2025-07-10 12:33:59,159 - app.db.init_db - INFO - 模型关系初始化完成
2025-07-10 12:33:59,178 - app.db.init_db - INFO - 模型关系设置完成
2025-07-10 12:33:59,198 - main - INFO - 数据库初始化完成（强制重建）
2025-07-10 12:33:59,210 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-10 12:33:59,232 - main - INFO - 数据库连接正常
2025-07-10 12:33:59,243 - main - INFO - 开始初始化模板数据
2025-07-10 12:33:59,274 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-10 12:33:59,897 - app.db.init_templates - INFO - 开始初始化评估量表模板...
2025-07-10 12:34:00,001 - app.db.init_templates - INFO - 成功初始化 5 个评估量表模板
2025-07-10 12:34:00,093 - app.db.init_templates - INFO - 开始初始化调查问卷模板...
2025-07-10 12:34:00,199 - app.db.init_templates - INFO - 成功初始化 5 个调查问卷模板
2025-07-10 12:34:00,202 - main - INFO - 模板数据初始化完成
2025-07-10 12:34:00,205 - main - INFO - 数据库表结构已经标准化，不需要迁移
2025-07-10 12:34:00,206 - main - INFO - 应用启动完成
2025-07-10 12:34:11,376 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.1%, CPU使用率 100.0%
