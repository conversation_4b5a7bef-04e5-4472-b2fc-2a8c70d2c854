<template>
  <div class="test-execution-engine">
    <!-- 页面标题 -->
    <div class="engine-header">
      <div class="header-content">
        <h1>测试执行引擎</h1>
        <p class="header-description">统一的测试执行、调度和监控平台</p>
      </div>
      
      <div class="header-actions">
        <el-button type="primary" size="large" @click="startExecution" :loading="isExecuting">
          <el-icon><VideoPlay /></el-icon>
          开始执行
        </el-button>
        <el-button size="large" @click="stopAllExecution" :disabled="!hasRunningTests">
          <el-icon><VideoPause /></el-icon>
          停止全部
        </el-button>
        <el-button size="large" @click="showEngineSettings = true">
          <el-icon><Setting /></el-icon>
          引擎设置
        </el-button>
      </div>
    </div>

    <!-- 执行状态概览 -->
    <div class="execution-overview">
      <div class="overview-card">
        <div class="card-icon running">
          <el-icon><Loading /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-value">{{ executionStats.running }}</div>
          <div class="card-label">运行中</div>
        </div>
      </div>
      
      <div class="overview-card">
        <div class="card-icon queued">
          <el-icon><Clock /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-value">{{ executionStats.queued }}</div>
          <div class="card-label">队列中</div>
        </div>
      </div>
      
      <div class="overview-card">
        <div class="card-icon completed">
          <el-icon><Check /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-value">{{ executionStats.completed }}</div>
          <div class="card-label">已完成</div>
        </div>
      </div>
      
      <div class="overview-card">
        <div class="card-icon failed">
          <el-icon><Close /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-value">{{ executionStats.failed }}</div>
          <div class="card-label">失败</div>
        </div>
      </div>
    </div>

    <!-- 执行队列和实时监控 -->
    <div class="execution-content">
      <div class="left-panel">
        <!-- 执行队列 -->
        <el-card class="queue-card">
          <template #header>
            <div class="card-header">
              <span>执行队列</span>
              <div class="header-actions">
                <el-button size="small" @click="clearQueue" :disabled="executionQueue.length === 0">
                  清空队列
                </el-button>
                <el-button size="small" @click="refreshQueue">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </div>
            </div>
          </template>
          
          <div class="queue-list">
            <div 
              v-for="item in executionQueue" 
              :key="item.id"
              class="queue-item"
              :class="item.status"
            >
              <div class="item-info">
                <div class="item-name">{{ item.name }}</div>
                <div class="item-type">{{ item.type }}</div>
              </div>
              
              <div class="item-status">
                <el-tag :type="getStatusType(item.status)" size="small">
                  {{ getStatusText(item.status) }}
                </el-tag>
              </div>
              
              <div class="item-progress" v-if="item.status === 'running'">
                <el-progress 
                  :percentage="item.progress" 
                  :stroke-width="4" 
                  :show-text="false"
                />
              </div>
              
              <div class="item-actions">
                <el-button 
                  size="small" 
                  type="text" 
                  @click="viewItemDetails(item)"
                >
                  详情
                </el-button>
                <el-button 
                  size="small" 
                  type="text" 
                  @click="cancelExecution(item.id)"
                  v-if="item.status === 'running' || item.status === 'queued'"
                >
                  取消
                </el-button>
              </div>
            </div>
            
            <div v-if="executionQueue.length === 0" class="empty-queue">
              <el-empty description="暂无执行任务" />
            </div>
          </div>
        </el-card>
        
        <!-- 执行历史 -->
        <el-card class="history-card">
          <template #header>
            <div class="card-header">
              <span>最近执行</span>
              <el-button size="small" @click="viewAllHistory">
                查看全部
              </el-button>
            </div>
          </template>
          
          <div class="history-list">
            <div 
              v-for="item in recentHistory" 
              :key="item.id"
              class="history-item"
            >
              <div class="item-info">
                <div class="item-name">{{ item.name }}</div>
                <div class="item-time">{{ formatTime(item.endTime) }}</div>
              </div>
              
              <div class="item-result">
                <el-tag :type="getResultType(item.result)" size="small">
                  {{ item.result }}
                </el-tag>
              </div>
              
              <div class="item-duration">
                {{ formatDuration(item.duration) }}
              </div>
            </div>
          </div>
        </el-card>
      </div>
      
      <div class="right-panel">
        <!-- 实时监控 -->
        <el-card class="monitoring-card">
          <template #header>
            <div class="card-header">
              <span>实时监控</span>
              <el-switch v-model="autoRefresh" active-text="自动刷新" />
            </div>
          </template>
          
          <div class="monitoring-content">
            <!-- 系统资源 -->
            <div class="resource-section">
              <h4>系统资源</h4>
              <div class="resource-metrics">
                <div class="metric-item">
                  <div class="metric-label">CPU使用率</div>
                  <el-progress 
                    :percentage="systemMetrics.cpu" 
                    :color="getProgressColor(systemMetrics.cpu)"
                  />
                </div>
                
                <div class="metric-item">
                  <div class="metric-label">内存使用</div>
                  <el-progress 
                    :percentage="systemMetrics.memory" 
                    :color="getProgressColor(systemMetrics.memory)"
                  />
                </div>
                
                <div class="metric-item">
                  <div class="metric-label">磁盘I/O</div>
                  <el-progress 
                    :percentage="systemMetrics.disk" 
                    :color="getProgressColor(systemMetrics.disk)"
                  />
                </div>
              </div>
            </div>
            
            <!-- 执行统计 -->
            <div class="stats-section">
              <h4>执行统计</h4>
              <div class="stats-grid">
                <div class="stat-item">
                  <div class="stat-value">{{ todayStats.total }}</div>
                  <div class="stat-label">今日执行</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ todayStats.success }}%</div>
                  <div class="stat-label">成功率</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ formatDuration(todayStats.avgTime) }}</div>
                  <div class="stat-label">平均耗时</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ todayStats.coverage }}%</div>
                  <div class="stat-label">覆盖率</div>
                </div>
              </div>
            </div>
            
            <!-- 性能趋势图 -->
            <div class="chart-section">
              <h4>性能趋势</h4>
              <div ref="performanceChart" class="performance-chart"></div>
            </div>
          </div>
        </el-card>
        
        <!-- 执行日志 -->
        <el-card class="logs-card">
          <template #header>
            <div class="card-header">
              <span>执行日志</span>
              <div class="header-actions">
                <el-button size="small" @click="clearLogs">
                  清空日志
                </el-button>
                <el-button size="small" @click="downloadLogs">
                  下载日志
                </el-button>
              </div>
            </div>
          </template>
          
          <div class="logs-content">
            <div 
              v-for="log in executionLogs" 
              :key="log.id"
              class="log-item"
              :class="log.level"
            >
              <div class="log-time">{{ formatLogTime(log.timestamp) }}</div>
              <div class="log-level">{{ log.level.toUpperCase() }}</div>
              <div class="log-message">{{ log.message }}</div>
            </div>
            
            <div v-if="executionLogs.length === 0" class="empty-logs">
              <el-empty description="暂无日志" />
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 执行详情对话框 -->
    <el-dialog
      v-model="showExecutionDetails"
      title="执行详情"
      width="800px"
    >
      <div class="execution-details" v-if="selectedExecution">
        <div class="details-section">
          <h4>基本信息</h4>
          <div class="info-grid">
            <div class="info-item">
              <label>任务名称：</label>
              <span>{{ selectedExecution.name }}</span>
            </div>
            <div class="info-item">
              <label>任务类型：</label>
              <span>{{ selectedExecution.type }}</span>
            </div>
            <div class="info-item">
              <label>执行状态：</label>
              <el-tag :type="getStatusType(selectedExecution.status)">
                {{ getStatusText(selectedExecution.status) }}
              </el-tag>
            </div>
            <div class="info-item">
              <label>开始时间：</label>
              <span>{{ formatTime(selectedExecution.startTime) }}</span>
            </div>
            <div class="info-item" v-if="selectedExecution.endTime">
              <label>结束时间：</label>
              <span>{{ formatTime(selectedExecution.endTime) }}</span>
            </div>
            <div class="info-item" v-if="selectedExecution.duration">
              <label>执行时长：</label>
              <span>{{ formatDuration(selectedExecution.duration) }}</span>
            </div>
          </div>
        </div>
        
        <div class="details-section" v-if="selectedExecution.config">
          <h4>执行配置</h4>
          <pre class="config-content">{{ JSON.stringify(selectedExecution.config, null, 2) }}</pre>
        </div>
        
        <div class="details-section" v-if="selectedExecution.results">
          <h4>执行结果</h4>
          <div class="results-grid">
            <div class="result-item">
              <label>通过：</label>
              <span class="success">{{ selectedExecution.results.passed }}</span>
            </div>
            <div class="result-item">
              <label>失败：</label>
              <span class="error">{{ selectedExecution.results.failed }}</span>
            </div>
            <div class="result-item">
              <label>跳过：</label>
              <span class="warning">{{ selectedExecution.results.skipped }}</span>
            </div>
            <div class="result-item">
              <label>覆盖率：</label>
              <span>{{ selectedExecution.results.coverage }}%</span>
            </div>
          </div>
        </div>
        
        <div class="details-section" v-if="selectedExecution.logs">
          <h4>执行日志</h4>
          <div class="execution-logs">
            <div 
              v-for="(log, index) in selectedExecution.logs" 
              :key="index"
              class="log-line"
            >
              {{ log }}
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showExecutionDetails = false">关闭</el-button>
          <el-button type="primary" @click="downloadExecutionReport">
            下载报告
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 引擎设置对话框 -->
    <el-dialog
      v-model="showEngineSettings"
      title="引擎设置"
      width="600px"
    >
      <div class="engine-settings">
        <el-form :model="engineConfig" label-width="120px">
          <el-form-item label="最大并发数">
            <el-input-number 
              v-model="engineConfig.maxConcurrency" 
              :min="1" 
              :max="20" 
              style="width: 100%;"
            />
          </el-form-item>
          
          <el-form-item label="队列大小">
            <el-input-number 
              v-model="engineConfig.queueSize" 
              :min="10" 
              :max="1000" 
              style="width: 100%;"
            />
          </el-form-item>
          
          <el-form-item label="超时时间">
            <el-input-number 
              v-model="engineConfig.timeout" 
              :min="1000" 
              :max="3600000" 
              :step="1000"
              style="width: 100%;"
            />
          </el-form-item>
          
          <el-form-item label="重试次数">
            <el-input-number 
              v-model="engineConfig.retries" 
              :min="0" 
              :max="10" 
              style="width: 100%;"
            />
          </el-form-item>
          
          <el-form-item label="日志级别">
            <el-select v-model="engineConfig.logLevel" style="width: 100%;">
              <el-option label="DEBUG" value="debug" />
              <el-option label="INFO" value="info" />
              <el-option label="WARN" value="warn" />
              <el-option label="ERROR" value="error" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="自动清理">
            <el-switch v-model="engineConfig.autoCleanup" />
          </el-form-item>
          
          <el-form-item label="通知设置">
            <el-checkbox-group v-model="engineConfig.notifications">
              <el-checkbox label="email">邮件通知</el-checkbox>
              <el-checkbox label="webhook">Webhook</el-checkbox>
              <el-checkbox label="browser">浏览器通知</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showEngineSettings = false">取消</el-button>
          <el-button type="primary" @click="saveEngineSettings">保存设置</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import {
  VideoPlay, VideoPause, Setting, Loading, Clock, Check, Close, 
  Refresh
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// 响应式数据
const isExecuting = ref(false)
const showExecutionDetails = ref(false)
const showEngineSettings = ref(false)
const autoRefresh = ref(true)
const selectedExecution = ref(null)

// 执行统计
const executionStats = reactive({
  running: 3,
  queued: 7,
  completed: 156,
  failed: 8
})

// 系统指标
const systemMetrics = reactive({
  cpu: 45,
  memory: 67,
  disk: 23
})

// 今日统计
const todayStats = reactive({
  total: 42,
  success: 94.2,
  avgTime: 2340,
  coverage: 87.5
})

// 执行队列
const executionQueue = ref([
  {
    id: 'exec-1',
    name: '用户模块单元测试',
    type: 'unit',
    status: 'running',
    progress: 65,
    startTime: new Date(Date.now() - 5 * 60 * 1000)
  },
  {
    id: 'exec-2',
    name: 'API集成测试',
    type: 'integration',
    status: 'queued',
    progress: 0,
    startTime: null
  },
  {
    id: 'exec-3',
    name: 'E2E回归测试',
    type: 'e2e',
    status: 'queued',
    progress: 0,
    startTime: null
  }
])

// 最近历史
const recentHistory = ref([
  {
    id: 'hist-1',
    name: '登录功能测试',
    result: '成功',
    duration: 1200,
    endTime: new Date(Date.now() - 10 * 60 * 1000)
  },
  {
    id: 'hist-2',
    name: '数据库连接测试',
    result: '失败',
    duration: 800,
    endTime: new Date(Date.now() - 25 * 60 * 1000)
  },
  {
    id: 'hist-3',
    name: '权限验证测试',
    result: '成功',
    duration: 1500,
    endTime: new Date(Date.now() - 45 * 60 * 1000)
  }
])

// 执行日志
const executionLogs = ref([
  {
    id: 'log-1',
    timestamp: new Date(Date.now() - 2 * 60 * 1000),
    level: 'info',
    message: '开始执行用户模块单元测试'
  },
  {
    id: 'log-2',
    timestamp: new Date(Date.now() - 3 * 60 * 1000),
    level: 'warn',
    message: 'API响应时间超过预期阈值'
  },
  {
    id: 'log-3',
    timestamp: new Date(Date.now() - 5 * 60 * 1000),
    level: 'error',
    message: '数据库连接测试失败'
  }
])

// 引擎配置
const engineConfig = reactive({
  maxConcurrency: 5,
  queueSize: 100,
  timeout: 30000,
  retries: 2,
  logLevel: 'info',
  autoCleanup: true,
  notifications: ['browser']
})

// 计算属性
const hasRunningTests = computed(() => {
  return executionQueue.value.some(item => item.status === 'running')
})

// 图表引用
const performanceChart = ref(null)
let chartInstance = null

// 定时器
let refreshTimer = null

// 方法
const startExecution = async () => {
  try {
    isExecuting.value = true
    ElMessage.info('开始执行测试任务...')
    
    // 模拟执行过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('测试任务已加入执行队列')
  } catch (error) {
    ElMessage.error('启动执行失败: ' + error.message)
  } finally {
    isExecuting.value = false
  }
}

const stopAllExecution = async () => {
  try {
    ElMessage.info('正在停止所有执行任务...')
    
    // 模拟停止过程
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 更新队列状态
    executionQueue.value.forEach(item => {
      if (item.status === 'running' || item.status === 'queued') {
        item.status = 'cancelled'
      }
    })
    
    ElMessage.success('所有执行任务已停止')
  } catch (error) {
    ElMessage.error('停止执行失败: ' + error.message)
  }
}

const clearQueue = () => {
  executionQueue.value = executionQueue.value.filter(item => 
    item.status === 'running'
  )
  ElMessage.success('队列已清空')
}

const refreshQueue = async () => {
  try {
    ElMessage.info('正在刷新队列...')
    
    // 模拟刷新
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('队列已刷新')
  } catch (error) {
    ElMessage.error('刷新失败: ' + error.message)
  }
}

const viewItemDetails = (item) => {
  selectedExecution.value = {
    ...item,
    config: {
      timeout: 30000,
      retries: 2,
      parallel: true
    },
    results: {
      passed: 15,
      failed: 2,
      skipped: 1,
      coverage: 85.6
    },
    logs: [
      '开始执行测试...',
      '初始化测试环境',
      '运行测试用例',
      '生成测试报告'
    ]
  }
  showExecutionDetails.value = true
}

const cancelExecution = async (id) => {
  try {
    const item = executionQueue.value.find(item => item.id === id)
    if (item) {
      item.status = 'cancelled'
      ElMessage.success('任务已取消')
    }
  } catch (error) {
    ElMessage.error('取消任务失败: ' + error.message)
  }
}

const viewAllHistory = () => {
  // 跳转到历史分析页面
  ElMessage.info('跳转到历史分析页面')
}

const clearLogs = () => {
  executionLogs.value = []
  ElMessage.success('日志已清空')
}

const downloadLogs = () => {
  // 模拟下载日志
  const logs = executionLogs.value.map(log => 
    `[${formatLogTime(log.timestamp)}] ${log.level.toUpperCase()}: ${log.message}`
  ).join('\n')
  
  const blob = new Blob([logs], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `execution-logs-${new Date().toISOString().slice(0, 10)}.txt`
  a.click()
  URL.revokeObjectURL(url)
  
  ElMessage.success('日志下载完成')
}

const downloadExecutionReport = () => {
  // 模拟下载执行报告
  ElMessage.success('报告下载完成')
}

const saveEngineSettings = async () => {
  try {
    ElMessage.info('正在保存设置...')
    
    // 模拟保存过程
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    showEngineSettings.value = false
    ElMessage.success('设置保存成功')
  } catch (error) {
    ElMessage.error('保存设置失败: ' + error.message)
  }
}

// 辅助函数
const getStatusType = (status) => {
  const types = {
    running: 'primary',
    queued: 'info',
    completed: 'success',
    failed: 'danger',
    cancelled: 'warning'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    running: '运行中',
    queued: '队列中',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const getResultType = (result) => {
  const types = {
    '成功': 'success',
    '失败': 'danger',
    '警告': 'warning'
  }
  return types[result] || 'info'
}

const getProgressColor = (percentage) => {
  if (percentage < 50) return '#67c23a'
  if (percentage < 80) return '#e6a23c'
  return '#f56c6c'
}

const formatTime = (time) => {
  if (!time) return '-'
  return new Date(time).toLocaleString()
}

const formatLogTime = (time) => {
  return new Date(time).toLocaleTimeString()
}

const formatDuration = (ms) => {
  if (!ms) return '-'
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`
  } else {
    return `${seconds}s`
  }
}

// 初始化性能图表
const initPerformanceChart = () => {
  if (!performanceChart.value) return
  
  chartInstance = echarts.init(performanceChart.value)
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['执行时间', 'CPU使用率', '内存使用率']
    },
    xAxis: {
      type: 'category',
      data: ['09:00', '10:00', '11:00', '12:00', '13:00', '14:00']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '执行时间',
        type: 'line',
        data: [2.3, 2.1, 2.8, 2.5, 2.2, 2.4]
      },
      {
        name: 'CPU使用率',
        type: 'line',
        data: [45, 52, 38, 61, 47, 55]
      },
      {
        name: '内存使用率',
        type: 'line',
        data: [67, 71, 65, 73, 69, 72]
      }
    ]
  }
  
  chartInstance.setOption(option)
}

// 自动刷新数据
const startAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
  
  refreshTimer = setInterval(() => {
    if (autoRefresh.value) {
      // 模拟数据更新
      systemMetrics.cpu = Math.floor(Math.random() * 100)
      systemMetrics.memory = Math.floor(Math.random() * 100)
      systemMetrics.disk = Math.floor(Math.random() * 100)
      
      // 更新执行进度
      executionQueue.value.forEach(item => {
        if (item.status === 'running' && item.progress < 100) {
          item.progress = Math.min(100, item.progress + Math.floor(Math.random() * 10))
        }
      })
    }
  }, 2000)
}

// 生命周期
onMounted(() => {
  nextTick(() => {
    initPerformanceChart()
    startAutoRefresh()
  })
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style scoped>
.test-execution-engine {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.engine-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content h1 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.execution-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.overview-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
}

.card-icon.running {
  background-color: #e1f3ff;
  color: #409eff;
}

.card-icon.queued {
  background-color: #f0f9ff;
  color: #909399;
}

.card-icon.completed {
  background-color: #f0f9ff;
  color: #67c23a;
}

.card-icon.failed {
  background-color: #fef0f0;
  color: #f56c6c;
}

.card-content {
  flex: 1;
}

.card-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.card-label {
  font-size: 14px;
  color: #909399;
}

.execution-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.left-panel,
.right-panel {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.queue-list,
.history-list {
  max-height: 400px;
  overflow-y: auto;
}

.queue-item,
.history-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  margin-bottom: 8px;
  background: #fafafa;
}

.queue-item.running {
  border-color: #409eff;
  background: #ecf5ff;
}

.item-info {
  flex: 1;
}

.item-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.item-type,
.item-time {
  font-size: 12px;
  color: #909399;
}

.item-status,
.item-result {
  margin: 0 12px;
}

.item-progress {
  width: 100px;
  margin: 0 12px;
}

.item-actions {
  display: flex;
  gap: 8px;
}

.item-duration {
  font-size: 12px;
  color: #909399;
  min-width: 60px;
  text-align: right;
}

.empty-queue,
.empty-logs {
  text-align: center;
  padding: 40px 20px;
}

.monitoring-content {
  padding: 0;
}

.resource-section,
.stats-section,
.chart-section {
  margin-bottom: 24px;
}

.resource-section h4,
.stats-section h4,
.chart-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 500;
}

.resource-metrics {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.metric-label {
  min-width: 80px;
  font-size: 14px;
  color: #606266;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.performance-chart {
  height: 200px;
  width: 100%;
}

.logs-content {
  max-height: 300px;
  overflow-y: auto;
  padding: 0;
}

.log-item {
  display: flex;
  align-items: flex-start;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-item.error {
  background-color: #fef0f0;
  color: #f56c6c;
}

.log-item.warn {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.log-item.info {
  background-color: #f0f9ff;
  color: #409eff;
}

.log-time {
  min-width: 80px;
  color: #909399;
  margin-right: 8px;
}

.log-level {
  min-width: 50px;
  font-weight: 600;
  margin-right: 8px;
}

.log-message {
  flex: 1;
  word-break: break-all;
}

.execution-details {
  max-height: 600px;
  overflow-y: auto;
}

.details-section {
  margin-bottom: 24px;
}

.details-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 500;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.info-grid,
.results-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.info-item,
.result-item {
  display: flex;
  align-items: center;
}

.info-item label,
.result-item label {
  min-width: 80px;
  font-weight: 500;
  color: #606266;
}

.result-item .success {
  color: #67c23a;
  font-weight: 600;
}

.result-item .error {
  color: #f56c6c;
  font-weight: 600;
}

.result-item .warning {
  color: #e6a23c;
  font-weight: 600;
}

.config-content {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #303133;
  overflow-x: auto;
}

.execution-logs {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  max-height: 200px;
  overflow-y: auto;
}

.log-line {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #303133;
  margin-bottom: 4px;
  word-break: break-all;
}

.engine-settings {
  max-height: 500px;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .execution-content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .test-execution-engine {
    padding: 10px;
  }
  
  .engine-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .header-actions {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .execution-overview {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .info-grid,
  .results-grid {
    grid-template-columns: 1fr;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .test-execution-engine {
    background-color: #1a1a1a;
    color: #e4e7ed;
  }
  
  .engine-header,
  .overview-card,
  .el-card {
    background-color: #2d2d2d;
    border-color: #4c4d4f;
  }
  
  .header-content h1 {
    color: #e4e7ed;
  }
  
  .queue-item,
  .history-item {
    background-color: #3a3a3a;
    border-color: #4c4d4f;
  }
  
  .stat-item {
    background-color: #3a3a3a;
  }
  
  .config-content,
  .execution-logs {
    background-color: #3a3a3a;
    color: #e4e7ed;
  }
}
</style>