# -*- coding: utf-8 -*-
"""
简化的API结构检查
不依赖登录，直接分析API响应格式
"""

import requests
import json
from datetime import datetime

# 配置
BASE_URL = "http://127.0.0.1:8006"

def check_api_without_auth():
    """检查API响应（不需要认证）"""
    print("=== 检查API响应结构（无认证） ===")
    
    # 检查几个可能的API端点
    endpoints = [
        "/api/clinical-scales/standard-assessments",
        "/api/assessments",
        "/api/health-check"
    ]
    
    for endpoint in endpoints:
        print(f"\n🔍 检查端点: {endpoint}")
        try:
            response = requests.get(f"{BASE_URL}{endpoint}")
            print(f"状态码: {response.status_code}")
            
            if response.status_code in [200, 401, 403]:
                try:
                    data = response.json()
                    print(f"响应数据类型: {type(data)}")
                    
                    if isinstance(data, dict):
                        print(f"响应字段: {list(data.keys())}")
                        
                        # 如果有data字段，分析其结构
                        if 'data' in data:
                            data_field = data['data']
                            print(f"data字段类型: {type(data_field)}")
                            if isinstance(data_field, list) and data_field:
                                first_item = data_field[0]
                                if isinstance(first_item, dict):
                                    print(f"data数组第一项字段: {list(first_item.keys())}")
                                    
                    elif isinstance(data, list) and data:
                        first_item = data[0]
                        if isinstance(first_item, dict):
                            print(f"数组第一项字段: {list(first_item.keys())}")
                            
                    # 输出部分响应内容
                    response_str = json.dumps(data, ensure_ascii=False, indent=2)
                    if len(response_str) > 500:
                        response_str = response_str[:500] + "..."
                    print(f"响应内容: {response_str}")
                    
                except Exception as json_error:
                    print(f"JSON解析失败: {json_error}")
                    print(f"原始响应: {response.text[:200]}...")
            else:
                print(f"响应内容: {response.text[:200]}...")
                
        except Exception as e:
            print(f"请求失败: {str(e)}")

def analyze_expected_frontend_structure():
    """分析前端期望的数据结构"""
    print("\n=== 前端期望的数据结构分析 ===")
    
    expected_structure = {
        "assessments": [
            {
                "id": "量表ID",
                "name": "量表名称", 
                "category": "分类",
                "item_count": "项目数",
                "target": "适用对象",
                "created_at": "创建时间"
            }
        ]
    }
    
    print("前端期望的assessments数组结构:")
    print(json.dumps(expected_structure, ensure_ascii=False, indent=2))
    
    print("\n前端模板中使用的字段:")
    frontend_fields = [
        "id - 显示在ID列",
        "name - 显示在量表名称列", 
        "category - 显示在分类列",
        "item_count - 显示在项目数列",
        "target - 显示在适用对象列",
        "created_at - 显示在创建时间列"
    ]
    
    for field in frontend_fields:
        print(f"  • {field}")

def suggest_data_mapping():
    """建议数据映射方案"""
    print("\n=== 数据映射建议 ===")
    
    # 常见的API字段到前端字段的映射
    common_mappings = {
        "template_id": "id",
        "template_name": "name",
        "assessment_name": "name",
        "question_count": "item_count",
        "item_count": "item_count",
        "category": "category",
        "type": "category",
        "target": "target",
        "target_group": "target",
        "created_at": "created_at",
        "create_time": "created_at",
        "creation_date": "created_at"
    }
    
    print("常见字段映射:")
    for api_field, frontend_field in common_mappings.items():
        print(f"  {api_field} -> {frontend_field}")
        
    print("\n建议的前端数据处理代码:")
    print("```javascript")
    print("// 在fetchAssessments函数中:")
    print("if (response.data && response.data.data) {")
    print("  assessments.value = response.data.data.map(item => ({")
    print("    id: item.template_id || item.id,")
    print("    name: item.template_name || item.assessment_name || item.name,")
    print("    category: item.category || item.type || '未分类',")
    print("    item_count: item.question_count || item.item_count || 0,")
    print("    target: item.target || item.target_group || '通用',")
    print("    created_at: item.created_at || item.create_time || ''")
    print("  }))")
    print("} else if (Array.isArray(response.data)) {")
    print("  assessments.value = response.data.map(item => ({")
    print("    // 同样的映射逻辑")
    print("  }))")
    print("}")
    print("```")

def main():
    print("=== 简化API结构检查 ===")
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"后端地址: {BASE_URL}")
    
    # 检查API响应
    check_api_without_auth()
    
    # 分析前端期望结构
    analyze_expected_frontend_structure()
    
    # 提供映射建议
    suggest_data_mapping()
    
    print("\n=== 检查完成 ===")
    print("\n💡 解决步骤:")
    print("1. 确认后端API返回的实际数据结构")
    print("2. 在前端fetchAssessments函数中添加字段映射")
    print("3. 确保数据正确赋值给assessments.value")
    print("4. 检查前端模板是否正确绑定字段")

if __name__ == "__main__":
    main()