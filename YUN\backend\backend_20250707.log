2025-07-07 09:35:36,879 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-07 09:35:36,886 - auth_service - INFO - 统一认证服务初始化完成
2025-07-07 09:35:36,995 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-07 09:35:36,998 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-07 09:35:37,387 - BackendMockDataManager - INFO - 后端模拟数据模式已启用
2025-07-07 09:35:38,058 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\psutil\__init__.py
2025-07-07 09:35:38,061 - fallback_manager - DEBUG - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-07-07 09:35:38,062 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-07-07 09:35:38,176 - health_monitor - INFO - 健康监控器初始化完成
2025-07-07 09:35:38,190 - app.core.system_monitor - INFO - 已加载 288 个历史数据点
2025-07-07 09:35:38,196 - app.core.alert_detector - INFO - 已加载 6 个告警规则
2025-07-07 09:35:38,198 - app.core.alert_detector - INFO - 已加载 0 个当前告警
2025-07-07 09:35:38,205 - app.core.alert_detector - INFO - 已加载 370 个历史告警
2025-07-07 09:35:38,207 - app.core.alert_detector - INFO - 已加载 1 个通知渠道
2025-07-07 09:35:38,212 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-07 09:35:38,220 - alert_manager - INFO - 已初始化默认告警规则
2025-07-07 09:35:38,221 - alert_manager - INFO - 已初始化默认通知渠道
2025-07-07 09:35:38,227 - alert_manager - INFO - 告警管理器初始化完成
2025-07-07 09:35:38,819 - db_service - INFO - 数据库引擎和会话工厂创建成功
2025-07-07 09:35:38,822 - db_service - INFO - 数据库服务初始化完成
2025-07-07 09:35:38,832 - notification_service - INFO - 通知服务初始化完成
2025-07-07 09:35:38,833 - main - INFO - 错误处理模块导入成功
2025-07-07 09:35:38,896 - main - INFO - 监控模块导入成功
2025-07-07 09:35:38,898 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-07-07 09:35:40,643 - main - INFO - 应用启动中...
2025-07-07 09:35:40,645 - error_handling - INFO - 错误处理已设置
2025-07-07 09:35:40,649 - main - INFO - 错误处理系统初始化完成
2025-07-07 09:35:40,650 - monitoring - INFO - 添加指标端点成功: /metrics
2025-07-07 09:35:40,652 - monitoring - INFO - 添加健康检查端点成功: /health
2025-07-07 09:35:40,653 - monitoring - INFO - 添加详细健康检查端点成功: /api/health/detailed
2025-07-07 09:35:40,654 - monitoring - INFO - 系统信息初始化完成: Markey, Windows-10-10.0.19045-SP0, Python 3.13.2, CPU核心数: 4
2025-07-07 09:35:40,658 - monitoring - INFO - 启动资源监控线程成功
2025-07-07 09:35:40,665 - monitoring - INFO - 监控系统初始化成功（不使用中间件）
2025-07-07 09:35:40,666 - monitoring - INFO - 监控系统初始化完成
2025-07-07 09:35:40,668 - main - INFO - 监控系统初始化完成
2025-07-07 09:35:40,670 - app.db.init_db - INFO - 所有模型导入成功
2025-07-07 09:35:40,671 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-07-07 09:35:40,684 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-07 09:35:40,686 - app.db.init_db - INFO - 所有模型导入成功
2025-07-07 09:35:40,687 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-07-07 09:35:40,688 - app.db.init_db - INFO - 正在运行数据库迁移...
2025-07-07 09:35:40,689 - app.db.init_db - INFO - 正在检查并更新数据库表结构...
2025-07-07 09:35:40,691 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-07 09:35:40,696 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alerts")
2025-07-07 09:35:40,698 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,705 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_rules")
2025-07-07 09:35:40,708 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,712 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_channels")
2025-07-07 09:35:40,714 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,716 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-07 09:35:40,718 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,720 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_records")
2025-07-07 09:35:40,722 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,728 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_overviews")
2025-07-07 09:35:40,731 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,733 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medical_records")
2025-07-07 09:35:40,735 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,737 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("inpatient_records")
2025-07-07 09:35:40,738 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,744 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("surgery_records")
2025-07-07 09:35:40,746 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,749 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_reports")
2025-07-07 09:35:40,750 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,752 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_report_items")
2025-07-07 09:35:40,754 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,759 - monitoring - DEBUG - 资源指标更新: 内存使用率 50.5%, CPU使用率 71.9%
2025-07-07 09:35:40,760 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_reports")
2025-07-07 09:35:40,762 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,765 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("examination_reports")
2025-07-07 09:35:40,767 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,769 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("documents")
2025-07-07 09:35:40,771 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,773 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("other_records")
2025-07-07 09:35:40,774 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,782 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("operation_logs")
2025-07-07 09:35:40,784 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,786 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("follow_up_records")
2025-07-07 09:35:40,787 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,790 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_diaries")
2025-07-07 09:35:40,794 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,797 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("registration_records")
2025-07-07 09:35:40,799 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,801 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("prescription_records")
2025-07-07 09:35:40,803 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,805 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("laboratory_records")
2025-07-07 09:35:40,812 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,819 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_records")
2025-07-07 09:35:40,824 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,828 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medications")
2025-07-07 09:35:40,831 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,834 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medication_usages")
2025-07-07 09:35:40,836 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,839 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("role_applications")
2025-07-07 09:35:40,844 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,847 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessments")
2025-07-07 09:35:40,849 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,851 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_items")
2025-07-07 09:35:40,852 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,854 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_responses")
2025-07-07 09:35:40,859 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,861 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_templates")
2025-07-07 09:35:40,862 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,865 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_template_questions")
2025-07-07 09:35:40,866 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,869 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaires")
2025-07-07 09:35:40,873 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,875 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_items")
2025-07-07 09:35:40,877 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,879 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_responses")
2025-07-07 09:35:40,881 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,883 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_answers")
2025-07-07 09:35:40,885 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,890 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_templates")
2025-07-07 09:35:40,892 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,894 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_template_questions")
2025-07-07 09:35:40,896 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,898 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_results")
2025-07-07 09:35:40,900 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,905 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_results")
2025-07-07 09:35:40,906 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,909 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("report_templates")
2025-07-07 09:35:40,910 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,912 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_distributions")
2025-07-07 09:35:40,914 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,916 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_distributions")
2025-07-07 09:35:40,920 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,923 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("service_stats")
2025-07-07 09:35:40,924 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-07 09:35:40,927 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-07 09:35:40,929 - app.db.init_db - INFO - 数据库表结构检查和更新完成
2025-07-07 09:35:40,934 - app.db.init_db - INFO - 模型关系初始化完成
2025-07-07 09:35:40,935 - app.db.init_db - INFO - 模型关系设置完成
2025-07-07 09:35:40,937 - main - INFO - 数据库初始化完成（强制重建）
2025-07-07 09:35:40,938 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-07 09:35:40,939 - main - INFO - 数据库连接正常
2025-07-07 09:35:40,940 - main - INFO - 开始初始化模板数据
2025-07-07 09:35:40,942 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-07 09:35:41,258 - app.db.init_templates - INFO - 开始初始化评估量表模板...
2025-07-07 09:35:41,291 - app.db.init_templates - INFO - 成功初始化 5 个评估量表模板
2025-07-07 09:35:41,333 - app.db.init_templates - INFO - 开始初始化调查问卷模板...
2025-07-07 09:35:41,375 - app.db.init_templates - INFO - 成功初始化 5 个调查问卷模板
2025-07-07 09:35:41,378 - main - INFO - 模板数据初始化完成
2025-07-07 09:35:41,381 - main - INFO - 数据库表结构已经标准化，不需要迁移
2025-07-07 09:35:41,382 - main - INFO - 应用启动完成
2025-07-07 09:35:55,865 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.2%, CPU使用率 75.9%
2025-07-07 09:36:10,973 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.8%, CPU使用率 89.3%
2025-07-07 09:36:26,080 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.6%, CPU使用率 66.7%
2025-07-07 09:36:30,408 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-07 09:36:30,409 - main - INFO - 请求没有认证头部
2025-07-07 09:36:30,410 - main - INFO - 没有认证头部，设置用户为None
2025-07-07 09:36:30,411 - main - INFO - --- 请求结束: 200 ---

2025-07-07 09:36:32,446 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-07 09:36:32,447 - main - INFO - 请求没有认证头部
2025-07-07 09:36:32,447 - main - INFO - 没有认证头部，设置用户为None
2025-07-07 09:36:32,450 - app.core.db_connection - DEBUG - 当前线程ID: 14944
2025-07-07 09:36:32,450 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-07 09:36:32,452 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-07 09:36:32,453 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-07 09:36:32,454 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-07 09:36:32,455 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-07 09:36:33,281 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-07 09:36:33,286 - main - INFO - --- 请求结束: 200 ---

2025-07-07 09:36:39,118 - health_monitor - DEBUG - 系统指标 - CPU: 28.8%, 内存: 55.1%, 磁盘: 87.2%
2025-07-07 09:36:41,184 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 9.1%
2025-07-07 09:36:56,292 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.7%, CPU使用率 60.7%
2025-07-07 09:37:11,397 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 10.7%
2025-07-07 09:37:26,502 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 37.5%
2025-07-07 09:37:40,168 - health_monitor - DEBUG - 系统指标 - CPU: 15.6%, 内存: 55.1%, 磁盘: 87.2%
2025-07-07 09:37:41,607 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 0.0%
2025-07-07 09:37:56,715 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 4.0%
2025-07-07 09:38:11,820 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.0%, CPU使用率 0.0%
2025-07-07 09:38:26,925 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.1%, CPU使用率 3.8%
2025-07-07 09:38:41,200 - health_monitor - DEBUG - 系统指标 - CPU: 36.7%, 内存: 52.0%, 磁盘: 87.2%
2025-07-07 09:38:42,030 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.0%, CPU使用率 0.0%
2025-07-07 09:38:57,134 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.9%, CPU使用率 23.1%
2025-07-07 09:39:12,239 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.9%, CPU使用率 29.6%
2025-07-07 09:39:27,343 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.9%, CPU使用率 0.0%
2025-07-07 09:39:42,231 - health_monitor - DEBUG - 系统指标 - CPU: 12.3%, 内存: 51.9%, 磁盘: 87.2%
2025-07-07 09:39:42,449 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.2%, CPU使用率 62.5%
2025-07-07 09:39:57,554 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.9%, CPU使用率 18.5%
2025-07-07 09:40:12,668 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.9%, CPU使用率 50.0%
2025-07-07 09:40:27,786 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.0%, CPU使用率 11.1%
2025-07-07 09:40:42,890 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.0%, CPU使用率 12.5%
2025-07-07 09:40:43,258 - health_monitor - DEBUG - 系统指标 - CPU: 9.8%, 内存: 52.0%, 磁盘: 87.2%
2025-07-07 09:40:57,995 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.0%, CPU使用率 8.0%
2025-07-07 09:41:13,105 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.0%, CPU使用率 84.6%
2025-07-07 09:41:28,210 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.0%, CPU使用率 0.0%
2025-07-07 09:41:38,232 - alert_manager - WARNING - 触发告警: disk_free_space, 当前值: 0.0, 阈值: 1024
2025-07-07 09:41:43,314 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.1%, CPU使用率 0.0%
2025-07-07 09:41:44,286 - health_monitor - DEBUG - 系统指标 - CPU: 7.8%, 内存: 52.1%, 磁盘: 87.2%
2025-07-07 09:41:58,419 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.1%, CPU使用率 0.0%
2025-07-07 09:42:13,523 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.1%, CPU使用率 7.1%
2025-07-07 09:42:28,627 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.4%, CPU使用率 45.8%
2025-07-07 09:42:43,733 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.0%, CPU使用率 20.8%
2025-07-07 09:42:45,318 - health_monitor - DEBUG - 系统指标 - CPU: 9.8%, 内存: 52.1%, 磁盘: 87.2%
2025-07-07 09:42:58,838 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.3%, CPU使用率 53.6%
2025-07-07 09:43:13,943 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.1%, CPU使用率 19.2%
2025-07-07 09:43:29,049 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.2%, CPU使用率 33.3%
2025-07-07 09:43:44,153 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.1%, CPU使用率 25.0%
2025-07-07 09:43:46,350 - health_monitor - DEBUG - 系统指标 - CPU: 14.2%, 内存: 52.1%, 磁盘: 87.2%
2025-07-07 09:43:59,270 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.2%, CPU使用率 73.1%
2025-07-07 09:44:14,375 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.0%, CPU使用率 26.1%
2025-07-07 09:44:29,479 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.0%, CPU使用率 12.5%
2025-07-07 09:44:44,584 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.0%, CPU使用率 23.1%
2025-07-07 09:44:47,377 - health_monitor - DEBUG - 系统指标 - CPU: 27.3%, 内存: 52.0%, 磁盘: 87.2%
2025-07-07 09:44:59,689 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.0%, CPU使用率 4.2%
2025-07-07 09:45:14,795 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.2%, CPU使用率 45.8%
2025-07-07 09:45:29,900 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.0%, CPU使用率 15.4%
2025-07-07 09:45:45,003 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.1%, CPU使用率 21.4%
2025-07-07 09:45:48,405 - health_monitor - DEBUG - 系统指标 - CPU: 23.6%, 内存: 52.1%, 磁盘: 87.2%
2025-07-07 09:46:00,107 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.1%, CPU使用率 33.3%
2025-07-07 09:46:15,211 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.4%, CPU使用率 50.0%
2025-07-07 09:46:30,316 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.1%, CPU使用率 17.9%
2025-07-07 09:46:45,420 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.2%, CPU使用率 0.0%
2025-07-07 09:46:49,434 - health_monitor - DEBUG - 系统指标 - CPU: 24.6%, 内存: 52.2%, 磁盘: 87.2%
2025-07-07 09:47:00,524 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.1%, CPU使用率 7.1%
2025-07-07 09:47:15,628 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.1%, CPU使用率 25.9%
2025-07-07 09:47:30,733 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.1%, CPU使用率 25.0%
2025-07-07 09:47:45,856 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.1%, CPU使用率 71.4%
2025-07-07 09:47:50,463 - health_monitor - DEBUG - 系统指标 - CPU: 13.7%, 内存: 52.1%, 磁盘: 87.2%
2025-07-07 09:48:00,961 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.3%, CPU使用率 25.9%
2025-07-07 09:48:16,066 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.1%, CPU使用率 11.1%
2025-07-07 09:48:31,170 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.2%, CPU使用率 16.7%
2025-07-07 09:48:46,276 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.1%, CPU使用率 39.3%
2025-07-07 09:48:51,489 - health_monitor - DEBUG - 系统指标 - CPU: 9.8%, 内存: 52.2%, 磁盘: 87.2%
2025-07-07 09:49:01,381 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.1%, CPU使用率 0.0%
2025-07-07 09:49:16,485 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.1%, CPU使用率 20.8%
2025-07-07 09:49:31,590 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.1%, CPU使用率 29.2%
2025-07-07 09:49:46,694 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.1%, CPU使用率 3.6%
2025-07-07 09:49:52,516 - health_monitor - DEBUG - 系统指标 - CPU: 19.6%, 内存: 52.1%, 磁盘: 87.2%
2025-07-07 09:50:01,799 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.0%, CPU使用率 4.2%
2025-07-07 09:50:16,903 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.3%, CPU使用率 25.0%
2025-07-07 09:50:32,007 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.0%, CPU使用率 14.3%
2025-07-07 09:50:47,112 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.1%, CPU使用率 0.0%
2025-07-07 09:50:53,547 - health_monitor - DEBUG - 系统指标 - CPU: 24.9%, 内存: 51.9%, 磁盘: 87.2%
2025-07-07 09:51:02,216 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.0%, CPU使用率 0.0%
2025-07-07 09:51:17,322 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.1%, CPU使用率 34.5%
2025-07-07 09:51:32,427 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.9%, CPU使用率 21.4%
2025-07-07 09:51:47,532 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.0%, CPU使用率 29.2%
2025-07-07 09:51:54,587 - health_monitor - DEBUG - 系统指标 - CPU: 15.2%, 内存: 52.0%, 磁盘: 87.2%
2025-07-07 09:52:02,636 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.0%, CPU使用率 29.2%
2025-07-07 09:52:17,741 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.0%, CPU使用率 0.0%
2025-07-07 09:52:32,844 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.0%, CPU使用率 0.0%
2025-07-07 09:52:47,949 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.3%, CPU使用率 75.0%
2025-07-07 09:52:55,620 - health_monitor - DEBUG - 系统指标 - CPU: 21.7%, 内存: 52.0%, 磁盘: 87.2%
2025-07-07 09:53:03,054 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.1%, CPU使用率 23.1%
2025-07-07 09:53:18,158 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.0%, CPU使用率 33.3%
2025-07-07 09:53:33,262 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.0%, CPU使用率 8.3%
2025-07-07 09:53:48,371 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.1%, CPU使用率 33.3%
2025-07-07 09:53:56,646 - health_monitor - DEBUG - 系统指标 - CPU: 23.1%, 内存: 52.0%, 磁盘: 87.2%
2025-07-07 09:54:03,476 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.1%, CPU使用率 25.0%
2025-07-07 09:54:18,588 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.1%, CPU使用率 42.9%
2025-07-07 09:54:33,697 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.0%, CPU使用率 0.0%
2025-07-07 09:54:48,801 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.1%, CPU使用率 0.0%
2025-07-07 09:54:57,675 - health_monitor - DEBUG - 系统指标 - CPU: 26.6%, 内存: 52.3%, 磁盘: 87.2%
2025-07-07 09:55:03,906 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.1%, CPU使用率 0.0%
2025-07-07 09:55:19,010 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.8%, CPU使用率 32.0%
2025-07-07 09:55:34,114 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.0%, CPU使用率 17.9%
2025-07-07 09:55:49,219 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.0%, CPU使用率 12.5%
2025-07-07 09:55:58,755 - health_monitor - DEBUG - 系统指标 - CPU: 51.3%, 内存: 51.4%, 磁盘: 87.2%
2025-07-07 09:56:04,323 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.3%, CPU使用率 40.7%
2025-07-07 09:56:19,428 - monitoring - DEBUG - 资源指标更新: 内存使用率 50.9%, CPU使用率 48.1%
2025-07-07 09:56:34,532 - monitoring - DEBUG - 资源指标更新: 内存使用率 50.9%, CPU使用率 25.0%
2025-07-07 09:56:49,637 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.0%, CPU使用率 26.9%
2025-07-07 09:56:59,785 - health_monitor - DEBUG - 系统指标 - CPU: 19.8%, 内存: 51.0%, 磁盘: 87.2%
2025-07-07 09:57:04,803 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.0%, CPU使用率 46.7%
2025-07-07 09:57:19,907 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.0%, CPU使用率 33.3%
2025-07-07 09:57:35,017 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.0%, CPU使用率 70.8%
2025-07-07 09:57:50,121 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.0%, CPU使用率 10.7%
2025-07-07 09:58:00,816 - health_monitor - DEBUG - 系统指标 - CPU: 31.0%, 内存: 51.0%, 磁盘: 87.2%
2025-07-07 09:58:05,225 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.0%, CPU使用率 23.1%
2025-07-07 09:58:20,329 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.1%, CPU使用率 0.0%
2025-07-07 09:58:35,434 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.2%, CPU使用率 25.0%
2025-07-07 09:58:50,540 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.8%, CPU使用率 64.3%
2025-07-07 09:59:01,846 - health_monitor - DEBUG - 系统指标 - CPU: 63.3%, 内存: 53.1%, 磁盘: 87.2%
2025-07-07 09:59:05,645 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.3%, CPU使用率 40.7%
2025-07-07 09:59:20,778 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.8%, CPU使用率 100.0%
2025-07-07 09:59:35,889 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.4%, CPU使用率 50.0%
2025-07-07 09:59:50,995 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.4%, CPU使用率 78.3%
2025-07-07 10:00:03,164 - health_monitor - DEBUG - 系统指标 - CPU: 84.0%, 内存: 55.7%, 磁盘: 87.2%
2025-07-07 10:00:06,113 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.7%, CPU使用率 93.1%
2025-07-07 10:00:19,480 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-07 10:00:19,481 - main - INFO - 请求没有认证头部
2025-07-07 10:00:19,482 - main - INFO - 没有认证头部，设置用户为None
2025-07-07 10:00:19,485 - main - INFO - --- 请求结束: 200 ---

2025-07-07 10:00:21,220 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.6%, CPU使用率 16.7%
2025-07-07 10:00:21,747 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-07 10:00:21,749 - main - INFO - 请求没有认证头部
2025-07-07 10:00:21,751 - main - INFO - 没有认证头部，设置用户为None
2025-07-07 10:00:21,753 - app.core.db_connection - DEBUG - 当前线程ID: 14944
2025-07-07 10:00:21,754 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-07 10:00:21,755 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-07 10:00:21,756 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-07 10:00:21,757 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-07 10:00:21,758 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-07 10:00:23,112 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-07 10:00:23,114 - main - INFO - --- 请求结束: 200 ---

2025-07-07 10:00:27,611 - main - INFO - 
--- 请求开始: GET /api/mobile/pending-assessments ---
2025-07-07 10:00:27,619 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-07-07 10:00:27,620 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-07-07 10:00:27,640 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-07 10:00:27,654 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-07-07 10:00:27,657 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-07-07 10:00:27,664 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-07-07 10:00:27,666 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-07-07 10:00:27,670 - app.core.auth - INFO - 检测到X-User-ID头部: SM_008
2025-07-07 10:00:27,671 - app.core.auth - INFO - 使用X-User-ID认证: SM_008
2025-07-07 10:00:27,674 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-07 10:00:27,679 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-07-07 10:00:27,681 - sqlalchemy.engine.Engine - INFO - [generated in 0.00228s] ('SM_008', 1, 0)
2025-07-07 10:00:27,686 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-07-07 10:00:27,687 - app.core.auth - INFO - 通过custom_id找到用户: markey
2025-07-07 10:00:27,689 - app.api.endpoints.mobile_api_new - INFO - 查询用户 SM_008 的pending量表
2025-07-07 10:00:27,692 - sqlalchemy.engine.Engine - INFO - SELECT assessment_distributions.id AS assessment_distributions_id, assessment_distributions.assessment_id AS assessment_distributions_assessment_id, assessment_distributions.custom_id AS assessment_distributions_custom_id, assessment_distributions.distributor_custom_id AS assessment_distributions_distributor_custom_id, assessment_distributions.status AS assessment_distributions_status, assessment_distributions.due_date AS assessment_distributions_due_date, assessment_distributions.completed_at AS assessment_distributions_completed_at, assessment_distributions.message AS assessment_distributions_message, assessment_distributions.created_at AS assessment_distributions_created_at, assessment_distributions.updated_at AS assessment_distributions_updated_at 
FROM assessment_distributions 
WHERE assessment_distributions.custom_id = ?
2025-07-07 10:00:27,694 - sqlalchemy.engine.Engine - INFO - [generated in 0.00197s] ('SM_008',)
2025-07-07 10:00:27,697 - app.api.endpoints.mobile_api_new - INFO - 用户总共有 8 个量表分发记录
2025-07-07 10:00:27,702 - sqlalchemy.engine.Engine - INFO - SELECT assessment_distributions.id AS assessment_distributions_id, assessment_distributions.assessment_id AS assessment_distributions_assessment_id, assessment_distributions.custom_id AS assessment_distributions_custom_id, assessment_distributions.distributor_custom_id AS assessment_distributions_distributor_custom_id, assessment_distributions.status AS assessment_distributions_status, assessment_distributions.due_date AS assessment_distributions_due_date, assessment_distributions.completed_at AS assessment_distributions_completed_at, assessment_distributions.message AS assessment_distributions_message, assessment_distributions.created_at AS assessment_distributions_created_at, assessment_distributions.updated_at AS assessment_distributions_updated_at 
FROM assessment_distributions 
WHERE assessment_distributions.custom_id = ? AND assessment_distributions.status = ?
2025-07-07 10:00:27,704 - sqlalchemy.engine.Engine - INFO - [generated in 0.00291s] ('SM_008', 'pending')
2025-07-07 10:00:27,706 - app.api.endpoints.mobile_api_new - INFO - 用户有 1 个pending状态的量表分发
2025-07-07 10:00:27,718 - sqlalchemy.engine.Engine - INFO - SELECT assessment_distributions.id AS assessment_distributions_id, assessment_distributions.assessment_id AS assessment_distributions_assessment_id, assessment_distributions.custom_id AS assessment_distributions_custom_id, assessment_distributions.distributor_custom_id AS assessment_distributions_distributor_custom_id, assessment_distributions.status AS assessment_distributions_status, assessment_distributions.due_date AS assessment_distributions_due_date, assessment_distributions.completed_at AS assessment_distributions_completed_at, assessment_distributions.message AS assessment_distributions_message, assessment_distributions.created_at AS assessment_distributions_created_at, assessment_distributions.updated_at AS assessment_distributions_updated_at, assessments.id AS assessments_id, assessments.custom_id AS assessments_custom_id, assessments.template_id AS assessments_template_id, assessments.assessment_type AS assessments_assessment_type, assessments.name AS assessments_name, assessments.version AS assessments_version, assessments.round_number AS assessments_round_number, assessments.sequence_number AS assessments_sequence_number, assessments.unique_identifier AS assessments_unique_identifier, assessments.completed_at AS assessments_completed_at, assessments.assessor AS assessments_assessor, assessments.score AS assessments_score, assessments.max_score AS assessments_max_score, assessments.result AS assessments_result, assessments.conclusion AS assessments_conclusion, assessments.notes AS assessments_notes, assessments.status AS assessments_status, assessments.created_at AS assessments_created_at, assessments.updated_at AS assessments_updated_at, assessments.last_reminded_at AS assessments_last_reminded_at, assessment_templates.id AS assessment_templates_id, assessment_templates.template_key AS assessment_templates_template_key, assessment_templates.assessment_type AS assessment_templates_assessment_type, assessment_templates.sub_type AS assessment_templates_sub_type, assessment_templates.name AS assessment_templates_name, assessment_templates.version AS assessment_templates_version, assessment_templates.description AS assessment_templates_description, assessment_templates.instructions AS assessment_templates_instructions, assessment_templates.scoring_method AS assessment_templates_scoring_method, assessment_templates.max_score AS assessment_templates_max_score, assessment_templates.result_ranges AS assessment_templates_result_ranges, assessment_templates.dimensions AS assessment_templates_dimensions, assessment_templates.is_active AS assessment_templates_is_active, assessment_templates.status AS assessment_templates_status, assessment_templates.created_by AS assessment_templates_created_by, assessment_templates.created_at AS assessment_templates_created_at 
FROM assessment_distributions JOIN assessments ON assessment_distributions.assessment_id = assessments.id LEFT OUTER JOIN assessment_templates ON assessments.template_id = assessment_templates.id 
WHERE assessment_distributions.custom_id = ? AND assessment_distributions.status = ?
2025-07-07 10:00:27,725 - sqlalchemy.engine.Engine - INFO - [generated in 0.00681s] ('SM_008', 'pending')
2025-07-07 10:00:27,729 - app.api.endpoints.mobile_api_new - INFO - 最终返回 1 个pending量表
2025-07-07 10:00:27,731 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-07 10:00:27,735 - main - INFO - --- 请求结束: 200 ---

2025-07-07 10:00:27,762 - main - INFO - 
--- 请求开始: GET /api/mobile/pending-assessments ---
2025-07-07 10:00:27,763 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-07-07 10:00:27,765 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-07-07 10:00:27,766 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-07 10:00:27,768 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-07-07 10:00:27,769 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-07-07 10:00:27,772 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-07-07 10:00:27,772 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-07-07 10:00:27,775 - app.core.auth - INFO - 检测到X-User-ID头部: SM_008
2025-07-07 10:00:27,777 - app.core.auth - INFO - 使用X-User-ID认证: SM_008
2025-07-07 10:00:27,778 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-07 10:00:27,779 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-07-07 10:00:27,782 - sqlalchemy.engine.Engine - INFO - [cached since 0.1039s ago] ('SM_008', 1, 0)
2025-07-07 10:00:27,785 - app.core.auth - INFO - 通过custom_id找到用户: markey
2025-07-07 10:00:27,786 - app.api.endpoints.mobile_api_new - INFO - 查询用户 SM_008 的pending量表
2025-07-07 10:00:27,788 - sqlalchemy.engine.Engine - INFO - SELECT assessment_distributions.id AS assessment_distributions_id, assessment_distributions.assessment_id AS assessment_distributions_assessment_id, assessment_distributions.custom_id AS assessment_distributions_custom_id, assessment_distributions.distributor_custom_id AS assessment_distributions_distributor_custom_id, assessment_distributions.status AS assessment_distributions_status, assessment_distributions.due_date AS assessment_distributions_due_date, assessment_distributions.completed_at AS assessment_distributions_completed_at, assessment_distributions.message AS assessment_distributions_message, assessment_distributions.created_at AS assessment_distributions_created_at, assessment_distributions.updated_at AS assessment_distributions_updated_at 
FROM assessment_distributions 
WHERE assessment_distributions.custom_id = ?
2025-07-07 10:00:27,791 - sqlalchemy.engine.Engine - INFO - [cached since 0.09869s ago] ('SM_008',)
2025-07-07 10:00:27,793 - app.api.endpoints.mobile_api_new - INFO - 用户总共有 8 个量表分发记录
2025-07-07 10:00:27,795 - sqlalchemy.engine.Engine - INFO - SELECT assessment_distributions.id AS assessment_distributions_id, assessment_distributions.assessment_id AS assessment_distributions_assessment_id, assessment_distributions.custom_id AS assessment_distributions_custom_id, assessment_distributions.distributor_custom_id AS assessment_distributions_distributor_custom_id, assessment_distributions.status AS assessment_distributions_status, assessment_distributions.due_date AS assessment_distributions_due_date, assessment_distributions.completed_at AS assessment_distributions_completed_at, assessment_distributions.message AS assessment_distributions_message, assessment_distributions.created_at AS assessment_distributions_created_at, assessment_distributions.updated_at AS assessment_distributions_updated_at 
FROM assessment_distributions 
WHERE assessment_distributions.custom_id = ? AND assessment_distributions.status = ?
2025-07-07 10:00:27,797 - sqlalchemy.engine.Engine - INFO - [cached since 0.09574s ago] ('SM_008', 'pending')
2025-07-07 10:00:27,801 - app.api.endpoints.mobile_api_new - INFO - 用户有 1 个pending状态的量表分发
2025-07-07 10:00:27,803 - sqlalchemy.engine.Engine - INFO - SELECT assessment_distributions.id AS assessment_distributions_id, assessment_distributions.assessment_id AS assessment_distributions_assessment_id, assessment_distributions.custom_id AS assessment_distributions_custom_id, assessment_distributions.distributor_custom_id AS assessment_distributions_distributor_custom_id, assessment_distributions.status AS assessment_distributions_status, assessment_distributions.due_date AS assessment_distributions_due_date, assessment_distributions.completed_at AS assessment_distributions_completed_at, assessment_distributions.message AS assessment_distributions_message, assessment_distributions.created_at AS assessment_distributions_created_at, assessment_distributions.updated_at AS assessment_distributions_updated_at, assessments.id AS assessments_id, assessments.custom_id AS assessments_custom_id, assessments.template_id AS assessments_template_id, assessments.assessment_type AS assessments_assessment_type, assessments.name AS assessments_name, assessments.version AS assessments_version, assessments.round_number AS assessments_round_number, assessments.sequence_number AS assessments_sequence_number, assessments.unique_identifier AS assessments_unique_identifier, assessments.completed_at AS assessments_completed_at, assessments.assessor AS assessments_assessor, assessments.score AS assessments_score, assessments.max_score AS assessments_max_score, assessments.result AS assessments_result, assessments.conclusion AS assessments_conclusion, assessments.notes AS assessments_notes, assessments.status AS assessments_status, assessments.created_at AS assessments_created_at, assessments.updated_at AS assessments_updated_at, assessments.last_reminded_at AS assessments_last_reminded_at, assessment_templates.id AS assessment_templates_id, assessment_templates.template_key AS assessment_templates_template_key, assessment_templates.assessment_type AS assessment_templates_assessment_type, assessment_templates.sub_type AS assessment_templates_sub_type, assessment_templates.name AS assessment_templates_name, assessment_templates.version AS assessment_templates_version, assessment_templates.description AS assessment_templates_description, assessment_templates.instructions AS assessment_templates_instructions, assessment_templates.scoring_method AS assessment_templates_scoring_method, assessment_templates.max_score AS assessment_templates_max_score, assessment_templates.result_ranges AS assessment_templates_result_ranges, assessment_templates.dimensions AS assessment_templates_dimensions, assessment_templates.is_active AS assessment_templates_is_active, assessment_templates.status AS assessment_templates_status, assessment_templates.created_by AS assessment_templates_created_by, assessment_templates.created_at AS assessment_templates_created_at 
FROM assessment_distributions JOIN assessments ON assessment_distributions.assessment_id = assessments.id LEFT OUTER JOIN assessment_templates ON assessments.template_id = assessment_templates.id 
WHERE assessment_distributions.custom_id = ? AND assessment_distributions.status = ?
2025-07-07 10:00:27,807 - sqlalchemy.engine.Engine - INFO - [cached since 0.08852s ago] ('SM_008', 'pending')
2025-07-07 10:00:27,809 - app.api.endpoints.mobile_api_new - INFO - 最终返回 1 个pending量表
2025-07-07 10:00:27,811 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-07 10:00:27,813 - main - INFO - --- 请求结束: 200 ---

2025-07-07 10:00:27,997 - main - INFO - 
--- 请求开始: GET /api/mobile/pending-assessments ---
2025-07-07 10:00:28,036 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-07-07 10:00:28,127 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-07-07 10:00:28,737 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-07 10:00:29,221 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-07-07 10:00:29,251 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-07-07 10:00:29,257 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-07-07 10:00:29,260 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-07-07 10:00:29,269 - app.core.auth - INFO - 检测到X-User-ID头部: SM_008
2025-07-07 10:00:29,272 - app.core.auth - INFO - 使用X-User-ID认证: SM_008
2025-07-07 10:00:29,274 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-07 10:00:29,289 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-07-07 10:00:29,306 - sqlalchemy.engine.Engine - INFO - [cached since 1.627s ago] ('SM_008', 1, 0)
2025-07-07 10:00:29,389 - app.core.auth - INFO - 通过custom_id找到用户: markey
2025-07-07 10:00:29,392 - app.api.endpoints.mobile_api_new - INFO - 查询用户 SM_008 的pending量表
2025-07-07 10:00:29,396 - sqlalchemy.engine.Engine - INFO - SELECT assessment_distributions.id AS assessment_distributions_id, assessment_distributions.assessment_id AS assessment_distributions_assessment_id, assessment_distributions.custom_id AS assessment_distributions_custom_id, assessment_distributions.distributor_custom_id AS assessment_distributions_distributor_custom_id, assessment_distributions.status AS assessment_distributions_status, assessment_distributions.due_date AS assessment_distributions_due_date, assessment_distributions.completed_at AS assessment_distributions_completed_at, assessment_distributions.message AS assessment_distributions_message, assessment_distributions.created_at AS assessment_distributions_created_at, assessment_distributions.updated_at AS assessment_distributions_updated_at 
FROM assessment_distributions 
WHERE assessment_distributions.custom_id = ?
2025-07-07 10:00:29,404 - sqlalchemy.engine.Engine - INFO - [cached since 1.712s ago] ('SM_008',)
2025-07-07 10:00:29,425 - app.api.endpoints.mobile_api_new - INFO - 用户总共有 8 个量表分发记录
2025-07-07 10:00:29,434 - sqlalchemy.engine.Engine - INFO - SELECT assessment_distributions.id AS assessment_distributions_id, assessment_distributions.assessment_id AS assessment_distributions_assessment_id, assessment_distributions.custom_id AS assessment_distributions_custom_id, assessment_distributions.distributor_custom_id AS assessment_distributions_distributor_custom_id, assessment_distributions.status AS assessment_distributions_status, assessment_distributions.due_date AS assessment_distributions_due_date, assessment_distributions.completed_at AS assessment_distributions_completed_at, assessment_distributions.message AS assessment_distributions_message, assessment_distributions.created_at AS assessment_distributions_created_at, assessment_distributions.updated_at AS assessment_distributions_updated_at 
FROM assessment_distributions 
WHERE assessment_distributions.custom_id = ? AND assessment_distributions.status = ?
2025-07-07 10:00:29,443 - sqlalchemy.engine.Engine - INFO - [cached since 1.742s ago] ('SM_008', 'pending')
2025-07-07 10:00:29,502 - app.api.endpoints.mobile_api_new - INFO - 用户有 1 个pending状态的量表分发
2025-07-07 10:00:29,577 - sqlalchemy.engine.Engine - INFO - SELECT assessment_distributions.id AS assessment_distributions_id, assessment_distributions.assessment_id AS assessment_distributions_assessment_id, assessment_distributions.custom_id AS assessment_distributions_custom_id, assessment_distributions.distributor_custom_id AS assessment_distributions_distributor_custom_id, assessment_distributions.status AS assessment_distributions_status, assessment_distributions.due_date AS assessment_distributions_due_date, assessment_distributions.completed_at AS assessment_distributions_completed_at, assessment_distributions.message AS assessment_distributions_message, assessment_distributions.created_at AS assessment_distributions_created_at, assessment_distributions.updated_at AS assessment_distributions_updated_at, assessments.id AS assessments_id, assessments.custom_id AS assessments_custom_id, assessments.template_id AS assessments_template_id, assessments.assessment_type AS assessments_assessment_type, assessments.name AS assessments_name, assessments.version AS assessments_version, assessments.round_number AS assessments_round_number, assessments.sequence_number AS assessments_sequence_number, assessments.unique_identifier AS assessments_unique_identifier, assessments.completed_at AS assessments_completed_at, assessments.assessor AS assessments_assessor, assessments.score AS assessments_score, assessments.max_score AS assessments_max_score, assessments.result AS assessments_result, assessments.conclusion AS assessments_conclusion, assessments.notes AS assessments_notes, assessments.status AS assessments_status, assessments.created_at AS assessments_created_at, assessments.updated_at AS assessments_updated_at, assessments.last_reminded_at AS assessments_last_reminded_at, assessment_templates.id AS assessment_templates_id, assessment_templates.template_key AS assessment_templates_template_key, assessment_templates.assessment_type AS assessment_templates_assessment_type, assessment_templates.sub_type AS assessment_templates_sub_type, assessment_templates.name AS assessment_templates_name, assessment_templates.version AS assessment_templates_version, assessment_templates.description AS assessment_templates_description, assessment_templates.instructions AS assessment_templates_instructions, assessment_templates.scoring_method AS assessment_templates_scoring_method, assessment_templates.max_score AS assessment_templates_max_score, assessment_templates.result_ranges AS assessment_templates_result_ranges, assessment_templates.dimensions AS assessment_templates_dimensions, assessment_templates.is_active AS assessment_templates_is_active, assessment_templates.status AS assessment_templates_status, assessment_templates.created_by AS assessment_templates_created_by, assessment_templates.created_at AS assessment_templates_created_at 
FROM assessment_distributions JOIN assessments ON assessment_distributions.assessment_id = assessments.id LEFT OUTER JOIN assessment_templates ON assessments.template_id = assessment_templates.id 
WHERE assessment_distributions.custom_id = ? AND assessment_distributions.status = ?
2025-07-07 10:00:29,774 - sqlalchemy.engine.Engine - INFO - [cached since 2.055s ago] ('SM_008', 'pending')
2025-07-07 10:00:29,869 - app.api.endpoints.mobile_api_new - INFO - 最终返回 1 个pending量表
2025-07-07 10:00:29,917 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-07 10:00:29,938 - main - INFO - --- 请求结束: 200 ---

2025-07-07 10:00:30,021 - main - INFO - 
--- 请求开始: GET /api/mobile/pending-assessments ---
2025-07-07 10:00:30,045 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-07-07 10:00:30,046 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-07-07 10:00:30,051 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-07 10:00:30,053 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-07-07 10:00:30,054 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-07-07 10:00:30,056 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-07-07 10:00:30,058 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-07-07 10:00:30,060 - app.core.auth - INFO - 检测到X-User-ID头部: SM_008
2025-07-07 10:00:30,067 - app.core.auth - INFO - 使用X-User-ID认证: SM_008
2025-07-07 10:00:30,069 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-07 10:00:30,079 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-07-07 10:00:30,093 - sqlalchemy.engine.Engine - INFO - [cached since 2.415s ago] ('SM_008', 1, 0)
2025-07-07 10:00:30,095 - app.core.auth - INFO - 通过custom_id找到用户: markey
2025-07-07 10:00:30,097 - app.api.endpoints.mobile_api_new - INFO - 查询用户 SM_008 的pending量表
2025-07-07 10:00:30,100 - sqlalchemy.engine.Engine - INFO - SELECT assessment_distributions.id AS assessment_distributions_id, assessment_distributions.assessment_id AS assessment_distributions_assessment_id, assessment_distributions.custom_id AS assessment_distributions_custom_id, assessment_distributions.distributor_custom_id AS assessment_distributions_distributor_custom_id, assessment_distributions.status AS assessment_distributions_status, assessment_distributions.due_date AS assessment_distributions_due_date, assessment_distributions.completed_at AS assessment_distributions_completed_at, assessment_distributions.message AS assessment_distributions_message, assessment_distributions.created_at AS assessment_distributions_created_at, assessment_distributions.updated_at AS assessment_distributions_updated_at 
FROM assessment_distributions 
WHERE assessment_distributions.custom_id = ?
2025-07-07 10:00:30,175 - sqlalchemy.engine.Engine - INFO - [cached since 2.482s ago] ('SM_008',)
2025-07-07 10:00:30,226 - app.api.endpoints.mobile_api_new - INFO - 用户总共有 8 个量表分发记录
2025-07-07 10:00:30,274 - sqlalchemy.engine.Engine - INFO - SELECT assessment_distributions.id AS assessment_distributions_id, assessment_distributions.assessment_id AS assessment_distributions_assessment_id, assessment_distributions.custom_id AS assessment_distributions_custom_id, assessment_distributions.distributor_custom_id AS assessment_distributions_distributor_custom_id, assessment_distributions.status AS assessment_distributions_status, assessment_distributions.due_date AS assessment_distributions_due_date, assessment_distributions.completed_at AS assessment_distributions_completed_at, assessment_distributions.message AS assessment_distributions_message, assessment_distributions.created_at AS assessment_distributions_created_at, assessment_distributions.updated_at AS assessment_distributions_updated_at 
FROM assessment_distributions 
WHERE assessment_distributions.custom_id = ? AND assessment_distributions.status = ?
2025-07-07 10:00:30,341 - sqlalchemy.engine.Engine - INFO - [cached since 2.64s ago] ('SM_008', 'pending')
2025-07-07 10:00:30,351 - app.api.endpoints.mobile_api_new - INFO - 用户有 1 个pending状态的量表分发
2025-07-07 10:00:30,359 - sqlalchemy.engine.Engine - INFO - SELECT assessment_distributions.id AS assessment_distributions_id, assessment_distributions.assessment_id AS assessment_distributions_assessment_id, assessment_distributions.custom_id AS assessment_distributions_custom_id, assessment_distributions.distributor_custom_id AS assessment_distributions_distributor_custom_id, assessment_distributions.status AS assessment_distributions_status, assessment_distributions.due_date AS assessment_distributions_due_date, assessment_distributions.completed_at AS assessment_distributions_completed_at, assessment_distributions.message AS assessment_distributions_message, assessment_distributions.created_at AS assessment_distributions_created_at, assessment_distributions.updated_at AS assessment_distributions_updated_at, assessments.id AS assessments_id, assessments.custom_id AS assessments_custom_id, assessments.template_id AS assessments_template_id, assessments.assessment_type AS assessments_assessment_type, assessments.name AS assessments_name, assessments.version AS assessments_version, assessments.round_number AS assessments_round_number, assessments.sequence_number AS assessments_sequence_number, assessments.unique_identifier AS assessments_unique_identifier, assessments.completed_at AS assessments_completed_at, assessments.assessor AS assessments_assessor, assessments.score AS assessments_score, assessments.max_score AS assessments_max_score, assessments.result AS assessments_result, assessments.conclusion AS assessments_conclusion, assessments.notes AS assessments_notes, assessments.status AS assessments_status, assessments.created_at AS assessments_created_at, assessments.updated_at AS assessments_updated_at, assessments.last_reminded_at AS assessments_last_reminded_at, assessment_templates.id AS assessment_templates_id, assessment_templates.template_key AS assessment_templates_template_key, assessment_templates.assessment_type AS assessment_templates_assessment_type, assessment_templates.sub_type AS assessment_templates_sub_type, assessment_templates.name AS assessment_templates_name, assessment_templates.version AS assessment_templates_version, assessment_templates.description AS assessment_templates_description, assessment_templates.instructions AS assessment_templates_instructions, assessment_templates.scoring_method AS assessment_templates_scoring_method, assessment_templates.max_score AS assessment_templates_max_score, assessment_templates.result_ranges AS assessment_templates_result_ranges, assessment_templates.dimensions AS assessment_templates_dimensions, assessment_templates.is_active AS assessment_templates_is_active, assessment_templates.status AS assessment_templates_status, assessment_templates.created_by AS assessment_templates_created_by, assessment_templates.created_at AS assessment_templates_created_at 
FROM assessment_distributions JOIN assessments ON assessment_distributions.assessment_id = assessments.id LEFT OUTER JOIN assessment_templates ON assessments.template_id = assessment_templates.id 
WHERE assessment_distributions.custom_id = ? AND assessment_distributions.status = ?
2025-07-07 10:00:30,383 - sqlalchemy.engine.Engine - INFO - [cached since 2.665s ago] ('SM_008', 'pending')
2025-07-07 10:00:30,398 - app.api.endpoints.mobile_api_new - INFO - 最终返回 1 个pending量表
2025-07-07 10:00:30,420 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-07 10:00:30,427 - main - INFO - --- 请求结束: 200 ---

2025-07-07 10:00:30,536 - main - INFO - 
--- 请求开始: GET /api/mobile/pending-assessments ---
2025-07-07 10:00:30,539 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-07-07 10:00:30,540 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-07-07 10:00:30,544 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-07 10:00:30,572 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-07-07 10:00:30,657 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-07-07 10:00:30,741 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-07-07 10:00:30,756 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-07-07 10:00:30,762 - app.core.auth - INFO - 检测到X-User-ID头部: SM_008
2025-07-07 10:00:30,764 - app.core.auth - INFO - 使用X-User-ID认证: SM_008
2025-07-07 10:00:30,771 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-07 10:00:30,782 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-07-07 10:00:30,786 - sqlalchemy.engine.Engine - INFO - [cached since 3.107s ago] ('SM_008', 1, 0)
2025-07-07 10:00:30,801 - app.core.auth - INFO - 通过custom_id找到用户: markey
2025-07-07 10:00:30,806 - app.api.endpoints.mobile_api_new - INFO - 查询用户 SM_008 的pending量表
2025-07-07 10:00:30,816 - sqlalchemy.engine.Engine - INFO - SELECT assessment_distributions.id AS assessment_distributions_id, assessment_distributions.assessment_id AS assessment_distributions_assessment_id, assessment_distributions.custom_id AS assessment_distributions_custom_id, assessment_distributions.distributor_custom_id AS assessment_distributions_distributor_custom_id, assessment_distributions.status AS assessment_distributions_status, assessment_distributions.due_date AS assessment_distributions_due_date, assessment_distributions.completed_at AS assessment_distributions_completed_at, assessment_distributions.message AS assessment_distributions_message, assessment_distributions.created_at AS assessment_distributions_created_at, assessment_distributions.updated_at AS assessment_distributions_updated_at 
FROM assessment_distributions 
WHERE assessment_distributions.custom_id = ?
2025-07-07 10:00:30,829 - sqlalchemy.engine.Engine - INFO - [cached since 3.137s ago] ('SM_008',)
2025-07-07 10:00:30,837 - app.api.endpoints.mobile_api_new - INFO - 用户总共有 8 个量表分发记录
2025-07-07 10:00:30,854 - sqlalchemy.engine.Engine - INFO - SELECT assessment_distributions.id AS assessment_distributions_id, assessment_distributions.assessment_id AS assessment_distributions_assessment_id, assessment_distributions.custom_id AS assessment_distributions_custom_id, assessment_distributions.distributor_custom_id AS assessment_distributions_distributor_custom_id, assessment_distributions.status AS assessment_distributions_status, assessment_distributions.due_date AS assessment_distributions_due_date, assessment_distributions.completed_at AS assessment_distributions_completed_at, assessment_distributions.message AS assessment_distributions_message, assessment_distributions.created_at AS assessment_distributions_created_at, assessment_distributions.updated_at AS assessment_distributions_updated_at 
FROM assessment_distributions 
WHERE assessment_distributions.custom_id = ? AND assessment_distributions.status = ?
2025-07-07 10:00:30,863 - sqlalchemy.engine.Engine - INFO - [cached since 3.161s ago] ('SM_008', 'pending')
2025-07-07 10:00:30,869 - app.api.endpoints.mobile_api_new - INFO - 用户有 1 个pending状态的量表分发
2025-07-07 10:00:30,872 - sqlalchemy.engine.Engine - INFO - SELECT assessment_distributions.id AS assessment_distributions_id, assessment_distributions.assessment_id AS assessment_distributions_assessment_id, assessment_distributions.custom_id AS assessment_distributions_custom_id, assessment_distributions.distributor_custom_id AS assessment_distributions_distributor_custom_id, assessment_distributions.status AS assessment_distributions_status, assessment_distributions.due_date AS assessment_distributions_due_date, assessment_distributions.completed_at AS assessment_distributions_completed_at, assessment_distributions.message AS assessment_distributions_message, assessment_distributions.created_at AS assessment_distributions_created_at, assessment_distributions.updated_at AS assessment_distributions_updated_at, assessments.id AS assessments_id, assessments.custom_id AS assessments_custom_id, assessments.template_id AS assessments_template_id, assessments.assessment_type AS assessments_assessment_type, assessments.name AS assessments_name, assessments.version AS assessments_version, assessments.round_number AS assessments_round_number, assessments.sequence_number AS assessments_sequence_number, assessments.unique_identifier AS assessments_unique_identifier, assessments.completed_at AS assessments_completed_at, assessments.assessor AS assessments_assessor, assessments.score AS assessments_score, assessments.max_score AS assessments_max_score, assessments.result AS assessments_result, assessments.conclusion AS assessments_conclusion, assessments.notes AS assessments_notes, assessments.status AS assessments_status, assessments.created_at AS assessments_created_at, assessments.updated_at AS assessments_updated_at, assessments.last_reminded_at AS assessments_last_reminded_at, assessment_templates.id AS assessment_templates_id, assessment_templates.template_key AS assessment_templates_template_key, assessment_templates.assessment_type AS assessment_templates_assessment_type, assessment_templates.sub_type AS assessment_templates_sub_type, assessment_templates.name AS assessment_templates_name, assessment_templates.version AS assessment_templates_version, assessment_templates.description AS assessment_templates_description, assessment_templates.instructions AS assessment_templates_instructions, assessment_templates.scoring_method AS assessment_templates_scoring_method, assessment_templates.max_score AS assessment_templates_max_score, assessment_templates.result_ranges AS assessment_templates_result_ranges, assessment_templates.dimensions AS assessment_templates_dimensions, assessment_templates.is_active AS assessment_templates_is_active, assessment_templates.status AS assessment_templates_status, assessment_templates.created_by AS assessment_templates_created_by, assessment_templates.created_at AS assessment_templates_created_at 
FROM assessment_distributions JOIN assessments ON assessment_distributions.assessment_id = assessments.id LEFT OUTER JOIN assessment_templates ON assessments.template_id = assessment_templates.id 
WHERE assessment_distributions.custom_id = ? AND assessment_distributions.status = ?
2025-07-07 10:00:30,877 - sqlalchemy.engine.Engine - INFO - [cached since 3.159s ago] ('SM_008', 'pending')
2025-07-07 10:00:30,881 - app.api.endpoints.mobile_api_new - INFO - 最终返回 1 个pending量表
2025-07-07 10:00:30,883 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-07 10:00:30,886 - main - INFO - --- 请求结束: 200 ---

2025-07-07 10:00:30,937 - main - INFO - 
--- 请求开始: GET /api/mobile/pending-assessments ---
2025-07-07 10:00:30,939 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-07-07 10:00:30,942 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-07-07 10:00:30,943 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-07 10:00:30,945 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-07-07 10:00:30,945 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-07-07 10:00:30,948 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-07-07 10:00:30,949 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-07-07 10:00:30,951 - app.core.auth - INFO - 检测到X-User-ID头部: SM_008
2025-07-07 10:00:30,952 - app.core.auth - INFO - 使用X-User-ID认证: SM_008
2025-07-07 10:00:30,954 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-07 10:00:30,955 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-07-07 10:00:30,958 - sqlalchemy.engine.Engine - INFO - [cached since 3.279s ago] ('SM_008', 1, 0)
2025-07-07 10:00:30,960 - app.core.auth - INFO - 通过custom_id找到用户: markey
2025-07-07 10:00:30,964 - app.api.endpoints.mobile_api_new - INFO - 查询用户 SM_008 的pending量表
2025-07-07 10:00:30,965 - sqlalchemy.engine.Engine - INFO - SELECT assessment_distributions.id AS assessment_distributions_id, assessment_distributions.assessment_id AS assessment_distributions_assessment_id, assessment_distributions.custom_id AS assessment_distributions_custom_id, assessment_distributions.distributor_custom_id AS assessment_distributions_distributor_custom_id, assessment_distributions.status AS assessment_distributions_status, assessment_distributions.due_date AS assessment_distributions_due_date, assessment_distributions.completed_at AS assessment_distributions_completed_at, assessment_distributions.message AS assessment_distributions_message, assessment_distributions.created_at AS assessment_distributions_created_at, assessment_distributions.updated_at AS assessment_distributions_updated_at 
FROM assessment_distributions 
WHERE assessment_distributions.custom_id = ?
2025-07-07 10:00:30,968 - sqlalchemy.engine.Engine - INFO - [cached since 3.276s ago] ('SM_008',)
2025-07-07 10:00:30,970 - app.api.endpoints.mobile_api_new - INFO - 用户总共有 8 个量表分发记录
2025-07-07 10:00:30,972 - sqlalchemy.engine.Engine - INFO - SELECT assessment_distributions.id AS assessment_distributions_id, assessment_distributions.assessment_id AS assessment_distributions_assessment_id, assessment_distributions.custom_id AS assessment_distributions_custom_id, assessment_distributions.distributor_custom_id AS assessment_distributions_distributor_custom_id, assessment_distributions.status AS assessment_distributions_status, assessment_distributions.due_date AS assessment_distributions_due_date, assessment_distributions.completed_at AS assessment_distributions_completed_at, assessment_distributions.message AS assessment_distributions_message, assessment_distributions.created_at AS assessment_distributions_created_at, assessment_distributions.updated_at AS assessment_distributions_updated_at 
FROM assessment_distributions 
WHERE assessment_distributions.custom_id = ? AND assessment_distributions.status = ?
2025-07-07 10:00:30,977 - sqlalchemy.engine.Engine - INFO - [cached since 3.275s ago] ('SM_008', 'pending')
2025-07-07 10:00:30,979 - app.api.endpoints.mobile_api_new - INFO - 用户有 1 个pending状态的量表分发
2025-07-07 10:00:30,980 - sqlalchemy.engine.Engine - INFO - SELECT assessment_distributions.id AS assessment_distributions_id, assessment_distributions.assessment_id AS assessment_distributions_assessment_id, assessment_distributions.custom_id AS assessment_distributions_custom_id, assessment_distributions.distributor_custom_id AS assessment_distributions_distributor_custom_id, assessment_distributions.status AS assessment_distributions_status, assessment_distributions.due_date AS assessment_distributions_due_date, assessment_distributions.completed_at AS assessment_distributions_completed_at, assessment_distributions.message AS assessment_distributions_message, assessment_distributions.created_at AS assessment_distributions_created_at, assessment_distributions.updated_at AS assessment_distributions_updated_at, assessments.id AS assessments_id, assessments.custom_id AS assessments_custom_id, assessments.template_id AS assessments_template_id, assessments.assessment_type AS assessments_assessment_type, assessments.name AS assessments_name, assessments.version AS assessments_version, assessments.round_number AS assessments_round_number, assessments.sequence_number AS assessments_sequence_number, assessments.unique_identifier AS assessments_unique_identifier, assessments.completed_at AS assessments_completed_at, assessments.assessor AS assessments_assessor, assessments.score AS assessments_score, assessments.max_score AS assessments_max_score, assessments.result AS assessments_result, assessments.conclusion AS assessments_conclusion, assessments.notes AS assessments_notes, assessments.status AS assessments_status, assessments.created_at AS assessments_created_at, assessments.updated_at AS assessments_updated_at, assessments.last_reminded_at AS assessments_last_reminded_at, assessment_templates.id AS assessment_templates_id, assessment_templates.template_key AS assessment_templates_template_key, assessment_templates.assessment_type AS assessment_templates_assessment_type, assessment_templates.sub_type AS assessment_templates_sub_type, assessment_templates.name AS assessment_templates_name, assessment_templates.version AS assessment_templates_version, assessment_templates.description AS assessment_templates_description, assessment_templates.instructions AS assessment_templates_instructions, assessment_templates.scoring_method AS assessment_templates_scoring_method, assessment_templates.max_score AS assessment_templates_max_score, assessment_templates.result_ranges AS assessment_templates_result_ranges, assessment_templates.dimensions AS assessment_templates_dimensions, assessment_templates.is_active AS assessment_templates_is_active, assessment_templates.status AS assessment_templates_status, assessment_templates.created_by AS assessment_templates_created_by, assessment_templates.created_at AS assessment_templates_created_at 
FROM assessment_distributions JOIN assessments ON assessment_distributions.assessment_id = assessments.id LEFT OUTER JOIN assessment_templates ON assessments.template_id = assessment_templates.id 
WHERE assessment_distributions.custom_id = ? AND assessment_distributions.status = ?
2025-07-07 10:00:30,984 - sqlalchemy.engine.Engine - INFO - [cached since 3.266s ago] ('SM_008', 'pending')
2025-07-07 10:00:30,986 - app.api.endpoints.mobile_api_new - INFO - 最终返回 1 个pending量表
2025-07-07 10:00:30,989 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-07 10:00:30,991 - main - INFO - --- 请求结束: 200 ---

2025-07-07 10:00:31,033 - main - INFO - 
--- 请求开始: GET /api/mobile/pending-assessments ---
2025-07-07 10:00:31,045 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-07-07 10:00:31,046 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-07-07 10:00:31,047 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-07 10:00:31,049 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-07-07 10:00:31,054 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-07-07 10:00:31,084 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-07-07 10:00:31,090 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-07-07 10:00:31,093 - app.core.auth - INFO - 检测到X-User-ID头部: SM_008
2025-07-07 10:00:31,129 - app.core.auth - INFO - 使用X-User-ID认证: SM_008
2025-07-07 10:00:31,141 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-07 10:00:31,158 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-07-07 10:00:31,163 - sqlalchemy.engine.Engine - INFO - [cached since 3.484s ago] ('SM_008', 1, 0)
2025-07-07 10:00:31,175 - app.core.auth - INFO - 通过custom_id找到用户: markey
2025-07-07 10:00:31,187 - app.api.endpoints.mobile_api_new - INFO - 查询用户 SM_008 的pending量表
2025-07-07 10:00:31,191 - sqlalchemy.engine.Engine - INFO - SELECT assessment_distributions.id AS assessment_distributions_id, assessment_distributions.assessment_id AS assessment_distributions_assessment_id, assessment_distributions.custom_id AS assessment_distributions_custom_id, assessment_distributions.distributor_custom_id AS assessment_distributions_distributor_custom_id, assessment_distributions.status AS assessment_distributions_status, assessment_distributions.due_date AS assessment_distributions_due_date, assessment_distributions.completed_at AS assessment_distributions_completed_at, assessment_distributions.message AS assessment_distributions_message, assessment_distributions.created_at AS assessment_distributions_created_at, assessment_distributions.updated_at AS assessment_distributions_updated_at 
FROM assessment_distributions 
WHERE assessment_distributions.custom_id = ?
2025-07-07 10:00:31,205 - sqlalchemy.engine.Engine - INFO - [cached since 3.513s ago] ('SM_008',)
2025-07-07 10:00:31,216 - app.api.endpoints.mobile_api_new - INFO - 用户总共有 8 个量表分发记录
2025-07-07 10:00:31,218 - sqlalchemy.engine.Engine - INFO - SELECT assessment_distributions.id AS assessment_distributions_id, assessment_distributions.assessment_id AS assessment_distributions_assessment_id, assessment_distributions.custom_id AS assessment_distributions_custom_id, assessment_distributions.distributor_custom_id AS assessment_distributions_distributor_custom_id, assessment_distributions.status AS assessment_distributions_status, assessment_distributions.due_date AS assessment_distributions_due_date, assessment_distributions.completed_at AS assessment_distributions_completed_at, assessment_distributions.message AS assessment_distributions_message, assessment_distributions.created_at AS assessment_distributions_created_at, assessment_distributions.updated_at AS assessment_distributions_updated_at 
FROM assessment_distributions 
WHERE assessment_distributions.custom_id = ? AND assessment_distributions.status = ?
2025-07-07 10:00:31,225 - sqlalchemy.engine.Engine - INFO - [cached since 3.523s ago] ('SM_008', 'pending')
2025-07-07 10:00:31,228 - app.api.endpoints.mobile_api_new - INFO - 用户有 1 个pending状态的量表分发
2025-07-07 10:00:31,230 - sqlalchemy.engine.Engine - INFO - SELECT assessment_distributions.id AS assessment_distributions_id, assessment_distributions.assessment_id AS assessment_distributions_assessment_id, assessment_distributions.custom_id AS assessment_distributions_custom_id, assessment_distributions.distributor_custom_id AS assessment_distributions_distributor_custom_id, assessment_distributions.status AS assessment_distributions_status, assessment_distributions.due_date AS assessment_distributions_due_date, assessment_distributions.completed_at AS assessment_distributions_completed_at, assessment_distributions.message AS assessment_distributions_message, assessment_distributions.created_at AS assessment_distributions_created_at, assessment_distributions.updated_at AS assessment_distributions_updated_at, assessments.id AS assessments_id, assessments.custom_id AS assessments_custom_id, assessments.template_id AS assessments_template_id, assessments.assessment_type AS assessments_assessment_type, assessments.name AS assessments_name, assessments.version AS assessments_version, assessments.round_number AS assessments_round_number, assessments.sequence_number AS assessments_sequence_number, assessments.unique_identifier AS assessments_unique_identifier, assessments.completed_at AS assessments_completed_at, assessments.assessor AS assessments_assessor, assessments.score AS assessments_score, assessments.max_score AS assessments_max_score, assessments.result AS assessments_result, assessments.conclusion AS assessments_conclusion, assessments.notes AS assessments_notes, assessments.status AS assessments_status, assessments.created_at AS assessments_created_at, assessments.updated_at AS assessments_updated_at, assessments.last_reminded_at AS assessments_last_reminded_at, assessment_templates.id AS assessment_templates_id, assessment_templates.template_key AS assessment_templates_template_key, assessment_templates.assessment_type AS assessment_templates_assessment_type, assessment_templates.sub_type AS assessment_templates_sub_type, assessment_templates.name AS assessment_templates_name, assessment_templates.version AS assessment_templates_version, assessment_templates.description AS assessment_templates_description, assessment_templates.instructions AS assessment_templates_instructions, assessment_templates.scoring_method AS assessment_templates_scoring_method, assessment_templates.max_score AS assessment_templates_max_score, assessment_templates.result_ranges AS assessment_templates_result_ranges, assessment_templates.dimensions AS assessment_templates_dimensions, assessment_templates.is_active AS assessment_templates_is_active, assessment_templates.status AS assessment_templates_status, assessment_templates.created_by AS assessment_templates_created_by, assessment_templates.created_at AS assessment_templates_created_at 
FROM assessment_distributions JOIN assessments ON assessment_distributions.assessment_id = assessments.id LEFT OUTER JOIN assessment_templates ON assessments.template_id = assessment_templates.id 
WHERE assessment_distributions.custom_id = ? AND assessment_distributions.status = ?
2025-07-07 10:00:31,234 - sqlalchemy.engine.Engine - INFO - [cached since 3.516s ago] ('SM_008', 'pending')
2025-07-07 10:00:31,237 - app.api.endpoints.mobile_api_new - INFO - 最终返回 1 个pending量表
2025-07-07 10:00:31,238 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-07 10:00:31,241 - main - INFO - --- 请求结束: 200 ---

2025-07-07 10:00:31,353 - main - INFO - 
--- 请求开始: GET /api/mobile/pending-assessments ---
2025-07-07 10:00:31,357 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-07-07 10:00:31,359 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-07-07 10:00:31,360 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-07 10:00:31,361 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-07-07 10:00:31,362 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-07-07 10:00:31,364 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-07-07 10:00:31,365 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-07-07 10:00:31,367 - app.core.auth - INFO - 检测到X-User-ID头部: SM_008
2025-07-07 10:00:31,368 - app.core.auth - INFO - 使用X-User-ID认证: SM_008
2025-07-07 10:00:31,369 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-07 10:00:31,370 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-07-07 10:00:31,372 - sqlalchemy.engine.Engine - INFO - [cached since 3.694s ago] ('SM_008', 1, 0)
2025-07-07 10:00:31,374 - app.core.auth - INFO - 通过custom_id找到用户: markey
2025-07-07 10:00:31,376 - app.api.endpoints.mobile_api_new - INFO - 查询用户 SM_008 的pending量表
2025-07-07 10:00:31,379 - sqlalchemy.engine.Engine - INFO - SELECT assessment_distributions.id AS assessment_distributions_id, assessment_distributions.assessment_id AS assessment_distributions_assessment_id, assessment_distributions.custom_id AS assessment_distributions_custom_id, assessment_distributions.distributor_custom_id AS assessment_distributions_distributor_custom_id, assessment_distributions.status AS assessment_distributions_status, assessment_distributions.due_date AS assessment_distributions_due_date, assessment_distributions.completed_at AS assessment_distributions_completed_at, assessment_distributions.message AS assessment_distributions_message, assessment_distributions.created_at AS assessment_distributions_created_at, assessment_distributions.updated_at AS assessment_distributions_updated_at 
FROM assessment_distributions 
WHERE assessment_distributions.custom_id = ?
2025-07-07 10:00:31,381 - sqlalchemy.engine.Engine - INFO - [cached since 3.689s ago] ('SM_008',)
2025-07-07 10:00:31,384 - app.api.endpoints.mobile_api_new - INFO - 用户总共有 8 个量表分发记录
2025-07-07 10:00:31,386 - sqlalchemy.engine.Engine - INFO - SELECT assessment_distributions.id AS assessment_distributions_id, assessment_distributions.assessment_id AS assessment_distributions_assessment_id, assessment_distributions.custom_id AS assessment_distributions_custom_id, assessment_distributions.distributor_custom_id AS assessment_distributions_distributor_custom_id, assessment_distributions.status AS assessment_distributions_status, assessment_distributions.due_date AS assessment_distributions_due_date, assessment_distributions.completed_at AS assessment_distributions_completed_at, assessment_distributions.message AS assessment_distributions_message, assessment_distributions.created_at AS assessment_distributions_created_at, assessment_distributions.updated_at AS assessment_distributions_updated_at 
FROM assessment_distributions 
WHERE assessment_distributions.custom_id = ? AND assessment_distributions.status = ?
2025-07-07 10:00:31,388 - sqlalchemy.engine.Engine - INFO - [cached since 3.686s ago] ('SM_008', 'pending')
2025-07-07 10:00:31,389 - app.api.endpoints.mobile_api_new - INFO - 用户有 1 个pending状态的量表分发
2025-07-07 10:00:31,391 - sqlalchemy.engine.Engine - INFO - SELECT assessment_distributions.id AS assessment_distributions_id, assessment_distributions.assessment_id AS assessment_distributions_assessment_id, assessment_distributions.custom_id AS assessment_distributions_custom_id, assessment_distributions.distributor_custom_id AS assessment_distributions_distributor_custom_id, assessment_distributions.status AS assessment_distributions_status, assessment_distributions.due_date AS assessment_distributions_due_date, assessment_distributions.completed_at AS assessment_distributions_completed_at, assessment_distributions.message AS assessment_distributions_message, assessment_distributions.created_at AS assessment_distributions_created_at, assessment_distributions.updated_at AS assessment_distributions_updated_at, assessments.id AS assessments_id, assessments.custom_id AS assessments_custom_id, assessments.template_id AS assessments_template_id, assessments.assessment_type AS assessments_assessment_type, assessments.name AS assessments_name, assessments.version AS assessments_version, assessments.round_number AS assessments_round_number, assessments.sequence_number AS assessments_sequence_number, assessments.unique_identifier AS assessments_unique_identifier, assessments.completed_at AS assessments_completed_at, assessments.assessor AS assessments_assessor, assessments.score AS assessments_score, assessments.max_score AS assessments_max_score, assessments.result AS assessments_result, assessments.conclusion AS assessments_conclusion, assessments.notes AS assessments_notes, assessments.status AS assessments_status, assessments.created_at AS assessments_created_at, assessments.updated_at AS assessments_updated_at, assessments.last_reminded_at AS assessments_last_reminded_at, assessment_templates.id AS assessment_templates_id, assessment_templates.template_key AS assessment_templates_template_key, assessment_templates.assessment_type AS assessment_templates_assessment_type, assessment_templates.sub_type AS assessment_templates_sub_type, assessment_templates.name AS assessment_templates_name, assessment_templates.version AS assessment_templates_version, assessment_templates.description AS assessment_templates_description, assessment_templates.instructions AS assessment_templates_instructions, assessment_templates.scoring_method AS assessment_templates_scoring_method, assessment_templates.max_score AS assessment_templates_max_score, assessment_templates.result_ranges AS assessment_templates_result_ranges, assessment_templates.dimensions AS assessment_templates_dimensions, assessment_templates.is_active AS assessment_templates_is_active, assessment_templates.status AS assessment_templates_status, assessment_templates.created_by AS assessment_templates_created_by, assessment_templates.created_at AS assessment_templates_created_at 
FROM assessment_distributions JOIN assessments ON assessment_distributions.assessment_id = assessments.id LEFT OUTER JOIN assessment_templates ON assessments.template_id = assessment_templates.id 
WHERE assessment_distributions.custom_id = ? AND assessment_distributions.status = ?
2025-07-07 10:00:31,396 - sqlalchemy.engine.Engine - INFO - [cached since 3.678s ago] ('SM_008', 'pending')
2025-07-07 10:00:31,399 - app.api.endpoints.mobile_api_new - INFO - 最终返回 1 个pending量表
2025-07-07 10:00:31,401 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-07 10:00:31,403 - main - INFO - --- 请求结束: 200 ---

2025-07-07 10:00:31,431 - main - INFO - 
--- 请求开始: GET /api/mobile/pending-assessments ---
2025-07-07 10:00:31,436 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-07-07 10:00:31,437 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-07-07 10:00:31,438 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-07 10:00:31,439 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-07-07 10:00:31,440 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-07-07 10:00:31,442 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-07-07 10:00:31,443 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-07-07 10:00:31,445 - app.core.auth - INFO - 检测到X-User-ID头部: SM_008
2025-07-07 10:00:31,446 - app.core.auth - INFO - 使用X-User-ID认证: SM_008
2025-07-07 10:00:31,448 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-07 10:00:31,449 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-07-07 10:00:31,451 - sqlalchemy.engine.Engine - INFO - [cached since 3.773s ago] ('SM_008', 1, 0)
2025-07-07 10:00:31,453 - app.core.auth - INFO - 通过custom_id找到用户: markey
2025-07-07 10:00:31,455 - app.api.endpoints.mobile_api_new - INFO - 查询用户 SM_008 的pending量表
2025-07-07 10:00:31,457 - sqlalchemy.engine.Engine - INFO - SELECT assessment_distributions.id AS assessment_distributions_id, assessment_distributions.assessment_id AS assessment_distributions_assessment_id, assessment_distributions.custom_id AS assessment_distributions_custom_id, assessment_distributions.distributor_custom_id AS assessment_distributions_distributor_custom_id, assessment_distributions.status AS assessment_distributions_status, assessment_distributions.due_date AS assessment_distributions_due_date, assessment_distributions.completed_at AS assessment_distributions_completed_at, assessment_distributions.message AS assessment_distributions_message, assessment_distributions.created_at AS assessment_distributions_created_at, assessment_distributions.updated_at AS assessment_distributions_updated_at 
FROM assessment_distributions 
WHERE assessment_distributions.custom_id = ?
2025-07-07 10:00:31,463 - sqlalchemy.engine.Engine - INFO - [cached since 3.771s ago] ('SM_008',)
2025-07-07 10:00:31,469 - app.api.endpoints.mobile_api_new - INFO - 用户总共有 8 个量表分发记录
2025-07-07 10:00:31,472 - sqlalchemy.engine.Engine - INFO - SELECT assessment_distributions.id AS assessment_distributions_id, assessment_distributions.assessment_id AS assessment_distributions_assessment_id, assessment_distributions.custom_id AS assessment_distributions_custom_id, assessment_distributions.distributor_custom_id AS assessment_distributions_distributor_custom_id, assessment_distributions.status AS assessment_distributions_status, assessment_distributions.due_date AS assessment_distributions_due_date, assessment_distributions.completed_at AS assessment_distributions_completed_at, assessment_distributions.message AS assessment_distributions_message, assessment_distributions.created_at AS assessment_distributions_created_at, assessment_distributions.updated_at AS assessment_distributions_updated_at 
FROM assessment_distributions 
WHERE assessment_distributions.custom_id = ? AND assessment_distributions.status = ?
2025-07-07 10:00:31,474 - sqlalchemy.engine.Engine - INFO - [cached since 3.772s ago] ('SM_008', 'pending')
2025-07-07 10:00:31,475 - app.api.endpoints.mobile_api_new - INFO - 用户有 1 个pending状态的量表分发
2025-07-07 10:00:31,478 - sqlalchemy.engine.Engine - INFO - SELECT assessment_distributions.id AS assessment_distributions_id, assessment_distributions.assessment_id AS assessment_distributions_assessment_id, assessment_distributions.custom_id AS assessment_distributions_custom_id, assessment_distributions.distributor_custom_id AS assessment_distributions_distributor_custom_id, assessment_distributions.status AS assessment_distributions_status, assessment_distributions.due_date AS assessment_distributions_due_date, assessment_distributions.completed_at AS assessment_distributions_completed_at, assessment_distributions.message AS assessment_distributions_message, assessment_distributions.created_at AS assessment_distributions_created_at, assessment_distributions.updated_at AS assessment_distributions_updated_at, assessments.id AS assessments_id, assessments.custom_id AS assessments_custom_id, assessments.template_id AS assessments_template_id, assessments.assessment_type AS assessments_assessment_type, assessments.name AS assessments_name, assessments.version AS assessments_version, assessments.round_number AS assessments_round_number, assessments.sequence_number AS assessments_sequence_number, assessments.unique_identifier AS assessments_unique_identifier, assessments.completed_at AS assessments_completed_at, assessments.assessor AS assessments_assessor, assessments.score AS assessments_score, assessments.max_score AS assessments_max_score, assessments.result AS assessments_result, assessments.conclusion AS assessments_conclusion, assessments.notes AS assessments_notes, assessments.status AS assessments_status, assessments.created_at AS assessments_created_at, assessments.updated_at AS assessments_updated_at, assessments.last_reminded_at AS assessments_last_reminded_at, assessment_templates.id AS assessment_templates_id, assessment_templates.template_key AS assessment_templates_template_key, assessment_templates.assessment_type AS assessment_templates_assessment_type, assessment_templates.sub_type AS assessment_templates_sub_type, assessment_templates.name AS assessment_templates_name, assessment_templates.version AS assessment_templates_version, assessment_templates.description AS assessment_templates_description, assessment_templates.instructions AS assessment_templates_instructions, assessment_templates.scoring_method AS assessment_templates_scoring_method, assessment_templates.max_score AS assessment_templates_max_score, assessment_templates.result_ranges AS assessment_templates_result_ranges, assessment_templates.dimensions AS assessment_templates_dimensions, assessment_templates.is_active AS assessment_templates_is_active, assessment_templates.status AS assessment_templates_status, assessment_templates.created_by AS assessment_templates_created_by, assessment_templates.created_at AS assessment_templates_created_at 
FROM assessment_distributions JOIN assessments ON assessment_distributions.assessment_id = assessments.id LEFT OUTER JOIN assessment_templates ON assessments.template_id = assessment_templates.id 
WHERE assessment_distributions.custom_id = ? AND assessment_distributions.status = ?
2025-07-07 10:00:31,484 - sqlalchemy.engine.Engine - INFO - [cached since 3.766s ago] ('SM_008', 'pending')
2025-07-07 10:00:31,489 - app.api.endpoints.mobile_api_new - INFO - 最终返回 1 个pending量表
2025-07-07 10:00:31,491 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-07 10:00:31,493 - main - INFO - --- 请求结束: 200 ---

2025-07-07 10:00:33,157 - main - INFO - 
--- 请求开始: GET /api/mobile/pending-questionnaires ---
2025-07-07 10:00:33,158 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-07-07 10:00:33,159 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-07-07 10:00:33,160 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-07 10:00:33,161 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-07-07 10:00:33,162 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-07-07 10:00:33,165 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-07-07 10:00:33,167 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-07-07 10:00:33,169 - app.core.auth - INFO - 检测到X-User-ID头部: SM_008
2025-07-07 10:00:33,170 - app.core.auth - INFO - 使用X-User-ID认证: SM_008
2025-07-07 10:00:33,171 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-07 10:00:33,173 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-07-07 10:00:33,175 - sqlalchemy.engine.Engine - INFO - [cached since 5.496s ago] ('SM_008', 1, 0)
2025-07-07 10:00:33,177 - app.core.auth - INFO - 通过custom_id找到用户: markey
2025-07-07 10:00:33,186 - sqlalchemy.engine.Engine - INFO - SELECT questionnaire_distributions.id AS questionnaire_distributions_id, questionnaire_distributions.questionnaire_id AS questionnaire_distributions_questionnaire_id, questionnaire_distributions.custom_id AS questionnaire_distributions_custom_id, questionnaire_distributions.distributor_custom_id AS questionnaire_distributions_distributor_custom_id, questionnaire_distributions.status AS questionnaire_distributions_status, questionnaire_distributions.due_date AS questionnaire_distributions_due_date, questionnaire_distributions.completed_at AS questionnaire_distributions_completed_at, questionnaire_distributions.message AS questionnaire_distributions_message, questionnaire_distributions.created_at AS questionnaire_distributions_created_at, questionnaire_distributions.updated_at AS questionnaire_distributions_updated_at, questionnaires.id AS questionnaires_id, questionnaires.title AS questionnaires_title, questionnaires.description AS questionnaires_description, questionnaires.max_score AS questionnaires_max_score, questionnaires.template_id AS questionnaires_template_id, questionnaires.custom_id AS questionnaires_custom_id, questionnaires.questionnaire_type AS questionnaires_questionnaire_type, questionnaires.notes AS questionnaires_notes, questionnaires.status AS questionnaires_status, questionnaires.version AS questionnaires_version, questionnaires.created_at AS questionnaires_created_at, questionnaires.updated_at AS questionnaires_updated_at, questionnaires.created_by AS questionnaires_created_by 
FROM questionnaire_distributions JOIN questionnaires ON questionnaire_distributions.questionnaire_id = questionnaires.id 
WHERE questionnaire_distributions.custom_id = ? AND questionnaire_distributions.status = ?
2025-07-07 10:00:33,189 - sqlalchemy.engine.Engine - INFO - [generated in 0.00306s] ('SM_008', 'pending')
2025-07-07 10:00:33,193 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-07 10:00:33,194 - main - INFO - --- 请求结束: 200 ---

2025-07-07 10:00:34,609 - main - INFO - 
--- 请求开始: GET /api/assessment-results/user/SM_008 ---
2025-07-07 10:00:34,624 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-07-07 10:00:34,626 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-07-07 10:00:34,628 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-07 10:00:34,634 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-07-07 10:00:34,635 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-07-07 10:00:34,643 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-07-07 10:00:34,674 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-07-07 10:00:34,695 - app.core.db_connection - DEBUG - 当前线程ID: 14944
2025-07-07 10:00:34,704 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-07 10:00:34,706 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-07 10:00:34,720 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-07 10:00:34,721 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-07 10:00:34,724 - auth - INFO - 数据库连接有效
2025-07-07 10:00:34,725 - auth - INFO - 检测到X-User-ID头部: SM_008
2025-07-07 10:00:34,726 - auth - INFO - 检测到Authorization头部: Bearer eyJhbGciOiJIU...
2025-07-07 10:00:34,727 - auth - INFO - 请求路径: /api/assessment-results/user/SM_008
2025-07-07 10:00:34,728 - auth - INFO - 请求方法: GET
2025-07-07 10:00:34,729 - auth - INFO - 客户端IP: 127.0.0.1
2025-07-07 10:00:34,729 - auth - INFO - 请求来源: python-requests/2.32.3
2025-07-07 10:00:34,731 - auth - INFO - 客户端类型: 其他
2025-07-07 10:00:34,733 - auth - INFO - 尝试使用JWT令牌认证
2025-07-07 10:00:34,740 - auth - INFO - JWT认证成功: markey, ID: 2, 角色: personal
2025-07-07 10:00:34,741 - auth - INFO - 尝试使用X-User-ID认证: SM_008
2025-07-07 10:00:34,745 - auth - INFO - X-User-ID认证成功: markey, ID: 2, 角色: personal
2025-07-07 10:00:34,746 - auth - INFO - JWT认证成功，返回用户: markey
2025-07-07 10:00:34,749 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-07 10:00:34,751 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-07-07 10:00:34,753 - sqlalchemy.engine.Engine - INFO - [cached since 7.075s ago] ('SM_008', 1, 0)
2025-07-07 10:00:34,761 - sqlalchemy.engine.Engine - INFO - SELECT assessment_results.id AS assessment_results_id, assessment_results.assessment_id AS assessment_results_assessment_id, assessment_results.custom_id AS assessment_results_custom_id, assessment_results.template_id AS assessment_results_template_id, assessment_results.total_score AS assessment_results_total_score, assessment_results.max_score AS assessment_results_max_score, assessment_results.percentage AS assessment_results_percentage, assessment_results.result_level AS assessment_results_result_level, assessment_results.result_category AS assessment_results_result_category, assessment_results.interpretation AS assessment_results_interpretation, assessment_results.recommendations AS assessment_results_recommendations, assessment_results.dimension_scores AS assessment_results_dimension_scores, assessment_results.calculation_details AS assessment_results_calculation_details, assessment_results.raw_answers AS assessment_results_raw_answers, assessment_results.report_generated AS assessment_results_report_generated, assessment_results.report_content AS assessment_results_report_content, assessment_results.report_format AS assessment_results_report_format, assessment_results.report_template AS assessment_results_report_template, assessment_results.status AS assessment_results_status, assessment_results.calculated_at AS assessment_results_calculated_at, assessment_results.created_at AS assessment_results_created_at, assessment_results.updated_at AS assessment_results_updated_at, assessments.name AS assessment_name, assessment_templates.name AS template_name 
FROM assessment_results JOIN assessments ON assessment_results.assessment_id = assessments.id LEFT OUTER JOIN assessment_templates ON assessments.template_id = assessment_templates.id 
WHERE assessment_results.custom_id = ?
 LIMIT ? OFFSET ?
2025-07-07 10:00:34,766 - sqlalchemy.engine.Engine - INFO - [generated in 0.00471s] ('SM_008', 100, 0)
2025-07-07 10:00:34,780 - sqlalchemy.engine.Engine - INFO - SELECT count(*) AS count_1 
FROM (SELECT assessment_results.id AS assessment_results_id, assessment_results.assessment_id AS assessment_results_assessment_id, assessment_results.custom_id AS assessment_results_custom_id, assessment_results.template_id AS assessment_results_template_id, assessment_results.total_score AS assessment_results_total_score, assessment_results.max_score AS assessment_results_max_score, assessment_results.percentage AS assessment_results_percentage, assessment_results.result_level AS assessment_results_result_level, assessment_results.result_category AS assessment_results_result_category, assessment_results.interpretation AS assessment_results_interpretation, assessment_results.recommendations AS assessment_results_recommendations, assessment_results.dimension_scores AS assessment_results_dimension_scores, assessment_results.calculation_details AS assessment_results_calculation_details, assessment_results.raw_answers AS assessment_results_raw_answers, assessment_results.report_generated AS assessment_results_report_generated, assessment_results.report_content AS assessment_results_report_content, assessment_results.report_format AS assessment_results_report_format, assessment_results.report_template AS assessment_results_report_template, assessment_results.status AS assessment_results_status, assessment_results.calculated_at AS assessment_results_calculated_at, assessment_results.created_at AS assessment_results_created_at, assessment_results.updated_at AS assessment_results_updated_at, assessments.name AS assessment_name, assessment_templates.name AS template_name 
FROM assessment_results JOIN assessments ON assessment_results.assessment_id = assessments.id LEFT OUTER JOIN assessment_templates ON assessments.template_id = assessment_templates.id 
WHERE assessment_results.custom_id = ?) AS anon_1
2025-07-07 10:00:34,783 - sqlalchemy.engine.Engine - INFO - [generated in 0.00348s] ('SM_008',)
2025-07-07 10:00:34,788 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-07-07 10:00:34,800 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-07 10:00:34,805 - main - INFO - --- 请求结束: 200 ---

2025-07-07 10:00:34,820 - main - INFO - 
--- 请求开始: GET /api/questionnaire-results/user/SM_008 ---
2025-07-07 10:00:34,821 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-07-07 10:00:34,822 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-07-07 10:00:34,823 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-07 10:00:34,824 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-07-07 10:00:34,825 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-07-07 10:00:34,827 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-07-07 10:00:34,828 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-07-07 10:00:34,832 - app.core.db_connection - DEBUG - 当前线程ID: 14944
2025-07-07 10:00:34,833 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-07 10:00:34,834 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-07 10:00:34,835 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-07 10:00:34,836 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-07 10:00:34,837 - auth - INFO - 数据库连接有效
2025-07-07 10:00:34,838 - auth - INFO - 检测到X-User-ID头部: SM_008
2025-07-07 10:00:34,839 - auth - INFO - 检测到Authorization头部: Bearer eyJhbGciOiJIU...
2025-07-07 10:00:34,840 - auth - INFO - 请求路径: /api/questionnaire-results/user/SM_008
2025-07-07 10:00:34,840 - auth - INFO - 请求方法: GET
2025-07-07 10:00:34,841 - auth - INFO - 客户端IP: 127.0.0.1
2025-07-07 10:00:34,842 - auth - INFO - 请求来源: python-requests/2.32.3
2025-07-07 10:00:34,842 - auth - INFO - 客户端类型: 其他
2025-07-07 10:00:34,843 - auth - INFO - 尝试使用JWT令牌认证
2025-07-07 10:00:34,848 - auth - INFO - JWT认证成功: markey, ID: 2, 角色: personal
2025-07-07 10:00:34,849 - auth - INFO - 尝试使用X-User-ID认证: SM_008
2025-07-07 10:00:34,852 - auth - INFO - X-User-ID认证成功: markey, ID: 2, 角色: personal
2025-07-07 10:00:34,853 - auth - INFO - JWT认证成功，返回用户: markey
2025-07-07 10:00:34,855 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-07 10:00:34,857 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-07-07 10:00:34,859 - sqlalchemy.engine.Engine - INFO - [cached since 7.181s ago] ('SM_008', 1, 0)
2025-07-07 10:00:34,871 - sqlalchemy.engine.Engine - INFO - SELECT questionnaire_results.id AS questionnaire_results_id, questionnaire_results.questionnaire_id AS questionnaire_results_questionnaire_id, questionnaire_results.response_id AS questionnaire_results_response_id, questionnaire_results.custom_id AS questionnaire_results_custom_id, questionnaire_results.template_id AS questionnaire_results_template_id, questionnaire_results.total_score AS questionnaire_results_total_score, questionnaire_results.max_score AS questionnaire_results_max_score, questionnaire_results.percentage AS questionnaire_results_percentage, questionnaire_results.result_level AS questionnaire_results_result_level, questionnaire_results.result_category AS questionnaire_results_result_category, questionnaire_results.interpretation AS questionnaire_results_interpretation, questionnaire_results.recommendations AS questionnaire_results_recommendations, questionnaire_results.dimension_scores AS questionnaire_results_dimension_scores, questionnaire_results.calculation_details AS questionnaire_results_calculation_details, questionnaire_results.raw_answers AS questionnaire_results_raw_answers, questionnaire_results.report_generated AS questionnaire_results_report_generated, questionnaire_results.report_content AS questionnaire_results_report_content, questionnaire_results.report_format AS questionnaire_results_report_format, questionnaire_results.report_template AS questionnaire_results_report_template, questionnaire_results.status AS questionnaire_results_status, questionnaire_results.calculated_at AS questionnaire_results_calculated_at, questionnaire_results.created_at AS questionnaire_results_created_at, questionnaire_results.updated_at AS questionnaire_results_updated_at, questionnaires.title AS questionnaire_name 
FROM questionnaire_results JOIN questionnaires ON questionnaire_results.questionnaire_id = questionnaires.id 
WHERE questionnaire_results.custom_id = ?
 LIMIT ? OFFSET ?
2025-07-07 10:00:34,874 - sqlalchemy.engine.Engine - INFO - [generated in 0.00289s] ('SM_008', 100, 0)
2025-07-07 10:00:34,887 - sqlalchemy.engine.Engine - INFO - SELECT count(*) AS count_1 
FROM (SELECT questionnaire_results.id AS questionnaire_results_id, questionnaire_results.questionnaire_id AS questionnaire_results_questionnaire_id, questionnaire_results.response_id AS questionnaire_results_response_id, questionnaire_results.custom_id AS questionnaire_results_custom_id, questionnaire_results.template_id AS questionnaire_results_template_id, questionnaire_results.total_score AS questionnaire_results_total_score, questionnaire_results.max_score AS questionnaire_results_max_score, questionnaire_results.percentage AS questionnaire_results_percentage, questionnaire_results.result_level AS questionnaire_results_result_level, questionnaire_results.result_category AS questionnaire_results_result_category, questionnaire_results.interpretation AS questionnaire_results_interpretation, questionnaire_results.recommendations AS questionnaire_results_recommendations, questionnaire_results.dimension_scores AS questionnaire_results_dimension_scores, questionnaire_results.calculation_details AS questionnaire_results_calculation_details, questionnaire_results.raw_answers AS questionnaire_results_raw_answers, questionnaire_results.report_generated AS questionnaire_results_report_generated, questionnaire_results.report_content AS questionnaire_results_report_content, questionnaire_results.report_format AS questionnaire_results_report_format, questionnaire_results.report_template AS questionnaire_results_report_template, questionnaire_results.status AS questionnaire_results_status, questionnaire_results.calculated_at AS questionnaire_results_calculated_at, questionnaire_results.created_at AS questionnaire_results_created_at, questionnaire_results.updated_at AS questionnaire_results_updated_at, questionnaires.title AS questionnaire_name 
FROM questionnaire_results JOIN questionnaires ON questionnaire_results.questionnaire_id = questionnaires.id 
WHERE questionnaire_results.custom_id = ?) AS anon_1
2025-07-07 10:00:34,890 - sqlalchemy.engine.Engine - INFO - [generated in 0.00303s] ('SM_008',)
2025-07-07 10:00:34,894 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-07-07 10:00:34,902 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-07 10:00:34,904 - main - INFO - --- 请求结束: 200 ---

2025-07-07 10:00:36,325 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.7%, CPU使用率 84.0%
2025-07-07 10:00:51,470 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.1%, CPU使用率 97.0%
2025-07-07 10:01:04,203 - health_monitor - DEBUG - 系统指标 - CPU: 55.4%, 内存: 56.3%, 磁盘: 87.3%
2025-07-07 10:01:06,585 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.7%, CPU使用率 93.1%
2025-07-07 10:01:21,693 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.8%, CPU使用率 32.1%
2025-07-07 10:01:36,798 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.6%, CPU使用率 0.0%
2025-07-07 10:01:51,903 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.6%, CPU使用率 0.0%
2025-07-07 10:02:05,229 - health_monitor - DEBUG - 系统指标 - CPU: 34.0%, 内存: 51.6%, 磁盘: 87.2%
2025-07-07 10:02:07,008 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.7%, CPU使用率 23.1%
2025-07-07 10:02:22,113 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.6%, CPU使用率 0.0%
2025-07-07 10:02:37,217 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.5%, CPU使用率 0.0%
2025-07-07 10:02:52,327 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.4%, CPU使用率 87.5%
2025-07-07 10:03:06,265 - health_monitor - DEBUG - 系统指标 - CPU: 39.8%, 内存: 51.7%, 磁盘: 87.2%
2025-07-07 10:03:07,432 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.6%, CPU使用率 57.1%
2025-07-07 10:03:22,600 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.8%, CPU使用率 53.3%
2025-07-07 10:03:37,704 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.7%, CPU使用率 33.3%
2025-07-07 10:03:52,808 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.6%, CPU使用率 25.0%
2025-07-07 10:04:07,292 - health_monitor - DEBUG - 系统指标 - CPU: 27.3%, 内存: 51.6%, 磁盘: 87.2%
2025-07-07 10:04:07,914 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.7%, CPU使用率 0.0%
2025-07-07 10:04:23,019 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.3%, CPU使用率 7.4%
2025-07-07 10:04:38,125 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.3%, CPU使用率 75.0%
2025-07-07 10:04:53,232 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.5%, CPU使用率 66.7%
2025-07-07 10:05:08,322 - health_monitor - DEBUG - 系统指标 - CPU: 34.8%, 内存: 52.4%, 磁盘: 87.2%
2025-07-07 10:05:08,337 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.5%, CPU使用率 34.6%
2025-07-07 10:05:23,444 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.5%, CPU使用率 64.3%
2025-07-07 10:05:38,549 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.3%, CPU使用率 45.8%
2025-07-07 10:05:53,657 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.3%, CPU使用率 50.0%
2025-07-07 10:06:08,763 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.3%, CPU使用率 0.0%
2025-07-07 10:06:09,358 - health_monitor - DEBUG - 系统指标 - CPU: 33.1%, 内存: 52.3%, 磁盘: 87.2%
2025-07-07 10:06:23,868 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.4%, CPU使用率 3.6%
2025-07-07 10:06:38,972 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.1%, CPU使用率 26.9%
2025-07-07 10:06:54,153 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.5%, CPU使用率 53.6%
2025-07-07 10:07:09,268 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.9%, CPU使用率 64.3%
2025-07-07 10:07:10,389 - health_monitor - DEBUG - 系统指标 - CPU: 23.4%, 内存: 51.9%, 磁盘: 87.2%
2025-07-07 10:07:24,372 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.9%, CPU使用率 0.0%
2025-07-07 10:07:39,477 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.9%, CPU使用率 50.0%
2025-07-07 10:07:54,582 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.0%, CPU使用率 50.0%
2025-07-07 10:08:09,686 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.9%, CPU使用率 12.5%
2025-07-07 10:08:11,422 - health_monitor - DEBUG - 系统指标 - CPU: 44.4%, 内存: 52.0%, 磁盘: 87.2%
2025-07-07 10:08:24,791 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.0%, CPU使用率 14.3%
2025-07-07 10:08:40,058 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.6%, CPU使用率 100.0%
2025-07-07 10:08:48,773 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-07 10:08:48,774 - main - INFO - 请求没有认证头部
2025-07-07 10:08:48,775 - main - INFO - 没有认证头部，设置用户为None
2025-07-07 10:08:48,776 - main - INFO - --- 请求结束: 200 ---

2025-07-07 10:08:50,806 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-07 10:08:50,807 - main - INFO - 请求没有认证头部
2025-07-07 10:08:50,808 - main - INFO - 没有认证头部，设置用户为None
2025-07-07 10:08:50,810 - app.core.db_connection - DEBUG - 当前线程ID: 14944
2025-07-07 10:08:50,811 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-07 10:08:50,811 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-07 10:08:50,812 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-07 10:08:50,813 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-07 10:08:51,548 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-07 10:08:51,550 - main - INFO - --- 请求结束: 200 ---

2025-07-07 10:08:55,180 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.0%, CPU使用率 19.2%
2025-07-07 10:09:10,284 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.8%, CPU使用率 16.0%
2025-07-07 10:09:12,457 - health_monitor - DEBUG - 系统指标 - CPU: 40.5%, 内存: 53.9%, 磁盘: 87.2%
2025-07-07 10:09:25,388 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.8%, CPU使用率 12.5%
2025-07-07 10:09:40,493 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.8%, CPU使用率 0.0%
2025-07-07 10:09:55,598 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.8%, CPU使用率 7.1%
2025-07-07 10:10:10,702 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.8%, CPU使用率 16.7%
2025-07-07 10:10:13,484 - health_monitor - DEBUG - 系统指标 - CPU: 19.5%, 内存: 53.8%, 磁盘: 87.2%
2025-07-07 10:10:25,823 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.9%, CPU使用率 96.4%
2025-07-07 10:10:40,931 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.6%, CPU使用率 0.0%
2025-07-07 10:10:56,035 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.6%, CPU使用率 17.9%
2025-07-07 10:11:11,141 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.6%, CPU使用率 25.0%
2025-07-07 10:11:14,516 - health_monitor - DEBUG - 系统指标 - CPU: 19.9%, 内存: 53.6%, 磁盘: 87.2%
2025-07-07 10:11:26,245 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.5%, CPU使用率 4.2%
2025-07-07 10:11:41,350 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.6%, CPU使用率 14.3%
2025-07-07 10:11:56,454 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.6%, CPU使用率 8.3%
2025-07-07 10:12:11,558 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.5%, CPU使用率 34.6%
2025-07-07 10:12:15,554 - health_monitor - DEBUG - 系统指标 - CPU: 33.6%, 内存: 53.3%, 磁盘: 87.2%
2025-07-07 10:12:26,666 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.2%, CPU使用率 37.0%
2025-07-07 10:12:41,770 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.2%, CPU使用率 7.7%
2025-07-07 10:12:56,874 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.1%, CPU使用率 8.3%
2025-07-07 10:13:11,978 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.1%, CPU使用率 46.4%
2025-07-07 10:13:16,592 - health_monitor - DEBUG - 系统指标 - CPU: 18.7%, 内存: 53.1%, 磁盘: 87.2%
2025-07-07 10:13:27,089 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.1%, CPU使用率 30.8%
2025-07-07 10:13:42,193 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.2%, CPU使用率 0.0%
2025-07-07 10:13:57,297 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.2%, CPU使用率 20.8%
2025-07-07 10:14:12,402 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.2%, CPU使用率 15.4%
2025-07-07 10:14:17,627 - health_monitor - DEBUG - 系统指标 - CPU: 21.1%, 内存: 53.2%, 磁盘: 87.2%
2025-07-07 10:14:27,506 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.2%, CPU使用率 0.0%
2025-07-07 10:14:42,611 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.2%, CPU使用率 29.2%
2025-07-07 10:14:57,715 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.2%, CPU使用率 0.0%
2025-07-07 10:15:12,819 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.9%, CPU使用率 25.0%
2025-07-07 10:15:18,655 - health_monitor - DEBUG - 系统指标 - CPU: 17.6%, 内存: 53.0%, 磁盘: 87.2%
2025-07-07 10:15:27,923 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.0%, CPU使用率 0.0%
2025-07-07 10:15:43,028 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.0%, CPU使用率 12.5%
2025-07-07 10:15:58,133 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.9%, CPU使用率 17.9%
2025-07-07 10:16:13,238 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.3%, CPU使用率 53.6%
2025-07-07 10:16:19,692 - health_monitor - DEBUG - 系统指标 - CPU: 15.2%, 内存: 53.2%, 磁盘: 87.2%
2025-07-07 10:16:28,344 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.9%, CPU使用率 64.0%
2025-07-07 10:16:43,452 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 91.7%
2025-07-07 10:16:58,558 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 0.0%
2025-07-07 10:17:13,663 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 11.5%
2025-07-07 10:17:20,720 - health_monitor - DEBUG - 系统指标 - CPU: 22.4%, 内存: 55.0%, 磁盘: 87.3%
2025-07-07 10:17:28,769 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 3.8%
2025-07-07 10:17:43,873 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 29.2%
2025-07-07 10:17:58,979 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.0%, CPU使用率 78.6%
2025-07-07 10:18:14,084 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.6%, CPU使用率 0.0%
2025-07-07 10:18:21,757 - health_monitor - DEBUG - 系统指标 - CPU: 42.6%, 内存: 55.0%, 磁盘: 87.3%
2025-07-07 10:18:29,193 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 78.6%
2025-07-07 10:18:44,297 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 8.3%
2025-07-07 10:18:59,473 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.2%, CPU使用率 100.0%
2025-07-07 10:19:14,595 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 29.2%
2025-07-07 10:19:22,787 - health_monitor - DEBUG - 系统指标 - CPU: 30.9%, 内存: 54.9%, 磁盘: 87.3%
2025-07-07 10:19:29,701 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 12.5%
2025-07-07 10:19:44,806 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.8%, CPU使用率 28.6%
2025-07-07 10:19:59,911 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.8%, CPU使用率 21.4%
2025-07-07 10:20:15,017 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.8%, CPU使用率 29.2%
2025-07-07 10:20:23,815 - health_monitor - DEBUG - 系统指标 - CPU: 23.1%, 内存: 54.7%, 磁盘: 87.3%
2025-07-07 10:20:30,121 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.7%, CPU使用率 15.4%
2025-07-07 10:20:45,229 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.7%, CPU使用率 3.6%
2025-07-07 10:21:00,333 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.8%, CPU使用率 11.5%
2025-07-07 10:21:15,438 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.8%, CPU使用率 25.0%
2025-07-07 10:21:24,853 - health_monitor - DEBUG - 系统指标 - CPU: 54.1%, 内存: 54.8%, 磁盘: 87.3%
2025-07-07 10:21:30,542 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.8%, CPU使用率 0.0%
2025-07-07 10:21:45,649 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.8%, CPU使用率 21.4%
2025-07-07 10:22:00,754 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.8%, CPU使用率 26.9%
2025-07-07 10:22:15,872 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 92.9%
2025-07-07 10:22:25,889 - health_monitor - DEBUG - 系统指标 - CPU: 21.9%, 内存: 54.7%, 磁盘: 87.3%
2025-07-07 10:22:30,984 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.7%, CPU使用率 0.0%
2025-07-07 10:22:46,089 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.7%, CPU使用率 0.0%
2025-07-07 10:23:01,193 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.7%, CPU使用率 0.0%
2025-07-07 10:23:16,298 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.7%, CPU使用率 25.0%
2025-07-07 10:23:26,929 - health_monitor - DEBUG - 系统指标 - CPU: 13.7%, 内存: 54.7%, 磁盘: 87.3%
2025-07-07 10:23:31,403 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.7%, CPU使用率 0.0%
2025-07-07 10:23:46,508 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 7.4%
2025-07-07 10:24:01,613 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 7.1%
2025-07-07 10:24:16,724 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.7%, CPU使用率 51.9%
2025-07-07 10:24:27,961 - health_monitor - DEBUG - 系统指标 - CPU: 20.7%, 内存: 54.8%, 磁盘: 87.3%
2025-07-07 10:24:31,828 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.7%, CPU使用率 4.2%
2025-07-07 10:24:46,932 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.6%, CPU使用率 28.0%
2025-07-07 10:25:02,040 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.8%, CPU使用率 64.3%
2025-07-07 10:25:17,145 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.7%, CPU使用率 21.4%
2025-07-07 10:25:28,990 - health_monitor - DEBUG - 系统指标 - CPU: 18.0%, 内存: 54.8%, 磁盘: 87.3%
2025-07-07 10:25:32,249 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.8%, CPU使用率 8.3%
2025-07-07 10:25:47,355 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.7%, CPU使用率 16.7%
2025-07-07 10:26:02,460 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.8%, CPU使用率 3.6%
2025-07-07 10:26:17,575 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 39.3%
2025-07-07 10:26:30,019 - health_monitor - DEBUG - 系统指标 - CPU: 23.0%, 内存: 54.9%, 磁盘: 87.3%
2025-07-07 10:26:32,681 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 26.9%
2025-07-07 10:26:47,785 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 30.8%
2025-07-07 10:27:02,894 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 38.5%
2025-07-07 10:27:17,999 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 0.0%
2025-07-07 10:27:31,049 - health_monitor - DEBUG - 系统指标 - CPU: 23.0%, 内存: 55.0%, 磁盘: 87.3%
2025-07-07 10:27:33,106 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 29.2%
2025-07-07 10:27:48,211 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 42.9%
2025-07-07 10:28:03,316 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 14.8%
2025-07-07 10:28:18,420 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 29.2%
2025-07-07 10:28:32,087 - health_monitor - DEBUG - 系统指标 - CPU: 25.7%, 内存: 55.0%, 磁盘: 87.3%
2025-07-07 10:28:33,524 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 0.0%
2025-07-07 10:28:48,629 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 48.1%
2025-07-07 10:29:03,733 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.2%, CPU使用率 16.7%
2025-07-07 10:29:18,838 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 21.4%
2025-07-07 10:29:33,123 - health_monitor - DEBUG - 系统指标 - CPU: 51.4%, 内存: 55.1%, 磁盘: 87.3%
2025-07-07 10:29:33,944 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 50.0%
2025-07-07 10:29:49,049 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 16.7%
2025-07-07 10:30:04,154 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 37.5%
2025-07-07 10:30:19,258 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 0.0%
2025-07-07 10:30:34,151 - health_monitor - DEBUG - 系统指标 - CPU: 35.4%, 内存: 55.1%, 磁盘: 87.3%
2025-07-07 10:30:34,363 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 11.1%
2025-07-07 10:30:49,467 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 0.0%
2025-07-07 10:31:04,572 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 25.0%
2025-07-07 10:31:19,676 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 15.4%
2025-07-07 10:31:34,780 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 8.3%
2025-07-07 10:31:35,179 - health_monitor - DEBUG - 系统指标 - CPU: 26.6%, 内存: 55.1%, 磁盘: 87.3%
2025-07-07 10:31:49,885 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 0.0%
2025-07-07 10:32:04,990 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 25.9%
2025-07-07 10:32:20,095 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 4.2%
2025-07-07 10:32:35,200 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 4.2%
2025-07-07 10:32:36,207 - health_monitor - DEBUG - 系统指标 - CPU: 19.5%, 内存: 55.0%, 磁盘: 87.3%
2025-07-07 10:32:50,305 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 11.1%
2025-07-07 10:33:05,411 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 46.2%
2025-07-07 10:33:20,515 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 29.2%
2025-07-07 10:33:35,621 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.8%, CPU使用率 37.5%
2025-07-07 10:33:37,236 - health_monitor - DEBUG - 系统指标 - CPU: 14.8%, 内存: 54.9%, 磁盘: 87.3%
2025-07-07 10:33:50,725 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 17.9%
2025-07-07 10:34:05,830 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 16.7%
2025-07-07 10:34:20,935 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.2%, CPU使用率 66.7%
2025-07-07 10:34:36,042 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.8%, CPU使用率 0.0%
2025-07-07 10:34:38,264 - health_monitor - DEBUG - 系统指标 - CPU: 19.5%, 内存: 54.9%, 磁盘: 87.3%
2025-07-07 10:34:51,147 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 3.8%
2025-07-07 10:35:06,251 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 25.0%
2025-07-07 10:35:21,355 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 15.4%
2025-07-07 10:35:36,460 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 23.1%
2025-07-07 10:35:39,293 - health_monitor - DEBUG - 系统指标 - CPU: 10.9%, 内存: 54.9%, 磁盘: 87.3%
2025-07-07 10:35:51,564 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 4.2%
2025-07-07 10:36:06,669 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 4.2%
2025-07-07 10:36:21,786 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 53.6%
2025-07-07 10:36:36,893 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 16.7%
2025-07-07 10:36:40,320 - health_monitor - DEBUG - 系统指标 - CPU: 13.2%, 内存: 54.9%, 磁盘: 87.3%
2025-07-07 10:36:51,997 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 32.0%
2025-07-07 10:37:07,104 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 50.0%
2025-07-07 10:37:22,211 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 0.0%
2025-07-07 10:37:37,316 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 30.8%
2025-07-07 10:37:41,348 - health_monitor - DEBUG - 系统指标 - CPU: 29.3%, 内存: 55.1%, 磁盘: 87.3%
2025-07-07 10:37:52,420 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 25.0%
2025-07-07 10:38:07,526 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 37.0%
2025-07-07 10:38:22,630 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 0.0%
2025-07-07 10:38:37,745 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 28.6%
2025-07-07 10:38:42,377 - health_monitor - DEBUG - 系统指标 - CPU: 13.3%, 内存: 55.0%, 磁盘: 87.3%
2025-07-07 10:38:52,849 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 0.0%
2025-07-07 10:39:07,954 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 20.8%
2025-07-07 10:39:23,059 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.2%, CPU使用率 23.1%
2025-07-07 10:39:38,184 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.3%, CPU使用率 46.4%
2025-07-07 10:39:43,419 - health_monitor - DEBUG - 系统指标 - CPU: 25.6%, 内存: 55.2%, 磁盘: 87.3%
2025-07-07 10:39:53,288 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.3%, CPU使用率 28.6%
2025-07-07 10:40:08,393 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.2%, CPU使用率 0.0%
2025-07-07 10:40:23,498 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.2%, CPU使用率 8.3%
2025-07-07 10:40:38,603 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.6%, CPU使用率 21.4%
2025-07-07 10:40:44,462 - health_monitor - DEBUG - 系统指标 - CPU: 32.3%, 内存: 54.4%, 磁盘: 87.3%
2025-07-07 10:40:53,708 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.4%, CPU使用率 40.7%
2025-07-07 10:41:08,813 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.4%, CPU使用率 0.0%
2025-07-07 10:41:23,917 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.5%, CPU使用率 17.4%
2025-07-07 10:41:39,021 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.4%, CPU使用率 21.4%
2025-07-07 10:41:45,490 - health_monitor - DEBUG - 系统指标 - CPU: 11.7%, 内存: 54.4%, 磁盘: 87.3%
2025-07-07 10:41:54,127 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.3%, CPU使用率 29.2%
2025-07-07 10:42:09,231 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.4%, CPU使用率 25.0%
2025-07-07 10:42:24,336 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.5%, CPU使用率 14.3%
2025-07-07 10:42:39,441 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.4%, CPU使用率 11.1%
2025-07-07 10:42:46,536 - health_monitor - DEBUG - 系统指标 - CPU: 14.1%, 内存: 54.5%, 磁盘: 87.3%
2025-07-07 10:42:54,546 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.3%, CPU使用率 8.3%
2025-07-07 10:43:09,650 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.4%, CPU使用率 0.0%
2025-07-07 10:43:24,762 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.5%, CPU使用率 50.0%
2025-07-07 10:43:39,871 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.6%, CPU使用率 0.0%
2025-07-07 10:43:47,585 - health_monitor - DEBUG - 系统指标 - CPU: 20.6%, 内存: 54.6%, 磁盘: 87.3%
2025-07-07 10:43:54,976 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.6%, CPU使用率 0.0%
2025-07-07 10:44:10,080 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.7%, CPU使用率 0.0%
2025-07-07 10:44:25,185 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.7%, CPU使用率 8.3%
2025-07-07 10:44:40,290 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.6%, CPU使用率 0.0%
2025-07-07 10:44:48,616 - health_monitor - DEBUG - 系统指标 - CPU: 15.6%, 内存: 54.6%, 磁盘: 87.3%
2025-07-07 10:44:55,395 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.7%, CPU使用率 0.0%
2025-07-07 10:45:10,500 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.8%, CPU使用率 16.7%
2025-07-07 10:45:25,608 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 29.2%
2025-07-07 10:45:40,713 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 17.9%
2025-07-07 10:45:49,644 - health_monitor - DEBUG - 系统指标 - CPU: 14.8%, 内存: 54.9%, 磁盘: 87.3%
2025-07-07 10:45:55,818 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.8%, CPU使用率 0.0%
2025-07-07 10:46:10,923 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 25.0%
2025-07-07 10:46:26,029 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.3%, CPU使用率 44.0%
2025-07-07 10:46:41,133 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.3%, CPU使用率 24.1%
2025-07-07 10:46:50,678 - health_monitor - DEBUG - 系统指标 - CPU: 34.0%, 内存: 54.9%, 磁盘: 87.3%
2025-07-07 10:46:56,238 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 25.9%
2025-07-07 10:47:11,342 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 16.7%
2025-07-07 10:47:26,446 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 14.3%
2025-07-07 10:47:41,551 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 17.9%
2025-07-07 10:47:51,705 - health_monitor - DEBUG - 系统指标 - CPU: 17.7%, 内存: 55.2%, 磁盘: 87.3%
2025-07-07 10:47:56,657 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 41.7%
2025-07-07 10:48:11,761 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 0.0%
2025-07-07 10:48:26,866 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 0.0%
2025-07-07 10:48:41,972 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 50.0%
2025-07-07 10:48:52,734 - health_monitor - DEBUG - 系统指标 - CPU: 14.4%, 内存: 55.0%, 磁盘: 87.3%
2025-07-07 10:48:57,077 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 21.7%
2025-07-07 10:49:12,182 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 23.1%
2025-07-07 10:49:27,288 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 26.9%
2025-07-07 10:49:42,393 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 20.8%
2025-07-07 10:49:53,762 - health_monitor - DEBUG - 系统指标 - CPU: 23.0%, 内存: 55.1%, 磁盘: 87.3%
2025-07-07 10:49:57,498 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 29.2%
2025-07-07 10:50:12,603 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 17.9%
2025-07-07 10:50:27,707 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 0.0%
2025-07-07 10:50:42,812 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 0.0%
2025-07-07 10:50:54,791 - health_monitor - DEBUG - 系统指标 - CPU: 22.3%, 内存: 55.2%, 磁盘: 87.3%
2025-07-07 10:50:57,917 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 0.0%
2025-07-07 10:51:13,023 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 0.0%
2025-07-07 10:51:28,128 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 12.5%
2025-07-07 10:51:43,262 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 58.6%
2025-07-07 10:51:55,822 - health_monitor - DEBUG - 系统指标 - CPU: 27.3%, 内存: 55.0%, 磁盘: 87.3%
2025-07-07 10:51:58,366 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 25.9%
2025-07-07 10:52:13,471 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 0.0%
2025-07-07 10:52:28,576 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 29.2%
2025-07-07 10:52:43,682 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 71.4%
2025-07-07 10:52:56,856 - health_monitor - DEBUG - 系统指标 - CPU: 19.1%, 内存: 55.1%, 磁盘: 87.3%
2025-07-07 10:52:58,786 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 7.4%
2025-07-07 10:53:13,891 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 0.0%
2025-07-07 10:53:28,995 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 12.0%
2025-07-07 10:53:44,102 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 44.4%
2025-07-07 10:53:57,886 - health_monitor - DEBUG - 系统指标 - CPU: 23.0%, 内存: 55.0%, 磁盘: 87.3%
2025-07-07 10:53:59,207 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 0.0%
2025-07-07 10:54:14,312 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 16.7%
2025-07-07 10:54:29,417 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 79.2%
2025-07-07 10:54:44,522 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 0.0%
2025-07-07 10:54:58,914 - health_monitor - DEBUG - 系统指标 - CPU: 20.2%, 内存: 54.9%, 磁盘: 87.3%
2025-07-07 10:54:59,627 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 4.2%
2025-07-07 10:55:14,731 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 0.0%
2025-07-07 10:55:29,836 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 14.3%
2025-07-07 10:55:44,941 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 74.1%
2025-07-07 10:55:59,942 - health_monitor - DEBUG - 系统指标 - CPU: 8.6%, 内存: 54.9%, 磁盘: 87.3%
2025-07-07 10:56:00,046 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 54.2%
2025-07-07 10:56:15,150 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 3.6%
2025-07-07 10:56:30,254 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 35.7%
2025-07-07 10:56:45,358 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 20.8%
2025-07-07 10:57:00,463 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 3.6%
2025-07-07 10:57:00,973 - health_monitor - DEBUG - 系统指标 - CPU: 28.4%, 内存: 54.8%, 磁盘: 87.3%
2025-07-07 10:57:15,568 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 31.0%
2025-07-07 10:57:30,673 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 25.0%
2025-07-07 10:57:45,778 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.8%, CPU使用率 16.0%
2025-07-07 10:58:00,884 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 71.4%
2025-07-07 10:58:02,001 - health_monitor - DEBUG - 系统指标 - CPU: 16.5%, 内存: 54.9%, 磁盘: 87.3%
2025-07-07 10:58:15,989 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 0.0%
2025-07-07 10:58:31,093 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.8%, CPU使用率 16.7%
2025-07-07 10:58:46,199 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 20.8%
2025-07-07 10:59:01,303 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 39.3%
2025-07-07 10:59:03,029 - health_monitor - DEBUG - 系统指标 - CPU: 12.1%, 内存: 55.0%, 磁盘: 87.3%
2025-07-07 10:59:16,407 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 33.3%
2025-07-07 10:59:31,513 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 0.0%
2025-07-07 10:59:46,617 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 3.6%
2025-07-07 11:00:01,722 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 25.0%
2025-07-07 11:00:04,062 - health_monitor - DEBUG - 系统指标 - CPU: 34.2%, 内存: 54.9%, 磁盘: 87.3%
2025-07-07 11:00:16,826 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 25.0%
2025-07-07 11:00:31,932 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 3.6%
2025-07-07 11:00:47,036 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 3.7%
2025-07-07 11:01:02,141 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 29.2%
2025-07-07 11:01:05,094 - health_monitor - DEBUG - 系统指标 - CPU: 24.6%, 内存: 54.9%, 磁盘: 87.3%
2025-07-07 11:01:17,247 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.5%, CPU使用率 3.7%
2025-07-07 11:01:32,351 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.6%, CPU使用率 25.0%
2025-07-07 11:01:47,457 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.6%, CPU使用率 32.1%
2025-07-07 11:02:02,562 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.5%, CPU使用率 8.3%
2025-07-07 11:02:06,125 - health_monitor - DEBUG - 系统指标 - CPU: 8.1%, 内存: 55.6%, 磁盘: 87.3%
2025-07-07 11:02:17,670 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.3%, CPU使用率 70.8%
2025-07-07 11:02:32,776 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.3%, CPU使用率 28.6%
2025-07-07 11:02:47,881 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.3%, CPU使用率 18.5%
2025-07-07 11:03:02,986 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 0.0%
2025-07-07 11:03:07,155 - health_monitor - DEBUG - 系统指标 - CPU: 21.8%, 内存: 55.2%, 磁盘: 87.3%
2025-07-07 11:03:18,091 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.2%, CPU使用率 33.3%
2025-07-07 11:03:33,197 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.3%, CPU使用率 50.0%
2025-07-07 11:03:48,301 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.3%, CPU使用率 3.8%
2025-07-07 11:04:03,406 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.3%, CPU使用率 0.0%
2025-07-07 11:04:08,184 - health_monitor - DEBUG - 系统指标 - CPU: 11.3%, 内存: 55.3%, 磁盘: 87.3%
2025-07-07 11:04:18,511 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.3%, CPU使用率 0.0%
2025-07-07 11:04:33,616 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.2%, CPU使用率 23.1%
2025-07-07 11:04:48,721 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.2%, CPU使用率 16.7%
2025-07-07 11:05:03,826 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 25.0%
2025-07-07 11:05:09,212 - health_monitor - DEBUG - 系统指标 - CPU: 16.8%, 内存: 55.2%, 磁盘: 87.3%
2025-07-07 11:05:18,931 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.8%, CPU使用率 38.5%
