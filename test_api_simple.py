#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的API测试脚本
"""

import requests
import json

# API基础URL
BASE_URL = "http://127.0.0.1:8006"

def test_api_without_auth():
    """测试不需要认证的API端点"""
    print("=== 测试健康检查API ===")
    try:
        response = requests.get(f"{BASE_URL}/api/health", timeout=5)
        print(f"健康检查状态码: {response.status_code}")
        if response.status_code == 200:
            print(f"健康检查响应: {response.json()}")
    except Exception as e:
        print(f"健康检查异常: {e}")

def test_questionnaires_api():
    """测试问卷API（无认证）"""
    print("\n=== 测试问卷API ===")
    url = f"{BASE_URL}/api/questionnaires"
    
    try:
        response = requests.get(url, timeout=10)
        print(f"问卷API状态码: {response.status_code}")
        print(f"问卷API响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"问卷API响应数据类型: {type(data)}")
            if isinstance(data, dict):
                print(f"问卷API响应键: {list(data.keys())}")
                if 'data' in data:
                    print(f"问卷数据长度: {len(data['data']) if isinstance(data['data'], list) else 'N/A'}")
            print(f"问卷API响应: {json.dumps(data, indent=2, ensure_ascii=False)[:500]}...")
        else:
            print(f"问卷API错误响应: {response.text[:200]}...")
            
    except Exception as e:
        print(f"问卷API请求异常: {e}")

def test_assessments_api():
    """测试量表API（无认证）"""
    print("\n=== 测试量表API ===")
    url = f"{BASE_URL}/api/assessments"
    
    try:
        response = requests.get(url, timeout=10)
        print(f"量表API状态码: {response.status_code}")
        print(f"量表API响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"量表API响应数据类型: {type(data)}")
            if isinstance(data, dict):
                print(f"量表API响应键: {list(data.keys())}")
                if 'data' in data:
                    print(f"量表数据长度: {len(data['data']) if isinstance(data['data'], list) else 'N/A'}")
            print(f"量表API响应: {json.dumps(data, indent=2, ensure_ascii=False)[:500]}...")
        else:
            print(f"量表API错误响应: {response.text[:200]}...")
            
    except Exception as e:
        print(f"量表API请求异常: {e}")

if __name__ == "__main__":
    print("开始测试API端点...")
    test_api_without_auth()
    test_questionnaires_api()
    test_assessments_api()
    print("\n测试完成！")