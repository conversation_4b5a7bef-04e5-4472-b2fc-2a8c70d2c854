# -*- coding: utf-8 -*-
"""
其他记录屏幕
管理各种健康相关的其他记录，如康复记录、营养记录、运动记录等

功能特点：
1. 多类型记录：支持康复、营养、运动、心理等多种记录类型
2. 自定义分类：用户可自定义记录分类
3. 多媒体支持：支持文字、图片、语音、视频记录
4. 时间轴展示：按时间顺序展示记录
5. 标签管理：支持为记录添加标签
6. 搜索筛选：支持按类型、时间、标签搜索
7. 数据统计：提供记录统计和趋势分析
8. 导出分享：支持记录导出和分享
"""

from kivy.clock import Clock
from kivy.metrics import dp
from kivy.logger import Logger
from kivymd.app import MDApp
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDIconButton, MDFabButton, MDButton, MDButtonText, MDButtonIcon
# MDTabsPrimary, MDTabsItem, MDTabsItemIcon, MDTabsItemText 在 KivyMD 2.0.1 dev0 中已被弃用
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
from kivymd.uix.list import MDList, MDListItem
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.floatlayout import MDFloatLayout
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText
from kivymd.uix.dialog import MDDialog
from kivymd.uix.textfield import MDTextField
from kivymd.uix.selectioncontrol import MDSwitch
from kivymd.uix.selectioncontrol import MDCheckbox
from kivymd.uix.menu import MDDropdownMenu
from kivymd.uix.chip import MDChip
from utils.toast import toast
from kivy.properties import StringProperty, NumericProperty, ListProperty, ObjectProperty

from screens.base_screen import BaseScreen
from widgets.logo import HealthLogo
from theme import ThemeManager
from theme import FontManager

# KV语言字符串
KV = '''
<RecordCard>:
    orientation: "vertical"
    size_hint_y: None
    height: dp(160)
    padding: dp(16)
    spacing: dp(12)
    elevation: 2
    radius: [dp(12)]
    md_bg_color: app.theme.ELEVATED_SURFACE
    
    MDBoxLayout:
        orientation: "horizontal"
        spacing: dp(16)
        size_hint_y: None
        height: dp(40)
        
        MDLabel:
            text: root.title
            theme_text_color: "Primary"
            font_size: dp(16)
            bold: True
            size_hint_y: None
            height: dp(40)
        
        MDLabel:
            text: root.date
            theme_text_color: "Secondary"
            font_size: dp(12)
            size_hint_y: None
            height: dp(40)
            size_hint_x: 0.3
            halign: "right"
    
    MDBoxLayout:
        orientation: "horizontal"
        size_hint_y: None
        height: dp(30)
        
        MDChip:
            text: root.category
            md_bg_color: app.theme.PRIMARY_LIGHT
            text_color: app.theme.PRIMARY_COLOR
            size_hint: None, None
            height: dp(30)
            width: dp(120)
    
    MDLabel:
        text: root.description
        theme_text_color: "Secondary"
        font_size: dp(14)
        size_hint_y: None
        height: dp(40)
    
    MDBoxLayout:
        orientation: "horizontal"
        spacing: dp(8)
        size_hint_y: None
        height: dp(36)
        
        MDIconButton:
            icon: "eye"
            theme_icon_color: "Custom"
            icon_color: app.theme.PRIMARY_COLOR
            on_release: root.on_view()
        
        MDIconButton:
            icon: "pencil"
            theme_icon_color: "Custom"
            icon_color: app.theme.SUCCESS_COLOR
            on_release: root.on_edit()
        
        MDIconButton:
            icon: "delete"
            theme_icon_color: "Custom"
            icon_color: app.theme.ERROR_COLOR
            on_release: root.on_delete()

<OtherRecordsScreen>:
    name: "other_records"
    
    MDBoxLayout:
        orientation: "vertical"
        
        # 顶部栏
        MDBoxLayout:
            id: app_bar
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(56)
            md_bg_color: app.theme.PRIMARY_COLOR
            padding: [dp(4), dp(0), dp(4), dp(0)]
            
            MDIconButton:
                icon: "arrow-left"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.go_back()
            
            MDLabel:
                text: "其他记录"
                font_size: dp(18)
                bold: True
                theme_text_color: "Custom"
                text_color: app.theme.TEXT_LIGHT
                halign: "center"
                valign: "center"
            
            MDIconButton:
                icon: "magnify"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.show_search()
            
            MDIconButton:
                icon: "plus"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.add_record()
        
        # 筛选条件
        MDCard:
            orientation: "horizontal"
            size_hint_y: None
            height: dp(60)
            padding: dp(16)
            spacing: dp(16)
            elevation: 1
            radius: [0]
            md_bg_color: app.theme.SURFACE_COLOR
            
            MDButton:
                style: "elevated"
                id: type_filter_btn
                size_hint: None, None
                size: dp(120), dp(36)
                on_release: root.show_filter_dialog()
                
                MDButtonText:
                    text: "全部类型"
            
            MDButton:
                style: "elevated"
                id: sort_btn
                size_hint: None, None
                size: dp(120), dp(36)
                on_release: root.show_sort_dialog()
                
                MDButtonText:
                    text: "时间排序"
        
        # 记录列表
        MDScrollView:
            id: records_scroll
            do_scroll_x: False
            do_scroll_y: True
            
            MDGridLayout:
                id: records_layout
                cols: 1
                spacing: dp(16)
                padding: dp(16)
                size_hint_y: None
                height: self.minimum_height
'''

# 加载KV字符串
from kivy.lang import Builder
Builder.load_string(KV)

class RecordTypeCard(MDCard):
    """记录类型卡片组件"""
    type_name = StringProperty('未知类型')
    description = StringProperty('暂无描述')
    record_count = NumericProperty(0)
    type_icon = StringProperty('file-document')
    icon_color = StringProperty('#2196F3')
    
    def __init__(self, type_data, **kwargs):
        super().__init__(**kwargs)
        self.type_data = type_data
        # 设置卡片属性
        self.type_name = type_data.get('name', '未知类型')
        self.description = type_data.get('description', '暂无描述')
        self.record_count = type_data.get('count', 0)
        self.type_icon = type_data.get('icon', 'file-document')
        self.icon_color = type_data.get('color', '#2196F3')
    
    def view_records(self):
        """查看该类型的记录"""
        screen = self.get_screen()
        if screen:
            screen.view_type_records(self.type_data)
    
    def add_record(self):
        """添加该类型的记录"""
        screen = self.get_screen()
        if screen:
            screen.add_type_record(self.type_data)
    
    def get_screen(self):
        """获取父屏幕"""
        parent = self.parent
        while parent:
            if hasattr(parent, 'name') and parent.name == 'other_records':
                return parent
            parent = parent.parent
        return None

class RecordCard(MDCard):
    """记录卡片组件"""
    title = StringProperty("")
    description = StringProperty("")
    date = StringProperty("")
    category = StringProperty("")
    icon = StringProperty("file-document")
    record_data = ObjectProperty(None)
    
    def __init__(self, record_data=None, **kwargs):
        super().__init__(**kwargs)
        if record_data:
            self.record_data = record_data
            self.title = record_data.get("title", "")
            self.description = record_data.get("description", "")
            self.date = record_data.get("date", "").split()[0]  # 只显示日期部分
            self.category = record_data.get("category", "")
            self.icon = record_data.get("icon", "file-document")
    
    def on_view(self):
        """查看记录"""
        app = MDApp.get_running_app()
        screen = app.root.get_screen("other_records")
        if hasattr(screen, 'view_record'):
            screen.view_record(self.record_data)
    
    def on_edit(self):
        """编辑记录"""
        app = MDApp.get_running_app()
        screen = app.root.get_screen("other_records")
        if hasattr(screen, 'edit_record'):
            screen.edit_record(self.record_data)
    
    def on_delete(self):
        """删除记录"""
        app = MDApp.get_running_app()
        screen = app.root.get_screen("other_records")
        if hasattr(screen, 'delete_record'):
            screen.delete_record(self.record_data)

class OtherRecordsScreen(BaseScreen):
    """其他记录屏幕"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.app = MDApp.get_running_app()
        self.records = []
        self.current_filter = "全部"
        self.current_sort = "日期降序"
        # 确保屏幕名称正确设置
        self.name = "other_records"
        # 延迟初始化UI，确保KV已经加载
        Clock.schedule_once(self.init_ui, 0.5)
    
    def init_ui(self, dt=0):
        """初始化UI"""
        try:
            Logger.info("OtherRecordsScreen: 初始化UI")
            # 加载测试数据
            self.load_data()
            # 刷新UI
            self.refresh_ui()
        except Exception as e:
            Logger.error(f"OtherRecordsScreen: 初始化UI失败: {str(e)}")
            import traceback
            Logger.error(traceback.format_exc())
    
    def on_enter(self):
        """进入屏幕时调用"""
        super().on_enter()
        Logger.info("OtherRecordsScreen: 进入屏幕")
        # 延迟刷新UI，确保屏幕已完全加载
        Clock.schedule_once(self.refresh_ui, 0.5)
    
    def go_back(self):
        """返回上一页"""
        Logger.info("OtherRecordsScreen: 返回上一页")
        app = MDApp.get_running_app()
        app.root.transition.direction = 'right'
        app.root.current = 'health_data_management_screen'
    
    def add_record(self):
        """添加记录"""
        Logger.info("OtherRecordsScreen: 添加记录")
        self.show_record_dialog()
    
    def show_search(self):
        """显示搜索对话框"""
        Logger.info("OtherRecordsScreen: 显示搜索对话框")
        self.show_message("搜索功能开发中...")
    
    def refresh_ui(self, dt=0):
        """刷新UI"""
        try:
            Logger.info("OtherRecordsScreen: 刷新UI")
            
            # 检查 records_layout 是否存在
            if not hasattr(self.ids, 'records_layout'):
                Logger.error("OtherRecordsScreen: records_layout ID不存在")
                return
            
            # 清空布局
            self.ids.records_layout.clear_widgets()
            
            # 获取筛选和排序后的记录
            records = self.get_filtered_and_sorted_records()
            Logger.info(f"OtherRecordsScreen: 获取到 {len(records)} 条记录")
            
            if not records:
                # 显示空状态
                empty_label = MDLabel(
                    text="暂无记录\n点击右上角+号添加记录",
                    halign="center",
                    theme_text_color="Secondary",
                    size_hint_y=None,
                    height=dp(120)
                )
                self.ids.records_layout.add_widget(empty_label)
                Logger.info("OtherRecordsScreen: 添加了空状态标签")
                return
            
            # 添加记录卡片
            for record in records:
                try:
                    card = RecordCard(record_data=record)
                    self.ids.records_layout.add_widget(card)
                    Logger.info(f"OtherRecordsScreen: 添加了记录卡片: {record['title']}")
                except Exception as e:
                    Logger.error(f"OtherRecordsScreen: 添加记录卡片失败: {str(e)}")
                    import traceback
                    Logger.error(traceback.format_exc())
            
        except Exception as e:
            Logger.error(f"OtherRecordsScreen: 刷新UI失败: {str(e)}")
            import traceback
            Logger.error(traceback.format_exc())
    
    def load_data(self):
        """加载数据"""
        try:
            # 在实际应用中，这里应该从API或本地存储加载数据
            # 这里使用模拟数据进行演示
            from datetime import datetime
            
            self.records = [
                {
                    "id": "1",
                    "title": "年度体检报告",
                    "category": "体检报告",
                    "description": "2023年度全面体检报告，各项指标正常",
                    "notes": "建议定期复查血脂",
                    "date": "2023-06-15 09:30:00",
                    "icon": "clipboard-check"
                },
                {
                    "id": "2",
                    "title": "血常规检查",
                    "category": "化验单",
                    "description": "血常规检查结果，白细胞、红细胞计数正常",
                    "notes": "血红蛋白略低，注意补铁",
                    "date": "2023-06-10 14:20:00",
                    "icon": "test-tube"
                },
                {
                    "id": "3",
                    "title": "胸部X光片",
                    "category": "影像资料",
                    "description": "胸部X光检查，肺部清晰，心影正常",
                    "notes": "无异常发现",
                    "date": "2023-06-08 11:15:00",
                    "icon": "image-multiple"
                },
                {
                    "id": "4",
                    "title": "心电图检查",
                    "category": "检查报告",
                    "description": "静息心电图检查，心律齐整",
                    "notes": "心率正常范围",
                    "date": "2023-06-05 16:45:00",
                    "icon": "file-document"
                },
                {
                    "id": "5",
                    "title": "处方单",
                    "category": "处方记录",
                    "description": "感冒药物处方，阿莫西林胶囊",
                    "notes": "按时服药，注意休息",
                    "date": "2023-06-01 10:30:00",
                    "icon": "pill"
                }
            ]
            Logger.info(f"OtherRecordsScreen: 加载了 {len(self.records)} 条记录")
        except Exception as e:
            Logger.error(f"OtherRecordsScreen: 加载记录失败: {str(e)}")
            self.records = []
    
    def get_filtered_and_sorted_records(self):
        """获取筛选和排序后的记录"""
        # 筛选
        filtered_records = self.records
        if self.current_filter != "全部":
            filtered_records = [r for r in self.records if r["category"] == self.current_filter]
        
        # 排序
        if self.current_sort == "日期降序":
            filtered_records.sort(key=lambda x: x["date"], reverse=True)
        elif self.current_sort == "日期升序":
            filtered_records.sort(key=lambda x: x["date"])
        elif self.current_sort == "标题A-Z":
            filtered_records.sort(key=lambda x: x["title"])
        elif self.current_sort == "标题Z-A":
            filtered_records.sort(key=lambda x: x["title"], reverse=True)
        elif self.current_sort == "分类A-Z":
            filtered_records.sort(key=lambda x: x["category"])
        
        return filtered_records
    
    def show_message(self, message):
        """显示消息提示"""
        # 使用应用程序的通知机制
        app = MDApp.get_running_app()
        if hasattr(app, 'show_notification'):
            app.show_notification(message)
        else:
            # 使用Snackbar作为备选
            snackbar = MDSnackbar(
                MDSnackbarText(
                    text=message,
                ),
                pos_hint={"center_x": 0.5},
                duration=2,
            )
            snackbar.open()
    
    def delete_record(self, record):
        """删除记录"""
        # 创建确认对话框
        from kivy.core.window import Window
        from kivymd.uix.card import MDCard
        from kivymd.uix.dialog import MDDialogHeadlineText, MDDialogSupportingText, MDDialogButtonContainer
        
        # 创建对话框
        dialog = MDDialog()
        dialog.add_widget(
            MDDialogHeadlineText(
                text="确认删除",
            )
        )
        dialog.add_widget(
            MDDialogSupportingText(
                text=f"确定要删除记录 '{record['title']}' 吗？",
            )
        )
        
        # 添加按钮
        button_container = MDDialogButtonContainer()
        
        # 取消按钮
        cancel_btn = MDButton(
            style="text",
            on_release=lambda x: dialog.dismiss(),
        )
        cancel_btn.add_widget(MDButtonText(text="取消"))
        button_container.add_widget(cancel_btn)
        
        # 确认按钮
        confirm_btn = MDButton(
            style="filled",
            on_release=lambda x: self.confirm_delete(record, dialog),
        )
        confirm_btn.add_widget(MDButtonText(text="确定"))
        button_container.add_widget(confirm_btn)
        
        dialog.add_widget(button_container)
        dialog.open()

    def confirm_delete(self, record, dialog):
        """确认删除记录"""
        # 从记录列表中删除
        self.records = [r for r in self.records if r['id'] != record['id']]
        # 刷新UI
        self.refresh_ui()
        # 关闭对话框
        dialog.dismiss()
        # 显示成功消息
        self.show_message("删除成功")
    
    def show_filter_dialog(self):
        """显示筛选对话框"""
        from kivymd.uix.dialog import MDDialogHeadlineText, MDDialogSupportingText, MDDialogButtonContainer
        
        categories = ["全部"] + list(set([record["category"] for record in self.records]))
        
        # 创建对话框
        dialog = MDDialog()
        dialog.add_widget(
            MDDialogHeadlineText(
                text="选择分类筛选",
            )
        )
        
        # 添加分类按钮的容器
        content = MDBoxLayout(
            orientation='vertical',
            spacing=dp(8),
            size_hint_y=None,
            height=len(categories) * dp(48),
            padding=[dp(16), dp(16), dp(16), dp(16)]
        )
        
        for category in categories:
            button = MDButton(
                style="outlined" if category != self.current_filter else "filled",
                size_hint_x=None,
                width=dp(120),
                on_release=lambda x, cat=category: self.apply_filter(cat, dialog)
            )
            button.add_widget(MDButtonText(text=category))
            content.add_widget(button)
        
        dialog.add_widget(MDDialogSupportingText(
            text=" ",  # 空文本，用于添加自定义内容
        ))
        dialog.children[0].children[0].add_widget(content)  # 将自定义内容添加到支持文本区域
        
        # 添加按钮
        button_container = MDDialogButtonContainer()
        
        # 关闭按钮
        close_button = MDButton(
            style="text",
            on_release=lambda x: dialog.dismiss()
        )
        close_button.add_widget(MDButtonText(text="关闭"))
        button_container.add_widget(close_button)
        
        dialog.add_widget(button_container)
        dialog.open()

    def apply_filter(self, category, dialog=None):
        """应用筛选"""
        self.current_filter = category
        self.refresh_ui()
        if dialog:
            dialog.dismiss()
    
    def show_sort_dialog(self):
        """显示排序对话框"""
        from kivymd.uix.dialog import MDDialogHeadlineText, MDDialogSupportingText, MDDialogButtonContainer
        
        sort_options = ["日期降序", "日期升序", "标题A-Z", "标题Z-A", "分类A-Z"]
        
        # 创建对话框
        dialog = MDDialog()
        dialog.add_widget(
            MDDialogHeadlineText(
                text="选择排序方式",
            )
        )
        
        # 添加排序选项按钮的容器
        content = MDBoxLayout(
            orientation='vertical',
            spacing=dp(8),
            size_hint_y=None,
            height=len(sort_options) * dp(48),
            padding=[dp(16), dp(16), dp(16), dp(16)]
        )
        
        for option in sort_options:
            button = MDButton(
                style="outlined" if option != self.current_sort else "filled",
                size_hint_x=None,
                width=dp(120),
                on_release=lambda x, opt=option: self.apply_sort(opt, dialog)
            )
            button.add_widget(MDButtonText(text=option))
            content.add_widget(button)
        
        dialog.add_widget(MDDialogSupportingText(
            text=" ",  # 空文本，用于添加自定义内容
        ))
        dialog.children[0].children[0].add_widget(content)  # 将自定义内容添加到支持文本区域
        
        # 添加按钮
        button_container = MDDialogButtonContainer()
        
        # 关闭按钮
        close_button = MDButton(
            style="text",
            on_release=lambda x: dialog.dismiss()
        )
        close_button.add_widget(MDButtonText(text="关闭"))
        button_container.add_widget(close_button)
        
        dialog.add_widget(button_container)
        dialog.open()

    def apply_sort(self, sort_option, dialog=None):
        """应用排序"""
        self.current_sort = sort_option
        self.refresh_ui()
        if dialog:
            dialog.dismiss()
    
    def view_record(self, record_data):
        """查看记录详情"""
        self.show_record_detail_dialog(record_data)
    
    def show_record_detail_dialog(self, record_data):
        """显示记录详情对话框"""
        from kivymd.uix.dialog import MDDialogHeadlineText, MDDialogSupportingText, MDDialogButtonContainer
        
        # 创建对话框
        dialog = MDDialog()
        dialog.add_widget(
            MDDialogHeadlineText(
                text="记录详情",
            )
        )
        
        # 添加详情信息
        content = MDBoxLayout(
            orientation='vertical',
            spacing=dp(8),
            size_hint_y=None,
            height=dp(300),  # 初始高度，会根据内容调整
            padding=[dp(16), dp(16), dp(16), dp(16)]
        )
        
        details = [
            ("标题", record_data.get('title', '')),
            ("分类", record_data.get('category', '')),
            ("日期", record_data.get('date', '')),
            ("描述", record_data.get('description', '')),
            ("备注", record_data.get('notes', ''))
        ]
        
        total_height = 0
        for label, value in details:
            if value:
                item_height = self.get_text_height(value) + dp(24)
                total_height += item_height
                
                detail_box = MDBoxLayout(
                    orientation='vertical',
                    spacing=dp(4),
                    size_hint_y=None,
                    height=item_height
                )
                
                detail_box.add_widget(MDLabel(
                    text=f"{label}:",
                    bold=True,
                    theme_text_color="Primary",
                    size_hint_y=None,
                    height=dp(20)
                ))
                
                detail_box.add_widget(MDLabel(
                    text=value,
                    theme_text_color="Secondary",
                    size_hint_y=None,
                    height=self.get_text_height(value)
                ))
                
                content.add_widget(detail_box)
        
        content.height = total_height
        
        dialog.add_widget(MDDialogSupportingText(
            text=" ",  # 空文本，用于添加自定义内容
        ))
        dialog.children[0].children[0].add_widget(content)  # 将自定义内容添加到支持文本区域
        
        # 添加按钮
        button_container = MDDialogButtonContainer()
        
        # 关闭按钮
        close_button = MDButton(
            style="text",
            on_release=lambda x: dialog.dismiss()
        )
        close_button.add_widget(MDButtonText(text="关闭"))
        button_container.add_widget(close_button)
        
        # 编辑按钮
        edit_button = MDButton(
            style="filled",
            on_release=lambda x: self.edit_from_detail(record_data, dialog)
        )
        edit_button.add_widget(MDButtonText(text="编辑"))
        button_container.add_widget(edit_button)
        
        dialog.add_widget(button_container)
        dialog.open()

    def edit_from_detail(self, record_data, dialog=None):
        """从详情对话框编辑记录"""
        if dialog:
            dialog.dismiss()
        self.show_record_dialog(record_data)
    
    def get_text_height(self, text):
        """计算文本高度"""
        if not text:
            return dp(20)
        lines = text.count('\n') + 1
        return max(dp(20), lines * dp(20))
    
    def show_record_dialog(self, record_data=None):
        """显示记录对话框"""
        from kivymd.uix.dialog import MDDialogHeadlineText, MDDialogSupportingText, MDDialogButtonContainer
        
        # 创建对话框
        dialog = MDDialog()
        dialog.add_widget(
            MDDialogHeadlineText(
                text="编辑记录" if record_data else "添加记录",
            )
        )
        
        # 创建表单内容
        content = MDBoxLayout(
            orientation='vertical',
            spacing=dp(16),
            size_hint_y=None,
            height=dp(400),
            padding=[dp(16), dp(16), dp(16), dp(16)]
        )
        
        # 标题输入
        title_field = MDTextField(
            hint_text="记录标题",
            text=record_data.get('title', '') if record_data else '',
            mode="outlined",
            size_hint_y=None,
            height=dp(48)
        )
        content.add_widget(title_field)
        
        # 分类选择
        category_field = MDTextField(
            hint_text="记录分类（如：体检报告、化验单、影像资料等）",
            text=record_data.get('category', '') if record_data else '',
            mode="outlined",
            size_hint_y=None,
            height=dp(48)
        )
        content.add_widget(category_field)
        
        # 描述输入
        desc_field = MDTextField(
            hint_text="记录描述",
            text=record_data.get('description', '') if record_data else '',
            mode="outlined",
            multiline=True,
            size_hint_y=None,
            height=dp(120)
        )
        content.add_widget(desc_field)
        
        # 备注输入
        notes_field = MDTextField(
            hint_text="备注信息",
            text=record_data.get('notes', '') if record_data else '',
            mode="outlined",
            multiline=True,
            size_hint_y=None,
            height=dp(80)
        )
        content.add_widget(notes_field)
        
        dialog.add_widget(MDDialogSupportingText(
            text=" ",  # 空文本，用于添加自定义内容
        ))
        dialog.children[0].children[0].add_widget(content)  # 将自定义内容添加到支持文本区域
        
        # 添加按钮
        button_container = MDDialogButtonContainer()
        
        # 取消按钮
        cancel_button = MDButton(
            style="text",
            on_release=lambda x: dialog.dismiss()
        )
        cancel_button.add_widget(MDButtonText(text="取消"))
        button_container.add_widget(cancel_button)
        
        # 保存按钮
        save_button = MDButton(
            style="filled",
            on_release=lambda x: self.save_record(
                record_data, title_field.text, 
                category_field.text, desc_field.text, notes_field.text, dialog
            )
        )
        save_button.add_widget(MDButtonText(text="保存"))
        button_container.add_widget(save_button)
        
        dialog.add_widget(button_container)
        dialog.open()

    def save_record(self, record_data, title, category, description, notes, dialog=None):
        """保存记录"""
        if not title.strip():
            self.show_message("请输入记录标题")
            return
        
        if not category.strip():
            self.show_message("请输入记录分类")
            return
        
        # 获取图标
        icon = self.get_category_icon(category.strip())
        
        from datetime import datetime
        
        new_record = {
            "id": record_data.get('id') if record_data else str(len(self.records) + 1),
            "title": title.strip(),
            "category": category.strip(),
            "description": description.strip(),
            "notes": notes.strip(),
            "date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "icon": icon
        }
        
        if record_data:
            # 编辑现有记录
            for i, record in enumerate(self.records):
                if record.get('id') == record_data.get('id'):
                    self.records[i] = new_record
                    break
        else:
            # 添加新记录
            self.records.append(new_record)
        
        # 刷新UI
        self.refresh_ui()
        
        # 关闭对话框
        if dialog:
            dialog.dismiss()
        
        # 显示成功消息
        self.show_message("保存成功")
    
    def get_category_icon(self, category):
        """根据分类获取图标"""
        category_lower = category.lower()
        if "体检" in category_lower or "检查" in category_lower:
            return "clipboard-check"
        elif "化验" in category_lower or "检验" in category_lower:
            return "test-tube"
        elif "影像" in category_lower or "x光" in category_lower or "ct" in category_lower or "mri" in category_lower:
            return "image-multiple"
        elif "报告" in category_lower:
            return "file-document"
        elif "处方" in category_lower or "药物" in category_lower:
            return "pill"
        else:
            return "file-document"