<template>
  <div class="health-diary-tab">
    <div class="tab-header">
      <h3>健康日记</h3>
      <div class="action-buttons">
        <el-button type="primary" size="small" @click="openAddDialog" v-if="editable">添加日记</el-button>
        <el-button type="primary" size="small" @click="refreshData">刷新数据</el-button>
      </div>
    </div>

    <el-table
      :data="diaries"
      style="width: 100%"
      v-loading="loading"
      :empty-text="emptyText"
    >
      <el-table-column prop="diary_date" label="日期" width="120">
        <template #default="scope">
          {{ formatDate(scope.row.diary_date) }}
        </template>
      </el-table-column>
      <el-table-column prop="title" label="标题" width="200" />
      <el-table-column prop="mood" label="心情" width="100">
        <template #default="scope">
          {{ getMoodEmoji(scope.row.mood) }}
        </template>
      </el-table-column>
      <el-table-column prop="sleep_quality" label="睡眠质量" width="120">
        <template #default="scope">
          {{ getSleepQualityLabel(scope.row.sleep_quality) }}
        </template>
      </el-table-column>
      <el-table-column prop="diet_quality" label="饮食质量" width="120">
        <template #default="scope">
          {{ getDietQualityLabel(scope.row.diet_quality) }}
        </template>
      </el-table-column>
      <el-table-column prop="exercise_minutes" label="运动时间(分钟)" width="150" />
      <el-table-column prop="content" label="内容" show-overflow-tooltip />
      <el-table-column label="操作" width="180" fixed="right">
        <template #default="scope">
          <el-button type="primary" link @click="viewDiary(scope.row)">查看</el-button>
          <el-button type="success" link @click="editDiary(scope.row)" v-if="editable">编辑</el-button>
          <el-button type="danger" link @click="deleteDiary(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 日记详情对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      :title="currentDiary ? currentDiary.title : '健康日记详情'"
      width="60%"
    >
      <div v-if="currentDiary" class="diary-detail">
        <div class="diary-header">
          <div class="diary-date">{{ formatDate(currentDiary.diary_date) }}</div>
          <div class="diary-mood">心情: {{ getMoodEmoji(currentDiary.mood) }}</div>
        </div>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="睡眠质量">{{ getSleepQualityLabel(currentDiary.sleep_quality) }}</el-descriptions-item>
          <el-descriptions-item label="饮食质量">{{ getDietQualityLabel(currentDiary.diet_quality) }}</el-descriptions-item>
          <el-descriptions-item label="运动时间">{{ currentDiary.exercise_minutes }} 分钟</el-descriptions-item>
          <el-descriptions-item label="体重">{{ currentDiary.weight ? `${currentDiary.weight} kg` : '未记录' }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="diary-content">
          <h4>日记内容</h4>
          <div class="content-text">{{ currentDiary.content }}</div>
        </div>
        
        <div class="diary-notes" v-if="currentDiary.notes">
          <h4>备注</h4>
          <div class="notes-text">{{ currentDiary.notes }}</div>
        </div>
      </div>
    </el-dialog>

    <!-- 添加/编辑日记对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      :title="isEditing ? '编辑健康日记' : '添加健康日记'"
      width="60%"
    >
      <el-form :model="diaryForm" label-width="120px" :rules="rules" ref="diaryFormRef">
        <el-form-item label="日期" prop="diary_date">
          <el-date-picker v-model="diaryForm.diary_date" type="date" placeholder="选择日期" style="width: 100%" />
        </el-form-item>
        <el-form-item label="标题" prop="title">
          <el-input v-model="diaryForm.title" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item label="心情" prop="mood">
          <el-select v-model="diaryForm.mood" placeholder="请选择心情" style="width: 100%">
            <el-option label="很好 😄" value="excellent" />
            <el-option label="良好 🙂" value="good" />
            <el-option label="一般 😐" value="fair" />
            <el-option label="不佳 😔" value="poor" />
            <el-option label="糟糕 😞" value="bad" />
          </el-select>
        </el-form-item>
        <el-form-item label="睡眠质量" prop="sleep_quality">
          <el-select v-model="diaryForm.sleep_quality" placeholder="请选择睡眠质量" style="width: 100%">
            <el-option label="很好" value="excellent" />
            <el-option label="良好" value="good" />
            <el-option label="一般" value="fair" />
            <el-option label="不佳" value="poor" />
            <el-option label="糟糕" value="bad" />
          </el-select>
        </el-form-item>
        <el-form-item label="饮食质量" prop="diet_quality">
          <el-select v-model="diaryForm.diet_quality" placeholder="请选择饮食质量" style="width: 100%">
            <el-option label="很好" value="excellent" />
            <el-option label="良好" value="good" />
            <el-option label="一般" value="fair" />
            <el-option label="不佳" value="poor" />
            <el-option label="糟糕" value="bad" />
          </el-select>
        </el-form-item>
        <el-form-item label="运动时间(分钟)" prop="exercise_minutes">
          <el-input-number v-model="diaryForm.exercise_minutes" :min="0" :max="1440" />
        </el-form-item>
        <el-form-item label="体重(kg)">
          <el-input-number v-model="diaryForm.weight" :min="0" :max="500" :precision="1" />
        </el-form-item>
        <el-form-item label="日记内容" prop="content">
          <el-input v-model="diaryForm.content" type="textarea" rows="6" placeholder="请输入日记内容" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="diaryForm.notes" type="textarea" rows="2" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveDiary">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import axios from 'axios';

const props = defineProps({
  customId: {
    type: [Number, String],
    required: true
  },
  editable: {
    type: Boolean,
    default: false
  }
});

const loading = ref(false);
const diaries = ref([]);
const viewDialogVisible = ref(false);
const editDialogVisible = ref(false);
const currentDiary = ref(null);
const isEditing = ref(false);
const diaryFormRef = ref(null);

const diaryForm = ref({
  diary_date: '',
  title: '',
  mood: '',
  sleep_quality: '',
  diet_quality: '',
  exercise_minutes: 0,
  weight: null,
  content: '',
  notes: ''
});

const rules = {
  diary_date: [
    { required: true, message: '请选择日期', trigger: 'change' }
  ],
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' }
  ],
  mood: [
    { required: true, message: '请选择心情', trigger: 'change' }
  ],
  sleep_quality: [
    { required: true, message: '请选择睡眠质量', trigger: 'change' }
  ],
  diet_quality: [
    { required: true, message: '请选择饮食质量', trigger: 'change' }
  ],
  exercise_minutes: [
    { required: true, message: '请输入运动时间', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入日记内容', trigger: 'blur' }
  ]
};

const emptyText = computed(() => {
  return loading.value ? '加载中...' : '暂无健康日记数据';
});

// 统一聚合接口请求
const fetchUserHealthRecords = async () => {
  if (!props.customId) return;
  loading.value = true;
  try {
    const response = await axios.get(`/api/v1/aggregated/health-profile/${props.customId}`, {
      params: {
        include_health_diaries: true
      }
    });
    const profileData = response.data.profile_data || {};
    diaries.value = profileData.health_diaries || [];
  } catch (error) {
    console.error('获取健康日记失败:', error);
    diaries.value = [];
  } finally {
    loading.value = false;
  }
};

watch(() => props.customId, (newVal) => {
  if (newVal) {
    fetchUserHealthRecords();
  } else {
    diaries.value = [];
  }
}, { immediate: true });

onMounted(() => {
  if (props.customId) {
    fetchUserHealthRecords();
  }
});



// 刷新数据
const refreshData = () => {
  fetchUserHealthRecords();
};

// 查看日记详情
const viewDiary = (diary) => {
  currentDiary.value = diary;
  viewDialogVisible.value = true;
};

// 打开添加日记对话框
const openAddDialog = () => {
  isEditing.value = false;
  diaryForm.value = {
    diary_date: new Date(),
    title: '',
    mood: '',
    sleep_quality: '',
    diet_quality: '',
    exercise_minutes: 0,
    weight: null,
    content: '',
    notes: ''
  };
  editDialogVisible.value = true;
};

// 编辑日记
const editDiary = (diary) => {
  isEditing.value = true;
  diaryForm.value = { ...diary };
  
  // 转换日期字符串为Date对象
  if (diaryForm.value.diary_date) {
    diaryForm.value.diary_date = new Date(diaryForm.value.diary_date);
  }
  
  editDialogVisible.value = true;
};

// 保存日记
const saveDiary = async () => {
  if (!diaryFormRef.value) return;
  
  await diaryFormRef.value.validate(async (valid) => {
    if (!valid) {
      ElMessage.warning('请完善表单信息');
      return;
    }
    
    try {
      const formData = { ...diaryForm.value };
      formData.custom_id = props.customId;
      
      // 转换日期对象为ISO字符串
      if (formData.diary_date instanceof Date) {
        formData.diary_date = formData.diary_date.toISOString().split('T')[0];
      }
      
      if (isEditing.value) {
        // 更新现有记录
        await axios.put(`/api/health-diaries/${formData.id}`, formData);
        ElMessage.success('健康日记更新成功');
      } else {
        // 添加新记录
        await axios.post('/api/health-diaries', formData);
        ElMessage.success('健康日记添加成功');
      }
      
      editDialogVisible.value = false;
      fetchUserHealthRecords(); // 刷新数据
      
      // 模拟保存成功（实际项目中应删除）
      if (isEditing.value) {
        const index = diaries.value.findIndex(item => item.id === formData.id);
        if (index !== -1) {
          diaries.value[index] = formData;
        }
      } else {
        formData.id = Date.now(); // 模拟生成ID
        diaries.value.push(formData);
      }
    } catch (error) {
      console.error('保存健康日记失败:', error);
      ElMessage.error('保存健康日记失败，请稍后重试');
    }
  });
};

// 删除日记
const deleteDiary = (diary) => {
  ElMessageBox.confirm(
    `确定要删除 ${formatDate(diary.diary_date)} 的健康日记吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await axios.delete(`/api/health-diaries/${diary.id}`);
      ElMessage.success('删除成功');
      fetchUserHealthRecords(); // 刷新数据
    } catch (error) {
      console.error('删除健康日记失败:', error);
      ElMessage.error('删除健康日记失败，请稍后重试');
      
      // 模拟删除成功（实际项目中应删除）
      diaries.value = diaries.value.filter(item => item.id !== diary.id);
      ElMessage.success('删除成功');
    }
  }).catch(() => {
    // 用户取消删除
  });
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  return date.toLocaleDateString();
};

// 获取心情表情
const getMoodEmoji = (mood) => {
  const moodMap = {
    'excellent': '很好 😄',
    'good': '良好 🙂',
    'fair': '一般 😐',
    'poor': '不佳 😔',
    'bad': '糟糕 😞'
  };
  
  return moodMap[mood] || mood;
};

// 获取睡眠质量标签
const getSleepQualityLabel = (quality) => {
  const qualityMap = {
    'excellent': '很好',
    'good': '良好',
    'fair': '一般',
    'poor': '不佳',
    'bad': '糟糕'
  };
  
  return qualityMap[quality] || quality;
};

// 获取饮食质量标签
const getDietQualityLabel = (quality) => {
  const qualityMap = {
    'excellent': '很好',
    'good': '良好',
    'fair': '一般',
    'poor': '不佳',
    'bad': '糟糕'
  };
  
  return qualityMap[quality] || quality;
};
</script>

<style scoped>
.health-diary-tab {
  padding: 10px;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.tab-header h3 {
  margin: 0;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.diary-detail {
  padding: 10px;
}

.diary-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.diary-date {
  font-size: 16px;
  font-weight: bold;
}

.diary-mood {
  font-size: 16px;
}

.diary-content,
.diary-notes {
  margin-top: 20px;
}

.diary-content h4,
.diary-notes h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #303133;
  font-size: 16px;
}

.content-text,
.notes-text {
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  white-space: pre-line;
}
</style>
