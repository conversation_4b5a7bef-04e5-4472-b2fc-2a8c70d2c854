#!/usr/bin/env python3
import sqlite3
import os

# 检查多个可能的数据库文件
db_paths = [
    "c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend\\app.db"
]

for db_path in db_paths:
    print(f"\n=== 检查数据库: {db_path} ===")
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在")
        continue
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"数据库中共有 {len(tables)} 个表:")
        for table in tables:
            print(f"  - {table[0]}")
        
        # 重点检查用户相关的表
        important_tables = ['users', 'assessments', 'questionnaires', 'assessment_templates', 'medications']
        
        for table_name in important_tables:
            if any(t[0] == table_name for t in tables):
                print(f"\n=== 表: {table_name} ===")
                
                # 获取表结构
                cursor.execute(f"PRAGMA table_info({table_name});")
                columns = cursor.fetchall()
                print("列信息:")
                for col in columns:
                    print(f"  {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'} - {'PK' if col[5] else ''}")
                
                # 获取数据量
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
                    count = cursor.fetchone()[0]
                    print(f"数据量: {count} 条记录")
                    
                    # 如果是用户表，检查SM_006用户
                    if table_name == 'users':
                        cursor.execute("SELECT id, custom_id, username, is_active FROM users WHERE custom_id='SM_008';")
                        user = cursor.fetchone()
                        if user:
                            print(f"✓ 用户SM_008存在: ID={user[0]}, custom_id={user[1]}, username={user[2]}, is_active={user[3]}")
                        else:
                            print("✗ 用户SM_008不存在")
                            # 显示所有用户
                            cursor.execute("SELECT custom_id, username FROM users LIMIT 10;")
                            all_users = cursor.fetchall()
                            print("现有用户:")
                            for u in all_users:
                                print(f"  {u[0]} - {u[1]}")
                    
                    # 如果是评估表，检查SM_006的数据
                    elif table_name == 'assessments':
                        cursor.execute("SELECT COUNT(*) FROM assessments WHERE custom_id='SM_008';")
                        sm006_count = cursor.fetchone()[0]
                        print(f"用户SM_008的评估记录数: {sm006_count}")
                        
                        if sm006_count > 0:
                            cursor.execute("""
                                SELECT id, name, assessment_type, status, created_at, score 
                                FROM assessments 
                                WHERE custom_id='SM_008' 
                                ORDER BY created_at DESC 
                                LIMIT 3
                            """)
                            records = cursor.fetchall()
                            print("SM_006的评估记录:")
                            for record in records:
                                print(f"  ID: {record[0]}, 名称: {record[1]}, 类型: {record[2]}, 状态: {record[3]}, 创建时间: {record[4]}, 得分: {record[5]}")
                    
                    # 如果是问卷表，检查SM_006的数据
                    elif table_name == 'questionnaires':
                        cursor.execute("SELECT COUNT(*) FROM questionnaires WHERE custom_id='SM_008';")
                        sm006_count = cursor.fetchone()[0]
                        print(f"用户SM_006的问卷记录数: {sm008_count}")
                        
                        if sm006_count > 0:
                            cursor.execute("""
                                SELECT id, title, questionnaire_type, status, created_at, score 
                                FROM questionnaires 
                                WHERE custom_id='SM_008' 
                                ORDER BY created_at DESC 
                                LIMIT 3
                            """)
                            records = cursor.fetchall()
                            print("SM_008的问卷记录:")
                            for record in records:
                                print(f"  ID: {record[0]}, 标题: {record[1]}, 类型: {record[2]}, 状态: {record[3]}, 创建时间: {record[4]}, 得分: {record[5]}")
                    
                    # 如果数据量不多，显示示例数据
                    elif count > 0 and count <= 5:
                        cursor.execute(f"SELECT * FROM {table_name} LIMIT 3;")
                        records = cursor.fetchall()
                        print("示例数据:")
                        for i, record in enumerate(records):
                            print(f"  记录{i+1}: {record}")
                            
                except Exception as e:
                    print(f"查询数据时出错: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"检查数据库时出错: {e}")

print("\n=== 所有数据库检查完成 ===")