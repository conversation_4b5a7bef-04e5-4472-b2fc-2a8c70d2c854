#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的移动端文件上传功能
验证移动端使用正确的API端点
"""

import requests
import json
import os
import tempfile
from datetime import datetime

# 测试配置
BASE_URL = "http://localhost:8006"
TEST_USER = {
    "username": "admin",
    "password": "admin123"
}

def create_test_file(filename, content, file_type="text/plain"):
    """创建测试文件"""
    temp_dir = tempfile.gettempdir()
    file_path = os.path.join(temp_dir, filename)
    
    if isinstance(content, str):
        content = content.encode('utf-8')
    
    with open(file_path, 'wb') as f:
        f.write(content)
    
    return file_path

def test_mobile_upload_fix():
    """测试修复后的移动端文件上传功能"""
    print("=== 测试修复后的移动端文件上传功能 ===")
    
    # 1. 用户登录
    print("\n[1] 用户登录...")
    login_url = f"{BASE_URL}/api/auth/register/login"
    login_data = {
        "username": TEST_USER["username"],
        "password": TEST_USER["password"]
    }
    
    try:
        login_response = requests.post(login_url, json=login_data)
        print(f"登录状态码: {login_response.status_code}")
        
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.text}")
            return False
        
        login_result = login_response.json()
        if login_result.get("status") != "success":
            print(f"❌ 登录失败: {login_result.get('message')}")
            return False
        
        access_token = login_result.get("access_token")
        print(f"✅ 登录成功，获取到token: {access_token[:50]}...")
        
    except Exception as e:
        print(f"❌ 登录异常: {str(e)}")
        return False
    
    # 2. 测试文件上传（使用修复后的端点）
    print("\n[2] 测试文件上传（使用修复后的端点 /api/mobile/upload）...")
    upload_url = f"{BASE_URL}/api/mobile/upload"
    
    # 创建测试文件
    test_content = f"移动端文件上传测试 - {datetime.now().isoformat()}"
    test_file_path = create_test_file("mobile_test.txt", test_content)
    
    try:
        # 准备上传数据
        with open(test_file_path, 'rb') as f:
            files = {
                'file': ('mobile_test.txt', f, 'text/plain')
            }
            
            data = {
                'title': '移动端测试文件',
                'document_type': 'test',
                'description': '测试修复后的移动端文件上传功能',
                'category': 'test'
            }
            
            headers = {
                'Authorization': f'Bearer {access_token}'
            }
            
            upload_response = requests.post(upload_url, files=files, data=data, headers=headers)
            print(f"上传状态码: {upload_response.status_code}")
            
            if upload_response.status_code == 200:
                upload_result = upload_response.json()
                if upload_result.get("status") == "success":
                    print("✅ 文件上传成功！")
                    print(f"   文档ID: {upload_result.get('data', {}).get('document_id')}")
                    print(f"   文件名: {upload_result.get('data', {}).get('filename')}")
                    print(f"   文件大小: {upload_result.get('data', {}).get('file_size')} bytes")
                    print(f"   状态: {upload_result.get('data', {}).get('status')}")
                    return True
                else:
                    print(f"❌ 上传失败: {upload_result.get('message')}")
                    return False
            else:
                print(f"❌ 上传失败，状态码: {upload_response.status_code}")
                try:
                    error_detail = upload_response.json()
                    print(f"   错误详情: {error_detail}")
                except:
                    print(f"   错误响应: {upload_response.text}")
                return False
                
    except Exception as e:
        print(f"❌ 上传异常: {str(e)}")
        return False
    finally:
        # 清理测试文件
        try:
            os.remove(test_file_path)
        except:
            pass

def test_old_endpoint_behavior():
    """测试旧端点的行为（应该重定向或失败）"""
    print("\n=== 测试旧端点行为 ===")
    
    # 登录获取token
    login_url = f"{BASE_URL}/api/auth/register/login"
    login_data = {
        "username": TEST_USER["username"],
        "password": TEST_USER["password"]
    }
    
    try:
        login_response = requests.post(login_url, json=login_data)
        if login_response.status_code == 200:
            login_result = login_response.json()
            if login_result.get("status") == "success":
                access_token = login_result.get("access_token")
                
                # 测试旧端点
                old_upload_url = f"{BASE_URL}/api/documents/upload"
                test_content = "测试旧端点"
                test_file_path = create_test_file("old_test.txt", test_content)
                
                with open(test_file_path, 'rb') as f:
                    files = {
                        'file': ('old_test.txt', f, 'text/plain')
                    }
                    
                    headers = {
                        'Authorization': f'Bearer {access_token}'
                    }
                    
                    old_response = requests.post(old_upload_url, files=files, headers=headers)
                    print(f"旧端点状态码: {old_response.status_code}")
                    print(f"旧端点响应: {old_response.text[:200]}...")
                
                os.remove(test_file_path)
                
    except Exception as e:
        print(f"测试旧端点异常: {str(e)}")

if __name__ == "__main__":
    success = test_mobile_upload_fix()
    test_old_endpoint_behavior()
    
    if success:
        print("\n🎉 移动端文件上传功能修复验证成功！")
    else:
        print("\n❌ 移动端文件上传功能仍有问题，需要进一步调试。")