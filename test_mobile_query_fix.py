#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的移动端文档查询功能
验证上传的文档能在移动端查询中显示
"""

import requests
import json
from datetime import datetime

# 测试配置
BASE_URL = "http://localhost:8006"
TEST_USER = {
    "username": "admin",
    "password": "admin123"
}

def test_mobile_query_fix():
    """测试修复后的移动端文档查询功能"""
    print("=== 测试修复后的移动端文档查询功能 ===")
    
    # 1. 用户登录
    print("\n[1] 用户登录...")
    login_url = f"{BASE_URL}/api/auth/register/login"
    login_data = {
        "username": TEST_USER["username"],
        "password": TEST_USER["password"]
    }
    
    try:
        login_response = requests.post(login_url, json=login_data)
        print(f"登录状态码: {login_response.status_code}")
        
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.text}")
            return False
        
        login_result = login_response.json()
        if login_result.get("status") != "success":
            print(f"❌ 登录失败: {login_result.get('message')}")
            return False
        
        access_token = login_result.get("access_token")
        user_info = login_result.get("user", {})
        custom_id = user_info.get("custom_id")
        
        print(f"✅ 登录成功，获取到token: {access_token[:50]}...")
        print(f"✅ 用户custom_id: {custom_id}")
        
    except Exception as e:
        print(f"❌ 登录异常: {str(e)}")
        return False
    
    # 2. 测试user-health-records端点查询
    print(f"\n[2] 测试user-health-records端点查询（custom_id: {custom_id}）...")
    query_url = f"{BASE_URL}/api/user-health-records/user/{custom_id}"
    
    headers = {
        'Authorization': f'Bearer {access_token}',
        'X-User-ID': str(custom_id)
    }
    
    params = {
        'page': 1,
        'page_size': 20,
        'custom_id': custom_id
    }
    
    try:
        query_response = requests.get(query_url, headers=headers, params=params)
        print(f"查询状态码: {query_response.status_code}")
        print(f"查询响应: {query_response.text[:500]}...")
        
        if query_response.status_code == 200:
            query_result = query_response.json()
            
            # 检查不同的响应格式
            if query_result.get("status") == "success":
                data = query_result.get("data", {})
                records = data.get("records", [])
                total = data.get("total", 0)
                
                print(f"✅ 查询成功，共找到 {total} 条健康记录")
                
                # 查找文档类型的记录
                document_records = [r for r in records if r.get("record_type") == "document"]
                print(f"✅ 其中文档类型记录: {len(document_records)} 条")
                
                if document_records:
                    print("\n📄 文档记录详情:")
                    for i, record in enumerate(document_records[:3], 1):  # 只显示前3条
                        print(f"  [{i}] ID: {record.get('id')}")
                        print(f"      标题: {record.get('title')}")
                        print(f"      类型: {record.get('record_type')}")
                        print(f"      状态: {record.get('status')}")
                        print(f"      创建时间: {record.get('created_at')}")
                        
                        # 解析content中的文档信息
                        content = record.get('content')
                        if content:
                            try:
                                content_data = json.loads(content)
                                print(f"      关联文档ID: {content_data.get('document_id')}")
                                print(f"      文件名: {content_data.get('filename')}")
                                print(f"      来源: {content_data.get('source')}")
                            except:
                                print(f"      内容: {content[:100]}...")
                        print()
                    
                    return True
                else:
                    print("❌ 未找到文档类型的健康记录")
                    return False
            else:
                print(f"❌ 查询失败: {query_result.get('message')}")
                return False
        else:
            print(f"❌ 查询请求失败: {query_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 查询异常: {str(e)}")
        return False
    
    # 3. 测试通用documents端点查询（对比）
    print("\n[3] 测试通用documents端点查询（对比）...")
    docs_url = f"{BASE_URL}/api/documents"
    
    try:
        docs_response = requests.get(docs_url, headers=headers, params={'custom_id': custom_id})
        print(f"Documents端点状态码: {docs_response.status_code}")
        
        if docs_response.status_code == 200:
            docs_result = docs_response.json()
            if docs_result.get("status") == "success":
                data = docs_result.get("data", {})
                documents = data.get("documents", [])
                total = data.get("total", 0)
                
                print(f"✅ Documents端点查询成功，共找到 {total} 个文档")
                
                if documents:
                    print("\n📄 文档列表:")
                    for i, doc in enumerate(documents[:3], 1):  # 只显示前3条
                        print(f"  [{i}] ID: {doc.get('id')}")
                        print(f"      标题: {doc.get('title')}")
                        print(f"      文件名: {doc.get('filename')}")
                        print(f"      状态: {doc.get('status')}")
                        print(f"      来源: {doc.get('source')}")
                        print(f"      创建时间: {doc.get('created_at')}")
                        print()
            else:
                print(f"❌ Documents端点查询失败: {docs_result.get('message')}")
        else:
            print(f"❌ Documents端点请求失败: {docs_response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ Documents端点查询异常: {str(e)}")

def test_mobile_api_compatibility():
    """测试移动端API兼容性"""
    print("\n=== 测试移动端API兼容性 ===")
    
    # 测试移动端API路径
    mobile_endpoints = [
        "/api/mobile/upload",
        "/api/user-health-records",
        "/api/documents"
    ]
    
    for endpoint in mobile_endpoints:
        url = f"{BASE_URL}{endpoint}"
        try:
            response = requests.get(url)
            print(f"📡 {endpoint}: 状态码 {response.status_code}")
        except Exception as e:
            print(f"❌ {endpoint}: 连接失败 - {str(e)}")

if __name__ == "__main__":
    success = test_mobile_query_fix()
    test_mobile_api_compatibility()
    
    if success:
        print("\n🎉 移动端文档查询功能修复验证成功！")
        print("✅ 上传的文档现在可以在移动端查询中正常显示")
    else:
        print("\n❌ 移动端文档查询功能仍有问题，需要进一步调试。")