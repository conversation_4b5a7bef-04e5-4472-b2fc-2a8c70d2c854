# -*- coding: utf-8 -*-
"""
告警API路由
"""
from typing import Any, Dict, List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Body, Query, Path, status
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from app.db.base_session import get_db
from app.models.user import User
from app.models.alert import Alert, AlertRule, AlertChannel, AlertSeverity, AlertStatus
from app.models.enums import UserRole
from app.api import deps
from app.core.auth import get_current_active_user_custom
from app.core.alert_manager import alert_manager

router = APIRouter()

# 请求模型
class AlertRuleRequest(BaseModel):
    """告警规则请求模型"""
    threshold: float = Field(..., description="阈值")
    duration: int = Field(..., description="持续时间（秒）")
    level: str = Field(..., description="告警级别")
    description: str = Field(..., description="告警描述")
    enabled: bool = Field(..., description="是否启用")

class NotificationChannelRequest(BaseModel):
    """通知渠道请求模型"""
    type: str = Field(..., description="渠道类型")
    recipients: List[str] = Field(default=[], description="接收者列表")
    url: Optional[str] = Field(None, description="Webhook URL")
    enabled: bool = Field(..., description="是否启用")

# 响应模型
class AlertResponse(BaseModel):
    """告警响应模型"""
    id: Optional[str] = Field(None, description="告警ID")
    rule_id: Optional[str] = Field(None, description="规则ID")
    level: Optional[str] = Field(None, description="告警级别")
    description: Optional[str] = Field(None, description="告警描述")
    value: Optional[float] = Field(None, description="当前值")
    threshold: Optional[float] = Field(None, description="阈值")
    timestamp: Optional[str] = Field(None, description="时间戳")
    resolved: Optional[bool] = Field(None, description="是否已解决")
    resolved_at: Optional[str] = Field(None, description="解决时间")

class AlertRuleResponse(BaseModel):
    """告警规则响应模型"""
    id: str = Field(..., description="规则ID")
    threshold: float = Field(..., description="阈值")
    duration: int = Field(..., description="持续时间（秒）")
    level: str = Field(..., description="告警级别")
    description: str = Field(..., description="告警描述")
    enabled: bool = Field(..., description="是否启用")

class NotificationChannelResponse(BaseModel):
    """通知渠道响应模型"""
    id: str = Field(..., description="渠道ID")
    type: str = Field(..., description="渠道类型")
    recipients: List[str] = Field(default=[], description="接收者列表")
    url: Optional[str] = Field(None, description="Webhook URL")
    enabled: bool = Field(..., description="是否启用")

@router.get("/", response_model=List[AlertResponse])
def get_alerts(
    limit: int = Query(100, ge=1, le=1000),
    offset: int = Query(0, ge=0),
    level: Optional[str] = Query(None),
    resolved: Optional[bool] = Query(None),
    current_user: User = Depends(get_current_active_user_custom)
) -> Any:
    """
    获取告警列表
    """
    def fill_alert_fields(alert):
        # 保证所有字段都存在
        return {
            "id": alert.get("id"),
            "rule_id": alert.get("rule_id"),
            "level": alert.get("level"),
            "description": alert.get("description"),
            "value": alert.get("value"),
            "threshold": alert.get("threshold"),
            "timestamp": alert.get("timestamp"),
            "resolved": alert.get("resolved"),
            "resolved_at": alert.get("resolved_at"),
        }

    try:
        # 先检查用户权限，对非管理员显示友好提示而不是直接返回空列表
        if not (current_user.role == UserRole.ADMIN.value or
                current_user.role == UserRole.SUPER_ADMIN.value or
                current_user.role == UserRole.UNIT_ADMIN.value):
            # 返回一个模拟告警，提示用户权限不足
            return [fill_alert_fields({
                "id": "permission_notice",
                "rule_id": "system_notice",
                "level": "info",
                "description": "需要管理员权限查看告警信息",
                "value": 0,
                "threshold": 0,
                "timestamp": alert_manager.get_current_timestamp(),
                "resolved": False,
                "resolved_at": None
            })]

        # 获取真实告警列表
        try:
            # 导入并使用告警检测器获取真实告警数据
            from app.core.alert_detector import alert_detector
            
            # 获取当前告警
            alerts_data = alert_detector.get_current_alerts()
            
            # 过滤条件
            if level:
                alerts_data = [a for a in alerts_data if a.get("level") == level]
            if resolved is not None:
                alerts_data = [a for a in alerts_data if a.get("resolved") == resolved]
            
            # 应用分页
            total_alerts = len(alerts_data)
            alerts_data = alerts_data[offset:offset+limit]
            
            # 如果有告警，返回实际告警数据
            if alerts_data:
                return [fill_alert_fields(a) for a in alerts_data]
        except ImportError:
            import logging
            logging.error("无法导入告警检测器模块")
        except Exception as e:
            import logging
            logging.error(f"从告警检测器获取数据时出错: {str(e)}")

        # 如果通过告警检测器获取失败，回退到告警管理器
        alerts = alert_manager.get_alerts(limit, offset, level, resolved)

        # 如果告警列表为空，添加一个提示信息
        if not alerts:
            # 尝试从系统监控获取真实系统状态
            try:
                from app.core.system_monitor import system_monitor
                metrics = system_monitor.get_current_metrics()
                
                cpu_percent = metrics.get("system", {}).get("cpu_percent", 0)
                memory_percent = metrics.get("system", {}).get("memory_percent", 0)
                disk_percent = metrics.get("system", {}).get("disk_percent", 0)
                
                # 创建基于真实数据的信息性告警
                alerts = [{
                    "id": "system_status",
                    "rule_id": "system_notice",
                    "level": "info",
                    "description": f"系统状态正常 (CPU: {cpu_percent:.1f}%, 内存: {memory_percent:.1f}%, 磁盘: {disk_percent:.1f}%)",
                    "value": 0,
                    "threshold": 0,
                    "timestamp": alert_manager.get_current_timestamp(),
                    "resolved": True,
                    "resolved_at": None
                }]
            except Exception:
                # 如果无法获取系统监控数据，返回默认提示
                alerts = [{
                    "id": "no_alerts",
                    "rule_id": "system_notice",
                    "level": "info",
                    "description": "当前没有告警信息",
                    "value": 0,
                    "threshold": 0,
                    "timestamp": alert_manager.get_current_timestamp(),
                    "resolved": True,
                    "resolved_at": None
                }]

        return [fill_alert_fields(a) for a in alerts]
    except Exception as e:
        # 记录错误但返回友好的错误信息，而不是空列表
        import logging
        logging.error(f"获取告警列表出错: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())

        # 返回一个错误告警
        return [fill_alert_fields({
            "id": "error_notice",
            "rule_id": "system_error",
            "level": "error",
            "description": f"获取告警信息时出错: {str(e)}",
            "value": 0,
            "threshold": 0,
            "timestamp": alert_manager.get_current_timestamp(),
            "resolved": False,
            "resolved_at": None
        })]

@router.post("/resolve/{alert_id}", response_model=Dict[str, Any])
def resolve_alert(
    alert_id: str = Path(...),
    current_user: User = Depends(get_current_active_user_custom)
) -> Any:
    """
    解决告警
    """
    # 检查用户权限
    if not (current_user.role == UserRole.ADMIN.value or
            current_user.role == UserRole.SUPER_ADMIN.value or
            current_user.role == UserRole.UNIT_ADMIN.value):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限"
        )

    # 解决告警
    success = alert_manager.resolve_alert(alert_id)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="告警不存在"
        )

    return {
        "success": True,
        "message": "告警已解决"
    }

# 基于数据库的告警记录API
@router.get("/user/{custom_id}/database", response_model=Dict[str, Any])
def get_user_alerts_from_db(
    *,
    db: Session = Depends(get_db),
    custom_id: str = Path(..., description="用户ID"),
    severity: Optional[str] = Query(None, description="告警级别"),
    status: Optional[str] = Query(None, description="告警状态"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    获取指定用户的数据库告警记录列表
    """
    # 查找用户
    user = db.query(User).filter(User.custom_id == custom_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到用户ID: {custom_id}"
        )

    # 权限校验
    if current_user.custom_id != custom_id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限访问此用户的告警记录"
        )

    # 构建查询
    query = db.query(Alert).filter(Alert.custom_id == custom_id)
    
    if severity:
        query = query.filter(Alert.severity == severity)
    if status:
        query = query.filter(Alert.status == status)
    if start_date:
        query = query.filter(Alert.created_at >= start_date)
    if end_date:
        query = query.filter(Alert.created_at <= end_date)
    
    # 分页
    alerts = query.offset(skip).limit(limit).all()
    total = query.count()
    
    return {
        "success": True,
        "data": [
            {
                "id": alert.id,
                "custom_id": alert.custom_id,
                "title": alert.title,
                "description": alert.description,
                "severity": alert.severity.value if alert.severity else None,
                "status": alert.status.value if alert.status else None,
                "rule_id": alert.rule_id,
                "triggered_at": alert.triggered_at,
                "resolved_at": alert.resolved_at,
                "created_at": alert.created_at,
                "updated_at": alert.updated_at
            }
            for alert in alerts
        ],
        "total": total,
        "skip": skip,
        "limit": limit
    }


@router.post("/database", response_model=Dict[str, Any], status_code=status.HTTP_201_CREATED)
def create_alert_in_db(
    *,
    db: Session = Depends(get_db),
    alert_data: dict,
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    在数据库中创建告警记录
    """
    # 验证用户存在
    custom_id = alert_data.get("custom_id")
    user = db.query(User).filter(User.custom_id == custom_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到用户ID: {custom_id}"
        )

    # 权限校验
    if current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限创建告警记录"
        )

    # 创建告警记录
    alert = Alert(
        custom_id=custom_id,
        title=alert_data.get("title"),
        description=alert_data.get("description"),
        severity=AlertSeverity(alert_data.get("severity")) if alert_data.get("severity") else None,
        status=AlertStatus(alert_data.get("status", "active")),
        rule_id=alert_data.get("rule_id"),
        triggered_at=alert_data.get("triggered_at", datetime.utcnow())
    )
    
    db.add(alert)
    db.commit()
    db.refresh(alert)
    
    return {
        "success": True,
        "data": {
            "id": alert.id,
            "custom_id": alert.custom_id,
            "title": alert.title,
            "description": alert.description,
            "severity": alert.severity.value if alert.severity else None,
            "status": alert.status.value if alert.status else None,
            "rule_id": alert.rule_id,
            "triggered_at": alert.triggered_at,
            "resolved_at": alert.resolved_at,
            "created_at": alert.created_at,
            "updated_at": alert.updated_at
        }
    }


@router.get("/database/{alert_id}", response_model=Dict[str, Any])
def get_alert_from_db(
    *,
    db: Session = Depends(get_db),
    alert_id: int = Path(..., description="告警ID"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    从数据库获取单个告警记录详情
    """
    alert = db.query(Alert).filter(Alert.id == alert_id).first()
    if not alert:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到告警记录ID: {alert_id}"
        )

    # 权限校验
    if current_user.custom_id != alert.custom_id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限访问此告警记录"
        )

    return {
        "success": True,
        "data": {
            "id": alert.id,
            "custom_id": alert.custom_id,
            "title": alert.title,
            "description": alert.description,
            "severity": alert.severity.value if alert.severity else None,
            "status": alert.status.value if alert.status else None,
            "rule_id": alert.rule_id,
            "triggered_at": alert.triggered_at,
            "resolved_at": alert.resolved_at,
            "created_at": alert.created_at,
            "updated_at": alert.updated_at
        }
    }


@router.put("/database/{alert_id}", response_model=Dict[str, Any])
def update_alert_in_db(
    *,
    db: Session = Depends(get_db),
    alert_id: int = Path(..., description="告警ID"),
    alert_data: dict,
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    更新数据库中的告警记录
    """
    alert = db.query(Alert).filter(Alert.id == alert_id).first()
    if not alert:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到告警记录ID: {alert_id}"
        )

    # 权限校验
    if current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限修改告警记录"
        )

    # 更新字段
    for field, value in alert_data.items():
        if field == "severity" and value:
            setattr(alert, field, AlertSeverity(value))
        elif field == "status" and value:
            setattr(alert, field, AlertStatus(value))
            if value == "resolved" and not alert.resolved_at:
                alert.resolved_at = datetime.utcnow()
        elif hasattr(alert, field) and field != "id":
            setattr(alert, field, value)
    
    db.commit()
    db.refresh(alert)
    
    return {
        "success": True,
        "data": {
            "id": alert.id,
            "custom_id": alert.custom_id,
            "title": alert.title,
            "description": alert.description,
            "severity": alert.severity.value if alert.severity else None,
            "status": alert.status.value if alert.status else None,
            "rule_id": alert.rule_id,
            "triggered_at": alert.triggered_at,
            "resolved_at": alert.resolved_at,
            "created_at": alert.created_at,
            "updated_at": alert.updated_at
        }
    }


@router.delete("/database/{alert_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_alert_from_db(
    *,
    db: Session = Depends(get_db),
    alert_id: int = Path(..., description="告警ID"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> None:
    """
    从数据库删除告警记录
    """
    alert = db.query(Alert).filter(Alert.id == alert_id).first()
    if not alert:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到告警记录ID: {alert_id}"
        )

    # 权限校验
    if current_user.role not in ["admin", "super_admin", "unit_admin"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限删除告警记录"
        )

    db.delete(alert)
    db.commit()


# 告警规则数据库API
@router.get("/rules/database", response_model=Dict[str, Any])
def get_alert_rules_from_db(
    *,
    db: Session = Depends(get_db),
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    从数据库获取告警规则列表
    """
    # 权限校验
    if current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限访问告警规则"
        )

    rules = db.query(AlertRule).offset(skip).limit(limit).all()
    total = db.query(AlertRule).count()
    
    return {
        "success": True,
        "data": [
            {
                "id": rule.id,
                "name": rule.name,
                "description": rule.description,
                "condition": rule.condition,
                "severity": rule.severity.value if rule.severity else None,
                "is_active": rule.is_active,
                "created_at": rule.created_at,
                "updated_at": rule.updated_at
            }
            for rule in rules
        ],
        "total": total,
        "skip": skip,
        "limit": limit
    }


@router.post("/rules/database", response_model=Dict[str, Any], status_code=status.HTTP_201_CREATED)
def create_alert_rule_in_db(
    *,
    db: Session = Depends(get_db),
    rule_data: dict,
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    在数据库中创建告警规则
    """
    # 权限校验
    if current_user.role not in ["admin", "super_admin", "unit_admin"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限创建告警规则"
        )

    rule = AlertRule(
        name=rule_data.get("name"),
        description=rule_data.get("description"),
        condition=rule_data.get("condition"),
        severity=AlertSeverity(rule_data.get("severity")) if rule_data.get("severity") else None,
        is_active=rule_data.get("is_active", True)
    )
    
    db.add(rule)
    db.commit()
    db.refresh(rule)
    
    return {
        "success": True,
        "data": {
            "id": rule.id,
            "name": rule.name,
            "description": rule.description,
            "condition": rule.condition,
            "severity": rule.severity.value if rule.severity else None,
            "is_active": rule.is_active,
            "created_at": rule.created_at,
            "updated_at": rule.updated_at
        }
    }


@router.get("/rules", response_model=Dict[str, AlertRuleResponse])
def get_alert_rules(
    current_user: User = Depends(get_current_active_user_custom)
) -> Any:
    """
    获取告警规则（原有API保持兼容）
    """
    try:
        # 先检查用户权限，对非管理员显示友好提示
        if not (current_user.role == UserRole.ADMIN.value or
                current_user.role == UserRole.SUPER_ADMIN.value or
                current_user.role == UserRole.UNIT_ADMIN.value):
            # 返回一个模拟规则，提示用户权限不足
            return {
                "permission_notice": {
                    "id": "permission_notice",
                    "threshold": 0,
                    "duration": 0,
                    "level": "info",
                    "description": "需要管理员权限查看告警规则",
                    "enabled": False
                }
            }

        # 获取真实告警规则
        try:
            # 从告警检测器获取规则
            from app.core.alert_detector import alert_detector
            rules = alert_detector.get_rules()
            
            # 格式化规则以符合API规范
            formatted_rules = {}
            for rule_id, rule in rules.items():
                formatted_rules[rule_id] = {
                    "id": rule_id,
                    "threshold": rule.get("threshold", 0),
                    "duration": rule.get("duration", 0),
                    "level": rule.get("level", "info"),
                    "description": rule.get("description", ""),
                    "enabled": rule.get("enabled", False)
                }
                
            # 如果有规则，返回实际规则数据
            if formatted_rules:
                return formatted_rules
        except ImportError:
            import logging
            logging.error("无法导入告警检测器模块")
        except Exception as e:
            import logging
            logging.error(f"从告警检测器获取规则时出错: {str(e)}")

        # 如果从告警检测器获取失败，回退到告警管理器
        rules = alert_manager.get_alert_rules()

        # 如果没有规则，添加一个提示
        if not rules:
            # 添加默认规则
            rules = {
                "cpu_usage": {
                    "threshold": 90,
                    "duration": 300,
                    "level": "warning",
                    "description": "CPU使用率过高",
                    "enabled": True
                },
                "memory_usage": {
                    "threshold": 90,
                    "duration": 300,
                    "level": "warning",
                    "description": "内存使用率过高",
                    "enabled": True
                },
                "disk_usage": {
                    "threshold": 90,
                    "duration": 300,
                    "level": "warning",
                    "description": "磁盘使用率过高",
                    "enabled": True
                }
            }

        # 添加规则ID
        for rule_id, rule in rules.items():
            rule["id"] = rule_id

        return rules
    except Exception as e:
        # 记录错误但返回友好的错误信息
        import logging
        logging.error(f"获取告警规则出错: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())

        # 返回一个错误规则
        return {
            "error_notice": {
                "id": "error_notice",
                "threshold": 0,
                "duration": 0,
                "level": "error",
                "description": f"获取告警规则时出错: {str(e)}",
                "enabled": False
            }
        }

@router.put("/rules/{rule_id}", response_model=Dict[str, Any])
def update_alert_rule(
    rule_id: str = Path(...),
    rule: AlertRuleRequest = Body(...),
    current_user: User = Depends(get_current_active_user_custom)
) -> Any:
    """
    更新告警规则
    """
    # 检查用户权限
    if not (current_user.role == UserRole.ADMIN.value or
            current_user.role == UserRole.SUPER_ADMIN.value or
            current_user.role == UserRole.UNIT_ADMIN.value):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限"
        )

    # 获取当前规则
    rules = alert_manager.get_alert_rules()
    if rule_id not in rules:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="告警规则不存在"
        )

    # 更新规则
    rule_dict = rule.dict()
    success = alert_manager.set_alert_rule(rule_id, rule_dict)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新告警规则失败"
        )

    return {
        "success": True,
        "message": "告警规则已更新",
        "rule": {**rule_dict, "id": rule_id}
    }

@router.get("/channels", response_model=Dict[str, NotificationChannelResponse])
def get_notification_channels(
    current_user: User = Depends(get_current_active_user_custom)
) -> Any:
    """
    获取通知渠道
    """
    try:
        # 先检查用户权限
        if not (current_user.role == UserRole.ADMIN.value or
                current_user.role == UserRole.SUPER_ADMIN.value):
            # 返回一个模拟渠道，提示用户权限不足
            return {
                "permission_notice": {
                    "id": "permission_notice",
                    "type": "none",
                    "recipients": [],
                    "url": None,
                    "enabled": False
                }
            }

        # 获取真实通知渠道
        try:
            # 从告警检测器获取通知渠道
            from app.core.alert_detector import alert_detector
            channels = alert_detector.get_notification_channels()
            
            # 格式化通知渠道以符合API规范
            formatted_channels = {}
            for channel_id, channel in channels.items():
                formatted_channels[channel_id] = {
                    "id": channel_id,
                    "type": channel.get("type", "none"),
                    "recipients": channel.get("recipients", []),
                    "url": channel.get("url"),
                    "enabled": channel.get("enabled", False)
                }
                
            # 如果有通知渠道，返回实际数据
            if formatted_channels:
                return formatted_channels
        except ImportError:
            import logging
            logging.error("无法导入告警检测器模块")
        except Exception as e:
            import logging
            logging.error(f"从告警检测器获取通知渠道时出错: {str(e)}")

        # 如果从告警检测器获取失败，回退到告警管理器
        channels = alert_manager.get_notification_channels()

        # 如果没有通知渠道，添加默认渠道
        if not channels:
            channels = {
                "email": {
                    "type": "email",
                    "recipients": ["<EMAIL>"],
                    "enabled": False
                },
                "webhook": {
                    "type": "webhook",
                    "url": "https://example.com/webhook",
                    "recipients": [],
                    "enabled": False
                },
                "console": {
                    "type": "console",
                    "recipients": [],
                    "enabled": True
                }
            }

        # 添加渠道ID
        for channel_id, channel in channels.items():
            channel["id"] = channel_id

        return channels
    except Exception as e:
        # 记录错误但返回友好的错误信息
        import logging
        logging.error(f"获取通知渠道出错: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())

        # 返回一个错误渠道
        return {
            "error_notice": {
                "id": "error_notice",
                "type": "none",
                "recipients": [],
                "url": None,
                "enabled": False
            }
        }

@router.put("/channels/{channel_id}", response_model=Dict[str, Any])
def update_notification_channel(
    channel_id: str = Path(...),
    channel: NotificationChannelRequest = Body(...),
    current_user: User = Depends(get_current_active_user_custom)
) -> Any:
    """
    更新通知渠道
    """
    # 检查用户权限
    if not (current_user.role == UserRole.ADMIN.value or
            current_user.role == UserRole.SUPER_ADMIN.value or
            current_user.role == UserRole.UNIT_ADMIN.value):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限"
        )

    # 获取当前渠道
    channels = alert_manager.get_notification_channels()
    if channel_id not in channels:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="通知渠道不存在"
        )

    # 更新渠道
    channel_dict = channel.dict()
    success = alert_manager.set_notification_channel(channel_id, channel_dict)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新通知渠道失败"
        )

    return {
        "success": True,
        "message": "通知渠道已更新",
        "channel": {**channel_dict, "id": channel_id}
    }

@router.post("/channels/{channel_id}/test", response_model=Dict[str, Any])
def test_notification_channel(
    channel_id: str = Path(...),
    current_user: User = Depends(get_current_active_user_custom)
) -> Any:
    """
    测试通知渠道
    """
    # 检查用户权限
    if not (current_user.role == UserRole.ADMIN.value or
            current_user.role == UserRole.SUPER_ADMIN.value or
            current_user.role == UserRole.UNIT_ADMIN.value):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限"
        )

    # 获取当前渠道
    channels = alert_manager.get_notification_channels()
    if channel_id not in channels:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="通知渠道不存在"
        )

    # 测试渠道
    success = alert_manager.test_notification_channel(channel_id)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="测试通知渠道失败"
        )

    return {
        "success": True,
        "message": "测试通知已发送"
    }
