#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入数据库相关模块
from app.db.base_session import get_db_context, engine, Base
from app.db.init_db import init_db

# 导入所有模型以确保它们被注册
from app.models.user import User
from app.models.assessment import Assessment, AssessmentTemplate
from app.models.questionnaire import Questionnaire, QuestionnaireTemplate
from app.models.health_record import HealthRecord
from app.models.document import Document
from app.models.lab_report import LabReport
from app.models.medical_record import MedicalRecord
from app.models.role_application import RoleApplication

def initialize_database():
    """初始化数据库，创建所有必要的表"""
    try:
        
        logger.info("开始初始化数据库...")
        
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        logger.info("数据库表创建完成")
        
        # 使用数据库会话
        with get_db_context() as db:
            # 运行初始化
            init_db(db, force_recreate=False)
            logger.info("数据库初始化完成")
            
        # 验证表是否创建成功
        verify_tables()
        
        logger.info("✅ 数据库初始化成功完成！")
        
    except Exception as e:
        logger.error(f"❌ 数据库初始化失败: {e}")
        raise

def verify_tables():
    """验证表是否创建成功"""
    import sqlite3
    
    db_path = "app.db"
    if not os.path.exists(db_path):
        logger.error(f"数据库文件 {db_path} 不存在")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 获取所有表名
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = [table[0] for table in cursor.fetchall()]
    
    logger.info(f"数据库中的表: {tables}")
    
    # 检查关键表是否存在
    required_tables = ['users', 'assessments', 'questionnaires', 'assessment_results', 'questionnaire_results']
    missing_tables = []
    
    for table in required_tables:
        if table in tables:
            # 获取记录数量
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            logger.info(f"✅ {table} 表存在，记录数: {count}")
        else:
            missing_tables.append(table)
            logger.warning(f"❌ {table} 表不存在")
    
    conn.close()
    
    if missing_tables:
        logger.warning(f"缺失的表: {missing_tables}")
    else:
        logger.info("所有必要的表都已创建")

def create_admin_user():
    """创建管理员用户"""
    try:
        from app.core.security import get_password_hash
        
        with get_db_context() as db:
            # 检查是否已存在管理员用户
            existing_admin = db.query(User).filter(User.username == "admin").first()
            if existing_admin:
                logger.info("管理员用户已存在")
                return
            
            # 创建管理员用户
            admin_user = User(
                username="admin",
                email="<EMAIL>",
                hashed_password=get_password_hash("admin123"),
                full_name="系统管理员",
                role="admin",
                custom_id="admin001"
            )
            
            db.add(admin_user)
            db.commit()
            logger.info("✅ 管理员用户创建成功: admin/admin123")
            
    except Exception as e:
        logger.error(f"❌ 创建管理员用户失败: {e}")

if __name__ == "__main__":
    print("🚀 开始数据库初始化...")
    initialize_database()
    create_admin_user()
    print("\n✅ 数据库初始化完成！")
    print("\n接下来可以:")
    print("1. 重启后端服务")
    print("2. 使用 admin/admin123 登录测试")