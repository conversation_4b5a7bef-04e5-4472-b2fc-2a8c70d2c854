from sqlalchemy import create_engine, desc
from sqlalchemy.orm import sessionmaker
import sys
sys.path.append('c:/Users/<USER>/Desktop/health-Trea/YUN/backend')

from app.models.assessment import Assessment
from app.models.distribution import AssessmentDistribution

# 连接数据库
engine = create_engine('sqlite:///c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db')
Session = sessionmaker(bind=engine)
session = Session()

print("=== 测试SQLAlchemy查询逻辑 ===")

# 1. 直接查询Assessment表
print("\n1. 直接查询Assessment表 (custom_id='SM_006'):")
query1 = session.query(Assessment).filter(Assessment.custom_id == 'SM_006')
results1 = query1.all()
print(f"结果数量: {len(results1)}")
for assessment in results1:
    print(f"  ID: {assessment.id}, 名称: {assessment.name}, 状态: {assessment.status}")

# 2. 使用outerjoin查询
print("\n2. 使用outerjoin查询:")
query2 = session.query(Assessment).outerjoin(AssessmentDistribution).filter(Assessment.custom_id == 'SM_006')
results2 = query2.all()
print(f"结果数量: {len(results2)}")
for assessment in results2:
    print(f"  ID: {assessment.id}, 名称: {assessment.name}, 状态: {assessment.status}")

# 3. 使用distinct查询
print("\n3. 使用distinct查询:")
query3 = session.query(Assessment).outerjoin(AssessmentDistribution).distinct(Assessment.id).filter(Assessment.custom_id == 'SM_006')
results3 = query3.all()
print(f"结果数量: {len(results3)}")
for assessment in results3:
    print(f"  ID: {assessment.id}, 名称: {assessment.name}, 状态: {assessment.status}")

# 4. 测试count查询
print("\n4. 测试count查询:")
count1 = session.query(Assessment).filter(Assessment.custom_id == 'SM_006').count()
print(f"直接查询count: {count1}")

count2 = session.query(Assessment).outerjoin(AssessmentDistribution).filter(Assessment.custom_id == 'SM_006').count()
print(f"outerjoin查询count: {count2}")

count3 = session.query(Assessment).outerjoin(AssessmentDistribution).distinct(Assessment.id).filter(Assessment.custom_id == 'SM_006').count()
print(f"distinct查询count: {count3}")

# 5. 测试按状态过滤
print("\n5. 测试按状态过滤:")
query_pending = session.query(Assessment).outerjoin(AssessmentDistribution).distinct(Assessment.id).filter(Assessment.custom_id == 'SM_006').filter(Assessment.status == 'pending')
results_pending = query_pending.all()
print(f"pending状态数量: {len(results_pending)}")

query_completed = session.query(Assessment).outerjoin(AssessmentDistribution).distinct(Assessment.id).filter(Assessment.custom_id == 'SM_006').filter(Assessment.status == 'completed')
results_completed = query_completed.all()
print(f"completed状态数量: {len(results_completed)}")

# 6. 测试排序和分页
print("\n6. 测试排序和分页:")
query_ordered = session.query(Assessment).outerjoin(AssessmentDistribution).distinct(Assessment.id).filter(Assessment.custom_id == 'SM_006').order_by(desc(Assessment.created_at))
results_ordered = query_ordered.limit(2).all()
print(f"分页结果数量: {len(results_ordered)}")
for assessment in results_ordered:
    print(f"  ID: {assessment.id}, 名称: {assessment.name}, 状态: {assessment.status}, 创建时间: {assessment.created_at}")

session.close()