from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from functools import wraps
import json
import hashlib
from sqlalchemy.orm import Session

# 简单的内存缓存实现
class HealthRecordsCache:
    def __init__(self, default_ttl: int = 300):  # 默认5分钟过期
        self._cache: Dict[str, Dict[str, Any]] = {}
        self.default_ttl = default_ttl
    
    def _generate_key(self, custom_id: str, **params) -> str:
        """生成缓存键"""
        # 将参数排序并序列化，确保相同参数生成相同的键
        sorted_params = sorted(params.items())
        params_str = json.dumps(sorted_params, default=str, sort_keys=True)
        key_data = f"{custom_id}:{params_str}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def get(self, custom_id: str, **params) -> Optional[Any]:
        """获取缓存数据"""
        key = self._generate_key(custom_id, **params)
        if key in self._cache:
            cache_entry = self._cache[key]
            # 检查是否过期
            if datetime.now() < cache_entry['expires_at']:
                return cache_entry['data']
            else:
                # 删除过期缓存
                del self._cache[key]
        return None
    
    def set(self, custom_id: str, data: Any, ttl: Optional[int] = None, **params) -> None:
        """设置缓存数据"""
        key = self._generate_key(custom_id, **params)
        expires_at = datetime.now() + timedelta(seconds=ttl or self.default_ttl)
        self._cache[key] = {
            'data': data,
            'expires_at': expires_at,
            'created_at': datetime.now()
        }
    
    def invalidate(self, custom_id: str, **params) -> None:
        """删除特定缓存"""
        key = self._generate_key(custom_id, **params)
        if key in self._cache:
            del self._cache[key]
    
    def invalidate_user(self, custom_id: str) -> None:
        """删除用户的所有缓存"""
        keys_to_delete = []
        for key in self._cache:
            if key.startswith(hashlib.md5(f"{custom_id}:".encode()).hexdigest()[:8]):
                keys_to_delete.append(key)
        
        for key in keys_to_delete:
            del self._cache[key]
    
    def clear_expired(self) -> None:
        """清理过期缓存"""
        now = datetime.now()
        keys_to_delete = []
        for key, cache_entry in self._cache.items():
            if now >= cache_entry['expires_at']:
                keys_to_delete.append(key)
        
        for key in keys_to_delete:
            del self._cache[key]
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        now = datetime.now()
        total_entries = len(self._cache)
        expired_entries = sum(1 for entry in self._cache.values() if now >= entry['expires_at'])
        
        return {
            'total_entries': total_entries,
            'active_entries': total_entries - expired_entries,
            'expired_entries': expired_entries
        }

# 全局缓存实例
health_records_cache = HealthRecordsCache()

def cache_health_records(ttl: int = 300):
    """健康记录缓存装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 提取custom_id和其他参数
            custom_id = None
            cache_params = {}
            
            # 从参数中提取custom_id
            if 'custom_id' in kwargs:
                custom_id = kwargs['custom_id']
            elif len(args) >= 2:  # 假设第二个参数是custom_id
                custom_id = args[1]
            
            if not custom_id:
                # 如果没有custom_id，直接执行函数
                return func(*args, **kwargs)
            
            # 提取缓存相关参数
            cache_params = {
                k: v for k, v in kwargs.items() 
                if k in ['record_type', 'status', 'start_date', 'end_date', 'skip', 'limit']
                and v is not None
            }
            
            # 尝试从缓存获取
            cached_result = health_records_cache.get(custom_id, **cache_params)
            if cached_result is not None:
                return cached_result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            
            # 只缓存成功的结果
            if isinstance(result, dict) and result.get('status') == 'success':
                health_records_cache.set(custom_id, result, ttl, **cache_params)
            
            return result
        
        return wrapper
    return decorator

def invalidate_user_cache(custom_id: str):
    """使用户缓存失效（在数据更新时调用）"""
    health_records_cache.invalidate_user(custom_id)

def clear_expired_cache():
    """清理过期缓存（可以定期调用）"""
    health_records_cache.clear_expired()

def get_cache_stats():
    """获取缓存统计信息"""
    return health_records_cache.get_stats()