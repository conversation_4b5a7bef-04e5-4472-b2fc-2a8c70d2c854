#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查评估量表的计分逻辑
"""

import sqlite3
import json
import sys

def check_scoring_logic():
    """检查评估量表的计分逻辑"""
    try:
        # 连接数据库
        conn = sqlite3.connect('c:/Users/<USER>/Desktop/health-Trea/YUN/backend/health_assessment.db')
        cursor = conn.cursor()
        
        print("=== 评估量表计分逻辑检查 ===")
        
        # 0. 先查看数据库中所有的表
        print("\n0. 数据库中的所有表:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        for table in tables:
            print(f"  {table[0]}")
        
        # 1. 检查assessment相关表的结构
        assessment_tables = [t[0] for t in tables if 'assessment' in t[0].lower()]
        print(f"\n1. 评估相关的表: {assessment_tables}")
        
        for table_name in assessment_tables:
            print(f"\n{table_name}表结构:")
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            for col in columns:
                print(f"  {col[1]} ({col[2]})")
        
        # 2. 检查蒙特利尔认知评估量表模板
        if 'assessment_templates' in [t[0] for t in tables]:
            print("\n\n2. 检查蒙特利尔认知评估量表模板:")
            cursor.execute("""
                SELECT id, name, description, instructions, is_active, status
                FROM assessment_templates 
                WHERE name LIKE '%蒙特利尔%' OR name LIKE '%MoCA%'
            """)
            templates = cursor.fetchall()
            
            for template in templates:
                template_id, name, description, instructions, is_active, status = template
                print(f"\n模板ID: {template_id}")
                print(f"名称: {name}")
                print(f"描述: {description}")
                print(f"说明: {instructions}")
                print(f"是否激活: {is_active}")
                print(f"状态: {status}")
        
        # 3. 检查问题模板
        if 'assessment_template_questions' in [t[0] for t in tables]:
            print("\n\n3. 检查问题模板:")
            cursor.execute("""
                SELECT COUNT(*) FROM assessment_template_questions
            """)
            question_count = cursor.fetchone()[0]
            print(f"问题模板总数: {question_count}")
            
            if question_count > 0:
                cursor.execute("""
                    SELECT * FROM assessment_template_questions LIMIT 3
                """)
                sample_questions = cursor.fetchall()
                
                cursor.execute("PRAGMA table_info(assessment_template_questions)")
                question_cols = cursor.fetchall()
                col_names = [col[1] for col in question_cols]
                
                print(f"\n问题表列名: {col_names}")
                print("\n前3个问题示例:")
                for i, question in enumerate(sample_questions):
                    print(f"\n  问题 {i+1}:")
                    for j, value in enumerate(question):
                        if j < len(col_names):
                            print(f"    {col_names[j]}: {value}")
        
        # 4. 检查用户回答数据
        response_tables = [t[0] for t in tables if 'response' in t[0].lower() or 'result' in t[0].lower()]
        print(f"\n\n4. 回答/结果相关的表: {response_tables}")
        
        for table_name in response_tables:
            print(f"\n检查 {table_name} 表:")
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"记录总数: {count}")
            
            if count > 0:
                # 查看表结构
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                col_names = [col[1] for col in columns]
                print(f"列名: {col_names}")
                
                # 查找SM_008用户的记录
                if 'custom_id' in col_names:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE custom_id = 'SM_008'")
                    user_count = cursor.fetchone()[0]
                    print(f"SM_008用户的记录数: {user_count}")
                    
                    if user_count > 0:
                        cursor.execute(f"SELECT * FROM {table_name} WHERE custom_id = 'SM_008' LIMIT 2")
                        user_records = cursor.fetchall()
                        
                        for i, record in enumerate(user_records):
                            print(f"\n  SM_008记录 {i+1}:")
                            for j, value in enumerate(record):
                                if j < len(col_names):
                                    col_name = col_names[j]
                                    if col_name in ['answers', 'raw_answers'] and value:
                                        try:
                                            parsed_value = json.loads(value)
                                            if isinstance(parsed_value, dict) and 'answers' in parsed_value:
                                                answers_list = parsed_value['answers']
                                                print(f"    {col_name}: {len(answers_list)}个回答")
                                                # 显示前2个回答
                                                for k, answer in enumerate(answers_list[:2]):
                                                    print(f"      回答{k+1}: 问题ID={answer.get('question_id')}, 答案={answer.get('answer')}, 得分={answer.get('score')}")
                                            else:
                                                print(f"    {col_name}: {str(value)[:100]}..." if len(str(value)) > 100 else f"    {col_name}: {value}")
                                        except:
                                            print(f"    {col_name}: {str(value)[:100]}..." if len(str(value)) > 100 else f"    {col_name}: {value}")
                                    else:
                                        print(f"    {col_name}: {value}")
        
        # 5. 分析计分问题的根本原因
        print("\n\n5. 计分问题根本原因分析:")
        print("\n从上面的分析可以看出可能的问题:")
        print("1. 需要检查评估模板中是否有计分规则")
        print("2. 需要检查后端提交逻辑是否正确计算得分")
        print("3. 需要检查客户端是否正确传递答案和得分")
        print("4. 可能需要实现服务端计分逻辑")
        
        conn.close()
        
    except Exception as e:
        print(f"检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_scoring_logic()