/**
 * 认证相关API
 */
import request from "@/utils/request";
import { ElMessage } from "element-plus";
import { setToken, updateAuth } from "@/utils/auth-standard";

/**
 * 用户登录
 * @param {Object} data 登录数据
 * @returns {Promise} 请求结果
 */
export function login(data) {
  console.log("发送登录请求: POST /api/auth/frontend_login 数据:", {
    username: data.username,
    password: "******",
  });

  return request({
    url: "/api/auth/frontend_login",
    method: "post",
    data: {
      username: data.username,
      password: data.password
    }
  })
    .then((response) => {
      // 登录成功，保存token和用户信息
      setToken(response.access_token);
      updateAuth(response);
      return response;
    })
    .catch((error) => {
      console.error("登录失败:", error);
      ElMessage.error("登录失败: " + (error.message || "未知错误"));
      throw error;
    });
}

/**
 * 用户注册
 * @param {Object} data 注册数据
 * @returns {Promise} 请求结果
 */
export function register(data) {
  return request({
    url: "/api/auth/register",
    method: "post",
    data,
  })
    .then((response) => {
      // 如果注册成功，保存token和用户信息
      if (response.status === "success" && response.access_token) {
        // 验证令牌格式
        const parts = response.access_token.split(".");
        if (parts.length !== 3) {
          console.error(`令牌格式不正确，部分数量: ${parts.length}，应为3部分`);
          return {
            status: "error",
            message: "注册成功但收到非标准JWT令牌",
            data: response,
          };
        }

        // 构建认证数据
        const authData = {
          access_token: response.access_token,
          user: {
            id: response.id,
            username: response.username,
            custom_id: response.custom_id,
            role: "personal", // 默认为个人用户
          },
          custom_id: response.custom_id,
        };

        // 使用标准认证模块更新认证信息
        updateAuth(authData);
      }

      return {
        status: response.status || "success",
        message: response.message || "注册成功",
        data: response,
      };
    })
    .catch((error) => {
      console.error("注册API错误:", error);
      return {
        status: "error",
        message: error.response?.data?.detail || error.message || "注册失败",
        data: null,
      };
    });
}

/**
 * 用户登出
 * @returns {Promise} 请求结果
 */
export function logout() {
  return request({
    url: "/api/auth/logout",
    method: "post",
  })
    .then(() => {
      // 无论后端返回什么，都清除认证信息
      import("@/utils/auth-standard").then(({ clearAuth }) => {
        clearAuth();
      });

      return {
        status: "success",
        message: "登出成功",
      };
    })
    .catch((error) => {
      console.error("登出API错误:", error);

      // 即使API调用失败，也清除认证信息
      import("@/utils/auth-standard").then(({ clearAuth }) => {
        clearAuth();
      });

      return {
        status: "success", // 仍然返回成功，因为本地状态已清除
        message: "登出成功",
      };
    });
}

/**
 * 获取当前用户信息
 * @returns {Promise} 请求结果
 */
export function getUserInfo() {
  return request({
    url: "/api/auth/test-unified-auth",
    method: "get",
  })
    .then((response) => {
      // 更新用户信息
      if (response && response.username) {
        // 构建用户数据
        const userData = {
          user: {
            id: response.id,
            username: response.username,
            custom_id: response.custom_id,
            role: response.role,
          },
          custom_id: response.custom_id,
        };

        // 使用标准认证模块更新用户信息
        import("@/utils/auth-standard").then(({ updateAuth }) => {
          updateAuth(userData);
        });
      }

      return {
        status: "success",
        message: "获取用户信息成功",
        data: response,
      };
    })
    .catch((error) => {
      console.error("获取用户信息API错误:", error);

      // 如果是401错误，清除认证信息
      if (error.response && error.response.status === 401) {
        import("@/utils/auth-standard").then(({ clearAuth }) => {
          clearAuth();
        });

        // 显示提示信息
        ElMessage({
          message: "登录已过期，请重新登录",
          type: "warning",
          duration: 5000,
        });
      }

      return {
        status: "error",
        message:
          error.response?.data?.detail || error.message || "获取用户信息失败",
        data: null,
      };
    });
}

/**
 * 检查认证状态
 * @returns {Object} 认证状态
 */
export function checkAuth() {
  const token = localStorage.getItem("token");
  const user = JSON.parse(localStorage.getItem("user") || "null");
  const custom_id = localStorage.getItem("custom_id");

  return {
    isAuthenticated: !!(token || custom_id),
    token,
    user,
    custom_id,
  };
}

/**
 * 刷新令牌
 * @returns {Promise} 请求结果
 */
export function refreshToken() {
  return request({
    url: "/api/auth/refresh",
    method: "post",
  })
    .then((response) => {
      if (response.access_token) {
        // 验证令牌格式
        const parts = response.access_token.split(".");
        if (parts.length !== 3) {
          console.error(`令牌格式不正确，部分数量: ${parts.length}，应为3部分`);
          return {
            status: "error",
            message: "令牌刷新失败：收到非标准JWT令牌",
            data: null,
          };
        }

        // 使用标准认证模块设置令牌
        setToken(response.access_token);

        return {
          status: "success",
          message: "令牌刷新成功",
          data: response,
        };
      } else {
        return {
          status: "error",
          message: "令牌刷新失败",
          data: null,
        };
      }
    })
    .catch((error) => {
      console.error("刷新令牌API错误:", error);
      return {
        status: "error",
        message:
          error.response?.data?.detail || error.message || "令牌刷新失败",
        data: null,
      };
    });
}
