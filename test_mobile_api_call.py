#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试移动端API调用，验证数据返回是否正确
"""

import requests
import json
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_mobile_api():
    """测试移动端API调用"""
    base_url = "http://localhost:8000/api"
    custom_id = "SM_006"
    
    # 测试获取量表
    logger.info("=== 测试获取量表API ===")
    
    # 1. 不带status参数
    url = f"{base_url}/mobile/assessments"
    headers = {
        'X-User-ID': custom_id,
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(url, headers=headers)
        logger.info(f"API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            logger.info(f"API返回数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if data.get('status') == 'success' and 'data' in data:
                assessments = data['data']
                logger.info(f"返回的量表总数: {len(assessments)}")
                
                # 统计不同状态的量表
                pending_count = sum(1 for a in assessments if a.get('status') == 'pending')
                completed_count = sum(1 for a in assessments if a.get('status') == 'completed')
                
                logger.info(f"Pending状态量表: {pending_count}")
                logger.info(f"Completed状态量表: {completed_count}")
                
                # 显示每个量表的详细信息
                for i, assessment in enumerate(assessments):
                    logger.info(f"量表{i+1}: ID={assessment.get('id')}, 名称={assessment.get('name')}, 状态={assessment.get('status')}")
            else:
                logger.error(f"API返回错误: {data}")
        else:
            logger.error(f"API请求失败: {response.status_code} - {response.text}")
            
    except Exception as e:
        logger.error(f"API请求异常: {str(e)}")
    
    # 测试获取问卷
    logger.info("\n=== 测试获取问卷API ===")
    
    url = f"{base_url}/mobile/questionnaires"
    
    try:
        response = requests.get(url, headers=headers)
        logger.info(f"API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            logger.info(f"API返回数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if data.get('status') == 'success' and 'data' in data:
                questionnaires = data['data']
                logger.info(f"返回的问卷总数: {len(questionnaires)}")
                
                # 统计不同状态的问卷
                pending_count = sum(1 for q in questionnaires if q.get('status') == 'pending')
                completed_count = sum(1 for q in questionnaires if q.get('status') == 'completed')
                
                logger.info(f"Pending状态问卷: {pending_count}")
                logger.info(f"Completed状态问卷: {completed_count}")
                
                # 显示每个问卷的详细信息
                for i, questionnaire in enumerate(questionnaires):
                    logger.info(f"问卷{i+1}: ID={questionnaire.get('id')}, 名称={questionnaire.get('name')}, 状态={questionnaire.get('status')}")
            else:
                logger.error(f"API返回错误: {data}")
        else:
            logger.error(f"API请求失败: {response.status_code} - {response.text}")
            
    except Exception as e:
        logger.error(f"API请求异常: {str(e)}")

if __name__ == "__main__":
    test_mobile_api()