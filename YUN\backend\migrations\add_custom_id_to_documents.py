"""添加custom_id字段到documents表

此脚本使用SQLite直接操作数据库，添加custom_id字段到documents表。
不再使用Alembic，避免依赖问题。

Create Date: 2025-05-11 13:30:00.000000
"""
import os
import sys
import sqlite3
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
versions_dir = os.path.dirname(current_dir)
migrations_dir = os.path.dirname(versions_dir)
backend_dir = os.path.dirname(migrations_dir)
project_dir = os.path.dirname(backend_dir)
sys.path.append(backend_dir)
sys.path.append(project_dir)

# 尝试多个可能的数据库路径
possible_db_paths = [
    os.path.join(backend_dir, "app.db"),
    os.path.join(project_dir, "app.db"),
    os.path.join(backend_dir, "app", "app.db"),
    os.path.join(project_dir, "YUN", "app.db"),
    os.path.join(project_dir, "YUN", "backend", "app.db")
]

# 导入数据库配置
try:
    from app.core.config import settings
    if hasattr(settings, 'SQLALCHEMY_DATABASE_URI'):
        db_uri = settings.SQLALCHEMY_DATABASE_URI
        if db_uri.startswith('sqlite:///'):
            DB_PATH = db_uri.replace("sqlite:///", "")
            logger.info(f"从配置中获取数据库路径: {DB_PATH}")
            possible_db_paths.insert(0, DB_PATH)  # 添加到可能路径的开头
except ImportError:
    logger.warning("无法导入配置，将使用默认数据库路径")

# 查找存在的数据库文件
DB_PATH = None
for path in possible_db_paths:
    if os.path.exists(path):
        DB_PATH = path
        logger.info(f"找到数据库文件: {DB_PATH}")
        break

if not DB_PATH:
    logger.error(f"未找到数据库文件，尝试过以下路径: {possible_db_paths}")
    # 尝试查找当前目录下的所有.db文件
    for root, dirs, files in os.walk(project_dir):
        for file in files:
            if file.endswith('.db'):
                logger.info(f"找到数据库文件: {os.path.join(root, file)}")
                DB_PATH = os.path.join(root, file)
                break
        if DB_PATH:
            break

def add_custom_id_to_documents():
    """向documents表添加custom_id字段"""
    try:
        # 检查数据库文件是否存在
        if not DB_PATH or not os.path.exists(DB_PATH):
            logger.error(f"数据库文件不存在或未找到: {DB_PATH}")
            return False

        logger.info(f"正在连接数据库: {DB_PATH}")
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 检查documents表是否存在
        logger.info("检查documents表是否存在...")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='documents'")
        if not cursor.fetchone():
            logger.warning("documents表不存在，无需修改")
            conn.close()
            return False

        # 检查表结构
        logger.info("检查表结构...")
        cursor.execute("PRAGMA table_info(documents)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        logger.info(f"当前列: {', '.join(column_names)}")

        # 检查是否已有custom_id字段
        if 'custom_id' in column_names:
            logger.info("✓ documents表已有custom_id字段，无需添加")
        else:
            # 添加custom_id字段
            logger.info("开始向documents表添加custom_id字段...")
            try:
                cursor.execute("ALTER TABLE documents ADD COLUMN custom_id TEXT")
                logger.info("✓ custom_id字段添加成功")
            except sqlite3.OperationalError as e:
                logger.error(f"❌ 添加custom_id字段失败: {str(e)}")
                conn.rollback()
                conn.close()
                return False

        # 提交更改
        conn.commit()
        logger.info("✓ 数据库更新完成")
        conn.close()
        return True
    except Exception as e:
        logger.error(f"❌ 修改documents表时出错: {str(e)}")
        return False

def main():
    """主函数"""
    success = add_custom_id_to_documents()
    if success:
        logger.info("数据库结构更新成功")
    else:
        logger.error("数据库结构更新失败")

if __name__ == "__main__":
    main()
