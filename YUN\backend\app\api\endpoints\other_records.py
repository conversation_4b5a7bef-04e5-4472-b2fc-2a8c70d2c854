from typing import Any, List, Optional
from datetime import datetime
from app.utils.field_compatibility import ensure_field_compatibility, ensure_list_field_compatibility

from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session
from starlette import status

from app import schemas
from app.models.user import User
from app.api import deps
from app.core.auth import get_current_active_user_custom
from app.models.other_record import OtherRecordType
from app.crud import other_record

router = APIRouter()


@router.get("/user/{custom_id}", response_model=schemas.other_record.OtherRecordListResponse)
def read_user_records(
    *,
    db: Session = Depends(deps.get_db),
    custom_id: str = Path(..., description="用户ID"),
    record_type: Optional[str] = Query(None, description="记录类型"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    current_user: User = Depends(get_current_active_user_custom)
) -> Any:
    """
    获取指定用户的记录列表
    """
    print(f"other_records.py - 获取用户记录 - 用户ID: {custom_id}")

    # 支持数字ID和自定义ID查找用户
    user = None
    if custom_id.isdigit():
        user = db.query(User).filter(User.id == int(custom_id)).first()
    else:
        user = db.query(User).filter(User.custom_id == custom_id).first()
    
    if not user:
        print(f"用户{custom_id}不存在，返回404异常")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到用户ID: {custom_id}"
        )

    # 权限校验
    if current_user.id != user.id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        print(f"权限检查失败 - 当前用户: {current_user.custom_id}, 角色: {current_user.role}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限查看此用户的数据"
        )

    record_type_enum = None
    if record_type is not None:
        try:
            record_type_enum = OtherRecordType(record_type)
        except Exception:
            if record_type != '':
                print(f"record_type非法或未定义: {record_type}, 返回空结果")
                return {"total": 0, "records": []}
            # record_type为None或空字符串时，不加类型过滤
    
    try:
        print(f"尝试获取记录总数 - 用户ID: {custom_id}")
        
        # 确保参数类型正确
        count_kwargs = {
            "db": db,
            "custom_id": user.custom_id,
        }
        
        # 单独处理可选参数，确保类型正确
        if record_type_enum is not None:
            count_kwargs["record_type"] = record_type_enum
            
        if start_date is not None:
            count_kwargs["start_date"] = start_date
            
        if end_date is not None:
            count_kwargs["end_date"] = end_date
        
        total = other_record.get_records_count(**count_kwargs)
        print(f"获取记录总数成功 - 总数: {total}")
        
        if total > 0:
            print(f"尝试获取记录列表 - 用户ID: {custom_id}, 跳过: {skip}, 限制: {limit}")
            
            # 构建获取记录的参数
            records_kwargs = count_kwargs.copy()
            records_kwargs["skip"] = skip
            records_kwargs["limit"] = limit
            
            records = other_record.get_records(**records_kwargs)
            print(f"获取记录列表成功 - 记录数: {len(records)}")
            return {"total": total, "records": ensure_list_field_compatibility(records)}
        else:
            print(f"用户{custom_id}存在但无记录，返回空数组")
            return {"total": 0, "records": []}
    except Exception as e:
        print(f"获取记录时出错 - 错误: {str(e)}")
        # 返回空结果而不是抛出异常
        return {"total": 0, "records": []}


@router.post("/", response_model=schemas.other_record.OtherRecordResponse, status_code=status.HTTP_201_CREATED)
def create_record(
    *,
    db: Session = Depends(deps.get_db),
    record_in: schemas.other_record.OtherRecordCreate,
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    创建新的其它记录
    """
    # 检查用户是否有权限为该用户创建记录
    if current_user.custom_id != record_in.custom_id and not current_user.is_superuser:
        if not current_user.role or current_user.role not in ["consultant", "unit_admin", "family_admin", "super_admin"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限为其他用户创建记录"
            )

    try:
        # 创建记录
        record = other_record.create_record(db=db, obj_in=record_in)
        return ensure_field_compatibility(record)
    except Exception as e:
        print(f"创建记录时出错 - 错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建记录失败: {str(e)}"
        )


@router.get("/{record_id}", response_model=schemas.other_record.OtherRecordResponse)
def read_record(
    *,
    db: Session = Depends(deps.get_db),
    record_id: int = Path(..., description="记录ID"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    获取指定ID的其它记录
    """
    try:
        # 查询记录
        record = other_record.get_record(db, record_id=record_id)
        if not record:
            # 记录不存在时返回空数据和200
            print(f"记录{record_id}不存在，返回空数据")
            return {}

        # 检查权限
        if current_user.custom_id != record.custom_id and not current_user.is_superuser:
            if current_user.role not in ["consultant", "unit_admin", "family_admin", "super_admin"]:
                print(f"权限检查失败 - 当前用户: {current_user.custom_id}, 角色: {current_user.role}")
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有权限查看此记录"
                )

        return ensure_field_compatibility(record)
    except HTTPException:
        raise
    except Exception as e:
        print(f"获取记录时出错 - 错误: {str(e)}")
        return {}


@router.put("/{record_id}", response_model=schemas.other_record.OtherRecordResponse)
def update_record(
    *,
    db: Session = Depends(deps.get_db),
    record_id: int = Path(..., gt=0),
    record_in: schemas.other_record.OtherRecordUpdate,
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    更新记录
    """
    try:
        record = other_record.get_record(db=db, record_id=record_id)
        if not record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="记录不存在"
            )

        # 检查用户是否有权限更新记录
        if record.custom_id != current_user.custom_id and not current_user.is_superuser:
            if not current_user.role or current_user.role not in ["consultant", "unit_admin", "family_admin", "super_admin"]:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有权限更新此记录"
                )

        # 更新记录
        updated_record = other_record.update_record(
            db=db,
            db_obj=record,
            obj_in=record_in
        )

        return ensure_field_compatibility(updated_record)
    except HTTPException:
        raise
    except Exception as e:
        print(f"更新记录时出错 - 错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新记录失败: {str(e)}"
        )


@router.delete("/{record_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_record(
    *,
    db: Session = Depends(deps.get_db),
    record_id: int = Path(..., gt=0),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> None:
    """
    删除记录
    """
    try:
        record = other_record.get_record(db=db, record_id=record_id)
        if not record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="记录不存在"
            )

        # 检查用户是否有权限删除记录
        if record.custom_id != current_user.custom_id and not current_user.is_superuser:
            if not current_user.role or current_user.role not in ["consultant", "unit_admin", "family_admin", "super_admin"]:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有权限删除此记录"
                )

        # 删除记录
        other_record.delete_record(db=db, record_id=record_id)
    except HTTPException:
        raise
    except Exception as e:
        print(f"删除记录时出错 - 错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除记录失败: {str(e)}"
        )


@router.get("/search/{custom_id}", response_model=schemas.other_record.OtherRecordListResponse)
def search_records(
    *,
    db: Session = Depends(deps.get_db),
    custom_id: str = Path(..., description="用户ID"),
    query: str = Query(..., min_length=1, description="搜索关键词"),
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    搜索用户记录
    """
    # 支持数字ID和自定义ID查找用户
    user = None
    if custom_id.isdigit():
        user = db.query(User).filter(User.id == int(custom_id)).first()
    else:
        user = db.query(User).filter(User.custom_id == custom_id).first()
    
    if not user:
        print(f"用户{custom_id}不存在，返回404异常")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到用户ID: {custom_id}"
        )
        
    # 检查用户是否有权限搜索记录
    if current_user.custom_id != user.custom_id and not current_user.is_superuser:
        if not current_user.role or current_user.role not in ["consultant", "unit_admin", "family_admin", "super_admin"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限搜索此用户的记录"
            )

    try:
        # 获取搜索结果总数
        total = other_record.search_records_count(
            db=db,
            custom_id=user.custom_id,
            search_term=query
        )

        if total > 0:
            # 获取搜索结果列表
            records = other_record.search_records(
                db=db,
                custom_id=user.custom_id,
                search_term=query,
                skip=skip,
                limit=limit
            )
            return {"total": total, "records": ensure_list_field_compatibility(records)}
        else:
            return {"total": 0, "records": []}
    except Exception as e:
        print(f"搜索记录时出错 - 错误: {str(e)}")
        return {"total": 0, "records": []}