#!/usr/bin/env node

/**
 * 依赖管理和安全检查脚本
 * 用于检查项目依赖的安全漏洞、版本更新和兼容性问题
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')
const https = require('https')

class DependencyChecker {
  constructor() {
    this.packageJsonPath = path.join(process.cwd(), 'package.json')
    this.packageLockPath = path.join(process.cwd(), 'package-lock.json')
    this.results = {
      security: [],
      outdated: [],
      compatibility: [],
      summary: {}
    }
  }

  /**
   * 运行完整的依赖检查
   */
  async run() {
    console.log('🔍 开始依赖检查...')
    
    try {
      // 检查 package.json 是否存在
      if (!fs.existsSync(this.packageJsonPath)) {
        throw new Error('未找到 package.json 文件')
      }

      // 读取 package.json
      const packageJson = JSON.parse(fs.readFileSync(this.packageJsonPath, 'utf8'))
      
      // 执行各项检查
      await this.checkSecurity()
      await this.checkOutdated()
      await this.checkCompatibility(packageJson)
      await this.checkDuplicates()
      
      // 生成报告
      this.generateReport()
      
      console.log('✅ 依赖检查完成')
      
    } catch (error) {
      console.error('❌ 依赖检查失败:', error.message)
      process.exit(1)
    }
  }

  /**
   * 安全漏洞检查
   */
  async checkSecurity() {
    console.log('🔒 检查安全漏洞...')
    
    try {
      // 使用 npm audit
      const auditResult = execSync('npm audit --json', { 
        encoding: 'utf8',
        stdio: ['pipe', 'pipe', 'pipe']
      })
      
      const audit = JSON.parse(auditResult)
      
      if (audit.vulnerabilities) {
        Object.entries(audit.vulnerabilities).forEach(([name, vuln]) => {
          this.results.security.push({
            package: name,
            severity: vuln.severity,
            title: vuln.title,
            url: vuln.url,
            range: vuln.range,
            fixAvailable: vuln.fixAvailable
          })
        })
      }
      
      console.log(`   发现 ${this.results.security.length} 个安全问题`)
      
    } catch (error) {
      // npm audit 可能返回非零退出码，但仍有有用信息
      if (error.stdout) {
        try {
          const audit = JSON.parse(error.stdout)
          if (audit.vulnerabilities) {
            Object.entries(audit.vulnerabilities).forEach(([name, vuln]) => {
              this.results.security.push({
                package: name,
                severity: vuln.severity,
                title: vuln.title,
                url: vuln.url,
                range: vuln.range,
                fixAvailable: vuln.fixAvailable
              })
            })
          }
        } catch (parseError) {
          console.warn('   无法解析安全检查结果')
        }
      }
    }
  }

  /**
   * 过时依赖检查
   */
  async checkOutdated() {
    console.log('📦 检查过时依赖...')
    
    try {
      const outdatedResult = execSync('npm outdated --json', { 
        encoding: 'utf8',
        stdio: ['pipe', 'pipe', 'pipe']
      })
      
      const outdated = JSON.parse(outdatedResult)
      
      Object.entries(outdated).forEach(([name, info]) => {
        this.results.outdated.push({
          package: name,
          current: info.current,
          wanted: info.wanted,
          latest: info.latest,
          type: info.type,
          homepage: info.homepage
        })
      })
      
    } catch (error) {
      // npm outdated 在有过时包时返回非零退出码
      if (error.stdout) {
        try {
          const outdated = JSON.parse(error.stdout)
          Object.entries(outdated).forEach(([name, info]) => {
            this.results.outdated.push({
              package: name,
              current: info.current,
              wanted: info.wanted,
              latest: info.latest,
              type: info.type,
              homepage: info.homepage
            })
          })
        } catch (parseError) {
          console.warn('   无法解析过时依赖检查结果')
        }
      }
    }
    
    console.log(`   发现 ${this.results.outdated.length} 个过时依赖`)
  }

  /**
   * 兼容性检查
   */
  async checkCompatibility(packageJson) {
    console.log('🔧 检查兼容性...')
    
    const dependencies = {
      ...packageJson.dependencies,
      ...packageJson.devDependencies
    }
    
    // 检查 Node.js 版本兼容性
    if (packageJson.engines && packageJson.engines.node) {
      const nodeVersion = process.version
      const requiredVersion = packageJson.engines.node
      
      if (!this.isVersionCompatible(nodeVersion, requiredVersion)) {
        this.results.compatibility.push({
          type: 'node',
          package: 'node',
          current: nodeVersion,
          required: requiredVersion,
          message: 'Node.js 版本不兼容'
        })
      }
    }
    
    // 检查关键依赖的兼容性
    const criticalDeps = ['vue', 'vite', 'element-plus']
    
    for (const dep of criticalDeps) {
      if (dependencies[dep]) {
        await this.checkPackageCompatibility(dep, dependencies[dep])
      }
    }
    
    console.log(`   发现 ${this.results.compatibility.length} 个兼容性问题`)
  }

  /**
   * 检查重复依赖
   */
  async checkDuplicates() {
    console.log('🔍 检查重复依赖...')
    
    try {
      const lsResult = execSync('npm ls --json --all', { 
        encoding: 'utf8',
        stdio: ['pipe', 'pipe', 'pipe']
      })
      
      const tree = JSON.parse(lsResult)
      const duplicates = this.findDuplicates(tree)
      
      this.results.duplicates = duplicates
      console.log(`   发现 ${duplicates.length} 个重复依赖`)
      
    } catch (error) {
      console.warn('   无法检查重复依赖')
    }
  }

  /**
   * 检查单个包的兼容性
   */
  async checkPackageCompatibility(packageName, version) {
    try {
      // 这里可以实现更复杂的兼容性检查逻辑
      // 例如检查 peerDependencies、engines 等
      
      // 简化实现：检查是否为预发布版本
      if (version.includes('alpha') || version.includes('beta') || version.includes('rc')) {
        this.results.compatibility.push({
          type: 'prerelease',
          package: packageName,
          version: version,
          message: '使用了预发布版本，可能不稳定'
        })
      }
      
    } catch (error) {
      console.warn(`   无法检查 ${packageName} 的兼容性`)
    }
  }

  /**
   * 查找重复依赖
   */
  findDuplicates(tree, packages = new Map(), duplicates = []) {
    if (!tree.dependencies) return duplicates
    
    Object.entries(tree.dependencies).forEach(([name, info]) => {
      const version = info.version
      
      if (packages.has(name)) {
        const existingVersions = packages.get(name)
        if (!existingVersions.includes(version)) {
          existingVersions.push(version)
          duplicates.push({
            package: name,
            versions: [...existingVersions]
          })
        }
      } else {
        packages.set(name, [version])
      }
      
      // 递归检查子依赖
      this.findDuplicates(info, packages, duplicates)
    })
    
    return duplicates
  }

  /**
   * 版本兼容性检查
   */
  isVersionCompatible(current, required) {
    // 简化的版本检查，实际应该使用 semver 库
    const currentMajor = parseInt(current.split('.')[0].replace('v', ''))
    const requiredMajor = parseInt(required.split('.')[0].replace('>=', '').replace('^', '').replace('~', ''))
    
    return currentMajor >= requiredMajor
  }

  /**
   * 生成检查报告
   */
  generateReport() {
    console.log('\n📊 依赖检查报告')
    console.log('=' .repeat(50))
    
    // 安全问题
    if (this.results.security.length > 0) {
      console.log('\n🔒 安全漏洞:')
      this.results.security.forEach(vuln => {
        const severity = this.getSeverityIcon(vuln.severity)
        console.log(`   ${severity} ${vuln.package}: ${vuln.title}`)
        if (vuln.fixAvailable) {
          console.log(`      💡 修复: npm audit fix`)
        }
      })
    } else {
      console.log('\n✅ 未发现安全漏洞')
    }
    
    // 过时依赖
    if (this.results.outdated.length > 0) {
      console.log('\n📦 过时依赖:')
      this.results.outdated.slice(0, 10).forEach(pkg => {
        console.log(`   📈 ${pkg.package}: ${pkg.current} → ${pkg.latest}`)
      })
      
      if (this.results.outdated.length > 10) {
        console.log(`   ... 还有 ${this.results.outdated.length - 10} 个过时依赖`)
      }
      
      console.log('\n   💡 更新建议: npm update')
    } else {
      console.log('\n✅ 所有依赖都是最新的')
    }
    
    // 兼容性问题
    if (this.results.compatibility.length > 0) {
      console.log('\n🔧 兼容性问题:')
      this.results.compatibility.forEach(issue => {
        console.log(`   ⚠️  ${issue.package}: ${issue.message}`)
      })
    } else {
      console.log('\n✅ 未发现兼容性问题')
    }
    
    // 重复依赖
    if (this.results.duplicates && this.results.duplicates.length > 0) {
      console.log('\n🔍 重复依赖:')
      this.results.duplicates.slice(0, 5).forEach(dup => {
        console.log(`   📦 ${dup.package}: ${dup.versions.join(', ')}`)
      })
      
      if (this.results.duplicates.length > 5) {
        console.log(`   ... 还有 ${this.results.duplicates.length - 5} 个重复依赖`)
      }
      
      console.log('\n   💡 优化建议: npm dedupe')
    }
    
    // 总结
    console.log('\n📋 总结:')
    console.log(`   🔒 安全问题: ${this.results.security.length}`)
    console.log(`   📦 过时依赖: ${this.results.outdated.length}`)
    console.log(`   🔧 兼容性问题: ${this.results.compatibility.length}`)
    console.log(`   🔍 重复依赖: ${this.results.duplicates?.length || 0}`)
    
    // 建议操作
    this.generateRecommendations()
    
    // 保存报告到文件
    this.saveReport()
  }

  /**
   * 生成修复建议
   */
  generateRecommendations() {
    console.log('\n💡 修复建议:')
    
    const recommendations = []
    
    if (this.results.security.length > 0) {
      recommendations.push('npm audit fix  # 修复安全漏洞')
    }
    
    if (this.results.outdated.length > 0) {
      recommendations.push('npm update     # 更新过时依赖')
    }
    
    if (this.results.duplicates && this.results.duplicates.length > 0) {
      recommendations.push('npm dedupe     # 去除重复依赖')
    }
    
    recommendations.push('npm ci         # 清理安装')
    
    if (recommendations.length > 0) {
      recommendations.forEach(cmd => {
        console.log(`   ${cmd}`)
      })
    } else {
      console.log('   ✅ 依赖状态良好，无需特殊操作')
    }
  }

  /**
   * 获取严重程度图标
   */
  getSeverityIcon(severity) {
    switch (severity) {
      case 'critical': return '🚨'
      case 'high': return '🔴'
      case 'moderate': return '🟡'
      case 'low': return '🟢'
      default: return '❓'
    }
  }

  /**
   * 保存报告到文件
   */
  saveReport() {
    const reportPath = path.join(process.cwd(), 'dependency-report.json')
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        security: this.results.security.length,
        outdated: this.results.outdated.length,
        compatibility: this.results.compatibility.length,
        duplicates: this.results.duplicates?.length || 0
      },
      details: this.results
    }
    
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
    console.log(`\n📄 详细报告已保存到: ${reportPath}`)
  }
}

/**
 * 自动修复脚本
 */
class DependencyFixer {
  constructor() {
    this.checker = new DependencyChecker()
  }

  /**
   * 自动修复依赖问题
   */
  async autoFix() {
    console.log('🔧 开始自动修复依赖问题...')
    
    try {
      // 先运行检查
      await this.checker.run()
      
      // 修复安全漏洞
      if (this.checker.results.security.length > 0) {
        console.log('\n🔒 修复安全漏洞...')
        execSync('npm audit fix', { stdio: 'inherit' })
      }
      
      // 去除重复依赖
      if (this.checker.results.duplicates && this.checker.results.duplicates.length > 0) {
        console.log('\n🔍 去除重复依赖...')
        execSync('npm dedupe', { stdio: 'inherit' })
      }
      
      // 清理安装
      console.log('\n🧹 清理安装...')
      execSync('npm ci', { stdio: 'inherit' })
      
      console.log('\n✅ 自动修复完成')
      
    } catch (error) {
      console.error('❌ 自动修复失败:', error.message)
      process.exit(1)
    }
  }
}

// 命令行接口
if (require.main === module) {
  const args = process.argv.slice(2)
  const command = args[0] || 'check'
  
  switch (command) {
    case 'check':
      new DependencyChecker().run()
      break
      
    case 'fix':
      new DependencyFixer().autoFix()
      break
      
    case 'help':
    default:
      console.log(`
依赖管理工具

用法:
  node dependency-check.js [command]

命令:
  check    检查依赖问题（默认）
  fix      自动修复依赖问题
  help     显示帮助信息
`)
      break
  }
}

module.exports = {
  DependencyChecker,
  DependencyFixer
}