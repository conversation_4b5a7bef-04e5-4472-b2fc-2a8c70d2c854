#!/usr/bin/env python3
"""
模拟数据迁移脚本

此脚本帮助开发者从旧的硬编码模拟数据迁移到新的统一模拟数据管理系统。
它会扫描代码库中的硬编码模拟数据，并提供迁移建议。

使用方法:
    python scripts/migrate_mock_data.py
"""

import os
import re
import sys
from pathlib import Path
from typing import List, Dict, Tuple


class MockDataMigrator:
    """模拟数据迁移器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.patterns = {
            'mock_functions': [
                r'def\s+generate_mock_\w+\s*\(',
                r'def\s+get_mock_\w+\s*\(',
                r'def\s+mock_\w+\s*\(',
            ],
            'hardcoded_data': [
                r'return\s*{[^}]*"[^"]*":\s*\d+',
                r'mock_data\s*=\s*{',
                r'fake_data\s*=\s*{',
                r'test_data\s*=\s*{',
            ],
            'random_usage': [
                r'random\.(randint|uniform|choice)\(',
                r'import\s+random',
                r'from\s+random\s+import',
            ]
        }
        
        self.exclude_dirs = {
            '__pycache__',
            '.git',
            'node_modules',
            'venv',
            'env',
            '.pytest_cache',
            'tests'  # 测试文件可能包含合法的模拟数据
        }
        
        self.exclude_files = {
            'mock_data_manager.py',  # 新的统一管理器
            'migrate_mock_data.py',  # 本脚本
            'test_mock_data_manager.py'  # 测试文件
        }
    
    def scan_files(self) -> List[Path]:
        """扫描Python文件"""
        python_files = []
        
        for root, dirs, files in os.walk(self.project_root):
            # 排除特定目录
            dirs[:] = [d for d in dirs if d not in self.exclude_dirs]
            
            for file in files:
                if file.endswith('.py') and file not in self.exclude_files:
                    python_files.append(Path(root) / file)
        
        return python_files
    
    def analyze_file(self, file_path: Path) -> Dict[str, List[Tuple[int, str]]]:
        """分析单个文件中的模拟数据模式"""
        results = {pattern_type: [] for pattern_type in self.patterns.keys()}
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                for pattern_type, patterns in self.patterns.items():
                    for pattern in patterns:
                        if re.search(pattern, line, re.IGNORECASE):
                            results[pattern_type].append((line_num, line.strip()))
        
        except Exception as e:
            print(f"警告: 无法读取文件 {file_path}: {e}")
        
        return results
    
    def generate_migration_suggestions(self, file_path: Path, analysis: Dict[str, List[Tuple[int, str]]]) -> List[str]:
        """生成迁移建议"""
        suggestions = []
        
        if analysis['mock_functions']:
            suggestions.append(
                "建议: 将模拟数据生成函数迁移到 MockDataManager 类中"
            )
            suggestions.append(
                "示例: 在 mock_data_manager.py 中添加相应的方法"
            )
        
        if analysis['hardcoded_data']:
            suggestions.append(
                "建议: 将硬编码的模拟数据移动到 MockDataManager 中"
            )
            suggestions.append(
                "示例: 使用 get_mock_xxx_data() 函数替代硬编码数据"
            )
        
        if analysis['random_usage']:
            suggestions.append(
                "建议: 将随机数生成逻辑集中到 MockDataManager 中"
            )
            suggestions.append(
                "示例: 在 MockDataManager 中统一管理随机数种子和生成逻辑"
            )
        
        return suggestions
    
    def run_migration_analysis(self) -> None:
        """运行迁移分析"""
        print("开始扫描模拟数据迁移机会...")
        print(f"项目根目录: {self.project_root}")
        print("-" * 60)
        
        files = self.scan_files()
        total_issues = 0
        files_with_issues = 0
        
        for file_path in files:
            analysis = self.analyze_file(file_path)
            
            # 检查是否有任何模式匹配
            has_issues = any(matches for matches in analysis.values())
            
            if has_issues:
                files_with_issues += 1
                relative_path = file_path.relative_to(self.project_root)
                print(f"\n📁 文件: {relative_path}")
                
                for pattern_type, matches in analysis.items():
                    if matches:
                        print(f"\n  🔍 {pattern_type.replace('_', ' ').title()}:")
                        for line_num, line in matches:
                            print(f"    第 {line_num} 行: {line[:80]}{'...' if len(line) > 80 else ''}")
                            total_issues += 1
                
                suggestions = self.generate_migration_suggestions(file_path, analysis)
                if suggestions:
                    print(f"\n  💡 迁移建议:")
                    for suggestion in suggestions:
                        print(f"    • {suggestion}")
                
                print("-" * 40)
        
        print(f"\n📊 扫描总结:")
        print(f"  • 扫描文件数: {len(files)}")
        print(f"  • 发现问题文件数: {files_with_issues}")
        print(f"  • 总问题数: {total_issues}")
        
        if files_with_issues > 0:
            print(f"\n🚀 下一步行动:")
            print(f"  1. 查看 MockDataManager 类的实现")
            print(f"  2. 将硬编码的模拟数据迁移到统一管理器")
            print(f"  3. 更新相关的导入语句")
            print(f"  4. 运行测试确保功能正常")
            print(f"  5. 更新环境变量配置")
        else:
            print(f"\n✅ 恭喜! 没有发现需要迁移的模拟数据")
    
    def generate_migration_template(self, output_file: str = "migration_template.py") -> None:
        """生成迁移模板"""
        template = '''
# 模拟数据迁移模板
# 使用此模板将旧的模拟数据迁移到新的统一管理系统

from app.core.mock_data_manager import (
    is_mock_enabled,
    get_mock_dashboard_stats,
    get_mock_weight_data,
    get_mock_bp_data,
    get_mock_service_stats,
    # 添加其他需要的导入
)

# 迁移前的代码示例:
# def old_mock_function():
#     return {
#         "data": "hardcoded_value",
#         "count": 123
#     }

# 迁移后的代码示例:
def new_mock_function():
    # 首先尝试使用统一的模拟数据管理器
    mock_data = get_mock_dashboard_stats()
    if mock_data:
        return mock_data
    
    # 如果模拟数据不可用，提供回退逻辑
    return {
        "data": "default_value",
        "count": 0
    }

# 迁移检查清单:
# □ 将硬编码数据移动到 MockDataManager
# □ 更新导入语句
# □ 添加环境变量检查
# □ 提供回退机制
# □ 更新测试用例
# □ 更新文档
'''
        
        output_path = self.project_root / output_file
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(template)
        
        print(f"\n📝 迁移模板已生成: {output_path}")


def main():
    """主函数"""
    # 获取项目根目录
    script_dir = Path(__file__).parent
    project_root = script_dir.parent  # 假设脚本在 scripts/ 目录下
    
    if len(sys.argv) > 1:
        project_root = Path(sys.argv[1])
    
    if not project_root.exists():
        print(f"错误: 项目目录不存在: {project_root}")
        sys.exit(1)
    
    migrator = MockDataMigrator(str(project_root))
    
    print("🔄 模拟数据迁移分析工具")
    print("=" * 60)
    
    # 运行分析
    migrator.run_migration_analysis()
    
    # 生成迁移模板
    migrator.generate_migration_template()
    
    print("\n✨ 分析完成!")
    print("\n📚 更多信息请参考: docs/MOCK_DATA_GUIDE.md")


if __name__ == "__main__":
    main()