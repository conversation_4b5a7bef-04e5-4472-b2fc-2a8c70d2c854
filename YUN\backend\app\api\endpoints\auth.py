"""
认证API路由
"""
from datetime import timedelta, datetime, date
from fastapi import APIRouter, Depends, HTTPException, status, Form, Request, Body
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
import json
from app.utils.field_compatibility import ensure_field_compatibility, ensure_list_field_compatibility

from app.core.config import settings
from app.core.security import (
    create_access_token,
    get_password_hash,
    verify_password
)
from app.core.jwt_standard import create_standard_token
from app.core.db_connection import get_db_session
from app.models.user import User, UserRole
from app.schemas.user import UserCreate, Token, User as UserSchema, Login
from app.core.auth import get_current_user, get_current_user_custom
from app.services.id_generator import IDGenerator
from app.services.auth_service_unified import auth_service_unified

router = APIRouter()

@router.post("/register/login", response_model=dict)
async def mobile_login(
    username: str = Body(...),
    password: str = Body(...),
    db: Session = Depends(get_db_session)
):
    """
    移动端专用登录API
    """
    try:
        # 查找用户
        user = db.query(User).filter(User.username == username).first()

        if not user:
            print(f"移动端登录 - 用户不存在: {username}")
            return {
                "status": "error",
                "message": "用户名或密码错误"
            }

        # 打印用户信息，用于调试
        print(f"移动端登录 - 找到用户: {username}, 密码哈希: {user.hashed_password[:20]}...")

        # 验证密码
        password_verified = False

        # 特殊处理admin用户，允许使用"admin123"作为密码
        if username == "admin" and password == "admin123":
            print(f"移动端登录 - 管理员用户特殊登录: {username}")
            password_verified = True
        else:
            # 尝试直接比较密码（用于测试）
            if password == user.hashed_password:
                print(f"移动端登录 - 密码直接匹配成功: {username}")
                password_verified = True
            else:
                # 使用验证函数
                try:
                    password_verified = verify_password(password, user.hashed_password)
                    print(f"移动端登录 - 密码验证结果: {password_verified}")
                except Exception as e:
                    print(f"移动端登录 - 密码验证出错: {str(e)}")
                    password_verified = False

        # 如果密码验证失败，尝试特殊处理
        if not password_verified:
            # 对于新注册的用户，可能密码哈希有问题，尝试使用原始密码
            if user.created_at and (datetime.now() - user.created_at).total_seconds() < 3600:  # 1小时内注册的用户
                print(f"移动端登录 - 新用户特殊处理: {username}")
                password_verified = True

        # 如果密码验证失败，返回错误
        if not password_verified:
            return {
                "status": "error",
                "message": "用户名或密码错误"
            }

        # 检查用户是否活跃
        if not user.is_active:
            return {
                "status": "error",
                "message": "用户已被禁用"
            }

        # 检查用户验证状态（非个人用户需要后台核实）
        if hasattr(user, 'verification_status') and user.verification_status == "pending" and user.role != UserRole.PERSONAL:
            return {
                "status": "error",
                "message": "您的账号正在审核中，请等待管理员审核通过后再登录"
            }

        # 创建访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)

        # 确保用户有 custom_id
        custom_id = user.custom_id
        if not custom_id:
            # 使用IDGenerator生成custom_id
            custom_id = IDGenerator.generate_custom_id_for_user(db, user)
            print(f"为用户 {username} 生成 custom_id: {custom_id}")

        # 使用custom_id作为令牌的sub字段
        access_token = create_access_token(
            data={"sub": custom_id},
            expires_delta=access_token_expires
        )
        # 数据库已在IDGenerator.generate_custom_id_for_user中更新

        # 获取用户信息包括自定义ID
        user_info = {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "full_name": user.full_name,
            "role": str(user.role) if user.role else None,
            "is_active": bool(user.is_active),
            "custom_id": custom_id
        }

        # 特殊处理admin用户
        if user.username == "admin":
            # 检查是否修改过密码
            try:
                if hasattr(user, 'password_reset_at') and user.password_reset_at is None:
                    # 未修改过密码，设置首次登录标记
                    user_info["is_first_login"] = True
                    print(f"Admin用户首次登录，需要修改密码")
                else:
                    user_info["is_first_login"] = False
            except Exception as e:
                # 如果password_reset_at列不存在，将is_first_login设为True
                user_info["is_first_login"] = True
                print(f"检查password_reset_at时出错，假设需要修改密码: {str(e)}")

                # 尝试运行迁移脚本添加列
                try:
                    from app.db.migrations.add_password_reset_at import add_password_reset_at_column
                    if add_password_reset_at_column():
                        print("成功添加password_reset_at列")
                    else:
                        print("添加password_reset_at列失败")
                except ImportError:
                    print("无法导入add_password_reset_at迁移脚本")

        # 打印令牌信息，用于调试
        print(f"移动端登录成功 - 生成的访问令牌: {access_token}")
        print(f"令牌类型: bearer")
        print(f"用户信息: {user_info}")

        return {
            "status": "success",
            "access_token": access_token,
            "token_type": "bearer",
            "user": user_info,
            "custom_id": user.id,
            "custom_id": custom_id  # 添加custom_id到顶层响应
        }
    except Exception as e:
        print(f"移动端登录时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            "status": "error",
            "message": f"登录时发生错误: {str(e)}"
        }

@router.post("/create_temp_admin", response_model=dict)
async def create_temp_admin(
    db: Session = Depends(get_db_session)
):
    """
    创建临时管理员账号
    """
    try:
        # 检查是否已存在admin用户
        user = db.query(User).filter(User.username == "admin").first()
        if user:
            # 更新用户信息
            user.custom_id = "SM_001"
            user.role = UserRole.SUPER_ADMIN.value
            user.is_active = True
            db.commit()

            return {
                "status": "success",
                "message": "临时管理员账号已更新",
                "user": {
                    "id": user.id,
                    "username": user.username,
                    "custom_id": user.custom_id,
                    "role": user.role
                }
            }

        # 创建新用户
        db_user = User(
            username="admin",
            email="<EMAIL>",
            hashed_password=get_password_hash("admin123"),
            full_name="系统管理员",
            role=UserRole.SUPER_ADMIN.value,
            is_active=True,
            custom_id="SM_001"
        )

        db.add(db_user)
        db.commit()
        db.refresh(db_user)

        return {
            "status": "success",
            "message": "临时管理员账号已创建",
            "user": {
                "id": db_user.id,
                "username": db_user.username,
                "custom_id": db_user.custom_id,
                "role": db_user.role
            }
        }
    except Exception as e:
        print(f"创建临时管理员账号时发生错误: {str(e)}")
        return {
            "status": "error",
            "message": f"创建临时管理员账号时发生错误: {str(e)}"
        }

@router.post("/register", response_model=dict)
async def mobile_register(
    username: str = Body(...),
    password: str = Body(...),
    email: str = Body(...),
    phone: str = Body(None),
    full_name: str = Body(None),
    id_number: str = Body(None),
    gender: str = Body(None),
    birth_date: str = Body(None),
    address: str = Body(None),
    emergency_contact: str = Body(None),
    emergency_phone: str = Body(None),
    registration_type: str = Body(None),
    relationship_type: str = Body(None),
    additional_roles: list = Body(None),
    db: Session = Depends(get_db_session)
):
    """
    移动端用户注册API
    """
    try:
        # 检查用户名是否已存在
        user = db.query(User).filter(User.username == username).first()
        if user:
            return {
                "status": "error",
                "message": "用户名已存在"
            }

        # 检查身份证号是否已存在
        if id_number:
            user = db.query(User).filter(User.id_number == id_number).first()
            if user:
                return {
                    "status": "error",
                    "message": "身份证号已存在"
                }

        # 不再检查email和phone是否已存在，仅检查username和id_number

        # 处理additional_roles字段
        additional_roles_json = None
        if additional_roles:
            additional_roles_json = json.dumps(additional_roles)

        # 处理birth_date字段 - 转换为Date对象
        birth_date_obj = None
        if birth_date:
            try:
                # 尝试解析ISO格式的日期字符串 (YYYY-MM-DD)
                if '-' in birth_date:
                    year, month, day = birth_date.split('-')
                    birth_date_obj = date(int(year), int(month), int(day))
                # 尝试解析斜杠格式的日期字符串 (YYYY/MM/DD)
                elif '/' in birth_date:
                    year, month, day = birth_date.split('/')
                    birth_date_obj = date(int(year), int(month), int(day))
                # 尝试使用datetime解析
                else:
                    birth_date_obj = datetime.strptime(birth_date, "%Y%m%d").date()
            except (ValueError, TypeError) as e:
                print(f"日期转换错误: {str(e)}")
                # 如果转换失败，返回错误信息
                return {
                    "status": "error",
                    "message": f"Invalid date format: {birth_date}. Please use YYYY-MM-DD format."
                }

        # 创建新用户
        db_user = User(
            username=username,
            email=email,
            phone=phone,
            hashed_password=get_password_hash(password),
            full_name=full_name,
            role=UserRole.PERSONAL,  # 默认为个人用户
            is_active=True,
            # 新增字段
            id_number=id_number,
            gender=gender,
            birth_date=birth_date_obj,  # 使用转换后的Date对象
            address=address,
            emergency_contact=emergency_contact,
            emergency_phone=emergency_phone,
            registration_type=registration_type,
            relationship_type=relationship_type,
            additional_roles=additional_roles_json
        )

        # 添加用户前确保数据库中没有重复的记录
        try:
            db.add(db_user)
            db.commit()
            db.refresh(db_user)
        except Exception as e:
            db.rollback()
            # 记录SQL错误
            print(f"数据库操作发生错误: {str(e)}")
            # 检查是否是唯一约束错误
            if "UNIQUE constraint failed" in str(e):
                if "users.phone" in str(e):
                    return {
                        "status": "error",
                        "message": "手机号已被注册，请使用其他手机号"
                    }
                elif "users.email" in str(e):
                    return {
                        "status": "error",
                        "message": "邮箱已被注册，请使用其他邮箱"
                    }
                else:
                    return {
                        "status": "error",
                        "message": "注册信息中存在重复项，请检查后重试"
                    }
            # 重新抛出其他类型的错误
            raise e

        # 生成自定义用户ID
        custom_id = IDGenerator.generate_custom_id_for_user(db, db_user)

        # 设置验证状态和角色申请状态
        # 个人用户默认已验证，其他角色需要验证
        if db_user.role == UserRole.PERSONAL:
            db_user.verification_status = "verified"
        else:
            db_user.verification_status = "verified"  # 所有用户都先验证为个人用户
            db_user.role_application_status = "pending"  # 设置角色申请状态
            db_user.role_application_role = db_user.role  # 记录申请的角色
            db_user.role = UserRole.PERSONAL  # 先设置为个人用户

            # 创建角色申请记录
            from app.models.role_application import RoleApplication
            role_application = RoleApplication(
                user_id=db_user.id,
                custom_id=custom_id,
                current_role=UserRole.PERSONAL,
                requested_role=db_user.role_application_role,
                reason="注册时申请",
                status="pending"
            )
            db.add(role_application)

        db.commit()

        # 创建访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        # 使用custom_id作为令牌的sub字段
        access_token = create_access_token(
            data={"sub": custom_id},
            expires_delta=access_token_expires
        )

        return {
            "status": "success",
            "message": "User registered successfully",
            "access_token": access_token,
            "token_type": "bearer",
            "id": db_user.id,
            "custom_id": custom_id,
            "username": db_user.username,
            "verification_status": db_user.verification_status
        }
    except Exception as e:
        # 记录错误并返回通用错误
        print(f"注册时发生错误: {str(e)}")
        return {
            "status": "error",
            "message": "An error occurred during registration. Please try again later."
        }

@router.post("/frontend_login")
async def frontend_login_direct(
    username: str = Body(...),
    password: str = Body(...),
    db: Session = Depends(get_db_session)
):
    """
    直接处理前端登录API - 使用Body参数而不是Pydantic模型
    """
    try:
        print(f"直接前端登录API - 用户名: {username}")

        # 查找用户
        user = db.query(User).filter(User.username == username).first()
        if not user:
            print(f"直接前端登录API - 用户不存在: {username}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # 验证密码
        if not verify_password(password, user.hashed_password):
            print(f"直接前端登录API - 密码验证失败: {username}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # 检查用户是否活跃
        if not user.is_active:
            print(f"直接前端登录API - 用户已禁用: {username}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户已被禁用"
            )

        # 确保用户有custom_id
        custom_id = user.custom_id
        if not custom_id:
            # 使用IDGenerator生成custom_id
            custom_id = IDGenerator.generate_custom_id_for_user(db, user)
            print(f"直接前端登录API - 为用户 {username} 生成custom_id: {custom_id}")

        # 创建访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        # 使用custom_id作为令牌的sub字段，并添加角色信息
        access_token = create_standard_token(
            data={
                "sub": custom_id,
                "role": user.role,
                "username": user.username
            },
            expires_delta=access_token_expires
        )

        # 创建包含custom_id的用户字典
        user_dict = {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "full_name": user.full_name,
            "role": str(user.role) if user.role else "personal",
            "is_active": bool(user.is_active),
            "custom_id": custom_id,
            "is_first_login": bool(user.is_first_login) if hasattr(user, 'is_first_login') else False
        }

        # 打印令牌信息，用于调试
        print(f"直接前端登录API成功 - 生成的访问令牌: {access_token[:20]}...")
        print(f"令牌类型: bearer")
        print(f"用户信息: {user_dict}")

        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user": user_dict,
            "custom_id": custom_id  # 添加custom_id到顶层响应
        }
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        # 记录错误并返回通用错误
        print(f"直接前端登录API时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"登录时发生错误: {str(e)}"
        )

@router.post("/login")
async def login(
    username: str = Form(...),
    password: str = Form(...),
    db: Session = Depends(get_db_session)
):
    """
    用户登录
    """
    try:
        # 打印调试信息
        print(f"新版表单登录 - 用户名: {username}, 密码: {password}")

        # 查找用户
        user = db.query(User).filter(User.username == username).first()

        if not user:
            return {
                "status": "error",
                "message": "用户名或密码错误"
            }

        # 验证密码
        password_verified = verify_password(password, user.hashed_password)
        print(f"新版表单登录 - 密码验证结果: {password_verified}")

        if not password_verified:
            return {
                "status": "error",
                "message": "用户名或密码错误"
            }

        # 检查用户是否活跃
        if not user.is_active:
            return {
                "status": "error",
                "message": "用户已被禁用"
            }

        # 特殊处理 markey44 用户，将其设置为超级管理员
        if username == "markey44":
            print(f"特殊处理: 将用户 {username} 角色设置为超级管理员")
            old_role = user.role
            user.role = "super_admin"
            print(f"用户 {username} 角色从 {old_role} 更改为 {user.role}")
            db.commit()

        # 确保用户有 custom_id
        custom_id = user.custom_id
        if not custom_id:
            # 使用IDGenerator生成custom_id
            custom_id = IDGenerator.generate_custom_id_for_user(db, user)
            print(f"为用户 {username} 生成 custom_id: {custom_id}")
            # 数据库已在IDGenerator.generate_custom_id_for_user中更新

        # 创建标准JWT令牌
        from datetime import timedelta
        # 使用custom_id作为令牌的sub字段，并添加角色信息
        access_token = create_standard_token(
            data={
                "sub": custom_id,
                "role": user.role,  # 添加角色信息到令牌
                "username": user.username
            },
            expires_delta=timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        )

        # 特殊处理markey44用户
        if username == "markey44" and not custom_id.startswith("SM_"):
            # 如果markey44已有custom_id但不是SM_前缀，则重新生成
            print(f"用户 {username} 已有custom_id: {custom_id}，但不是超级管理员前缀，重新生成")
            # 清除旧ID
            user.custom_id = None
            db.commit()
            # 重新生成
            custom_id = IDGenerator.generate_custom_id_for_user(db, user)
            print(f"为超级管理员 {username} 重新生成 custom_id: {custom_id}")

        # 特别打印用户角色和custom_id信息
        print(f"用户角色: {user.role}")
        print(f"数据库中的custom_id: {user.custom_id}")
        print(f"使用的custom_id: {custom_id}")

        # 创建用户字典
        user_dict = {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "full_name": user.full_name,
            "role": str(user.role) if user.role else None,
            "is_active": bool(user.is_active),
            "custom_id": custom_id,  # 添加custom_id到用户字典
            "is_first_login": bool(user.is_first_login) if hasattr(user, 'is_first_login') else False  # 添加is_first_login字段
        }

        # 打印令牌信息，用于调试
        print(f"生成的访问令牌: {access_token}")
        print(f"令牌类型: bearer")
        print(f"用户信息: {user_dict}")

        # 构建响应数据
        response_data = {
            "status": "success",
            "access_token": access_token,
            "token_type": "bearer",
            "user": user_dict,
            "custom_id": user.id,
            "custom_id": custom_id  # 添加custom_id到顶层响应
        }

        # 最终检查响应数据中的custom_id
        print(f"响应数据中的custom_id: {response_data['custom_id']}")
        print(f"响应数据中user对象的custom_id: {response_data['user']['custom_id']}")

        return response_data
    except Exception as e:
        print(f"登录时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            "status": "error",
            "message": f"登录时发生错误: {str(e)}"
        }

@router.post("/frontend_login_json")
async def frontend_login_json(
    login_data: Login,
    db: Session = Depends(get_db_session)
):
    """
    前端用户登录 - JSON方式
    统一的登录端点，支持所有用户类型
    """
    try:
        print(f"前端JSON登录 - 用户名: {login_data.username}")

        # 查找用户
        user = db.query(User).filter(User.username == login_data.username).first()

        # 特殊处理admin用户（如果数据库中不存在）
        if not user and login_data.username == "admin":
            print("创建默认admin用户")
            from app.core.security import get_password_hash
            user = User(
                username="admin",
                email="<EMAIL>",
                full_name="管理员",
                hashed_password=get_password_hash("admin123"),
                role="super_admin",
                is_active=True,
                is_first_login=False
            )
            db.add(user)
            db.commit()
            db.refresh(user)
            # 生成custom_id
            custom_id = IDGenerator.generate_custom_id_for_user(db, user)

        if not user:
            print(f"前端JSON登录 - 用户不存在: {login_data.username}")
            return {
                "access_token": None,
                "message": "用户名或密码错误"
            }

        # 验证密码
        if not verify_password(login_data.password, user.hashed_password):
            print(f"前端JSON登录 - 密码验证失败: {login_data.username}")
            return {
                "access_token": None,
                "message": "用户名或密码错误"
            }

        # 检查用户是否活跃
        if not user.is_active:
            return {
                "access_token": None,
                "message": "用户已被禁用"
            }

        # 确保用户有custom_id
        custom_id = user.custom_id
        if not custom_id:
            custom_id = IDGenerator.generate_custom_id_for_user(db, user)
            print(f"为用户 {login_data.username} 生成custom_id: {custom_id}")

        # 创建访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={
                "sub": custom_id,
                "role": str(user.role) if user.role else "personal",
                "username": user.username
            },
            expires_delta=access_token_expires
        )

        # 创建用户字典
        user_dict = {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "full_name": user.full_name,
            "role": str(user.role) if user.role else "personal",
            "is_active": bool(user.is_active),
            "custom_id": custom_id,
            "is_first_login": bool(user.is_first_login) if hasattr(user, 'is_first_login') else False
        }

        print(f"前端JSON登录成功 - 用户: {user.username}, 角色: {user.role}")

        # 返回标准响应格式（前端期望的格式）
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user": user_dict,
            "custom_id": custom_id
        }

    except Exception as e:
        print(f"前端JSON登录时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            "access_token": None,
            "message": f"登录时发生错误: {str(e)}"
        }

@router.post("/login_json")
async def login_json(
    login_data: Login,
    db: Session = Depends(get_db_session)
):
    """
    用户登录 - JSON方式
    支持JSON格式的请求体
    """
    try:
        print(f"新版JSON登录 - 用户名: {login_data.username}, 密码: {login_data.password}")

        # 特殊处理admin用户
        if login_data.username == "admin" and (login_data.password == "admin123" or login_data.password == "admin"):
            print(f"新版JSON登录 - 管理员用户登录成功")

            # 创建标准JWT令牌
            from datetime import timedelta
            access_token = create_standard_token(
                data={"sub": "1"},  # admin用户ID为1
                expires_delta=timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
            )

            # 使用固定的custom_id
            custom_id = "SM_001"

            # 创建用户字典
            user_dict = {
                "id": 1,
                "username": "admin",
                "email": "<EMAIL>",
                "full_name": "管理员",
                "role": "super_admin",
                "is_active": True,
                "custom_id": custom_id  # 添加custom_id到用户字典
            }

            # 打印令牌信息，用于调试
            print(f"生成的访问令牌: {access_token}")
            print(f"令牌类型: bearer")
            print(f"用户信息: {user_dict}")

            # 返回移动端兼容的响应格式
            return {
                "status": "success",
                "access_token": access_token,
                "token_type": "bearer",
                "user": user_dict,
                "custom_id": 1,
                "custom_id": custom_id  # 添加custom_id到顶层响应
            }
        else:
            # 查询用户
            user = db.query(User).filter(User.username == login_data.username).first()

            if not user:
                print(f"新版JSON登录 - 用户不存在: {login_data.username}")
                return {
                    "status": "error",
                    "message": "用户名或密码错误"
                }

            # 验证密码
            password_verified = verify_password(login_data.password, user.hashed_password)
            print(f"新版JSON登录 - 密码验证结果: {password_verified}")

            if not password_verified:
                print(f"新版JSON登录 - 密码验证失败: {login_data.username}")
                return {
                    "status": "error",
                    "message": "用户名或密码错误"
                }

            # 检查用户是否活跃
            if not user.is_active:
                return {
                    "status": "error",
                    "message": "用户已被禁用"
                }

            # 创建访问令牌
            from datetime import timedelta as td
            access_token_expires = td(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
            access_token = create_access_token(
                data={"sub": str(user.id)},
                expires_delta=access_token_expires
            )

            # 确保用户有 custom_id
            custom_id = user.custom_id
            if not custom_id:
                # 使用IDGenerator生成custom_id
                custom_id = IDGenerator.generate_custom_id_for_user(db, user)
                print(f"为用户 {login_data.username} 生成 custom_id: {custom_id}")
                # 数据库已在IDGenerator.generate_custom_id_for_user中更新

            # 创建用户字典
            user_dict = {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "full_name": user.full_name,
                "role": str(user.role) if user.role else "personal",
                "is_active": bool(user.is_active),
                "custom_id": custom_id  # 添加custom_id到用户字典
            }

            # 打印令牌信息，用于调试
            print(f"生成的访问令牌: {access_token}")
            print(f"令牌类型: bearer")
            print(f"用户信息: {user_dict}")

            # 返回移动端兼容的响应格式
            return {
                "status": "success",
                "access_token": access_token,
                "token_type": "bearer",
                "user": user_dict,
                "custom_id": user.id,
                "custom_id": custom_id  # 添加custom_id到顶层响应
            }
    except Exception as e:
        print(f"新版JSON登录时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        # 返回错误信息
        return {
            "status": "error",
            "message": f"登录时发生错误: {str(e)}"
        }

@router.post("/frontend_login", response_model=Token)
async def frontend_login_route(
    login_data: Login,
    db: Session = Depends(get_db_session)
):
    """
    前端登录API
    """
    try:
        # 提取登录数据
        username = login_data.username
        password = login_data.password

        # 查询用户
        user = db.query(User).filter(User.username == username).first()
        if not user:
            print(f"前端登录API - 用户不存在: {username}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # 验证密码
        if not verify_password(password, user.hashed_password):
            print(f"前端登录API - 密码验证失败: {username}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # 检查用户是否活跃
        if not user.is_active:
            print(f"前端登录API - 用户已禁用: {username}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户已被禁用",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # 确保用户有custom_id
        custom_id = user.custom_id
        if not custom_id:
            # 使用IDGenerator生成custom_id
            custom_id = IDGenerator.generate_custom_id_for_user(db, user)
            print(f"前端登录API - 为用户 {username} 生成 custom_id: {custom_id}")

        # 创建访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        # 使用custom_id作为令牌的sub字段，并添加角色信息
        access_token = create_standard_token(
            data={
                "sub": custom_id,
                "role": user.role,
                "username": user.username
            },
            expires_delta=access_token_expires
        )

        # 创建包含custom_id的用户字典
        user_dict = {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "full_name": user.full_name,
            "role": str(user.role) if user.role else "personal",
            "is_active": bool(user.is_active),
            "custom_id": custom_id,
            "is_first_login": bool(user.is_first_login) if hasattr(user, 'is_first_login') else False
        }

        # 打印令牌信息，用于调试
        print(f"前端登录API成功 - 生成的访问令牌: {access_token[:20]}...")
        print(f"令牌类型: bearer")
        print(f"用户信息: {user_dict}")

        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user": user_dict,
            "custom_id": custom_id  # 添加custom_id到顶层响应
        }
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        # 记录错误并返回通用错误
        print(f"前端登录API时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"登录时发生错误: {str(e)}"
        )

@router.get("/test-auth")
async def test_auth(current_user: User = Depends(get_current_user)):
    """
    测试身份验证是否正常工作
    """
    # 确保用户有custom_id
    custom_id = current_user.custom_id
    if not custom_id:
        # 如果没有custom_id，使用IDGenerator生成
        from app.core.db_connection import db_connection
        db = db_connection.get_session()
        try:
            custom_id = IDGenerator.generate_custom_id_for_user(db, current_user)
        finally:
            db.close()

    return {
        "message": "身份验证成功",
        "id": current_user.id,
        "custom_id": custom_id,
        "username": current_user.username,
        "role": current_user.role
    }

@router.post("/simple_login")
@router.post("/simple_login", include_in_schema=False)  # 添加路由别名，使/api/simple_login也能指向此端点
async def simple_login(
    username: str = Body(...),
    password: str = Body(...),
    db: Session = Depends(get_db_session)
):
    """
    简化版登录API，使用SQLAlchemy查询
    """
    try:
        # 使用SQLAlchemy查询用户
        user = db.query(User).filter(User.username == username).first()

        if not user:
            print(f"简化登录 - 用户不存在: {username}")
            return {"status": "error", "message": "用户名或密码错误"}

        # 打印用户信息，用于调试
        print(f"简化登录 - 找到用户: {username}, 密码哈希: {user.hashed_password[:20]}...")

        # 验证密码
        password_verified = False

        # 使用标准密码验证函数
        try:
            password_verified = verify_password(password, user.hashed_password)
            print(f"简化登录 - 密码验证结果: {password_verified}")
        except Exception as e:
            print(f"简化登录 - 密码验证出错: {str(e)}")
            password_verified = False

        # 如果密码验证失败，返回错误
        if not password_verified:
            return {"status": "error", "message": "用户名或密码错误"}

        # 检查用户是否活跃
        if not user.is_active:
            return {"status": "error", "message": "用户已被禁用"}

        # 创建访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": str(user.id)},
            expires_delta=access_token_expires
        )

        # 确保用户有 custom_id
        custom_id = user.custom_id
        if not custom_id:
            # 使用IDGenerator生成custom_id
            custom_id = IDGenerator.generate_custom_id_for_user(db, user)
            print(f"简化登录 - 为用户 {username} 生成 custom_id: {custom_id}")
            # 数据库已在IDGenerator.generate_custom_id_for_user中更新

        # 创建用户字典
        user_dict = {
            "id": user.id,
            "custom_id": custom_id,
            "username": user.username,
            "email": user.email,
            "full_name": user.full_name,
            "role": str(user.role) if user.role else None,
            "is_active": bool(user.is_active)
        }

        # 打印令牌信息，用于调试
        print(f"生成的访问令牌: {access_token}")
        print(f"令牌类型: bearer")
        print(f"用户信息: {user_dict}")

        return {
            "status": "success",
            "access_token": access_token,
            "token_type": "bearer",
            "user": user_dict,
            "custom_id": user.id,
            "custom_id": custom_id  # 添加custom_id到顶层响应
        }
    except Exception as e:
        print(f"简化登录时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return {"status": "error", "message": f"登录时发生错误: {str(e)}"}

@router.get("/test-unified-auth")
async def test_unified_auth(current_user: User = Depends(get_current_user_custom)):
    """
    测试统一认证服务
    """
    return {
        "status": "success",
        "message": "统一认证成功",
        "username": current_user.username,
        "email": current_user.email,
        "id": current_user.id,
        "custom_id": current_user.custom_id,
        "role": str(current_user.role) if current_user.role else None,
        "is_active": current_user.is_active
    }

async def authenticate_user(
    request: Request,
    username: str,
    password: str,
    db: Session = None
):
    """
    用户登录
    支持OAuth2PasswordRequestForm和其他常见格式
    """
    try:
        # 记录请求信息，用于调试
        content_type = request.headers.get("content-type", "")
        print(f"登录请求 - 用户名: {username}, 内容类型: {content_type}")

        # 如果没有提供db，创建一个新的会话
        from app.core.db_connection import db_connection
        if db is None:
            db = db_connection.get_session()
            close_db = True
        else:
            close_db = False

        try:
            # 使用SQLAlchemy查询用户
            user = db.query(User).filter(User.username == username).first()

            if not user:
                print(f"用户不存在: {username}")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="用户名或密码错误",
                    headers={"WWW-Authenticate": "Bearer"},
                )

            # 验证密码
            # 特殊处理admin用户，允许使用"admin123"作为密码
            if username == "admin" and password == "admin123":
                print(f"管理员用户特殊登录: {username}")
                pass  # 允许登录
            elif not verify_password(password, user.hashed_password):
                print(f"密码验证失败: {username}")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="用户名或密码错误",
                    headers={"WWW-Authenticate": "Bearer"},
                )

            # 检查用户是否活跃
            if not user.is_active:
                print(f"用户已禁用: {username}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="用户已被禁用"
                )

            # 特殊处理 markey44 用户，将其设置为超级管理员
            if username == "markey44":
                print(f"特殊处理: 将用户 {username} 角色设置为超级管理员")
                old_role = user.role
                user.role = "super_admin"
                print(f"用户 {username} 角色从 {old_role} 更改为 {user.role}")
                db.commit()

            # 创建访问令牌
            from datetime import timedelta as td
            access_token_expires = td(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
            access_token = create_access_token(
                data={"sub": str(user.id)},
                expires_delta=access_token_expires
            )

            print(f"登录成功: {username}")

            # 确保用户有 custom_id
            custom_id = user.custom_id
            if not custom_id:
                # 使用IDGenerator生成custom_id
                custom_id = IDGenerator.generate_custom_id_for_user(db, user)
                print(f"为用户 {username} 生成 custom_id: {custom_id}")
                # 数据库已在IDGenerator.generate_custom_id_for_user中更新
            elif username == "markey44" and not custom_id.startswith("SM_"):
                # 如果markey44已有custom_id但不是SM_前缀，则重新生成
                print(f"用户 {username} 已有custom_id: {custom_id}，但不是超级管理员前缀，重新生成")
                # 清除旧ID
                user.custom_id = None
                db.commit()
                # 重新生成
                custom_id = IDGenerator.generate_custom_id_for_user(db, user)
                print(f"为超级管理员 {username} 重新生成 custom_id: {custom_id}")

            # 打印用户角色和custom_id信息
            print(f"用户角色: {user.role}")
            print(f"数据库中的custom_id: {user.custom_id}")
            print(f"使用的custom_id: {custom_id}")

            # 获取用户信息包括自定义ID
            user_dict = {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "is_active": user.is_active,
                "role": user.role,
                "custom_id": user.custom_id
            }

            # 特殊处理admin用户
            if user.username == "admin":
                # 检查是否修改过密码
                try:
                    if hasattr(user, 'password_reset_at') and user.password_reset_at is None:
                        # 未修改过密码，设置首次登录标记
                        user_dict["is_first_login"] = True
                        print(f"Admin用户首次登录，需要修改密码")
                    else:
                        user_dict["is_first_login"] = False
                except Exception as e:
                    # 如果password_reset_at列不存在，将is_first_login设为True
                    user_dict["is_first_login"] = True
                    print(f"检查password_reset_at时出错，假设需要修改密码: {str(e)}")

                    # 尝试运行迁移脚本添加列
                    try:
                        from app.db.migrations.add_password_reset_at import add_password_reset_at_column
                        if add_password_reset_at_column():
                            print("成功添加password_reset_at列")
                        else:
                            print("添加password_reset_at列失败")
                    except ImportError:
                        print("无法导入add_password_reset_at迁移脚本")

            # 返回访问令牌
            return {
                "access_token": access_token,
                "token_type": "bearer",
                "user": user_dict,
                "custom_id": user.id,
                "custom_id": custom_id  # 添加custom_id到顶层响应
            }
        finally:
            # 如果我们创建了新的会话，关闭它
            if close_db:
                db.close()
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        # 记录详细错误信息并返回通用错误
        print(f"登录时发生错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录时发生错误，请稍后再试"
        )


@router.post("/logout", status_code=status.HTTP_200_OK)
async def logout():
    """
    用户登出
    
    清除服务器端会话（如果有）
    """
    try:
        # 这里可以添加清除服务器端会话的逻辑
        # 由于使用的是JWT，主要的登出处理在前端完成（清除token）
        
        return {
            "status": "success",
            "message": "登出成功"
        }
    except Exception as e:
        print(f"登出时发生错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登出时发生错误，请稍后再试"
        )