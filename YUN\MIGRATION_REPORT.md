# 硬编码数据迁移报告

## 概述

- 扫描文件数: 263
- 发现硬编码数据: 2846 处

## 详细分析

### basic_connectivity_test.py

- 行 57: 考虑将 '{"endpoint": endpoint['name'], "status": "success"...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 60: 考虑将 '{"endpoint": endpoint['name'], "status": "success"...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 63: 考虑将 '{"endpoint": endpoint['name'], "status": "success"...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 66: 考虑将 '{"endpoint": endpoint['name'], "status": "failed",...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 70: 考虑将 '{"endpoint": endpoint['name'], "status": "error", ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 89: 考虑将 '{"status": "success", "note": "页面访问正常"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 92: 考虑将 '{"status": "warning", "note": "页面内容可能异常"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 95: 考虑将 '{"status": "failed", "status_code": response.statu...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 99: 考虑将 '{"status": "error", "error": str(e)}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 110: 考虑将 '{"status": "error", "error": "数据库文件不存在"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 143: 考虑将 '{
            "status": "success", 
            "t...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 151: 考虑将 '{"status": "error", "error": str(e)}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 169: 考虑将 '{frontend_result['status']}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 170: 考虑将 '{database_result['status']}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 174: 考虑将 '{result['status']}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 18: 考虑将 '[
        {
            "name": "API文档",
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 45: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 57: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 60: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 63: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 66: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 70: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 169: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 170: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 174: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 57: 考虑使用 dataMode.js 中的 API 替换 '{"endpoint": endpoint['name'], "status": "success"...'
- 行 60: 考虑使用 dataMode.js 中的 API 替换 '{"endpoint": endpoint['name'], "status": "success"...'
- 行 63: 考虑使用 dataMode.js 中的 API 替换 '{"endpoint": endpoint['name'], "status": "success"...'
- 行 66: 考虑使用 dataMode.js 中的 API 替换 '{"endpoint": endpoint['name'], "status": "failed",...'
- 行 70: 考虑使用 dataMode.js 中的 API 替换 '{"endpoint": endpoint['name'], "status": "error", ...'
- 行 89: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "note": "页面访问正常"}...'
- 行 92: 考虑使用 dataMode.js 中的 API 替换 '{"status": "warning", "note": "页面内容可能异常"}...'
- 行 95: 考虑使用 dataMode.js 中的 API 替换 '{"status": "failed", "status_code": response.statu...'
- 行 99: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "error": str(e)}...'
- 行 110: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "error": "数据库文件不存在"}...'
- 行 143: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success", 
            "t...'
- 行 151: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "error": str(e)}...'
- 行 169: 考虑使用 dataMode.js 中的 API 替换 '{frontend_result['status']}...'
- 行 170: 考虑使用 dataMode.js 中的 API 替换 '{database_result['status']}...'
- 行 174: 考虑使用 dataMode.js 中的 API 替换 '{result['status']}...'
- 行 18: 考虑使用 dataMode.js 中的 API 替换 '[
        {
            "name": "API文档",
         ...'
- 行 45: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 57: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 60: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 63: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 66: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 70: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 169: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 170: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 174: 考虑使用 dataMode.js 中的 API 替换 '['status']...'


### comprehensive_test.py

- 行 34: 考虑将 '{
            "test_name": test_name,
            ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 61: 考虑将 '[
            {
                "name": "API文档",
 ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 98: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 104: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 110: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 254: 考虑将 '[
            {
                "name": "聚合API健康检查...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 275: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 282: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 288: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 302: 考虑将 '[r for r in self.test_results if r["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 303: 考虑将 '[r for r in self.test_results if r["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 304: 考虑将 '[r for r in self.test_results if r["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 329: 考虑将 '[r for r in self.test_results if r["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 330: 考虑将 '[r for r in self.test_results if r["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 338: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 341: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 344: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 269: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 34: 考虑使用 dataMode.js 中的 API 替换 '{
            "test_name": test_name,
            ...'
- 行 61: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "name": "API文档",
 ...'
- 行 98: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 104: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 110: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 254: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "name": "聚合API健康检查...'
- 行 275: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 282: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 288: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 302: 考虑使用 dataMode.js 中的 API 替换 '[r for r in self.test_results if r["status"]...'
- 行 303: 考虑使用 dataMode.js 中的 API 替换 '[r for r in self.test_results if r["status"]...'
- 行 304: 考虑使用 dataMode.js 中的 API 替换 '[r for r in self.test_results if r["status"]...'
- 行 329: 考虑使用 dataMode.js 中的 API 替换 '[r for r in self.test_results if r["status"]...'
- 行 330: 考虑使用 dataMode.js 中的 API 替换 '[r for r in self.test_results if r["status"]...'
- 行 338: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'
- 行 341: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'
- 行 344: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'
- 行 269: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'


### create_admin_test_data.py

- 行 103: 考虑将 '{"test": "data"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 124: 考虑将 '{"test": "data"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 103: 考虑使用 dataMode.js 中的 API 替换 '{"test": "data"}...'
- 行 124: 考虑使用 dataMode.js 中的 API 替换 '{"test": "data"}...'
- 行 53: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 76: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 102: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 123: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 51: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 74: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 100: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 121: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### debug_api_response.py

- 行 44: 考虑将 '{
            "name": "只获取问卷数据",
            "para...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 48: 考虑将 '{
            "name": "只获取量表数据", 
            "par...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 52: 考虑将 '{
            "name": "获取问卷和量表数据",
            "pa...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 56: 考虑将 '{
            "name": "获取所有已完成的问卷和量表",
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 108: 考虑将 '{record.get('status')}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 140: 考虑将 '{item.get('status')}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 43: 考虑将 '[
        {
            "name": "只获取问卷数据",
       ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 67: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 87: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 88: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 119: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 120: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 44: 考虑使用 dataMode.js 中的 API 替换 '{
            "name": "只获取问卷数据",
            "para...'
- 行 48: 考虑使用 dataMode.js 中的 API 替换 '{
            "name": "只获取量表数据", 
            "par...'
- 行 52: 考虑使用 dataMode.js 中的 API 替换 '{
            "name": "获取问卷和量表数据",
            "pa...'
- 行 56: 考虑使用 dataMode.js 中的 API 替换 '{
            "name": "获取所有已完成的问卷和量表",
           ...'
- 行 108: 考虑使用 dataMode.js 中的 API 替换 '{record.get('status')}...'
- 行 140: 考虑使用 dataMode.js 中的 API 替换 '{item.get('status')}...'
- 行 43: 考虑使用 dataMode.js 中的 API 替换 '[
        {
            "name": "只获取问卷数据",
       ...'
- 行 67: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 87: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'
- 行 88: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'
- 行 119: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'
- 行 120: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'


### debug_frontend_assessments.py

- 行 89: 考虑将 '['id', 'name', 'category', 'item_count', 'target',...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 89: 考虑使用 dataMode.js 中的 API 替换 '['id', 'name', 'category', 'item_count', 'target',...'


### deployment_manager.py

- 行 602: 考虑将 '{
                'name': result.config.name,
    ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 617: 考虑将 '{
                'name': result.config.name,
    ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 736: 考虑将 '{item['status']}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 756: 考虑将 '{item['status']}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 734: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 736: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 736: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 754: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 756: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 756: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 504: 考虑将 'total=10...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 602: 考虑使用 dataMode.js 中的 API 替换 '{
                'name': result.config.name,
    ...'
- 行 617: 考虑使用 dataMode.js 中的 API 替换 '{
                'name': result.config.name,
    ...'
- 行 736: 考虑使用 dataMode.js 中的 API 替换 '{item['status']}...'
- 行 756: 考虑使用 dataMode.js 中的 API 替换 '{item['status']}...'
- 行 734: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 736: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 736: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 754: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 756: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 756: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 504: 考虑使用 dataMode.js 中的 API 替换 'total=10...'


### final_comprehensive_test_summary.py

- 行 40: 考虑将 '{
                            "file": file_path,
 ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 56: 考虑将 '{
                "status": "未测试",
               ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 68: 考虑将 '{
                "status": "已测试",
               ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 78: 考虑将 '{
                "status": "报告格式异常",
            ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 91: 考虑将 '{
                "status": "未测试",
               ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 103: 考虑将 '{
                "status": "已测试",
               ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 113: 考虑将 '{
                "status": "报告格式异常",
            ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 126: 考虑将 '{
                "status": "未测试",
               ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 149: 考虑将 '{
            "status": "已测试",
            "workin...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 309: 考虑将 '{frontend['status']}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 321: 考虑将 '{backend['status']}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 336: 考虑将 '{api_tests['status']}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 309: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 321: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 336: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 40: 考虑使用 dataMode.js 中的 API 替换 '{
                            "file": file_path,
 ...'
- 行 56: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "未测试",
               ...'
- 行 68: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "已测试",
               ...'
- 行 78: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "报告格式异常",
            ...'
- 行 91: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "未测试",
               ...'
- 行 103: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "已测试",
               ...'
- 行 113: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "报告格式异常",
            ...'
- 行 126: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "未测试",
               ...'
- 行 149: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "已测试",
            "workin...'
- 行 309: 考虑使用 dataMode.js 中的 API 替换 '{frontend['status']}...'
- 行 321: 考虑使用 dataMode.js 中的 API 替换 '{backend['status']}...'
- 行 336: 考虑使用 dataMode.js 中的 API 替换 '{api_tests['status']}...'
- 行 309: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 321: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 336: 考虑使用 dataMode.js 中的 API 替换 '['status']...'


### final_test_frontend_fix.py

- 行 135: 考虑将 '[
        ("template_id", "id", "量表ID"),
        (...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 135: 考虑使用 dataMode.js 中的 API 替换 '[
        ("template_id", "id", "量表ID"),
        (...'


### fix_api_routes.py

- 行 46: 考虑将 '{
                "url": "/api/auth/login",
      ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 51: 考虑将 '{
                "url": "/api/auth/login",
      ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 56: 考虑将 '{
                "url": "/api/auth/frontend_login...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 61: 考虑将 '{
                "url": "/api/auth/simple_login",...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 66: 考虑将 '{
                "url": "/auth/login",
          ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 46: 考虑使用 dataMode.js 中的 API 替换 '{
                "url": "/api/auth/login",
      ...'
- 行 51: 考虑使用 dataMode.js 中的 API 替换 '{
                "url": "/api/auth/login",
      ...'
- 行 56: 考虑使用 dataMode.js 中的 API 替换 '{
                "url": "/api/auth/frontend_login...'
- 行 61: 考虑使用 dataMode.js 中的 API 替换 '{
                "url": "/api/auth/simple_login",...'
- 行 66: 考虑使用 dataMode.js 中的 API 替换 '{
                "url": "/auth/login",
          ...'


### fix_system_issues.py

- 行 37: 考虑将 '{
            "name": fix_name,
            "statu...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 118: 考虑将 '{
                "id": 1,
                "name":...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 126: 考虑将 '{
                "id": 2,
                "name":...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 137: 考虑将 '{
                "templates": templates[skip:skip...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 171: 考虑将 '{
                "id": 1,
                "name":...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 179: 考虑将 '{
                "id": 2,
                "name":...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 190: 考虑将 '{
                "templates": templates[skip:skip...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 344: 考虑将 '{
                "id": 1,
                "custom...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 353: 考虑将 '{
                "id": 2,
                "custom...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 373: 考虑将 '{
                "records": paginated_records,
  ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 416: 考虑将 '{
            "fix_summary": {
                "to...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 117: 考虑将 '[
            {
                "id": 1,
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 170: 考虑将 '[
            {
                "id": 1,
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 343: 考虑将 '[
            {
                "id": 1,
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 419: 考虑将 '[f for f in self.fixes_applied if f["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 450: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 37: 考虑使用 dataMode.js 中的 API 替换 '{
            "name": fix_name,
            "statu...'
- 行 118: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": 1,
                "name":...'
- 行 126: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": 2,
                "name":...'
- 行 137: 考虑使用 dataMode.js 中的 API 替换 '{
                "templates": templates[skip:skip...'
- 行 171: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": 1,
                "name":...'
- 行 179: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": 2,
                "name":...'
- 行 190: 考虑使用 dataMode.js 中的 API 替换 '{
                "templates": templates[skip:skip...'
- 行 344: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": 1,
                "custom...'
- 行 353: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": 2,
                "custom...'
- 行 373: 考虑使用 dataMode.js 中的 API 替换 '{
                "records": paginated_records,
  ...'
- 行 416: 考虑使用 dataMode.js 中的 API 替换 '{
            "fix_summary": {
                "to...'
- 行 117: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "id": 1,
         ...'
- 行 170: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "id": 1,
         ...'
- 行 343: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "id": 1,
         ...'
- 行 419: 考虑使用 dataMode.js 中的 API 替换 '[f for f in self.fixes_applied if f["status"]...'
- 行 450: 考虑使用 dataMode.js 中的 API 替换 '['name']...'


### get_valid_token.py

- 行 61: 考虑将 '{len(data.get('data', []))}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 63: 考虑将 '{data.get('total')}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 61: 考虑使用 dataMode.js 中的 API 替换 '{len(data.get('data', []))}...'
- 行 63: 考虑使用 dataMode.js 中的 API 替换 '{data.get('total')}...'


### main_manager.py

- 行 285: 考虑将 '{status['summary']['total']}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 296: 考虑将 '{info['status']}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 319: 考虑将 '{latest_build['status']}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 328: 考虑将 '{latest_deploy['status']}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 295: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 296: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 318: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 319: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 319: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 327: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 328: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 328: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 285: 考虑使用 dataMode.js 中的 API 替换 '{status['summary']['total']}...'
- 行 296: 考虑使用 dataMode.js 中的 API 替换 '{info['status']}...'
- 行 319: 考虑使用 dataMode.js 中的 API 替换 '{latest_build['status']}...'
- 行 328: 考虑使用 dataMode.js 中的 API 替换 '{latest_deploy['status']}...'
- 行 295: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 296: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 318: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 319: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 319: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 327: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 328: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 328: 考虑使用 dataMode.js 中的 API 替换 '['status']...'


### project_manager.py

- 行 41: 考虑将 '{"success": True, "data": data}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 394: 考虑将 '{
                    'total': len(self.components...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 406: 考虑将 '{
                    'name': component.name,
    ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 479: 考虑将 '{status['summary']['total']}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 494: 考虑将 '{info['status']}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 552: 考虑将 '{info['status']}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 492: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 494: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 494: 考虑将 '['type']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 494: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 551: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 552: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 552: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 41: 考虑使用 dataMode.js 中的 API 替换 '{"success": True, "data": data}...'
- 行 394: 考虑使用 dataMode.js 中的 API 替换 '{
                    'total': len(self.components...'
- 行 406: 考虑使用 dataMode.js 中的 API 替换 '{
                    'name': component.name,
    ...'
- 行 479: 考虑使用 dataMode.js 中的 API 替换 '{status['summary']['total']}...'
- 行 494: 考虑使用 dataMode.js 中的 API 替换 '{info['status']}...'
- 行 552: 考虑使用 dataMode.js 中的 API 替换 '{info['status']}...'
- 行 492: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 494: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 494: 考虑使用 dataMode.js 中的 API 替换 '['type']...'
- 行 494: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 551: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 552: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 552: 考虑使用 dataMode.js 中的 API 替换 '['status']...'


### service_manager.py

- 行 151: 考虑将 '{
                'cpu_percent': process.cpu_perce...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 365: 考虑将 '{
            'name': config.name,
            'ty...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 400: 考虑将 '{
                'total': len(self.services),
   ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 586: 考虑将 '{status['summary']['total']}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 601: 考虑将 '{info['status']}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 599: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 601: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 183: 考虑将 'total=5...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 151: 考虑使用 dataMode.js 中的 API 替换 '{
                'cpu_percent': process.cpu_perce...'
- 行 365: 考虑使用 dataMode.js 中的 API 替换 '{
            'name': config.name,
            'ty...'
- 行 400: 考虑使用 dataMode.js 中的 API 替换 '{
                'total': len(self.services),
   ...'
- 行 586: 考虑使用 dataMode.js 中的 API 替换 '{status['summary']['total']}...'
- 行 601: 考虑使用 dataMode.js 中的 API 替换 '{info['status']}...'
- 行 599: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 601: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 183: 考虑使用 dataMode.js 中的 API 替换 'total=5...'


### simple_api_structure_check.py

- 行 73: 考虑将 '[
            {
                "id": "量表ID",
    ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 73: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "id": "量表ID",
    ...'


### test_frontend_api.py

- 行 99: 考虑将 '{data['status']}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 128: 考虑将 '{data['total']}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 58: 考虑将 '[
        {
            "name": "标准量表列表",
        ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 77: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 99: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 116: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 118: 考虑将 '['id']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 99: 考虑使用 dataMode.js 中的 API 替换 '{data['status']}...'
- 行 128: 考虑使用 dataMode.js 中的 API 替换 '{data['total']}...'
- 行 58: 考虑使用 dataMode.js 中的 API 替换 '[
        {
            "name": "标准量表列表",
        ...'
- 行 77: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 99: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 116: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 118: 考虑使用 dataMode.js 中的 API 替换 '['id']...'


### test_manager.py

- 行 41: 考虑将 '{"success": True, "data": data}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 204: 考虑将 '{
            'test_name': test_name,
            ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 436: 考虑将 '{
                    'name': suite.name,
        ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 445: 考虑将 '[
                        {
                      ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 325: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 326: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 327: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 328: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 41: 考虑使用 dataMode.js 中的 API 替换 '{"success": True, "data": data}...'
- 行 204: 考虑使用 dataMode.js 中的 API 替换 '{
            'test_name': test_name,
            ...'
- 行 436: 考虑使用 dataMode.js 中的 API 替换 '{
                    'name': suite.name,
        ...'
- 行 445: 考虑使用 dataMode.js 中的 API 替换 '[
                        {
                      ...'
- 行 325: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'
- 行 326: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'
- 行 327: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'
- 行 328: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'
- 行 388: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 389: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 390: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 391: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 392: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 409: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 410: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 411: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 412: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 413: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 416: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\add_questionnaire_scoring_fields.py

- 行 124: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 124: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'


### backend\add_scoring_rules.py

- 行 36: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 36: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'


### backend\auto_sync_templates.py

- 行 22: 考虑将 '{' in line:
                in_template = True
   ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 27: 考虑将 '['template_key', 'name', 'name_en', 'version', 'de...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 22: 考虑使用 dataMode.js 中的 API 替换 '{' in line:
                in_template = True
   ...'
- 行 27: 考虑使用 dataMode.js 中的 API 替换 '['template_key', 'name', 'name_en', 'version', 'de...'
- 行 113: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 163: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 111: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 161: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\check_tables.py

- 行 113: 考虑将 '{
                    'status': 'error',
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 151: 考虑将 '{
                'status': status,
              ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 161: 考虑将 '{
                'status': 'error',
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 175: 考虑将 '{
                    'status': 'error',
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 261: 考虑将 '{
                'status': status,
              ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 271: 考虑将 '{
                'status': 'error',
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 338: 考虑将 '{
                'status': status,
              ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 349: 考虑将 '{
                'status': 'error',
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 400: 考虑将 '{
                'status': status,
              ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 410: 考虑将 '{
                'status': 'error',
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 443: 考虑将 '{
                'status': status,
              ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 464: 考虑将 '{
                'status': 'error',
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 509: 考虑将 '{
                'status': 'error',
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 128: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 129: 考虑将 '['type']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 135: 考虑将 '['id', 'uuid', 'created_at']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 197: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 608: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 191: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 113: 考虑使用 dataMode.js 中的 API 替换 '{
                    'status': 'error',
         ...'
- 行 151: 考虑使用 dataMode.js 中的 API 替换 '{
                'status': status,
              ...'
- 行 161: 考虑使用 dataMode.js 中的 API 替换 '{
                'status': 'error',
             ...'
- 行 175: 考虑使用 dataMode.js 中的 API 替换 '{
                    'status': 'error',
         ...'
- 行 261: 考虑使用 dataMode.js 中的 API 替换 '{
                'status': status,
              ...'
- 行 271: 考虑使用 dataMode.js 中的 API 替换 '{
                'status': 'error',
             ...'
- 行 338: 考虑使用 dataMode.js 中的 API 替换 '{
                'status': status,
              ...'
- 行 349: 考虑使用 dataMode.js 中的 API 替换 '{
                'status': 'error',
             ...'
- 行 400: 考虑使用 dataMode.js 中的 API 替换 '{
                'status': status,
              ...'
- 行 410: 考虑使用 dataMode.js 中的 API 替换 '{
                'status': 'error',
             ...'
- 行 443: 考虑使用 dataMode.js 中的 API 替换 '{
                'status': status,
              ...'
- 行 464: 考虑使用 dataMode.js 中的 API 替换 '{
                'status': 'error',
             ...'
- 行 509: 考虑使用 dataMode.js 中的 API 替换 '{
                'status': 'error',
             ...'
- 行 128: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 129: 考虑使用 dataMode.js 中的 API 替换 '['type']...'
- 行 135: 考虑使用 dataMode.js 中的 API 替换 '['id', 'uuid', 'created_at']...'
- 行 197: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 608: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 191: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'


### backend\cleanup_databases.py

- 行 33: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 33: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'


### backend\comprehensive_fix.py

- 行 227: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 257: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 273: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 291: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 227: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 257: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 273: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 291: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 151: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 178: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\comprehensive_raw_answers_fix.py

- 行 41: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 41: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'
- 行 148: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 145: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\comprehensive_scoring_analysis.py

- 行 90: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 90: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'


### backend\comprehensive_scoring_fix.py

- 行 273: 考虑将 '['type']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 284: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 213: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 262: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 316: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 273: 考虑使用 dataMode.js 中的 API 替换 '['type']...'
- 行 284: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 213: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'
- 行 262: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'
- 行 316: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'


### backend\comprehensive_system_fix.py

- 行 232: 考虑将 '['type']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 243: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 272: 考虑将 '['type']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 221: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 232: 考虑使用 dataMode.js 中的 API 替换 '['type']...'
- 行 243: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 272: 考虑使用 dataMode.js 中的 API 替换 '['type']...'
- 行 221: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'


### backend\create_assessment_test_data.py

- 行 77: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 100: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 73: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 95: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\create_distribution_data.py

- 行 35: 考虑将 '{
                "title": "健康状况调查问卷",
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 41: 考虑将 '{
                "title": "生活方式问卷",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 47: 考虑将 '{
                "title": "饮食习惯问卷",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 34: 考虑将 '[
            {
                "title": "健康状况调查问卷...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 60: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 35: 考虑使用 dataMode.js 中的 API 替换 '{
                "title": "健康状况调查问卷",
           ...'
- 行 41: 考虑使用 dataMode.js 中的 API 替换 '{
                "title": "生活方式问卷",
             ...'
- 行 47: 考虑使用 dataMode.js 中的 API 替换 '{
                "title": "饮食习惯问卷",
             ...'
- 行 34: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "title": "健康状况调查问卷...'
- 行 60: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'


### backend\create_mobile_scoring_api.py

- 行 487: 考虑将 '{
            "status": "success",
            "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 622: 考虑将 '{
            "status": "success",
            "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 716: 考虑将 '{
  "status": "success",
  "message": "评估量表提交成功",
...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 744: 考虑将 '{
  "status": "success",
  "message": "问卷提交成功",
  ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 99: 考虑将 '["name"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 129: 考虑将 'total = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 178: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 487: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "me...'
- 行 622: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "me...'
- 行 716: 考虑使用 dataMode.js 中的 API 替换 '{
  "status": "success",
  "message": "评估量表提交成功",
...'
- 行 744: 考虑使用 dataMode.js 中的 API 替换 '{
  "status": "success",
  "message": "问卷提交成功",
  ...'
- 行 99: 考虑使用 dataMode.js 中的 API 替换 '["name"]...'
- 行 129: 考虑使用 dataMode.js 中的 API 替换 'total = 0...'
- 行 178: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'


### backend\create_sm008_distributions.py

- 行 60: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 73: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 60: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'
- 行 73: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'
- 行 67: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 80: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 65: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 78: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\create_test_data.py

- 行 36: 考虑将 '{
                "name": "健康状况调查问卷",
            ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 43: 考虑将 '{
                "name": "生活方式问卷",
              ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 50: 考虑将 '{
                "name": "饮食习惯问卷",
              ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 75: 考虑将 '{
                "name": "焦虑自评量表(SAS)",
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 82: 考虑将 '{
                "name": "抑郁自评量表(SDS)",
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 89: 考虑将 '{
                "name": "睡眠质量评估",
              ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 35: 考虑将 '[
            {
                "name": "健康状况调查问卷"...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 62: 考虑将 '["name"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 65: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 71: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 74: 考虑将 '[
            {
                "name": "焦虑自评量表(SA...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 101: 考虑将 '["name"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 104: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 108: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 111: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 36: 考虑使用 dataMode.js 中的 API 替换 '{
                "name": "健康状况调查问卷",
            ...'
- 行 43: 考虑使用 dataMode.js 中的 API 替换 '{
                "name": "生活方式问卷",
              ...'
- 行 50: 考虑使用 dataMode.js 中的 API 替换 '{
                "name": "饮食习惯问卷",
              ...'
- 行 75: 考虑使用 dataMode.js 中的 API 替换 '{
                "name": "焦虑自评量表(SAS)",
         ...'
- 行 82: 考虑使用 dataMode.js 中的 API 替换 '{
                "name": "抑郁自评量表(SDS)",
         ...'
- 行 89: 考虑使用 dataMode.js 中的 API 替换 '{
                "name": "睡眠质量评估",
              ...'
- 行 35: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "name": "健康状况调查问卷"...'
- 行 62: 考虑使用 dataMode.js 中的 API 替换 '["name"]...'
- 行 65: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'
- 行 71: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 74: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "name": "焦虑自评量表(SA...'
- 行 101: 考虑使用 dataMode.js 中的 API 替换 '["name"]...'
- 行 104: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'
- 行 108: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'
- 行 111: 考虑使用 dataMode.js 中的 API 替换 '['name']...'


### backend\direct_check_scoring.py

- 行 106: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 106: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'


### backend\final_comparison_test.py

- 行 60: 考虑将 '{questionnaire_result.get('status')}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 60: 考虑将 '{questionnaire_result.get('total')}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 74: 考虑将 '{assessment_result.get('status')}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 74: 考虑将 '{assessment_result.get('total')}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 83: 考虑将 '{q_data.get('status')}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 83: 考虑将 '{q_data.get('total')}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 92: 考虑将 '{a_data.get('status')}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 92: 考虑将 '{a_data.get('total')}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 103: 考虑将 '{all_data.get('status')}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 103: 考虑将 '{all_data.get('total')}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 103: 考虑将 '{len(all_data.get('records', []))}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 120: 考虑将 '{q_data.get('status')}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 120: 考虑将 '{q_data.get('total')}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 120: 考虑将 '{len(q_data.get('records', []))}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 129: 考虑将 '{a_data.get('status')}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 129: 考虑将 '{a_data.get('total')}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 129: 考虑将 '{len(a_data.get('records', []))}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 60: 考虑使用 dataMode.js 中的 API 替换 '{questionnaire_result.get('status')}...'
- 行 60: 考虑使用 dataMode.js 中的 API 替换 '{questionnaire_result.get('total')}...'
- 行 74: 考虑使用 dataMode.js 中的 API 替换 '{assessment_result.get('status')}...'
- 行 74: 考虑使用 dataMode.js 中的 API 替换 '{assessment_result.get('total')}...'
- 行 83: 考虑使用 dataMode.js 中的 API 替换 '{q_data.get('status')}...'
- 行 83: 考虑使用 dataMode.js 中的 API 替换 '{q_data.get('total')}...'
- 行 92: 考虑使用 dataMode.js 中的 API 替换 '{a_data.get('status')}...'
- 行 92: 考虑使用 dataMode.js 中的 API 替换 '{a_data.get('total')}...'
- 行 103: 考虑使用 dataMode.js 中的 API 替换 '{all_data.get('status')}...'
- 行 103: 考虑使用 dataMode.js 中的 API 替换 '{all_data.get('total')}...'
- 行 103: 考虑使用 dataMode.js 中的 API 替换 '{len(all_data.get('records', []))}...'
- 行 120: 考虑使用 dataMode.js 中的 API 替换 '{q_data.get('status')}...'
- 行 120: 考虑使用 dataMode.js 中的 API 替换 '{q_data.get('total')}...'
- 行 120: 考虑使用 dataMode.js 中的 API 替换 '{len(q_data.get('records', []))}...'
- 行 129: 考虑使用 dataMode.js 中的 API 替换 '{a_data.get('status')}...'
- 行 129: 考虑使用 dataMode.js 中的 API 替换 '{a_data.get('total')}...'
- 行 129: 考虑使用 dataMode.js 中的 API 替换 '{len(a_data.get('records', []))}...'


### backend\final_frontend_verification.py

- 行 135: 考虑将 '{
            "code": 200,
            "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 80: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 86: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 93: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 127: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 148: 考虑将 '["id"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 135: 考虑使用 dataMode.js 中的 API 替换 '{
            "code": 200,
            "message": ...'
- 行 80: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 86: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 93: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 127: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 148: 考虑使用 dataMode.js 中的 API 替换 '["id"]...'


### backend\force_reset_database.py

- 行 71: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 71: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\generate_missing_results.py

- 行 111: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 204: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 105: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 198: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\main.py

- 行 460: 考虑将 '{"status": "working", "message": "后端API可正常访问"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 472: 考虑将 '{"status": "success", "message": "登录成功", "username...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 474: 考虑将 '{"status": "error", "message": "用户名或密码错误"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 477: 考虑将 '{"status": "error", "message": "登录验证失败"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 494: 考虑将 '{"status": "healthy"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 525: 考虑将 '{
    "status": "success",
    "message": "这是一个公开的...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 542: 考虑将 '{
                "status": "success",
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 555: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 560: 考虑将 '{
            "status": "error",
            "mess...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 570: 考虑将 '{
        "status": "success",
        "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 773: 考虑将 '{"status": "error", "message": "请求体为空"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 782: 考虑将 '{"status": "error", "message": f"JSON格式错误: {str(e)...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 796: 考虑将 '{"status": "error", "message": "用户不存在"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 801: 考虑将 '{"status": "error", "message": "密码错误"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 460: 考虑使用 dataMode.js 中的 API 替换 '{"status": "working", "message": "后端API可正常访问"}...'
- 行 472: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "message": "登录成功", "username...'
- 行 474: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": "用户名或密码错误"}...'
- 行 477: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": "登录验证失败"}...'
- 行 494: 考虑使用 dataMode.js 中的 API 替换 '{"status": "healthy"}...'
- 行 525: 考虑使用 dataMode.js 中的 API 替换 '{
    "status": "success",
    "message": "这是一个公开的...'
- 行 542: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "success",
           ...'
- 行 555: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 560: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "error",
            "mess...'
- 行 570: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "message": ...'
- 行 773: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": "请求体为空"}...'
- 行 782: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": f"JSON格式错误: {str(e)...'
- 行 796: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": "用户不存在"}...'
- 行 801: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": "密码错误"}...'


### backend\migration_template.py

- 行 16: 考虑将 '{
#         "data": "hardcoded_value",
#         "...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 29: 考虑将 '{
        "data": "default_value",
        "count"...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 16: 考虑使用 dataMode.js 中的 API 替换 '{
#         "data": "hardcoded_value",
#         "...'
- 行 29: 考虑使用 dataMode.js 中的 API 替换 '{
        "data": "default_value",
        "count"...'


### backend\recalculate_sm008_results.py

- 行 170: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 166: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\setup_plan2.py

- 行 280: 考虑将 'SIZE=1000...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 283: 考虑将 'SIZE=1000...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 288: 考虑将 'SIZE=10485760...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 280: 考虑使用 dataMode.js 中的 API 替换 'SIZE=1000...'
- 行 283: 考虑使用 dataMode.js 中的 API 替换 'SIZE=1000...'
- 行 288: 考虑使用 dataMode.js 中的 API 替换 'SIZE=10485760...'
- 行 102: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\test_aggregated_api.py

- 行 460: 考虑将 '{
                    "strategy": "timeline",
    ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 460: 考虑使用 dataMode.js 中的 API 替换 '{
                    "strategy": "timeline",
    ...'
- 行 242: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\test_data_export.py

- 行 67: 考虑将 'size=1000...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 105: 考虑将 'size=500...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 113: 考虑将 'size=200...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 121: 考虑将 'size=1000...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 443: 考虑将 'size=1000...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 484: 考虑将 'size=1000...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 497: 考虑将 'size=0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 67: 考虑使用 dataMode.js 中的 API 替换 'size=1000...'
- 行 105: 考虑使用 dataMode.js 中的 API 替换 'size=500...'
- 行 113: 考虑使用 dataMode.js 中的 API 替换 'size=200...'
- 行 121: 考虑使用 dataMode.js 中的 API 替换 'size=1000...'
- 行 443: 考虑使用 dataMode.js 中的 API 替换 'size=1000...'
- 行 484: 考虑使用 dataMode.js 中的 API 替换 'size=1000...'
- 行 497: 考虑使用 dataMode.js 中的 API 替换 'size=0...'
- 行 239: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 289: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\update_backend_sas_scoring.py

- 行 45: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 45: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'


### backend\verify_scoring_update.py

- 行 31: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 31: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'


### backend\alembic\versions\add_reminded_fields.py

- 行 20: 考虑将 '[col['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 20: 考虑使用 dataMode.js 中的 API 替换 '[col['name']...'


### backend\app\init_clinical_scales.py

- 行 33: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 38: 考虑将 '["name"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 68: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 89: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 94: 考虑将 '["name"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 121: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 29: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 85: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 33: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 38: 考虑使用 dataMode.js 中的 API 替换 '["name"]...'
- 行 68: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 89: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 94: 考虑使用 dataMode.js 中的 API 替换 '["name"]...'
- 行 121: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 29: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'
- 行 85: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'
- 行 30: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 86: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\app\main.py

- 行 117: 考虑将 '{
            "status": "running",
            "ve...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 183: 考虑将 '{
            "id": str(datetime.datetime.now().ti...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 208: 考虑将 '{
            "id": str(datetime.datetime.now().ti...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 235: 考虑将 '{
                "id": "1",
                "name...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 253: 考虑将 '{
            "id": str(datetime.datetime.now().ti...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 272: 考虑将 '{
                "id": "api-service",
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 280: 考虑将 '{
                "id": "db-service",
            ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 343: 考虑将 '{
                "id": 1,
                "name":...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 350: 考虑将 '{
                "id": 2,
                "name":...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 390: 考虑将 '{
                "id": "1",
                "name...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 397: 考虑将 '{
                "id": "2",
                "name...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 426: 考虑将 '{
                "id": "1",
                "name...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 444: 考虑将 '{
            "id": str(datetime.datetime.now().ti...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 463: 考虑将 '{
                "id": 1,
                "title"...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 472: 考虑将 '{
                "id": 2,
                "title"...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 491: 考虑将 '{
            "id": int(datetime.datetime.now().ti...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 537: 考虑将 '{
                "id": "dev",
                "na...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 546: 考虑将 '{
                "id": "test",
                "n...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 555: 考虑将 '{
                "id": "prod",
                "n...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 582: 考虑将 '{
                "id": "backend",
               ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 593: 考虑将 '{
                "id": "frontend",
              ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 604: 考虑将 '{
                "id": "database",
              ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 632: 考虑将 '{
                    "name": service.get("id", "u...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 650: 考虑将 '{
                "name": "backend",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 663: 考虑将 '{
                "name": "frontend",
            ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 676: 考虑将 '{
                "name": "database",
            ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 713: 考虑将 '{
            "success": True,
            "compon...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 876: 考虑将 '{
                "id": 1,
                "name":...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 887: 考虑将 '{
                "id": 2,
                "name":...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 898: 考虑将 '{
                "id": 3,
                "name":...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1002: 考虑将 '{"success": True, "data": result}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 164: 考虑将 '[
            {
                "id": "1",
       ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 234: 考虑将 '[
            {
                "id": "1",
       ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 271: 考虑将 '[
            {
                "id": "api-service...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 342: 考虑将 '[
            {
                "id": 1,
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 389: 考虑将 '[
            {
                "id": "1",
       ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 425: 考虑将 '[
            {
                "id": "1",
       ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 462: 考虑将 '[
            {
                "id": 1,
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 536: 考虑将 '[
            {
                "id": "dev",
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 581: 考虑将 '[
            {
                "id": "backend",
 ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 649: 考虑将 '[
            {
                "name": "backend",...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 829: 考虑将 '[
            {
                "id": 1,
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 875: 考虑将 '[
            {
                "id": 1,
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 117: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "running",
            "ve...'
- 行 183: 考虑使用 dataMode.js 中的 API 替换 '{
            "id": str(datetime.datetime.now().ti...'
- 行 208: 考虑使用 dataMode.js 中的 API 替换 '{
            "id": str(datetime.datetime.now().ti...'
- 行 235: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": "1",
                "name...'
- 行 253: 考虑使用 dataMode.js 中的 API 替换 '{
            "id": str(datetime.datetime.now().ti...'
- 行 272: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": "api-service",
           ...'
- 行 280: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": "db-service",
            ...'
- 行 343: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": 1,
                "name":...'
- 行 350: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": 2,
                "name":...'
- 行 390: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": "1",
                "name...'
- 行 397: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": "2",
                "name...'
- 行 426: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": "1",
                "name...'
- 行 444: 考虑使用 dataMode.js 中的 API 替换 '{
            "id": str(datetime.datetime.now().ti...'
- 行 463: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": 1,
                "title"...'
- 行 472: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": 2,
                "title"...'
- 行 491: 考虑使用 dataMode.js 中的 API 替换 '{
            "id": int(datetime.datetime.now().ti...'
- 行 537: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": "dev",
                "na...'
- 行 546: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": "test",
                "n...'
- 行 555: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": "prod",
                "n...'
- 行 582: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": "backend",
               ...'
- 行 593: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": "frontend",
              ...'
- 行 604: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": "database",
              ...'
- 行 632: 考虑使用 dataMode.js 中的 API 替换 '{
                    "name": service.get("id", "u...'
- 行 650: 考虑使用 dataMode.js 中的 API 替换 '{
                "name": "backend",
             ...'
- 行 663: 考虑使用 dataMode.js 中的 API 替换 '{
                "name": "frontend",
            ...'
- 行 676: 考虑使用 dataMode.js 中的 API 替换 '{
                "name": "database",
            ...'
- 行 713: 考虑使用 dataMode.js 中的 API 替换 '{
            "success": True,
            "compon...'
- 行 876: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": 1,
                "name":...'
- 行 887: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": 2,
                "name":...'
- 行 898: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": 3,
                "name":...'
- 行 1002: 考虑使用 dataMode.js 中的 API 替换 '{"success": True, "data": result}...'
- 行 164: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "id": "1",
       ...'
- 行 234: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "id": "1",
       ...'
- 行 271: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "id": "api-service...'
- 行 342: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "id": 1,
         ...'
- 行 389: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "id": "1",
       ...'
- 行 425: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "id": "1",
       ...'
- 行 462: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "id": 1,
         ...'
- 行 536: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "id": "dev",
     ...'
- 行 581: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "id": "backend",
 ...'
- 行 649: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "name": "backend",...'
- 行 829: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "id": 1,
         ...'
- 行 875: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "id": 1,
         ...'


### backend\app\api\auth.py

- 行 241: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 252: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 260: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 284: 考虑将 '{
            "status": "success",
            "ac...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 297: 考虑将 '{
            "status": "error",
            "mess...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 393: 考虑将 '{
            "status": "error",
            "mess...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 402: 考虑将 '{
            "status": "error",
            "mess...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 412: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 466: 考虑将 '{
        "status": "success",
        "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 241: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 252: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 260: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 284: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "ac...'
- 行 297: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "error",
            "mess...'
- 行 393: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "error",
            "mess...'
- 行 402: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "error",
            "mess...'
- 行 412: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 466: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "message": ...'


### backend\app\api\documents.py

- 行 32: 考虑将 'size = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 32: 考虑使用 dataMode.js 中的 API 替换 'size = 0...'


### backend\app\api\health.py

- 行 19: 考虑将 '{"status": "ok", "timestamp": datetime.now().isofo...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 28: 考虑将 '{
        "status": "ok",
        "timestamp": dat...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 61: 考虑将 '{
        "status": "ok",
        "timestamp": dat...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 19: 考虑使用 dataMode.js 中的 API 替换 '{"status": "ok", "timestamp": datetime.now().isofo...'
- 行 28: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "ok",
        "timestamp": dat...'
- 行 61: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "ok",
        "timestamp": dat...'


### backend\app\api\health_records.py

- 行 301: 考虑将 '{
        "items": items,
        "total": total,
...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 329: 考虑将 '{
        "id": record.id,
        "custom_id": re...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 379: 考虑将 '{
        "id": db_record.id,
        "custom_id":...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 434: 考虑将 '{
        "id": record.id,
        "custom_id": re...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 301: 考虑使用 dataMode.js 中的 API 替换 '{
        "items": items,
        "total": total,
...'
- 行 329: 考虑使用 dataMode.js 中的 API 替换 '{
        "id": record.id,
        "custom_id": re...'
- 行 379: 考虑使用 dataMode.js 中的 API 替换 '{
        "id": db_record.id,
        "custom_id":...'
- 行 434: 考虑使用 dataMode.js 中的 API 替换 '{
        "id": record.id,
        "custom_id": re...'


### backend\app\api\lab_reports.py

- 行 437: 考虑将 '{
                    "status": "success",
       ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 444: 考虑将 '{
                "status": "success",
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 485: 考虑将 '{
        "status": "success",
        "total": to...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 526: 考虑将 '{
        "status": "success",
        "data": {
 ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 554: 考虑将 '{"status": "success", "types": types, "labels": la...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 437: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "success",
       ...'
- 行 444: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "success",
           ...'
- 行 485: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "total": to...'
- 行 526: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "data": {
 ...'
- 行 554: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "types": types, "labels": la...'


### backend\app\api\medical_records.py

- 行 584: 考虑将 '{
        "status": "success",
        "total": to...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 634: 考虑将 '{
        "status": "success",
        "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 584: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "total": to...'
- 行 634: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "message": ...'


### backend\app\api\users.py

- 行 122: 考虑将 '{
        "status": "success",
        "data": res...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 324: 考虑将 '{
        "status": "success",
        "data": {
 ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 383: 考虑将 '{
        "status": "success",
        "data": {
 ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 469: 考虑将 '{
        "status": "success",
        "data": {
 ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 556: 考虑将 '{
        "status": "success",
        "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 684: 考虑将 '{
        "status": "success",
        "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 445: 考虑将 '["id"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 122: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "data": res...'
- 行 324: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "data": {
 ...'
- 行 383: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "data": {
 ...'
- 行 469: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "data": {
 ...'
- 行 556: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "message": ...'
- 行 684: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "message": ...'
- 行 445: 考虑使用 dataMode.js 中的 API 替换 '["id"]...'


### backend\app\api\endpoints\alerts.py

- 行 278: 考虑将 '{
        "success": True,
        "data": [
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 343: 考虑将 '{
        "success": True,
        "data": {
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 385: 考虑将 '{
        "success": True,
        "data": {
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 442: 考虑将 '{
        "success": True,
        "data": {
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 510: 考虑将 '{
        "success": True,
        "data": [
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 560: 考虑将 '{
        "success": True,
        "data": {
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 98: 考虑将 '[fill_alert_fields({
                "id": "permis...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 153: 考虑将 '[{
                    "id": "system_status",
    ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 166: 考虑将 '[{
                    "id": "no_alerts",
        ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 187: 考虑将 '[fill_alert_fields({
            "id": "error_noti...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 280: 考虑将 '[
            {
                "id": alert.id,
  ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 512: 考虑将 '[
            {
                "id": rule.id,
   ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 659: 考虑将 '["id"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 798: 考虑将 '["id"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 278: 考虑使用 dataMode.js 中的 API 替换 '{
        "success": True,
        "data": [
     ...'
- 行 343: 考虑使用 dataMode.js 中的 API 替换 '{
        "success": True,
        "data": {
     ...'
- 行 385: 考虑使用 dataMode.js 中的 API 替换 '{
        "success": True,
        "data": {
     ...'
- 行 442: 考虑使用 dataMode.js 中的 API 替换 '{
        "success": True,
        "data": {
     ...'
- 行 510: 考虑使用 dataMode.js 中的 API 替换 '{
        "success": True,
        "data": [
     ...'
- 行 560: 考虑使用 dataMode.js 中的 API 替换 '{
        "success": True,
        "data": {
     ...'
- 行 98: 考虑使用 dataMode.js 中的 API 替换 '[fill_alert_fields({
                "id": "permis...'
- 行 153: 考虑使用 dataMode.js 中的 API 替换 '[{
                    "id": "system_status",
    ...'
- 行 166: 考虑使用 dataMode.js 中的 API 替换 '[{
                    "id": "no_alerts",
        ...'
- 行 187: 考虑使用 dataMode.js 中的 API 替换 '[fill_alert_fields({
            "id": "error_noti...'
- 行 280: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "id": alert.id,
  ...'
- 行 512: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "id": rule.id,
   ...'
- 行 659: 考虑使用 dataMode.js 中的 API 替换 '["id"]...'
- 行 798: 考虑使用 dataMode.js 中的 API 替换 '["id"]...'


### backend\app\api\endpoints\assessments.py

- 行 31: 考虑将 '{
            "id": template.id,
            "temp...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 100: 考虑将 '{
            "id": assessment.id,
            "ti...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 173: 考虑将 '{
                "id": a.id,
                "tit...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 188: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 195: 考虑将 '{
            "status": "error",
            "mess...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 227: 考虑将 '{
        "status": "success",
        "data": {
 ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 279: 考虑将 '{
        "status": "success",
        "questions"...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 320: 考虑将 '{
        "status": "success",
        "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 342: 考虑将 '{
        "status": "success",
        "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 370: 考虑将 '{
        "status": "success",
        "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 412: 考虑将 '{"status": "success", "message": message}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 430: 考虑将 '{"status": "success", "message": "推送成功"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 466: 考虑将 '{"status": "success", "message": "批量导入题目成功"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 512: 考虑将 '{"status": "success", "total": 0, "items": [], "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 515: 考虑将 '{"status": "success", "total": 0, "items": [], "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 551: 考虑将 '{
                "id": item.id,
                "...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 562: 考虑将 '{
            "status": "success",
            "to...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 570: 考虑将 '{
            "status": "success",
            "to...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 592: 考虑将 '{"status": "success", "message": "所有题目已删除"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 722: 考虑将 '{"status": "success", "message": "量表分发成功"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 45: 考虑将 '[
                {
                    "id": q.id...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 86: 考虑将 '[
                    {
                        "i...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 259: 考虑将 '[
        {
            "id": item.id,
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 31: 考虑使用 dataMode.js 中的 API 替换 '{
            "id": template.id,
            "temp...'
- 行 100: 考虑使用 dataMode.js 中的 API 替换 '{
            "id": assessment.id,
            "ti...'
- 行 173: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": a.id,
                "tit...'
- 行 188: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 195: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "error",
            "mess...'
- 行 227: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "data": {
 ...'
- 行 279: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "questions"...'
- 行 320: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "message": ...'
- 行 342: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "message": ...'
- 行 370: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "message": ...'
- 行 412: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "message": message}...'
- 行 430: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "message": "推送成功"}...'
- 行 466: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "message": "批量导入题目成功"}...'
- 行 512: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "total": 0, "items": [], "me...'
- 行 515: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "total": 0, "items": [], "me...'
- 行 551: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": item.id,
                "...'
- 行 562: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "to...'
- 行 570: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "to...'
- 行 592: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "message": "所有题目已删除"}...'
- 行 722: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "message": "量表分发成功"}...'
- 行 45: 考虑使用 dataMode.js 中的 API 替换 '[
                {
                    "id": q.id...'
- 行 86: 考虑使用 dataMode.js 中的 API 替换 '[
                    {
                        "i...'
- 行 259: 考虑使用 dataMode.js 中的 API 替换 '[
        {
            "id": item.id,
           ...'


### backend\app\api\endpoints\assessment_distributions.py

- 行 69: 考虑将 '{
            "id": distribution.id,
            "...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 91: 考虑将 '{
        "status": "success",
        "data": res...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 131: 考虑将 '{
        "status": "success",
        "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 69: 考虑使用 dataMode.js 中的 API 替换 '{
            "id": distribution.id,
            "...'
- 行 91: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "data": res...'
- 行 131: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "message": ...'


### backend\app\api\endpoints\assessment_responses.py

- 行 85: 考虑将 '{
        "status": "success",
        "data": {
 ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 190: 考虑将 '{
        "status": "success",
        "data": res...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 238: 考虑将 '{
        "status": "success",
        "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 85: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "data": {
 ...'
- 行 190: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "data": res...'
- 行 238: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "message": ...'


### backend\app\api\endpoints\assessment_results.py

- 行 81: 考虑将 '{
        "success": True,
        "data": [
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 174: 考虑将 '{
        "success": True,
        "data": {
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 234: 考虑将 '{
        "success": True,
        "data": {
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 298: 考虑将 '{
        "success": True,
        "data": {
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 419: 考虑将 '{
        "success": True,
        "data": {
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 671: 考虑将 '{
        "code": 200,
        "message": "获取成功",
...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 83: 考虑将 '[
            {
                "id": result.Asses...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 292: 考虑将 '["id", "assessment_id", "custom_id", "created_at"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 476: 考虑将 'Size=18...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 81: 考虑使用 dataMode.js 中的 API 替换 '{
        "success": True,
        "data": [
     ...'
- 行 174: 考虑使用 dataMode.js 中的 API 替换 '{
        "success": True,
        "data": {
     ...'
- 行 234: 考虑使用 dataMode.js 中的 API 替换 '{
        "success": True,
        "data": {
     ...'
- 行 298: 考虑使用 dataMode.js 中的 API 替换 '{
        "success": True,
        "data": {
     ...'
- 行 419: 考虑使用 dataMode.js 中的 API 替换 '{
        "success": True,
        "data": {
     ...'
- 行 671: 考虑使用 dataMode.js 中的 API 替换 '{
        "code": 200,
        "message": "获取成功",
...'
- 行 83: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "id": result.Asses...'
- 行 292: 考虑使用 dataMode.js 中的 API 替换 '["id", "assessment_id", "custom_id", "created_at"]...'
- 行 476: 考虑使用 dataMode.js 中的 API 替换 'Size=18...'


### backend\app\api\endpoints\auth.py

- 行 42: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 80: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 87: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 94: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 157: 考虑将 '{
            "status": "success",
            "ac...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 169: 考虑将 '{
            "status": "error",
            "mess...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 191: 考虑将 '{
                "status": "success",
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 217: 考虑将 '{
            "status": "success",
            "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 229: 考虑将 '{
            "status": "error",
            "mess...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 259: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 268: 考虑将 '{
                    "status": "error",
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 298: 考虑将 '{
                    "status": "error",
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 336: 考虑将 '{
                        "status": "error",
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 341: 考虑将 '{
                        "status": "error",
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 346: 考虑将 '{
                        "status": "error",
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 388: 考虑将 '{
            "status": "success",
            "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 401: 考虑将 '{
            "status": "error",
            "mess...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 517: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 527: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 534: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 601: 考虑将 '{
            "status": "success",
            "ac...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 619: 考虑将 '{
            "status": "error",
            "mess...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 771: 考虑将 '{
                "status": "success",
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 785: 考虑将 '{
                    "status": "error",
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 796: 考虑将 '{
                    "status": "error",
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 803: 考虑将 '{
                    "status": "error",
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 841: 考虑将 '{
                "status": "success",
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 854: 考虑将 '{
            "status": "error",
            "mess...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 995: 考虑将 '{"status": "error", "message": "用户名或密码错误"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1013: 考虑将 '{"status": "error", "message": "用户名或密码错误"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1017: 考虑将 '{"status": "error", "message": "用户已被禁用"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1050: 考虑将 '{
            "status": "success",
            "ac...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1062: 考虑将 '{"status": "error", "message": f"登录时发生错误: {str(e)}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1069: 考虑将 '{
        "status": "success",
        "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1246: 考虑将 '{
            "status": "success",
            "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 42: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 80: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 87: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 94: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 157: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "ac...'
- 行 169: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "error",
            "mess...'
- 行 191: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "success",
           ...'
- 行 217: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "me...'
- 行 229: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "error",
            "mess...'
- 行 259: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 268: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "error",
         ...'
- 行 298: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "error",
         ...'
- 行 336: 考虑使用 dataMode.js 中的 API 替换 '{
                        "status": "error",
     ...'
- 行 341: 考虑使用 dataMode.js 中的 API 替换 '{
                        "status": "error",
     ...'
- 行 346: 考虑使用 dataMode.js 中的 API 替换 '{
                        "status": "error",
     ...'
- 行 388: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "me...'
- 行 401: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "error",
            "mess...'
- 行 517: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 527: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 534: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 601: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "ac...'
- 行 619: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "error",
            "mess...'
- 行 771: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "success",
           ...'
- 行 785: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "error",
         ...'
- 行 796: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "error",
         ...'
- 行 803: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "error",
         ...'
- 行 841: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "success",
           ...'
- 行 854: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "error",
            "mess...'
- 行 995: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": "用户名或密码错误"}...'
- 行 1013: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": "用户名或密码错误"}...'
- 行 1017: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": "用户已被禁用"}...'
- 行 1050: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "ac...'
- 行 1062: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": f"登录时发生错误: {str(e)}...'
- 行 1069: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "message": ...'
- 行 1246: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "me...'


### backend\app\api\endpoints\clinical_scales.py

- 行 34: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 80: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 126: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 189: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 231: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 281: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 38: 考虑将 '[
                    {
                        "i...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 84: 考虑将 '[
                    {
                        "i...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 130: 考虑将 '[
                    {
                        "i...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 235: 考虑将 '[
                    {
                        "i...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 285: 考虑将 '[
                    {
                        "i...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 34: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 80: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 126: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 189: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 231: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 281: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 38: 考虑使用 dataMode.js 中的 API 替换 '[
                    {
                        "i...'
- 行 84: 考虑使用 dataMode.js 中的 API 替换 '[
                    {
                        "i...'
- 行 130: 考虑使用 dataMode.js 中的 API 替换 '[
                    {
                        "i...'
- 行 235: 考虑使用 dataMode.js 中的 API 替换 '[
                    {
                        "i...'
- 行 285: 考虑使用 dataMode.js 中的 API 替换 '[
                    {
                        "i...'
- 行 26: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 72: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 118: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 223: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 273: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\app\api\endpoints\dashboard.py

- 行 106: 考虑将 '{
        "status": "success",
        "data": {
 ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 123: 考虑将 '{
        "status": "success",
        "python_ver...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 368: 考虑将 '[
        { "value": 10, "name": "实验室检验" },
      ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 106: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "data": {
 ...'
- 行 123: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "python_ver...'
- 行 368: 考虑使用 dataMode.js 中的 API 替换 '[
        { "value": 10, "name": "实验室检验" },
      ...'


### backend\app\api\endpoints\data_mode_switch.py

- 行 54: 考虑将 '{
            'status': 'success',
            'da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 133: 考虑将 '{
            'status': 'success',
            'me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 185: 考虑将 '{
            'status': 'success',
            'da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 217: 考虑将 '{
            'status': 'success',
            'da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 54: 考虑使用 dataMode.js 中的 API 替换 '{
            'status': 'success',
            'da...'
- 行 133: 考虑使用 dataMode.js 中的 API 替换 '{
            'status': 'success',
            'me...'
- 行 185: 考虑使用 dataMode.js 中的 API 替换 '{
            'status': 'success',
            'da...'
- 行 217: 考虑使用 dataMode.js 中的 API 替换 '{
            'status': 'success',
            'da...'


### backend\app\api\endpoints\debug.py

- 行 19: 考虑将 '{
        "status": "success",
        "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 29: 考虑将 '{
        "status": "success",
        "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 64: 考虑将 '{
        "status": "success",
        "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 47: 考虑将 '[
        {
            "id": user.id,
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 19: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "message": ...'
- 行 29: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "message": ...'
- 行 64: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "message": ...'
- 行 47: 考虑使用 dataMode.js 中的 API 替换 '[
        {
            "id": user.id,
           ...'


### backend\app\api\endpoints\dimensions.py

- 行 58: 考虑将 '{
            "status": "success",
            "di...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 114: 考虑将 '{
            "status": "success",
            "di...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 159: 考虑将 '{
            "status": "success",
            "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 58: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "di...'
- 行 114: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "di...'
- 行 159: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "me...'


### backend\app\api\endpoints\documents.py

- 行 45: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 92: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 162: 考虑将 '{
            "status": "success",
            "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 348: 考虑将 '{
        "status": "success",
        "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 485: 考虑将 '{
        "status": "success",
        "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 516: 考虑将 '{
        "status": "success",
        "data": {
 ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 49: 考虑将 '[
                    {
                        "i...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 45: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 92: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 162: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "me...'
- 行 348: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "message": ...'
- 行 485: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "message": ...'
- 行 516: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "data": {
 ...'
- 行 49: 考虑使用 dataMode.js 中的 API 替换 '[
                    {
                        "i...'


### backend\app\api\endpoints\documents_fix.py

- 行 84: 考虑将 '[col['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 84: 考虑使用 dataMode.js 中的 API 替换 '[col['name']...'


### backend\app\api\endpoints\examination_reports.py

- 行 95: 考虑将 '{
        "success": True,
        "data": [
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 200: 考虑将 '{
        "success": True,
        "data": {
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 248: 考虑将 '{
        "success": True,
        "data": {
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 307: 考虑将 '{
        "success": True,
        "data": {
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 388: 考虑将 '{
            "status": "success",
            "to...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 396: 考虑将 '{
            "status": "success",
            "to...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 97: 考虑将 '[
            {
                "id": report.id,
 ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 95: 考虑使用 dataMode.js 中的 API 替换 '{
        "success": True,
        "data": [
     ...'
- 行 200: 考虑使用 dataMode.js 中的 API 替换 '{
        "success": True,
        "data": {
     ...'
- 行 248: 考虑使用 dataMode.js 中的 API 替换 '{
        "success": True,
        "data": {
     ...'
- 行 307: 考虑使用 dataMode.js 中的 API 替换 '{
        "success": True,
        "data": {
     ...'
- 行 388: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "to...'
- 行 396: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "to...'
- 行 97: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "id": report.id,
 ...'


### backend\app\api\endpoints\follow_up_records.py

- 行 59: 考虑将 '{
        "success": True,
        "data": [
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 125: 考虑将 '{
        "success": True,
        "data": {
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 167: 考虑将 '{
        "success": True,
        "data": {
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 220: 考虑将 '{
        "success": True,
        "data": {
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 61: 考虑将 '[
            {
                "id": record.id,
 ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 59: 考虑使用 dataMode.js 中的 API 替换 '{
        "success": True,
        "data": [
     ...'
- 行 125: 考虑使用 dataMode.js 中的 API 替换 '{
        "success": True,
        "data": {
     ...'
- 行 167: 考虑使用 dataMode.js 中的 API 替换 '{
        "success": True,
        "data": {
     ...'
- 行 220: 考虑使用 dataMode.js 中的 API 替换 '{
        "success": True,
        "data": {
     ...'
- 行 61: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "id": record.id,
 ...'


### backend\app\api\endpoints\health.py

- 行 31: 考虑将 '{
        "status": "ok",
        "timestamp": dat...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 78: 考虑将 '{
        "memory": {
            "status": memory...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 86: 考虑将 '{
            "status": cpu_status,
            "p...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 90: 考虑将 '{
            "status": disk_status,
            "...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 128: 考虑将 '{
        "status": overall_status,
        "times...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 181: 考虑将 '{
        "status": overall_status,
        "depen...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 110: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 114: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 116: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 121: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 124: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 158: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 175: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 178: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 31: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "ok",
        "timestamp": dat...'
- 行 78: 考虑使用 dataMode.js 中的 API 替换 '{
        "memory": {
            "status": memory...'
- 行 86: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": cpu_status,
            "p...'
- 行 90: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": disk_status,
            "...'
- 行 128: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": overall_status,
        "times...'
- 行 181: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": overall_status,
        "depen...'
- 行 110: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'
- 行 114: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'
- 行 116: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'
- 行 121: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'
- 行 124: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'
- 行 158: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'
- 行 175: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'
- 行 178: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'


### backend\app\api\endpoints\health_diaries.py

- 行 59: 考虑将 '{
        "success": True,
        "data": [
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 127: 考虑将 '{
        "success": True,
        "data": {
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 170: 考虑将 '{
        "success": True,
        "data": {
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 224: 考虑将 '{
        "success": True,
        "data": {
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 61: 考虑将 '[
            {
                "id": diary.id,
  ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 59: 考虑使用 dataMode.js 中的 API 替换 '{
        "success": True,
        "data": [
     ...'
- 行 127: 考虑使用 dataMode.js 中的 API 替换 '{
        "success": True,
        "data": {
     ...'
- 行 170: 考虑使用 dataMode.js 中的 API 替换 '{
        "success": True,
        "data": {
     ...'
- 行 224: 考虑使用 dataMode.js 中的 API 替换 '{
        "success": True,
        "data": {
     ...'
- 行 61: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "id": diary.id,
  ...'


### backend\app\api\endpoints\health_records.py

- 行 14: 考虑将 '{"status": "success", "message": "健康记录API已废弃，请使用聚合...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 14: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "message": "健康记录API已废弃，请使用聚合...'


### backend\app\api\endpoints\health_statistics.py

- 行 75: 考虑将 '{
                "period": period,
              ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 136: 考虑将 '{
        "total": total_distributed,
        "com...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 181: 考虑将 '{
        "total": total_distributed,
        "com...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 207: 考虑将 '{
        "total": total_records,
        "by_type...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 258: 考虑将 '{
            'date': date_str,
            'quest...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 75: 考虑使用 dataMode.js 中的 API 替换 '{
                "period": period,
              ...'
- 行 136: 考虑使用 dataMode.js 中的 API 替换 '{
        "total": total_distributed,
        "com...'
- 行 181: 考虑使用 dataMode.js 中的 API 替换 '{
        "total": total_distributed,
        "com...'
- 行 207: 考虑使用 dataMode.js 中的 API 替换 '{
        "total": total_records,
        "by_type...'
- 行 258: 考虑使用 dataMode.js 中的 API 替换 '{
            'date': date_str,
            'quest...'


### backend\app\api\endpoints\imaging_reports.py

- 行 59: 考虑将 '{
        "success": True,
        "data": [
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 162: 考虑将 '{
        "success": True,
        "data": {
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 209: 考虑将 '{
        "success": True,
        "data": {
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 267: 考虑将 '{
        "success": True,
        "data": {
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 61: 考虑将 '[
            {
                "id": report.id,
 ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 59: 考虑使用 dataMode.js 中的 API 替换 '{
        "success": True,
        "data": [
     ...'
- 行 162: 考虑使用 dataMode.js 中的 API 替换 '{
        "success": True,
        "data": {
     ...'
- 行 209: 考虑使用 dataMode.js 中的 API 替换 '{
        "success": True,
        "data": {
     ...'
- 行 267: 考虑使用 dataMode.js 中的 API 替换 '{
        "success": True,
        "data": {
     ...'
- 行 61: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "id": report.id,
 ...'


### backend\app\api\endpoints\lab_reports.py

- 行 72: 考虑将 '{"status": "success", "total": 0, "items": [], "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 76: 考虑将 '{"status": "success", "total": 0, "items": [], "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 119: 考虑将 '{
            "status": "success",
            "to...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 127: 考虑将 '{
            "status": "success",
            "to...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 156: 考虑将 '{"total": 0, "records": []}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 158: 考虑将 '{"total": 0, "records": []}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 167: 考虑将 '{"total": 0, "records": [], "message": f"无效的report...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 169: 考虑将 '{"total": 0, "records": [], "message": f"report_ty...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 207: 考虑将 '{"total": total, "records": records}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 210: 考虑将 '{"total": 0, "records": []}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 243: 考虑将 '{
        "status": "success",
        "total": to...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 226: 考虑将 '[
        {
            "id": 1,
            "cust...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 223: 考虑将 'total = 1...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 72: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "total": 0, "items": [], "me...'
- 行 76: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "total": 0, "items": [], "me...'
- 行 119: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "to...'
- 行 127: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "to...'
- 行 156: 考虑使用 dataMode.js 中的 API 替换 '{"total": 0, "records": []}...'
- 行 158: 考虑使用 dataMode.js 中的 API 替换 '{"total": 0, "records": []}...'
- 行 167: 考虑使用 dataMode.js 中的 API 替换 '{"total": 0, "records": [], "message": f"无效的report...'
- 行 169: 考虑使用 dataMode.js 中的 API 替换 '{"total": 0, "records": [], "message": f"report_ty...'
- 行 207: 考虑使用 dataMode.js 中的 API 替换 '{"total": total, "records": records}...'
- 行 210: 考虑使用 dataMode.js 中的 API 替换 '{"total": 0, "records": []}...'
- 行 243: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "total": to...'
- 行 226: 考虑使用 dataMode.js 中的 API 替换 '[
        {
            "id": 1,
            "cust...'
- 行 223: 考虑使用 dataMode.js 中的 API 替换 'total = 1...'


### backend\app\api\endpoints\medical_records.py

- 行 37: 考虑将 '{"status": "success", "total": 0, "items": []}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 49: 考虑将 '{"status": "success", "total": 0, "items": [], "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 52: 考虑将 '{"status": "success", "total": 0, "items": [], "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 86: 考虑将 '{
            "status": "success",
            "to...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 94: 考虑将 '{
            "status": "success",
            "to...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 154: 考虑将 '{"status": "success", "total": 0, "items": [], "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 158: 考虑将 '{"status": "success", "total": 0, "items": [], "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 217: 考虑将 '{
            "status": "success",
            "to...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 225: 考虑将 '{
            "status": "success",
            "to...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 284: 考虑将 '{
            "status": "success",
            "to...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 293: 考虑将 '{
            "status": "error",
            "mess...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 333: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 342: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 383: 考虑将 '{
            "status": "success",
            "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 390: 考虑将 '{
            "status": "error",
            "mess...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 37: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "total": 0, "items": []}...'
- 行 49: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "total": 0, "items": [], "me...'
- 行 52: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "total": 0, "items": [], "me...'
- 行 86: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "to...'
- 行 94: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "to...'
- 行 154: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "total": 0, "items": [], "me...'
- 行 158: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "total": 0, "items": [], "me...'
- 行 217: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "to...'
- 行 225: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "to...'
- 行 284: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "to...'
- 行 293: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "error",
            "mess...'
- 行 333: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 342: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 383: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "me...'
- 行 390: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "error",
            "mess...'


### backend\app\api\endpoints\metrics.py

- 行 109: 考虑将 '{
                "status": "running",
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 113: 考虑将 '{
                    "total": new_metrics.get("ap...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 209: 考虑将 '{
                "total": base_metrics.get("datab...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 109: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "running",
           ...'
- 行 113: 考虑使用 dataMode.js 中的 API 替换 '{
                    "total": new_metrics.get("ap...'
- 行 209: 考虑使用 dataMode.js 中的 API 替换 '{
                "total": base_metrics.get("datab...'


### backend\app\api\endpoints\metrics_public.py

- 行 98: 考虑将 '{
                "status": base_metrics.get("appl...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 104: 考虑将 '{
                    "total": base_metrics.get("a...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 117: 考虑将 '{
                    "total": base_metrics.get("d...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 177: 考虑将 '{
            "status": "running",
            "ve...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 196: 考虑将 '{
                "total": random.randint(40000, 6...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 98: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": base_metrics.get("appl...'
- 行 104: 考虑使用 dataMode.js 中的 API 替换 '{
                    "total": base_metrics.get("a...'
- 行 117: 考虑使用 dataMode.js 中的 API 替换 '{
                    "total": base_metrics.get("d...'
- 行 177: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "running",
            "ve...'
- 行 196: 考虑使用 dataMode.js 中的 API 替换 '{
                "total": random.randint(40000, 6...'


### backend\app\api\endpoints\mobile_api.py

- 行 134: 考虑将 '{"status": "success", "data": templates}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 143: 考虑将 '{"status": "success", "data": assessments}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 154: 考虑将 '{"status": "success", "data": assessment, "message...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 164: 考虑将 '{"status": "success", "data": templates}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 173: 考虑将 '{"status": "success", "data": questionnaires}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 184: 考虑将 '{"status": "success", "data": questionnaire, "mess...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 193: 考虑将 '{"status": "success", "message": "移动端API正常工作"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 134: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "data": templates}...'
- 行 143: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "data": assessments}...'
- 行 154: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "data": assessment, "message...'
- 行 164: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "data": templates}...'
- 行 173: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "data": questionnaires}...'
- 行 184: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "data": questionnaire, "mess...'
- 行 193: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "message": "移动端API正常工作"}...'


### backend\app\api\endpoints\mobile_api_new.py

- 行 89: 考虑将 '{
                "id": tpl.id,
                "t...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 104: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 109: 考虑将 '{
            "status": "error",
            "mess...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 164: 考虑将 '{
                "id": assessment.id,
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 189: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 194: 考虑将 '{
            "status": "error",
            "mess...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 212: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 226: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 234: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 241: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 263: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 281: 考虑将 '{
                    "status": "error",
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 288: 考虑将 '{
                    "status": "error",
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 297: 考虑将 '{
                    "status": "error",
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 404: 考虑将 '{
            "status": "success",
            "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 420: 考虑将 '{
            "status": "error",
            "mess...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 464: 考虑将 '{
                "id": tpl.id,
                "t...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 479: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 484: 考虑将 '{
            "status": "error",
            "mess...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 533: 考虑将 '{
                        "id": questionnaire.id,
...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 554: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 559: 考虑将 '{
            "status": "error",
            "mess...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 577: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 591: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 599: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 606: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 617: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 634: 考虑将 '{
                    "status": "error",
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 641: 考虑将 '{
                    "status": "error",
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 650: 考虑将 '{
                    "status": "error",
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 744: 考虑将 '{
            "status": "success",
            "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 759: 考虑将 '{
            "status": "error",
            "mess...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 777: 考虑将 '{
            "id": q.id,
            "title": q.t...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 786: 考虑将 '{"status": "success", "data": result}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 810: 考虑将 '{"status": "success", "data": result}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 840: 考虑将 '{"status": "success", "data": {"original": origina...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 876: 考虑将 '{
            "id": a.id,
            "title": tit...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 887: 考虑将 '{"status": "success", "data": result, "debug": {"t...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 952: 考虑将 '{"status": "success", "data": result}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 982: 考虑将 '{"status": "success", "data": {"original": origina...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1443: 考虑将 '{"status": "success", "data": report_data}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1542: 考虑将 '{"status": "success", "data": report_data}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 415: 考虑将 '["id"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 754: 考虑将 '["id"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 836: 考虑将 '["type"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 978: 考虑将 '["type"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1434: 考虑将 '[{
                    "id": q.id,
               ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1533: 考虑将 '[{
                    "id": q.id,
               ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 89: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": tpl.id,
                "t...'
- 行 104: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 109: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "error",
            "mess...'
- 行 164: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": assessment.id,
           ...'
- 行 189: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 194: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "error",
            "mess...'
- 行 212: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 226: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 234: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 241: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 263: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 281: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "error",
         ...'
- 行 288: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "error",
         ...'
- 行 297: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "error",
         ...'
- 行 404: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "me...'
- 行 420: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "error",
            "mess...'
- 行 464: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": tpl.id,
                "t...'
- 行 479: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 484: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "error",
            "mess...'
- 行 533: 考虑使用 dataMode.js 中的 API 替换 '{
                        "id": questionnaire.id,
...'
- 行 554: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 559: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "error",
            "mess...'
- 行 577: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 591: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 599: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 606: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 617: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 634: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "error",
         ...'
- 行 641: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "error",
         ...'
- 行 650: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "error",
         ...'
- 行 744: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "me...'
- 行 759: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "error",
            "mess...'
- 行 777: 考虑使用 dataMode.js 中的 API 替换 '{
            "id": q.id,
            "title": q.t...'
- 行 786: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "data": result}...'
- 行 810: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "data": result}...'
- 行 840: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "data": {"original": origina...'
- 行 876: 考虑使用 dataMode.js 中的 API 替换 '{
            "id": a.id,
            "title": tit...'
- 行 887: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "data": result, "debug": {"t...'
- 行 952: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "data": result}...'
- 行 982: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "data": {"original": origina...'
- 行 1443: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "data": report_data}...'
- 行 1542: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "data": report_data}...'
- 行 415: 考虑使用 dataMode.js 中的 API 替换 '["id"]...'
- 行 754: 考虑使用 dataMode.js 中的 API 替换 '["id"]...'
- 行 836: 考虑使用 dataMode.js 中的 API 替换 '["type"]...'
- 行 978: 考虑使用 dataMode.js 中的 API 替换 '["type"]...'
- 行 1434: 考虑使用 dataMode.js 中的 API 替换 '[{
                    "id": q.id,
               ...'
- 行 1533: 考虑使用 dataMode.js 中的 API 替换 '[{
                    "id": q.id,
               ...'


### backend\app\api\endpoints\mobile_notifications.py

- 行 58: 考虑将 '{
                "id": alert.id,
                ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 67: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 106: 考虑将 '{
            "status": "success",
            "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 197: 考虑将 '{
                "id": assessment.id,
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 207: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 309: 考虑将 '{
                "id": questionnaire.id,
        ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 320: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 392: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 457: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 491: 考虑将 '{"status": "error", "message": "未认证"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 494: 考虑将 '{"status": "error", "message": "缺少answers字段"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 502: 考虑将 '{"status": "error", "message": "无权限提交此评估量表"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 505: 考虑将 '{"status": "error", "message": "评估量表不存在"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 507: 考虑将 '{"status": "error", "message": "评估量表已完成，无法重复提交"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 513: 考虑将 '{"status": "error", "message": "评估量表模板不存在"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 568: 考虑将 '{"status": "error", "message": "答案格式错误"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 571: 考虑将 '{"status": "error", "message": f"问题ID {question_id...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 574: 考虑将 '{"status": "error", "message": f"问题 {question_id}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 635: 考虑将 '{"total": total_score}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 647: 考虑将 '{
            "status": "success",
            "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 663: 考虑将 '{"status": "error", "message": f"提交评估量表失败: {str(e)...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 679: 考虑将 '{"status": "error", "message": "未认证"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 682: 考虑将 '{"status": "error", "message": "缺少answers字段"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 690: 考虑将 '{"status": "error", "message": "无权限提交此问卷"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 693: 考虑将 '{"status": "error", "message": "问卷不存在"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 695: 考虑将 '{"status": "error", "message": "问卷已完成，无法重复提交"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 701: 考虑将 '{"status": "error", "message": "问卷模板不存在"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 709: 考虑将 '{"status": "error", "message": "答案格式错误"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 712: 考虑将 '{"status": "error", "message": f"问题ID {question_id...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 715: 考虑将 '{"status": "error", "message": f"问题 {question_id}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 765: 考虑将 '{
            "status": "success",
            "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 779: 考虑将 '{"status": "error", "message": f"提交问卷失败: {str(e)}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 183: 考虑将 '[
                        {
                      ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 58: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": alert.id,
                ...'
- 行 67: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 106: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "me...'
- 行 197: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": assessment.id,
           ...'
- 行 207: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 309: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": questionnaire.id,
        ...'
- 行 320: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 392: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 457: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 491: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": "未认证"}...'
- 行 494: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": "缺少answers字段"}...'
- 行 502: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": "无权限提交此评估量表"}...'
- 行 505: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": "评估量表不存在"}...'
- 行 507: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": "评估量表已完成，无法重复提交"}...'
- 行 513: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": "评估量表模板不存在"}...'
- 行 568: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": "答案格式错误"}...'
- 行 571: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": f"问题ID {question_id...'
- 行 574: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": f"问题 {question_id}...'
- 行 635: 考虑使用 dataMode.js 中的 API 替换 '{"total": total_score}...'
- 行 647: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "me...'
- 行 663: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": f"提交评估量表失败: {str(e)...'
- 行 679: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": "未认证"}...'
- 行 682: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": "缺少answers字段"}...'
- 行 690: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": "无权限提交此问卷"}...'
- 行 693: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": "问卷不存在"}...'
- 行 695: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": "问卷已完成，无法重复提交"}...'
- 行 701: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": "问卷模板不存在"}...'
- 行 709: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": "答案格式错误"}...'
- 行 712: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": f"问题ID {question_id...'
- 行 715: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": f"问题 {question_id}...'
- 行 765: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "me...'
- 行 779: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": f"提交问卷失败: {str(e)}...'
- 行 183: 考虑使用 dataMode.js 中的 API 替换 '[
                        {
                      ...'


### backend\app\api\endpoints\mobile_scoring_api.py

- 行 462: 考虑将 '{
            "status": "success",
            "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 597: 考虑将 '{
            "status": "success",
            "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 74: 考虑将 '["name"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 104: 考虑将 'total = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 153: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 462: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "me...'
- 行 597: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "me...'
- 行 74: 考虑使用 dataMode.js 中的 API 替换 '["name"]...'
- 行 104: 考虑使用 dataMode.js 中的 API 替换 'total = 0...'
- 行 153: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'


### backend\app\api\endpoints\monitoring.py

- 行 63: 考虑将 '{
                            'name': stage_name,
...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 89: 考虑将 '{
                        'timestamp': item.get('t...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 133: 考虑将 '{
                    "labels": trend_labels,
    ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 140: 考虑将 '{
                            "label": "问卷完成数", 
 ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 175: 考虑将 '{
                    "labels": distribution_label...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 182: 考虑将 '{
                            "label": "问卷分发数",
  ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 205: 考虑将 '{
                    "status": "error",
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 223: 考虑将 '{
                    "status": "error",
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 239: 考虑将 '{
                    "status": "error",
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 280: 考虑将 '{
                "status": "healthy",
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 332: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 365: 考虑将 '{
                "status": "success",
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 373: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 417: 考虑将 '{
                "status": "success",
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 426: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 451: 考虑将 '{
            "report_type": "assessment_questionn...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 464: 考虑将 '{
            "status": "success",
            "ti...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 489: 考虑将 '{
            "report_type": "monitoring_report",
...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 506: 考虑将 '{
            "status": "success",
            "re...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 591: 考虑将 '{
            "id": user.id,
            "username...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 670: 考虑将 '{
            "status": "success",
            "ti...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 695: 考虑将 '{
            "timestamp": datetime.now().isoforma...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 73: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 76: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 301: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 309: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 317: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 325: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 409: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 63: 考虑使用 dataMode.js 中的 API 替换 '{
                            'name': stage_name,
...'
- 行 89: 考虑使用 dataMode.js 中的 API 替换 '{
                        'timestamp': item.get('t...'
- 行 133: 考虑使用 dataMode.js 中的 API 替换 '{
                    "labels": trend_labels,
    ...'
- 行 140: 考虑使用 dataMode.js 中的 API 替换 '{
                            "label": "问卷完成数", 
 ...'
- 行 175: 考虑使用 dataMode.js 中的 API 替换 '{
                    "labels": distribution_label...'
- 行 182: 考虑使用 dataMode.js 中的 API 替换 '{
                            "label": "问卷分发数",
  ...'
- 行 205: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "error",
         ...'
- 行 223: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "error",
         ...'
- 行 239: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "error",
         ...'
- 行 280: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "healthy",
           ...'
- 行 332: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 365: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "success",
           ...'
- 行 373: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 417: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "success",
           ...'
- 行 426: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 451: 考虑使用 dataMode.js 中的 API 替换 '{
            "report_type": "assessment_questionn...'
- 行 464: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "ti...'
- 行 489: 考虑使用 dataMode.js 中的 API 替换 '{
            "report_type": "monitoring_report",
...'
- 行 506: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "re...'
- 行 591: 考虑使用 dataMode.js 中的 API 替换 '{
            "id": user.id,
            "username...'
- 行 670: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "ti...'
- 行 695: 考虑使用 dataMode.js 中的 API 替换 '{
            "timestamp": datetime.now().isoforma...'
- 行 73: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 76: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 301: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'
- 行 309: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'
- 行 317: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'
- 行 325: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'
- 行 409: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'
- 行 196: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\app\api\endpoints\ocr.py

- 行 28: 考虑将 '{"status": "error", "message": "OCR服务不可用"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 31: 考虑将 '{"status": "error", "message": "OCR服务不可用", "ocr_st...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 137: 考虑将 '{
        "status": "success",
        "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 162: 考虑将 '{
            "status": "error",
            "mess...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 210: 考虑将 '{
        "status": "success",
        "data": res...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 28: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": "OCR服务不可用"}...'
- 行 31: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": "OCR服务不可用", "ocr_st...'
- 行 137: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "message": ...'
- 行 162: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "error",
            "mess...'
- 行 210: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "data": res...'


### backend\app\api\endpoints\other_records.py

- 行 65: 考虑将 '{"total": 0, "records": []}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 100: 考虑将 '{"total": total, "records": ensure_list_field_comp...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 103: 考虑将 '{"total": 0, "records": []}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 107: 考虑将 '{"total": 0, "records": []}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 310: 考虑将 '{"total": total, "records": ensure_list_field_comp...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 312: 考虑将 '{"total": 0, "records": []}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 315: 考虑将 '{"total": 0, "records": []}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 263: 考虑将 'length=1...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 65: 考虑使用 dataMode.js 中的 API 替换 '{"total": 0, "records": []}...'
- 行 100: 考虑使用 dataMode.js 中的 API 替换 '{"total": total, "records": ensure_list_field_comp...'
- 行 103: 考虑使用 dataMode.js 中的 API 替换 '{"total": 0, "records": []}...'
- 行 107: 考虑使用 dataMode.js 中的 API 替换 '{"total": 0, "records": []}...'
- 行 310: 考虑使用 dataMode.js 中的 API 替换 '{"total": total, "records": ensure_list_field_comp...'
- 行 312: 考虑使用 dataMode.js 中的 API 替换 '{"total": 0, "records": []}...'
- 行 315: 考虑使用 dataMode.js 中的 API 替换 '{"total": 0, "records": []}...'
- 行 263: 考虑使用 dataMode.js 中的 API 替换 'length=1...'


### backend\app\api\endpoints\performance_metrics.py

- 行 149: 考虑将 '{
            "status": health_status,
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 149: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": health_status,
           ...'


### backend\app\api\endpoints\permissions.py

- 行 153: 考虑将 '{
        "status": "success",
        "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 414: 考虑将 '{
        "status": "success",
        "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 475: 考虑将 '{
            "status": "success",
            "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 511: 考虑将 '{
            "status": "success",
            "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 46: 考虑将 '[
        {"id": "mobile_upload_document", "name":...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 53: 考虑将 '[
        {"id": "backend_user_management", "name"...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 60: 考虑将 '[
        {"id": "frontend_dashboard", "name": "仪表...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 153: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "message": ...'
- 行 414: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "message": ...'
- 行 475: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "me...'
- 行 511: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "me...'
- 行 46: 考虑使用 dataMode.js 中的 API 替换 '[
        {"id": "mobile_upload_document", "name":...'
- 行 53: 考虑使用 dataMode.js 中的 API 替换 '[
        {"id": "backend_user_management", "name"...'
- 行 60: 考虑使用 dataMode.js 中的 API 替换 '[
        {"id": "frontend_dashboard", "name": "仪表...'


### backend\app\api\endpoints\physical_exams.py

- 行 83: 考虑将 '{
            "status": "success",
            "to...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 91: 考虑将 '{
            "status": "success",
            "to...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 83: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "to...'
- 行 91: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "to...'


### backend\app\api\endpoints\questionnaire.py

- 行 116: 考虑将 '{
        "status": "success",
        "data": {
 ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 171: 考虑将 '{
        "status": "success",
        "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 228: 考虑将 '{
            "status": "success",
            "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 300: 考虑将 '{
        "status": "success",
        "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 341: 考虑将 '{
        "status": "success",
        "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 104: 考虑将 '[
        {
            "id": q.id,
            "q...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 216: 考虑将 '[
            {
                "id": q.id,
      ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 116: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "data": {
 ...'
- 行 171: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "message": ...'
- 行 228: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "me...'
- 行 300: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "message": ...'
- 行 341: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "message": ...'
- 行 104: 考虑使用 dataMode.js 中的 API 替换 '[
        {
            "id": q.id,
            "q...'
- 行 216: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "id": q.id,
      ...'


### backend\app\api\endpoints\questionnaires.py

- 行 29: 考虑将 '{
            "id": template.id,
            "temp...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 69: 考虑将 '{
            "id": questionnaire.id,
            ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 141: 考虑将 '{
                "id": q.id,
                "tit...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 159: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 166: 考虑将 '{
            "status": "error",
            "mess...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 202: 考虑将 '{
        "status": "success",
        "data": {
 ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 258: 考虑将 '{
        "status": "success",
        "questions"...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 300: 考虑将 '{
        "status": "success",
        "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 322: 考虑将 '{
        "status": "success",
        "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 350: 考虑将 '{
        "status": "success",
        "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 392: 考虑将 '{"status": "success", "message": message}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 410: 考虑将 '{"status": "success", "message": "推送成功"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 446: 考虑将 '{"status": "success", "message": "批量导入题目成功"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 606: 考虑将 '{"status": "success", "message": "问卷分发成功"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 700: 考虑将 '{
                "id": q.id,
                "tit...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 726: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 762: 考虑将 '{
                "id": response.id,
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 777: 考虑将 '{
        "status": "success",
        "data": {
 ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 837: 考虑将 '{
            "status": "success",
            "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 40: 考虑将 '[
                {
                    "id": q.id...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 237: 考虑将 '[
        {
            "id": item.id,
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 29: 考虑使用 dataMode.js 中的 API 替换 '{
            "id": template.id,
            "temp...'
- 行 69: 考虑使用 dataMode.js 中的 API 替换 '{
            "id": questionnaire.id,
            ...'
- 行 141: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": q.id,
                "tit...'
- 行 159: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 166: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "error",
            "mess...'
- 行 202: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "data": {
 ...'
- 行 258: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "questions"...'
- 行 300: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "message": ...'
- 行 322: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "message": ...'
- 行 350: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "message": ...'
- 行 392: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "message": message}...'
- 行 410: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "message": "推送成功"}...'
- 行 446: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "message": "批量导入题目成功"}...'
- 行 606: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "message": "问卷分发成功"}...'
- 行 700: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": q.id,
                "tit...'
- 行 726: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 762: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": response.id,
             ...'
- 行 777: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "data": {
 ...'
- 行 837: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "me...'
- 行 40: 考虑使用 dataMode.js 中的 API 替换 '[
                {
                    "id": q.id...'
- 行 237: 考虑使用 dataMode.js 中的 API 替换 '[
        {
            "id": item.id,
           ...'


### backend\app\api\endpoints\questionnaire_distributions.py

- 行 69: 考虑将 '{
            "id": distribution.id,
            "...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 89: 考虑将 '{
        "status": "success",
        "data": res...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 129: 考虑将 '{
        "status": "success",
        "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 69: 考虑使用 dataMode.js 中的 API 替换 '{
            "id": distribution.id,
            "...'
- 行 89: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "data": res...'
- 行 129: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "message": ...'


### backend\app\api\endpoints\questionnaire_responses.py

- 行 75: 考虑将 '{
        "status": "success",
        "data": {
 ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 159: 考虑将 '{
            "id": response.id,
            "ques...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 178: 考虑将 '{
        "status": "success",
        "data": res...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 218: 考虑将 '{
        "status": "success",
        "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 75: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "data": {
 ...'
- 行 159: 考虑使用 dataMode.js 中的 API 替换 '{
            "id": response.id,
            "ques...'
- 行 178: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "data": res...'
- 行 218: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "message": ...'


### backend\app\api\endpoints\questionnaire_results.py

- 行 75: 考虑将 '{
        "success": True,
        "data": [
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 170: 考虑将 '{
        "success": True,
        "data": {
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 226: 考虑将 '{
        "success": True,
        "data": {
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 291: 考虑将 '{
        "success": True,
        "data": {
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 413: 考虑将 '{
        "success": True,
        "data": {
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 456: 考虑将 '{
            "success": True,
            "messag...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 500: 考虑将 '{
        "success": True,
        "message": "计算完...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 742: 考虑将 '{
        "code": 200,
        "message": "获取成功",
...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 77: 考虑将 '[
            {
                "id": result.Quest...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 285: 考虑将 '["id", "questionnaire_id", "custom_id", "created_a...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 559: 考虑将 'Size=18...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 75: 考虑使用 dataMode.js 中的 API 替换 '{
        "success": True,
        "data": [
     ...'
- 行 170: 考虑使用 dataMode.js 中的 API 替换 '{
        "success": True,
        "data": {
     ...'
- 行 226: 考虑使用 dataMode.js 中的 API 替换 '{
        "success": True,
        "data": {
     ...'
- 行 291: 考虑使用 dataMode.js 中的 API 替换 '{
        "success": True,
        "data": {
     ...'
- 行 413: 考虑使用 dataMode.js 中的 API 替换 '{
        "success": True,
        "data": {
     ...'
- 行 456: 考虑使用 dataMode.js 中的 API 替换 '{
            "success": True,
            "messag...'
- 行 500: 考虑使用 dataMode.js 中的 API 替换 '{
        "success": True,
        "message": "计算完...'
- 行 742: 考虑使用 dataMode.js 中的 API 替换 '{
        "code": 200,
        "message": "获取成功",
...'
- 行 77: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "id": result.Quest...'
- 行 285: 考虑使用 dataMode.js 中的 API 替换 '["id", "questionnaire_id", "custom_id", "created_a...'
- 行 559: 考虑使用 dataMode.js 中的 API 替换 'Size=18...'


### backend\app\api\endpoints\roles.py

- 行 18: 考虑将 '[
    {"id": 1, "name": "admin", "description": "超...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 38: 考虑将 '["id"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 18: 考虑使用 dataMode.js 中的 API 替换 '[
    {"id": 1, "name": "admin", "description": "超...'
- 行 38: 考虑使用 dataMode.js 中的 API 替换 '["id"]...'


### backend\app\api\endpoints\role_applications.py

- 行 294: 考虑将 '{
        "status": "success",
        "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 258: 考虑将 'size = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 294: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "message": ...'
- 行 258: 考虑使用 dataMode.js 中的 API 替换 'size = 0...'


### backend\app\api\endpoints\service_stats.py

- 行 98: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 108: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 118: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 125: 考虑将 '{
            "status": "success",
            "ti...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 142: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 300: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 317: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 349: 考虑将 '{
                "status": "success",
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 379: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 397: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 438: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 473: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 98: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 108: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 118: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 125: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "ti...'
- 行 142: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 300: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 317: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 349: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "success",
           ...'
- 行 379: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 397: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 438: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 473: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'


### backend\app\api\endpoints\simple_api.py

- 行 77: 考虑将 '{
        "status": "success",
        "total": le...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 100: 考虑将 '{
        "status": "success",
        "data": rec...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 126: 考虑将 '{
        "status": "success",
        "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 153: 考虑将 '{
        "status": "success",
        "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 164: 考虑将 '{
        "status": "success",
        "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 56: 考虑将 '[
        {
            "id": 1,
            "cust...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 77: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "total": le...'
- 行 100: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "data": rec...'
- 行 126: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "message": ...'
- 行 153: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "message": ...'
- 行 164: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "message": ...'
- 行 56: 考虑使用 dataMode.js 中的 API 替换 '[
        {
            "id": 1,
            "cust...'


### backend\app\api\endpoints\templates.py

- 行 49: 考虑将 '{
                    "id": template.id,
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 99: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 105: 考虑将 '{
            "status": "error",
            "mess...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 193: 考虑将 '{
                "status": "success",
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 206: 考虑将 '{
                "status": "success",
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 258: 考虑将 '{"status": "success", "message": "获取成功", "data": d...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 534: 考虑将 '{"status": "success", "message": "量表分发成功", "count"...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 565: 考虑将 '{
                        "id": template.id,
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 633: 考虑将 '{
                                "id": tpl.id,
  ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 651: 考虑将 '{
            "code": 200,
            "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 748: 考虑将 '{
                "status": "success",
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 762: 考虑将 '{
                "status": "success",
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 809: 考虑将 '{"status": "success", "message": "获取成功", "data": d...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 913: 考虑将 '{
            "status": "success",
            "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 955: 考虑将 '{
            "status": "success",
            "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 977: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 982: 考虑将 '{
            "status": "error",
            "mess...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1159: 考虑将 '{"status": "success", "message": "问卷分发成功", "count"...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1187: 考虑将 '{
        "status": "success",
        "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 210: 考虑将 '["name"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 767: 考虑将 '["name"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 456: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1110: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 49: 考虑使用 dataMode.js 中的 API 替换 '{
                    "id": template.id,
         ...'
- 行 99: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 105: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "error",
            "mess...'
- 行 193: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "success",
           ...'
- 行 206: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "success",
           ...'
- 行 258: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "message": "获取成功", "data": d...'
- 行 534: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "message": "量表分发成功", "count"...'
- 行 565: 考虑使用 dataMode.js 中的 API 替换 '{
                        "id": template.id,
     ...'
- 行 633: 考虑使用 dataMode.js 中的 API 替换 '{
                                "id": tpl.id,
  ...'
- 行 651: 考虑使用 dataMode.js 中的 API 替换 '{
            "code": 200,
            "message": ...'
- 行 748: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "success",
           ...'
- 行 762: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "success",
           ...'
- 行 809: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "message": "获取成功", "data": d...'
- 行 913: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "me...'
- 行 955: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "me...'
- 行 977: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 982: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "error",
            "mess...'
- 行 1159: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "message": "问卷分发成功", "count"...'
- 行 1187: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "message": ...'
- 行 210: 考虑使用 dataMode.js 中的 API 替换 '["name"]...'
- 行 767: 考虑使用 dataMode.js 中的 API 替换 '["name"]...'
- 行 456: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'
- 行 1110: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'
- 行 976: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\app\api\endpoints\update_password.py

- 行 81: 考虑将 '{
            "status": "success",
            "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 81: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "me...'


### backend\app\api\endpoints\users.py

- 行 265: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 305: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 316: 考虑将 '{
            "status": "error",
            "mess...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 336: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 362: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 368: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 374: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 382: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 390: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 399: 考虑将 '{
                    "status": "error",
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 480: 考虑将 '{
            "status": "success",
            "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 490: 考虑将 '{
            "status": "error",
            "mess...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 631: 考虑将 '{
            "status": "success",
            "it...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 642: 考虑将 '{
            "status": "error",
            "mess...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 735: 考虑将 '{
        "status": "success",
        "data": {
 ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 810: 考虑将 '{
        "status": "success",
        "data": use...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 941: 考虑将 '{
            "status": "success",
            "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 969: 考虑将 '{
            "status": "error",
            "mess...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1002: 考虑将 '{
        "status": "success",
        "user": {
 ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1051: 考虑将 '{
                    "status": "error",
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1060: 考虑将 '{
                    "status": "error",
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1080: 考虑将 '{
                    "status": "error",
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1111: 考虑将 '{
            "status": "success",
            "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1135: 考虑将 '{
            "status": "error",
            "mess...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1266: 考虑将 '{
            "status": "success",
            "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1295: 考虑将 '{
            "status": "error",
            "mess...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1388: 考虑将 '{
            "status": "success",
            "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1487: 考虑将 '{
                    "status": "warning",
       ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1497: 考虑将 '{
                "status": "success",
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1582: 考虑将 '{
                "status": "success",
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 289: 考虑将 '["id"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 265: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 305: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 316: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "error",
            "mess...'
- 行 336: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 362: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 368: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 374: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 382: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 390: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 399: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "error",
         ...'
- 行 480: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "me...'
- 行 490: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "error",
            "mess...'
- 行 631: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "it...'
- 行 642: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "error",
            "mess...'
- 行 735: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "data": {
 ...'
- 行 810: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "data": use...'
- 行 941: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "me...'
- 行 969: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "error",
            "mess...'
- 行 1002: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "user": {
 ...'
- 行 1051: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "error",
         ...'
- 行 1060: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "error",
         ...'
- 行 1080: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "error",
         ...'
- 行 1111: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "me...'
- 行 1135: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "error",
            "mess...'
- 行 1266: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "me...'
- 行 1295: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "error",
            "mess...'
- 行 1388: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "me...'
- 行 1487: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "warning",
       ...'
- 行 1497: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "success",
           ...'
- 行 1582: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "success",
           ...'
- 行 289: 考虑使用 dataMode.js 中的 API 替换 '["id"]...'


### backend\app\api\endpoints\user_health_records.py

- 行 46: 考虑将 '{"status": "success", "total": 0, "items": []}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 59: 考虑将 '{
                "id": record.id,
               ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 74: 考虑将 '{"status": "success", "total": total, "items": ite...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 77: 考虑将 '{"status": "error", "total": 0, "items": [], "mess...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 79: 考虑将 '{
    # 修改health类型的处理方式
    "health": lambda db, c...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 84: 考虑将 '{k: v for k, v in kwargs.items() if k != 'status'}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 85: 考虑将 '{k: v for k, v in kwargs.items() if k != 'status'}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 91: 考虑将 '{k: v for k, v in kwargs.items() if k != 'status'}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 126: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 175: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 216: 考虑将 '{"status": "error", "message": "用户不存在", "data": {"...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 231: 考虑将 '{
                    "id": distribution.id,
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 269: 考虑将 '{
                    "id": response.id,
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 295: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 304: 考虑将 '{"status": "error", "message": str(e), "data": {"t...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 323: 考虑将 '{"status": "error", "message": "用户不存在", "data": {"...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 338: 考虑将 '{
                    "id": distribution.id,
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 377: 考虑将 '{
                    "id": response.id,
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 403: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 412: 考虑将 '{"status": "error", "message": str(e), "data": {"t...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 464: 考虑将 '{
                    "status": "success",
       ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 517: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 58: 考虑将 '[
            {
                "id": record.id,
 ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 130: 考虑将 '[
                    {
                        "i...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 474: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 46: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "total": 0, "items": []}...'
- 行 59: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": record.id,
               ...'
- 行 74: 考虑使用 dataMode.js 中的 API 替换 '{"status": "success", "total": total, "items": ite...'
- 行 77: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "total": 0, "items": [], "mess...'
- 行 79: 考虑使用 dataMode.js 中的 API 替换 '{
    # 修改health类型的处理方式
    "health": lambda db, c...'
- 行 84: 考虑使用 dataMode.js 中的 API 替换 '{k: v for k, v in kwargs.items() if k != 'status'}...'
- 行 85: 考虑使用 dataMode.js 中的 API 替换 '{k: v for k, v in kwargs.items() if k != 'status'}...'
- 行 91: 考虑使用 dataMode.js 中的 API 替换 '{k: v for k, v in kwargs.items() if k != 'status'}...'
- 行 126: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 175: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 216: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": "用户不存在", "data": {"...'
- 行 231: 考虑使用 dataMode.js 中的 API 替换 '{
                    "id": distribution.id,
     ...'
- 行 269: 考虑使用 dataMode.js 中的 API 替换 '{
                    "id": response.id,
         ...'
- 行 295: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 304: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": str(e), "data": {"t...'
- 行 323: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": "用户不存在", "data": {"...'
- 行 338: 考虑使用 dataMode.js 中的 API 替换 '{
                    "id": distribution.id,
     ...'
- 行 377: 考虑使用 dataMode.js 中的 API 替换 '{
                    "id": response.id,
         ...'
- 行 403: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 412: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": str(e), "data": {"t...'
- 行 464: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "success",
       ...'
- 行 517: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 58: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "id": record.id,
 ...'
- 行 130: 考虑使用 dataMode.js 中的 API 替换 '[
                    {
                        "i...'
- 行 474: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'


### backend\app\api\endpoints\user_health_records_fixed.py

- 行 36: 考虑将 '{
                "id": 1,
                "custom...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 45: 考虑将 '{
                "id": 2,
                "custom...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 65: 考虑将 '{
                "records": paginated_records,
  ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 35: 考虑将 '[
            {
                "id": 1,
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 36: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": 1,
                "custom...'
- 行 45: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": 2,
                "custom...'
- 行 65: 考虑使用 dataMode.js 中的 API 替换 '{
                "records": paginated_records,
  ...'
- 行 35: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "id": 1,
         ...'


### backend\app\api\utils\user_response.py

- 行 135: 考虑将 '{
        "status": status,
        "data": data
 ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 135: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": status,
        "data": data
 ...'


### backend\app\api\v1\aggregated.py

- 行 50: 考虑将 '{
            "status": "ok",
            "service...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 63: 考虑将 '{
            "status": "error",
            "serv...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 480: 考虑将 '{
                    "data": result.data,
       ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 775: 考虑将 '{
            "status": "healthy",
            "ti...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 784: 考虑将 '{
                "status": cache_status
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 887: 考虑将 '{
                "id": 1,
                "name":...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 895: 考虑将 '{
                "id": 2,
                "name":...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 906: 考虑将 '{
                "templates": templates[skip:skip...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 940: 考虑将 '{
                "id": 1,
                "name":...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 948: 考虑将 '{
                "id": 2,
                "name":...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 959: 考虑将 '{
                "templates": templates[skip:skip...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 175: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 358: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 446: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 886: 考虑将 '[
            {
                "id": 1,
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 939: 考虑将 '[
            {
                "id": 1,
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 50: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "ok",
            "service...'
- 行 63: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "error",
            "serv...'
- 行 480: 考虑使用 dataMode.js 中的 API 替换 '{
                    "data": result.data,
       ...'
- 行 775: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "healthy",
            "ti...'
- 行 784: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": cache_status
         ...'
- 行 887: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": 1,
                "name":...'
- 行 895: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": 2,
                "name":...'
- 行 906: 考虑使用 dataMode.js 中的 API 替换 '{
                "templates": templates[skip:skip...'
- 行 940: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": 1,
                "name":...'
- 行 948: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": 2,
                "name":...'
- 行 959: 考虑使用 dataMode.js 中的 API 替换 '{
                "templates": templates[skip:skip...'
- 行 175: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 358: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 446: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 886: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "id": 1,
         ...'
- 行 939: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "id": 1,
         ...'
- 行 781: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\app\api\v1\data_management.py

- 行 54: 考虑将 '{
            "status": "ok",
            "service...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 69: 考虑将 '{
            "status": "error",
            "serv...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 603: 考虑将 '{
                "table_name": table_name,
      ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 54: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "ok",
            "service...'
- 行 69: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "error",
            "serv...'
- 行 603: 考虑使用 dataMode.js 中的 API 替换 '{
                "table_name": table_name,
      ...'


### backend\app\clinical_scales\assessment\hamilton_depression.py

- 行 11: 考虑将 '{
    "id": 5,
    "template_key": "hamilton_depre...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 25: 考虑将 '[  # 维度定义
        {
            "key": "mood_sympt...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 11: 考虑使用 dataMode.js 中的 API 替换 '{
    "id": 5,
    "template_key": "hamilton_depre...'
- 行 25: 考虑使用 dataMode.js 中的 API 替换 '[  # 维度定义
        {
            "key": "mood_sympt...'


### backend\app\clinical_scales\assessment\mmse.py

- 行 22: 考虑将 '{
    "id": 3,  # 唯一标识符
    "template_key": "mmse"...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 42: 考虑将 '[  # 维度定义
        {
            "key": "orientatio...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 22: 考虑使用 dataMode.js 中的 API 替换 '{
    "id": 3,  # 唯一标识符
    "template_key": "mmse"...'
- 行 42: 考虑使用 dataMode.js 中的 API 替换 '[  # 维度定义
        {
            "key": "orientatio...'


### backend\app\clinical_scales\assessment\moca.py

- 行 49: 考虑将 '{
    "id": 4,  # 唯一标识符
    "template_key": "moca"...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 49: 考虑使用 dataMode.js 中的 API 替换 '{
    "id": 4,  # 唯一标识符
    "template_key": "moca"...'


### backend\app\clinical_scales\assessment\sas.py

- 行 20: 考虑将 '{
    "id": 2,
    "template_key": "sas",
    "nam...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 40: 考虑将 '[  # 维度定义
        {
            "key": "anxiety_em...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 20: 考虑使用 dataMode.js 中的 API 替换 '{
    "id": 2,
    "template_key": "sas",
    "nam...'
- 行 40: 考虑使用 dataMode.js 中的 API 替换 '[  # 维度定义
        {
            "key": "anxiety_em...'


### backend\app\clinical_scales\assessment\sds.py

- 行 20: 考虑将 '{
    "id": 1,
    "template_key": "sds",
    "nam...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 40: 考虑将 '[  # 维度定义
        {
            "key": "depressive...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 20: 考虑使用 dataMode.js 中的 API 替换 '{
    "id": 1,
    "template_key": "sds",
    "nam...'
- 行 40: 考虑使用 dataMode.js 中的 API 替换 '[  # 维度定义
        {
            "key": "depressive...'


### backend\app\clinical_scales\assessment\__init__.py

- 行 57: 考虑将 '[t.get('name') for t in ALL_ASSESSMENT_TEMPLATES]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 57: 考虑使用 dataMode.js 中的 API 替换 '[t.get('name') for t in ALL_ASSESSMENT_TEMPLATES]...'


### backend\app\clinical_scales\examples\dimension_usage_example.py

- 行 30: 考虑将 '[
        {
            "key": "cognitive",
      ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 187: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 30: 考虑使用 dataMode.js 中的 API 替换 '[
        {
            "key": "cognitive",
      ...'
- 行 187: 考虑使用 dataMode.js 中的 API 替换 '['name']...'


### backend\app\clinical_scales\generators\assessment_generator.py

- 行 53: 考虑将 '[{"key": "mood", "name": "情绪", "description": "情绪相...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 53: 考虑使用 dataMode.js 中的 API 替换 '[{"key": "mood", "name": "情绪", "description": "情绪相...'


### backend\app\clinical_scales\generators\questionnaire_generator.py

- 行 48: 考虑将 '[{"key": "physical", "name": "生理功能", "description"...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 178: 考虑将 '[
                {
                    "id": q.id...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 48: 考虑使用 dataMode.js 中的 API 替换 '[{"key": "physical", "name": "生理功能", "description"...'
- 行 178: 考虑使用 dataMode.js 中的 API 替换 '[
                {
                    "id": q.id...'


### backend\app\clinical_scales\generators\questionnaire_instance_generator.py

- 行 148: 考虑将 '{
                    "user_id": user.id,
        ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 157: 考虑将 '{
                    "user_id": user.id,
        ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 148: 考虑使用 dataMode.js 中的 API 替换 '{
                    "user_id": user.id,
        ...'
- 行 157: 考虑使用 dataMode.js 中的 API 替换 '{
                    "user_id": user.id,
        ...'


### backend\app\clinical_scales\questionnaire\health_questionnaire.py

- 行 10: 考虑将 '{
    "id": 1,  # 唯一标识符
    "template_key": "healt...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 21: 考虑将 '[  # 维度定义
        {
            "key": "basic_info...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 10: 考虑使用 dataMode.js 中的 API 替换 '{
    "id": 1,  # 唯一标识符
    "template_key": "healt...'
- 行 21: 考虑使用 dataMode.js 中的 API 替换 '[  # 维度定义
        {
            "key": "basic_info...'


### backend\app\clinical_scales\questionnaire\medical_history.py

- 行 10: 考虑将 '{
    "id": 5,  # 唯一标识符
    "template_key": "medic...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 10: 考虑使用 dataMode.js 中的 API 替换 '{
    "id": 5,  # 唯一标识符
    "template_key": "medic...'


### backend\app\clinical_scales\questionnaire\psqi.py

- 行 11: 考虑将 '{
    "id": 3,
    "template_key": "psqi",
    "na...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 31: 考虑将 '[  # 维度定义
        {
            "key": "sleep_qual...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 11: 考虑使用 dataMode.js 中的 API 替换 '{
    "id": 3,
    "template_key": "psqi",
    "na...'
- 行 31: 考虑使用 dataMode.js 中的 API 替换 '[  # 维度定义
        {
            "key": "sleep_qual...'


### backend\app\clinical_scales\questionnaire\satisfaction_survey.py

- 行 10: 考虑将 '{
    "id": 2,  # 唯一标识符
    "template_key": "satis...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 10: 考虑使用 dataMode.js 中的 API 替换 '{
    "id": 2,  # 唯一标识符
    "template_key": "satis...'


### backend\app\clinical_scales\questionnaire\sf36.py

- 行 10: 考虑将 '{
    "id": 4,  # 唯一标识符
    "template_key": "sf36"...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 21: 考虑将 '[  # 维度定义
        {
            "key": "physical_f...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 10: 考虑使用 dataMode.js 中的 API 替换 '{
    "id": 4,  # 唯一标识符
    "template_key": "sf36"...'
- 行 21: 考虑使用 dataMode.js 中的 API 替换 '[  # 维度定义
        {
            "key": "physical_f...'


### backend\app\clinical_scales\questionnaire\__init__.py

- 行 54: 考虑将 '[t.get('name') for t in ALL_QUESTIONNAIRE_TEMPLATE...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 54: 考虑使用 dataMode.js 中的 API 替换 '[t.get('name') for t in ALL_QUESTIONNAIRE_TEMPLATE...'


### backend\app\clinical_scales\services\dimension_service.py

- 行 266: 考虑将 '["name"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 272: 考虑将 '["name"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 305: 考虑将 '["name"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 266: 考虑使用 dataMode.js 中的 API 替换 '["name"]...'
- 行 272: 考虑使用 dataMode.js 中的 API 替换 '["name"]...'
- 行 305: 考虑使用 dataMode.js 中的 API 替换 '["name"]...'
- 行 257: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\app\core\aggregated_api_service.py

- 行 228: 考虑将 '{
                    'id': record.id,
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 294: 考虑将 '{
                    'id': dist.id,
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 362: 考虑将 '{
                    'id': response.id,
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 429: 考虑将 '{
                    'id': result_record.id,
    ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 511: 考虑将 '{
                    'id': dist.id,
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 644: 考虑将 '{
                    'id': result_record.id,
    ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 933: 考虑将 '{
                    'id': item.get('id'),
      ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 947: 考虑将 '{
                    'id': item.get('id'),
      ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 204: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 338: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 405: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 620: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 907: 考虑将 '[{'type': k, 'items': v} for k, v in grouped_data....' 迁移到 MockDataManager.get_mock_*() 方法
- 行 932: 考虑将 '[
                {
                    'id': item...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 946: 考虑将 '[
                {
                    'id': item...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 228: 考虑使用 dataMode.js 中的 API 替换 '{
                    'id': record.id,
           ...'
- 行 294: 考虑使用 dataMode.js 中的 API 替换 '{
                    'id': dist.id,
             ...'
- 行 362: 考虑使用 dataMode.js 中的 API 替换 '{
                    'id': response.id,
         ...'
- 行 429: 考虑使用 dataMode.js 中的 API 替换 '{
                    'id': result_record.id,
    ...'
- 行 511: 考虑使用 dataMode.js 中的 API 替换 '{
                    'id': dist.id,
             ...'
- 行 644: 考虑使用 dataMode.js 中的 API 替换 '{
                    'id': result_record.id,
    ...'
- 行 933: 考虑使用 dataMode.js 中的 API 替换 '{
                    'id': item.get('id'),
      ...'
- 行 947: 考虑使用 dataMode.js 中的 API 替换 '{
                    'id': item.get('id'),
      ...'
- 行 204: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 338: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 405: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 620: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 907: 考虑使用 dataMode.js 中的 API 替换 '[{'type': k, 'items': v} for k, v in grouped_data....'
- 行 932: 考虑使用 dataMode.js 中的 API 替换 '[
                {
                    'id': item...'
- 行 946: 考虑使用 dataMode.js 中的 API 替换 '[
                {
                    'id': item...'


### backend\app\core\alert_detector.py

- 行 181: 考虑将 '{
                        "current": error_rate,
 ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 198: 考虑将 '{
                        "current": error_rate_5x...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 211: 考虑将 '{
                    "status": db_status,
       ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 247: 考虑将 '{alert['count']}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 251: 考虑将 '{
                "id": alert_id,
                ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 696: 考虑将 '{
                "id": "test",
                "r...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 342: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 361: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 376: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 181: 考虑使用 dataMode.js 中的 API 替换 '{
                        "current": error_rate,
 ...'
- 行 198: 考虑使用 dataMode.js 中的 API 替换 '{
                        "current": error_rate_5x...'
- 行 211: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": db_status,
       ...'
- 行 247: 考虑使用 dataMode.js 中的 API 替换 '{alert['count']}...'
- 行 251: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": alert_id,
                ...'
- 行 696: 考虑使用 dataMode.js 中的 API 替换 '{
                "id": "test",
                "r...'
- 行 342: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 361: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 376: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 517: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 595: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\app\core\alert_manager.py

- 行 694: 考虑将 '['id']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 37: 考虑将 'size = 1000...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 694: 考虑使用 dataMode.js 中的 API 替换 '['id']...'
- 行 37: 考虑使用 dataMode.js 中的 API 替换 'size = 1000...'


### backend\app\core\api_service.py

- 行 202: 考虑将 '{
            "items": items,
            "paginat...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 179: 考虑将 '['type']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 181: 考虑将 '['type']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 202: 考虑使用 dataMode.js 中的 API 替换 '{
            "items": items,
            "paginat...'
- 行 179: 考虑使用 dataMode.js 中的 API 替换 '['type']...'
- 行 181: 考虑使用 dataMode.js 中的 API 替换 '['type']...'
- 行 306: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 315: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\app\core\auth_service.py

- 行 329: 考虑将 '{
                "status": "success",
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 335: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 341: 考虑将 '{
            "status": "error",
            "mess...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 329: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "success",
           ...'
- 行 335: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 341: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "error",
            "mess...'


### backend\app\core\business_logic.py

- 行 257: 考虑将 '["id"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 294: 考虑将 '["id"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 371: 考虑将 '["id"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 372: 考虑将 '['id']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 375: 考虑将 '["id"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 461: 考虑将 '["id"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 529: 考虑将 '["id"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 539: 考虑将 '["id"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 539: 考虑将 '["id"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 543: 考虑将 '["id"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 257: 考虑使用 dataMode.js 中的 API 替换 '["id"]...'
- 行 294: 考虑使用 dataMode.js 中的 API 替换 '["id"]...'
- 行 371: 考虑使用 dataMode.js 中的 API 替换 '["id"]...'
- 行 372: 考虑使用 dataMode.js 中的 API 替换 '['id']...'
- 行 375: 考虑使用 dataMode.js 中的 API 替换 '["id"]...'
- 行 461: 考虑使用 dataMode.js 中的 API 替换 '["id"]...'
- 行 529: 考虑使用 dataMode.js 中的 API 替换 '["id"]...'
- 行 539: 考虑使用 dataMode.js 中的 API 替换 '["id"]...'
- 行 539: 考虑使用 dataMode.js 中的 API 替换 '["id"]...'
- 行 543: 考虑使用 dataMode.js 中的 API 替换 '["id"]...'


### backend\app\core\database_utils.py

- 行 588: 考虑将 '{
                    "table_name": table_name,
  ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 588: 考虑使用 dataMode.js 中的 API 替换 '{
                    "table_name": table_name,
  ...'
- 行 498: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 622: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\app\core\data_export.py

- 行 446: 考虑将 '{
            'data': data,
            'metadata'...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 446: 考虑使用 dataMode.js 中的 API 替换 '{
            'data': data,
            'metadata'...'


### backend\app\core\db_connection.py

- 行 34: 考虑将 'size=5...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 47: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 34: 考虑使用 dataMode.js 中的 API 替换 'size=5...'
- 行 47: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'


### backend\app\core\distributed_lock.py

- 行 285: 考虑将 '["id"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 293: 考虑将 '['id']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 376: 考虑将 '["id"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 382: 考虑将 '['id']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 285: 考虑使用 dataMode.js 中的 API 替换 '["id"]...'
- 行 293: 考虑使用 dataMode.js 中的 API 替换 '['id']...'
- 行 376: 考虑使用 dataMode.js 中的 API 替换 '["id"]...'
- 行 382: 考虑使用 dataMode.js 中的 API 替换 '['id']...'


### backend\app\core\email_service.py

- 行 149: 考虑使用 dataMode.js 中的 API 替换 'size: 24...'
- 行 151: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 218: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 299: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'


### backend\app\core\error_handling.py

- 行 28: 考虑将 '{
    "total": 0,
    "by_type": {}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 117: 考虑将 '{error_counts['total']}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 163: 考虑将 '['type']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 28: 考虑使用 dataMode.js 中的 API 替换 '{
    "total": 0,
    "by_type": {}...'
- 行 117: 考虑使用 dataMode.js 中的 API 替换 '{error_counts['total']}...'
- 行 163: 考虑使用 dataMode.js 中的 API 替换 '['type']...'


### backend\app\core\file_utils.py

- 行 785: 考虑将 '{"count": 0, "size": 0}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 785: 考虑使用 dataMode.js 中的 API 替换 '{"count": 0, "size": 0}...'
- 行 639: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 665: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 684: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 779: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 782: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\app\core\health_monitor.py

- 行 84: 考虑将 '{
                "requests": {
                  ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 245: 考虑将 '{
                    "total": 0,
                ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 355: 考虑将 '{
                "status": overall_status,
      ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 367: 考虑将 '{
                    "status": application_status...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 374: 考虑将 '{
                    "status": dependencies_statu...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 390: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 453: 考虑将 '{
                    "status": "running",
       ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 495: 考虑将 '{
                        "status": "error",
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 522: 考虑将 '{
                    "status": "error",
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 84: 考虑使用 dataMode.js 中的 API 替换 '{
                "requests": {
                  ...'
- 行 245: 考虑使用 dataMode.js 中的 API 替换 '{
                    "total": 0,
                ...'
- 行 355: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": overall_status,
      ...'
- 行 367: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": application_status...'
- 行 374: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": dependencies_statu...'
- 行 390: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 453: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "running",
       ...'
- 行 495: 考虑使用 dataMode.js 中的 API 替换 '{
                        "status": "error",
     ...'
- 行 522: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "error",
         ...'


### backend\app\core\ip_limiter.py

- 行 155: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 183: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 155: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'
- 行 183: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'


### backend\app\core\logging_utils.py

- 行 141: 考虑将 '['name', 'msg', 'args', 'levelname', 'levelno', 'p...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 141: 考虑使用 dataMode.js 中的 API 替换 '['name', 'msg', 'args', 'levelname', 'levelno', 'p...'


### backend\app\core\mock_data_manager.py

- 行 256: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 274: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 316: 考虑将 '{
            "status": "healthy",
            "up...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 329: 考虑将 '{
                "status": "healthy",
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 336: 考虑将 '{
                "status": "healthy",
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 521: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 82: 考虑将 '[
                {
                    "date": (d...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 156: 考虑将 '[
            {"value": 10, "name": "实验室检验"},
    ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 172: 考虑将 '[
                {"name": "BMI", "max": 30},
    ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 191: 考虑将 '[
            {
                "date": (datetime....' 迁移到 MockDataManager.get_mock_*() 方法
- 行 256: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 274: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 316: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "healthy",
            "up...'
- 行 329: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "healthy",
           ...'
- 行 336: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "healthy",
           ...'
- 行 521: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 82: 考虑使用 dataMode.js 中的 API 替换 '[
                {
                    "date": (d...'
- 行 156: 考虑使用 dataMode.js 中的 API 替换 '[
            {"value": 10, "name": "实验室检验"},
    ...'
- 行 172: 考虑使用 dataMode.js 中的 API 替换 '[
                {"name": "BMI", "max": 30},
    ...'
- 行 191: 考虑使用 dataMode.js 中的 API 替换 '[
            {
                "date": (datetime....'


### backend\app\core\monitoring.py

- 行 346: 考虑将 '{
        "name": "memory",
        "status": memo...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 360: 考虑将 '{
        "name": "cpu",
        "status": cpu_sta...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 371: 考虑将 '{
        "name": "disk",
        "status": disk_s...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 393: 考虑将 '{
        "name": "database",
        "status": db...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 407: 考虑将 '{
        "status": system_status,
        "timest...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 24: 考虑将 '["method", "endpoint", "status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 39: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 48: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 57: 考虑将 '["type"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 71: 考虑将 '["type", "location"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 76: 考虑将 '["method", "status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 81: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 274: 考虑将 '["type"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 295: 考虑将 '["type"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 296: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 400: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 403: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 346: 考虑使用 dataMode.js 中的 API 替换 '{
        "name": "memory",
        "status": memo...'
- 行 360: 考虑使用 dataMode.js 中的 API 替换 '{
        "name": "cpu",
        "status": cpu_sta...'
- 行 371: 考虑使用 dataMode.js 中的 API 替换 '{
        "name": "disk",
        "status": disk_s...'
- 行 393: 考虑使用 dataMode.js 中的 API 替换 '{
        "name": "database",
        "status": db...'
- 行 407: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": system_status,
        "timest...'
- 行 24: 考虑使用 dataMode.js 中的 API 替换 '["method", "endpoint", "status"]...'
- 行 39: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'
- 行 48: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'
- 行 57: 考虑使用 dataMode.js 中的 API 替换 '["type"]...'
- 行 71: 考虑使用 dataMode.js 中的 API 替换 '["type", "location"]...'
- 行 76: 考虑使用 dataMode.js 中的 API 替换 '["method", "status"]...'
- 行 81: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'
- 行 274: 考虑使用 dataMode.js 中的 API 替换 '["type"]...'
- 行 295: 考虑使用 dataMode.js 中的 API 替换 '["type"]...'
- 行 296: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'
- 行 400: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'
- 行 403: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'


### backend\app\core\monitoring_utils.py

- 行 98: 考虑将 '{
            "component": self.component,
       ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 280: 考虑将 '{
            "count": count,
            "min": m...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 98: 考虑使用 dataMode.js 中的 API 替换 '{
            "component": self.component,
       ...'
- 行 280: 考虑使用 dataMode.js 中的 API 替换 '{
            "count": count,
            "min": m...'
- 行 250: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 436: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 457: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 458: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 459: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 460: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 704: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 708: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\app\core\mysql_config.py

- 行 165: 考虑将 '{"type": "sqlite", "status": "active"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 192: 考虑将 '{
                    "type": "mysql",
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 205: 考虑将 '{
                "type": "mysql",
               ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 165: 考虑使用 dataMode.js 中的 API 替换 '{"type": "sqlite", "status": "active"}...'
- 行 192: 考虑使用 dataMode.js 中的 API 替换 '{
                    "type": "mysql",
           ...'
- 行 205: 考虑使用 dataMode.js 中的 API 替换 '{
                "type": "mysql",
               ...'


### backend\app\core\notification_service.py

- 行 154: 考虑将 '{
            "id": self.id,
            "notifica...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 721: 考虑将 '["id"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 154: 考虑使用 dataMode.js 中的 API 替换 '{
            "id": self.id,
            "notifica...'
- 行 721: 考虑使用 dataMode.js 中的 API 替换 '["id"]...'
- 行 691: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 740: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\app\core\query_cache.py

- 行 238: 考虑将 '{
                    "data": data,
              ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 244: 考虑将 '{
                    "data": data,
              ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 276: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 238: 考虑使用 dataMode.js 中的 API 替换 '{
                    "data": data,
              ...'
- 行 244: 考虑使用 dataMode.js 中的 API 替换 '{
                    "data": data,
              ...'
- 行 276: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'


### backend\app\core\redis_enhanced.py

- 行 375: 考虑将 '{"status": "disabled"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 379: 考虑将 '{
                "status": "active",
            ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 393: 考虑将 '{"status": "error", "error": str(e)}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 375: 考虑使用 dataMode.js 中的 API 替换 '{"status": "disabled"}...'
- 行 379: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "active",
            ...'
- 行 393: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "error": str(e)}...'


### backend\app\core\redis_manager.py

- 行 114: 考虑将 '{
                    "data": self.data,
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 682: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 718: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 114: 考虑使用 dataMode.js 中的 API 替换 '{
                    "data": self.data,
         ...'
- 行 682: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'
- 行 718: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'


### backend\app\core\request_stats.py

- 行 61: 考虑将 '{
                    "total": 0,
                ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 104: 考虑将 '{
                    "total": stats["total"],
   ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 112: 考虑将 '{
                "total": self.total_requests,
  ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 142: 考虑将 '{
                "total": stats["total"],
       ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 61: 考虑使用 dataMode.js 中的 API 替换 '{
                    "total": 0,
                ...'
- 行 104: 考虑使用 dataMode.js 中的 API 替换 '{
                    "total": stats["total"],
   ...'
- 行 112: 考虑使用 dataMode.js 中的 API 替换 '{
                "total": self.total_requests,
  ...'
- 行 142: 考虑使用 dataMode.js 中的 API 替换 '{
                "total": stats["total"],
       ...'


### backend\app\core\security.py

- 行 82: 考虑将 '{username: {'count': int, 'last_attempt': timestam...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 109: 考虑将 '{'count': 0, 'last_attempt': 0}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 82: 考虑使用 dataMode.js 中的 API 替换 '{username: {'count': int, 'last_attempt': timestam...'
- 行 109: 考虑使用 dataMode.js 中的 API 替换 '{'count': 0, 'last_attempt': 0}...'


### backend\app\core\security_utils.py

- 行 333: 考虑将 '["type"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 337: 考虑将 '["sub", "type", "iat", "exp", "jti"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 449: 考虑将 'length=32...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 797: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 333: 考虑使用 dataMode.js 中的 API 替换 '["type"]...'
- 行 337: 考虑使用 dataMode.js 中的 API 替换 '["sub", "type", "iat", "exp", "jti"]...'
- 行 449: 考虑使用 dataMode.js 中的 API 替换 'length=32...'
- 行 797: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'


### backend\app\core\system_monitor.py

- 行 98: 考虑将 '{
                "requests": {
                  ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 163: 考虑将 '{
                "status": "healthy",
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 169: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 183: 考虑将 '{
                "status": "healthy" if redis_pin...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 190: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 98: 考虑使用 dataMode.js 中的 API 替换 '{
                "requests": {
                  ...'
- 行 163: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "healthy",
           ...'
- 行 169: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 183: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "healthy" if redis_pin...'
- 行 190: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'


### backend\app\core\task_scheduler.py

- 行 76: 考虑将 '{
            "task_id": self.task_id,
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 540: 考虑将 '{
                    "id": task_id,
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 549: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 921: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 76: 考虑使用 dataMode.js 中的 API 替换 '{
            "task_id": self.task_id,
           ...'
- 行 540: 考虑使用 dataMode.js 中的 API 替换 '{
                    "id": task_id,
             ...'
- 行 549: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'
- 行 921: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 411: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 415: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 564: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\app\core\token_manager.py

- 行 63: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 116: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 63: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'
- 行 116: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'


### backend\app\core\totp.py

- 行 29: 考虑将 'size = 300...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 30: 考虑将 'size = 6...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 29: 考虑使用 dataMode.js 中的 API 替换 'size = 300...'
- 行 30: 考虑使用 dataMode.js 中的 API 替换 'size = 6...'


### backend\app\core\two_factor_auth.py

- 行 40: 考虑将 'length = 6...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 43: 考虑将 'count = 10...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 44: 考虑将 'length = 8...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 40: 考虑使用 dataMode.js 中的 API 替换 'length = 6...'
- 行 43: 考虑使用 dataMode.js 中的 API 替换 'count = 10...'
- 行 44: 考虑使用 dataMode.js 中的 API 替换 'length = 8...'


### backend\app\core\validators.py

- 行 624: 考虑将 '["id", "title", "type"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 624: 考虑使用 dataMode.js 中的 API 替换 '["id", "title", "type"]...'
- 行 784: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\app\crud\health_record.py

- 行 161: 考虑将 '{"status": "error", "message": "用户不存在", "data": {"...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 189: 考虑将 '{
                    "id": questionnaire.id,
    ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 216: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 226: 考虑将 '{"status": "error", "message": str(e), "data": {"t...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 250: 考虑将 '{"status": "error", "message": "用户不存在", "data": {"...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 278: 考虑将 '{
                    "id": assessment.id,
       ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 305: 考虑将 '{
            "status": "success",
            "da...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 315: 考虑将 '{"status": "error", "message": str(e), "data": {"t...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 161: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": "用户不存在", "data": {"...'
- 行 189: 考虑使用 dataMode.js 中的 API 替换 '{
                    "id": questionnaire.id,
    ...'
- 行 216: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 226: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": str(e), "data": {"t...'
- 行 250: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": "用户不存在", "data": {"...'
- 行 278: 考虑使用 dataMode.js 中的 API 替换 '{
                    "id": assessment.id,
       ...'
- 行 305: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "da...'
- 行 315: 考虑使用 dataMode.js 中的 API 替换 '{"status": "error", "message": str(e), "data": {"t...'


### backend\app\data\assessment_templates.py

- 行 7: 考虑将 '[
    {
        "id": 1,
        "assessment_type"...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 7: 考虑使用 dataMode.js 中的 API 替换 '[
    {
        "id": 1,
        "assessment_type"...'


### backend\app\db\init_clinical_scales.py

- 行 45: 考虑将 '["name"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 101: 考虑将 '["name"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 45: 考虑使用 dataMode.js 中的 API 替换 '["name"]...'
- 行 101: 考虑使用 dataMode.js 中的 API 替换 '["name"]...'
- 行 25: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 95: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\app\db\init_db.py

- 行 136: 考虑将 '[col["name"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 154: 考虑将 '[col["name"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 180: 考虑将 '[col["name"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 136: 考虑使用 dataMode.js 中的 API 替换 '[col["name"]...'
- 行 154: 考虑使用 dataMode.js 中的 API 替换 '[col["name"]...'
- 行 180: 考虑使用 dataMode.js 中的 API 替换 '[col["name"]...'


### backend\app\db\init_standard_scales.py

- 行 155: 考虑将 '{
                    "custom_id": "1",  # 系统用户ID，...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 79: 考虑将 '["name"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 80: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 87: 考虑将 '["name"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 113: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 138: 考虑将 '["name"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 139: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 158: 考虑将 '["name"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 186: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 155: 考虑使用 dataMode.js 中的 API 替换 '{
                    "custom_id": "1",  # 系统用户ID，...'
- 行 79: 考虑使用 dataMode.js 中的 API 替换 '["name"]...'
- 行 80: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 87: 考虑使用 dataMode.js 中的 API 替换 '["name"]...'
- 行 113: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 138: 考虑使用 dataMode.js 中的 API 替换 '["name"]...'
- 行 139: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 158: 考虑使用 dataMode.js 中的 API 替换 '["name"]...'
- 行 186: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 77: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 136: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 149: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 147: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\app\db\init_templates.py

- 行 42: 考虑将 '{
            'template_key', 'assessment_type', '...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 42: 考虑使用 dataMode.js 中的 API 替换 '{
            'template_key', 'assessment_type', '...'


### backend\app\db\migrations\add_template_key_to_templates.py

- 行 92: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 108: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\app\db\migrations\create_service_stats.py

- 行 34: 考虑将 '[column["name"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 37: 考虑将 '["id", "timestamp", "request_count", "active_users...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 34: 考虑使用 dataMode.js 中的 API 替换 '[column["name"]...'
- 行 37: 考虑使用 dataMode.js 中的 API 替换 '["id", "timestamp", "request_count", "active_users...'


### backend\app\middleware\error_handler.py

- 行 45: 考虑将 '{
                    "status": "error",
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 57: 考虑将 '{
                    "status": "error",
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 69: 考虑将 '{
                    "status": "error",
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 81: 考虑将 '{
                    "status": "error",
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 93: 考虑将 '{
                    "status": "error",
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 106: 考虑将 '{
                    "status": "error",
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 127: 考虑将 '{
            "status": "error",
            "erro...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 149: 考虑将 '{
            "status": "success",
            "me...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 45: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "error",
         ...'
- 行 57: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "error",
         ...'
- 行 69: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "error",
         ...'
- 行 81: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "error",
         ...'
- 行 93: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "error",
         ...'
- 行 106: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "error",
         ...'
- 行 127: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "error",
            "erro...'
- 行 149: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "success",
            "me...'


### backend\app\middleware\response_wrapper.py

- 行 19: 考虑将 '{"code": 0, "message": "success", "data": data}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 19: 考虑使用 dataMode.js 中的 API 替换 '{"code": 0, "message": "success", "data": data}...'


### backend\app\routes\monitoring.py

- 行 67: 考虑将 '{
                        'name': stage_name,
    ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 93: 考虑将 '{
                    'timestamp': result.timestam...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 248: 考虑将 '{
                    'stage': get_stage_display_n...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 256: 考虑将 '{
                'user_id': user_id,
            ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 295: 考虑将 '{
                'timestamp': datetime.now().isof...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 77: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 80: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 260: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 154: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 67: 考虑使用 dataMode.js 中的 API 替换 '{
                        'name': stage_name,
    ...'
- 行 93: 考虑使用 dataMode.js 中的 API 替换 '{
                    'timestamp': result.timestam...'
- 行 248: 考虑使用 dataMode.js 中的 API 替换 '{
                    'stage': get_stage_display_n...'
- 行 256: 考虑使用 dataMode.js 中的 API 替换 '{
                'user_id': user_id,
            ...'
- 行 295: 考虑使用 dataMode.js 中的 API 替换 '{
                'timestamp': datetime.now().isof...'
- 行 77: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 80: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 260: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 154: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'
- 行 106: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\app\schemas\other_record.py

- 行 13: 考虑将 'length=1...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 13: 考虑将 'length=255...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 27: 考虑将 'length=1...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 27: 考虑将 'length=255...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 13: 考虑使用 dataMode.js 中的 API 替换 'length=1...'
- 行 13: 考虑使用 dataMode.js 中的 API 替换 'length=255...'
- 行 27: 考虑使用 dataMode.js 中的 API 替换 'length=1...'
- 行 27: 考虑使用 dataMode.js 中的 API 替换 'length=255...'


### backend\app\schemas\user.py

- 行 63: 考虑将 'length=8...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 71: 考虑将 'length=8...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 63: 考虑使用 dataMode.js 中的 API 替换 'length=8...'
- 行 71: 考虑使用 dataMode.js 中的 API 替换 'length=8...'


### backend\app\scripts\fix_api_responses.py

- 行 80: 考虑将 '{"users": ensure_list_field_compatibility(\2)\3',
...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 87: 考虑将 '{"data": ensure_field_compatibility(\2)\3',
      ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 114: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 80: 考虑使用 dataMode.js 中的 API 替换 '{"users": ensure_list_field_compatibility(\2)\3',
...'
- 行 87: 考虑使用 dataMode.js 中的 API 替换 '{"data": ensure_field_compatibility(\2)\3',
      ...'
- 行 114: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'


### backend\app\scripts\fix_db_models.py

- 行 69: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 69: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'


### backend\app\scripts\fix_model_relationships.py

- 行 82: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 82: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'


### backend\app\scripts\sync_templates.py

- 行 120: 考虑将 '{
            "status": "error",
            "mess...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 147: 考虑将 '{
        "status": "success",
        "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 176: 考虑将 '{
            "status": "error",
            "mess...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 201: 考虑将 '{
        "status": "success",
        "message": ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 26: 考虑将 '[{tpl.get('name', '')}]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 65: 考虑将 '[{tpl.get('name', '')}]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 120: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "error",
            "mess...'
- 行 147: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "message": ...'
- 行 176: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "error",
            "mess...'
- 行 201: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": "success",
        "message": ...'
- 行 26: 考虑使用 dataMode.js 中的 API 替换 '[{tpl.get('name', '')}]...'
- 行 65: 考虑使用 dataMode.js 中的 API 替换 '[{tpl.get('name', '')}]...'


### backend\app\services\db_service.py

- 行 33: 考虑将 'size = 5...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 33: 考虑使用 dataMode.js 中的 API 替换 'size = 5...'


### backend\app\services\health_service.py

- 行 40: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 58: 考虑将 '{
                "status": "ok",
                ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 65: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 130: 考虑将 '{
                    "status": "ok",
            ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 136: 考虑将 '{
                    "status": "error",
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 141: 考虑将 '{
                "status": "warning",
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 147: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 167: 考虑将 '{
                    "status": "ok",
            ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 173: 考虑将 '{
                    "status": "warning",
       ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 178: 考虑将 '{
                "status": "warning",
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 184: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 204: 考虑将 '{
                        "status": "warning",
   ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 209: 考虑将 '{
                        "status": "error",
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 225: 考虑将 '{
                    "status": "ok",
            ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 235: 考虑将 '{
                    "status": "error",
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 241: 考虑将 '{
                "status": "error",
             ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 263: 考虑将 '{
                    "status": "ok",
            ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 270: 考虑将 '{
                    "status": "error",
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 277: 考虑将 '{
                    "status": "error",
         ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 284: 考虑将 '{
            "status": "ok" if all_ok else "warni...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 40: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 58: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "ok",
                ...'
- 行 65: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 130: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "ok",
            ...'
- 行 136: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "error",
         ...'
- 行 141: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "warning",
           ...'
- 行 147: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 167: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "ok",
            ...'
- 行 173: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "warning",
       ...'
- 行 178: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "warning",
           ...'
- 行 184: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 204: 考虑使用 dataMode.js 中的 API 替换 '{
                        "status": "warning",
   ...'
- 行 209: 考虑使用 dataMode.js 中的 API 替换 '{
                        "status": "error",
     ...'
- 行 225: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "ok",
            ...'
- 行 235: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "error",
         ...'
- 行 241: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "error",
             ...'
- 行 263: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "ok",
            ...'
- 行 270: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "error",
         ...'
- 行 277: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "error",
         ...'
- 行 284: 考虑使用 dataMode.js 中的 API 替换 '{
            "status": "ok" if all_ok else "warni...'


### backend\app\services\id_generator.py

- 行 354: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 354: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'


### backend\app\services\notification_service.py

- 行 80: 考虑将 '['type']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 84: 考虑将 '["type"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 80: 考虑使用 dataMode.js 中的 API 替换 '['type']...'
- 行 84: 考虑使用 dataMode.js 中的 API 替换 '["type"]...'


### backend\app\services\ocr_service.py

- 行 570: 考虑将 '{
        "status": document.ocr_status or "unknow...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 355: 考虑将 '["name"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 547: 考虑将 '["name"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 570: 考虑使用 dataMode.js 中的 API 替换 '{
        "status": document.ocr_status or "unknow...'
- 行 355: 考虑使用 dataMode.js 中的 API 替换 '["name"]...'
- 行 547: 考虑使用 dataMode.js 中的 API 替换 '["name"]...'


### backend\app\services\token_manager.py

- 行 241: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 264: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 241: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'
- 行 264: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'


### backend\app\tools\alert.py

- 行 195: 考虑将 '['type']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 250: 考虑将 '[{
            "type": "system_error",
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 44: 考虑将 'size=5...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 55: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 195: 考虑使用 dataMode.js 中的 API 替换 '['type']...'
- 行 250: 考虑使用 dataMode.js 中的 API 替换 '[{
            "type": "system_error",
           ...'
- 行 44: 考虑使用 dataMode.js 中的 API 替换 'size=5...'
- 行 55: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'


### backend\app\tools\collect_stats.py

- 行 53: 考虑将 'size=5...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 64: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 53: 考虑使用 dataMode.js 中的 API 替换 'size=5...'
- 行 64: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'


### backend\app\tools\test_service_stats.py

- 行 47: 考虑将 '[column["name"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 51: 考虑将 '["id", "timestamp", "request_count", "active_users...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 67: 考虑将 'count=100...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 70: 考虑将 'count=5...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 98: 考虑将 'count = 200...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 47: 考虑使用 dataMode.js 中的 API 替换 '[column["name"]...'
- 行 51: 考虑使用 dataMode.js 中的 API 替换 '["id", "timestamp", "request_count", "active_users...'
- 行 67: 考虑使用 dataMode.js 中的 API 替换 'count=100...'
- 行 70: 考虑使用 dataMode.js 中的 API 替换 'count=5...'
- 行 98: 考虑使用 dataMode.js 中的 API 替换 'count = 200...'


### backend\app\utils\health_records_cache.py

- 行 39: 考虑将 '{
            'data': data,
            'expires_a...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 107: 考虑将 '{
                k: v for k, v in kwargs.items() ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 109: 考虑将 '['record_type', 'status', 'start_date', 'end_date'...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 39: 考虑使用 dataMode.js 中的 API 替换 '{
            'data': data,
            'expires_a...'
- 行 107: 考虑使用 dataMode.js 中的 API 替换 '{
                k: v for k, v in kwargs.items() ...'
- 行 109: 考虑使用 dataMode.js 中的 API 替换 '['record_type', 'status', 'start_date', 'end_date'...'
- 行 76: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\app\utils\performance_monitor.py

- 行 136: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 137: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 198: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\lib\psutil\__init__.py

- 行 23: 考虑将 'total = 8...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 32: 考虑将 'total = 100...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 23: 考虑使用 dataMode.js 中的 API 替换 'total = 8...'
- 行 32: 考虑使用 dataMode.js 中的 API 替换 'total = 100...'


### backend\lib\redis\__init__.py

- 行 33: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 33: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'


### backend\migrations\migrate_to_mysql.py

- 行 236: 考虑将 'size = 1000...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 236: 考虑使用 dataMode.js 中的 API 替换 'size = 1000...'
- 行 232: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\scripts\assessment_questionnaire_monitor.py

- 行 230: 考虑将 '{
                'stage': stage.value,
          ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 496: 考虑将 '{
                'assessment_total': assessment_b...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1192: 考虑将 '{issue['status'].upper()}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1204: 考虑将 '{
            'timestamp': datetime.now().isoforma...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1356: 考虑将 '{
                    'timestamp': datetime.now()....' 迁移到 MockDataManager.get_mock_*() 方法
- 行 402: 考虑将 '['type']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 403: 考虑将 '['type']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 557: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 581: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 619: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 636: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 954: 考虑将 '['id']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 958: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 959: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 992: 考虑将 '['id']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 996: 考虑将 '['name']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 997: 考虑将 '['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1192: 考虑将 '[{issue['status']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1208: 考虑将 '[
                {
                    'stage': r...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1253: 考虑将 '['id']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1272: 考虑将 '['id']...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1360: 考虑将 '[
                        {
                      ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 1235: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 230: 考虑使用 dataMode.js 中的 API 替换 '{
                'stage': stage.value,
          ...'
- 行 496: 考虑使用 dataMode.js 中的 API 替换 '{
                'assessment_total': assessment_b...'
- 行 1192: 考虑使用 dataMode.js 中的 API 替换 '{issue['status'].upper()}...'
- 行 1204: 考虑使用 dataMode.js 中的 API 替换 '{
            'timestamp': datetime.now().isoforma...'
- 行 1356: 考虑使用 dataMode.js 中的 API 替换 '{
                    'timestamp': datetime.now()....'
- 行 402: 考虑使用 dataMode.js 中的 API 替换 '['type']...'
- 行 403: 考虑使用 dataMode.js 中的 API 替换 '['type']...'
- 行 557: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 581: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 619: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 636: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 954: 考虑使用 dataMode.js 中的 API 替换 '['id']...'
- 行 958: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 959: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 992: 考虑使用 dataMode.js 中的 API 替换 '['id']...'
- 行 996: 考虑使用 dataMode.js 中的 API 替换 '['name']...'
- 行 997: 考虑使用 dataMode.js 中的 API 替换 '['status']...'
- 行 1192: 考虑使用 dataMode.js 中的 API 替换 '[{issue['status']...'
- 行 1208: 考虑使用 dataMode.js 中的 API 替换 '[
                {
                    'stage': r...'
- 行 1253: 考虑使用 dataMode.js 中的 API 替换 '['id']...'
- 行 1272: 考虑使用 dataMode.js 中的 API 替换 '['id']...'
- 行 1360: 考虑使用 dataMode.js 中的 API 替换 '[
                        {
                      ...'
- 行 1235: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'


### backend\scripts\assessment_questionnaire_workflow.py

- 行 104: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 121: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 322: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 342: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 364: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 386: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 408: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 100: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 118: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 318: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 338: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 360: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 382: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 405: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\scripts\fix_sqlite3_datetime_deprecation.py

- 行 102: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 101: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\scripts\init_demo_data.py

- 行 14: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 20: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 27: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 34: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 38: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 19: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 26: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 33: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 37: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\scripts\migrate_hardcoded_data.py

- 行 138: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 155: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 156: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\scripts\migrate_mock_data.py

- 行 190: 考虑将 '{
#         "data": "hardcoded_value",
#         "...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 203: 考虑将 '{
        "data": "default_value",
        "count"...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 190: 考虑使用 dataMode.js 中的 API 替换 '{
#         "data": "hardcoded_value",
#         "...'
- 行 203: 考虑使用 dataMode.js 中的 API 替换 '{
        "data": "default_value",
        "count"...'
- 行 136: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\scripts\validate_mock_config.py

- 行 323: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### backend\tests\test_api_endpoints.py

- 行 151: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 185: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 216: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 255: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 288: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 151: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'
- 行 185: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'
- 行 216: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'
- 行 255: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'
- 行 288: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'


### backend\tests\test_db_service.py

- 行 145: 考虑将 '["id", "name"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 154: 考虑将 '["id"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 155: 考虑将 '["name"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 156: 考虑将 '["id"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 157: 考虑将 '["name"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 145: 考虑使用 dataMode.js 中的 API 替换 '["id", "name"]...'
- 行 154: 考虑使用 dataMode.js 中的 API 替换 '["id"]...'
- 行 155: 考虑使用 dataMode.js 中的 API 替换 '["name"]...'
- 行 156: 考虑使用 dataMode.js 中的 API 替换 '["id"]...'
- 行 157: 考虑使用 dataMode.js 中的 API 替换 '["name"]...'


### backend\tests\test_db_service_new.py

- 行 160: 考虑将 '["id", "name"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 169: 考虑将 '["id"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 170: 考虑将 '["name"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 171: 考虑将 '["id"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 172: 考虑将 '["name"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 160: 考虑使用 dataMode.js 中的 API 替换 '["id", "name"]...'
- 行 169: 考虑使用 dataMode.js 中的 API 替换 '["id"]...'
- 行 170: 考虑使用 dataMode.js 中的 API 替换 '["name"]...'
- 行 171: 考虑使用 dataMode.js 中的 API 替换 '["id"]...'
- 行 172: 考虑使用 dataMode.js 中的 API 替换 '["name"]...'


### backend\tests\test_integration.py

- 行 98: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 98: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'


### backend\tests\test_metrics.py

- 行 39: 考虑将 '{
                "status": "running",
           ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 140: 考虑将 '{
                    "status": "running",
       ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 215: 考虑将 '{"status": "running"}...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 172: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 39: 考虑使用 dataMode.js 中的 API 替换 '{
                "status": "running",
           ...'
- 行 140: 考虑使用 dataMode.js 中的 API 替换 '{
                    "status": "running",
       ...'
- 行 215: 考虑使用 dataMode.js 中的 API 替换 '{"status": "running"}...'
- 行 172: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'


### backend\tests\test_security_features.py

- 行 406: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 416: 考虑将 '["status"]...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 406: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'
- 行 416: 考虑使用 dataMode.js 中的 API 替换 '["status"]...'


### frontend\fix-util-extend.js

- 行 13: 考虑将 'Count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 13: 考虑使用 dataMode.js 中的 API 替换 'Count = 0...'


### frontend\scripts\generate-docs.js

- 行 171: 考虑将 '{
    const methods = []
    const methodRegex = /...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 171: 考虑使用 dataMode.js 中的 API 替换 '{
    const methods = []
    const methodRegex = /...'


### frontend\src\api\metrics.js

- 行 266: 考虑使用 dataMode.js 中的 API 替换 'total: 16...'
- 行 270: 考虑使用 dataMode.js 中的 API 替换 'total: 500...'
- 行 280: 考虑使用 dataMode.js 中的 API 替换 'total: 15000...'
- 行 291: 考虑使用 dataMode.js 中的 API 替换 'size: 20...'
- 行 296: 考虑使用 dataMode.js 中的 API 替换 'total: 45000...'


### frontend\src\components\QuestionnaireGenerator.vue

- 行 167: 考虑使用 dataMode.js 中的 API 替换 'count: 10...'


### frontend\src\components\UserSearch.vue

- 行 163: 考虑将 'total = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 116: 考虑使用 dataMode.js 中的 API 替换 'Size: 10...'
- 行 117: 考虑使用 dataMode.js 中的 API 替换 'total: 0...'
- 行 163: 考虑使用 dataMode.js 中的 API 替换 'total = 0...'


### frontend\src\components\analysis\AssessmentAnalysis.vue

- 行 212: 考虑使用 dataMode.js 中的 API 替换 'length : 0...'
- 行 333: 考虑使用 dataMode.js 中的 API 替换 'count: 0...'
- 行 347: 考虑使用 dataMode.js 中的 API 替换 'count : 0...'
- 行 16: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### frontend\src\components\assessment\AssessmentEditor.vue

- 行 1073: 考虑使用 dataMode.js 中的 API 替换 'size: 16...'
- 行 1083: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 1097: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 1102: 考虑使用 dataMode.js 中的 API 替换 'size: 13...'
- 行 1126: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'


### frontend\src\components\assessment\AssessmentList.vue

- 行 244: 考虑使用 dataMode.js 中的 API 替换 'size: 13...'


### frontend\src\components\assessment\AssessmentPreview.vue

- 行 92: 考虑使用 dataMode.js 中的 API 替换 'size:12...'
- 行 178: 考虑使用 dataMode.js 中的 API 替换 'size: 16...'
- 行 188: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 209: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 219: 考虑使用 dataMode.js 中的 API 替换 'size: 13...'


### frontend\src\components\assessment\QuestionnaireGenerator.vue

- 行 167: 考虑使用 dataMode.js 中的 API 替换 'count: 10...'


### frontend\src\components\charts\BarChart.vue

- 行 124: 考虑将 '{
      seriesItem.stack = 'total';
    }...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 129: 考虑将 '{
      seriesItem.stack = 'total';
      seriesIt...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 124: 考虑使用 dataMode.js 中的 API 替换 '{
      seriesItem.stack = 'total';
    }...'
- 行 129: 考虑使用 dataMode.js 中的 API 替换 '{
      seriesItem.stack = 'total';
      seriesIt...'


### frontend\src\components\charts\LineChart.vue

- 行 150: 考虑将 '{
      seriesItem.stack = 'total';
      seriesIt...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 150: 考虑使用 dataMode.js 中的 API 替换 '{
      seriesItem.stack = 'total';
      seriesIt...'


### frontend\src\components\charts\MetricsChart.vue

- 行 153: 考虑使用 dataMode.js 中的 API 替换 'Size: 16...'
- 行 179: 考虑使用 dataMode.js 中的 API 替换 'Size: 11...'
- 行 616: 考虑使用 dataMode.js 中的 API 替换 'size: 16...'


### frontend\src\components\common\DataModeSwitch.vue

- 行 403: 考虑使用 dataMode.js 中的 API 替换 'size: 18...'
- 行 408: 考虑使用 dataMode.js 中的 API 替换 'size: 16...'
- 行 432: 考虑使用 dataMode.js 中的 API 替换 'size: 18...'
- 行 454: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 460: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 477: 考虑使用 dataMode.js 中的 API 替换 'size: 16...'
- 行 520: 考虑使用 dataMode.js 中的 API 替换 'size: 16...'
- 行 524: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 531: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 574: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 582: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 594: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 599: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 178: 考虑使用 dataMode.js 中的 API 替换 'data() {
    return {
      // 数据模式常量
      DATA_M...'
- 行 212: 考虑使用 dataMode.js 中的 API 替换 'computed: {
    currentModeLabel() {
      return ...'


### frontend\src\components\common\question-editor.vue

- 行 152: 考虑使用 dataMode.js 中的 API 替换 'length: 6...'


### frontend\src\components\dashboard\ActivityLog.vue

- 行 221: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 226: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'


### frontend\src\components\dashboard\AdminDashboard.vue

- 行 187: 考虑使用 dataMode.js 中的 API 替换 'Count: 0...'
- 行 188: 考虑使用 dataMode.js 中的 API 替换 'Count: 0...'
- 行 189: 考虑使用 dataMode.js 中的 API 替换 'Count: 0...'
- 行 190: 考虑使用 dataMode.js 中的 API 替换 'Count: 0...'
- 行 223: 考虑使用 dataMode.js 中的 API 替换 'length 
        : 0...'
- 行 227: 考虑使用 dataMode.js 中的 API 替换 'Count: 128...'
- 行 228: 考虑使用 dataMode.js 中的 API 替换 'Count: 5...'
- 行 230: 考虑使用 dataMode.js 中的 API 替换 'Count: 32...'
- 行 236: 考虑使用 dataMode.js 中的 API 替换 'Count: 128...'
- 行 237: 考虑使用 dataMode.js 中的 API 替换 'Count: 5...'
- 行 238: 考虑使用 dataMode.js 中的 API 替换 'Count: 0...'
- 行 239: 考虑使用 dataMode.js 中的 API 替换 'Count: 32...'
- 行 280: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 313: 考虑使用 dataMode.js 中的 API 替换 'size: 2...'
- 行 330: 考虑使用 dataMode.js 中的 API 替换 'size: 0...'
- 行 336: 考虑使用 dataMode.js 中的 API 替换 'size: 1...'
- 行 374: 考虑使用 dataMode.js 中的 API 替换 'size: 24...'


### frontend\src\components\dashboard\StatisticsPanel.vue

- 行 129: 考虑使用 dataMode.js 中的 API 替换 'size: 2...'
- 行 146: 考虑使用 dataMode.js 中的 API 替换 'size: 0...'
- 行 152: 考虑使用 dataMode.js 中的 API 替换 'size: 1...'
- 行 158: 考虑使用 dataMode.js 中的 API 替换 'size: 0...'


### frontend\src\components\dashboard\SystemStatus.vue

- 行 179: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 191: 考虑使用 dataMode.js 中的 API 替换 'size: 16...'


### frontend\src\components\dashboard\UserDashboard.vue

- 行 151: 考虑使用 dataMode.js 中的 API 替换 'Count: 0...'
- 行 152: 考虑使用 dataMode.js 中的 API 替换 'Count: 0...'
- 行 153: 考虑使用 dataMode.js 中的 API 替换 'Count: 0...'
- 行 154: 考虑使用 dataMode.js 中的 API 替换 'Count: 0...'
- 行 185: 考虑使用 dataMode.js 中的 API 替换 'Count: 24...'
- 行 186: 考虑使用 dataMode.js 中的 API 替换 'Count: 12...'
- 行 187: 考虑使用 dataMode.js 中的 API 替换 'Count: 8...'
- 行 188: 考虑使用 dataMode.js 中的 API 替换 'Count: 5...'
- 行 228: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 274: 考虑使用 dataMode.js 中的 API 替换 'size: 24...'
- 行 293: 考虑使用 dataMode.js 中的 API 替换 'size: 24...'
- 行 299: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'


### frontend\src\components\health-data\DashboardTab.vue

- 行 155: 考虑使用 dataMode.js 中的 API 替换 'Count: 0...'
- 行 617: 考虑使用 dataMode.js 中的 API 替换 'Count: 5...'
- 行 765: 考虑使用 dataMode.js 中的 API 替换 'size: 36...'
- 行 772: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'


### frontend\src\components\health-data\ExaminationReportsTab.vue

- 行 369: 考虑使用 dataMode.js 中的 API 替换 'size: 16...'
- 行 394: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 410: 考虑使用 dataMode.js 中的 API 替换 'size: 32...'


### frontend\src\components\health-data\HealthDiaryTab.vue

- 行 441: 考虑使用 dataMode.js 中的 API 替换 'size: 16...'
- 行 446: 考虑使用 dataMode.js 中的 API 替换 'size: 16...'
- 行 459: 考虑使用 dataMode.js 中的 API 替换 'size: 16...'


### frontend\src\components\health-data\LabReportsTab.vue

- 行 336: 考虑使用 dataMode.js 中的 API 替换 'size: 16...'


### frontend\src\components\health-data\OtherRecordsTab.vue

- 行 396: 考虑使用 dataMode.js 中的 API 替换 'size: 16...'


### frontend\src\components\health-data\QuestionnairesTab.vue

- 行 548: 考虑使用 dataMode.js 中的 API 替换 'count: 0...'
- 行 588: 考虑使用 dataMode.js 中的 API 替换 'count: 0...'
- 行 1224: 考虑使用 dataMode.js 中的 API 替换 'Size: 16...'
- 行 1242: 考虑使用 dataMode.js 中的 API 替换 'Size: 12...'
- 行 1338: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 1355: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 1368: 考虑使用 dataMode.js 中的 API 替换 'size: 13...'
- 行 1375: 考虑使用 dataMode.js 中的 API 替换 'size: 11...'
- 行 1380: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 1391: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 1396: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 1438: 考虑使用 dataMode.js 中的 API 替换 'size: 16...'
- 行 1524: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 1583: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 1601: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 1657: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 1669: 考虑使用 dataMode.js 中的 API 替换 'size: 18...'


### frontend\src\components\monitoring\ServiceStatsPanel.vue

- 行 272: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'


### frontend\src\components\security\PerformanceMonitor.vue

- 行 241: 考虑使用 dataMode.js 中的 API 替换 'total: 0...'
- 行 244: 考虑使用 dataMode.js 中的 API 替换 'total: 0...'
- 行 251: 考虑使用 dataMode.js 中的 API 替换 'total: 0...'
- 行 259: 考虑使用 dataMode.js 中的 API 替换 'size: 0...'
- 行 264: 考虑使用 dataMode.js 中的 API 替换 'total: 0...'
- 行 454: 考虑使用 dataMode.js 中的 API 替换 'size: 0...'


### frontend\src\components\security\TwoFactorAuth.vue

- 行 394: 考虑使用 dataMode.js 中的 API 替换 'size: 0...'


### frontend\src\mocks\mockDataManager.js

- 行 215: 考虑使用 dataMode.js 中的 API 替换 'count: 9...'
- 行 245: 考虑使用 dataMode.js 中的 API 替换 'count: 7...'


### frontend\src\services\aggregatedApi.js

- 行 318: 考虑将 '{
      const params = new URLSearchParams()
     ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 318: 考虑使用 dataMode.js 中的 API 替换 '{
      const params = new URLSearchParams()
     ...'
- 行 124: 考虑使用 dataMode.js 中的 API 替换 'size: 20...'


### frontend\src\utils\chartUtils.js

- 行 125: 考虑使用 dataMode.js 中的 API 替换 'count: 0...'
- 行 128: 考虑使用 dataMode.js 中的 API 替换 'count: 0...'
- 行 45: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件
- 行 73: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### frontend\src\utils\health-monitor.js

- 行 415: 考虑使用 dataMode.js 中的 API 替换 'length : 0...'
- 行 455: 考虑使用 dataMode.js 中的 API 替换 'length : 0...'


### frontend\src\utils\performance.js

- 行 338: 考虑使用 dataMode.js 中的 API 替换 'length : 0...'
- 行 339: 考虑使用 dataMode.js 中的 API 替换 'length : 0...'
- 行 345: 考虑使用 dataMode.js 中的 API 替换 'length : 0...'


### frontend\src\utils\system-manager.js

- 行 22: 考虑将 'Count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 89: 考虑将 'Count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 22: 考虑使用 dataMode.js 中的 API 替换 'Count = 0...'
- 行 89: 考虑使用 dataMode.js 中的 API 替换 'Count = 0...'


### frontend\src\utils\testUtils.js

- 行 493: 考虑将 'length = 10...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 560: 考虑将 'count = 10...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 577: 考虑将 'count = 4...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 592: 考虑将 'count = 10...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 493: 考虑使用 dataMode.js 中的 API 替换 'length = 10...'
- 行 560: 考虑使用 dataMode.js 中的 API 替换 'count = 10...'
- 行 577: 考虑使用 dataMode.js 中的 API 替换 'count = 4...'
- 行 592: 考虑使用 dataMode.js 中的 API 替换 'count = 10...'
- 行 475: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### frontend\src\views\AssessmentManagement.vue

- 行 303: 考虑使用 dataMode.js 中的 API 替换 'size: 100...'
- 行 345: 考虑使用 dataMode.js 中的 API 替换 'count: 0...'
- 行 363: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### frontend\src\views\Assessments.vue

- 行 556: 考虑将 '{
    await assessmentForm$.value.validate()

    ...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 615: 考虑将 '{
  if (newVal === 'records') {
    fetchAssessmen...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 556: 考虑使用 dataMode.js 中的 API 替换 '{
    await assessmentForm$.value.validate()

    ...'
- 行 615: 考虑使用 dataMode.js 中的 API 替换 '{
  if (newVal === 'records') {
    fetchAssessmen...'
- 行 304: 考虑使用 dataMode.js 中的 API 替换 'count: 20...'
- 行 353: 考虑使用 dataMode.js 中的 API 替换 'count: 20...'
- 行 365: 考虑使用 dataMode.js 中的 API 替换 'count: 30...'
- 行 755: 考虑使用 dataMode.js 中的 API 替换 'size: 11...'


### frontend\src\views\ConfigManagement.vue

- 行 924: 考虑使用 dataMode.js 中的 API 替换 'size: 24...'
- 行 931: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 957: 考虑使用 dataMode.js 中的 API 替换 'size: 24...'
- 行 975: 考虑使用 dataMode.js 中的 API 替换 'size: 24...'
- 行 983: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 1016: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 1041: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 1088: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'


### frontend\src\views\DataModeManagement.vue

- 行 421: 考虑使用 dataMode.js 中的 API 替换 'size: 24...'
- 行 431: 考虑使用 dataMode.js 中的 API 替换 'size: 28...'
- 行 436: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 469: 考虑使用 dataMode.js 中的 API 替换 'size: 16...'
- 行 478: 考虑使用 dataMode.js 中的 API 替换 'size: 18...'
- 行 505: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 514: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 527: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 545: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 579: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 585: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 640: 考虑使用 dataMode.js 中的 API 替换 'size: 20...'
- 行 644: 考虑使用 dataMode.js 中的 API 替换 'size: 24...'
- 行 234: 考虑使用 dataMode.js 中的 API 替换 'data() {
    return {
      refreshing: false,
   ...'


### frontend\src\views\DeploymentManagement.vue

- 行 1115: 考虑使用 dataMode.js 中的 API 替换 'size: 24...'
- 行 1122: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 1148: 考虑使用 dataMode.js 中的 API 替换 'size: 24...'
- 行 1166: 考虑使用 dataMode.js 中的 API 替换 'size: 24...'
- 行 1174: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 1208: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 1219: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 1263: 考虑使用 dataMode.js 中的 API 替换 'size: 16...'
- 行 1285: 考虑使用 dataMode.js 中的 API 替换 'size: 16...'
- 行 1291: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 1302: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 1338: 考虑使用 dataMode.js 中的 API 替换 'size: 16...'
- 行 1345: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 1368: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 1374: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 1405: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'


### frontend\src\views\Documents.vue

- 行 414: 考虑将 'total = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 302: 考虑使用 dataMode.js 中的 API 替换 'Size: 10...'
- 行 303: 考虑使用 dataMode.js 中的 API 替换 'total: 0...'
- 行 414: 考虑使用 dataMode.js 中的 API 替换 'total = 0...'


### frontend\src\views\ExaminationReports.vue

- 行 277: 考虑使用 dataMode.js 中的 API 替换 'Size: 10...'
- 行 278: 考虑使用 dataMode.js 中的 API 替换 'total: 0...'


### frontend\src\views\FollowUp.vue

- 行 204: 考虑使用 dataMode.js 中的 API 替换 'Size: 10...'
- 行 205: 考虑使用 dataMode.js 中的 API 替换 'total: 0...'


### frontend\src\views\HealthDataOverview.vue

- 行 499: 考虑使用 dataMode.js 中的 API 替换 'size: 18...'
- 行 531: 考虑使用 dataMode.js 中的 API 替换 'size: 16...'
- 行 552: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'


### frontend\src\views\HealthDiary.vue

- 行 233: 考虑使用 dataMode.js 中的 API 替换 'Size: 10...'
- 行 234: 考虑使用 dataMode.js 中的 API 替换 'total: 0...'


### frontend\src\views\HealthRecords.vue

- 行 165: 考虑使用 dataMode.js 中的 API 替换 'Size: 10...'
- 行 166: 考虑使用 dataMode.js 中的 API 替换 'total: 0...'


### frontend\src\views\ImagingRecords.vue

- 行 771: 考虑使用 dataMode.js 中的 API 替换 'size: 48...'


### frontend\src\views\LabReports.vue

- 行 524: 考虑将 'total = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 94: 考虑使用 dataMode.js 中的 API 替换 'length : 0...'
- 行 383: 考虑使用 dataMode.js 中的 API 替换 'Size: 10...'
- 行 384: 考虑使用 dataMode.js 中的 API 替换 'total: 0...'
- 行 524: 考虑使用 dataMode.js 中的 API 替换 'total = 0...'


### frontend\src\views\Layout.vue

- 行 267: 考虑使用 dataMode.js 中的 API 替换 'size: 18...'
- 行 289: 考虑使用 dataMode.js 中的 API 替换 'size: 20...'
- 行 293: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 314: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'


### frontend\src\views\Login.vue

- 行 425: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 435: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'


### frontend\src\views\MedicalRecords.vue

- 行 219: 考虑使用 dataMode.js 中的 API 替换 'Size: 10...'
- 行 220: 考虑使用 dataMode.js 中的 API 替换 'total: 0...'


### frontend\src\views\MonitoringAlerts.vue

- 行 610: 考虑使用 dataMode.js 中的 API 替换 'count: 15...'
- 行 622: 考虑使用 dataMode.js 中的 API 替换 'count: 8...'
- 行 634: 考虑使用 dataMode.js 中的 API 替换 'count: 1...'
- 行 646: 考虑使用 dataMode.js 中的 API 替换 'count: 3...'
- 行 1224: 考虑使用 dataMode.js 中的 API 替换 'size: 24...'
- 行 1231: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 1257: 考虑使用 dataMode.js 中的 API 替换 'size: 24...'
- 行 1278: 考虑使用 dataMode.js 中的 API 替换 'size: 24...'
- 行 1286: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 1313: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 1319: 考虑使用 dataMode.js 中的 API 替换 'size: 16...'
- 行 1365: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 1374: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 1412: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 1435: 考虑使用 dataMode.js 中的 API 替换 'size: 16...'
- 行 1444: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 1472: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 1478: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 1492: 考虑使用 dataMode.js 中的 API 替换 'size: 16...'


### frontend\src\views\NotFound.vue

- 行 41: 考虑使用 dataMode.js 中的 API 替换 'size: 120...'
- 行 48: 考虑使用 dataMode.js 中的 API 替换 'size: 24...'
- 行 54: 考虑使用 dataMode.js 中的 API 替换 'size: 16...'


### frontend\src\views\OperationLogs.vue

- 行 166: 考虑使用 dataMode.js 中的 API 替换 'Size: 10...'
- 行 167: 考虑使用 dataMode.js 中的 API 替换 'total: 0...'


### frontend\src\views\PrescriptionRecords.vue

- 行 438: 考虑将 'count = 0...' 迁移到 MockDataManager.get_mock_*() 方法
- 行 240: 考虑使用 dataMode.js 中的 API 替换 'count: 0...'
- 行 312: 考虑使用 dataMode.js 中的 API 替换 'count: 3...'
- 行 326: 考虑使用 dataMode.js 中的 API 替换 'count: 0...'
- 行 349: 考虑使用 dataMode.js 中的 API 替换 'count: 3...'
- 行 363: 考虑使用 dataMode.js 中的 API 替换 'count: 0...'
- 行 438: 考虑使用 dataMode.js 中的 API 替换 'count = 0...'


### frontend\src\views\Profile.vue

- 行 400: 考虑使用 dataMode.js 中的 API 替换 'size: 28...'


### frontend\src\views\ProjectManagement.vue

- 行 620: 考虑使用 dataMode.js 中的 API 替换 'size: 24...'
- 行 627: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 667: 考虑使用 dataMode.js 中的 API 替换 'size: 18...'
- 行 693: 考虑使用 dataMode.js 中的 API 替换 'size: 16...'
- 行 699: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 732: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 757: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 763: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 792: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 879: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'


### frontend\src\views\QuestionnaireManagement.vue

- 行 282: 考虑使用 dataMode.js 中的 API 替换 'size: 100...'
- 行 325: 考虑使用 dataMode.js 中的 API 替换 'count: 0...'
- 行 341: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件


### frontend\src\views\ServiceManagement.vue

- 行 855: 考虑使用 dataMode.js 中的 API 替换 'size: 24...'
- 行 862: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 888: 考虑使用 dataMode.js 中的 API 替换 'size: 24...'
- 行 909: 考虑使用 dataMode.js 中的 API 替换 'size: 24...'
- 行 917: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 949: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 974: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 1000: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 1018: 考虑使用 dataMode.js 中的 API 替换 'size: 16...'
- 行 1068: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 1072: 考虑使用 dataMode.js 中的 API 替换 'size: 24...'
- 行 1098: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'


### frontend\src\views\SystemManagement.vue

- 行 577: 考虑使用 dataMode.js 中的 API 替换 'size: 24...'
- 行 584: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 614: 考虑使用 dataMode.js 中的 API 替换 'size: 20...'
- 行 635: 考虑使用 dataMode.js 中的 API 替换 'size: 16...'
- 行 641: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 647: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 712: 考虑使用 dataMode.js 中的 API 替换 'size: 16...'
- 行 718: 考虑使用 dataMode.js 中的 API 替换 'size: 13...'
- 行 732: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 770: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'


### frontend\src\views\SystemSettings.vue

- 行 285: 考虑使用 dataMode.js 中的 API 替换 'Length: 8...'
- 行 512: 考虑使用 dataMode.js 中的 API 替换 'size: 24...'
- 行 523: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 535: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 573: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'


### frontend\src\views\TestManagement.vue

- 行 1054: 考虑使用 dataMode.js 中的 API 替换 'size: 24...'
- 行 1061: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 1087: 考虑使用 dataMode.js 中的 API 替换 'size: 24...'
- 行 1105: 考虑使用 dataMode.js 中的 API 替换 'size: 24...'
- 行 1113: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 1156: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 1181: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 1198: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 1203: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 1213: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 1251: 考虑使用 dataMode.js 中的 API 替换 'size: 48...'
- 行 1270: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 1276: 考虑使用 dataMode.js 中的 API 替换 'size: 16...'
- 行 1299: 考虑使用 dataMode.js 中的 API 替换 'size: 12...'
- 行 1316: 考虑使用 dataMode.js 中的 API 替换 'size: 20...'
- 行 1322: 考虑使用 dataMode.js 中的 API 替换 'size: 14...'
- 行 1332: 考虑使用 dataMode.js 中的 API 替换 'size: 16...'


## 迁移建议

1. **后端 Python 文件**: 使用 `MockDataManager` 类的相应方法
2. **前端 JavaScript/Vue 文件**: 使用 `dataMode.js` 中的 API
3. **SQL 文件**: 考虑迁移到配置文件或模拟数据管理器
4. **测试文件**: 可以保留硬编码数据，但建议使用工厂模式