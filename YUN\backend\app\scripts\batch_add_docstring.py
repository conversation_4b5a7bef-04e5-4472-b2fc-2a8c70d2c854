import os
import re

API_DIR = os.path.join(os.path.dirname(__file__), '../api')

def add_docstring_to_route_functions():
    for fname in os.listdir(API_DIR):
        if not fname.endswith('.py'):
            continue
        fpath = os.path.join(API_DIR, fname)
        with open(fpath, 'r', encoding='utf-8') as f:
            code = f.read()
        # 匹配无docstring的路由函数
        def replacer(match):
            funcdef = match.group(0)
            if '"""' in code[match.end():match.end()+100]:
                return funcdef  # 已有docstring
            # 自动补全标准docstring
            doc = '    """\n    TODO: 请补充接口功能描述、参数、返回值、错误码等\n    """\n'
            return funcdef + '\n' + doc
        # 匹配def ...():
        code_new = re.sub(r'^(def [\w_]+\(.*\):)', replacer, code, flags=re.MULTILINE)
        with open(fpath, 'w', encoding='utf-8') as f:
            f.write(code_new)
    print('API 路由函数批量补全 docstring 完成，请人工完善具体内容！')

if __name__ == '__main__':
    add_docstring_to_route_functions() 