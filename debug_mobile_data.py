#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import logging
import json

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_mobile_api_data():
    """详细测试移动端API返回的数据"""
    base_url = "http://localhost:8000/api/mobile"
    headers = {
        "X-User-ID": "SM_006",
        "Content-Type": "application/json"
    }
    
    logger.info("=== 开始详细测试移动端API数据 ===")
    
    # 1. 测试量表API
    logger.info("\n=== 测试量表API ===")
    try:
        response = requests.get(f"{base_url}/assessments", headers=headers)
        logger.info(f"量表API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            logger.info(f"量表API原始响应结构: {type(result)}")
            logger.info(f"量表API响应键: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
            
            # 解析数据
            if isinstance(result, dict):
                data = result.get('data', {})
                if isinstance(data, dict):
                    assessments = data.get('assessments', [])
                else:
                    assessments = data if isinstance(data, list) else []
            else:
                assessments = result if isinstance(result, list) else []
            
            logger.info(f"解析到的量表总数: {len(assessments)}")
            
            # 统计状态
            pending_count = sum(1 for a in assessments if a.get('status') == 'pending')
            completed_count = sum(1 for a in assessments if a.get('status') == 'completed')
            other_count = len(assessments) - pending_count - completed_count
            
            logger.info(f"量表状态统计:")
            logger.info(f"  - Pending: {pending_count}")
            logger.info(f"  - Completed: {completed_count}")
            logger.info(f"  - Other: {other_count}")
            
            # 显示前5条数据的详细信息
            logger.info(f"\n前5条量表数据详情:")
            for i, assessment in enumerate(assessments[:5]):
                logger.info(f"  量表{i+1}: ID={assessment.get('id')}, 标题={assessment.get('title', 'N/A')}, 状态={assessment.get('status', 'N/A')}, 创建时间={assessment.get('created_at', 'N/A')}")
                
        else:
            logger.error(f"量表API请求失败: {response.status_code} - {response.text}")
            
    except Exception as e:
        logger.error(f"量表API请求异常: {e}")
    
    # 2. 测试问卷API
    logger.info("\n=== 测试问卷API ===")
    try:
        response = requests.get(f"{base_url}/questionnaires", headers=headers)
        logger.info(f"问卷API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            logger.info(f"问卷API原始响应结构: {type(result)}")
            logger.info(f"问卷API响应键: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
            
            # 解析数据
            if isinstance(result, dict):
                data = result.get('data', {})
                if isinstance(data, dict):
                    questionnaires = data.get('questionnaires', [])
                else:
                    questionnaires = data if isinstance(data, list) else []
            else:
                questionnaires = result if isinstance(result, list) else []
            
            logger.info(f"解析到的问卷总数: {len(questionnaires)}")
            
            # 统计状态
            pending_count = sum(1 for q in questionnaires if q.get('status') == 'pending')
            completed_count = sum(1 for q in questionnaires if q.get('status') == 'completed')
            other_count = len(questionnaires) - pending_count - completed_count
            
            logger.info(f"问卷状态统计:")
            logger.info(f"  - Pending: {pending_count}")
            logger.info(f"  - Completed: {completed_count}")
            logger.info(f"  - Other: {other_count}")
            
            # 显示前5条数据的详细信息
            logger.info(f"\n前5条问卷数据详情:")
            for i, questionnaire in enumerate(questionnaires[:5]):
                logger.info(f"  问卷{i+1}: ID={questionnaire.get('id')}, 标题={questionnaire.get('title', 'N/A')}, 状态={questionnaire.get('status', 'N/A')}, 创建时间={questionnaire.get('created_at', 'N/A')}")
                
        else:
            logger.error(f"问卷API请求失败: {response.status_code} - {response.text}")
            
    except Exception as e:
        logger.error(f"问卷API请求异常: {e}")
    
    # 3. 模拟移动端过滤逻辑
    logger.info("\n=== 模拟移动端过滤逻辑 ===")
    logger.info("评估量表tab (pending状态): 应该显示pending状态的量表")
    logger.info("问卷tab (pending状态): 应该显示pending状态的问卷")
    logger.info("历史记录tab (completed状态): 应该显示completed状态的量表和问卷")
    
    logger.info("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_mobile_api_data()