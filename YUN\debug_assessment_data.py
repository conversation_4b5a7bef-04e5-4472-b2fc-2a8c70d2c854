# -*- coding: utf-8 -*-
"""
调试量表数据
检查SM_008用户的量表分发和回答数据
"""

import sqlite3
import json
from datetime import datetime

def debug_assessment_data():
    """调试量表数据"""
    print("=== 调试量表数据 ===")
    
    # 连接数据库
    db_path = "backend/app.db"
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 1. 检查SM_008用户信息
        print("\n1. 检查SM_008用户信息...")
        cursor.execute("""
            SELECT id, username, custom_id, role 
            FROM users 
            WHERE custom_id = 'SM_008'
        """)
        user_info = cursor.fetchone()
        if user_info:
            print(f"用户信息: ID={user_info[0]}, 用户名={user_info[1]}, 自定义ID={user_info[2]}, 角色={user_info[3]}")
        else:
            print("未找到SM_008用户")
            return
        
        # 2. 检查量表分发数据
        print("\n2. 检查量表分发数据...")
        cursor.execute("""
            SELECT ad.id, ad.assessment_id, ad.custom_id, ad.status, ad.created_at, ad.due_date,
                   a.name, a.notes
            FROM assessment_distributions ad
            JOIN assessments a ON ad.assessment_id = a.id
            WHERE ad.custom_id = 'SM_008'
            ORDER BY ad.created_at DESC
        """)
        distributions = cursor.fetchall()
        print(f"找到 {len(distributions)} 条量表分发记录")
        
        for i, dist in enumerate(distributions):
            print(f"  分发 {i+1}:")
            print(f"    ID: {dist[0]}")
            print(f"    量表ID: {dist[1]}")
            print(f"    用户ID: {dist[2]}")
            print(f"    状态: {dist[3]}")
            print(f"    创建时间: {dist[4]}")
            print(f"    截止时间: {dist[5]}")
            print(f"    量表名称: {dist[6]}")
            print(f"    量表描述: {dist[7]}")
        
        # 3. 检查量表回答数据
        print("\n3. 检查量表回答数据...")
        cursor.execute("""
            SELECT ar.id, ar.assessment_id, ar.custom_id, ar.status, ar.created_at, ar.updated_at,
                   ar.score, a.name, a.notes
            FROM assessment_responses ar
            JOIN assessments a ON ar.assessment_id = a.id
            WHERE ar.custom_id = 'SM_008'
            ORDER BY ar.created_at DESC
        """)
        responses = cursor.fetchall()
        print(f"找到 {len(responses)} 条量表回答记录")
        
        for i, resp in enumerate(responses):
            print(f"  回答 {i+1}:")
            print(f"    ID: {resp[0]}")
            print(f"    量表ID: {resp[1]}")
            print(f"    用户ID: {resp[2]}")
            print(f"    状态: {resp[3]}")
            print(f"    创建时间: {resp[4]}")
            print(f"    更新时间: {resp[5]}")
            print(f"    分数: {resp[6]}")
            print(f"    量表名称: {resp[7]}")
            print(f"    量表描述: {resp[8]}")
        
        # 4. 检查量表结果数据
        print("\n4. 检查量表结果数据...")
        cursor.execute("""
            SELECT ar.id, ar.assessment_id, ar.custom_id, ar.result_level, ar.interpretation,
                   ar.report_content, ar.created_at
            FROM assessment_results ar
            WHERE ar.custom_id = 'SM_008'
            ORDER BY ar.created_at DESC
        """)
        results = cursor.fetchall()
        print(f"找到 {len(results)} 条量表结果记录")
        
        for i, result in enumerate(results):
            print(f"  结果 {i+1}:")
            print(f"    ID: {result[0]}")
            print(f"    量表ID: {result[1]}")
            print(f"    用户ID: {result[2]}")
            print(f"    结果等级: {result[3]}")
            print(f"    解释: {result[4][:100] if result[4] else None}...")
            print(f"    报告内容: {result[5][:100] if result[5] else None}...")
            print(f"    创建时间: {result[6]}")
        
        # 5. 检查所有量表模板
        print("\n5. 检查所有量表模板...")
        cursor.execute("""
            SELECT id, name, notes
            FROM assessments
            ORDER BY id
        """)
        assessments = cursor.fetchall()
        print(f"找到 {len(assessments)} 个量表模板")
        
        for i, assessment in enumerate(assessments):
            print(f"  量表 {i+1}:")
            print(f"    ID: {assessment[0]}")
            print(f"    名称: {assessment[1]}")
            print(f"    描述: {assessment[2][:100] if assessment[2] else None}...")
            print(f"    分类: {assessment[3]}")
            print(f"    目标: {assessment[4]}")
        
        # 6. 测试API函数的SQL查询
        print("\n6. 测试API函数的SQL查询...")
        
        # 测试待完成的量表查询
        print("\n6.1 测试待完成的量表查询...")
        cursor.execute("""
            SELECT ad.id, ad.assessment_id, ad.custom_id, ad.status, ad.created_at, ad.due_date,
                   a.id, a.name, a.notes
            FROM assessment_distributions ad
            JOIN assessments a ON ad.assessment_id = a.id
            WHERE ad.custom_id = 'SM_008' AND ad.status = 'pending'
            ORDER BY ad.created_at DESC
        """)
        pending_assessments = cursor.fetchall()
        print(f"待完成量表查询结果: {len(pending_assessments)} 条记录")
        
        for i, item in enumerate(pending_assessments):
            print(f"  待完成量表 {i+1}:")
            print(f"    分发ID: {item[0]}")
            print(f"    量表ID: {item[1]}")
            print(f"    用户ID: {item[2]}")
            print(f"    状态: {item[3]}")
            print(f"    创建时间: {item[4]}")
            print(f"    截止时间: {item[5]}")
            print(f"    量表名称: {item[7]}")
        
        # 测试已完成的量表查询
        print("\n6.2 测试已完成的量表查询...")
        cursor.execute("""
            SELECT ar.id, ar.assessment_id, ar.custom_id, ar.created_at, ar.updated_at, ar.score,
                   a.id, a.name, a.notes
            FROM assessment_responses ar
            JOIN assessments a ON ar.assessment_id = a.id
            WHERE ar.custom_id = 'SM_008'
            ORDER BY ar.created_at DESC
        """)
        completed_assessments = cursor.fetchall()
        print(f"已完成量表查询结果: {len(completed_assessments)} 条记录")
        
        for i, item in enumerate(completed_assessments):
            print(f"  已完成量表 {i+1}:")
            print(f"    回答ID: {item[0]}")
            print(f"    量表ID: {item[1]}")
            print(f"    用户ID: {item[2]}")
            print(f"    创建时间: {item[3]}")
            print(f"    更新时间: {item[4]}")
            print(f"    分数: {item[5]}")
            print(f"    量表名称: {item[7]}")
        
    except Exception as e:
        print(f"数据库查询错误: {e}")
    finally:
        conn.close()
    
    print("\n=== 调试完成 ===")

if __name__ == "__main__":
    debug_assessment_data()