$headers = @{
    'Authorization' = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************.Uy8Ej8Uy8Ej8Uy8Ej8Uy8Ej8Uy8Ej8Uy8Ej8Uy8Ej8'
    'Content-Type' = 'application/json'
}

try {
    $response = Invoke-WebRequest -Uri 'http://localhost:8006/api/user-health-records/SM_008' -Method GET -Headers $headers
    Write-Host "Status: $($response.StatusCode)"
    $response.Content | Out-File -FilePath 'api_response.json' -Encoding UTF8
    Write-Host "Response saved to api_response.json"
} catch {
    Write-Host "Error: $($_.Exception.Message)"
    Write-Host "Response: $($_.Exception.Response)"
}