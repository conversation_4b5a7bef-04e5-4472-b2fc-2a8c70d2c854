import asyncio
from playwright.async_api import async_playwright
import json

async def test_frontend_debug():
    """测试前端调试功能"""
    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(headless=False, slow_mo=1000)
        context = await browser.new_context()
        page = await context.new_page()
        
        # 监听控制台输出
        console_logs = []
        
        def handle_console(msg):
            console_logs.append({
                'type': msg.type,
                'text': msg.text,
                'timestamp': msg.location
            })
            print(f"[{msg.type.upper()}] {msg.text}")
        
        page.on('console', handle_console)
        
        # 监听网络请求
        api_requests = []
        
        def handle_request(request):
            if '/api/' in request.url:
                api_requests.append({
                    'url': request.url,
                    'method': request.method,
                    'headers': dict(request.headers)
                })
                print(f"[API请求] {request.method} {request.url}")
        
        def handle_response(response):
            if '/api/' in response.url:
                print(f"[API响应] {response.status} {response.url}")
        
        page.on('request', handle_request)
        page.on('response', handle_response)
        
        try:
            print("正在访问前端页面...")
            await page.goto('http://localhost:8006', timeout=30000)
            
            # 等待页面加载
            await page.wait_for_timeout(3000)
            
            # 检查是否需要登录
            if 'login' in page.url.lower():
                print("需要登录，尝试登录...")
                
                # 填写登录信息
                await page.fill('input[type="text"], input[name="username"]', 'admin')
                await page.fill('input[type="password"], input[name="password"]', 'admin123')
                
                # 点击登录按钮
                await page.click('button[type="submit"], .login-btn, .el-button--primary')
                
                # 等待登录完成
                await page.wait_for_timeout(3000)
            
            # 导航到健康数据页面
            print("导航到健康数据页面...")
            
            # 尝试多种方式找到健康数据菜单
            health_data_selectors = [
                'text=健康数据',
                '[data-testid="health-data"]',
                '.el-menu-item:has-text("健康数据")',
                'a[href*="health"]',
                '.sidebar-item:has-text("健康数据")'
            ]
            
            health_data_found = False
            for selector in health_data_selectors:
                try:
                    await page.click(selector, timeout=2000)
                    health_data_found = True
                    print(f"成功点击健康数据菜单: {selector}")
                    break
                except:
                    continue
            
            if not health_data_found:
                print("未找到健康数据菜单，尝试直接访问URL...")
                await page.goto('http://localhost:3000/health-data', timeout=10000)
            
            await page.wait_for_timeout(2000)
            
            # 查找用户SM_008
            print("查找用户SM_008...")
            
            # 尝试在搜索框中输入SM_008
            search_selectors = [
                'input[placeholder*="搜索"]',
                'input[placeholder*="用户"]',
                '.el-input__inner',
                'input[type="text"]'
            ]
            
            for selector in search_selectors:
                try:
                    await page.fill(selector, 'SM_008')
                    await page.press(selector, 'Enter')
                    print(f"在搜索框中输入SM_008: {selector}")
                    break
                except:
                    continue
            
            await page.wait_for_timeout(2000)
            
            # 查找并点击"查看原始回答"按钮
            print("查找查看原始回答按钮...")
            
            view_answer_selectors = [
                'text=查看原始回答',
                'button:has-text("查看原始回答")',
                '.el-button:has-text("查看原始回答")',
                '[data-testid="view-answers"]'
            ]
            
            button_found = False
            for selector in view_answer_selectors:
                try:
                    # 等待按钮出现
                    await page.wait_for_selector(selector, timeout=5000)
                    
                    # 滚动到按钮位置
                    await page.locator(selector).first.scroll_into_view_if_needed()
                    
                    # 点击按钮
                    await page.locator(selector).first.click()
                    
                    print(f"成功点击查看原始回答按钮: {selector}")
                    button_found = True
                    break
                except Exception as e:
                    print(f"尝试点击 {selector} 失败: {e}")
                    continue
            
            if not button_found:
                print("未找到查看原始回答按钮，列出页面上的所有按钮...")
                buttons = await page.locator('button').all()
                for i, button in enumerate(buttons[:10]):  # 只显示前10个按钮
                    try:
                        text = await button.text_content()
                        print(f"按钮 {i+1}: {text}")
                    except:
                        pass
            
            # 等待一段时间让调试信息输出
            await page.wait_for_timeout(5000)
            
            print("\n=== 控制台日志分析 ===")
            template_logs = [log for log in console_logs if '模板' in log['text'] or 'template' in log['text'].lower()]
            if template_logs:
                print("找到模板相关日志:")
                for log in template_logs:
                    print(f"  [{log['type']}] {log['text']}")
            else:
                print("未找到模板相关的控制台日志")
            
            print("\n=== API请求分析 ===")
            template_requests = [req for req in api_requests if 'template' in req['url']]
            if template_requests:
                print("找到模板API请求:")
                for req in template_requests:
                    print(f"  {req['method']} {req['url']}")
            else:
                print("未找到模板API请求")
            
            # 保存测试结果
            test_result = {
                'console_logs': console_logs,
                'api_requests': api_requests,
                'template_logs': template_logs,
                'template_requests': template_requests,
                'button_found': button_found
            }
            
            with open('frontend_debug_test_result.json', 'w', encoding='utf-8') as f:
                json.dump(test_result, f, ensure_ascii=False, indent=2)
            
            print("\n测试结果已保存到 frontend_debug_test_result.json")
            
        except Exception as e:
            print(f"测试过程中出现错误: {e}")
        
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(test_frontend_debug())