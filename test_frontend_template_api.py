#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用Playwright测试前端获取评估模板详情的功能
重点测试用户SM_008的简易精神状态检查量表数据显示
"""

import asyncio
from playwright.async_api import async_playwright
import json
from datetime import datetime

async def test_frontend_template_api():
    """测试前端获取模板详情API"""
    print("=== 使用Playwright测试前端模板API ===")
    print(f"测试时间: {datetime.now()}")
    
    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(headless=False, slow_mo=1000)
        context = await browser.new_context()
        page = await context.new_page()
        
        # 监听网络请求
        api_requests = []
        api_responses = []
        
        async def handle_request(request):
            if '/api/' in request.url:
                api_requests.append({
                    'url': request.url,
                    'method': request.method,
                    'headers': dict(request.headers),
                    'timestamp': datetime.now().isoformat()
                })
                print(f"API请求: {request.method} {request.url}")
        
        async def handle_response(response):
            if '/api/' in response.url:
                try:
                    # 尝试获取响应内容
                    if response.status == 200:
                        content_type = response.headers.get('content-type', '')
                        if 'application/json' in content_type:
                            response_data = await response.json()
                            api_responses.append({
                                'url': response.url,
                                'status': response.status,
                                'data': response_data,
                                'timestamp': datetime.now().isoformat()
                            })
                            
                            # 特别关注模板API响应
                            if 'assessment-templates' in response.url:
                                print(f"\n=== 模板API响应 ===")
                                print(f"URL: {response.url}")
                                print(f"状态: {response.status}")
                                
                                if 'data' in response_data:
                                    template_data = response_data['data']
                                    if isinstance(template_data, dict):
                                        print(f"模板名称: {template_data.get('name', '未知')}")
                                        print(f"模板类型: {template_data.get('assessment_type', '未知')}")
                                        
                                        if 'questions' in template_data:
                                            questions = template_data['questions']
                                            print(f"问题数量: {len(questions)}")
                                            
                                            if questions:
                                                first_q = questions[0]
                                                print(f"第一个问题字段: {list(first_q.keys())}")
                                                print(f"第一个问题文本: {first_q.get('question_text', '无')}")
                                                print(f"第一个问题选项: {first_q.get('options', [])}")
                                        else:
                                            print("响应中不包含questions字段")
                                    elif isinstance(template_data, list):
                                        print(f"模板列表数量: {len(template_data)}")
                                        if template_data:
                                            first_template = template_data[0]
                                            print(f"第一个模板: {first_template.get('name', '未知')}")
                                print("=== 模板API响应结束 ===")
                        else:
                            api_responses.append({
                                'url': response.url,
                                'status': response.status,
                                'content_type': content_type,
                                'timestamp': datetime.now().isoformat()
                            })
                    else:
                        print(f"API响应错误: {response.status} {response.url}")
                        
                except Exception as e:
                    print(f"处理响应失败: {e}")
        
        page.on('request', handle_request)
        page.on('response', handle_response)
        
        try:
            # 1. 访问前端登录页面
            print("\n1. 访问前端登录页面...")
            await page.goto('http://localhost:3000/login')
            await page.wait_for_load_state('networkidle')
            
            # 2. 登录
            print("\n2. 执行登录...")
            await page.fill('input[type="text"]', 'admin')
            await page.fill('input[type="password"]', 'admin123')
            await page.click('button[type="submit"]')
            
            # 等待登录完成
            await page.wait_for_timeout(3000)
            
            # 3. 导航到健康数据页面
            print("\n3. 导航到健康数据页面...")
            await page.goto('http://localhost:3000/health-data')
            await page.wait_for_load_state('networkidle')
            
            # 4. 查找用户SM_008
            print("\n4. 查找用户SM_008...")
            await page.wait_for_timeout(2000)
            
            # 尝试在用户列表中找到SM_008
            sm008_found = False
            try:
                # 查找包含SM_008的元素
                sm008_elements = await page.query_selector_all('text=SM_008')
                if sm008_elements:
                    print(f"找到 {len(sm008_elements)} 个SM_008相关元素")
                    # 点击第一个SM_008元素
                    await sm008_elements[0].click()
                    sm008_found = True
                    await page.wait_for_timeout(2000)
                else:
                    print("页面中未找到SM_008用户")
            except Exception as e:
                print(f"查找SM_008失败: {e}")
            
            if sm008_found:
                # 5. 查看问卷和评估数据
                print("\n5. 查看问卷和评估数据...")
                await page.wait_for_timeout(2000)
                
                # 查找"查看原始回答"按钮
                try:
                    view_buttons = await page.query_selector_all('text=查看原始回答')
                    if view_buttons:
                        print(f"找到 {len(view_buttons)} 个查看原始回答按钮")
                        # 点击第一个按钮
                        await view_buttons[0].click()
                        await page.wait_for_timeout(3000)
                        
                        # 检查是否有模板API调用
                        print("\n6. 检查模板API调用...")
                        template_api_calls = [req for req in api_requests if 'assessment-templates' in req['url']]
                        print(f"模板API调用次数: {len(template_api_calls)}")
                        
                        for call in template_api_calls:
                            print(f"  - {call['method']} {call['url']}")
                        
                    else:
                        print("未找到查看原始回答按钮")
                        
                except Exception as e:
                    print(f"查看原始回答失败: {e}")
            
            # 6. 等待所有API调用完成
            await page.wait_for_timeout(5000)
            
            # 7. 输出API调用总结
            print("\n=== API调用总结 ===")
            print(f"总请求数: {len(api_requests)}")
            print(f"总响应数: {len(api_responses)}")
            
            # 分析模板相关的API调用
            template_requests = [req for req in api_requests if 'templates' in req['url']]
            template_responses = [resp for resp in api_responses if 'templates' in resp['url']]
            
            print(f"\n模板相关请求: {len(template_requests)}")
            for req in template_requests:
                print(f"  - {req['method']} {req['url']}")
            
            print(f"\n模板相关响应: {len(template_responses)}")
            for resp in template_responses:
                print(f"  - {resp['status']} {resp['url']}")
                if 'data' in resp and isinstance(resp['data'], dict):
                    data = resp['data'].get('data', {})
                    if isinstance(data, dict) and 'questions' in data:
                        print(f"    包含 {len(data['questions'])} 个问题")
            
        except Exception as e:
            print(f"测试过程中发生错误: {e}")
        
        finally:
            # 保存测试结果
            test_result = {
                'timestamp': datetime.now().isoformat(),
                'api_requests': api_requests,
                'api_responses': api_responses,
                'summary': {
                    'total_requests': len(api_requests),
                    'total_responses': len(api_responses),
                    'template_requests': len([req for req in api_requests if 'templates' in req['url']]),
                    'template_responses': len([resp for resp in api_responses if 'templates' in resp['url']])
                }
            }
            
            with open('frontend_template_api_test_result.json', 'w', encoding='utf-8') as f:
                json.dump(test_result, f, ensure_ascii=False, indent=2)
            
            print(f"\n测试结果已保存到: frontend_template_api_test_result.json")
            
            await browser.close()
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    asyncio.run(test_frontend_template_api())