import { createRouter, createWebHistory } from "vue-router";
import { useUserStore } from "../store/user";
import securityRoutes from "./security";

// 路由配置
const routes = [
  {
    path: "/",
    name: "Root",
    redirect: (to) => {
      // 检查是否已登录
      const token = localStorage.getItem("token");
      // 如果已登录，重定向到仪表盘，否则重定向到登录页
      return token ? "/admin/dashboard" : "/login";
    },
  },
  {
    path: "/users",
    redirect: "/admin/users",
  },
  {
    path: "/dashboard",
    redirect: "/admin/dashboard",
  },
  {
    path: "/fix_login.html",
    name: "FixLogin",
    component: () => import("../views/NotFound.vue"),
    meta: { title: "修复登录" },
  },
  {
    path: "/login_fix",
    name: "LoginFix",
    beforeEnter: (to, from, next) => {
      // 直接重定向到静态HTML页面
      window.location.href = "/login_fix.html";
    },
    meta: { title: "登录修复工具" },
  },
  {
    path: "/admin",
    name: "Layout",
    component: () => import("../views/Layout.vue"),
    redirect: "/admin/dashboard",
    children: [
      {
        path: "dashboard",
        name: "Dashboard",
        component: () => import("../views/HomePage.vue"),
        meta: { title: "仪表盘", requiresAuth: true },
      },
      {
        path: "users",
        name: "Users",
        component: () => import("../views/Users.vue"),
        meta: { title: "用户管理", requiresAuth: true },
      },
      {
        path: "health-data-overview",
        name: "HealthDataOverview",
        component: () => import("../views/HealthDataOverview.vue"),
        meta: { title: "健康资料", requiresAuth: true },
      },
      {
        path: "health-records",
        name: "HealthRecords",
        component: () => import("../views/HealthRecords.vue"),
        meta: { title: "健康记录", requiresAuth: true },
      },
      {
        path: "documents",
        name: "Documents",
        component: () => import("../views/Documents.vue"),
        meta: { title: "文档管理", requiresAuth: true },
      },
      {
        path: "profile",
        name: "Profile",
        component: () => import("../views/Profile.vue"),
        meta: { title: "个人资料", requiresAuth: true },
      },
      {
        path: "medical-records",
        name: "MedicalRecords",
        component: () => import("../views/MedicalRecords.vue"),
        meta: { title: "医疗记录", requiresAuth: true },
      },
      {
        path: "lab-reports",
        name: "LabReports",
        component: () => import("../views/LabReports.vue"),
        meta: { title: "实验室检验报告", requiresAuth: true },
      },
      {
        path: "examination-reports",
        name: "ExaminationReports",
        component: () => import("../views/ExaminationReports.vue"),
        meta: { title: "技诊检查报告", requiresAuth: true },
      },
      {
        path: "medications",
        name: "Medications",
        component: () => import("../views/Medications.vue"),
        meta: { title: "用药管理", requiresAuth: true },
      },
      {
        path: "assessments",
        name: "Assessments",
        component: () => import("../views/Assessments.vue"),
        meta: { title: "评估量表", requiresAuth: true },
      },
      {
        path: "assessment-management",
        name: "AssessmentManagement",
        component: () => import("../views/AssessmentManagement.vue"),
        meta: { title: "评估量表管理", requiresAuth: true },
      },
      {
        path: "questionnaires",
        name: "Questionnaires",
        component: () => import("../views/QuestionnaireManagement.vue"),
        meta: { title: "调查问卷管理", requiresAuth: true },
      },
      {
        path: "follow-up",
        name: "FollowUp",
        component: () => import("../views/FollowUp.vue"),
        meta: { title: "随访记录", requiresAuth: true },
      },
      {
        path: "health-diary",
        name: "HealthDiary",
        component: () => import("../views/HealthDiary.vue"),
        meta: { title: "健康日记", requiresAuth: true },
      },
      {
        path: "other-records",
        name: "OtherRecords",
        component: () => import("../views/OtherRecords.vue"),
        meta: { title: "其它记录", requiresAuth: true },
      },

      {
        path: "operation-logs",
        name: "OperationLogs",
        component: () => import("../views/OperationLogs.vue"),
        meta: { title: "操作日志", requiresAuth: true },
      },
      {
        path: "system-settings",
        name: "SystemSettings",
        component: () => import("../views/SystemSettings.vue"),
        meta: { title: "系统设置", requiresAuth: true },
      },
      {
        path: "template-management",
        name: "TemplateManagement",
        component: () => import("../views/TemplateManagement.vue"),
        meta: { title: "模板管理", requiresAuth: true },
      },
      {
        path: "permission-management",
        name: "PermissionManagement",
        component: () => import("../views/PermissionManagement.vue"),
        meta: { title: "权限管理", requiresAuth: true, requiresAdmin: true },
      },

      // 系统管理路由
      {
        path: "system-management",
        name: "SystemManagement",
        component: () => import("../views/SystemManagement.vue"),
        meta: { title: "系统管理", requiresAuth: true, requiresAdmin: true },
      },
      {
        path: "project-management",
        name: "ProjectManagement",
        component: () => import("../views/ProjectManagement.vue"),
        meta: { title: "项目管理", requiresAuth: true, requiresAdmin: true },
      },
      {
        path: "service-management",
        name: "ServiceManagement",
        component: () => import("../views/ServiceManagement.vue"),
        meta: { title: "服务管理", requiresAuth: true, requiresAdmin: true },
      },
      {
        path: "deployment-management",
        name: "DeploymentManagement",
        component: () => import("../views/DeploymentManagement.vue"),
        meta: { title: "部署管理", requiresAuth: true, requiresAdmin: true },
      },
      {
        path: "test-management",
        name: "TestManagement",
        component: () => import("../components/test/TestManagementDashboard.vue"),
        meta: { title: "测试管理", requiresAuth: true, requiresAdmin: true },
        children: [
          {
            path: "management",
            name: "TestManagementMain",
            component: () => import("../components/test/TestManagement.vue"),
            meta: { title: "测试管理详情", requiresAuth: true, requiresAdmin: true }
          },
          {
            path: "automation",
            name: "AutoTestExecutor",
            component: () => import("../components/test/AutoTestExecutor.vue"),
            meta: { title: "自动化测试执行", requiresAuth: true, requiresAdmin: true }
          },
          {
            path: "suites",
            name: "TestSuiteManager",
            component: () => import("../components/test/TestSuiteManager.vue"),
            meta: { title: "测试套件管理", requiresAuth: true, requiresAdmin: true }
          },
          {
            path: "coverage",
            name: "CoverageAnalyzer",
            component: () => import("../components/test/CoverageAnalyzer.vue"),
            meta: { title: "覆盖率分析", requiresAuth: true, requiresAdmin: true }
          },
          {
            path: "history",
            name: "TestHistoryAnalyzer",
            component: () => import("../components/test/TestHistoryAnalyzer.vue"),
            meta: { title: "测试历史分析", requiresAuth: true, requiresAdmin: true }
          },
          {
            path: "generator",
            name: "TestDataGenerator",
            component: () => import("../components/test/TestDataGenerator.vue"),
            meta: { title: "测试数据生成器", requiresAuth: true, requiresAdmin: true }
          },
          {
            path: "config",
            name: "TestConfigManager",
            component: () => import("../components/test/TestConfigManager.vue"),
            meta: { title: "测试配置管理", requiresAuth: true, requiresAdmin: true }
          },
          {
            path: "reports",
            name: "TestReportGenerator",
            component: () => import("../components/test/TestReportGenerator.vue"),
            meta: { title: "测试报告生成器", requiresAuth: true, requiresAdmin: true }
          }
        ]
      },
      {
        path: "data-mode-management",
        name: "DataModeManagement",
        component: () => import("../views/DataModeManagement.vue"),
        meta: { title: "数据模式管理", requiresAuth: true, requiresAdmin: true },
      },
      {
        path: "config-management",
        name: "ConfigManagement",
        component: () => import("../views/ConfigManagement.vue"),
        meta: { title: "配置管理", requiresAuth: true, requiresAdmin: true },
      },
      {
        path: "monitoring-alerts",
        name: "MonitoringAlerts",
        component: () => import("../views/MonitoringAlerts.vue"),
        meta: { title: "监控告警", requiresAuth: true, requiresAdmin: true },
      },

      // 安全相关路由
      ...securityRoutes,
    ],
  },
  {
    path: "/login",
    name: "Login",
    component: () => import("../views/Login.vue"),
    meta: { title: "登录", disableRedirect: true },
  },
  {
    path: "/register",
    name: "Register",
    component: () => import("../views/Register.vue"),
    meta: { title: "注册" },
  },
  {
    path: "/:pathMatch(.*)*",
    name: "NotFound",
    component: () => import("../views/NotFound.vue"),
    meta: { title: "页面未找到" },
  },
];

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
});

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = to.meta.title
    ? `${to.meta.title} - 健康管理系统`
    : "健康管理系统";

  // 添加调试信息
  console.log(`路由导航: ${from.path} -> ${to.path}`);

  // 处理登录页面的特殊情况
  if (to.path === "/login") {
    // 获取已保存的token
    const token = localStorage.getItem("token");
    console.log(`访问登录页，token状态: ${token ? "已登录" : "未登录"}`);

    // 如果用户已登录，直接重定向到dashboard
    if (token) {
      console.log("检测到用户已登录，重定向到dashboard");
      return next("/admin/dashboard");
    }

    // 没有登录，正常显示登录页
    return next();
  }

  // 检查是否需要认证
  if (to.matched.some((record) => record.meta.requiresAuth)) {
    // 检查登录状态
    const token = localStorage.getItem("token");

    // 如果未登录，重定向到登录页
    if (!token) {
      console.log(`需要认证但未登录，重定向到登录页 (目标: ${to.path})`);
      return next({
        path: "/login",
        query: { redirect: to.fullPath },
      });
    }

    // 检查用户是否应该进入管理界面
    let shouldRedirectToAdmin = false;
    let userRole = "";
    let customId = "";

    try {
      // 1. 从localStorage获取用户信息
      const userInfo = JSON.parse(localStorage.getItem("user") || "{}");

      // 检查用户名是否是admin
      if (userInfo.username === "admin") {
        console.log("检测到管理员用户admin");
        shouldRedirectToAdmin = true;
        userRole = "super_admin";

        // 确保admin用户有正确的角色
        if (!userInfo.role || userInfo.role !== "super_admin") {
          console.log("更新admin用户角色为super_admin");
          userInfo.role = "super_admin";
          localStorage.setItem("user", JSON.stringify(userInfo));
        }
      }

      // 检查角色
      if (["super_admin", "admin", "unit_admin"].includes(userInfo.role)) {
        console.log(`检测到管理员角色: ${userInfo.role}`);
        shouldRedirectToAdmin = true;
        userRole = userInfo.role;
      }

      // 检查custom_id是否以SM开头
      if (
        userInfo.custom_id &&
        userInfo.custom_id.toString().startsWith("SM")
      ) {
        console.log(`检测到以SM开头的custom_id: ${userInfo.custom_id}`);
        shouldRedirectToAdmin = true;
        customId = userInfo.custom_id;
      }

      // 2. 从token检查用户名
      if (!shouldRedirectToAdmin && token) {
        try {
          const tokenParts = token.split(".");
          if (tokenParts.length === 3) {
            const payload = JSON.parse(atob(tokenParts[1]));

            // 检查token中的用户名
            if (payload.sub === "admin" || payload.username === "admin") {
              console.log("从令牌中检测到admin用户");
              shouldRedirectToAdmin = true;
              userRole = "super_admin";

              // 更新localStorage中的用户信息
              if (!userInfo.username || userInfo.username !== "admin") {
                userInfo.username = "admin";
                userInfo.role = "super_admin";
                localStorage.setItem("user", JSON.stringify(userInfo));
              }
            }

            // 检查token中的角色
            if (
              payload.role &&
              ["super_admin", "admin"].includes(payload.role)
            ) {
              console.log(`从令牌中检测到管理员角色: ${payload.role}`);
              shouldRedirectToAdmin = true;
              userRole = payload.role;
            }

            // 检查token中的custom_id
            if (
              payload.custom_id &&
              payload.custom_id.toString().startsWith("SM")
            ) {
              console.log(
                `从令牌中检测到以SM开头的custom_id: ${payload.custom_id}`
              );
              shouldRedirectToAdmin = true;
              customId = payload.custom_id;
            }
          }
        } catch (e) {
          console.error("解析令牌时出错:", e);
        }
      }
    } catch (e) {
      console.error("检查用户信息时出错:", e);
    }

    // 如果用户应该进入管理界面，但请求的URL不在管理界面内，则重定向
    if (
      shouldRedirectToAdmin &&
      to.path.indexOf("/admin") !== 0 &&
      to.path !== "/"
    ) {
      console.log(
        `检测到管理员用户/角色: ${userRole} 或以SM开头的custom_id: ${customId}，重定向到管理界面`
      );
      return next("/admin/dashboard");
    }

    // 特殊处理：确保管理员用户访问dashboard时加载管理员仪表盘
    if (shouldRedirectToAdmin && to.path === "/admin/dashboard") {
      console.log("管理员用户访问仪表盘，确保加载管理员仪表盘");

      // 确保用户信息中的角色正确
      try {
        const userInfo = JSON.parse(localStorage.getItem("user") || "{}");
        if (
          !userInfo.role ||
          !["super_admin", "admin", "unit_admin"].includes(userInfo.role)
        ) {
          console.log("更新localStorage中的用户角色为super_admin");
          userInfo.role = "super_admin";
          localStorage.setItem("user", JSON.stringify(userInfo));
        }
      } catch (e) {
        console.error("更新用户角色失败:", e);
      }
    }

    // 检查权限需求 - 如果是管理员用户，始终允许访问
    if (to.matched.some((record) => record.meta.requiresAdmin)) {
      if (shouldRedirectToAdmin) {
        console.log("管理员用户特殊处理: 允许访问管理员页面");
        return next();
      }

      const userStore = useUserStore();

      // 检查Pinia store中的isAdmin
      if (!userStore.isAdmin) {
        console.log("当前路由需要管理员权限，但用户不是管理员");
        // 如果当前用户不是管理员，且目标页面需要管理员权限，重定向到仪表盘
        return next({ path: "/admin/dashboard" });
      }
      console.log("用户是管理员，允许访问管理员页面");
    }

    // 特殊管理admin用户的跳转
    if (shouldRedirectToAdmin && to.path === "/") {
      console.log("管理员用户访问根路径，重定向到管理员仪表盘");
      return next("/admin/dashboard");
    }

    // 已登录，继续导航
    next();
    return;
  }

  // 不需要认证的路由，直接放行
  next();
});

export default router;
