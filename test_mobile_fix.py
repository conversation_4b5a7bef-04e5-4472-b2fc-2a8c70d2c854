#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

# 配置
base_url = "http://localhost:8000"
headers = {
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJTTV8wMDYiLCJleHAiOjE3MzQ2NzQ0MDB9.123",
    "Content-Type": "application/json",
    "X-User-ID": "SM_006"
}

def test_mobile_apis():
    """测试移动端API修复效果"""
    print("=== 测试移动端API修复效果 ===")
    
    # 测试1: 获取所有量表（不带status参数）
    print("\n1. 测试获取所有量表:")
    response = requests.get(f"{base_url}/api/mobile/assessments?custom_id=SM_006&limit=50", headers=headers)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"API返回数据类型: {type(data)}")
        print(f"API返回数据: {json.dumps(data, ensure_ascii=False, indent=2)[:500]}...")
        
        # 根据实际返回的数据结构解析
        if isinstance(data, list):
            assessments = data
            total = len(data)
        else:
            # 如果是字典，尝试不同的键
            assessments = data.get('assessments', data.get('data', []))
            total = data.get('total', len(assessments))
            
        print(f"总数: {total}")
        print(f"返回记录数: {len(assessments)}")
        
        if assessments:
            # 统计不同状态的量表
            pending_count = 0
            completed_count = 0
            for a in assessments:
                if isinstance(a, dict):
                    status = a.get('status', 'unknown')
                    if status == 'pending':
                        pending_count += 1
                    elif status == 'completed':
                        completed_count += 1
            
            print(f"状态统计: pending={pending_count}, completed={completed_count}")
            
            print("量表列表:")
            for i, assessment in enumerate(assessments[:10]):  # 只显示前10个
                if isinstance(assessment, dict):
                    print(f"  {i+1}. ID: {assessment.get('id')}, 状态: {assessment.get('status')}, 名称: {assessment.get('name')}")
        else:
            print("没有找到量表数据")
    else:
        print(f"请求失败: {response.text}")
    
    # 测试2: 获取所有问卷（不带status参数）
    print("\n2. 测试获取所有问卷:")
    response = requests.get(f"{base_url}/api/mobile/questionnaires?custom_id=SM_006&limit=50", headers=headers)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"API返回数据类型: {type(data)}")
        
        # 根据实际返回的数据结构解析
        if isinstance(data, list):
            questionnaires = data
            total = len(data)
        else:
            # 如果是字典，尝试不同的键
            questionnaires = data.get('questionnaires', data.get('data', []))
            total = data.get('total', len(questionnaires))
            
        print(f"总数: {total}")
        print(f"返回记录数: {len(questionnaires)}")
        
        if questionnaires:
            # 统计不同状态的问卷
            pending_count = 0
            completed_count = 0
            for q in questionnaires:
                if isinstance(q, dict):
                    status = q.get('status', 'unknown')
                    if status == 'pending':
                        pending_count += 1
                    elif status == 'completed':
                        completed_count += 1
            
            print(f"状态统计: pending={pending_count}, completed={completed_count}")
            
            print("问卷列表:")
            for i, questionnaire in enumerate(questionnaires[:10]):  # 只显示前10个
                if isinstance(questionnaire, dict):
                    print(f"  {i+1}. ID: {questionnaire.get('id')}, 状态: {questionnaire.get('status')}, 名称: {questionnaire.get('name')}")
        else:
            print("没有找到问卷数据")
    else:
        print(f"请求失败: {response.text}")
    
    # 测试3: 仍然测试pending状态过滤
    print("\n3. 测试pending状态过滤:")
    response = requests.get(f"{base_url}/api/mobile/assessments?custom_id=SM_006&status=pending&limit=50", headers=headers)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        
        # 根据实际返回的数据结构解析
        if isinstance(data, list):
            assessments = data
            total = len(data)
        else:
            # 如果是字典，尝试不同的键
            assessments = data.get('assessments', data.get('data', []))
            total = data.get('total', len(assessments))
            
        print(f"pending状态量表总数: {total}")
        print(f"pending状态量表返回记录数: {len(assessments)}")
        
        if assessments:
            print("pending状态量表列表:")
            for i, assessment in enumerate(assessments[:10]):  # 只显示前10个
                if isinstance(assessment, dict):
                    print(f"  {i+1}. ID: {assessment.get('id')}, 状态: {assessment.get('status')}, 名称: {assessment.get('name')}")
        else:
            print("没有找到pending状态的量表")
    else:
        print(f"请求失败: {response.text}")

if __name__ == "__main__":
    test_mobile_apis()