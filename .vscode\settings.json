{"bitoAI.codeCompletion.enableAutoCompletion": false, "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.analysis.typeCheckingMode": "basic", "python.analysis.diagnosticSeverityOverrides": {"reportAttributeAccessIssue": "none", "reportOptionalMemberAccess": "none", "reportOptionalOperation": "none", "reportOptionalCall": "none", "reportOptionalIterable": "none", "reportOptionalSubscript": "none", "reportOptionalMethodAccess": "none"}, "python.analysis.extraPaths": ["${workspaceFolder}"], "python.autoComplete.extraPaths": ["${workspaceFolder}"], "python.analysis.diagnosticMode": "workspace", "python.languageServer": "None", "postman.settings.dotenv-detection-notification-visibility": false, "codingcopilot.modelDocumentLanguage": "中文", "python.testing.unittestArgs": ["-v", "-s", "./mobile", "-p", "*test.py"], "python.testing.pytestEnabled": false, "python.testing.unittestEnabled": true, "http.proxy": "", "http.proxySupport": "off"}