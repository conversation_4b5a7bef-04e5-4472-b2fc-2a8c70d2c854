#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

# 配置
base_url = "http://localhost:8000"
headers = {
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJTTV8wMDYiLCJleHAiOjE3MzQ2NzQ0MDB9.123",
    "Content-Type": "application/json",
    "X-User-ID": "SM_006"
}

def test_assessments():
    """测试量表API"""
    print("=== 测试量表API ===")
    
    # 测试1: 获取所有量表
    print("\n1. 获取所有量表:")
    response = requests.get(f"{base_url}/api/mobile/assessments?custom_id=SM_006&limit=50", headers=headers)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"API响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        if isinstance(data.get('data'), list):
            assessments = data.get('data', [])
            total = len(assessments)
        else:
            assessments = data.get('data', {}).get('assessments', [])
            total = data.get('data', {}).get('total', 0)
        print(f"总数: {total}")
        print(f"返回记录数: {len(assessments)}")
        for i, assessment in enumerate(assessments):
            print(f"  {i+1}. ID: {assessment.get('id')}, 名称: {assessment.get('name')}, 状态: {assessment.get('status')}")
    else:
        print(f"请求失败: {response.text}")
    
    # 测试2: 获取pending状态的量表
    print("\n2. 获取pending状态的量表:")
    response = requests.get(f"{base_url}/api/mobile/assessments?custom_id=SM_006&status=pending&limit=50", headers=headers)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        if isinstance(data.get('data'), list):
            assessments = data.get('data', [])
            total = len(assessments)
        else:
            assessments = data.get('data', {}).get('assessments', [])
            total = data.get('data', {}).get('total', 0)
        print(f"总数: {total}")
        print(f"返回记录数: {len(assessments)}")
        for i, assessment in enumerate(assessments):
            print(f"  {i+1}. ID: {assessment.get('id')}, 名称: {assessment.get('name')}, 状态: {assessment.get('status')}")
    else:
        print(f"请求失败: {response.text}")
    
    # 测试3: 获取completed状态的量表
    print("\n3. 获取completed状态的量表:")
    response = requests.get(f"{base_url}/api/mobile/assessments?custom_id=SM_006&status=completed&limit=50", headers=headers)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        if isinstance(data.get('data'), list):
            assessments = data.get('data', [])
            total = len(assessments)
        else:
            assessments = data.get('data', {}).get('assessments', [])
            total = data.get('data', {}).get('total', 0)
        print(f"总数: {total}")
        print(f"返回记录数: {len(assessments)}")
        for i, assessment in enumerate(assessments):
            print(f"  {i+1}. ID: {assessment.get('id')}, 名称: {assessment.get('name')}, 状态: {assessment.get('status')}")
    else:
        print(f"请求失败: {response.text}")

def test_questionnaires():
    """测试问卷API"""
    print("\n=== 测试问卷API ===")
    
    # 测试1: 获取所有问卷
    print("\n1. 获取所有问卷:")
    response = requests.get(f"{base_url}/api/mobile/questionnaires?custom_id=SM_006&limit=50", headers=headers)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        if isinstance(data.get('data'), list):
            questionnaires = data.get('data', [])
            total = len(questionnaires)
        else:
            questionnaires = data.get('data', {}).get('questionnaires', [])
            total = data.get('data', {}).get('total', 0)
        print(f"总数: {total}")
        print(f"返回记录数: {len(questionnaires)}")
        for i, questionnaire in enumerate(questionnaires):
            print(f"  {i+1}. ID: {questionnaire.get('id')}, 标题: {questionnaire.get('title')}, 状态: {questionnaire.get('status')}")
    else:
        print(f"请求失败: {response.text}")
    
    # 测试2: 获取pending状态的问卷
    print("\n2. 获取pending状态的问卷:")
    response = requests.get(f"{base_url}/api/mobile/questionnaires?custom_id=SM_006&status=pending&limit=50", headers=headers)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        if isinstance(data.get('data'), list):
            questionnaires = data.get('data', [])
            total = len(questionnaires)
        else:
            questionnaires = data.get('data', {}).get('questionnaires', [])
            total = data.get('data', {}).get('total', 0)
        print(f"总数: {total}")
        print(f"返回记录数: {len(questionnaires)}")
        for i, questionnaire in enumerate(questionnaires):
            print(f"  {i+1}. ID: {questionnaire.get('id')}, 标题: {questionnaire.get('title')}, 状态: {questionnaire.get('status')}")
    else:
        print(f"请求失败: {response.text}")
    
    # 测试3: 获取completed状态的问卷
    print("\n3. 获取completed状态的问卷:")
    response = requests.get(f"{base_url}/api/mobile/questionnaires?custom_id=SM_006&status=completed&limit=50", headers=headers)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        if isinstance(data.get('data'), list):
            questionnaires = data.get('data', [])
            total = len(questionnaires)
        else:
            questionnaires = data.get('data', {}).get('questionnaires', [])
            total = data.get('data', {}).get('total', 0)
        print(f"总数: {total}")
        print(f"返回记录数: {len(questionnaires)}")
        for i, questionnaire in enumerate(questionnaires):
            print(f"  {i+1}. ID: {questionnaire.get('id')}, 标题: {questionnaire.get('title')}, 状态: {questionnaire.get('status')}")
    else:
        print(f"请求失败: {response.text}")

if __name__ == "__main__":
    test_assessments()
    test_questionnaires()