#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库中的评估和问卷数据
分析为什么前端只显示问卷而没有评估量表
"""

import sqlite3
import json
import os

def check_assessment_questionnaire_data():
    """检查评估和问卷数据"""
    db_path = r'C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db'
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print(f"检查数据库: {db_path}")
        print("=" * 60)
        
        # 检查问卷相关表
        print("\n1. 检查问卷相关数据:")
        print("-" * 30)
        
        # 问卷模板
        cursor.execute("SELECT COUNT(*) FROM questionnaire_templates")
        questionnaire_template_count = cursor.fetchone()[0]
        print(f"问卷模板数量: {questionnaire_template_count}")
        
        if questionnaire_template_count > 0:
            # 先查看questionnaire_templates表结构
            cursor.execute("PRAGMA table_info(questionnaire_templates)")
            qt_columns = cursor.fetchall()
            print(f"问卷模板表列: {[col[1] for col in qt_columns]}")
            
            cursor.execute("SELECT * FROM questionnaire_templates LIMIT 5")
            questionnaire_templates = cursor.fetchall()
            print(f"问卷模板示例: {questionnaire_templates}")
        
        # 问卷分发
        cursor.execute("SELECT COUNT(*) FROM questionnaire_distributions")
        questionnaire_dist_count = cursor.fetchone()[0]
        print(f"问卷分发数量: {questionnaire_dist_count}")
        
        if questionnaire_dist_count > 0:
            cursor.execute("SELECT id, questionnaire_id, custom_id, status FROM questionnaire_distributions LIMIT 5")
            distributions = cursor.fetchall()
            print("问卷分发示例:")
            for dist in distributions:
                print(f"  ID: {dist[0]}, 问卷ID: {dist[1]}, 用户: {dist[2]}, 状态: {dist[3]}")
        
        # 问卷回答
        cursor.execute("SELECT COUNT(*) FROM questionnaire_responses")
        questionnaire_resp_count = cursor.fetchone()[0]
        print(f"问卷回答数量: {questionnaire_resp_count}")
        
        # 检查评估相关表
        print("\n2. 检查评估相关数据:")
        print("-" * 30)
        
        # 检查是否有assessment_templates表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='assessment_templates'")
        assessment_table_exists = cursor.fetchone()
        
        if assessment_table_exists:
            cursor.execute("SELECT COUNT(*) FROM assessment_templates")
            assessment_template_count = cursor.fetchone()[0]
            print(f"评估模板数量: {assessment_template_count}")
            
            cursor.execute("PRAGMA table_info(assessment_templates)")
            at_columns = cursor.fetchall()
            print(f"评估模板表列: {[col[1] for col in at_columns]}")
            
            cursor.execute("SELECT * FROM assessment_templates LIMIT 5")
            assessment_templates = cursor.fetchall()
            print(f"评估模板示例: {assessment_templates}")
        else:
            print("未找到assessment_templates表")
            
        # 检查是否有assessments表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='assessments'")
        assessments_table = cursor.fetchone()
        
        if assessments_table:
            cursor.execute("SELECT COUNT(*) FROM assessments")
            assessment_count = cursor.fetchone()[0]
            print(f"评估数量: {assessment_count}")
            
            if assessment_count > 0:
                cursor.execute("PRAGMA table_info(assessments)")
                a_columns = cursor.fetchall()
                print(f"评估表列: {[col[1] for col in a_columns]}")
                
                cursor.execute("SELECT * FROM assessments LIMIT 5")
                assessments = cursor.fetchall()
                print(f"评估示例: {assessments}")
        else:
            print("评估表(assessments)不存在！")
        
        # 评估分发
        cursor.execute("SELECT COUNT(*) FROM assessment_distributions")
        assessment_dist_count = cursor.fetchone()[0]
        print(f"评估分发数量: {assessment_dist_count}")
        
        if assessment_dist_count > 0:
            cursor.execute("SELECT id, assessment_id, custom_id, status FROM assessment_distributions LIMIT 5")
            distributions = cursor.fetchall()
            print("评估分发示例:")
            for dist in distributions:
                print(f"  ID: {dist[0]}, 评估ID: {dist[1]}, 用户: {dist[2]}, 状态: {dist[3]}")
        
        # 评估回答
        cursor.execute("SELECT COUNT(*) FROM assessment_responses")
        assessment_resp_count = cursor.fetchone()[0]
        print(f"评估回答数量: {assessment_resp_count}")
        
        # 检查特定用户的数据
        print("\n3. 检查特定用户数据:")
        print("-" * 30)
        
        test_users = ['SM_001', 'SM_006']
        for user_id in test_users:
            print(f"\n用户 {user_id}:")
            
            # 用户的问卷分发
            cursor.execute("SELECT COUNT(*) FROM questionnaire_distributions WHERE custom_id = ?", (user_id,))
            user_q_dist = cursor.fetchone()[0]
            print(f"  问卷分发: {user_q_dist}")
            
            # 用户的评估分发
            cursor.execute("SELECT COUNT(*) FROM assessment_distributions WHERE custom_id = ?", (user_id,))
            user_a_dist = cursor.fetchone()[0]
            print(f"  评估分发: {user_a_dist}")
            
            # 用户的问卷回答
            cursor.execute("SELECT COUNT(*) FROM questionnaire_responses WHERE custom_id = ?", (user_id,))
            user_q_resp = cursor.fetchone()[0]
            print(f"  问卷回答: {user_q_resp}")
            
            # 用户的评估回答
            cursor.execute("SELECT COUNT(*) FROM assessment_responses WHERE custom_id = ?", (user_id,))
            user_a_resp = cursor.fetchone()[0]
            print(f"  评估回答: {user_a_resp}")
        
        # 检查API相关的数据结构
        print("\n4. 分析API数据结构问题:")
        print("-" * 30)
        
        # 检查问卷分发和模板的关联
        cursor.execute("""
        SELECT qd.id, qd.custom_id, qd.status, qd.questionnaire_id
        FROM questionnaire_distributions qd
        LIMIT 5
        """)
        questionnaire_distributions_sample = cursor.fetchall()
        print(f"问卷分发示例: {questionnaire_distributions_sample}")
        
        # 检查问卷分发的详细数据
        cursor.execute("""
            SELECT qd.id, qd.custom_id, qd.status, qd.questionnaire_id
            FROM questionnaire_distributions qd 
            WHERE qd.custom_id IN ('SM_001', 'SM_006') 
            LIMIT 5
        """)
        q_details = cursor.fetchall()
        
        if q_details:
            print(f"问卷分发详情: {q_details}")
        else:
            print("没有找到问卷分发详情数据")
        
        # 检查评估分发的详细数据
        if assessments_table:
            cursor.execute("""
                SELECT ad.id, ad.custom_id, ad.status, ad.created_at, ad.assessment_id
                FROM assessment_distributions ad
                WHERE ad.custom_id IN ('SM_001', 'SM_006')
                ORDER BY ad.created_at DESC
                LIMIT 10
            """)
            user_assessment_distributions = cursor.fetchall()
            print(f"用户评估分发详情: {user_assessment_distributions}")
        else:
            print("没有找到评估分发详情数据")
        
        conn.close()
        
        # 总结分析
        print("\n5. 问题分析总结:")
        print("-" * 30)
        
        if not assessments_table:
            print("❌ 关键问题: 数据库中缺少 'assessments' 表！")
            print("   这是导致前端无法显示评估量表的主要原因。")
        elif assessment_dist_count == 0:
            print("❌ 问题: 虽然有评估表结构，但没有评估分发数据")
        elif assessment_resp_count == 0:
            print("⚠️  问题: 有评估分发但没有评估回答数据")
        else:
            print("✅ 评估相关数据看起来正常，可能是API逻辑问题")
        
        print(f"\n数据统计:")
        print(f"  问卷模板: {questionnaire_template_count}")
        print(f"  问卷分发: {questionnaire_dist_count}")
        print(f"  问卷回答: {questionnaire_resp_count}")
        if assessments_table:
            cursor = sqlite3.connect(db_path).cursor()
            cursor.execute("SELECT COUNT(*) FROM assessments")
            assessment_count = cursor.fetchone()[0]
            print(f"  评估模板: {assessment_count}")
        else:
            print(f"  评估模板: 表不存在")
        print(f"  评估分发: {assessment_dist_count}")
        print(f"  评估回答: {assessment_resp_count}")
        
    except Exception as e:
        print(f"检查数据库时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    check_assessment_questionnaire_data()