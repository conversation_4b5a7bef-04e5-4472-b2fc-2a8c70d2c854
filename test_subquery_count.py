from sqlalchemy import create_engine, text, func
from sqlalchemy.orm import sessionmaker
import sys
sys.path.append('c:/Users/<USER>/Desktop/health-Trea/YUN/backend')

from app.models.assessment import Assessment
from app.models.distribution import AssessmentDistribution

# 连接数据库
engine = create_engine('sqlite:///c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db', echo=True)
Session = sessionmaker(bind=engine)
session = Session()

print("=== 测试修复后的子查询Count ===")

# 测试修复后的子查询count逻辑
print("\n测试修复后的子查询count逻辑:")
try:
    custom_id = 'SM_006'
    status = None
    
    # 模拟API中的修复后逻辑
    subquery = session.query(Assessment.id).outerjoin(AssessmentDistribution).distinct(Assessment.id)
    if custom_id:
        subquery = subquery.filter(Assessment.custom_id == custom_id)
    if status:
        subquery = subquery.filter(Assessment.status == status)
    
    print(f"子查询SQL: {subquery.statement.compile(compile_kwargs={'literal_binds': True})}")
    
    total = subquery.count()
    print(f"修复后的Count结果: {total}")
    
    # 测试实际的查询结果
    main_query = session.query(Assessment).outerjoin(AssessmentDistribution).distinct(Assessment.id)
    if custom_id:
        main_query = main_query.filter(Assessment.custom_id == custom_id)
    if status:
        main_query = main_query.filter(Assessment.status == status)
    
    results = main_query.all()
    print(f"实际查询结果数量: {len(results)}")
    
    # 测试带status的查询
    print("\n测试带status=completed的查询:")
    status = 'completed'
    
    subquery2 = session.query(Assessment.id).outerjoin(AssessmentDistribution).distinct(Assessment.id)
    if custom_id:
        subquery2 = subquery2.filter(Assessment.custom_id == custom_id)
    if status:
        subquery2 = subquery2.filter(Assessment.status == status)
    
    print(f"带status的子查询SQL: {subquery2.statement.compile(compile_kwargs={'literal_binds': True})}")
    
    total2 = subquery2.count()
    print(f"带status的Count结果: {total2}")
    
    main_query2 = session.query(Assessment).outerjoin(AssessmentDistribution).distinct(Assessment.id)
    if custom_id:
        main_query2 = main_query2.filter(Assessment.custom_id == custom_id)
    if status:
        main_query2 = main_query2.filter(Assessment.status == status)
    
    results2 = main_query2.all()
    print(f"带status的实际查询结果数量: {len(results2)}")
    for assessment in results2:
        print(f"  ID: {assessment.id}, Name: {assessment.name}, Status: {assessment.status}")
    
except Exception as e:
    print(f"查询出错: {e}")

session.close()