<template>
  <div class="admin-layout">
    <header class="admin-header">
      <div class="logo-container">
        <img src="@/assets/logo.png" alt="Logo" class="logo" />
        <h1>健康管理系统</h1>
      </div>
      <div class="user-info">
        <span>{{ username }}</span>
        <button @click="logout" class="logout-btn">退出登录</button>
      </div>
    </header>
    
    <div class="main-container">
      <aside class="sidebar">
        <nav>
          <ul>
            <li>
              <router-link to="/admin/dashboard">首页</router-link>
            </li>
            <li>
              <router-link to="/admin/users">用户管理</router-link>
            </li>
            <li>
              <router-link to="/admin/health-records">健康记录</router-link>
            </li>
            <li>
              <router-link to="/admin/questionnaires">问卷管理</router-link>
            </li>
            <li>
              <router-link to="/admin/security/two-factor-auth">双因素认证</router-link>
            </li>

          </ul>
        </nav>
      </aside>
      
      <main class="content">
        <router-view />
      </main>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const username = ref('管理员');

onMounted(() => {
  // 从localStorage获取用户信息
  const userStr = localStorage.getItem('user');
  if (userStr) {
    try {
      const user = JSON.parse(userStr);
      username.value = user.username || user.full_name || '管理员';
    } catch (e) {
      console.error('解析用户信息失败', e);
    }
  }
});

const logout = () => {
  // 清除登录信息
  localStorage.removeItem('token');
  localStorage.removeItem('user');
  
  // 跳转到登录页
  router.push('/login');
};
</script>

<style scoped>
.admin-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.admin-header {
  background-color: #1976d2;
  color: white;
  padding: 0.5rem 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo {
  height: 40px;
  margin-right: 1rem;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logout-btn {
  background-color: transparent;
  border: 1px solid white;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  cursor: pointer;
}

.logout-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.main-container {
  display: flex;
  flex: 1;
}

.sidebar {
  width: 250px;
  background-color: #f5f5f5;
  padding: 1rem;
}

.sidebar ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar li {
  margin-bottom: 0.5rem;
}

.sidebar a {
  display: block;
  padding: 0.75rem 1rem;
  color: #333;
  text-decoration: none;
  border-radius: 4px;
}

.sidebar a:hover, .sidebar a.router-link-active {
  background-color: #e0e0e0;
}

.content {
  flex: 1;
  padding: 1.5rem;
  background-color: #fff;
}
</style>
