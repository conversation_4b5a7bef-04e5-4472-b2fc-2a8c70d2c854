#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os
from datetime import datetime, <PERSON><PERSON><PERSON>

def create_sm008_distributions():
    """为SM_008用户创建问卷和评估分发记录"""
    
    # 数据库文件路径
    db_path = os.path.join(os.path.dirname(__file__), 'app.db')
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("=== 为SM_008用户创建分发记录 ===")
        
        # 1. 检查用户是否存在
        cursor.execute("SELECT id, username, custom_id FROM users WHERE custom_id = 'SM_008'")
        user = cursor.fetchone()
        if not user:
            print("✗ 未找到用户SM_008")
            return
        
        print(f"✓ 找到用户SM_008: {user[1]} (ID: {user[0]})")
        
        # 2. 获取所有可用的问卷模板
        cursor.execute("SELECT id, title FROM questionnaires ORDER BY id")
        questionnaires = cursor.fetchall()
        print(f"\n可用问卷模板数量: {len(questionnaires)}")
        
        # 3. 获取所有可用的评估模板
        cursor.execute("SELECT id, name FROM assessments ORDER BY id")
        assessments = cursor.fetchall()
        print(f"可用评估模板数量: {len(assessments)}")
        
        # 4. 检查已有的问卷分发记录
        cursor.execute("""
            SELECT questionnaire_id FROM questionnaire_distributions 
            WHERE custom_id = 'SM_008'
        """)
        existing_q_distributions = [row[0] for row in cursor.fetchall()]
        print(f"\n已有问卷分发记录: {existing_q_distributions}")
        
        # 5. 检查已有的评估分发记录
        cursor.execute("""
            SELECT assessment_id FROM assessment_distributions 
            WHERE custom_id = 'SM_008'
        """)
        existing_a_distributions = [row[0] for row in cursor.fetchall()]
        print(f"已有评估分发记录: {existing_a_distributions}")
        
        # 6. 为缺少的问卷创建分发记录
        created_q_count = 0
        for q_id, q_title in questionnaires:
            if q_id not in existing_q_distributions:
                due_date = datetime.now() + timedelta(days=30)
                cursor.execute("""
                    INSERT INTO questionnaire_distributions 
                    (custom_id, questionnaire_id, status, created_at, due_date)
                    VALUES (?, ?, 'pending', ?, ?)
                """, ('SM_008', q_id, datetime.now(), due_date))
                print(f"  ✓ 创建问卷分发: {q_title} (ID: {q_id})")
                created_q_count += 1
        
        # 7. 为缺少的评估创建分发记录
        created_a_count = 0
        for a_id, a_name in assessments:
            if a_id not in existing_a_distributions:
                due_date = datetime.now() + timedelta(days=30)
                cursor.execute("""
                    INSERT INTO assessment_distributions 
                    (custom_id, assessment_id, status, created_at, due_date)
                    VALUES (?, ?, 'pending', ?, ?)
                """, ('SM_008', a_id, datetime.now(), due_date))
                print(f"  ✓ 创建评估分发: {a_name} (ID: {a_id})")
                created_a_count += 1
        
        # 8. 提交更改
        conn.commit()
        
        print(f"\n=== 创建完成 ===")
        print(f"新增问卷分发记录: {created_q_count} 条")
        print(f"新增评估分发记录: {created_a_count} 条")
        
        if created_q_count > 0 or created_a_count > 0:
            print("\n✓ SM_008用户现在应该能够看到待完成的问卷和评估了！")
        else:
            print("\n⚠️  所有问卷和评估都已经分发给SM_008用户了。")
        
        conn.close()
        
    except Exception as e:
        print(f"创建分发记录时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    create_sm008_distributions()