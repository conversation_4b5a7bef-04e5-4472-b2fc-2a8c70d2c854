from typing import Any, List, Dict, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.orm import Session
from datetime import datetime
from app.utils.field_compatibility import ensure_field_compatibility, ensure_list_field_compatibility

from app.db.session import get_db
from app.models.user import User
from app.core.auth import get_current_active_user_custom
from app.api import deps

router = APIRouter()

# 管理端API - 获取特定用户的体检报告
@router.get("/user/{custom_id}", response_model=Dict[str, Any])
def read_user_records(
    *,
    db: Session = Depends(deps.get_db),
    custom_id: str = Path(..., description="用户ID"),
    exam_type: Optional[str] = Query(None, description="体检类型"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    current_user: User = Depends(get_current_active_user_custom)
) -> Any:
    """
    获取指定用户的体检报告列表
    """
    print(f"physical_exams.py - 获取用户记录 - 用户ID: {custom_id}")

    # 支持数字ID和自定义ID查找用户
    user = None
    if custom_id.isdigit():
        user = db.query(User).filter(User.id == int(custom_id)).first()
    else:
        user = db.query(User).filter(User.custom_id == custom_id).first()
    
    if not user:
        print(f"用户{custom_id}不存在，返回404异常")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到用户ID: {custom_id}"
        )

    # 权限校验
    if current_user.id != user.id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        print(f"权限检查失败 - 当前用户: {current_user.custom_id}, 角色: {current_user.role}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限查看此用户的数据"
        )

    try:
        # 这里应该查询数据库获取体检报告数据
        # 由于可能没有实际的体检报告模型，我们返回一个空数组
        
        # 如果有体检报告模型，可以使用类似以下代码：
        # from app.models.physical_exam import PhysicalExam
        # query = db.query(PhysicalExam).filter(PhysicalExam.custom_id == user.custom_id)
        # if exam_type:
        #     query = query.filter(PhysicalExam.exam_type == exam_type)
        # if start_date:
        #     query = query.filter(PhysicalExam.exam_date >= start_date)
        # if end_date:
        #     query = query.filter(PhysicalExam.exam_date <= end_date)
        # total = query.count()
        # records = []
        # if total > 0:
        #     exams = query.order_by(PhysicalExam.created_at.desc()).offset(skip).limit(limit).all()
        #     for exam in exams:
        #         records.append({
        #             "id": exam.id,
        #             "custom_id": exam.custom_id,
        #             "exam_type": exam.exam_type,
        #             "exam_date": exam.exam_date.isoformat() if exam.exam_date else None,
        #             "results": exam.results,
        #             "notes": exam.notes
        #         })
        
        # 返回空结果
        print(f"用户{custom_id}存在但无体检报告记录，返回空数组")
        return {
            "status": "success",
            "total": 0,
            "items": []
        }
    except Exception as e:
        print(f"获取用户体检报告时出错: {str(e)}")
        # 返回空结果而不是抛出异常
        return {
            "status": "success",
            "total": 0,
            "items": []
        }