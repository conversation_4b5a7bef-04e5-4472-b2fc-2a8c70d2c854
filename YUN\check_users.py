#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库中的用户信息
"""

import sqlite3
import os

def check_users():
    """检查用户信息"""
    print("=== 检查用户信息 ===")
    
    # 数据库路径
    db_path = "c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db"
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 检查users表结构
        print("\n1. 检查users表结构...")
        cursor.execute("PRAGMA table_info(users)")
        columns = cursor.fetchall()
        print("users表字段:")
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
        
        # 2. 检查所有用户
        print("\n2. 检查所有用户...")
        cursor.execute("SELECT id, username, custom_id, role, is_active FROM users")
        users = cursor.fetchall()
        print(f"找到 {len(users)} 个用户:")
        for user in users:
            print(f"  ID: {user[0]}, 用户名: {user[1]}, 自定义ID: {user[2]}, 角色: {user[3]}, 激活: {user[4]}")
        
        # 3. 检查SM_008用户详情
        print("\n3. 检查SM_008用户详情...")
        cursor.execute("SELECT * FROM users WHERE custom_id = 'SM_008'")
        sm008_user = cursor.fetchone()
        if sm008_user:
            print(f"SM_008用户详情: {sm008_user}")
        else:
            print("未找到SM_008用户")
        
        # 4. 检查有assessment数据的用户
        print("\n4. 检查有assessment数据的用户...")
        cursor.execute("""
            SELECT DISTINCT ad.custom_id, u.username, u.role
            FROM assessment_distributions ad
            LEFT JOIN users u ON ad.custom_id = u.custom_id
        """)
        assessment_users = cursor.fetchall()
        print(f"有assessment数据的用户:")
        for user in assessment_users:
            print(f"  自定义ID: {user[0]}, 用户名: {user[1]}, 角色: {user[2]}")
        
        conn.close()
        
    except Exception as e:
        print(f"检查数据库时出错: {str(e)}")
    
    print("\n=== 检查完成 ===")

if __name__ == "__main__":
    check_users()