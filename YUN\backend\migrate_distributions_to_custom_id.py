
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库迁移：将分发表的user_id字段改为custom_id
"""

import sqlite3
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate_assessment_distributions():
    """迁移评估分发表"""
    db_path = r"c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='assessment_distributions'
        """)
        
        if not cursor.fetchone():
            logger.info("assessment_distributions表不存在，跳过迁移")
            return
        
        # 检查是否已经有custom_id字段
        cursor.execute("PRAGMA table_info(assessment_distributions)")
        columns = [col[1] for col in cursor.fetchall()]
        
        if 'custom_id' in columns:
            logger.info("assessment_distributions表已有custom_id字段")
            return
        
        logger.info("开始迁移assessment_distributions表")
        
        # 创建新表
        cursor.execute("""
            CREATE TABLE assessment_distributions_new (
                id INTEGER PRIMARY KEY,
                assessment_id INTEGER NOT NULL,
                custom_id TEXT NOT NULL,
                distributor_custom_id TEXT,
                status TEXT DEFAULT 'pending',
                due_date DATETIME,
                completed_at DATETIME,
                message TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (assessment_id) REFERENCES assessments(id) ON DELETE CASCADE,
                FOREIGN KEY (custom_id) REFERENCES users(custom_id) ON DELETE CASCADE,
                FOREIGN KEY (distributor_custom_id) REFERENCES users(custom_id) ON DELETE SET NULL
            )
        """)
        
        # 迁移数据：通过users表将user_id转换为custom_id
        cursor.execute("""
            INSERT INTO assessment_distributions_new (
                id, assessment_id, custom_id, distributor_custom_id,
                status, due_date, completed_at, message, created_at, updated_at
            )
            SELECT 
                ad.id, ad.assessment_id, u.custom_id, du.custom_id,
                ad.status, ad.due_date, ad.completed_at, ad.message, 
                ad.created_at, ad.updated_at
            FROM assessment_distributions ad
            JOIN users u ON ad.user_id = u.id
            LEFT JOIN users du ON ad.distributor_id = du.id
        """)
        
        # 删除旧表，重命名新表
        cursor.execute("DROP TABLE assessment_distributions")
        cursor.execute("ALTER TABLE assessment_distributions_new RENAME TO assessment_distributions")
        
        conn.commit()
        logger.info("assessment_distributions表迁移完成")
        
    except Exception as e:
        logger.error(f"迁移assessment_distributions表失败: {e}")
        conn.rollback()
    finally:
        conn.close()

def migrate_questionnaire_distributions():
    """迁移问卷分发表"""
    db_path = r"c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='questionnaire_distributions'
        """)
        
        if not cursor.fetchone():
            logger.info("questionnaire_distributions表不存在，跳过迁移")
            return
        
        # 检查是否已经有custom_id字段
        cursor.execute("PRAGMA table_info(questionnaire_distributions)")
        columns = [col[1] for col in cursor.fetchall()]
        
        if 'custom_id' in columns:
            logger.info("questionnaire_distributions表已有custom_id字段")
            return
        
        logger.info("开始迁移questionnaire_distributions表")
        
        # 创建新表
        cursor.execute("""
            CREATE TABLE questionnaire_distributions_new (
                id INTEGER PRIMARY KEY,
                questionnaire_id INTEGER NOT NULL,
                custom_id TEXT NOT NULL,
                distributor_custom_id TEXT,
                status TEXT DEFAULT 'pending',
                due_date DATETIME,
                completed_at DATETIME,
                message TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (questionnaire_id) REFERENCES questionnaires(id) ON DELETE CASCADE,
                FOREIGN KEY (custom_id) REFERENCES users(custom_id) ON DELETE CASCADE,
                FOREIGN KEY (distributor_custom_id) REFERENCES users(custom_id) ON DELETE SET NULL
            )
        """)
        
        # 迁移数据：通过users表将user_id转换为custom_id
        cursor.execute("""
            INSERT INTO questionnaire_distributions_new (
                id, questionnaire_id, custom_id, distributor_custom_id,
                status, due_date, completed_at, message, created_at, updated_at
            )
            SELECT 
                qd.id, qd.questionnaire_id, u.custom_id, du.custom_id,
                qd.status, qd.due_date, qd.completed_at, qd.message, 
                qd.created_at, qd.updated_at
            FROM questionnaire_distributions qd
            JOIN users u ON qd.user_id = u.id
            LEFT JOIN users du ON qd.distributor_id = du.id
        """)
        
        # 删除旧表，重命名新表
        cursor.execute("DROP TABLE questionnaire_distributions")
        cursor.execute("ALTER TABLE questionnaire_distributions_new RENAME TO questionnaire_distributions")
        
        conn.commit()
        logger.info("questionnaire_distributions表迁移完成")
        
    except Exception as e:
        logger.error(f"迁移questionnaire_distributions表失败: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    print("开始数据库迁移...")
    migrate_assessment_distributions()
    migrate_questionnaire_distributions()
    print("数据库迁移完成")
