#!/usr/bin/env node

/**
 * 文档生成脚本
 * 自动生成项目文档，包括API文档、组件文档、架构文档等
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

class DocumentationGenerator {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..')
    this.docsDir = path.join(this.projectRoot, 'docs')
    this.srcDir = path.join(this.projectRoot, 'src')
    this.outputDir = path.join(this.docsDir, 'generated')
  }

  /**
   * 初始化文档目录
   */
  initializeDirectories() {
    const dirs = [
      this.docsDir,
      this.outputDir,
      path.join(this.outputDir, 'api'),
      path.join(this.outputDir, 'components'),
      path.join(this.outputDir, 'architecture')
    ]

    dirs.forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true })
        console.log(`✅ 创建目录: ${dir}`)
      }
    })
  }

  /**
   * 扫描并分析Vue组件
   */
  analyzeComponents() {
    console.log('🔍 分析Vue组件...')
    const components = []
    const componentsDir = path.join(this.srcDir, 'components')

    if (fs.existsSync(componentsDir)) {
      this.scanDirectory(componentsDir, '.vue', (filePath) => {
        const content = fs.readFileSync(filePath, 'utf-8')
        const component = this.parseVueComponent(filePath, content)
        if (component) {
          components.push(component)
        }
      })
    }

    return components
  }

  /**
   * 解析Vue组件
   */
  parseVueComponent(filePath, content) {
    const relativePath = path.relative(this.srcDir, filePath)
    const componentName = path.basename(filePath, '.vue')

    // 提取组件信息
    const props = this.extractProps(content)
    const emits = this.extractEmits(content)
    const slots = this.extractSlots(content)
    const methods = this.extractMethods(content)

    return {
      name: componentName,
      path: relativePath,
      props,
      emits,
      slots,
      methods,
      description: this.extractDescription(content)
    }
  }

  /**
   * 提取组件Props
   */
  extractProps(content) {
    const props = []
    const propsRegex = /props:\s*{([\s\S]*?)}/
    const match = content.match(propsRegex)

    if (match) {
      const propsContent = match[1]
      const propRegex = /(\w+):\s*{([^}]*)}/g
      let propMatch

      while ((propMatch = propRegex.exec(propsContent)) !== null) {
        const [, name, definition] = propMatch
        const type = this.extractType(definition)
        const required = definition.includes('required: true')
        const defaultValue = this.extractDefault(definition)

        props.push({
          name,
          type,
          required,
          default: defaultValue,
          description: this.extractPropDescription(definition)
        })
      }
    }

    return props
  }

  /**
   * 提取组件事件
   */
  extractEmits(content) {
    const emits = []
    const emitsRegex = /emits:\s*\[([^\]]+)\]/
    const match = content.match(emitsRegex)

    if (match) {
      const emitsContent = match[1]
      const eventNames = emitsContent.split(',').map(name => 
        name.trim().replace(/['"`]/g, '')
      )

      eventNames.forEach(name => {
        emits.push({
          name,
          description: `${name} 事件`
        })
      })
    }

    return emits
  }

  /**
   * 提取插槽信息
   */
  extractSlots(content) {
    const slots = []
    const slotRegex = /<slot\s+name=["']([^"']+)["'][^>]*>/g
    let match

    while ((match = slotRegex.exec(content)) !== null) {
      slots.push({
        name: match[1],
        description: `${match[1]} 插槽`
      })
    }

    // 检查默认插槽
    if (content.includes('<slot>') || content.includes('<slot/>')) {
      slots.push({
        name: 'default',
        description: '默认插槽'
      })
    }

    return slots
  }

  /**
   * 提取方法信息
   */
  extractMethods(content) {
    const methods = []
    const methodRegex = /(\w+)\s*\([^)]*\)\s*{/g
    let match

    while ((match = methodRegex.exec(content)) !== null) {
      const methodName = match[1]
      if (!['data', 'computed', 'watch', 'mounted', 'created'].includes(methodName)) {
        methods.push({
          name: methodName,
          description: `${methodName} 方法`
        })
      }
    }

    return methods
  }

  /**
   * 分析API接口
   */
  analyzeAPIs() {
    console.log('🔍 分析API接口...')
    const apis = []
    const apiDir = path.join(this.srcDir, 'api')

    if (fs.existsSync(apiDir)) {
      this.scanDirectory(apiDir, '.js', (filePath) => {
        const content = fs.readFileSync(filePath, 'utf-8')
        const apiInfo = this.parseAPIFile(filePath, content)
        if (apiInfo) {
          apis.push(apiInfo)
        }
      })
    }

    return apis
  }

  /**
   * 解析API文件
   */
  parseAPIFile(filePath, content) {
    const relativePath = path.relative(this.srcDir, filePath)
    const moduleName = path.basename(filePath, '.js')
    const endpoints = []

    // 提取API端点
    const exportRegex = /export\s+(?:const|function)\s+(\w+)\s*[=\(]/g
    let match

    while ((match = exportRegex.exec(content)) !== null) {
      const functionName = match[1]
      const functionContent = this.extractFunction(content, functionName)
      
      if (functionContent) {
        const endpoint = this.parseAPIEndpoint(functionName, functionContent)
        if (endpoint) {
          endpoints.push(endpoint)
        }
      }
    }

    return {
      module: moduleName,
      path: relativePath,
      endpoints,
      description: `${moduleName} API模块`
    }
  }

  /**
   * 解析API端点
   */
  parseAPIEndpoint(functionName, content) {
    const urlMatch = content.match(/['"`]([^'"` ]+)['"`]/)
    const methodMatch = content.match(/method:\s*['"`](\w+)['"`]/)
    
    return {
      name: functionName,
      url: urlMatch ? urlMatch[1] : '',
      method: methodMatch ? methodMatch[1].toUpperCase() : 'GET',
      description: `${functionName} 接口`
    }
  }

  /**
   * 生成组件文档
   */
  generateComponentDocs(components) {
    console.log('📝 生成组件文档...')
    
    components.forEach(component => {
      const markdown = this.generateComponentMarkdown(component)
      const fileName = `${component.name}.md`
      const filePath = path.join(this.outputDir, 'components', fileName)
      
      fs.writeFileSync(filePath, markdown, 'utf-8')
      console.log(`✅ 生成组件文档: ${fileName}`)
    })

    // 生成组件索引
    const indexMarkdown = this.generateComponentIndex(components)
    const indexPath = path.join(this.outputDir, 'components', 'README.md')
    fs.writeFileSync(indexPath, indexMarkdown, 'utf-8')
    console.log('✅ 生成组件索引文档')
  }

  /**
   * 生成单个组件的Markdown文档
   */
  generateComponentMarkdown(component) {
    let markdown = `# ${component.name}

`
    markdown += `${component.description}\n\n`
    markdown += `**文件路径**: \`${component.path}\`\n\n`

    // Props
    if (component.props.length > 0) {
      markdown += '## Props\n\n'
      markdown += '| 属性名 | 类型 | 必填 | 默认值 | 描述 |\n'
      markdown += '|--------|------|------|--------|------|\n'
      
      component.props.forEach(prop => {
        markdown += `| ${prop.name} | ${prop.type} | ${prop.required ? '是' : '否'} | ${prop.default || '-'} | ${prop.description} |\n`
      })
      markdown += '\n'
    }

    // Events
    if (component.emits.length > 0) {
      markdown += '## 事件\n\n'
      markdown += '| 事件名 | 描述 |\n'
      markdown += '|--------|------|\n'
      
      component.emits.forEach(emit => {
        markdown += `| ${emit.name} | ${emit.description} |\n`
      })
      markdown += '\n'
    }

    // Slots
    if (component.slots.length > 0) {
      markdown += '## 插槽\n\n'
      markdown += '| 插槽名 | 描述 |\n'
      markdown += '|--------|------|\n'
      
      component.slots.forEach(slot => {
        markdown += `| ${slot.name} | ${slot.description} |\n`
      })
      markdown += '\n'
    }

    // Methods
    if (component.methods.length > 0) {
      markdown += '## 方法\n\n'
      markdown += '| 方法名 | 描述 |\n'
      markdown += '|--------|------|\n'
      
      component.methods.forEach(method => {
        markdown += `| ${method.name} | ${method.description} |\n`
      })
      markdown += '\n'
    }

    return markdown
  }

  /**
   * 生成API文档
   */
  generateAPIDocs(apis) {
    console.log('📝 生成API文档...')
    
    apis.forEach(api => {
      const markdown = this.generateAPIMarkdown(api)
      const fileName = `${api.module}.md`
      const filePath = path.join(this.outputDir, 'api', fileName)
      
      fs.writeFileSync(filePath, markdown, 'utf-8')
      console.log(`✅ 生成API文档: ${fileName}`)
    })

    // 生成API索引
    const indexMarkdown = this.generateAPIIndex(apis)
    const indexPath = path.join(this.outputDir, 'api', 'README.md')
    fs.writeFileSync(indexPath, indexMarkdown, 'utf-8')
    console.log('✅ 生成API索引文档')
  }

  /**
   * 生成单个API模块的Markdown文档
   */
  generateAPIMarkdown(api) {
    let markdown = `# ${api.module} API\n\n`
    markdown += `${api.description}\n\n`
    markdown += `**文件路径**: \`${api.path}\`\n\n`

    if (api.endpoints.length > 0) {
      markdown += '## 接口列表\n\n'
      markdown += '| 函数名 | 请求方法 | 路径 | 描述 |\n'
      markdown += '|--------|----------|------|------|\n'
      
      api.endpoints.forEach(endpoint => {
        markdown += `| ${endpoint.name} | ${endpoint.method} | ${endpoint.url} | ${endpoint.description} |\n`
      })
      markdown += '\n'
    }

    return markdown
  }

  /**
   * 生成架构文档
   */
  generateArchitectureDocs() {
    console.log('📝 生成架构文档...')
    
    const architectureDoc = this.generateArchitectureMarkdown()
    const filePath = path.join(this.outputDir, 'architecture', 'README.md')
    
    fs.writeFileSync(filePath, architectureDoc, 'utf-8')
    console.log('✅ 生成架构文档')
  }

  /**
   * 生成架构Markdown文档
   */
  generateArchitectureMarkdown() {
    return `# 项目架构文档

## 技术栈

- **前端框架**: Vue 3
- **构建工具**: Vite
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **路由管理**: Vue Router
- **HTTP客户端**: Axios
- **代码规范**: ESLint + Prettier

## 目录结构

\`\`\`
src/
├── api/                    # API接口定义
├── assets/                 # 静态资源
├── components/             # 公共组件
├── composables/            # 组合式API
├── config/                 # 配置文件
├── layouts/                # 布局组件
├── plugins/                # Vue插件
├── router/                 # 路由配置
├── services/               # 业务服务
├── store/                  # 状态管理
├── utils/                  # 工具函数
└── views/                  # 页面组件
\`\`\`

## 核心模块

### API层
- 统一的请求封装
- 错误处理机制
- 请求拦截器和响应拦截器

### 组件层
- 可复用的UI组件
- 业务组件
- 布局组件

### 状态管理
- 用户状态
- 应用配置
- 缓存管理

### 路由管理
- 页面路由
- 权限控制
- 路由守卫

## 数据流

1. 用户操作触发事件
2. 组件调用API接口
3. 请求经过拦截器处理
4. 服务器返回数据
5. 响应经过拦截器处理
6. 更新组件状态
7. 视图重新渲染

## 性能优化

- 组件懒加载
- 路由懒加载
- 图片懒加载
- 虚拟滚动
- 代码分割
- 缓存策略

## 错误处理

- 全局错误捕获
- 错误分类和上报
- 用户友好的错误提示
- 错误恢复机制
`
  }

  /**
   * 生成组件索引
   */
  generateComponentIndex(components) {
    let markdown = '# 组件文档\n\n'
    markdown += '本目录包含所有组件的详细文档。\n\n'
    markdown += '## 组件列表\n\n'
    markdown += '| 组件名 | 文件路径 | 描述 |\n'
    markdown += '|--------|----------|------|\n'
    
    components.forEach(component => {
      markdown += `| [${component.name}](./${component.name}.md) | ${component.path} | ${component.description} |\n`
    })
    
    return markdown
  }

  /**
   * 生成API索引
   */
  generateAPIIndex(apis) {
    let markdown = '# API文档\n\n'
    markdown += '本目录包含所有API模块的详细文档。\n\n'
    markdown += '## API模块列表\n\n'
    markdown += '| 模块名 | 文件路径 | 描述 |\n'
    markdown += '|--------|----------|------|\n'
    
    apis.forEach(api => {
      markdown += `| [${api.module}](./${api.module}.md) | ${api.path} | ${api.description} |\n`
    })
    
    return markdown
  }

  /**
   * 扫描目录
   */
  scanDirectory(dir, extension, callback) {
    if (!fs.existsSync(dir)) return

    const files = fs.readdirSync(dir)
    
    files.forEach(file => {
      const filePath = path.join(dir, file)
      const stat = fs.statSync(filePath)
      
      if (stat.isDirectory()) {
        this.scanDirectory(filePath, extension, callback)
      } else if (file.endsWith(extension)) {
        callback(filePath)
      }
    })
  }

  /**
   * 提取类型信息
   */
  extractType(definition) {
    const typeMatch = definition.match(/type:\s*(\w+)/)
    return typeMatch ? typeMatch[1] : 'Any'
  }

  /**
   * 提取默认值
   */
  extractDefault(definition) {
    const defaultMatch = definition.match(/default:\s*([^,}]+)/)
    return defaultMatch ? defaultMatch[1].trim() : undefined
  }

  /**
   * 提取描述信息
   */
  extractDescription(content) {
    const descMatch = content.match(/\/\*\*\s*\n\s*\*\s*(.+?)\s*\n/)
    return descMatch ? descMatch[1] : '组件描述'
  }

  /**
   * 提取属性描述
   */
  extractPropDescription(definition) {
    const descMatch = definition.match(/\/\/\s*(.+)/)
    return descMatch ? descMatch[1] : '属性描述'
  }

  /**
   * 提取函数内容
   */
  extractFunction(content, functionName) {
    const regex = new RegExp(`(export\\s+(?:const|function)\\s+${functionName}[\\s\\S]*?)(?=export|$)`, 'g')
    const match = content.match(regex)
    return match ? match[0] : null
  }

  /**
   * 运行文档生成
   */
  async run() {
    console.log('🚀 开始生成项目文档...')
    
    try {
      // 初始化目录
      this.initializeDirectories()
      
      // 分析项目
      const components = this.analyzeComponents()
      const apis = this.analyzeAPIs()
      
      // 生成文档
      this.generateComponentDocs(components)
      this.generateAPIDocs(apis)
      this.generateArchitectureDocs()
      
      console.log('\n✅ 文档生成完成!')
      console.log(`📁 文档目录: ${this.outputDir}`)
      console.log(`📊 组件数量: ${components.length}`)
      console.log(`🔌 API模块数量: ${apis.length}`)
      
    } catch (error) {
      console.error('❌ 文档生成失败:', error.message)
      process.exit(1)
    }
  }
}

// 命令行参数处理
const args = process.argv.slice(2)
const command = args[0] || 'generate'

switch (command) {
  case 'generate':
  case 'gen':
    new DocumentationGenerator().run()
    break
    
  case 'clean':
    const generator = new DocumentationGenerator()
    const outputDir = generator.outputDir
    if (fs.existsSync(outputDir)) {
      fs.rmSync(outputDir, { recursive: true, force: true })
      console.log('🧹 清理文档目录完成')
    }
    break
    
  case 'help':
  case '--help':
  case '-h':
    console.log(`
📚 文档生成工具

用法:
  node generate-docs.js [command]

命令:
  generate, gen    生成项目文档 (默认)
  clean           清理生成的文档
  help            显示帮助信息

示例:
  node generate-docs.js
  node generate-docs.js generate
  node generate-docs.js clean
`)
    break
    
  default:
    console.error(`❌ 未知命令: ${command}`)
    console.log('使用 "node generate-docs.js help" 查看帮助信息')
    process.exit(1)
}