/**
 * API组合函数
 * 提供统一的API调用、错误处理、加载状态管理和缓存功能
 * 减少组件中的重复代码，提高代码复用性
 */

import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import aggregatedApiService from '@/services/aggregatedApi'

/**
 * 通用API调用组合函数
 * @param {Object} options - 配置选项
 * @returns {Object} API调用相关的响应式数据和方法
 */
export function useApi(options = {}) {
  const {
    immediate = false,
    showErrorMessage = true,
    showSuccessMessage = false,
    enableRetry = true,
    maxRetries = 3,
    retryDelay = 1000
  } = options

  // 响应式状态
  const loading = ref(false)
  const error = ref(null)
  const data = ref(null)
  const retryCount = ref(0)

  // 统计信息
  const stats = reactive({
    totalRequests: 0,
    successRequests: 0,
    errorRequests: 0,
    averageResponseTime: 0,
    lastRequestTime: null
  })

  // 计算属性
  const isLoading = computed(() => loading.value)
  const hasError = computed(() => error.value !== null)
  const hasData = computed(() => data.value !== null)
  const successRate = computed(() => {
    return stats.totalRequests > 0 ? (stats.successRequests / stats.totalRequests * 100).toFixed(2) : 0
  })

  /**
   * 执行API调用
   * @param {Function} apiCall - API调用函数
   * @param {Object} params - API参数
   * @param {Object} options - 调用选项
   * @returns {Promise} API调用结果
   */
  const execute = async (apiCall, params = {}, callOptions = {}) => {
    const {
      showLoading = true,
      showError = showErrorMessage,
      showSuccess = showSuccessMessage,
      successMessage = '操作成功',
      transform = null,
      validate = null
    } = callOptions

    if (showLoading) {
      loading.value = true
    }
    error.value = null
    
    const startTime = Date.now()
    stats.totalRequests++
    stats.lastRequestTime = new Date()

    try {
      // 参数验证
      if (validate && typeof validate === 'function') {
        const validationResult = validate(params)
        if (validationResult !== true) {
          throw new Error(validationResult || '参数验证失败')
        }
      }

      // 执行API调用
      const result = await apiCall(params)
      
      // 数据转换
      const transformedResult = transform && typeof transform === 'function' 
        ? transform(result) 
        : result
      
      data.value = transformedResult
      stats.successRequests++
      retryCount.value = 0

      // 显示成功消息
      if (showSuccess) {
        ElMessage.success(successMessage)
      }

      return transformedResult
    } catch (err) {
      console.error('API调用失败:', err)
      error.value = err
      stats.errorRequests++

      // 重试逻辑
      if (enableRetry && retryCount.value < maxRetries) {
        retryCount.value++
        console.log(`正在重试 (${retryCount.value}/${maxRetries})...`)
        
        await new Promise(resolve => setTimeout(resolve, retryDelay * retryCount.value))
        return execute(apiCall, params, callOptions)
      }

      // 显示错误消息
      if (showError) {
        const errorMessage = err.response?.data?.message || err.message || '操作失败'
        ElMessage.error(errorMessage)
      }

      throw err
    } finally {
      if (showLoading) {
        loading.value = false
      }
      
      // 更新统计信息
      const endTime = Date.now()
      const responseTime = endTime - startTime
      stats.averageResponseTime = (
        (stats.averageResponseTime * (stats.totalRequests - 1) + responseTime) / stats.totalRequests
      ).toFixed(2)
    }
  }

  /**
   * 重置状态
   */
  const reset = () => {
    loading.value = false
    error.value = null
    data.value = null
    retryCount.value = 0
  }

  /**
   * 重试上次失败的请求
   */
  const retry = async (apiCall, params, callOptions) => {
    retryCount.value = 0
    return execute(apiCall, params, callOptions)
  }

  return {
    // 状态
    loading: readonly(loading),
    error: readonly(error),
    data: readonly(data),
    retryCount: readonly(retryCount),
    stats: readonly(stats),
    
    // 计算属性
    isLoading,
    hasError,
    hasData,
    successRate,
    
    // 方法
    execute,
    reset,
    retry
  }
}

/**
 * 健康记录API组合函数
 * @param {string} customId - 用户自定义ID
 * @returns {Object} 健康记录相关的API调用方法
 */
export function useHealthRecordsApi(customId) {
  const api = useApi({
    showErrorMessage: true,
    enableRetry: true
  })

  /**
   * 获取健康记录
   * @param {Object} filters - 过滤条件
   * @returns {Promise} 健康记录数据
   */
  const getHealthRecords = async (filters = {}) => {
    return api.execute(
      () => aggregatedApiService.getHealthRecords(customId, filters),
      filters,
      {
        transform: (result) => {
          // 数据转换逻辑
          if (result && result.data) {
            return result.data.map(record => ({
              ...record,
              formatted_date: new Date(record.created_at).toLocaleDateString('zh-CN'),
              type_label: getRecordTypeLabel(record.record_type)
            }))
          }
          return []
        },
        validate: (params) => {
          if (!customId) {
            return '用户ID不能为空'
          }
          return true
        }
      }
    )
  }

  /**
   * 刷新健康记录
   */
  const refreshHealthRecords = async (filters = {}) => {
    // 清除缓存
    aggregatedApiService.clearCache(`user_data_${customId}`)
    return getHealthRecords(filters)
  }

  return {
    ...api,
    getHealthRecords,
    refreshHealthRecords
  }
}

/**
 * 问卷API组合函数
 * @param {string} customId - 用户自定义ID
 * @returns {Object} 问卷相关的API调用方法
 */
export function useQuestionnaireApi(customId) {
  const api = useApi({
    showErrorMessage: true,
    enableRetry: true
  })

  /**
   * 获取未完成问卷
   * @param {Object} filters - 过滤条件
   * @returns {Promise} 未完成问卷数据
   */
  const getPendingQuestionnaires = async (filters = {}) => {
    return api.execute(
      () => aggregatedApiService.getQuestionnaireData(customId, 'pending', filters),
      filters,
      {
        transform: (result) => {
          if (result && result.data) {
            return result.data.map(item => ({
              ...item,
              type: 'questionnaire',
              title: item.questionnaire?.title || item.title,
              description: item.questionnaire?.description || item.description
            }))
          }
          return []
        }
      }
    )
  }

  /**
   * 获取已完成问卷
   * @param {Object} filters - 过滤条件
   * @returns {Promise} 已完成问卷数据
   */
  const getCompletedQuestionnaires = async (filters = {}) => {
    return api.execute(
      () => aggregatedApiService.getQuestionnaireData(customId, 'completed', filters),
      filters,
      {
        transform: (result) => {
          if (result && result.data) {
            return result.data.map(item => ({
              ...item,
              type: 'questionnaire',
              title: item.questionnaire_name || item.title
            }))
          }
          return []
        }
      }
    )
  }

  /**
   * 获取未完成评估
   * @param {Object} filters - 过滤条件
   * @returns {Promise} 未完成评估数据
   */
  const getPendingAssessments = async (filters = {}) => {
    return api.execute(
      () => aggregatedApiService.getAssessmentData(customId, 'pending', filters),
      filters,
      {
        transform: (result) => {
          if (result && result.data) {
            return result.data.map(item => ({
              ...item,
              type: 'assessment',
              title: item.assessment?.title || item.title,
              description: item.assessment?.description || item.description
            }))
          }
          return []
        }
      }
    )
  }

  /**
   * 获取已完成评估
   * @param {Object} filters - 过滤条件
   * @returns {Promise} 已完成评估数据
   */
  const getCompletedAssessments = async (filters = {}) => {
    return api.execute(
      () => aggregatedApiService.getAssessmentData(customId, 'completed', filters),
      filters,
      {
        transform: (result) => {
          if (result && result.data) {
            return result.data.map(item => ({
              ...item,
              type: 'assessment',
              title: item.assessment_name || item.title
            }))
          }
          return []
        }
      }
    )
  }

  /**
   * 刷新问卷数据
   */
  const refreshQuestionnaireData = async () => {
    aggregatedApiService.clearCache(`user_data_${customId}`)
    const [pending, completed] = await Promise.all([
      getPendingQuestionnaires(),
      getCompletedQuestionnaires()
    ])
    return { pending, completed }
  }

  /**
   * 刷新评估数据
   */
  const refreshAssessmentData = async () => {
    aggregatedApiService.clearCache(`user_data_${customId}`)
    const [pending, completed] = await Promise.all([
      getPendingAssessments(),
      getCompletedAssessments()
    ])
    return { pending, completed }
  }

  return {
    ...api,
    getPendingQuestionnaires,
    getCompletedQuestionnaires,
    getPendingAssessments,
    getCompletedAssessments,
    refreshQuestionnaireData,
    refreshAssessmentData
  }
}

/**
 * 统计数据API组合函数
 * @param {string} customId - 用户自定义ID
 * @returns {Object} 统计数据相关的API调用方法
 */
export function useStatisticsApi(customId) {
  const api = useApi({
    showErrorMessage: true,
    enableRetry: true
  })

  /**
   * 获取统计数据
   * @param {Array} dataSources - 数据源类型
   * @returns {Promise} 统计数据
   */
  const getStatistics = async (dataSources = null) => {
    return api.execute(
      () => aggregatedApiService.getStatistics(customId, dataSources),
      { dataSources },
      {
        transform: (result) => {
          if (result && result.statistics) {
            return {
              ...result.statistics,
              total_records: result.total_count || 0,
              source_counts: result.source_counts || {},
              last_updated: new Date().toISOString()
            }
          }
          return {}
        }
      }
    )
  }

  return {
    ...api,
    getStatistics
  }
}

/**
 * 确认对话框组合函数
 * @returns {Object} 确认对话框相关方法
 */
export function useConfirmDialog() {
  /**
   * 显示确认对话框
   * @param {string} message - 确认消息
   * @param {string} title - 对话框标题
   * @param {Object} options - 选项
   * @returns {Promise} 确认结果
   */
  const confirm = async (message, title = '确认操作', options = {}) => {
    const {
      confirmButtonText = '确定',
      cancelButtonText = '取消',
      type = 'warning'
    } = options

    try {
      await ElMessageBox.confirm(message, title, {
        confirmButtonText,
        cancelButtonText,
        type
      })
      return true
    } catch {
      return false
    }
  }

  return {
    confirm
  }
}

// 工具函数
function getRecordTypeLabel(recordType) {
  const typeLabels = {
    'health_record': '健康记录',
    'medical_record': '医疗记录',
    'lab_report': '检验报告',
    'examination_report': '检查报告',
    'physical_exam': '体检记录',
    'medication': '用药记录',
    'follow_up': '随访记录',
    'health_diary': '健康日记',
    'other_record': '其他记录'
  }
  return typeLabels[recordType] || recordType
}

// 只读包装函数
function readonly(ref) {
  return computed(() => ref.value)
}