2025-07-31 10:37:07,522 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-31 10:37:07,532 - auth_service - INFO - 统一认证服务初始化完成
2025-07-31 10:37:07,682 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-31 10:37:07,685 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-31 10:37:08,178 - BackendMockDataManager - INFO - 后端模拟数据模式已启用
2025-07-31 10:37:09,465 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\psutil\__init__.py
2025-07-31 10:37:09,468 - fallback_manager - DEBUG - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-07-31 10:37:09,470 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-07-31 10:37:09,573 - health_monitor - INFO - 健康监控器初始化完成
2025-07-31 10:37:09,609 - app.core.system_monitor - INFO - 已加载 288 个历史数据点
2025-07-31 10:37:09,636 - app.core.alert_detector - INFO - 已加载 6 个告警规则
2025-07-31 10:37:09,638 - app.core.alert_detector - INFO - 已加载 0 个当前告警
2025-07-31 10:37:09,664 - app.core.alert_detector - INFO - 已加载 370 个历史告警
2025-07-31 10:37:09,680 - app.core.alert_detector - INFO - 已加载 1 个通知渠道
2025-07-31 10:37:09,687 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-31 10:37:09,695 - alert_manager - INFO - 已初始化默认告警规则
2025-07-31 10:37:09,697 - alert_manager - INFO - 已初始化默认通知渠道
2025-07-31 10:37:09,699 - alert_manager - INFO - 告警管理器初始化完成
