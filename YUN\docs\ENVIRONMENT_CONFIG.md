# 环境配置指南

## 概述

本文档提供了健康管理系统在不同环境（开发、测试、生产）中的配置指南，包括环境变量设置、数据库配置、模拟数据管理等。

## 环境类型

### 开发环境 (Development)

开发环境用于日常开发和调试，应该启用所有调试功能和模拟数据。

#### 后端配置 (.env.development)

```bash
# 基本配置
ENVIRONMENT=development
DEBUG=true
HOST=127.0.0.1
PORT=8000

# 安全配置
SECRET_KEY=dev-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 数据库配置
DATABASE_URL=sqlite:///./health_management_dev.db
DATABASE_TYPE=sqlite

# Redis 配置
REDIS_URL=redis://localhost:6379/0
REDIS_ENABLED=true

# 模拟数据配置
ENABLE_MOCK_DATA=true
MOCK_DATA_ENABLED=true
MOCK_DASHBOARD_ENABLED=true
MOCK_SERVICE_STATS_ENABLED=true
MOCK_HEALTH_MONITOR_ENABLED=true

# 日志配置
LOG_LEVEL=DEBUG
LOG_FILE=logs/app_dev.log

# 性能配置
WORKERS=1
MAX_CONNECTIONS=100

# 安全策略
MAX_LOGIN_ATTEMPTS=10
LOCKOUT_DURATION=300
```

#### 前端配置 (.env.development)

```bash
# API 配置
VUE_APP_API_BASE_URL=http://localhost:8000
VUE_APP_ENVIRONMENT=development

# 调试配置
VUE_APP_DEBUG=true
VUE_APP_LOG_LEVEL=debug

# 模拟数据配置
VUE_APP_ENABLE_MOCK_DATA=true
VUE_APP_MOCK_API_DELAY=500

# 开发工具
VUE_APP_ENABLE_DEVTOOLS=true
```

### 测试环境 (Testing)

测试环境用于集成测试和用户验收测试，应该尽可能接近生产环境。

#### 后端配置 (.env.testing)

```bash
# 基本配置
ENVIRONMENT=testing
DEBUG=false
HOST=0.0.0.0
PORT=8000

# 安全配置
SECRET_KEY=testing-secret-key-different-from-prod
ACCESS_TOKEN_EXPIRE_MINUTES=60

# 数据库配置
DATABASE_URL=postgresql://test_user:test_pass@localhost:5432/health_test
DATABASE_TYPE=postgresql

# Redis 配置
REDIS_URL=redis://localhost:6379/1
REDIS_ENABLED=true

# 模拟数据配置（选择性启用）
ENABLE_MOCK_DATA=true
MOCK_DATA_ENABLED=false  # 使用真实数据测试
MOCK_DASHBOARD_ENABLED=true  # 仪表盘可以使用模拟数据
MOCK_SERVICE_STATS_ENABLED=true
MOCK_HEALTH_MONITOR_ENABLED=false  # 健康监控使用真实数据

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app_test.log

# 性能配置
WORKERS=2
MAX_CONNECTIONS=200

# 安全策略
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=600
```

#### 前端配置 (.env.testing)

```bash
# API 配置
VUE_APP_API_BASE_URL=http://test-api.example.com
VUE_APP_ENVIRONMENT=testing

# 调试配置
VUE_APP_DEBUG=false
VUE_APP_LOG_LEVEL=info

# 模拟数据配置
VUE_APP_ENABLE_MOCK_DATA=false
VUE_APP_MOCK_API_DELAY=0

# 开发工具
VUE_APP_ENABLE_DEVTOOLS=false
```

### 生产环境 (Production)

生产环境是面向最终用户的环境，必须确保安全性、性能和稳定性。

#### 后端配置 (.env.production)

```bash
# 基本配置
ENVIRONMENT=production
DEBUG=false
HOST=0.0.0.0
PORT=8000

# 安全配置（使用强密钥）
SECRET_KEY=${PRODUCTION_SECRET_KEY}  # 从环境变量或密钥管理服务获取
ACCESS_TOKEN_EXPIRE_MINUTES=120

# 数据库配置
DATABASE_URL=${DATABASE_URL}  # 从环境变量获取
DATABASE_TYPE=postgresql

# Redis 配置
REDIS_URL=${REDIS_URL}  # 从环境变量获取
REDIS_ENABLED=true

# 模拟数据配置（生产环境禁用）
ENABLE_MOCK_DATA=false
MOCK_DATA_ENABLED=false
MOCK_DASHBOARD_ENABLED=false
MOCK_SERVICE_STATS_ENABLED=false
MOCK_HEALTH_MONITOR_ENABLED=false

# 日志配置
LOG_LEVEL=WARNING
LOG_FILE=logs/app_prod.log

# 性能配置
WORKERS=4
MAX_CONNECTIONS=1000

# 安全策略
MAX_LOGIN_ATTEMPTS=3
LOCKOUT_DURATION=1800

# 管理员配置（从环境变量获取）
ADMIN_USERNAME=${ADMIN_USERNAME}
ADMIN_PASSWORD=${ADMIN_PASSWORD}
```

#### 前端配置 (.env.production)

```bash
# API 配置
VUE_APP_API_BASE_URL=https://api.yourdomain.com
VUE_APP_ENVIRONMENT=production

# 调试配置（生产环境禁用）
VUE_APP_DEBUG=false
VUE_APP_LOG_LEVEL=error

# 模拟数据配置（生产环境禁用）
VUE_APP_ENABLE_MOCK_DATA=false
VUE_APP_MOCK_API_DELAY=0

# 开发工具（生产环境禁用）
VUE_APP_ENABLE_DEVTOOLS=false
```

## 环境切换

### 使用环境文件

1. **复制对应的环境文件**：
   ```bash
   # 开发环境
   cp .env.development .env
   
   # 测试环境
   cp .env.testing .env
   
   # 生产环境
   cp .env.production .env
   ```

2. **使用脚本自动切换**：
   ```bash
   # 创建环境切换脚本
   ./scripts/switch-env.sh development
   ./scripts/switch-env.sh testing
   ./scripts/switch-env.sh production
   ```

### 容器化环境

#### Docker Compose

```yaml
# docker-compose.yml
version: '3.8'
services:
  backend:
    build: ./backend
    env_file:
      - .env.${ENVIRONMENT:-development}
    ports:
      - "8000:8000"
  
  frontend:
    build: ./frontend
    env_file:
      - .env.${ENVIRONMENT:-development}
    ports:
      - "8080:8080"
```

使用方法：
```bash
# 开发环境
ENVIRONMENT=development docker-compose up

# 测试环境
ENVIRONMENT=testing docker-compose up

# 生产环境
ENVIRONMENT=production docker-compose up
```

#### Kubernetes

```yaml
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config-dev
data:
  ENVIRONMENT: "development"
  DEBUG: "true"
  ENABLE_MOCK_DATA: "true"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config-prod
data:
  ENVIRONMENT: "production"
  DEBUG: "false"
  ENABLE_MOCK_DATA: "false"
```

## 配置验证

### 自动验证脚本

创建配置验证脚本 `scripts/validate-config.py`：

```python
#!/usr/bin/env python3
import os
import sys
from pathlib import Path

def validate_environment_config():
    """验证环境配置"""
    environment = os.getenv('ENVIRONMENT', 'development')
    
    # 必需的环境变量
    required_vars = [
        'SECRET_KEY',
        'DATABASE_URL',
        'REDIS_URL'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ 缺少必需的环境变量: {', '.join(missing_vars)}")
        return False
    
    # 生产环境特殊检查
    if environment == 'production':
        if os.getenv('DEBUG', '').lower() == 'true':
            print("❌ 生产环境不应启用 DEBUG 模式")
            return False
        
        if os.getenv('ENABLE_MOCK_DATA', '').lower() == 'true':
            print("⚠️  警告：生产环境启用了模拟数据")
    
    print(f"✅ 环境配置验证通过 ({environment})")
    return True

if __name__ == '__main__':
    if not validate_environment_config():
        sys.exit(1)
```

### 配置检查清单

#### 开发环境检查清单
- [ ] DEBUG=true
- [ ] ENABLE_MOCK_DATA=true
- [ ] 使用本地数据库
- [ ] 日志级别为 DEBUG
- [ ] 启用开发工具

#### 测试环境检查清单
- [ ] DEBUG=false
- [ ] 使用测试数据库
- [ ] 模拟数据配置合理
- [ ] 日志级别为 INFO
- [ ] 禁用开发工具

#### 生产环境检查清单
- [ ] DEBUG=false
- [ ] ENABLE_MOCK_DATA=false
- [ ] 使用强密钥
- [ ] 使用生产数据库
- [ ] 日志级别为 WARNING 或 ERROR
- [ ] 禁用所有调试功能
- [ ] 配置监控和告警

## 故障排除

### 常见问题

1. **环境变量未加载**
   - 检查 .env 文件是否存在
   - 确认文件权限正确
   - 验证环境变量语法

2. **数据库连接失败**
   - 检查数据库 URL 格式
   - 确认数据库服务运行状态
   - 验证用户权限

3. **模拟数据未生效**
   - 检查 ENABLE_MOCK_DATA 设置
   - 确认具体功能的模拟数据开关
   - 查看应用日志

### 调试命令

```bash
# 检查当前环境变量
env | grep -E "(ENVIRONMENT|DEBUG|MOCK|DATABASE)"

# 验证配置
python scripts/validate-config.py

# 测试数据库连接
python -c "from backend.database import test_connection; test_connection()"

# 检查模拟数据状态
python -c "from backend.mock_data_manager import MockDataManager; print(MockDataManager().is_enabled)"
```

## 相关文档

- [模拟数据使用指南](../MOCK_DATA_GUIDE.md)
- [生产环境模拟数据配置](./PRODUCTION_MOCK_DATA_CONFIG.md)
- [前端数据模式管理](../frontend/MOCK_DATA_GUIDE.md)
- [部署指南](./DEPLOYMENT.md)