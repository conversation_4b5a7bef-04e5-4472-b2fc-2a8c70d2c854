#!/usr/bin/env python3
"""
数据库迁移脚本：为assessments表添加多次评估支持字段
"""

import sqlite3
import os
import sys

def migrate_database():
    """执行数据库迁移"""
    # 可能的数据库路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    backend_dir = os.path.dirname(current_dir)
    
    possible_paths = [
        # 本地开发环境路径
        os.path.join(backend_dir, 'app.db'),
        os.path.join(backend_dir, 'healthapp.db'),
        os.path.join(backend_dir, 'health_app.db'),
        # 生产环境路径
        '/www/wwwroot/healthapp/app.db',
        '/www/wwwroot/healthapp/backend/app.db',
        '/www/wwwroot/healthapp/data/health_app.db',
        '/www/wwwroot/healthapp/backend/health_app.db',
        '/www/wwwroot/healthapp/backend/healthapp.db'
    ]
    
    db_path = None
    for path in possible_paths:
        if os.path.exists(path):
            db_path = path
            print(f"找到数据库文件: {db_path}")
            break
    
    if not db_path:
        print(f"错误：未找到数据库文件，检查的路径: {possible_paths}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查assessments表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='assessments';")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("错误：assessments表不存在")
            return False
        
        # 检查现有字段
        cursor.execute("PRAGMA table_info(assessments);")
        columns = [column[1] for column in cursor.fetchall()]
        print(f"现有字段: {columns}")
        
        # 添加新字段（如果不存在）
        fields_added = []
        
        if 'round_number' not in columns:
            cursor.execute('ALTER TABLE assessments ADD COLUMN round_number INTEGER DEFAULT 1;')
            fields_added.append('round_number')
            print('✓ 添加了 round_number 字段')
        else:
            print('- round_number 字段已存在')
        
        if 'sequence_number' not in columns:
            cursor.execute('ALTER TABLE assessments ADD COLUMN sequence_number INTEGER DEFAULT 1;')
            fields_added.append('sequence_number')
            print('✓ 添加了 sequence_number 字段')
        else:
            print('- sequence_number 字段已存在')
        
        if 'unique_identifier' not in columns:
            cursor.execute('ALTER TABLE assessments ADD COLUMN unique_identifier TEXT;')
            fields_added.append('unique_identifier')
            print('✓ 添加了 unique_identifier 字段')
        else:
            print('- unique_identifier 字段已存在')
        
        # 提交更改
        conn.commit()
        
        if fields_added:
            print(f"\n✅ 数据库迁移完成！添加了字段: {', '.join(fields_added)}")
        else:
            print("\n✅ 所有字段已存在，无需迁移")
        
        return True
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        if 'conn' in locals():
            conn.rollback()
        return False
    
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == '__main__':
    print("开始数据库迁移...")
    success = migrate_database()
    sys.exit(0 if success else 1)