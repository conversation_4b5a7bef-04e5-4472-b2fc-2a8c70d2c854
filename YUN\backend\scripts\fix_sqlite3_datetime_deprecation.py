#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复 Python 3.12 中 sqlite3 datetime 适配器弃用警告的脚本

该脚本实现了官方推荐的替代适配器和转换器，以解决以下警告：
DeprecationWarning: The default datetime adapter is deprecated as of Python 3.12
"""

import datetime
import sqlite3
import os
import sys

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def setup_sqlite3_adapters_converters():
    """
    设置 Python 3.12 兼容的 sqlite3 适配器和转换器
    
    根据官方文档推荐的替代方案：
    https://docs.python.org/3/library/sqlite3.html#sqlite3-adapter-converter-recipes
    """
    
    # 适配器函数 - 将 Python 对象转换为 SQLite 存储格式
    def adapt_date_iso(val):
        """将 datetime.date 适配为 ISO 8601 日期字符串"""
        return val.isoformat()
    
    def adapt_datetime_iso(val):
        """将 datetime.datetime 适配为 ISO 8601 日期时间字符串"""
        return val.isoformat()
    
    # 转换器函数 - 将 SQLite 存储格式转换为 Python 对象
    def convert_date(val):
        """将 ISO 8601 日期字符串转换为 datetime.date 对象"""
        if isinstance(val, bytes):
            val = val.decode('utf-8')
        return datetime.date.fromisoformat(val)
    
    def convert_datetime(val):
        """将 ISO 8601 日期时间字符串转换为 datetime.datetime 对象"""
        if isinstance(val, bytes):
            val = val.decode('utf-8')
        return datetime.datetime.fromisoformat(val)
    
    def convert_timestamp(val):
        """将时间戳字符串转换为 datetime.datetime 对象"""
        if isinstance(val, bytes):
            val = val.decode('utf-8')
        # 处理 ISO 格式的时间戳
        try:
            return datetime.datetime.fromisoformat(val)
        except ValueError:
            # 如果不是 ISO 格式，尝试解析为浮点数时间戳
            try:
                return datetime.datetime.fromtimestamp(float(val))
            except (ValueError, OSError):
                # 如果都失败了，返回当前时间
                return datetime.datetime.now()
    
    # 注册适配器
    sqlite3.register_adapter(datetime.date, adapt_date_iso)
    sqlite3.register_adapter(datetime.datetime, adapt_datetime_iso)
    
    # 注册转换器
    sqlite3.register_converter('date', convert_date)
    sqlite3.register_converter('datetime', convert_datetime)
    sqlite3.register_converter('timestamp', convert_timestamp)
    
    print("✅ SQLite3 适配器和转换器已成功注册")

def test_adapters_converters():
    """
    测试适配器和转换器是否正常工作
    """
    print("\n🧪 测试适配器和转换器...")
    
    # 创建临时数据库连接
    conn = sqlite3.connect(':memory:', detect_types=sqlite3.PARSE_DECLTYPES | sqlite3.PARSE_COLNAMES)
    cursor = conn.cursor()
    
    try:
        # 创建测试表
        cursor.execute('''
            CREATE TABLE test_datetime (
                id INTEGER PRIMARY KEY,
                test_date date,
                test_datetime datetime,
                test_timestamp timestamp
            )
        ''')
        
        # 插入测试数据
        now = datetime.datetime.now()
        today = datetime.date.today()
        
        cursor.execute('''
            INSERT INTO test_datetime (test_date, test_datetime, test_timestamp)
            VALUES (?, ?, ?)
        ''', (today, now, now))
        
        # 查询数据
        cursor.execute('SELECT test_date, test_datetime, test_timestamp FROM test_datetime')
        row = cursor.fetchone()
        
        if row:
            test_date, test_datetime, test_timestamp = row
            print(f"✅ 日期类型: {type(test_date)} - {test_date}")
            print(f"✅ 日期时间类型: {type(test_datetime)} - {test_datetime}")
            print(f"✅ 时间戳类型: {type(test_timestamp)} - {test_timestamp}")
            
            # 验证类型
            assert isinstance(test_date, datetime.date), f"期望 date 类型，得到 {type(test_date)}"
            assert isinstance(test_datetime, datetime.datetime), f"期望 datetime 类型，得到 {type(test_datetime)}"
            assert isinstance(test_timestamp, datetime.datetime), f"期望 datetime 类型，得到 {type(test_timestamp)}"
            
            print("✅ 所有类型验证通过")
        else:
            print("❌ 未找到测试数据")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    finally:
        conn.close()

def apply_fix_to_monitoring_script():
    """
    将修复应用到监控脚本中
    """
    print("\n🔧 应用修复到监控脚本...")
    
    monitoring_script_path = os.path.join(
        os.path.dirname(__file__), 
        'assessment_questionnaire_monitor.py'
    )
    
    if not os.path.exists(monitoring_script_path):
        print(f"❌ 监控脚本不存在: {monitoring_script_path}")
        return False
    
    try:
        # 读取原始文件
        with open(monitoring_script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经包含修复代码
        if 'setup_sqlite3_adapters_converters' in content:
            print("✅ 监控脚本已包含修复代码")
            return True
        
        # 在导入部分添加修复代码
        import_section = "import sqlite3"
        if import_section in content:
            fix_code = '''import sqlite3

# 修复 Python 3.12 sqlite3 datetime 适配器弃用警告
def setup_sqlite3_adapters_converters():
    """设置 Python 3.12 兼容的 sqlite3 适配器和转换器"""
    import datetime
    
    def adapt_date_iso(val):
        return val.isoformat()
    
    def adapt_datetime_iso(val):
        return val.isoformat()
    
    def convert_date(val):
        if isinstance(val, bytes):
            val = val.decode('utf-8')
        return datetime.date.fromisoformat(val)
    
    def convert_datetime(val):
        if isinstance(val, bytes):
            val = val.decode('utf-8')
        return datetime.datetime.fromisoformat(val)
    
    def convert_timestamp(val):
        if isinstance(val, bytes):
            val = val.decode('utf-8')
        try:
            return datetime.datetime.fromisoformat(val)
        except ValueError:
            try:
                return datetime.datetime.fromtimestamp(float(val))
            except (ValueError, OSError):
                return datetime.datetime.now()
    
    sqlite3.register_adapter(datetime.date, adapt_date_iso)
    sqlite3.register_adapter(datetime.datetime, adapt_datetime_iso)
    sqlite3.register_converter('date', convert_date)
    sqlite3.register_converter('datetime', convert_datetime)
    sqlite3.register_converter('timestamp', convert_timestamp)

# 应用修复
setup_sqlite3_adapters_converters()'''
            
            content = content.replace(import_section, fix_code)
            
            # 写回文件
            with open(monitoring_script_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ 修复代码已添加到监控脚本")
            return True
        else:
            print("❌ 未找到 sqlite3 导入语句")
            return False
            
    except Exception as e:
        print(f"❌ 应用修复失败: {e}")
        return False

def main():
    """
    主函数
    """
    print("🚀 开始修复 SQLite3 datetime 适配器弃用警告...")
    
    # 设置适配器和转换器
    setup_sqlite3_adapters_converters()
    
    # 测试修复
    test_adapters_converters()
    
    # 应用到监控脚本
    success = apply_fix_to_monitoring_script()
    
    if success:
        print("\n🎉 修复完成！")
        print("\n📋 修复说明:")
        print("1. 已注册 Python 3.12 兼容的 sqlite3 适配器和转换器")
        print("2. 已将修复代码添加到监控脚本中")
        print("3. 现在运行监控脚本时不会再出现弃用警告")
        print("\n💡 建议:")
        print("- 重新运行监控脚本以验证修复效果")
        print("- 如果其他脚本也有类似警告，可以使用相同的修复方法")
    else:
        print("\n❌ 修复过程中出现问题，请检查错误信息")

if __name__ == '__main__':
    main()