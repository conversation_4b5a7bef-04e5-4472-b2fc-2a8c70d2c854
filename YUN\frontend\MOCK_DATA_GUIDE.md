# 前端模拟数据管理指南

## 概述

本指南介绍如何在前端项目中使用和管理模拟数据，配合后端的统一模拟数据管理系统，为开发和测试提供一致的数据环境。

## 数据模式管理

### 数据模式切换组件

位置：`src/components/common/DataModeSwitch.vue`

这是一个Vue组件，提供以下功能：
- 显示当前数据模式状态（模拟数据/真实数据）
- 允许用户切换数据模式
- 输入切换原因和备注
- 测试当前模式的连接状态
- 查看数据模式切换历史

### 数据模式管理页面

位置：`src/views/DataModeManagement.vue`

完整的数据模式管理界面，包含：
- 系统状态概览
- 数据模式切换控制
- 影响分析和快速操作
- 实时监控和历史记录

## API 接口

### 数据模式 API

位置：`src/api/dataMode.js`

提供以下接口函数：

```javascript
// 获取当前数据模式状态
getDataModeStatus()

// 切换数据模式
switchDataMode(mode, reason)

// 测试当前数据模式
testDataMode()

// 获取数据模式切换历史
getDataModeSwitchHistory()
```

## 使用方式

### 在组件中使用数据模式检查

```javascript
import { getDataModeStatus } from '@/api/dataMode'

export default {
  async mounted() {
    try {
      const status = await getDataModeStatus()
      if (status.mode === 'mock') {
        console.log('当前使用模拟数据模式')
        this.showMockDataIndicator = true
      }
    } catch (error) {
      console.error('获取数据模式状态失败:', error)
    }
  }
}
```

### 在开发环境中的配置

在 `.env.development` 文件中配置：

```env
# API 基础地址
VITE_LOCAL_API_URL=http://localhost:8006/api
VITE_PUBLIC_API_URL=http://localhost:8006/api

# 开发模式标识
VITE_APP_ENV=development
```

## 路由配置

数据模式管理页面已添加到系统管理路由中：

```javascript
{
  path: '/admin/data-mode-management',
  name: 'DataModeManagement',
  component: () => import('@/views/DataModeManagement.vue'),
  meta: {
    title: '数据模式管理',
    requiresAuth: true,
    roles: ['admin']
  }
}
```

## 导航菜单

在系统管理子菜单中添加了数据模式管理入口：

```vue
<el-menu-item index="/admin/data-mode-management">
  <el-icon><Switch /></el-icon>
  <span>数据模式管理</span>
</el-menu-item>
```

## 最佳实践

### 1. 开发环境使用

- 在开发环境中默认启用模拟数据模式
- 使用数据模式切换功能快速测试不同场景
- 定期切换到真实数据模式验证接口兼容性

### 2. 测试环境配置

- 根据测试需求灵活配置数据模式
- 使用切换历史功能追踪测试过程
- 在自动化测试中考虑数据模式的影响

### 3. 生产环境注意事项

- 生产环境应禁用模拟数据模式
- 确保 `ENABLE_MOCK_DATA` 环境变量设置为 `false`
- 移除或隐藏数据模式管理相关的UI组件

## 故障排除

### 常见问题

1. **数据模式切换失败**
   - 检查后端服务是否正常运行
   - 确认用户权限是否足够
   - 查看浏览器控制台错误信息

2. **模拟数据不生效**
   - 检查后端 `ENABLE_MOCK_DATA` 环境变量
   - 确认相关的子开关是否启用
   - 重启后端服务使配置生效

3. **界面显示异常**
   - 检查前端路由配置是否正确
   - 确认组件导入路径是否正确
   - 验证用户角色权限设置

### 调试技巧

1. **使用浏览器开发者工具**
   - 查看网络请求和响应
   - 检查控制台错误信息
   - 使用Vue DevTools调试组件状态

2. **API 测试**
   - 使用Postman或类似工具测试API接口
   - 验证请求参数和响应格式
   - 检查认证token是否有效

## 环境配置示例

### 开发环境 (.env.development)

```env
VITE_LOCAL_API_URL=http://localhost:8006/api
VITE_PUBLIC_API_URL=http://localhost:8006/api
VITE_APP_ENV=development
VITE_ENABLE_MOCK_DATA_UI=true
```

### 生产环境 (.env.production)

```env
VITE_LOCAL_API_URL=https://api.yourdomain.com/api
VITE_PUBLIC_API_URL=https://api.yourdomain.com/api
VITE_APP_ENV=production
VITE_ENABLE_MOCK_DATA_UI=false
```

## 相关文档

- [后端模拟数据管理指南](../backend/docs/MOCK_DATA_GUIDE.md)
- [API 文档](../backend/API_DOCUMENTATION.md)
- [系统架构文档](../系统架构与功能实现文档.md)

## 总结

前端模拟数据管理系统与后端紧密配合，提供了完整的数据模式管理解决方案。通过统一的界面和API，开发者可以轻松地在不同数据模式之间切换，提高开发效率和测试质量。

在使用过程中，请遵循最佳实践，确保在不同环境中正确配置数据模式，避免在生产环境中意外启用模拟数据。