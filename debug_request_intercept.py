#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'mobile'))

import requests
import json
from unittest.mock import patch

def intercept_requests():
    """拦截并记录所有HTTP请求"""
    original_request = requests.request
    
    def logged_request(*args, **kwargs):
        print(f"\n=== HTTP请求拦截 ===")
        print(f"方法: {kwargs.get('method', args[0] if args else 'Unknown')}")
        print(f"URL: {kwargs.get('url', args[1] if len(args) > 1 else 'Unknown')}")
        print(f"Headers: {kwargs.get('headers', {})}")
        print(f"Data: {kwargs.get('data')}")
        print(f"JSON: {kwargs.get('json')}")
        print(f"Params: {kwargs.get('params')}")
        print(f"Proxies: {kwargs.get('proxies')}")
        print(f"========================")
        
        # 调用原始请求
        response = original_request(*args, **kwargs)
        
        print(f"\n=== HTTP响应 ===")
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        print(f"==================")
        
        return response
    
    return logged_request

def test_with_interception():
    """使用请求拦截测试移动端登录"""
    print("=== 开始请求拦截测试 ===")
    
    # 设置代理环境变量
    proxy_vars_to_remove = [
        'HTTP_PROXY', 'HTTPS_PROXY', 'FTP_PROXY', 'SOCKS_PROXY',
        'http_proxy', 'https_proxy', 'ftp_proxy', 'socks_proxy',
        'ALL_PROXY', 'all_proxy'
    ]
    
    for proxy_var in proxy_vars_to_remove:
        os.environ.pop(proxy_var, None)
    
    # 强制设置NO_PROXY环境变量
    os.environ['NO_PROXY'] = '*,localhost,127.0.0.1,************'
    os.environ['no_proxy'] = '*,localhost,127.0.0.1,************'
    
    # 拦截requests
    with patch('requests.request', side_effect=intercept_requests()):
        from utils.cloud_api import get_cloud_api
        
        # 获取API实例
        api = get_cloud_api()
        print(f"当前使用的服务器: {api.base_url}")
        
        # 测试登录
        print("\n发送登录请求...")
        result = api.authenticate(username="admin", password="admin123")
        
        print(f"\n最终登录结果: {result}")

def test_manual_request():
    """手动构造请求进行测试"""
    print("\n=== 手动构造请求测试 ===")
    
    # 清理代理设置
    proxy_vars_to_remove = [
        'HTTP_PROXY', 'HTTPS_PROXY', 'FTP_PROXY', 'SOCKS_PROXY',
        'http_proxy', 'https_proxy', 'ftp_proxy', 'socks_proxy',
        'ALL_PROXY', 'all_proxy'
    ]
    
    for proxy_var in proxy_vars_to_remove:
        os.environ.pop(proxy_var, None)
    
    # 强制设置NO_PROXY环境变量
    os.environ['NO_PROXY'] = '*,localhost,127.0.0.1,************'
    os.environ['no_proxy'] = '*,localhost,127.0.0.1,************'
    
    # 测试数据
    auth_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    # 模拟移动端API的请求参数
    request_kwargs = {
        'method': 'POST',
        'url': 'http://************/api/direct-login',
        'params': None,
        'data': None,
        'json': auth_data,
        'headers': {},
        'files': None,
        'proxies': {
            'http': None,
            'https': None,
            'ftp': None,
            'socks4': None,
            'socks5': None
        },
        'timeout': 30
    }
    
    print(f"请求参数: {request_kwargs}")
    
    try:
        response = requests.request(**request_kwargs)
        print(f"\n响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"\n解析后的JSON: {result}")
                
                if result.get('access_token'):
                    print("✓ 手动请求登录成功")
                else:
                    print("✗ 手动请求登录失败")
                    print(f"错误: {result.get('error', '未知错误')}")
            except json.JSONDecodeError as e:
                print(f"JSON解析失败: {e}")
        else:
            print("✗ HTTP请求失败")
            
    except Exception as e:
        print(f"请求异常: {e}")

if __name__ == "__main__":
    test_manual_request()
    test_with_interception()