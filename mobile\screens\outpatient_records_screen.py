"""门诊资料管理屏幕模块

提供门诊记录的查看、添加和管理功能。
"""

from kivymd.app import MDApp
from kivy.logger import Logger as KivyLogger
from kivy.metrics import dp
from kivy.properties import StringProperty, ListProperty
from .base_screen import BaseScreen
from widgets.logo import HealthLogo
from kivy.clock import Clock
from kivy.lang import Builder
from kivy.core.window import Window
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDIconButton, MDButtonText, MDButton
from kivymd.uix.label import MDLabel
from kivymd.uix.card import MDCard
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.list import MDList, MDListItem
from kivymd.uix.divider import MDDivider
from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText
from theme import AppTheme, AppMetrics, FontStyles
from kivymd.uix.dialog import MDDialog
from kivymd.uix.menu import MDDropdownMenu
from kivymd.uix.textfield import MDTextField
from kivy.factory import Factory
import datetime
import uuid

KV = '''
<OutpatientRecordCard>:
    orientation: 'vertical'
    size_hint_y: None
    height: dp(100)
    md_bg_color: app.theme.CARD_BACKGROUND
    radius: [dp(12)]
    elevation: 2
    padding: [dp(16), dp(8), dp(16), dp(8)]
    MDLabel:
        text: root.title
        font_size: app.font_styles.TITLE_MEDIUM['font_size']
        font_name: app.font_styles.TITLE_MEDIUM['font_name']
        bold: app.font_styles.TITLE_MEDIUM['bold']
        theme_text_color: "Custom"
        text_color: app.theme.TEXT_PRIMARY
        size_hint_y: None
        height: self.texture_size[1]
    MDLabel:
        text: root.date
        font_size: app.font_styles.BODY_SMALL['font_size']
        font_name: app.font_styles.BODY_SMALL['font_name']
        theme_text_color: "Custom"
        text_color: app.theme.TEXT_SECONDARY
        size_hint_y: None
        height: self.texture_size[1]
    MDLabel:
        text: root.summary
        font_size: app.font_styles.BODY_SMALL['font_size']
        font_name: app.font_styles.BODY_SMALL['font_name']
        theme_text_color: "Custom"
        text_color: app.theme.TEXT_SECONDARY
        size_hint_y: None
        height: self.texture_size[1]

<OutpatientRecordsScreen>:
    md_bg_color: app.theme.BACKGROUND_COLOR
    
    MDBoxLayout:
        orientation: "vertical"
        spacing: dp(8)
        
        # 顶部应用栏
        MDBoxLayout:
            id: app_bar
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(56)
            md_bg_color: app.theme.PRIMARY_COLOR
            padding: [dp(4), dp(0), dp(4), dp(0)]
            
            MDIconButton:
                icon: "arrow-left"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.go_back()
            
            MDLabel:
                text: "门诊资料管理"
                font_size: app.font_styles.TITLE_LARGE['font_size']
                font_name: app.font_styles.TITLE_LARGE['font_name']
                bold: app.font_styles.TITLE_LARGE['bold']
                theme_text_color: "Custom"
                text_color: app.theme.TEXT_LIGHT
                halign: "center"
                valign: "center"
            
            MDIconButton:
                icon: "plus"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.add_record()
        
        # 添加Logo组件
        HealthLogo:
            id: health_logo
            size_hint_y: None
            height: dp(120)
            logo_size: dp(80), dp(80)
            title_font_size: dp(18)
            subtitle_font_size: dp(14)
            
        MDScrollView:
            do_scroll_x: False
            do_scroll_y: True
            
            MDBoxLayout:
                id: records_container
                orientation: 'vertical'
                size_hint_y: None
                height: self.minimum_height
                padding: [dp(16), dp(16), dp(16), dp(16)]
                spacing: dp(12)
'''

class OutpatientRecordCard(MDCard):
    title = StringProperty("")
    date = StringProperty("")
    summary = StringProperty("")
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.ripple_behavior = True  # 启用涟漪效果
        self.ripple_duration_in_slow = 0.1  # 加快涟漪动画
        self.ripple_color = (0.8, 0.8, 0.8, 0.5)  # 设置涟漪颜色

class OutpatientRecordsScreen(BaseScreen):
    records = ListProperty([])
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.app = MDApp.get_running_app()
        self.records = []
        Clock.schedule_once(self.init_ui, 0.2)

    def init_ui(self, dt=0):
        self.load_records()

    def on_enter(self):
        super().on_enter()
        self.init_ui()

    def load_records(self):
        try:
            custom_id = getattr(self.app, 'user_data', {}).get('custom_id', None)
            if not custom_id:
                KivyLogger.warning("OutpatientRecordsScreen: 未获取到用户ID")
                # 尝试从app获取用户数据
                app = MDApp.get_running_app()
                custom_id = getattr(app, 'user_data', {}).get('custom_id', None)
                if not custom_id:
                    KivyLogger.warning("OutpatientRecordsScreen: 未获取到用户ID")
                    # 不显示错误，继续加载示例数据
            
            # 检查控件是否存在
            if not hasattr(self, 'ids'):
                return
            if 'records_container' not in self.ids or not self.ids.records_container:
                return
            
            # 清空现有记录
            try:
                self.ids.records_container.clear_widgets()
            except ReferenceError:
                return
            
            # 合并本地记录和示例数据
            all_records = []
            
            # 添加本地保存的记录
            if hasattr(self, 'records') and self.records:
                all_records.extend(self.records)
            
            # 添加示例数据（如果没有本地记录或需要演示）
            sample_records = [
                {
                    "id": "OP001",
                    "title": "内科门诊",
                    "date": "2024-01-15",
                    "hospital": "市人民医院",
                    "department": "内科",
                    "doctor": "张医生",
                    "chief_complaint": "头痛、发热",
                    "diagnosis": "上呼吸道感染",
                    "treatment": "对症治疗，多休息",
                    "prescription": "阿莫西林胶囊、布洛芬片",
                    "summary": "主诉：头痛、发热2天。诊断：上呼吸道感染。",
                    "original_doc": "outpatient_001.pdf",
                    "structured_doc": "outpatient_001_structured.json"
                },
                {
                    "id": "OP002",
                    "title": "皮肤科门诊",
                    "date": "2024-01-10",
                    "hospital": "中医院",
                    "department": "皮肤科",
                    "doctor": "李医生",
                    "chief_complaint": "皮疹、瘙痒",
                    "diagnosis": "湿疹",
                    "treatment": "外用药膏，避免刺激",
                    "prescription": "炉甘石洗剂、氢化可的松软膏",
                    "summary": "主诉：皮疹、瘙痒1周。诊断：湿疹。",
                    "original_doc": "outpatient_002.pdf",
                    "structured_doc": "outpatient_002_structured.json"
                },
                {
                    "id": "OP003",
                    "title": "眼科门诊",
                    "date": "2024-01-05",
                    "hospital": "眼科医院",
                    "department": "眼科",
                    "doctor": "王医生",
                    "chief_complaint": "视力下降",
                    "diagnosis": "近视",
                    "treatment": "配镜矫正",
                    "prescription": "无",
                    "summary": "主诉：视力下降3个月。诊断：近视。",
                    "original_doc": "outpatient_003.pdf",
                    "structured_doc": "outpatient_003_structured.json"
                }
            ]
            
            # 如果没有本地记录，使用示例数据
            if not all_records:
                all_records = sample_records
            
            # 根据记录列表动态创建卡片
            for record in all_records:
                card = OutpatientRecordCard(
                    title=record["title"],
                    date=record["date"],
                    summary=record["summary"]
                )
                # 绑定点击事件
                card.bind(on_release=lambda x, r=record: self.show_record_detail(r))
                self.ids.records_container.add_widget(card)
            
            # 如果没有记录，显示提示
            if not all_records:
                empty_label = MDLabel(
                    text="暂无门诊记录\n点击右上角 + 号添加记录",
                    halign="center",
                    theme_text_color="Secondary",
                    font_size=app.font_styles.BODY_MEDIUM['font_size'],
                    font_name=app.font_styles.BODY_MEDIUM['font_name'],
                    bold=app.font_styles.BODY_MEDIUM['bold']
                )
                self.ids.records_container.add_widget(empty_label)
                
        except Exception as e:
            KivyLogger.error(f"加载门诊记录时出错: {e}")

    def go_back(self):
        """返回上一页"""
        try:
            # 直接返回主页，避免调用不存在的super().go_back()
            app = MDApp.get_running_app()
            app.root.current = 'homepage_screen'
        except Exception as e:
            KivyLogger.error(f"OutpatientRecordsScreen: 返回失败: {e}")
            app = MDApp.get_running_app()
            app.root.current = 'homepage_screen'

    def add_record(self):
        """添加门诊记录"""
        try:
            self.show_upload_dialog()
        except Exception as e:
            KivyLogger.error(f"OutpatientRecordsScreen: 添加记录失败: {e}")
            self.show_error(f"添加记录失败: {str(e)}")
    
    def show_upload_dialog(self):
        """显示上传对话框"""
        
        # 创建对话框内容
        content = MDBoxLayout(
            orientation='vertical',
            spacing=dp(16),
            size_hint_y=None,
            height=dp(400),
            padding=[dp(16), dp(16), dp(16), dp(16)]
        )
        
        # 记录标题输入
        self.title_field = MDTextField(
            hint_text="门诊记录标题",
            text="",
            size_hint_y=None,
            height=dp(56)
        )
        content.add_widget(self.title_field)
        
        # 分类选择
        category_layout = MDBoxLayout(
            orientation='horizontal',
            spacing=dp(8),
            size_hint_y=None,
            height=dp(56)
        )
        
        category_label = MDLabel(
            text="分类:",
            size_hint_x=None,
            width=dp(60),
            theme_text_color="Primary",
            font_size=app.font_styles.BODY_SMALL['font_size'],
            font_name: app.font_styles.BODY_SMALL['font_name'],
            bold=app.font_styles.BODY_SMALL['bold']
        )
        category_layout.add_widget(category_label)
        
        self.category_button = MDButton(
            MDButtonText(text="选择分类"),
            style="outlined",
            size_hint_x=1,
            on_release=self.show_category_menu
        )
        category_layout.add_widget(self.category_button)
        content.add_widget(category_layout)
        
        # 医院输入
        self.hospital_field = MDTextField(
            hint_text="医院名称",
            text="",
            size_hint_y=None,
            height=dp(56)
        )
        content.add_widget(self.hospital_field)
        
        # 科室输入
        self.department_field = MDTextField(
            hint_text="科室",
            text="",
            size_hint_y=None,
            height=dp(56)
        )
        content.add_widget(self.department_field)
        
        # 日期输入
        self.date_field = MDTextField(
            hint_text="就诊日期 (YYYY-MM-DD)",
            text=datetime.date.today().strftime("%Y-%m-%d"),
            size_hint_y=None,
            height=dp(56)
        )
        content.add_widget(self.date_field)
        
        # 上传方式按钮
        upload_label = MDLabel(
            text="选择上传方式:",
            size_hint_y=None,
            height=dp(32),
            theme_text_color="Primary",
            font_size=app.font_styles.BODY_SMALL['font_size'],
            font_name: app.font_styles.BODY_SMALL['font_name'],
            bold=app.font_styles.BODY_SMALL['bold']
        )
        content.add_widget(upload_label)
        
        upload_buttons = MDBoxLayout(
            orientation='horizontal',
            spacing=dp(8),
            size_hint_y=None,
            height=dp(48)
        )
        
        direct_btn = MDButton(
            MDButtonText(text="直接上传"),
            style="filled",
            size_hint_x=1,
            on_release=self.direct_upload
        )
        upload_buttons.add_widget(direct_btn)
        
        qr_btn = MDButton(
            MDButtonText(text="二维码上传"),
            style="outlined",
            size_hint_x=1,
            on_release=self.qr_upload
        )
        upload_buttons.add_widget(qr_btn)
        
        photo_btn = MDButton(
            MDButtonText(text="拍照上传"),
            style="outlined",
            size_hint_x=1,
            on_release=self.photo_upload
        )
        upload_buttons.add_widget(photo_btn)
        
        content.add_widget(upload_buttons)
        
        # 创建对话框
        self.upload_dialog = MDDialog(
            title="添加门诊记录",
            content=content,
            buttons=[
                MDButton(
                    MDButtonText(text="取消"),
                    style="text",
                    on_release=lambda x: self.upload_dialog.dismiss()
                ),
                MDButton(
                    MDButtonText(text="保存"),
                    style="filled",
                    on_release=self.save_record
                )
            ]
        )
        
        self.selected_category = "内科"  # 默认分类
        self.upload_dialog.open()
    
    def show_category_menu(self, button):
        """显示分类菜单"""
        categories = [
            "内科", "外科", "妇科", "儿科", "眼科", 
            "耳鼻喉科", "皮肤科", "神经科", "心理科", "其他"
        ]
        
        menu_items = []
        for category in categories:
            menu_items.append({
                "text": category,
                "on_release": lambda x=category: self.select_category(x)
            })
        
        self.category_menu = MDDropdownMenu(
            caller=button,
            items=menu_items,
            width_mult=4
        )
        self.category_menu.open()
    
    def select_category(self, category):
        """选择分类"""
        self.selected_category = category
        self.category_button.children[0].text = category
        self.category_menu.dismiss()
    
    def direct_upload(self, button):
        """直接上传文件"""
        try:
            # 这里应该实现文件选择和上传逻辑
            # 暂时显示提示信息
            self.show_info("请选择要上传的门诊记录文件")
            # TODO: 实现文件选择器
            # from plyer import filechooser
            # filechooser.open_file(on_selection=self.handle_file_selection)
        except Exception as e:
            KivyLogger.error(f"直接上传失败: {e}")
            self.show_error("上传功能暂时不可用")
    
    def qr_upload(self, button):
        """二维码上传"""
        try:
            # 这里应该实现二维码扫描逻辑
            self.show_info("请扫描门诊记录二维码")
            # TODO: 实现二维码扫描
            # 可以使用zbarcam或其他二维码库
        except Exception as e:
            KivyLogger.error(f"二维码上传失败: {e}")
            self.show_error("二维码扫描功能暂时不可用")
    
    def photo_upload(self, button):
        """拍照上传"""
        try:
            # 这里应该实现相机拍照逻辑
            self.show_info("请拍摄门诊记录照片")
            # TODO: 实现相机功能
            # from plyer import camera
            # camera.take_picture(filename='temp_photo.jpg', on_complete=self.handle_photo)
        except Exception as e:
            KivyLogger.error(f"拍照上传失败: {e}")
            self.show_error("拍照功能暂时不可用")
    
    def save_record(self, button):
        """保存记录"""
        try:
            # 验证输入
            title = self.title_field.text.strip()
            hospital = self.hospital_field.text.strip()
            department = self.department_field.text.strip()
            date = self.date_field.text.strip()
            
            if not title:
                self.show_error("请输入记录标题")
                return
            
            if not hospital:
                self.show_error("请输入医院名称")
                return
            
            if not department:
                self.show_error("请输入科室")
                return
            
            if not date:
                self.show_error("请输入就诊日期")
                return
            
            # 生成记录数据
            record_data = {
                "id": f"OP{str(uuid.uuid4())[:8].upper()}",
                "title": title,
                "date": date,
                "hospital": hospital,
                "department": department,
                "category": getattr(self, 'selected_category', '内科'),
                "doctor": "",
                "chief_complaint": "",
                "diagnosis": "",
                "treatment": "",
                "prescription": "",
                "summary": f"门诊记录 - {title}",
                "original_doc": "",
                "structured_doc": ""
            }
            
            # 调用API保存记录
            self.save_record_to_api(record_data)
            
        except Exception as e:
            KivyLogger.error(f"保存门诊记录失败: {e}")
            self.show_error(f"保存失败: {str(e)}")
    
    def save_record_to_api(self, record_data):
        """保存记录到API"""
        try:
            # TODO: 实现API调用
            # 暂时添加到本地列表
            if not hasattr(self, 'records'):
                self.records = []
            self.records.append(record_data)
            
            # 关闭对话框
            self.upload_dialog.dismiss()
            
            # 刷新记录列表
            self.load_records()
            
            self.show_info("门诊记录添加成功")
            
        except Exception as e:
            KivyLogger.error(f"保存门诊记录失败: {e}")
            self.show_error(f"保存失败: {str(e)}")

    def show_info(self, message):
        """显示信息提示"""
        try:
            app = MDApp.get_running_app()
            if hasattr(app, 'show_notification'):
                app.show_notification(message)
            else:
                snackbar = MDSnackbar(
                    MDSnackbarText(
                        text=message,
                    ),
                    pos_hint={"center_x": 0.5},
                    duration=2,
                )
                snackbar.open()
        except Exception as e:
            KivyLogger.error(f"OutpatientRecordsScreen: 显示信息失败: {e}")
    
    def show_error(self, message):
        """显示错误提示"""
        try:
            app = MDApp.get_running_app()
            if hasattr(app, 'show_error'):
                app.show_error(message)
            else:
                self.show_info(f"错误: {message}")
        except Exception as e:
            KivyLogger.error(f"OutpatientRecordsScreen: 显示错误失败: {e}")

    def show_record_detail(self, record_data):
        """显示门诊记录详情"""
        try:
            # 创建详情内容
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(12),
                size_hint_y=None,
                height=dp(400),
                padding=[dp(16), dp(16), dp(16), dp(16)]
            )
            
            # 添加详细信息
            details = [
                ("记录ID", record_data.get("id", "未知")),
                ("标题", record_data.get("title", "未知")),
                ("就诊日期", record_data.get("date", "未知")),
                ("医院", record_data.get("hospital", "未知")),
                ("科室", record_data.get("department", "未知")),
                ("医生", record_data.get("doctor", "未填写")),
                ("主诉", record_data.get("chief_complaint", "未填写")),
                ("诊断", record_data.get("diagnosis", "未填写")),
                ("治疗方案", record_data.get("treatment", "未填写")),
                ("处方", record_data.get("prescription", "未填写"))
            ]
            
            for label, value in details:
                detail_layout = MDBoxLayout(
                    orientation='horizontal',
                    spacing=dp(8),
                    size_hint_y=None,
                    height=dp(32)
                )
                
                label_widget = MDLabel(
                    text=f"{label}:",
                    size_hint_x=None,
                    width=dp(80),
                    font_size=app.font_styles.BODY_MEDIUM['font_size'],
                    font_name: app.font_styles.BODY_MEDIUM['font_name'],
                    theme_text_color="Primary",
                    bold=app.font_styles.BODY_MEDIUM['bold']
                )
                detail_layout.add_widget(label_widget)
                
                value_widget = MDLabel(
                    text=str(value),
                    font_size=app.font_styles.BODY_MEDIUM['font_size'],
                    font_name: app.font_styles.BODY_MEDIUM['font_name'],
                    theme_text_color="Secondary",
                    text_size=(dp(200), None)
                )
                detail_layout.add_widget(value_widget)
                
                content.add_widget(detail_layout)
            
            # 创建对话框
            dialog = MDDialog(
                title="门诊记录详情",
                content_cls=content,
                buttons=[
                    MDButton(
                        MDButtonText(text="编辑"),
                        style="outlined",
                        on_release=lambda x: self.edit_record(record_data, dialog)
                    ),
                    MDButton(
                        MDButtonText(text="删除"),
                        style="outlined",
                        on_release=lambda x: self.delete_record(record_data, dialog)
                    ),
                    MDButton(
                        MDButtonText(text="关闭"),
                        style="text",
                        on_release=lambda x: dialog.dismiss()
                    )
                ]
            )
            dialog.open()
            
        except Exception as e:
            KivyLogger.error(f"显示门诊记录详情失败: {e}")
            self.show_error("无法显示记录详情")
    
    def edit_record(self, record_data, dialog):
        """编辑门诊记录"""
        try:
            dialog.dismiss()
            # TODO: 实现编辑功能
            self.show_info("编辑功能开发中")
        except Exception as e:
            KivyLogger.error(f"编辑门诊记录失败: {e}")
            self.show_error("编辑功能暂时不可用")
    
    def delete_record(self, record_data, dialog):
        """删除门诊记录"""
        try:
            dialog.dismiss()
            
            # 创建确认对话框
            confirm_dialog = MDDialog(
                title="确认删除",
                text=f"确定要删除门诊记录 '{record_data.get('title', '未知')}' 吗？",
                buttons=[
                    MDButton(
                        MDButtonText(text="取消"),
                        style="text",
                        on_release=lambda x: confirm_dialog.dismiss()
                    ),
                    MDButton(
                        MDButtonText(text="删除"),
                        style="filled",
                        on_release=lambda x: self.confirm_delete_record(record_data, confirm_dialog)
                    )
                ]
            )
            confirm_dialog.open()
            
        except Exception as e:
            KivyLogger.error(f"删除门诊记录失败: {e}")
            self.show_error("删除功能暂时不可用")
    
    def confirm_delete_record(self, record_data, dialog):
        """确认删除门诊记录"""
        try:
            dialog.dismiss()
            
            # TODO: 调用API删除记录
            # 暂时从本地列表删除
            if hasattr(self, 'records'):
                self.records = [r for r in self.records if r.get('id') != record_data.get('id')]
            
            # 刷新记录列表
            self.load_records()
            
            self.show_info("门诊记录已删除")
            
        except Exception as e:
            KivyLogger.error(f"确认删除门诊记录失败: {e}")
            self.show_error("删除失败")

# 在类定义后注册Factory
Factory.register('OutpatientRecordsScreen', cls=OutpatientRecordsScreen)
Builder.load_string(KV)