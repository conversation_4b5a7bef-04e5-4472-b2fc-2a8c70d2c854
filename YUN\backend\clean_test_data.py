#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理测试数据脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from app.db.base_session import SessionLocal
from app.models.assessment import Assessment
from app.models.questionnaire import Questionnaire

def clean_test_data():
    """清理测试数据"""
    db: Session = SessionLocal()
    
    try:
        # 删除评估记录
        assessment_count = db.query(Assessment).filter(Assessment.custom_id == "SM_001").count()
        db.query(Assessment).filter(Assessment.custom_id == "SM_001").delete()
        print(f"删除了 {assessment_count} 条评估记录")
        
        # 删除问卷记录
        questionnaire_count = db.query(Questionnaire).filter(Questionnaire.custom_id == "SM_001").count()
        db.query(Questionnaire).filter(Questionnaire.custom_id == "SM_001").delete()
        print(f"删除了 {questionnaire_count} 条问卷记录")
        
        db.commit()
        print("清理完成")
        
    except Exception as e:
        print(f"清理数据时出错: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    clean_test_data()