<template>
  <div class="medical-records-tab">
    <div class="tab-header">
      <h3>就诊记录</h3>
      <div class="filter-container">
        <el-select v-model="filterType" placeholder="记录类型" clearable @change="filterRecords">
          <el-option label="全部" value="" />
          <el-option label="门诊记录" value="outpatient" />
          <el-option label="入院记录" value="admission" />
          <el-option label="出院小结" value="discharge" />
          <el-option label="手术记录" value="surgery" />
          <el-option label="其他记录" value="other" />
        </el-select>
        <el-button type="primary" size="small" @click="refreshData">刷新数据</el-button>
      </div>
    </div>

    <el-table
      :data="filteredRecords"
      style="width: 100%"
      v-loading="loading"
      :empty-text="emptyText"
    >
      <el-table-column prop="visit_date" label="就诊日期" width="120">
        <template #default="scope">
          {{ formatDate(scope.row.visit_date) }}
        </template>
      </el-table-column>
      <el-table-column prop="record_type" label="记录类型" width="120">
        <template #default="scope">
          {{ getRecordTypeLabel(scope.row.record_type) }}
        </template>
      </el-table-column>
      <el-table-column prop="hospital_name" label="医院" width="180" />
      <el-table-column prop="department" label="科室" width="120" />
      <el-table-column prop="doctor_name" label="医生" width="100" />
      <el-table-column prop="diagnosis" label="诊断" show-overflow-tooltip />
      <el-table-column prop="is_important" label="重要标记" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.is_important ? 'danger' : 'info'">
            {{ scope.row.is_important ? '重要' : '普通' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120" fixed="right">
        <template #default="scope">
          <el-button type="primary" link @click="viewRecord(scope.row)">查看</el-button>
          <el-button type="danger" link @click="deleteRecord(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 记录详情对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="60%"
    >
      <div v-if="currentRecord" class="record-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="就诊日期">{{ formatDate(currentRecord.visit_date) }}</el-descriptions-item>
          <el-descriptions-item label="记录类型">{{ getRecordTypeLabel(currentRecord.record_type) }}</el-descriptions-item>
          <el-descriptions-item label="医院">{{ currentRecord.hospital_name }}</el-descriptions-item>
          <el-descriptions-item label="科室">{{ currentRecord.department }}</el-descriptions-item>
          <el-descriptions-item label="医生">{{ currentRecord.doctor_name }}</el-descriptions-item>
          <el-descriptions-item label="重要标记">
            <el-tag :type="currentRecord.is_important ? 'danger' : 'info'">
              {{ currentRecord.is_important ? '重要' : '普通' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="诊断" :span="2">{{ currentRecord.diagnosis }}</el-descriptions-item>
          <el-descriptions-item label="治疗方案" :span="2">{{ currentRecord.treatment }}</el-descriptions-item>
          <el-descriptions-item label="处方" :span="2">{{ currentRecord.prescription }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ currentRecord.notes }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import axios from 'axios';
import { getMockData, isMockEnabled } from '../../mocks/mockDataManager';

// 接收用户ID作为属性
const props = defineProps({
  customId: {
    type: [Number, String],
    required: true
  }
});

// 状态变量
const loading = ref(false);
const medicalRecords = ref([]);
const filterType = ref('');
const dialogVisible = ref(false);
const currentRecord = ref(null);

// 计算属性
const dialogTitle = computed(() => {
  if (!currentRecord.value) return '就诊记录详情';
  
  const typeLabel = getRecordTypeLabel(currentRecord.value.record_type);
  const dateStr = formatDate(currentRecord.value.visit_date);
  
  return `${typeLabel} (${dateStr})`;
});

const emptyText = computed(() => {
  return loading.value ? '加载中...' : '暂无就诊记录数据';
});

const filteredRecords = computed(() => {
  if (!filterType.value) return medicalRecords.value;
  
  return medicalRecords.value.filter(record => record.record_type === filterType.value);
});

// 获取医疗记录数据
const fetchMedicalRecords = async () => {
  if (!props.customId) return;
  
  loading.value = true;
  try {
    const response = await axios.get(`/api/v1/aggregated/health-profile/${props.customId}`, {
        params: {
          include_medical_records: true
        }
      });
    const profileData = response.data.profile_data || {};
    medicalRecords.value = profileData.medical_records || [];
    
    // 如果没有真实数据且启用了模拟数据，使用模拟数据
    if ((!response.data || response.data.length === 0) && isMockEnabled()) {
      medicalRecords.value = getMockData('medicalRecords', { custom_id: props.customId });
    }
  } catch (error) {
    console.error('获取医疗记录失败:', error);
    ElMessage.error('获取医疗记录失败，请稍后重试');
    
    // 如果启用了模拟数据，使用模拟数据
    if (isMockEnabled()) {
      medicalRecords.value = getMockData('medicalRecords', { custom_id: props.customId });
    }
  } finally {
    loading.value = false;
  }
};

// 监听用户ID变化
watch(() => props.customId, (newVal) => {
  if (newVal) {
    fetchMedicalRecords();
  } else {
    medicalRecords.value = [];
  }
}, { immediate: true });

// 初始化
onMounted(() => {
  if (props.customId) {
    fetchMedicalRecords();
  }
});

// 刷新数据
const refreshData = () => {
  fetchMedicalRecords();
};

// 筛选记录
const filterRecords = () => {
  // 筛选逻辑已通过计算属性实现
};

// 查看记录详情
const viewRecord = (record) => {
  currentRecord.value = record;
  dialogVisible.value = true;
};

// 删除记录
const deleteRecord = (record) => {
  ElMessageBox.confirm(
    `确定要删除 ${formatDate(record.visit_date)} 的${getRecordTypeLabel(record.record_type)}吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await axios.delete(`/api/user-health-records/user/${props.customId}/${record.id}`, {
        params: {
          record_type: 'medical'
        }
      });
      ElMessage.success('删除成功');
      fetchMedicalRecords(); // 刷新数据
    } catch (error) {
      console.error('删除医疗记录失败:', error);
      ElMessage.error('删除医疗记录失败，请稍后重试');
      
      // 如果启用了模拟数据，模拟删除成功
      if (isMockEnabled()) {
        medicalRecords.value = medicalRecords.value.filter(item => item.id !== record.id);
        ElMessage.success('删除成功');
      }
    }
  }).catch(() => {
    // 用户取消删除
  });
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  return date.toLocaleDateString();
};

// 获取记录类型标签
const getRecordTypeLabel = (type) => {
  const typeMap = {
    'outpatient': '门诊记录',
    'admission': '入院记录',
    'discharge': '出院小结',
    'surgery': '手术记录',
    'other': '其他记录'
  };
  
  return typeMap[type] || type;
};
</script>

<style scoped>
.medical-records-tab {
  padding: 10px;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.tab-header h3 {
  margin: 0;
}

.filter-container {
  display: flex;
  gap: 10px;
}

.record-detail {
  padding: 10px;
}
</style>
