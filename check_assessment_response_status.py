import sqlite3
import sys
import os

# 连接数据库
db_path = os.path.join(os.path.dirname(__file__), 'YUN', 'backend', 'app.db')
if not os.path.exists(db_path):
    print(f"数据库文件不存在: {db_path}")
    sys.exit(1)

conn = sqlite3.connect(db_path)
cursor = conn.cursor()

try:
    # 检查assessment_responses表结构
    print("=== assessment_responses表结构 ===")
    cursor.execute("PRAGMA table_info(assessment_responses)")
    columns = cursor.fetchall()
    for col in columns:
        print(f"{col[1]} ({col[2]})")
    
    # 检查assessment_responses表中的状态分布
    print("\n=== assessment_responses表状态分布 ===")
    cursor.execute("SELECT status, COUNT(*) FROM assessment_responses GROUP BY status")
    status_counts = cursor.fetchall()
    for status, count in status_counts:
        print(f"{status}: {count}条记录")
    
    # 查看SM_008用户的assessment_responses记录
    print("\n=== SM_008用户的assessment_responses记录 ===")
    cursor.execute("""
        SELECT id, assessment_id, custom_id, status, created_at, updated_at 
        FROM assessment_responses 
        WHERE custom_id = 'SM_008'
        ORDER BY created_at DESC
    """)
    records = cursor.fetchall()
    for record in records:
        print(f"ID: {record[0]}, Assessment ID: {record[1]}, Status: {record[3]}, Created: {record[4]}, Updated: {record[5]}")
    
    # 检查assessment_results表中SM_008的记录
    print("\n=== SM_008用户的assessment_results记录 ===")
    cursor.execute("""
        SELECT id, assessment_id, custom_id, status, created_at 
        FROM assessment_results 
        WHERE custom_id = 'SM_008'
        ORDER BY created_at DESC
    """)
    results = cursor.fetchall()
    for result in results:
        print(f"ID: {result[0]}, Assessment ID: {result[1]}, Status: {result[3]}, Created: {result[4]}")
        
except Exception as e:
    print(f"查询出错: {e}")
finally:
    conn.close()