
# -*- coding: utf-8 -*-
"""
标准化的raw_answers数据同步函数
用于确保评估和问卷提交时正确保存raw_answers
"""

import json
from datetime import datetime
from typing import Dict, Any, Optional

def ensure_raw_answers_sync(db_session, custom_id: str, template_id: int, 
                           answers: Dict[str, Any], assessment_type: str = 'assessment'):
    """
    确保raw_answers数据正确同步到assessment_results表
    
    Args:
        db_session: 数据库会话
        custom_id: 用户自定义ID
        template_id: 模板ID
        answers: 答案数据字典
        assessment_type: 评估类型 ('assessment' 或 'questionnaire')
    """
    
    try:
        # 将答案转换为JSON字符串
        raw_answers_json = json.dumps(answers, ensure_ascii=False)
        
        # 查找现有的assessment_results记录
        from app.models.assessment import AssessmentResult  # 假设存在这个模型
        
        existing_result = db_session.query(AssessmentResult).filter(
            AssessmentResult.custom_id == custom_id,
            AssessmentResult.template_id == template_id
        ).first()
        
        if existing_result:
            # 更新现有记录的raw_answers
            existing_result.raw_answers = raw_answers_json
            existing_result.updated_at = datetime.now()
            print(f"✓ 更新raw_answers: custom_id={custom_id}, template_id={template_id}")
        else:
            # 创建新的assessment_results记录
            new_result = AssessmentResult(
                custom_id=custom_id,
                template_id=template_id,
                raw_answers=raw_answers_json,
                status='completed',
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            db_session.add(new_result)
            print(f"✓ 创建raw_answers记录: custom_id={custom_id}, template_id={template_id}")
        
        db_session.commit()
        return True
        
    except Exception as e:
        print(f"✗ raw_answers同步失败: {e}")
        db_session.rollback()
        return False

def validate_answers_format(answers: Any) -> Dict[str, Any]:
    """
    验证和标准化答案格式
    
    Args:
        answers: 原始答案数据
        
    Returns:
        标准化的答案字典
    """
    
    if isinstance(answers, str):
        try:
            answers = json.loads(answers)
        except:
            return {}
    
    if isinstance(answers, list):
        # 将列表格式转换为字典格式
        answers_dict = {}
        for i, answer in enumerate(answers):
            if isinstance(answer, dict) and 'question_id' in answer and 'answer' in answer:
                answers_dict[answer['question_id']] = answer['answer']
            else:
                answers_dict[f"question_{i+1}"] = answer
        return answers_dict
    
    if isinstance(answers, dict):
        return answers
    
    return {}

def calculate_scores_with_raw_answers(answers: Dict[str, Any], template_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    计算分数并保留原始答案数据
    
    Args:
        answers: 答案数据
        template_config: 模板配置
        
    Returns:
        包含分数和原始答案的结果字典
    """
    
    result = {
        'raw_answers': answers,
        'total_score': 0,
        'dimension_scores': {},
        'calculation_details': {}
    }
    
    try:
        # 根据模板配置计算分数
        if 'scoring_rules' in template_config:
            scoring_rules = template_config['scoring_rules']
            
            # 计算总分
            total_score = 0
            for question_id, answer in answers.items():
                if question_id in scoring_rules:
                    score = scoring_rules[question_id].get(str(answer), 0)
                    total_score += score
            
            result['total_score'] = total_score
        
        # 计算维度分数
        if 'dimensions' in template_config:
            dimensions = template_config['dimensions']
            dimension_scores = {}
            
            for dim_name, dim_config in dimensions.items():
                dim_score = 0
                dim_questions = dim_config.get('questions', [])
                
                for question_id in dim_questions:
                    if question_id in answers:
                        answer = answers[question_id]
                        if 'scoring' in dim_config:
                            score = dim_config['scoring'].get(question_id, {}).get(str(answer), 0)
                            dim_score += score
                
                dimension_scores[dim_name] = dim_score
            
            result['dimension_scores'] = dimension_scores
        
        result['calculation_details'] = {
            'calculated_at': datetime.now().isoformat(),
            'template_used': template_config.get('name', 'Unknown'),
            'scoring_method': template_config.get('scoring_method', 'standard')
        }
        
    except Exception as e:
        print(f"计算分数时出错: {e}")
        result['calculation_details']['error'] = str(e)
    
    return result

def get_template_scoring_config(template_id: int, template_type: str = 'assessment') -> Dict[str, Any]:
    """
    获取模板的计分配置
    
    Args:
        template_id: 模板ID
        template_type: 模板类型
        
    Returns:
        模板配置字典
    """
    
    # 这里应该从数据库获取实际的模板配置
    # 暂时返回默认配置
    default_config = {
        'name': f'Template_{template_id}',
        'scoring_method': 'standard',
        'needs_scoring': True,
        'needs_dimensions': True,
        'scoring_rules': {},
        'dimensions': {}
    }
    
    return default_config
