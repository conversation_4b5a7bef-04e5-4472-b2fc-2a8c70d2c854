# -*- coding: utf-8 -*-
import sqlite3

# 数据库路径
db_path = 'c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db'

# 评估模板分类映射
category_mapping = {
    'sds': '抑郁量表',
    'hamilton_depression': '抑郁量表', 
    'sas': '焦虑量表',
    'mmse': '认知量表',
    'moca': '认知量表'
}

try:
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    print("=== 更新评估模板分类 ===")
    
    for template_key, category in category_mapping.items():
        cursor.execute(
            'UPDATE assessment_templates SET sub_type = ? WHERE template_key = ?',
            (category, template_key)
        )
        print(f"更新模板 {template_key} 的分类为: {category}")
    
    conn.commit()
    
    print("\n=== 验证更新结果 ===")
    cursor.execute('SELECT template_key, sub_type, assessment_type FROM assessment_templates')
    templates = cursor.fetchall()
    
    for template in templates:
        print(f"  模板: {template[0]}, 子类型: {template[1]}, 评估类型: {template[2]}")
    
    conn.close()
    print("\n更新完成!")
    
except Exception as e:
    print(f"更新失败: {e}")