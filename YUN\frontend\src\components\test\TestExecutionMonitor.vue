<template>
  <div class="test-execution-monitor">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>测试执行监控</h2>
      <p class="page-description">实时监控测试执行状态和系统资源使用情况</p>
    </div>

    <!-- 控制面板 -->
    <div class="control-panel">
      <div class="panel-left">
        <el-button type="primary" icon="Refresh" @click="refreshMonitorData" :loading="refreshing">
          刷新数据
        </el-button>
        <el-button icon="VideoPlay" @click="startMonitoring" :disabled="isMonitoring">
          开始监控
        </el-button>
        <el-button icon="VideoPause" @click="stopMonitoring" :disabled="!isMonitoring">
          停止监控
        </el-button>
        <el-button icon="Setting" @click="showSettingsDialog = true">
          监控设置
        </el-button>
      </div>
      <div class="panel-right">
        <div class="monitor-status">
          <el-tag :type="isMonitoring ? 'success' : 'info'" size="large">
            <el-icon><component :is="isMonitoring ? 'VideoPlay' : 'VideoPause'" /></el-icon>
            {{ isMonitoring ? '监控中' : '已停止' }}
          </el-tag>
          <span class="last-update">最后更新: {{ formatTime(lastUpdateTime) }}</span>
        </div>
      </div>
    </div>

    <!-- 实时状态概览 -->
    <div class="status-overview">
      <div class="status-cards">
        <el-card class="status-card running">
          <div class="card-content">
            <div class="status-icon">
              <el-icon><Loading /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-value">{{ runningTests }}</div>
              <div class="status-label">运行中</div>
            </div>
            <div class="status-trend">
              <el-progress 
                :percentage="runningProgress" 
                :stroke-width="4" 
                color="#409eff" 
                :show-text="false"
              />
            </div>
          </div>
        </el-card>

        <el-card class="status-card queued">
          <div class="card-content">
            <div class="status-icon">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-value">{{ queuedTests }}</div>
              <div class="status-label">队列中</div>
            </div>
            <div class="status-trend">
              <span class="trend-text">等待执行</span>
            </div>
          </div>
        </el-card>

        <el-card class="status-card completed">
          <div class="card-content">
            <div class="status-icon">
              <el-icon><SuccessFilled /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-value">{{ completedTests }}</div>
              <div class="status-label">已完成</div>
            </div>
            <div class="status-trend">
              <span class="trend-text success">+{{ recentCompleted }}</span>
            </div>
          </div>
        </el-card>

        <el-card class="status-card failed">
          <div class="card-content">
            <div class="status-icon">
              <el-icon><CircleCloseFilled /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-value">{{ failedTests }}</div>
              <div class="status-label">失败</div>
            </div>
            <div class="status-trend">
              <span class="trend-text danger">{{ failedTests > 0 ? '+' + recentFailed : '0' }}</span>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 系统资源监控 -->
    <div class="resource-monitoring">
      <div class="monitoring-grid">
        <!-- CPU使用率 -->
        <el-card class="resource-card">
          <template #header>
            <div class="card-header">
              <span>CPU使用率</span>
              <el-tag :type="getCpuStatusType(systemResources.cpu)" size="small">
                {{ systemResources.cpu }}%
              </el-tag>
            </div>
          </template>
          <div class="resource-chart" ref="cpuChartRef"></div>
          <div class="resource-details">
            <div class="detail-item">
              <span class="detail-label">当前:</span>
              <span class="detail-value">{{ systemResources.cpu }}%</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">平均:</span>
              <span class="detail-value">{{ systemResources.avgCpu }}%</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">峰值:</span>
              <span class="detail-value">{{ systemResources.maxCpu }}%</span>
            </div>
          </div>
        </el-card>

        <!-- 内存使用 -->
        <el-card class="resource-card">
          <template #header>
            <div class="card-header">
              <span>内存使用</span>
              <el-tag :type="getMemoryStatusType(systemResources.memory)" size="small">
                {{ systemResources.memory }}MB
              </el-tag>
            </div>
          </template>
          <div class="resource-chart" ref="memoryChartRef"></div>
          <div class="resource-details">
            <div class="detail-item">
              <span class="detail-label">当前:</span>
              <span class="detail-value">{{ systemResources.memory }}MB</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">可用:</span>
              <span class="detail-value">{{ systemResources.availableMemory }}MB</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">使用率:</span>
              <span class="detail-value">{{ systemResources.memoryUsage }}%</span>
            </div>
          </div>
        </el-card>

        <!-- 网络I/O -->
        <el-card class="resource-card">
          <template #header>
            <div class="card-header">
              <span>网络I/O</span>
              <el-tag type="info" size="small">
                {{ systemResources.networkSpeed }}MB/s
              </el-tag>
            </div>
          </template>
          <div class="resource-chart" ref="networkChartRef"></div>
          <div class="resource-details">
            <div class="detail-item">
              <span class="detail-label">上传:</span>
              <span class="detail-value">{{ systemResources.uploadSpeed }}MB/s</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">下载:</span>
              <span class="detail-value">{{ systemResources.downloadSpeed }}MB/s</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">延迟:</span>
              <span class="detail-value">{{ systemResources.latency }}ms</span>
            </div>
          </div>
        </el-card>

        <!-- 磁盘I/O -->
        <el-card class="resource-card">
          <template #header>
            <div class="card-header">
              <span>磁盘I/O</span>
              <el-tag type="warning" size="small">
                {{ systemResources.diskUsage }}%
              </el-tag>
            </div>
          </template>
          <div class="resource-chart" ref="diskChartRef"></div>
          <div class="resource-details">
            <div class="detail-item">
              <span class="detail-label">读取:</span>
              <span class="detail-value">{{ systemResources.diskRead }}MB/s</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">写入:</span>
              <span class="detail-value">{{ systemResources.diskWrite }}MB/s</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">使用率:</span>
              <span class="detail-value">{{ systemResources.diskUsage }}%</span>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 执行队列监控 -->
    <div class="execution-queue">
      <div class="queue-header">
        <h3>执行队列</h3>
        <div class="queue-controls">
          <el-button size="small" @click="clearCompletedTasks">
            <el-icon><Delete /></el-icon>
            清理已完成
          </el-button>
          <el-button size="small" @click="pauseAllTasks">
            <el-icon><VideoPause /></el-icon>
            暂停全部
          </el-button>
          <el-button size="small" @click="resumeAllTasks">
            <el-icon><VideoPlay /></el-icon>
            恢复全部
          </el-button>
        </div>
      </div>

      <el-table :data="executionQueue" style="width: 100%" stripe>
        <el-table-column prop="id" label="任务ID" width="100" />
        
        <el-table-column prop="name" label="测试套件" min-width="200">
          <template #default="{ row }">
            <div class="task-info">
              <el-icon :class="getTaskIcon(row.type)"><component :is="getTaskIcon(row.type)" /></el-icon>
              <span class="task-name">{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getTaskStatusType(row.status)" size="small">
              {{ getTaskStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="progress" label="进度" width="150">
          <template #default="{ row }">
            <div class="progress-info">
              <el-progress
                :percentage="row.progress"
                :stroke-width="6"
                :color="getProgressColor(row.progress)"
                :show-text="false"
              />
              <span class="progress-text">{{ row.progress }}%</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="startTime" label="开始时间" width="150">
          <template #default="{ row }">
            <span v-if="row.startTime">{{ formatTime(row.startTime) }}</span>
            <span v-else class="text-muted">未开始</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="duration" label="耗时" width="100">
          <template #default="{ row }">
            <span v-if="row.duration">{{ formatDuration(row.duration) }}</span>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="priority" label="优先级" width="100">
          <template #default="{ row }">
            <el-tag :type="getPriorityType(row.priority)" size="small">
              {{ getPriorityText(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button 
              v-if="row.status === 'running'" 
              type="text" 
              size="small" 
              @click="pauseTask(row)"
            >
              <el-icon><VideoPause /></el-icon>
              暂停
            </el-button>
            <el-button 
              v-else-if="row.status === 'paused'" 
              type="text" 
              size="small" 
              @click="resumeTask(row)"
            >
              <el-icon><VideoPlay /></el-icon>
              恢复
            </el-button>
            <el-button 
              v-if="['queued', 'paused'].includes(row.status)" 
              type="text" 
              size="small" 
              @click="cancelTask(row)"
            >
              <el-icon><Close /></el-icon>
              取消
            </el-button>
            <el-button type="text" size="small" @click="viewTaskDetail(row)">
              <el-icon><View /></el-icon>
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 实时日志 -->
    <div class="real-time-logs">
      <div class="logs-header">
        <h3>实时日志</h3>
        <div class="logs-controls">
          <el-select v-model="logLevel" size="small" style="width: 120px;">
            <el-option label="全部" value="all" />
            <el-option label="错误" value="error" />
            <el-option label="警告" value="warn" />
            <el-option label="信息" value="info" />
            <el-option label="调试" value="debug" />
          </el-select>
          <el-button size="small" @click="clearLogs" style="margin-left: 10px;">
            <el-icon><Delete /></el-icon>
            清空日志
          </el-button>
          <el-button size="small" @click="downloadLogs">
            <el-icon><Download /></el-icon>
            下载日志
          </el-button>
          <el-switch 
            v-model="autoScroll" 
            active-text="自动滚动" 
            style="margin-left: 10px;"
          />
        </div>
      </div>
      
      <div class="logs-container" ref="logsContainerRef">
        <div 
          v-for="(log, index) in filteredLogs" 
          :key="index" 
          class="log-entry"
          :class="log.level"
        >
          <span class="log-time">{{ formatTime(log.timestamp) }}</span>
          <span class="log-level">{{ log.level.toUpperCase() }}</span>
          <span class="log-source">{{ log.source }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
        
        <div v-if="filteredLogs.length === 0" class="empty-logs">
          <el-empty description="暂无日志" />
        </div>
      </div>
    </div>

    <!-- 监控设置对话框 -->
    <el-dialog v-model="showSettingsDialog" title="监控设置" width="600px">
      <el-form :model="monitorSettings" label-width="120px">
        <el-form-item label="刷新间隔">
          <el-input-number 
            v-model="monitorSettings.refreshInterval" 
            :min="1" 
            :max="60" 
            controls-position="right"
          />
          <span style="margin-left: 10px;">秒</span>
        </el-form-item>
        
        <el-form-item label="数据保留时间">
          <el-input-number 
            v-model="monitorSettings.dataRetention" 
            :min="1" 
            :max="24" 
            controls-position="right"
          />
          <span style="margin-left: 10px;">小时</span>
        </el-form-item>
        
        <el-form-item label="CPU警告阈值">
          <el-input-number 
            v-model="monitorSettings.cpuThreshold" 
            :min="50" 
            :max="95" 
            controls-position="right"
          />
          <span style="margin-left: 10px;">%</span>
        </el-form-item>
        
        <el-form-item label="内存警告阈值">
          <el-input-number 
            v-model="monitorSettings.memoryThreshold" 
            :min="50" 
            :max="95" 
            controls-position="right"
          />
          <span style="margin-left: 10px;">%</span>
        </el-form-item>
        
        <el-form-item label="启用通知">
          <el-switch v-model="monitorSettings.enableNotifications" />
        </el-form-item>
        
        <el-form-item label="声音提醒">
          <el-switch v-model="monitorSettings.enableSound" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showSettingsDialog = false">取消</el-button>
          <el-button type="primary" @click="saveSettings">保存设置</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 任务详情对话框 -->
    <el-dialog v-model="showTaskDetailDialog" title="任务详情" width="800px">
      <div v-if="selectedTask" class="task-detail">
        <div class="detail-section">
          <h4>基本信息</h4>
          <div class="info-grid">
            <div class="info-item">
              <span class="info-label">任务ID:</span>
              <span class="info-value">{{ selectedTask.id }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">测试套件:</span>
              <span class="info-value">{{ selectedTask.name }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">状态:</span>
              <el-tag :type="getTaskStatusType(selectedTask.status)">
                {{ getTaskStatusText(selectedTask.status) }}
              </el-tag>
            </div>
            <div class="info-item">
              <span class="info-label">优先级:</span>
              <el-tag :type="getPriorityType(selectedTask.priority)">
                {{ getPriorityText(selectedTask.priority) }}
              </el-tag>
            </div>
          </div>
        </div>
        
        <div class="detail-section">
          <h4>执行信息</h4>
          <div class="info-grid">
            <div class="info-item">
              <span class="info-label">开始时间:</span>
              <span class="info-value">{{ selectedTask.startTime ? formatTime(selectedTask.startTime) : '未开始' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">预计完成:</span>
              <span class="info-value">{{ selectedTask.estimatedCompletion ? formatTime(selectedTask.estimatedCompletion) : '计算中' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">已用时间:</span>
              <span class="info-value">{{ selectedTask.duration ? formatDuration(selectedTask.duration) : '-' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">进度:</span>
              <span class="info-value">{{ selectedTask.progress }}%</span>
            </div>
          </div>
        </div>
        
        <div class="detail-section">
          <h4>配置信息</h4>
          <div class="config-info">
            <pre>{{ JSON.stringify(selectedTask.config, null, 2) }}</pre>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh, VideoPlay, VideoPause, Setting, Loading, Clock, SuccessFilled, 
  CircleCloseFilled, Delete, Close, View, Download
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// 响应式数据
const isMonitoring = ref(false)
const refreshing = ref(false)
const lastUpdateTime = ref(new Date())
const showSettingsDialog = ref(false)
const showTaskDetailDialog = ref(false)
const selectedTask = ref(null)
const logLevel = ref('all')
const autoScroll = ref(true)

// 图表引用
const cpuChartRef = ref()
const memoryChartRef = ref()
const networkChartRef = ref()
const diskChartRef = ref()
const logsContainerRef = ref()

// 监控数据
const runningTests = ref(3)
const queuedTests = ref(7)
const completedTests = ref(45)
const failedTests = ref(2)
const runningProgress = ref(65)
const recentCompleted = ref(12)
const recentFailed = ref(1)

// 系统资源数据
const systemResources = reactive({
  cpu: 45,
  avgCpu: 38,
  maxCpu: 72,
  memory: 2048,
  availableMemory: 6144,
  memoryUsage: 25,
  networkSpeed: 12.5,
  uploadSpeed: 5.2,
  downloadSpeed: 7.3,
  latency: 45,
  diskUsage: 68,
  diskRead: 15.6,
  diskWrite: 8.9
})

// 执行队列数据
const executionQueue = ref([
  {
    id: 'T001',
    name: '前端单元测试',
    type: 'unit',
    status: 'running',
    progress: 75,
    startTime: new Date(Date.now() - 300000),
    duration: 300000,
    priority: 'high',
    config: { timeout: 30000, retries: 2 }
  },
  {
    id: 'T002',
    name: 'API集成测试',
    type: 'api',
    status: 'queued',
    progress: 0,
    startTime: null,
    duration: null,
    priority: 'medium',
    config: { timeout: 45000, retries: 1 }
  },
  {
    id: 'T003',
    name: 'E2E测试',
    type: 'e2e',
    status: 'paused',
    progress: 30,
    startTime: new Date(Date.now() - 600000),
    duration: 180000,
    priority: 'low',
    config: { timeout: 120000, retries: 0 }
  }
])

// 日志数据
const logs = ref([
  {
    timestamp: new Date(Date.now() - 60000),
    level: 'info',
    source: 'TestRunner',
    message: '开始执行前端单元测试套件'
  },
  {
    timestamp: new Date(Date.now() - 45000),
    level: 'debug',
    source: 'TestRunner',
    message: '加载测试配置文件: frontend.config.js'
  },
  {
    timestamp: new Date(Date.now() - 30000),
    level: 'warn',
    source: 'TestRunner',
    message: 'CPU使用率较高: 72%'
  },
  {
    timestamp: new Date(Date.now() - 15000),
    level: 'error',
    source: 'TestCase',
    message: '测试用例 "用户登录" 执行失败: 网络超时'
  },
  {
    timestamp: new Date(),
    level: 'info',
    source: 'TestRunner',
    message: '测试套件执行完成，成功率: 95%'
  }
])

// 监控设置
const monitorSettings = reactive({
  refreshInterval: 5,
  dataRetention: 2,
  cpuThreshold: 80,
  memoryThreshold: 85,
  enableNotifications: true,
  enableSound: false
})

// 计算属性
const filteredLogs = computed(() => {
  if (logLevel.value === 'all') {
    return logs.value
  }
  return logs.value.filter(log => log.level === logLevel.value)
})

// 图表实例
let cpuChart = null
let memoryChart = null
let networkChart = null
let diskChart = null
let monitorInterval = null

// 方法
const startMonitoring = () => {
  isMonitoring.value = true
  monitorInterval = setInterval(() => {
    updateMonitorData()
    updateCharts()
  }, monitorSettings.refreshInterval * 1000)
  ElMessage.success('开始监控测试执行')
}

const stopMonitoring = () => {
  isMonitoring.value = false
  if (monitorInterval) {
    clearInterval(monitorInterval)
    monitorInterval = null
  }
  ElMessage.info('停止监控测试执行')
}

const refreshMonitorData = async () => {
  refreshing.value = true
  try {
    // 模拟数据刷新
    await new Promise(resolve => setTimeout(resolve, 1000))
    updateMonitorData()
    updateCharts()
    lastUpdateTime.value = new Date()
    ElMessage.success('监控数据已刷新')
  } catch (error) {
    ElMessage.error('刷新失败: ' + error.message)
  } finally {
    refreshing.value = false
  }
}

const updateMonitorData = () => {
  // 模拟实时数据更新
  systemResources.cpu = Math.floor(Math.random() * 40) + 30
  systemResources.memory = Math.floor(Math.random() * 1000) + 1500
  systemResources.networkSpeed = (Math.random() * 20 + 5).toFixed(1)
  systemResources.diskUsage = Math.floor(Math.random() * 30) + 50
  
  // 更新测试状态
  runningProgress.value = Math.min(runningProgress.value + Math.floor(Math.random() * 10), 100)
  
  // 添加新日志
  if (Math.random() > 0.7) {
    const levels = ['info', 'warn', 'error', 'debug']
    const sources = ['TestRunner', 'TestCase', 'System']
    const messages = [
      '测试用例执行完成',
      '检测到内存泄漏',
      '网络连接异常',
      '测试数据准备完成',
      'CPU使用率正常'
    ]
    
    logs.value.push({
      timestamp: new Date(),
      level: levels[Math.floor(Math.random() * levels.length)],
      source: sources[Math.floor(Math.random() * sources.length)],
      message: messages[Math.floor(Math.random() * messages.length)]
    })
    
    // 保持日志数量在合理范围内
    if (logs.value.length > 100) {
      logs.value.splice(0, logs.value.length - 100)
    }
    
    // 自动滚动到底部
    if (autoScroll.value) {
      nextTick(() => {
        const container = logsContainerRef.value
        if (container) {
          container.scrollTop = container.scrollHeight
        }
      })
    }
  }
}

const initCharts = () => {
  // CPU图表
  if (cpuChartRef.value) {
    cpuChart = echarts.init(cpuChartRef.value)
    cpuChart.setOption({
      grid: { top: 10, right: 10, bottom: 20, left: 30 },
      xAxis: { type: 'category', show: false },
      yAxis: { type: 'value', min: 0, max: 100, show: false },
      series: [{
        type: 'line',
        data: Array(20).fill(0).map(() => Math.floor(Math.random() * 40) + 30),
        smooth: true,
        lineStyle: { color: '#409eff' },
        areaStyle: { color: 'rgba(64, 158, 255, 0.1)' },
        symbol: 'none'
      }]
    })
  }
  
  // 内存图表
  if (memoryChartRef.value) {
    memoryChart = echarts.init(memoryChartRef.value)
    memoryChart.setOption({
      grid: { top: 10, right: 10, bottom: 20, left: 30 },
      xAxis: { type: 'category', show: false },
      yAxis: { type: 'value', show: false },
      series: [{
        type: 'line',
        data: Array(20).fill(0).map(() => Math.floor(Math.random() * 1000) + 1500),
        smooth: true,
        lineStyle: { color: '#67c23a' },
        areaStyle: { color: 'rgba(103, 194, 58, 0.1)' },
        symbol: 'none'
      }]
    })
  }
  
  // 网络图表
  if (networkChartRef.value) {
    networkChart = echarts.init(networkChartRef.value)
    networkChart.setOption({
      grid: { top: 10, right: 10, bottom: 20, left: 30 },
      xAxis: { type: 'category', show: false },
      yAxis: { type: 'value', show: false },
      series: [{
        type: 'line',
        data: Array(20).fill(0).map(() => (Math.random() * 20 + 5).toFixed(1)),
        smooth: true,
        lineStyle: { color: '#e6a23c' },
        areaStyle: { color: 'rgba(230, 162, 60, 0.1)' },
        symbol: 'none'
      }]
    })
  }
  
  // 磁盘图表
  if (diskChartRef.value) {
    diskChart = echarts.init(diskChartRef.value)
    diskChart.setOption({
      grid: { top: 10, right: 10, bottom: 20, left: 30 },
      xAxis: { type: 'category', show: false },
      yAxis: { type: 'value', min: 0, max: 100, show: false },
      series: [{
        type: 'line',
        data: Array(20).fill(0).map(() => Math.floor(Math.random() * 30) + 50),
        smooth: true,
        lineStyle: { color: '#f56c6c' },
        areaStyle: { color: 'rgba(245, 108, 108, 0.1)' },
        symbol: 'none'
      }]
    })
  }
}

const updateCharts = () => {
  // 更新CPU图表
  if (cpuChart) {
    const option = cpuChart.getOption()
    const data = option.series[0].data
    data.shift()
    data.push(systemResources.cpu)
    cpuChart.setOption({ series: [{ data }] })
  }
  
  // 更新内存图表
  if (memoryChart) {
    const option = memoryChart.getOption()
    const data = option.series[0].data
    data.shift()
    data.push(systemResources.memory)
    memoryChart.setOption({ series: [{ data }] })
  }
  
  // 更新网络图表
  if (networkChart) {
    const option = networkChart.getOption()
    const data = option.series[0].data
    data.shift()
    data.push(parseFloat(systemResources.networkSpeed))
    networkChart.setOption({ series: [{ data }] })
  }
  
  // 更新磁盘图表
  if (diskChart) {
    const option = diskChart.getOption()
    const data = option.series[0].data
    data.shift()
    data.push(systemResources.diskUsage)
    diskChart.setOption({ series: [{ data }] })
  }
}

const clearCompletedTasks = () => {
  const completedCount = executionQueue.value.filter(task => task.status === 'completed').length
  executionQueue.value = executionQueue.value.filter(task => task.status !== 'completed')
  ElMessage.success(`已清理 ${completedCount} 个已完成任务`)
}

const pauseAllTasks = () => {
  executionQueue.value.forEach(task => {
    if (task.status === 'running') {
      task.status = 'paused'
    }
  })
  ElMessage.info('已暂停所有运行中的任务')
}

const resumeAllTasks = () => {
  executionQueue.value.forEach(task => {
    if (task.status === 'paused') {
      task.status = 'running'
    }
  })
  ElMessage.success('已恢复所有暂停的任务')
}

const pauseTask = (task) => {
  task.status = 'paused'
  ElMessage.info(`任务 ${task.id} 已暂停`)
}

const resumeTask = (task) => {
  task.status = 'running'
  ElMessage.success(`任务 ${task.id} 已恢复`)
}

const cancelTask = async (task) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消任务 "${task.name}" 吗？`,
      '确认取消',
      {
        confirmButtonText: '取消任务',
        cancelButtonText: '保留任务',
        type: 'warning'
      }
    )
    
    const index = executionQueue.value.findIndex(t => t.id === task.id)
    if (index > -1) {
      executionQueue.value.splice(index, 1)
      ElMessage.success(`任务 ${task.id} 已取消`)
    }
  } catch {
    // 用户取消操作
  }
}

const viewTaskDetail = (task) => {
  selectedTask.value = task
  showTaskDetailDialog.value = true
}

const clearLogs = () => {
  logs.value = []
  ElMessage.success('日志已清空')
}

const downloadLogs = () => {
  const logText = logs.value.map(log => 
    `[${formatTime(log.timestamp)}] ${log.level.toUpperCase()} ${log.source}: ${log.message}`
  ).join('\n')
  
  const blob = new Blob([logText], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `test-execution-logs-${new Date().toISOString().slice(0, 10)}.txt`
  a.click()
  URL.revokeObjectURL(url)
  ElMessage.success('日志已下载')
}

const saveSettings = () => {
  // 保存设置到本地存储
  localStorage.setItem('monitorSettings', JSON.stringify(monitorSettings))
  
  // 如果监控正在运行，重新启动以应用新设置
  if (isMonitoring.value) {
    stopMonitoring()
    startMonitoring()
  }
  
  showSettingsDialog.value = false
  ElMessage.success('设置已保存')
}

// 工具函数
const getCpuStatusType = (cpu) => {
  if (cpu >= monitorSettings.cpuThreshold) return 'danger'
  if (cpu >= 60) return 'warning'
  return 'success'
}

const getMemoryStatusType = (memory) => {
  const usage = (memory / (memory + systemResources.availableMemory)) * 100
  if (usage >= monitorSettings.memoryThreshold) return 'danger'
  if (usage >= 70) return 'warning'
  return 'success'
}

const getTaskIcon = (type) => {
  const icons = {
    unit: 'Cpu',
    integration: 'Connection',
    api: 'Link',
    e2e: 'Monitor',
    performance: 'Odometer'
  }
  return icons[type] || 'Document'
}

const getTaskStatusType = (status) => {
  const types = {
    running: 'primary',
    queued: 'info',
    paused: 'warning',
    completed: 'success',
    failed: 'danger',
    cancelled: 'info'
  }
  return types[status] || 'info'
}

const getTaskStatusText = (status) => {
  const texts = {
    running: '运行中',
    queued: '队列中',
    paused: '已暂停',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const getProgressColor = (progress) => {
  if (progress >= 80) return '#67c23a'
  if (progress >= 50) return '#e6a23c'
  return '#409eff'
}

const getPriorityType = (priority) => {
  const types = {
    high: 'danger',
    medium: 'warning',
    low: 'info'
  }
  return types[priority] || 'info'
}

const getPriorityText = (priority) => {
  const texts = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return texts[priority] || priority
}

const formatTime = (time) => {
  if (!time) return '-'
  return new Date(time).toLocaleTimeString()
}

const formatDuration = (duration) => {
  if (!duration) return '-'
  const seconds = Math.floor(duration / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`
  } else {
    return `${seconds}s`
  }
}

// 生命周期
onMounted(() => {
  // 加载保存的设置
  const savedSettings = localStorage.getItem('monitorSettings')
  if (savedSettings) {
    Object.assign(monitorSettings, JSON.parse(savedSettings))
  }
  
  // 初始化图表
  nextTick(() => {
    initCharts()
  })
  
  // 开始监控
  startMonitoring()
})

onUnmounted(() => {
  // 清理定时器
  if (monitorInterval) {
    clearInterval(monitorInterval)
  }
  
  // 销毁图表
  if (cpuChart) cpuChart.dispose()
  if (memoryChart) memoryChart.dispose()
  if (networkChart) networkChart.dispose()
  if (diskChart) diskChart.dispose()
})

// 监听窗口大小变化，重新调整图表
window.addEventListener('resize', () => {
  if (cpuChart) cpuChart.resize()
  if (memoryChart) memoryChart.resize()
  if (networkChart) networkChart.resize()
  if (diskChart) diskChart.resize()
})
</script>

<style scoped>
.test-execution-monitor {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.control-panel {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.monitor-status {
  display: flex;
  align-items: center;
  gap: 12px;
}

.last-update {
  color: #909399;
  font-size: 12px;
}

.status-overview {
  margin-bottom: 20px;
}

.status-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.status-card {
  border: none;
  border-radius: 8px;
  overflow: hidden;
}

.status-card.running {
  border-left: 4px solid #409eff;
}

.status-card.queued {
  border-left: 4px solid #909399;
}

.status-card.completed {
  border-left: 4px solid #67c23a;
}

.status-card.failed {
  border-left: 4px solid #f56c6c;
}

.card-content {
  display: flex;
  align-items: center;
  padding: 16px;
}

.status-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
}

.running .status-icon {
  background: rgba(64, 158, 255, 0.1);
  color: #409eff;
}

.queued .status-icon {
  background: rgba(144, 147, 153, 0.1);
  color: #909399;
}

.completed .status-icon {
  background: rgba(103, 194, 58, 0.1);
  color: #67c23a;
}

.failed .status-icon {
  background: rgba(245, 108, 108, 0.1);
  color: #f56c6c;
}

.status-info {
  flex: 1;
}

.status-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.status-label {
  color: #606266;
  font-size: 14px;
}

.status-trend {
  margin-left: 16px;
}

.trend-text {
  font-size: 12px;
  font-weight: 500;
}

.trend-text.success {
  color: #67c23a;
}

.trend-text.danger {
  color: #f56c6c;
}

.resource-monitoring {
  margin-bottom: 20px;
}

.monitoring-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.resource-card {
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.resource-chart {
  height: 120px;
  margin: 16px 0;
}

.resource-details {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.detail-item {
  text-align: center;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
}

.detail-label {
  display: block;
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.detail-value {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.execution-queue {
  margin-bottom: 20px;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.queue-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.queue-header h3 {
  margin: 0;
  color: #303133;
}

.task-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.task-name {
  font-weight: 500;
}

.progress-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-text {
  font-size: 12px;
  color: #606266;
  min-width: 35px;
}

.real-time-logs {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.logs-header h3 {
  margin: 0;
  color: #303133;
}

.logs-controls {
  display: flex;
  align-items: center;
}

.logs-container {
  height: 300px;
  overflow-y: auto;
  background: #1e1e1e;
  border-radius: 4px;
  padding: 12px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.log-entry {
  margin-bottom: 4px;
  display: flex;
  gap: 8px;
}

.log-time {
  color: #888;
  min-width: 80px;
}

.log-level {
  min-width: 50px;
  font-weight: bold;
}

.log-entry.info .log-level {
  color: #409eff;
}

.log-entry.warn .log-level {
  color: #e6a23c;
}

.log-entry.error .log-level {
  color: #f56c6c;
}

.log-entry.debug .log-level {
  color: #909399;
}

.log-source {
  color: #67c23a;
  min-width: 80px;
}

.log-message {
  color: #ddd;
  flex: 1;
}

.empty-logs {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #909399;
}

.task-detail {
  max-height: 500px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f5f7fa;
  border-radius: 4px;
}

.info-label {
  color: #606266;
  font-weight: 500;
}

.info-value {
  color: #303133;
}

.config-info {
  background: #f5f7fa;
  border-radius: 4px;
  padding: 12px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.text-muted {
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .control-panel {
    flex-direction: column;
    gap: 16px;
  }
  
  .status-cards {
    grid-template-columns: 1fr;
  }
  
  .monitoring-grid {
    grid-template-columns: 1fr;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .logs-controls {
    flex-wrap: wrap;
    gap: 8px;
  }
}
</style>