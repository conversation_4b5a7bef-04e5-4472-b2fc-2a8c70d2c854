import requests
import json

try:
    url = "http://localhost:8006/api/auth/frontend_login"
    data = {"username": "admin", "password": "admin123"}
    headers = {"Content-Type": "application/json"}
    
    print(f"发送请求到: {url}")
    print(f"请求数据: {data}")
    
    response = requests.post(url, json=data, headers=headers, timeout=10)
    
    print(f"响应状态码: {response.status_code}")
    print(f"响应内容: {response.text}")
    
except requests.exceptions.Timeout:
    print("请求超时")
except requests.exceptions.ConnectionError:
    print("连接错误")
except Exception as e:
    print(f"其他错误: {e}")