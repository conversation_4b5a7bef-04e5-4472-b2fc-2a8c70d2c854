#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查documents表的实际结构
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, inspect, text
from app.core.config import settings
from app.db.base_session import SessionLocal

def check_table_structure():
    """检查documents表的实际结构"""
    print("=== 检查documents表结构 ===")
    
    try:
        # 创建数据库连接
        db = SessionLocal()
        engine = db.bind
        inspector = inspect(engine)
        
        # 获取表信息
        if 'documents' not in inspector.get_table_names():
            print("❌ documents表不存在")
            return
            
        # 获取列信息
        columns = inspector.get_columns('documents')
        print(f"表中共有 {len(columns)} 列:")
        
        column_names = []
        for i, col in enumerate(columns, 1):
            print(f"{i:2d}. {col['name']:25s} {str(col['type']):20s} {'NOT NULL' if not col['nullable'] else 'NULL':8s}")
            column_names.append(col['name'])
            
        # 检查重复列
        duplicates = []
        seen = set()
        for name in column_names:
            if name in seen:
                duplicates.append(name)
            seen.add(name)
            
        if duplicates:
            print(f"\n❌ 发现重复列: {duplicates}")
        else:
            print("\n✅ 没有重复列")
            
        # 检查索引
        indexes = inspector.get_indexes('documents')
        print(f"\n表中共有 {len(indexes)} 个索引:")
        for idx in indexes:
            print(f"  - {idx['name']}: {idx['column_names']}")
            
        db.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_table_structure()