#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合系统测试脚本
验证所有修复后的功能
"""

import requests
import json
import time
from pathlib import Path
import logging
from datetime import datetime
from typing import Dict, List, Any
import sys
import os

# 添加后端路径以导入MockDataManager
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
try:
    from backend.app.core.mock_data_manager import get_test_endpoints_config, get_aggregated_endpoints_config
except ImportError:
    # 如果导入失败，使用默认配置
    def get_test_endpoints_config():
        return []
    def get_aggregated_endpoints_config():
        return []

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ComprehensiveSystemTester:
    """综合系统测试器"""
    
    def __init__(self):
        self.backend_url = "http://localhost:8006"
        self.frontend_url = "http://localhost:8080"
        self.test_results = []
        self.session = None
        
    def log_test_result(self, test_name: str, status: str, details: str = ""):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "status": status,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status_icon = "✅" if status == "success" else "❌" if status == "failed" else "⚠️"
        logger.info(f"{status_icon} {test_name}: {details}")
    
    def test_backend_health(self):
        """测试后端健康状态"""
        try:
            response = requests.get(f"{self.backend_url}/api/health", timeout=10)
            if response.status_code == 200:
                self.log_test_result("后端健康检查", "success", "后端服务正常运行")
                return True
            else:
                self.log_test_result("后端健康检查", "failed", f"状态码: {response.status_code}")
                return False
        except Exception as e:
            self.log_test_result("后端健康检查", "failed", f"连接失败: {str(e)}")
            return False
    
    def test_api_endpoints(self):
        """测试API端点"""
        # 从MockDataManager获取端点配置
        endpoint_configs = get_test_endpoints_config()
        
        # 如果无法获取配置，使用默认配置
        if not endpoint_configs:
            endpoint_configs = [
                {
                    "name": "API文档",
                    "url": "/docs",
                    "method": "GET"
                },
                {
                    "name": "问卷模板列表",
                    "url": "/api/templates/questionnaire-templates",
                    "method": "GET"
                },
                {
                    "name": "评估模板列表",
                    "url": "/api/templates/assessment-templates",
                    "method": "GET"
                },
                {
                    "name": "聚合API健康检查",
                    "url": "/api/v1/aggregated/health",
                    "method": "GET"
                },
                {
                    "name": "数据管理API",
                    "url": "/api/v1/data-management/health",
                    "method": "GET"
                }
            ]
        
        # 构建完整的端点列表
        endpoints = []
        for config in endpoint_configs:
            endpoints.append({
                "name": config["name"],
                "url": f"{self.backend_url}{config['url']}",
                "method": config.get("method", "GET")
            })
        
        for endpoint in endpoints:
            try:
                if endpoint["method"] == "GET":
                    response = requests.get(endpoint["url"], timeout=10)
                else:
                    response = requests.post(endpoint["url"], timeout=10)
                
                if response.status_code in [200, 201]:
                    self.log_test_result(
                        f"API端点: {endpoint['name']}",
                        "success",
                        f"状态码: {response.status_code}"
                    )
                else:
                    self.log_test_result(
                        f"API端点: {endpoint['name']}",
                        "warning",
                        f"状态码: {response.status_code}"
                    )
            except Exception as e:
                self.log_test_result(
                    f"API端点: {endpoint['name']}",
                    "failed",
                    f"请求失败: {str(e)}"
                )
    
    def test_database_connection(self):
        """测试数据库连接"""
        try:
            # 测试数据库健康检查API
            response = requests.get(f"{self.backend_url}/api/v1/health", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get("database", {}).get("status") == "healthy":
                    self.log_test_result("数据库连接", "success", "数据库连接正常")
                    return True
                else:
                    self.log_test_result("数据库连接", "warning", "数据库状态异常")
                    return False
            else:
                self.log_test_result("数据库连接", "failed", f"健康检查失败: {response.status_code}")
                return False
        except Exception as e:
            self.log_test_result("数据库连接", "failed", f"测试失败: {str(e)}")
            return False
    
    def test_frontend_accessibility(self):
        """测试前端可访问性"""
        try:
            response = requests.get(self.frontend_url, timeout=10)
            if response.status_code == 200:
                self.log_test_result("前端可访问性", "success", "前端页面可正常访问")
                return True
            else:
                self.log_test_result("前端可访问性", "failed", f"状态码: {response.status_code}")
                return False
        except Exception as e:
            self.log_test_result("前端可访问性", "failed", f"连接失败: {str(e)}")
            return False
    
    def test_authentication(self):
        """测试认证功能"""
        try:
            # 测试登录API
            login_data = {
                "username": "admin",
                "password": "admin123"
            }
            
            response = requests.post(
                f"{self.backend_url}/api/auth/login",
                json=login_data,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if "access_token" in data:
                    self.log_test_result("用户认证", "success", "登录功能正常")
                    return data["access_token"]
                else:
                    self.log_test_result("用户认证", "warning", "登录响应格式异常")
                    return None
            else:
                self.log_test_result("用户认证", "failed", f"登录失败: {response.status_code}")
                return None
        except Exception as e:
            self.log_test_result("用户认证", "failed", f"认证测试失败: {str(e)}")
            return None
    
    def test_questionnaire_templates(self, token=None):
        """测试问卷模板功能"""
        headers = {}
        if token:
            headers["Authorization"] = f"Bearer {token}"
        
        try:
            # 测试获取问卷模板列表
            response = requests.get(
                f"{self.backend_url}/api/templates/questionnaire-templates",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                template_count = len(data.get("items", [])) if isinstance(data, dict) else len(data)
                self.log_test_result(
                    "问卷模板功能",
                    "success",
                    f"成功获取 {template_count} 个问卷模板"
                )
                return True
            else:
                self.log_test_result(
                    "问卷模板功能",
                    "warning",
                    f"状态码: {response.status_code}"
                )
                return False
        except Exception as e:
            self.log_test_result("问卷模板功能", "failed", f"测试失败: {str(e)}")
            return False
    
    def test_assessment_templates(self, token=None):
        """测试评估模板功能"""
        headers = {}
        if token:
            headers["Authorization"] = f"Bearer {token}"
        
        try:
            # 测试获取评估模板列表
            response = requests.get(
                f"{self.backend_url}/api/templates/assessment-templates",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                template_count = len(data.get("items", [])) if isinstance(data, dict) else len(data)
                self.log_test_result(
                    "评估模板功能",
                    "success",
                    f"成功获取 {template_count} 个评估模板"
                )
                return True
            else:
                self.log_test_result(
                    "评估模板功能",
                    "warning",
                    f"状态码: {response.status_code}"
                )
                return False
        except Exception as e:
            self.log_test_result("评估模板功能", "failed", f"测试失败: {str(e)}")
            return False
    
    def test_aggregated_api(self, token=None):
        """测试聚合API功能"""
        headers = {}
        if token:
            headers["Authorization"] = f"Bearer {token}"
        
        # 从MockDataManager获取聚合API端点配置
        endpoint_configs = get_aggregated_endpoints_config()
        
        # 如果无法获取配置，使用默认配置
        if not endpoint_configs:
            endpoint_configs = [
                {
                    "name": "聚合API健康检查",
                    "url": "/api/v1/health"
                },
                {
                    "name": "问卷模板聚合",
                    "url": "/api/v1/questionnaire-templates"
                },
                {
                    "name": "评估模板聚合",
                    "url": "/api/v1/assessment-templates"
                }
            ]
        
        # 构建完整的端点列表
        endpoints = []
        for config in endpoint_configs:
            endpoints.append({
                "name": config["name"],
                "url": f"{self.backend_url}{config['url']}"
            })
        
        success_count = 0
        for endpoint in endpoints:
            try:
                response = requests.get(endpoint["url"], headers=headers, timeout=10)
                if response.status_code == 200:
                    self.log_test_result(
                        f"聚合API: {endpoint['name']}",
                        "success",
                        "端点响应正常"
                    )
                    success_count += 1
                else:
                    self.log_test_result(
                        f"聚合API: {endpoint['name']}",
                        "warning",
                        f"状态码: {response.status_code}"
                    )
            except Exception as e:
                self.log_test_result(
                    f"聚合API: {endpoint['name']}",
                    "failed",
                    f"请求失败: {str(e)}"
                )
        
        return success_count > 0
    
    def generate_report(self):
        """生成测试报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = Path(f"comprehensive_test_report_{timestamp}.json")
        
        # 统计测试结果
        total_tests = len(self.test_results)
        successful_tests = len([r for r in self.test_results if r["status"] == "success"])
        failed_tests = len([r for r in self.test_results if r["status"] == "failed"])
        warning_tests = len([r for r in self.test_results if r["status"] == "warning"])
        
        report = {
            "test_summary": {
                "total_tests": total_tests,
                "successful_tests": successful_tests,
                "failed_tests": failed_tests,
                "warning_tests": warning_tests,
                "success_rate": f"{(successful_tests/total_tests*100):.1f}%" if total_tests > 0 else "0%",
                "timestamp": datetime.now().isoformat()
            },
            "test_results": self.test_results,
            "recommendations": self.get_recommendations()
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"测试报告已生成: {report_file}")
        return report
    
    def get_recommendations(self):
        """获取修复建议"""
        recommendations = []
        
        failed_tests = [r for r in self.test_results if r["status"] == "failed"]
        warning_tests = [r for r in self.test_results if r["status"] == "warning"]
        
        if failed_tests:
            recommendations.append("检查失败的测试项目，确保相关服务正常运行")
        
        if warning_tests:
            recommendations.append("关注警告项目，可能需要进一步配置或优化")
        
        if any("后端" in r["test_name"] and r["status"] == "failed" for r in self.test_results):
            recommendations.append("重启后端服务并检查配置")
        
        if any("前端" in r["test_name"] and r["status"] == "failed" for r in self.test_results):
            recommendations.append("重启前端服务并检查构建")
        
        if any("数据库" in r["test_name"] and r["status"] == "failed" for r in self.test_results):
            recommendations.append("检查数据库连接和配置")
        
        return recommendations
    
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始综合系统测试...")
        
        # 1. 测试后端健康状态
        backend_healthy = self.test_backend_health()
        
        # 2. 测试前端可访问性
        self.test_frontend_accessibility()
        
        if backend_healthy:
            # 3. 测试API端点
            self.test_api_endpoints()
            
            # 4. 测试数据库连接
            self.test_database_connection()
            
            # 5. 测试认证功能
            token = self.test_authentication()
            
            # 6. 测试问卷模板功能
            self.test_questionnaire_templates(token)
            
            # 7. 测试评估模板功能
            self.test_assessment_templates(token)
            
            # 8. 测试聚合API功能
            self.test_aggregated_api(token)
        else:
            self.log_test_result("系统测试", "failed", "后端服务不可用，跳过其他测试")
        
        # 生成测试报告
        report = self.generate_report()
        
        # 打印摘要
        self.print_summary(report)
        
        return report
    
    def print_summary(self, report):
        """打印测试摘要"""
        summary = report["test_summary"]
        
        print("\n" + "="*60)
        print("综合系统测试摘要")
        print("="*60)
        print(f"总测试数: {summary['total_tests']}")
        print(f"成功: {summary['successful_tests']} ✅")
        print(f"警告: {summary['warning_tests']} ⚠️")
        print(f"失败: {summary['failed_tests']} ❌")
        print(f"成功率: {summary['success_rate']}")
        
        if report["recommendations"]:
            print("\n建议:")
            for i, rec in enumerate(report["recommendations"], 1):
                print(f"  {i}. {rec}")
        
        print("="*60)

def main():
    """主函数"""
    tester = ComprehensiveSystemTester()
    
    try:
        report = tester.run_all_tests()
        
        # 如果有失败的测试，返回非零退出码
        if report["test_summary"]["failed_tests"] > 0:
            return 1
        else:
            return 0
            
    except Exception as e:
        logger.error(f"测试执行失败: {e}")
        return 1

if __name__ == "__main__":
    exit(main())