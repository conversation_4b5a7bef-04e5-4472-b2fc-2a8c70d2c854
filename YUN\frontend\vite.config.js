import { defineConfig } from 'vite';
import vue from "@vitejs/plugin-vue";
import path from "path";

// 导入网络配置
const backendHost = "127.0.0.1"; // 使用IPv4地址而不是localhost
const backendPort = 8006; // 后端端口
const frontendPort = 8080; // 前端端口
const publicIP = "************"; // 服务器公网IP

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "src"),
    },
  },
  optimizeDeps: {
    include: ['vue', 'vue-router', 'pinia', 'axios', 'element-plus'],
    force: true,
    esbuildOptions: {
      target: 'es2020'
    }
  },
  esbuild: {
    target: 'es2020',
    keepNames: true
  },
  build: {
    chunkSizeWarningLimit: 1600,
    rollupOptions: {
      output: {
        manualChunks: {
          'element-plus': ['element-plus'],
          'vue-libs': ['vue', 'vue-router', 'pinia'],
        }
      }
    }
  },
  server: {
    port: frontendPort,
    host: "0.0.0.0", // 监听所有网络接口
    strictPort: true, // 如果端口已被占用，则退出而不是尝试下一个可用端口
    cors: true, // 启用CORS
    hmr: {
      protocol: 'ws',
      host: 'localhost',
      port: frontendPort,
      overlay: false
    },
    watch: {
      usePolling: true,
      interval: 1000
    },
    fs: {
      allow: [
        '/www/wwwroot/healthapp/frontend/',
        '.'
      ]
    },
    // 代理配置，将请求转发到后端服务器
    proxy: {
      // 处理所有API请求
      "/api": {
        target: `http://${backendHost}:${backendPort}`,
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path,
        configure: (proxy, _options) => {
          proxy.on("error", (err, _req, _res) => {
            console.log("proxy error", err);
          });
          proxy.on("proxyReq", (proxyReq, req, _res) => {
            console.log("Sending Request:", req.method, req.url);
          });
          proxy.on("proxyRes", (proxyRes, req, _res) => {
            console.log(
              "Received Response from:",
              req.url,
              proxyRes.statusCode
            );
          });
        },
      },
      // 处理直接的auth请求
      "/auth": {
        target: `http://${backendHost}:${backendPort}`,
        changeOrigin: true,
        secure: false,
        configure: (proxy, _options) => {
          proxy.on("error", (err, _req, _res) => {
            console.log("auth proxy error:", err);
          });
          proxy.on("proxyReq", (proxyReq, req, _res) => {
            console.log("Sending Auth Request:", req.method, req.url);
          });
          proxy.on("proxyRes", (proxyRes, req, _res) => {
            console.log(
              "Received Auth Response from:",
              req.url,
              proxyRes.statusCode
            );
          });
        },
      }
    },
  },
});
