<template>
  <div class="test-data-generator">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>测试数据生成器</h2>
      <p class="page-description">生成各种类型的测试数据，支持用户、评估、问卷等数据模拟</p>
    </div>

    <!-- 快速操作栏 -->
    <div class="quick-actions">
      <div class="action-buttons">
        <el-button type="primary" size="large" @click="generateAllData" :loading="isGenerating">
          <el-icon><Magic /></el-icon>
          一键生成全部数据
        </el-button>
        <el-button type="success" size="large" @click="showBatchGenerateDialog = true">
          <el-icon><DocumentAdd /></el-icon>
          批量生成
        </el-button>
        <el-button size="large" @click="clearAllData" :disabled="isGenerating">
          <el-icon><Delete /></el-icon>
          清空数据
        </el-button>
        <el-button size="large" @click="exportData">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>
      
      <div class="data-stats">
        <div class="stat-item">
          <el-icon class="stat-icon"><User /></el-icon>
          <span class="stat-text">用户: {{ dataStats.users }}</span>
        </div>
        <div class="stat-item">
          <el-icon class="stat-icon"><Document /></el-icon>
          <span class="stat-text">评估: {{ dataStats.assessments }}</span>
        </div>
        <div class="stat-item">
          <el-icon class="stat-icon"><List /></el-icon>
          <span class="stat-text">问卷: {{ dataStats.questionnaires }}</span>
        </div>
      </div>
    </div>

    <!-- 数据生成器列表 -->
    <div class="generators-grid">
      <!-- 用户数据生成器 -->
      <div class="generator-card">
        <div class="card-header">
          <div class="header-left">
            <el-icon class="card-icon"><User /></el-icon>
            <div class="header-content">
              <h3>用户数据生成器</h3>
              <p>生成测试用户账户和个人信息</p>
            </div>
          </div>
          <el-switch v-model="generators.users.enabled" />
        </div>
        
        <div class="card-body" v-if="generators.users.enabled">
          <div class="form-grid">
            <el-form-item label="生成数量">
              <el-input-number 
                v-model="generators.users.count" 
                :min="1" 
                :max="1000" 
                style="width: 100%;"
              />
            </el-form-item>
            
            <el-form-item label="用户类型">
              <el-select v-model="generators.users.userType" multiple style="width: 100%;">
                <el-option label="管理员" value="admin" />
                <el-option label="医生" value="doctor" />
                <el-option label="患者" value="patient" />
                <el-option label="访客" value="guest" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="年龄范围">
              <div class="age-range">
                <el-input-number v-model="generators.users.ageRange.min" :min="1" :max="100" />
                <span class="range-separator">-</span>
                <el-input-number v-model="generators.users.ageRange.max" :min="1" :max="100" />
              </div>
            </el-form-item>
            
            <el-form-item label="性别分布">
              <el-radio-group v-model="generators.users.genderDistribution">
                <el-radio label="random">随机</el-radio>
                <el-radio label="equal">均等</el-radio>
                <el-radio label="custom">自定义</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="包含头像" v-if="generators.users.genderDistribution === 'custom'">
              <el-switch v-model="generators.users.includeAvatar" />
            </el-form-item>
            
            <el-form-item label="真实姓名">
              <el-switch v-model="generators.users.realNames" />
            </el-form-item>
          </div>
          
          <div class="card-actions">
            <el-button type="primary" @click="generateUsers" :loading="generators.users.loading">
              <el-icon><VideoPlay /></el-icon>
              生成用户数据
            </el-button>
            <el-button @click="previewUsers">
              <el-icon><View /></el-icon>
              预览
            </el-button>
          </div>
        </div>
      </div>

      <!-- 评估数据生成器 -->
      <div class="generator-card">
        <div class="card-header">
          <div class="header-left">
            <el-icon class="card-icon"><Document /></el-icon>
            <div class="header-content">
              <h3>评估数据生成器</h3>
              <p>生成健康评估记录和结果</p>
            </div>
          </div>
          <el-switch v-model="generators.assessments.enabled" />
        </div>
        
        <div class="card-body" v-if="generators.assessments.enabled">
          <div class="form-grid">
            <el-form-item label="生成数量">
              <el-input-number 
                v-model="generators.assessments.count" 
                :min="1" 
                :max="5000" 
                style="width: 100%;"
              />
            </el-form-item>
            
            <el-form-item label="评估类型">
              <el-select v-model="generators.assessments.types" multiple style="width: 100%;">
                <el-option label="心理健康" value="mental" />
                <el-option label="身体健康" value="physical" />
                <el-option label="营养评估" value="nutrition" />
                <el-option label="睡眠质量" value="sleep" />
                <el-option label="压力水平" value="stress" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="时间范围">
              <el-date-picker
                v-model="generators.assessments.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 100%;"
              />
            </el-form-item>
            
            <el-form-item label="完成状态">
              <el-select v-model="generators.assessments.status" multiple style="width: 100%;">
                <el-option label="已完成" value="completed" />
                <el-option label="进行中" value="in_progress" />
                <el-option label="已暂停" value="paused" />
                <el-option label="已取消" value="cancelled" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="分数分布">
              <el-radio-group v-model="generators.assessments.scoreDistribution">
                <el-radio label="normal">正态分布</el-radio>
                <el-radio label="uniform">均匀分布</el-radio>
                <el-radio label="skewed">偏态分布</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="包含详细报告">
              <el-switch v-model="generators.assessments.includeReports" />
            </el-form-item>
          </div>
          
          <div class="card-actions">
            <el-button type="primary" @click="generateAssessments" :loading="generators.assessments.loading">
                <el-icon><VideoPlay /></el-icon>
                生成评估数据
              </el-button>
            <el-button @click="previewAssessments">
              <el-icon><View /></el-icon>
              预览
            </el-button>
          </div>
        </div>
      </div>

      <!-- 问卷数据生成器 -->
      <div class="generator-card">
        <div class="card-header">
          <div class="header-left">
            <el-icon class="card-icon"><List /></el-icon>
            <div class="header-content">
              <h3>问卷数据生成器</h3>
              <p>生成问卷模板和回答记录</p>
            </div>
          </div>
          <el-switch v-model="generators.questionnaires.enabled" />
        </div>
        
        <div class="card-body" v-if="generators.questionnaires.enabled">
          <div class="form-grid">
            <el-form-item label="问卷数量">
              <el-input-number 
                v-model="generators.questionnaires.count" 
                :min="1" 
                :max="100" 
                style="width: 100%;"
              />
            </el-form-item>
            
            <el-form-item label="问题数量">
              <div class="question-range">
                <el-input-number v-model="generators.questionnaires.questionRange.min" :min="1" :max="100" />
                <span class="range-separator">-</span>
                <el-input-number v-model="generators.questionnaires.questionRange.max" :min="1" :max="100" />
              </div>
            </el-form-item>
            
            <el-form-item label="问题类型">
              <el-select v-model="generators.questionnaires.questionTypes" multiple style="width: 100%;">
                <el-option label="单选题" value="single_choice" />
                <el-option label="多选题" value="multiple_choice" />
                <el-option label="文本题" value="text" />
                <el-option label="评分题" value="rating" />
                <el-option label="是非题" value="boolean" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="回答数量">
              <el-input-number 
                v-model="generators.questionnaires.responseCount" 
                :min="0" 
                :max="10000" 
                style="width: 100%;"
              />
            </el-form-item>
            
            <el-form-item label="完成率">
              <el-slider 
                v-model="generators.questionnaires.completionRate" 
                :min="0" 
                :max="100" 
                show-input
                style="width: 100%;"
              />
            </el-form-item>
            
            <el-form-item label="包含逻辑跳转">
              <el-switch v-model="generators.questionnaires.includeLogic" />
            </el-form-item>
          </div>
          
          <div class="card-actions">
            <el-button type="primary" @click="generateQuestionnaires" :loading="generators.questionnaires.loading">
                <el-icon><VideoPlay /></el-icon>
                生成问卷数据
              </el-button>
            <el-button @click="previewQuestionnaires">
              <el-icon><View /></el-icon>
              预览
            </el-button>
          </div>
        </div>
      </div>

      <!-- API测试数据生成器 -->
      <div class="generator-card">
        <div class="card-header">
          <div class="header-left">
            <el-icon class="card-icon"><Link /></el-icon>
            <div class="header-content">
              <h3>API测试数据生成器</h3>
              <p>生成API接口测试用例和响应数据</p>
            </div>
          </div>
          <el-switch v-model="generators.apiTests.enabled" />
        </div>
        
        <div class="card-body" v-if="generators.apiTests.enabled">
          <div class="form-grid">
            <el-form-item label="接口数量">
              <el-input-number 
                v-model="generators.apiTests.count" 
                :min="1" 
                :max="200" 
                style="width: 100%;"
              />
            </el-form-item>
            
            <el-form-item label="HTTP方法">
              <el-select v-model="generators.apiTests.methods" multiple style="width: 100%;">
                <el-option label="GET" value="GET" />
                <el-option label="POST" value="POST" />
                <el-option label="PUT" value="PUT" />
                <el-option label="DELETE" value="DELETE" />
                <el-option label="PATCH" value="PATCH" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="响应状态">
              <el-select v-model="generators.apiTests.statusCodes" multiple style="width: 100%;">
                <el-option label="200 OK" value="200" />
                <el-option label="201 Created" value="201" />
                <el-option label="400 Bad Request" value="400" />
                <el-option label="401 Unauthorized" value="401" />
                <el-option label="404 Not Found" value="404" />
                <el-option label="500 Server Error" value="500" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="数据格式">
              <el-select v-model="generators.apiTests.dataFormat" style="width: 100%;">
                <el-option label="JSON" value="json" />
                <el-option label="XML" value="xml" />
                <el-option label="Form Data" value="form" />
                <el-option label="Plain Text" value="text" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="包含认证">
              <el-switch v-model="generators.apiTests.includeAuth" />
            </el-form-item>
            
            <el-form-item label="生成Mock数据">
              <el-switch v-model="generators.apiTests.generateMock" />
            </el-form-item>
          </div>
          
          <div class="card-actions">
            <el-button type="primary" @click="generateApiTests" :loading="generators.apiTests.loading">
                <el-icon><VideoPlay /></el-icon>
                生成API测试
              </el-button>
            <el-button @click="previewApiTests">
              <el-icon><View /></el-icon>
              预览
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 生成历史 -->
    <div class="generation-history">
      <div class="section-header">
        <h3>生成历史</h3>
        <div class="history-controls">
          <el-input
            v-model="historySearch"
            placeholder="搜索历史记录..."
            prefix-icon="Search"
            style="width: 250px;"
            clearable
          />
          <el-button @click="refreshHistory" style="margin-left: 10px;">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
          <el-button @click="clearHistory">
            <el-icon><Delete /></el-icon>
            清空历史
          </el-button>
        </div>
      </div>
      
      <el-table :data="filteredHistory" style="width: 100%" stripe>
        <el-table-column prop="timestamp" label="生成时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.timestamp) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="type" label="数据类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeColor(row.type)" size="small">
              {{ getTypeText(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="count" label="生成数量" width="100" />
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="duration" label="耗时" width="100">
          <template #default="{ row }">
            {{ formatDuration(row.duration) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="size" label="数据大小" width="120">
          <template #default="{ row }">
            {{ formatFileSize(row.size) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="description" label="描述" min-width="200" />
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="viewHistoryDetail(row)">
              <el-icon><View /></el-icon>
              详情
            </el-button>
            <el-button type="text" size="small" @click="downloadHistory(row)">
              <el-icon><Download /></el-icon>
              下载
            </el-button>
            <el-button type="text" size="small" @click="regenerateData(row)">
              <el-icon><Refresh /></el-icon>
              重新生成
            </el-button>
            <el-button type="text" size="small" @click="deleteHistory(row)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 批量生成对话框 -->
    <el-dialog
      v-model="showBatchGenerateDialog"
      title="批量生成配置"
      width="800px"
    >
      <div class="batch-config">
        <el-form :model="batchConfig" label-width="120px">
          <el-form-item label="生成方案">
            <el-select v-model="batchConfig.template" style="width: 100%;">
              <el-option label="完整测试环境" value="full" />
              <el-option label="基础数据集" value="basic" />
              <el-option label="性能测试数据" value="performance" />
              <el-option label="自定义配置" value="custom" />
            </el-select>
          </el-form-item>
          
          <div v-if="batchConfig.template === 'custom'">
            <el-form-item label="用户数量">
              <el-input-number v-model="batchConfig.users" :min="0" :max="10000" style="width: 100%;" />
            </el-form-item>
            
            <el-form-item label="评估数量">
              <el-input-number v-model="batchConfig.assessments" :min="0" :max="50000" style="width: 100%;" />
            </el-form-item>
            
            <el-form-item label="问卷数量">
              <el-input-number v-model="batchConfig.questionnaires" :min="0" :max="1000" style="width: 100%;" />
            </el-form-item>
            
            <el-form-item label="API测试数量">
              <el-input-number v-model="batchConfig.apiTests" :min="0" :max="500" style="width: 100%;" />
            </el-form-item>
          </div>
          
          <el-form-item label="输出格式">
            <el-checkbox-group v-model="batchConfig.outputFormats">
              <el-checkbox label="json">JSON</el-checkbox>
              <el-checkbox label="csv">CSV</el-checkbox>
              <el-checkbox label="sql">SQL</el-checkbox>
              <el-checkbox label="excel">Excel</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          
          <el-form-item label="保存位置">
            <el-input v-model="batchConfig.outputPath" placeholder="选择保存目录">
              <template #append>
                <el-button @click="selectOutputPath">
                  <el-icon><Folder /></el-icon>
                  浏览
                </el-button>
              </template>
            </el-input>
          </el-form-item>
          
          <el-form-item label="生成选项">
            <el-checkbox-group v-model="batchConfig.options">
              <el-checkbox label="compress">压缩输出</el-checkbox>
              <el-checkbox label="validate">数据验证</el-checkbox>
              <el-checkbox label="backup">创建备份</el-checkbox>
              <el-checkbox label="notify">完成通知</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showBatchGenerateDialog = false">取消</el-button>
          <el-button type="primary" @click="startBatchGeneration" :loading="batchGenerating">
            开始生成
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 数据预览对话框 -->
    <el-dialog
      v-model="showPreviewDialog"
      :title="`数据预览 - ${previewData.type}`"
      width="1000px"
    >
      <div class="preview-content">
        <div class="preview-header">
          <div class="preview-info">
            <span>数据类型: {{ previewData.type }}</span>
            <span>记录数: {{ previewData.records?.length || 0 }}</span>
            <span>生成时间: {{ formatDateTime(previewData.timestamp) }}</span>
          </div>
          <div class="preview-actions">
            <el-button size="small" @click="exportPreviewData">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
            <el-button size="small" @click="copyPreviewData">
              <el-icon><CopyDocument /></el-icon>
              复制
            </el-button>
          </div>
        </div>
        
        <div class="preview-data">
          <el-table :data="previewData.records" style="width: 100%" max-height="400">
            <el-table-column 
              v-for="column in previewColumns" 
              :key="column.prop" 
              :prop="column.prop" 
              :label="column.label" 
              :width="column.width"
              show-overflow-tooltip
            />
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Magic, DocumentAdd, Delete, Download, User, Document, List, Link,
  VideoPlay, View, Search, Refresh, Folder, CopyDocument
} from '@element-plus/icons-vue'

// 响应式数据
const isGenerating = ref(false)
const batchGenerating = ref(false)
const historySearch = ref('')
const showBatchGenerateDialog = ref(false)
const showPreviewDialog = ref(false)

// 数据统计
const dataStats = reactive({
  users: 0,
  assessments: 0,
  questionnaires: 0,
  apiTests: 0
})

// 生成器配置
const generators = reactive({
  users: {
    enabled: true,
    loading: false,
    count: 50,
    userType: ['patient', 'doctor'],
    ageRange: { min: 18, max: 80 },
    genderDistribution: 'random',
    includeAvatar: true,
    realNames: true
  },
  assessments: {
    enabled: true,
    loading: false,
    count: 200,
    types: ['mental', 'physical'],
    dateRange: [new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), new Date()],
    status: ['completed', 'in_progress'],
    scoreDistribution: 'normal',
    includeReports: true
  },
  questionnaires: {
    enabled: true,
    loading: false,
    count: 20,
    questionRange: { min: 5, max: 30 },
    questionTypes: ['single_choice', 'multiple_choice', 'rating'],
    responseCount: 100,
    completionRate: 85,
    includeLogic: false
  },
  apiTests: {
    enabled: false,
    loading: false,
    count: 50,
    methods: ['GET', 'POST', 'PUT', 'DELETE'],
    statusCodes: ['200', '201', '400', '404'],
    dataFormat: 'json',
    includeAuth: true,
    generateMock: true
  }
})

// 批量生成配置
const batchConfig = reactive({
  template: 'basic',
  users: 100,
  assessments: 500,
  questionnaires: 50,
  apiTests: 100,
  outputFormats: ['json'],
  outputPath: '',
  options: ['validate']
})

// 生成历史
const generationHistory = ref([
  {
    id: 1,
    timestamp: new Date('2024-01-15T10:30:00'),
    type: 'users',
    count: 50,
    status: 'completed',
    duration: 1200,
    size: 1024 * 50,
    description: '生成50个测试用户'
  },
  {
    id: 2,
    timestamp: new Date('2024-01-15T09:15:00'),
    type: 'assessments',
    count: 200,
    status: 'completed',
    duration: 3600,
    size: 1024 * 200,
    description: '生成200个健康评估记录'
  },
  {
    id: 3,
    timestamp: new Date('2024-01-14T16:45:00'),
    type: 'questionnaires',
    count: 20,
    status: 'failed',
    duration: 800,
    size: 0,
    description: '生成问卷数据失败'
  }
])

// 预览数据
const previewData = reactive({
  type: '',
  timestamp: null,
  records: []
})

// 计算属性
const filteredHistory = computed(() => {
  if (!historySearch.value) return generationHistory.value
  
  return generationHistory.value.filter(item => 
    item.description.toLowerCase().includes(historySearch.value.toLowerCase()) ||
    item.type.toLowerCase().includes(historySearch.value.toLowerCase())
  )
})

const previewColumns = computed(() => {
  if (!previewData.records || previewData.records.length === 0) return []
  
  const firstRecord = previewData.records[0]
  return Object.keys(firstRecord).map(key => ({
    prop: key,
    label: key.charAt(0).toUpperCase() + key.slice(1),
    width: key.length > 10 ? 150 : 120
  }))
})

// 方法
const generateAllData = async () => {
  try {
    isGenerating.value = true
    ElMessage.info('开始生成全部测试数据...')
    
    // 按顺序生成各类数据
    if (generators.users.enabled) {
      await generateUsers()
    }
    
    if (generators.assessments.enabled) {
      await generateAssessments()
    }
    
    if (generators.questionnaires.enabled) {
      await generateQuestionnaires()
    }
    
    if (generators.apiTests.enabled) {
      await generateApiTests()
    }
    
    ElMessage.success('全部测试数据生成完成')
  } catch (error) {
    ElMessage.error('生成失败: ' + error.message)
  } finally {
    isGenerating.value = false
  }
}

const generateUsers = async () => {
  try {
    generators.users.loading = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 更新统计
    dataStats.users += generators.users.count
    
    // 添加历史记录
    addHistoryRecord({
      type: 'users',
      count: generators.users.count,
      status: 'completed',
      duration: 2000,
      size: generators.users.count * 1024,
      description: `生成${generators.users.count}个用户数据`
    })
    
    ElMessage.success(`成功生成${generators.users.count}个用户`)
  } catch (error) {
    ElMessage.error('用户数据生成失败: ' + error.message)
  } finally {
    generators.users.loading = false
  }
}

const generateAssessments = async () => {
  try {
    generators.assessments.loading = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    // 更新统计
    dataStats.assessments += generators.assessments.count
    
    // 添加历史记录
    addHistoryRecord({
      type: 'assessments',
      count: generators.assessments.count,
      status: 'completed',
      duration: 3000,
      size: generators.assessments.count * 2048,
      description: `生成${generators.assessments.count}个评估记录`
    })
    
    ElMessage.success(`成功生成${generators.assessments.count}个评估记录`)
  } catch (error) {
    ElMessage.error('评估数据生成失败: ' + error.message)
  } finally {
    generators.assessments.loading = false
  }
}

const generateQuestionnaires = async () => {
  try {
    generators.questionnaires.loading = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // 更新统计
    dataStats.questionnaires += generators.questionnaires.count
    
    // 添加历史记录
    addHistoryRecord({
      type: 'questionnaires',
      count: generators.questionnaires.count,
      status: 'completed',
      duration: 1500,
      size: generators.questionnaires.count * 4096,
      description: `生成${generators.questionnaires.count}个问卷`
    })
    
    ElMessage.success(`成功生成${generators.questionnaires.count}个问卷`)
  } catch (error) {
    ElMessage.error('问卷数据生成失败: ' + error.message)
  } finally {
    generators.questionnaires.loading = false
  }
}

const generateApiTests = async () => {
  try {
    generators.apiTests.loading = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 更新统计
    dataStats.apiTests += generators.apiTests.count
    
    // 添加历史记录
    addHistoryRecord({
      type: 'apiTests',
      count: generators.apiTests.count,
      status: 'completed',
      duration: 1000,
      size: generators.apiTests.count * 512,
      description: `生成${generators.apiTests.count}个API测试用例`
    })
    
    ElMessage.success(`成功生成${generators.apiTests.count}个API测试用例`)
  } catch (error) {
    ElMessage.error('API测试数据生成失败: ' + error.message)
  } finally {
    generators.apiTests.loading = false
  }
}

const previewUsers = () => {
  // 生成预览数据
  const sampleUsers = Array.from({ length: Math.min(10, generators.users.count) }, (_, i) => ({
    id: i + 1,
    username: `user${i + 1}`,
    email: `user${i + 1}@example.com`,
    name: `测试用户${i + 1}`,
    age: Math.floor(Math.random() * (generators.users.ageRange.max - generators.users.ageRange.min)) + generators.users.ageRange.min,
    gender: Math.random() > 0.5 ? '男' : '女',
    type: generators.users.userType[Math.floor(Math.random() * generators.users.userType.length)]
  }))
  
  previewData.type = '用户数据'
  previewData.timestamp = new Date()
  previewData.records = sampleUsers
  showPreviewDialog.value = true
}

const previewAssessments = () => {
  // 生成预览数据
  const sampleAssessments = Array.from({ length: Math.min(10, generators.assessments.count) }, (_, i) => ({
    id: i + 1,
    userId: Math.floor(Math.random() * 100) + 1,
    type: generators.assessments.types[Math.floor(Math.random() * generators.assessments.types.length)],
    score: Math.floor(Math.random() * 100),
    status: generators.assessments.status[Math.floor(Math.random() * generators.assessments.status.length)],
    createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
  }))
  
  previewData.type = '评估数据'
  previewData.timestamp = new Date()
  previewData.records = sampleAssessments
  showPreviewDialog.value = true
}

const previewQuestionnaires = () => {
  // 生成预览数据
  const sampleQuestionnaires = Array.from({ length: Math.min(10, generators.questionnaires.count) }, (_, i) => ({
    id: i + 1,
    title: `问卷${i + 1}`,
    description: `这是第${i + 1}个测试问卷`,
    questionCount: Math.floor(Math.random() * (generators.questionnaires.questionRange.max - generators.questionnaires.questionRange.min)) + generators.questionnaires.questionRange.min,
    responseCount: Math.floor(Math.random() * generators.questionnaires.responseCount),
    status: '已发布'
  }))
  
  previewData.type = '问卷数据'
  previewData.timestamp = new Date()
  previewData.records = sampleQuestionnaires
  showPreviewDialog.value = true
}

const previewApiTests = () => {
  // 生成预览数据
  const sampleApiTests = Array.from({ length: Math.min(10, generators.apiTests.count) }, (_, i) => ({
    id: i + 1,
    name: `API测试${i + 1}`,
    method: generators.apiTests.methods[Math.floor(Math.random() * generators.apiTests.methods.length)],
    endpoint: `/api/test${i + 1}`,
    statusCode: generators.apiTests.statusCodes[Math.floor(Math.random() * generators.apiTests.statusCodes.length)],
    responseTime: Math.floor(Math.random() * 1000) + 100
  }))
  
  previewData.type = 'API测试数据'
  previewData.timestamp = new Date()
  previewData.records = sampleApiTests
  showPreviewDialog.value = true
}

const clearAllData = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有生成的测试数据吗？此操作不可恢复。',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 重置统计
    Object.assign(dataStats, {
      users: 0,
      assessments: 0,
      questionnaires: 0,
      apiTests: 0
    })
    
    ElMessage.success('所有测试数据已清空')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('清空失败: ' + error.message)
    }
  }
}

const exportData = () => {
  // 导出数据逻辑
  ElMessage.info('数据导出功能开发中...')
}

const startBatchGeneration = async () => {
  try {
    batchGenerating.value = true
    
    // 根据模板设置配置
    if (batchConfig.template !== 'custom') {
      const templates = {
        full: { users: 500, assessments: 2000, questionnaires: 100, apiTests: 200 },
        basic: { users: 100, assessments: 500, questionnaires: 50, apiTests: 100 },
        performance: { users: 1000, assessments: 10000, questionnaires: 200, apiTests: 500 }
      }
      
      Object.assign(batchConfig, templates[batchConfig.template])
    }
    
    ElMessage.info('开始批量生成数据...')
    
    // 模拟批量生成过程
    await new Promise(resolve => setTimeout(resolve, 5000))
    
    ElMessage.success('批量生成完成')
    showBatchGenerateDialog.value = false
  } catch (error) {
    ElMessage.error('批量生成失败: ' + error.message)
  } finally {
    batchGenerating.value = false
  }
}

const selectOutputPath = () => {
  // 选择输出路径
  ElMessage.info('文件选择功能开发中...')
}

const addHistoryRecord = (record) => {
  const newRecord = {
    id: Date.now(),
    timestamp: new Date(),
    ...record
  }
  
  generationHistory.value.unshift(newRecord)
  
  // 限制历史记录数量
  if (generationHistory.value.length > 100) {
    generationHistory.value = generationHistory.value.slice(0, 100)
  }
}

const refreshHistory = () => {
  ElMessage.success('历史记录已刷新')
}

const clearHistory = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有历史记录吗？',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    generationHistory.value = []
    ElMessage.success('历史记录已清空')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('清空失败: ' + error.message)
    }
  }
}

const viewHistoryDetail = (record) => {
  ElMessage.info('历史详情功能开发中...')
}

const downloadHistory = (record) => {
  ElMessage.info('下载功能开发中...')
}

const regenerateData = (record) => {
  ElMessage.info('重新生成功能开发中...')
}

const deleteHistory = async (record) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除这条历史记录吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = generationHistory.value.findIndex(item => item.id === record.id)
    if (index > -1) {
      generationHistory.value.splice(index, 1)
    }
    
    ElMessage.success('历史记录已删除')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error.message)
    }
  }
}

const exportPreviewData = () => {
  // 导出预览数据
  const dataStr = JSON.stringify(previewData.records, null, 2)
  const blob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  
  const link = document.createElement('a')
  link.href = url
  link.download = `${previewData.type}_preview.json`
  link.click()
  
  URL.revokeObjectURL(url)
  ElMessage.success('预览数据已导出')
}

const copyPreviewData = () => {
  const dataStr = JSON.stringify(previewData.records, null, 2)
  navigator.clipboard.writeText(dataStr).then(() => {
    ElMessage.success('预览数据已复制到剪贴板')
  }).catch(() => {
    ElMessage.error('复制失败')
  })
}

// 工具函数
const getTypeColor = (type) => {
  const colors = {
    users: 'primary',
    assessments: 'success',
    questionnaires: 'warning',
    apiTests: 'info'
  }
  return colors[type] || ''
}

const getTypeText = (type) => {
  const texts = {
    users: '用户数据',
    assessments: '评估数据',
    questionnaires: '问卷数据',
    apiTests: 'API测试'
  }
  return texts[type] || type
}

const getStatusColor = (status) => {
  const colors = {
    completed: 'success',
    failed: 'danger',
    running: 'warning'
  }
  return colors[status] || ''
}

const getStatusText = (status) => {
  const texts = {
    completed: '已完成',
    failed: '失败',
    running: '运行中'
  }
  return texts[status] || status
}

const formatDateTime = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatDuration = (ms) => {
  if (!ms) return '0s'
  
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`
  } else {
    return `${seconds}s`
  }
}

const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  
  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
}

// 生命周期
onMounted(() => {
  // 初始化数据统计
  dataStats.users = 150
  dataStats.assessments = 680
  dataStats.questionnaires = 45
  dataStats.apiTests = 120
})
</script>

<style scoped>
.test-data-generator {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.quick-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.data-stats {
  display: flex;
  gap: 24px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stat-icon {
  font-size: 16px;
  color: #409eff;
}

.stat-text {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.generators-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.generator-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.generator-card:hover {
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.card-icon {
  font-size: 24px;
  color: #409eff;
}

.header-content h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  color: #303133;
}

.header-content p {
  margin: 0;
  font-size: 13px;
  color: #909399;
}

.card-body {
  margin-bottom: 20px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.age-range,
.question-range {
  display: flex;
  align-items: center;
  gap: 8px;
}

.range-separator {
  color: #909399;
  font-weight: 500;
}

.card-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.generation-history {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.history-controls {
  display: flex;
  align-items: center;
}

.batch-config {
  padding: 0;
}

.preview-content {
  padding: 0;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.preview-info {
  display: flex;
  gap: 16px;
  font-size: 13px;
  color: #606266;
}

.preview-actions {
  display: flex;
  gap: 8px;
}

.preview-data {
  max-height: 400px;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .quick-actions {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .action-buttons {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .data-stats {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .generators-grid {
    grid-template-columns: 1fr;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .history-controls {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .preview-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .preview-info {
    justify-content: center;
    flex-wrap: wrap;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .test-data-generator {
    background-color: #1a1a1a;
    color: #e4e7ed;
  }
  
  .quick-actions,
  .generator-card,
  .generation-history {
    background: #2d2d2d;
    border: 1px solid #404040;
  }
  
  .card-header {
    border-bottom-color: #404040;
  }
  
  .preview-header {
    border-bottom-color: #404040;
  }
}
</style>