<template>
  <div class="test-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>测试管理</h2>
      <p class="page-description">统一管理和执行各种类型的测试，包括单元测试、集成测试、端到端测试等</p>
    </div>

    <!-- 快速操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" icon="Play" @click="runAllTests" :loading="runningAll">
          运行全部测试
        </el-button>
        <el-button icon="Refresh" @click="refreshTestData">
          刷新数据
        </el-button>
        <el-button icon="Setting" @click="showSettingsDialog = true">
          测试设置
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索测试套件或用例"
          prefix-icon="Search"
          style="width: 300px; margin-right: 10px"
          clearable
        />
        <el-select v-model="filterStatus" placeholder="状态筛选" style="width: 120px; margin-right: 10px">
          <el-option label="全部" value="" />
          <el-option label="通过" value="passed" />
          <el-option label="失败" value="failed" />
          <el-option label="跳过" value="skipped" />
          <el-option label="运行中" value="running" />
        </el-select>
        <el-select v-model="filterType" placeholder="类型筛选" style="width: 120px">
          <el-option label="全部" value="" />
          <el-option label="单元测试" value="unit" />
          <el-option label="集成测试" value="integration" />
          <el-option label="端到端" value="e2e" />
          <el-option label="API测试" value="api" />
          <el-option label="性能测试" value="performance" />
        </el-select>
      </div>
    </div>

    <!-- 测试概览统计 -->
    <div class="stats-overview">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon success">
              <el-icon><SuccessFilled /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ testStats.passed }}</div>
              <div class="stat-label">通过测试</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon danger">
              <el-icon><CircleCloseFilled /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ testStats.failed }}</div>
              <div class="stat-label">失败测试</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon warning">
              <el-icon><WarningFilled /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ testStats.skipped }}</div>
              <div class="stat-label">跳过测试</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon info">
              <el-icon><InfoFilled /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ testStats.total }}</div>
              <div class="stat-label">总测试数</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 测试套件列表 -->
    <div class="test-suites">
      <div class="section-header">
        <h3>测试套件</h3>
        <el-button type="primary" size="small" @click="createTestSuite">
          <el-icon><Plus /></el-icon>
          新建套件
        </el-button>
      </div>
      
      <div class="suites-grid">
        <div
          v-for="suite in filteredTestSuites"
          :key="suite.id"
          class="suite-card"
          :class="{ running: suite.status === 'running' }"
        >
          <div class="suite-header">
            <div class="suite-title">
              <h4>{{ suite.name }}</h4>
              <el-tag :type="getSuiteStatusType(suite.status)" size="small">
                {{ getSuiteStatusText(suite.status) }}
              </el-tag>
            </div>
            <div class="suite-actions">
              <el-button
                type="primary"
                icon="VideoPlay"
                size="small"
                @click="runTestSuite(suite)"
                :loading="suite.status === 'running'"
              >
                运行
              </el-button>
              <el-dropdown @command="(command) => handleSuiteAction(command, suite)">
                <el-button icon="More" size="small" />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="edit">编辑</el-dropdown-item>
                    <el-dropdown-item command="clone">克隆</el-dropdown-item>
                    <el-dropdown-item command="export">导出</el-dropdown-item>
                    <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
          
          <div class="suite-description">
            <p>{{ suite.description || '暂无描述' }}</p>
          </div>
          
          <div class="suite-stats">
            <div class="stat-item">
              <span class="label">测试用例:</span>
              <span class="value">{{ suite.testCount }}</span>
            </div>
            <div class="stat-item">
              <span class="label">成功率:</span>
              <span class="value">{{ suite.successRate }}%</span>
            </div>
            <div class="stat-item">
              <span class="label">平均耗时:</span>
              <span class="value">{{ suite.avgDuration }}ms</span>
            </div>
          </div>
          
          <div class="suite-progress" v-if="suite.status === 'running'">
            <el-progress :percentage="suite.progress" :show-text="false" />
            <span class="progress-text">{{ suite.progress }}%</span>
          </div>
          
          <div class="suite-footer">
            <div class="suite-meta">
              <span>最后运行: {{ formatDate(suite.lastRun) }}</span>
            </div>
            <div class="suite-type">
              <el-tag size="small">{{ getTestTypeText(suite.type) }}</el-tag>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近测试结果 -->
    <div class="recent-results">
      <div class="section-header">
        <h3>最近测试结果</h3>
        <el-button type="text" @click="viewAllResults">
          查看全部
          <el-icon><ArrowRight /></el-icon>
        </el-button>
      </div>
      
      <el-table :data="recentTestResults" style="width: 100%">
        <el-table-column prop="name" label="测试名称" width="200" />
        <el-table-column prop="suite" label="所属套件" width="150" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="duration" label="耗时" width="100">
          <template #default="{ row }">
            {{ row.duration }}ms
          </template>
        </el-table-column>
        <el-table-column prop="coverage" label="覆盖率" width="100">
          <template #default="{ row }">
            {{ row.coverage }}%
          </template>
        </el-table-column>
        <el-table-column prop="runTime" label="运行时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.runTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="error" label="错误信息" show-overflow-tooltip />
        <el-table-column label="操作" width="150">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="viewTestDetails(row)">
              详情
            </el-button>
            <el-button type="text" size="small" @click="rerunTest(row)">
              重跑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 测试设置对话框 -->
    <el-dialog v-model="showSettingsDialog" title="测试设置" width="600px">
      <el-form :model="testSettings" label-width="120px">
        <el-form-item label="默认超时时间">
          <el-input-number
            v-model="testSettings.defaultTimeout"
            :min="1000"
            :max="300000"
            :step="1000"
            style="width: 100%"
          />
          <div class="form-tip">单位: 毫秒</div>
        </el-form-item>
        
        <el-form-item label="并行执行">
          <el-switch v-model="testSettings.parallel" />
        </el-form-item>
        
        <el-form-item label="并行数量" v-if="testSettings.parallel">
          <el-input-number
            v-model="testSettings.parallelCount"
            :min="1"
            :max="20"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="失败重试次数">
          <el-input-number
            v-model="testSettings.retries"
            :min="0"
            :max="5"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="覆盖率检查">
          <el-switch v-model="testSettings.coverage" />
        </el-form-item>
        
        <el-form-item label="最小覆盖率" v-if="testSettings.coverage">
          <el-input-number
            v-model="testSettings.minCoverage"
            :min="0"
            :max="100"
            style="width: 100%"
          />
          <div class="form-tip">单位: %</div>
        </el-form-item>
        
        <el-form-item label="自动保存报告">
          <el-switch v-model="testSettings.autoSaveReports" />
        </el-form-item>
        
        <el-form-item label="报告格式" v-if="testSettings.autoSaveReports">
          <el-checkbox-group v-model="testSettings.reportFormats">
            <el-checkbox label="html">HTML</el-checkbox>
            <el-checkbox label="json">JSON</el-checkbox>
            <el-checkbox label="xml">XML</el-checkbox>
            <el-checkbox label="pdf">PDF</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showSettingsDialog = false">取消</el-button>
        <el-button type="primary" @click="saveSettings">
          保存设置
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Play,
  Refresh,
  Setting,
  Search,
  SuccessFilled,
  CircleCloseFilled,
  WarningFilled,
  InfoFilled,
  Plus,
  VideoPlay,
  More,
  ArrowRight
} from '@element-plus/icons-vue'

// 响应式数据
const searchKeyword = ref('')
const filterStatus = ref('')
const filterType = ref('')
const runningAll = ref(false)
const showSettingsDialog = ref(false)

// 测试统计数据
const testStats = reactive({
  passed: 0,
  failed: 0,
  skipped: 0,
  total: 0
})

// 测试套件数据
const testSuites = ref([])

// 最近测试结果
const recentTestResults = ref([])

// 测试设置
const testSettings = reactive({
  defaultTimeout: 30000,
  parallel: true,
  parallelCount: 4,
  retries: 2,
  coverage: true,
  minCoverage: 80,
  autoSaveReports: true,
  reportFormats: ['html', 'json']
})

// 计算属性
const filteredTestSuites = computed(() => {
  let filtered = testSuites.value
  
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(suite => 
      suite.name.toLowerCase().includes(keyword) ||
      (suite.description && suite.description.toLowerCase().includes(keyword))
    )
  }
  
  if (filterStatus.value) {
    filtered = filtered.filter(suite => suite.status === filterStatus.value)
  }
  
  if (filterType.value) {
    filtered = filtered.filter(suite => suite.type === filterType.value)
  }
  
  return filtered
})

// 方法
const loadTestData = async () => {
  try {
    // 模拟加载测试数据
    testSuites.value = [
      {
        id: '1',
        name: '用户管理模块测试',
        description: '测试用户注册、登录、权限管理等功能',
        type: 'unit',
        status: 'passed',
        testCount: 25,
        successRate: 96,
        avgDuration: 1200,
        lastRun: new Date(Date.now() - 2 * 60 * 60 * 1000),
        progress: 0
      },
      {
        id: '2',
        name: '健康数据API测试',
        description: '测试健康数据相关的API接口',
        type: 'api',
        status: 'failed',
        testCount: 18,
        successRate: 83,
        avgDuration: 2500,
        lastRun: new Date(Date.now() - 1 * 60 * 60 * 1000),
        progress: 0
      },
      {
        id: '3',
        name: '端到端流程测试',
        description: '测试完整的用户操作流程',
        type: 'e2e',
        status: 'running',
        testCount: 12,
        successRate: 100,
        avgDuration: 8000,
        lastRun: new Date(),
        progress: 65
      },
      {
        id: '4',
        name: '性能压力测试',
        description: '测试系统在高负载下的性能表现',
        type: 'performance',
        status: 'skipped',
        testCount: 8,
        successRate: 0,
        avgDuration: 15000,
        lastRun: new Date(Date.now() - 24 * 60 * 60 * 1000),
        progress: 0
      }
    ]
    
    recentTestResults.value = [
      {
        id: '1',
        name: '用户登录测试',
        suite: '用户管理模块',
        status: 'passed',
        duration: 1250,
        coverage: 95,
        runTime: new Date(Date.now() - 30 * 60 * 1000),
        error: ''
      },
      {
        id: '2',
        name: 'API响应时间测试',
        suite: '健康数据API',
        status: 'failed',
        duration: 3200,
        coverage: 78,
        runTime: new Date(Date.now() - 45 * 60 * 1000),
        error: '响应时间超过预期阈值'
      },
      {
        id: '3',
        name: '数据库连接测试',
        suite: '基础设施',
        status: 'passed',
        duration: 800,
        coverage: 100,
        runTime: new Date(Date.now() - 60 * 60 * 1000),
        error: ''
      },
      {
        id: '4',
        name: '文件上传测试',
        suite: '文件管理',
        status: 'skipped',
        duration: 0,
        coverage: 0,
        runTime: new Date(Date.now() - 90 * 60 * 1000),
        error: '依赖服务不可用'
      }
    ]
    
    // 更新统计数据
    updateTestStats()
  } catch (error) {
    ElMessage.error('加载测试数据失败: ' + error.message)
  }
}

const updateTestStats = () => {
  const allResults = recentTestResults.value
  testStats.passed = allResults.filter(r => r.status === 'passed').length
  testStats.failed = allResults.filter(r => r.status === 'failed').length
  testStats.skipped = allResults.filter(r => r.status === 'skipped').length
  testStats.total = allResults.length
}

const runAllTests = async () => {
  try {
    runningAll.value = true
    ElMessage.info('开始运行全部测试...')
    
    // 模拟运行测试
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    ElMessage.success('全部测试运行完成')
    await loadTestData()
  } catch (error) {
    ElMessage.error('运行测试失败: ' + error.message)
  } finally {
    runningAll.value = false
  }
}

const refreshTestData = async () => {
  await loadTestData()
  ElMessage.success('数据刷新成功')
}

const runTestSuite = async (suite) => {
  try {
    suite.status = 'running'
    suite.progress = 0
    
    ElMessage.info(`开始运行测试套件: ${suite.name}`)
    
    // 模拟测试进度
    const progressInterval = setInterval(() => {
      if (suite.progress < 100) {
        suite.progress += Math.random() * 20
        if (suite.progress > 100) suite.progress = 100
      } else {
        clearInterval(progressInterval)
        suite.status = Math.random() > 0.3 ? 'passed' : 'failed'
        suite.lastRun = new Date()
        ElMessage.success(`测试套件 ${suite.name} 运行完成`)
      }
    }, 500)
  } catch (error) {
    suite.status = 'failed'
    ElMessage.error('运行测试套件失败: ' + error.message)
  }
}

const handleSuiteAction = async (command, suite) => {
  switch (command) {
    case 'edit':
      ElMessage.info(`编辑测试套件: ${suite.name}`)
      break
    case 'clone':
      ElMessage.info(`克隆测试套件: ${suite.name}`)
      break
    case 'export':
      ElMessage.info(`导出测试套件: ${suite.name}`)
      break
    case 'delete':
      await deleteTestSuite(suite)
      break
  }
}

const deleteTestSuite = async (suite) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除测试套件 "${suite.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = testSuites.value.findIndex(s => s.id === suite.id)
    if (index > -1) {
      testSuites.value.splice(index, 1)
      ElMessage.success('测试套件删除成功')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error.message)
    }
  }
}

const createTestSuite = () => {
  ElMessage.info('跳转到测试套件创建页面')
}

const viewAllResults = () => {
  ElMessage.info('跳转到测试结果详情页面')
}

const viewTestDetails = (result) => {
  ElMessage.info(`查看测试详情: ${result.name}`)
}

const rerunTest = async (result) => {
  try {
    ElMessage.info(`重新运行测试: ${result.name}`)
    // 模拟重跑测试
    await new Promise(resolve => setTimeout(resolve, 2000))
    result.status = Math.random() > 0.5 ? 'passed' : 'failed'
    result.runTime = new Date()
    ElMessage.success('测试重跑完成')
  } catch (error) {
    ElMessage.error('重跑测试失败: ' + error.message)
  }
}

const saveSettings = () => {
  ElMessage.success('测试设置保存成功')
  showSettingsDialog.value = false
}

// 工具函数
const getSuiteStatusType = (status) => {
  const types = {
    passed: 'success',
    failed: 'danger',
    running: 'warning',
    skipped: 'info'
  }
  return types[status] || 'info'
}

const getSuiteStatusText = (status) => {
  const texts = {
    passed: '通过',
    failed: '失败',
    running: '运行中',
    skipped: '跳过'
  }
  return texts[status] || status
}

const getStatusType = (status) => {
  const types = {
    passed: 'success',
    failed: 'danger',
    running: 'warning',
    skipped: 'info'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    passed: '通过',
    failed: '失败',
    running: '运行中',
    skipped: '跳过'
  }
  return texts[status] || status
}

const getTestTypeText = (type) => {
  const texts = {
    unit: '单元测试',
    integration: '集成测试',
    e2e: '端到端测试',
    api: 'API测试',
    performance: '性能测试'
  }
  return texts[type] || type
}

const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadTestData()
})
</script>

<style scoped>
.test-management {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar-left {
  display: flex;
  gap: 10px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.stats-overview {
  margin-bottom: 24px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stat-icon.success {
  background: #67c23a;
}

.stat-icon.danger {
  background: #f56c6c;
}

.stat-icon.warning {
  background: #e6a23c;
}

.stat-icon.info {
  background: #409eff;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.test-suites,
.recent-results {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.suites-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.suite-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.suite-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.suite-card.running {
  border-color: #e6a23c;
  background: linear-gradient(135deg, #fff 0%, #fef9e7 100%);
}

.suite-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.suite-title {
  flex: 1;
}

.suite-title h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.suite-actions {
  display: flex;
  gap: 8px;
}

.suite-description {
  margin-bottom: 16px;
}

.suite-description p {
  margin: 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.suite-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.stat-item {
  text-align: center;
}

.stat-item .label {
  display: block;
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.stat-item .value {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.suite-progress {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-text {
  font-size: 14px;
  font-weight: 600;
  color: #e6a23c;
  min-width: 40px;
}

.suite-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.suite-meta {
  font-size: 12px;
  color: #909399;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .test-management {
    padding: 10px;
  }
  
  .toolbar {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .toolbar-right {
    justify-content: space-between;
  }
  
  .suites-grid {
    grid-template-columns: 1fr;
  }
  
  .suite-header {
    flex-direction: column;
    gap: 12px;
  }
  
  .suite-actions {
    align-self: stretch;
    justify-content: space-between;
  }
  
  .suite-stats {
    flex-direction: column;
    gap: 8px;
  }
  
  .stat-item {
    display: flex;
    justify-content: space-between;
    text-align: left;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .test-management {
    background: #1a1a1a;
  }
  
  .page-header h2 {
    color: #e4e7ed;
  }
  
  .page-description {
    color: #a8abb2;
  }
  
  .toolbar,
  .stat-card,
  .suite-card {
    background: #2d2d2d;
    border-color: #4c4d4f;
  }
  
  .suite-title h4,
  .section-header h3,
  .stat-value {
    color: #e4e7ed;
  }
  
  .suite-description p,
  .stat-item .value {
    color: #c0c4cc;
  }
  
  .stat-item .label,
  .stat-label,
  .suite-meta {
    color: #a8abb2;
  }
  
  .suite-stats {
    background: #1a1a1a;
  }
  
  .suite-footer {
    border-top-color: #4c4d4f;
  }
  
  .suite-card.running {
    background: linear-gradient(135deg, #2d2d2d 0%, #3a3a2a 100%);
  }
}
</style>