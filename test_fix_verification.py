# -*- coding: utf-8 -*-
import requests
import json

def test_fix_verification():
    """验证文档类型记录修复效果"""
    base_url = "http://localhost:8006"
    
    # 登录获取token
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        print("=== 验证文档类型记录修复效果 ===")
        
        # 登录
        login_response = requests.post(f"{base_url}/auth/login", data=login_data)
        if login_response.status_code != 200:
            print(f"登录失败: {login_response.text}")
            return
            
        token_data = login_response.json()
        access_token = token_data.get("access_token")
        if not access_token:
            print("未获取到访问令牌")
            return
            
        headers = {
            "Authorization": f"Bearer {access_token}"
        }
        
        # 测试聚合API
        api_url = f"{base_url}/api/v1/aggregated/users/SM_001/data"
        response = requests.get(api_url, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get("status") == "success":
                records = data.get("data", {}).get("data", [])
                
                print(f"✅ 聚合API调用成功，返回 {len(records)} 条记录")
                
                # 统计记录类型
                type_counts = {}
                for record in records:
                    record_type = record.get("record_type", "unknown")
                    type_counts[record_type] = type_counts.get(record_type, 0) + 1
                
                print("\n=== 记录类型统计 ===")
                for record_type, count in type_counts.items():
                    print(f"{record_type}: {count} 条")
                
                # 显示前3条文档类型记录
                document_records = [r for r in records if r.get("record_type") == "document"]
                if document_records:
                    print(f"\n=== 前3条文档类型记录 ===")
                    for i, record in enumerate(document_records[:3]):
                        print(f"{i+1}. ID: {record.get('id')}, 标题: {record.get('title')}, 创建时间: {record.get('created_at')}")
                    
                    print(f"\n✅ 修复成功！现在可以正确获取到 {len(document_records)} 条文档类型记录")
                else:
                    print("\n❌ 仍然没有找到文档类型记录")
                    
            else:
                print(f"❌ API返回错误: {data.get('message')}")
        else:
            print(f"❌ API调用失败: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_fix_verification()