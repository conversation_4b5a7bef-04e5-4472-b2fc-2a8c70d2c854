#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_with_token():
    """使用token测试API"""
    base_url = 'http://localhost:8006'
    
    # 先登录获取token
    print("=== 登录获取token ===")
    login_response = requests.post(
        f'{base_url}/api/auth/login_json',
        json={'username': 'admin', 'password': 'admin'},
        timeout=5
    )
    
    if login_response.status_code != 200:
        print(f"登录失败: {login_response.text}")
        return
    
    login_data = login_response.json()
    token = login_data.get('access_token')
    
    if not token:
        print(f"未获取到token: {login_data}")
        return
    
    print(f"登录成功，token: {token[:30]}...")
    
    # 测试API调用
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    # 测试评估记录API
    print("\n=== 测试评估记录API ===")
    try:
        response = requests.get(
            f'{base_url}/api/assessments/records',
            headers=headers,
            timeout=5
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"数据类型: {type(data)}")
            if isinstance(data, list):
                print(f"记录数量: {len(data)}")
            elif isinstance(data, dict):
                print(f"响应键: {list(data.keys())}")
    except Exception as e:
        print(f"请求失败: {e}")
    
    # 测试评估列表API
    print("\n=== 测试评估列表API ===")
    try:
        response = requests.get(
            f'{base_url}/api/assessments',
            headers=headers,
            timeout=5
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"数据类型: {type(data)}")
            if isinstance(data, list):
                print(f"评估数量: {len(data)}")
            elif isinstance(data, dict):
                print(f"响应键: {list(data.keys())}")
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == '__main__':
    test_with_token()