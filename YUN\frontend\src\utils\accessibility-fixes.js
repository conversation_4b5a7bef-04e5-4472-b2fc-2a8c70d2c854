/**
 * 可访问性修复工具
 * 用于修复Element UI组件的可访问性问题
 */

/**
 * 修复空的ARIA属性
 */
export function fixEmptyAriaAttributes() {
  // 修复空的 aria-activedescendant 属性
  const elementsWithEmptyAria = document.querySelectorAll('[aria-activedescendant=""]');
  elementsWithEmptyAria.forEach(element => {
    element.removeAttribute('aria-activedescendant');
  });

  // 修复其他可能的空ARIA属性
  const ariaAttributes = [
    'aria-describedby',
    'aria-labelledby',
    'aria-controls',
    'aria-owns',
    'aria-flowto'
  ];

  ariaAttributes.forEach(attr => {
    const elements = document.querySelectorAll(`[${attr}=""]`);
    elements.forEach(element => {
      element.removeAttribute(attr);
    });
  });
}

/**
 * 为Element UI组件添加适当的标签
 */
export function addLabelsToFormElements() {
  // 为没有标签的输入框添加aria-label
  const unlabeledInputs = document.querySelectorAll('input:not([aria-label]):not([aria-labelledby]):not([id])');
  unlabeledInputs.forEach((input, index) => {
    const parentFormItem = input.closest('.el-form-item');
    if (parentFormItem) {
      const label = parentFormItem.querySelector('.el-form-item__label');
      if (label && label.textContent.trim()) {
        input.setAttribute('aria-label', label.textContent.trim());
      } else {
        // 如果没有找到标签，根据输入框类型添加通用标签
        const placeholder = input.getAttribute('placeholder');
        if (placeholder) {
          input.setAttribute('aria-label', placeholder);
        } else {
          input.setAttribute('aria-label', `输入框 ${index + 1}`);
        }
      }
    }
  });

  // 为Element UI的选择框添加标签
  const selectInputs = document.querySelectorAll('.el-select .el-input__inner:not([aria-label])');
  selectInputs.forEach((input, index) => {
    const selectWrapper = input.closest('.el-select');
    if (selectWrapper) {
      const formItem = selectWrapper.closest('.el-form-item');
      if (formItem) {
        const label = formItem.querySelector('.el-form-item__label');
        if (label && label.textContent.trim()) {
          input.setAttribute('aria-label', label.textContent.trim());
        } else {
          input.setAttribute('aria-label', `选择框 ${index + 1}`);
        }
      } else {
        // 如果没有form-item，尝试从父元素获取标签
        const parent = selectWrapper.previousElementSibling;
        if (parent && parent.textContent && parent.textContent.trim()) {
          input.setAttribute('aria-label', parent.textContent.trim());
        } else {
          // 如果都没有，给一个默认标签
          input.setAttribute('aria-label', '选择选项');
        }
      }
    }
  });

  // 为日期选择器添加标签
  const dateInputs = document.querySelectorAll('.el-date-editor .el-input__inner:not([aria-label])');
  dateInputs.forEach((input, index) => {
    const dateWrapper = input.closest('.el-date-editor');
    if (dateWrapper) {
      const formItem = dateWrapper.closest('.el-form-item');
      if (formItem) {
        const label = formItem.querySelector('.el-form-item__label');
        if (label && label.textContent.trim()) {
          input.setAttribute('aria-label', label.textContent.trim());
        } else {
          input.setAttribute('aria-label', `日期选择 ${index + 1}`);
        }
      }
    }
  });
}

/**
 * 为按钮添加可访问性文本
 */
export function addButtonAccessibilityText() {
  // 为没有可访问文本的按钮添加aria-label
  const buttons = document.querySelectorAll('button:not([aria-label]):not([aria-labelledby])');
  buttons.forEach(button => {
    const text = button.textContent.trim();
    const icon = button.querySelector('i[class*="el-icon"]');
    
    if (!text && icon) {
      // 如果按钮只有图标，根据图标类名推断功能
      const iconClass = icon.className;
      if (iconClass.includes('edit')) {
        button.setAttribute('aria-label', '编辑');
      } else if (iconClass.includes('delete')) {
        button.setAttribute('aria-label', '删除');
      } else if (iconClass.includes('view')) {
        button.setAttribute('aria-label', '查看');
      } else if (iconClass.includes('download')) {
        button.setAttribute('aria-label', '下载');
      } else if (iconClass.includes('upload')) {
        button.setAttribute('aria-label', '上传');
      } else if (iconClass.includes('search')) {
        button.setAttribute('aria-label', '搜索');
      } else {
        button.setAttribute('aria-label', '操作按钮');
      }
    } else if (text) {
      button.setAttribute('aria-label', text);
    }
  });
}

/**
 * 修复表格的可访问性
 */
export function fixTableAccessibility() {
  // 为表格添加适当的ARIA属性
  const tables = document.querySelectorAll('.el-table');
  tables.forEach(table => {
    if (!table.getAttribute('role')) {
      table.setAttribute('role', 'table');
    }
    if (!table.getAttribute('aria-label')) {
      table.setAttribute('aria-label', '数据表格');
    }
  });

  // 为表格头部添加适当的属性
  const tableHeaders = document.querySelectorAll('.el-table__header th');
  tableHeaders.forEach(th => {
    if (!th.getAttribute('scope')) {
      th.setAttribute('scope', 'col');
    }
  });

  // 修复Element UI表格的tbody结构问题 - 处理表格头部
  const elHeaderTables = document.querySelectorAll('.el-table__header');
  elHeaderTables.forEach(tableHeader => {
    const table = tableHeader.querySelector('table');
    if (table) {
      // 检查是否已有tbody
      let tbody = table.querySelector('tbody');
      if (!tbody) {
        // 创建tbody并移动所有tr到其中
        tbody = document.createElement('tbody');
        const rows = Array.from(table.querySelectorAll('tr'));
        rows.forEach(row => {
          tbody.appendChild(row);
        });
        table.appendChild(tbody);
      }
      
      // 为tbody添加role属性
      if (!tbody.getAttribute('role')) {
        tbody.setAttribute('role', 'rowgroup');
      }
    }
  });

  // 修复Element UI表格的tbody结构问题 - 处理表格主体
  const elTables = document.querySelectorAll('.el-table__body');
  elTables.forEach(tableBody => {
    const table = tableBody.querySelector('table');
    if (table) {
      // 检查是否已有tbody
      let tbody = table.querySelector('tbody');
      if (!tbody) {
        // 创建tbody并移动所有tr到其中
        tbody = document.createElement('tbody');
        const rows = Array.from(table.querySelectorAll('tr'));
        rows.forEach(row => {
          tbody.appendChild(row);
        });
        table.appendChild(tbody);
      }
      
      // 为tbody添加role属性
      if (!tbody.getAttribute('role')) {
        tbody.setAttribute('role', 'rowgroup');
      }
    }
  });

  // 为表格行添加role属性
  const tableRows = document.querySelectorAll('.el-table__body tr, .el-table__header tr');
  tableRows.forEach(row => {
    if (!row.getAttribute('role')) {
      row.setAttribute('role', 'row');
    }
  });

  // 为表格单元格添加role属性
  const tableCells = document.querySelectorAll('.el-table__body td, .el-table__header th');
  tableCells.forEach(cell => {
    if (!cell.getAttribute('role')) {
      if (cell.tagName.toLowerCase() === 'th') {
        cell.setAttribute('role', 'columnheader');
      } else {
        cell.setAttribute('role', 'cell');
      }
    }
  });
}

/**
 * 主修复函数
 */
export function applyAccessibilityFixes() {
  try {
    fixEmptyAriaAttributes();
    addLabelsToFormElements();
    addButtonAccessibilityText();
    fixTableAccessibility();
    
    console.log('可访问性修复已应用');
  } catch (error) {
    console.error('应用可访问性修复时出错:', error);
  }
}

/**
 * 监听DOM变化并自动应用修复
 */
export function startAccessibilityWatcher() {
  // 初始修复
  applyAccessibilityFixes();
  
  // 创建观察器来监听DOM变化
  const observer = new MutationObserver((mutations) => {
    let shouldFix = false;
    
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        // 检查是否有新的Element UI组件被添加
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            if (node.classList && (node.classList.contains('el-form-item') || 
                node.classList.contains('el-button') ||
                node.classList.contains('el-select') ||
                node.classList.contains('el-date-editor') ||
                node.classList.contains('el-table'))) {
              shouldFix = true;
            }
          }
        });
      }
    });
    
    if (shouldFix) {
      // 延迟执行修复，确保组件完全渲染
      setTimeout(applyAccessibilityFixes, 100);
    }
  });
  
  // 开始观察
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
  
  return observer;
}

/**
 * Vue插件形式的可访问性修复
 */
export default {
  install(app) {
    // 在Vue应用挂载后启动可访问性监听器
    app.config.globalProperties.$startAccessibilityWatcher = startAccessibilityWatcher;
    app.config.globalProperties.$applyAccessibilityFixes = applyAccessibilityFixes;
    
    // 在路由变化后自动应用修复
    if (app.config.globalProperties.$router) {
      app.config.globalProperties.$router.afterEach(() => {
        setTimeout(applyAccessibilityFixes, 200);
      });
    }
  }
};