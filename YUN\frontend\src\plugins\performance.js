/**
 * Vue 组件性能监控插件
 * 自动监控组件渲染性能、生命周期耗时等
 */
import { componentProfiler, performanceCollector } from '@/utils/performance'
import { CONFIG } from '@/config/environment'

/**
 * Vue 性能监控插件
 */
export default {
  install(app, options = {}) {
    const {
      enableComponentProfiling = CONFIG.performance.enableMonitoring,
      enableLifecycleProfiling = CONFIG.performance.enableMonitoring,
      slowRenderThreshold = 16, // 16ms (一帧的时间)
      enableDevtools = CONFIG.debug.showErrorDetails
    } = options

    // 组件性能监控
    if (enableComponentProfiling) {
      app.mixin({
        beforeCreate() {
          if (this.$options.name) {
            componentProfiler.startProfile(this.$options.name)
          }
        },
        
        mounted() {
          if (this.$options.name) {
            const duration = componentProfiler.endProfile(this.$options.name)
            
            // 慢渲染警告
            if (duration && duration > slowRenderThreshold) {
              console.warn(
                `[Performance] 组件 ${this.$options.name} 渲染耗时 ${duration.toFixed(2)}ms，超过阈值 ${slowRenderThreshold}ms`
              )
            }
          }
        },
        
        beforeUnmount() {
          // 清理性能数据
          if (this.$options.name) {
            // 可以在这里清理组件相关的性能数据
          }
        }
      })
    }

    // 生命周期性能监控
    if (enableLifecycleProfiling) {
      const lifecycleHooks = [
        'beforeCreate', 'created', 'beforeMount', 'mounted',
        'beforeUpdate', 'updated', 'beforeUnmount', 'unmounted'
      ]

      lifecycleHooks.forEach(hook => {
        app.mixin({
          [hook]() {
            if (this.$options.name && CONFIG.debug.showErrorDetails) {
              console.log(`[Lifecycle] ${this.$options.name}.${hook}()`)
            }
          }
        })
      })
    }

    // 添加全局性能方法
    app.config.globalProperties.$performance = {
      /**
       * 标记性能点
       */
      mark(name) {
        if (performance.mark) {
          performance.mark(name)
        }
      },

      /**
       * 测量性能
       */
      measure(name, startMark, endMark) {
        if (performance.measure) {
          performance.measure(name, startMark, endMark)
          
          const entries = performance.getEntriesByName(name, 'measure')
          if (entries.length > 0) {
            const duration = entries[entries.length - 1].duration
            console.log(`[Performance] ${name}: ${duration.toFixed(2)}ms`)
            return duration
          }
        }
        return 0
      },

      /**
       * 获取组件性能统计
       */
      getComponentStats(componentName) {
        return componentProfiler.getComponentStats(componentName)
      },

      /**
       * 获取所有组件性能统计
       */
      getAllComponentStats() {
        return componentProfiler.getAllStats()
      },

      /**
       * 获取页面性能摘要
       */
      getPerformanceSummary() {
        return performanceCollector.getPerformanceSummary()
      }
    }

    // 开发工具集成
    if (enableDevtools && typeof window !== 'undefined') {
      // 添加到 window 对象，方便调试
      window.__VUE_PERFORMANCE__ = {
        componentProfiler,
        performanceCollector,
        getStats: () => ({
          components: componentProfiler.getAllStats(),
          performance: performanceCollector.getPerformanceSummary()
        })
      }

      // 定期输出性能报告
      if (CONFIG.performance.enableReporting) {
        setInterval(() => {
          const stats = componentProfiler.getAllStats()
          if (stats.length > 0) {
            console.group('📊 组件性能报告')
            stats.slice(0, 5).forEach(stat => {
              console.log(
                `${stat.componentName}: 平均 ${stat.avgDuration}ms, 最大 ${stat.maxDuration}ms, 渲染次数 ${stat.renderCount}`
              )
            })
            console.groupEnd()
          }
        }, 30000) // 每30秒输出一次
      }
    }
  }
}

/**
 * 性能监控指令
 */
export const performanceDirective = {
  name: 'performance',
  
  beforeMount(el, binding) {
    const { value: name, modifiers } = binding
    
    if (name) {
      // 开始监控
      el._performanceStart = performance.now()
      el._performanceName = name
      
      if (modifiers.log) {
        console.log(`[Performance] 开始监控: ${name}`)
      }
    }
  },
  
  mounted(el, binding) {
    const { modifiers } = binding
    
    if (el._performanceStart && el._performanceName) {
      const duration = performance.now() - el._performanceStart
      
      if (modifiers.log || duration > 16) {
        console.log(
          `[Performance] ${el._performanceName} 渲染完成: ${duration.toFixed(2)}ms`
        )
      }
      
      // 清理
      delete el._performanceStart
      delete el._performanceName
    }
  }
}

/**
 * 路由性能监控
 */
export function createRouterPerformancePlugin(router) {
  let navigationStart = 0
  
  router.beforeEach((to, from, next) => {
    navigationStart = performance.now()
    
    if (CONFIG.debug.showErrorDetails) {
      console.log(`[Router] 导航开始: ${from.path} → ${to.path}`)
    }
    
    next()
  })
  
  router.afterEach((to, from) => {
    const navigationEnd = performance.now()
    const duration = navigationEnd - navigationStart
    
    if (CONFIG.debug.showErrorDetails) {
      console.log(
        `[Router] 导航完成: ${from.path} → ${to.path}, 耗时: ${duration.toFixed(2)}ms`
      )
    }
    
    // 记录路由性能
    performanceCollector.metrics.set('lastNavigation', {
      from: from.path,
      to: to.path,
      duration,
      timestamp: Date.now()
    })
  })
}

/**
 * 异步组件性能监控
 */
export function withPerformanceTracking(asyncComponent, name) {
  return () => {
    const start = performance.now()
    
    return asyncComponent().then(component => {
      const end = performance.now()
      const duration = end - start
      
      console.log(
        `[Performance] 异步组件 ${name} 加载耗时: ${duration.toFixed(2)}ms`
      )
      
      return component
    })
  }
}

/**
 * 性能监控装饰器（用于 Composition API）
 */
export function usePerformanceTracking(name) {
  const start = performance.now()
  
  const end = () => {
    const duration = performance.now() - start
    console.log(`[Performance] ${name} 执行耗时: ${duration.toFixed(2)}ms`)
    return duration
  }
  
  return { end }
}

/**
 * 批量性能测试
 */
export async function runPerformanceTests(tests) {
  const results = []
  
  for (const test of tests) {
    const { name, fn, iterations = 1 } = test
    const durations = []
    
    for (let i = 0; i < iterations; i++) {
      const start = performance.now()
      await fn()
      const duration = performance.now() - start
      durations.push(duration)
    }
    
    const avgDuration = durations.reduce((sum, d) => sum + d, 0) / durations.length
    const minDuration = Math.min(...durations)
    const maxDuration = Math.max(...durations)
    
    results.push({
      name,
      iterations,
      avgDuration: avgDuration.toFixed(2),
      minDuration: minDuration.toFixed(2),
      maxDuration: maxDuration.toFixed(2),
      durations
    })
  }
  
  return results
}