#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'YUN', 'backend'))

import sqlite3
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    # 数据库路径
    db_path = "c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db"
    
    logger.info("=== 调试问卷分发记录 ===")
    
    # 1. 直接SQL查询分发记录
    logger.info("\n1. 检查QuestionnaireDistribution表:")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 检查表是否存在
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='questionnaire_distributions';")
    table_exists = cursor.fetchone()
    
    if table_exists:
        logger.info("QuestionnaireDistribution表存在")
        
        # 查询所有分发记录
        cursor.execute("SELECT * FROM questionnaire_distributions;")
        distributions = cursor.fetchall()
        
        logger.info(f"找到 {len(distributions)} 条分发记录:")
        for dist in distributions:
            logger.info(f"  分发记录: {dist}")
            
        # 查询用户ID为2的分发记录 (SM_006对应的用户ID)
        cursor.execute("SELECT * FROM questionnaire_distributions WHERE user_id = 2;")
        user_distributions = cursor.fetchall()
        
        logger.info(f"\n用户ID=2的分发记录: {len(user_distributions)} 条")
        for dist in user_distributions:
            logger.info(f"  用户分发记录: {dist}")
            
    else:
        logger.info("QuestionnaireDistribution表不存在!")
        
        # 列出所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        logger.info("数据库中的所有表:")
        for table in tables:
            logger.info(f"  - {table[0]}")
    
    # 2. 检查用户表
    logger.info("\n2. 检查用户表:")
    cursor.execute("SELECT id, custom_id, username FROM users WHERE custom_id = 'SM_006';")
    user = cursor.fetchone()
    
    if user:
        logger.info(f"找到用户: ID={user[0]}, custom_id={user[1]}, username={user[2]}")
    else:
        logger.info("未找到custom_id=SM_006的用户")
    
    # 3. 检查问卷表
    logger.info("\n3. 检查问卷表:")
    cursor.execute("SELECT id, name, status, custom_id FROM questionnaires WHERE custom_id = 'SM_006';")
    questionnaires = cursor.fetchall()
    
    logger.info(f"找到 {len(questionnaires)} 条问卷记录:")
    for q in questionnaires:
        logger.info(f"  问卷: ID={q[0]}, 名称={q[1]}, 状态={q[2]}, custom_id={q[3]}")
    
    conn.close()
    
    # 4. 使用ORM查询
    logger.info("\n4. 使用ORM查询分发记录:")
    
    try:
        # 创建数据库引擎
        engine = create_engine(f"sqlite:///{db_path}")
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        # 查询分发记录
        result = db.execute(text("SELECT * FROM questionnaire_distributions WHERE user_id = 2"))
        distributions = result.fetchall()
        
        logger.info(f"ORM查询到 {len(distributions)} 条分发记录")
        for dist in distributions:
            logger.info(f"  ORM分发记录: {dist}")
            
        db.close()
        
    except Exception as e:
        logger.error(f"ORM查询失败: {e}")

if __name__ == "__main__":
    main()