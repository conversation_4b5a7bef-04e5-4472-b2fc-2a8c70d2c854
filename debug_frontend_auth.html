<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端认证状态调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .info {
            background-color: #e7f3ff;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            background-color: #ffe7e7;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #e7ffe7;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>前端认证状态调试工具</h1>
        
        <div class="section">
            <h3>1. 检查localStorage认证信息</h3>
            <button onclick="checkLocalStorage()">检查localStorage</button>
            <div id="localStorage-result"></div>
        </div>
        
        <div class="section">
            <h3>2. 测试登录获取token</h3>
            <button onclick="testLogin()">测试登录</button>
            <div id="login-result"></div>
        </div>
        
        <div class="section">
            <h3>3. 测试API调用</h3>
            <button onclick="testAPIs()">测试问卷和量表API</button>
            <div id="api-result"></div>
        </div>
        
        <div class="section">
            <h3>4. 清除认证信息</h3>
            <button onclick="clearAuth()">清除所有认证信息</button>
            <div id="clear-result"></div>
        </div>
    </div>

    <script>
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = message;
            element.appendChild(div);
        }
        
        function clearLog(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }
        
        function checkLocalStorage() {
            clearLog('localStorage-result');
            
            const token = localStorage.getItem('token');
            const user = localStorage.getItem('user');
            const customId = localStorage.getItem('custom_id');
            
            log('localStorage-result', `<strong>Token:</strong> ${token ? token.substring(0, 50) + '...' : '无'}`);
            log('localStorage-result', `<strong>User:</strong> ${user || '无'}`);
            log('localStorage-result', `<strong>Custom ID:</strong> ${customId || '无'}`);
            
            if (token) {
                try {
                    const parts = token.split('.');
                    log('localStorage-result', `<strong>Token格式:</strong> ${parts.length}部分 ${parts.length === 3 ? '(正确)' : '(错误)'}`, parts.length === 3 ? 'success' : 'error');
                    
                    if (parts.length === 3) {
                        const payload = JSON.parse(atob(parts[1]));
                        log('localStorage-result', `<strong>Token内容:</strong><pre>${JSON.stringify(payload, null, 2)}</pre>`);
                    }
                } catch (e) {
                    log('localStorage-result', `<strong>Token解析错误:</strong> ${e.message}`, 'error');
                }
            }
        }
        
        async function testLogin() {
            clearLog('login-result');
            log('login-result', '正在尝试登录...');
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: 'username=admin&password=admin123'
                });
                
                log('login-result', `<strong>登录状态码:</strong> ${response.status}`);
                
                if (response.ok) {
                    const data = await response.json();
                    log('login-result', `<strong>登录成功!</strong>`, 'success');
                    log('login-result', `<strong>响应数据:</strong><pre>${JSON.stringify(data, null, 2)}</pre>`);
                    
                    // 保存token
                    if (data.access_token) {
                        localStorage.setItem('token', data.access_token);
                        log('login-result', '已保存token到localStorage', 'success');
                    }
                    
                    if (data.user) {
                        localStorage.setItem('user', JSON.stringify(data.user));
                        log('login-result', '已保存用户信息到localStorage', 'success');
                    }
                    
                    if (data.custom_id) {
                        localStorage.setItem('custom_id', data.custom_id);
                        log('login-result', '已保存custom_id到localStorage', 'success');
                    }
                } else {
                    const errorText = await response.text();
                    log('login-result', `<strong>登录失败:</strong> ${errorText}`, 'error');
                }
            } catch (error) {
                log('login-result', `<strong>登录异常:</strong> ${error.message}`, 'error');
            }
        }
        
        async function testAPIs() {
            clearLog('api-result');
            
            const token = localStorage.getItem('token');
            if (!token) {
                log('api-result', '没有token，请先登录', 'error');
                return;
            }
            
            const apis = [
                { url: '/api/questionnaires', name: '问卷API' },
                { url: '/api/assessments', name: '量表API' },
                { url: '/api/clinical-scales/standard-questionnaires', name: '标准问卷API' },
                { url: '/api/clinical-scales/standard-assessments', name: '标准量表API' }
            ];
            
            for (const api of apis) {
                log('api-result', `<strong>测试 ${api.name}...</strong>`);
                
                try {
                    const response = await fetch(api.url, {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    log('api-result', `${api.name} 状态码: ${response.status}`);
                    
                    if (response.ok) {
                        const data = await response.json();
                        log('api-result', `${api.name} 成功!`, 'success');
                        log('api-result', `数据结构: ${typeof data}, 键: ${Object.keys(data).join(', ')}`);
                        
                        if (data.data && Array.isArray(data.data)) {
                            log('api-result', `数据数量: ${data.data.length}`);
                            if (data.data.length > 0) {
                                log('api-result', `第一项数据:<pre>${JSON.stringify(data.data[0], null, 2)}</pre>`);
                            }
                        } else {
                            log('api-result', `完整响应:<pre>${JSON.stringify(data, null, 2)}</pre>`);
                        }
                    } else {
                        const errorText = await response.text();
                        log('api-result', `${api.name} 失败: ${errorText}`, 'error');
                    }
                } catch (error) {
                    log('api-result', `${api.name} 异常: ${error.message}`, 'error');
                }
            }
        }
        
        function clearAuth() {
            clearLog('clear-result');
            
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            localStorage.removeItem('custom_id');
            localStorage.removeItem('login_method');
            
            log('clear-result', '已清除所有认证信息', 'success');
        }
        
        // 页面加载时自动检查localStorage
        window.onload = function() {
            checkLocalStorage();
        };
    </script>
</body>
</html>