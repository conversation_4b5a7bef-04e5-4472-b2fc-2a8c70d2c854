# -*- coding: utf-8 -*-
"""
健康管理系统统一配置管理器
统一管理前端、后端配置文件，确保配置一致性和规范性

版本: 1.0
作者: Health Management System
创建时间: 2024-12-30
"""

import os
import sys
import json
import yaml
import configparser
import importlib.util
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from enum import Enum

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入日志模块
try:
    from backend.app.core.logging_utils import get_logger
except ImportError:
    import logging
    def get_logger(name):
        return logging.getLogger(name)

logger = get_logger(__name__)

class ConfigType(Enum):
    """配置类型"""
    JSON = "json"
    YAML = "yaml"
    INI = "ini"
    ENV = "env"
    PYTHON = "python"
    JAVASCRIPT = "javascript"

class Environment(Enum):
    """环境类型"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"

@dataclass
class DatabaseConfig:
    """数据库配置"""
    host: str = "localhost"
    port: int = 3306
    username: str = "root"
    password: str = ""
    name: str = "health_management"
    charset: str = "utf8mb4"
    pool_size: int = 10
    max_overflow: int = 20
    pool_timeout: int = 30
    pool_recycle: int = 3600

@dataclass
class RedisConfig:
    """Redis配置"""
    host: str = "localhost"
    port: int = 6379
    password: str = ""
    db: int = 0
    max_connections: int = 10
    socket_timeout: int = 5
    socket_connect_timeout: int = 5

@dataclass
class ServerConfig:
    """服务器配置"""
    host: str = "0.0.0.0"
    port: int = 8006
    workers: int = 1
    debug: bool = True
    reload: bool = True
    log_level: str = "info"
    access_log: bool = True

@dataclass
class SecurityConfig:
    """安全配置"""
    secret_key: str = "your-secret-key-here"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    refresh_token_expire_days: int = 7
    password_min_length: int = 8
    max_login_attempts: int = 5
    lockout_duration: int = 300

@dataclass
class FrontendConfig:
    """前端配置"""
    api_base_url: str = "http://localhost:8006"
    app_title: str = "健康管理系统"
    app_version: str = "1.0.0"
    theme: str = "default"
    language: str = "zh-CN"
    debug: bool = True
    mock_api: bool = False
    upload_max_size: int = 10485760  # 10MB

@dataclass
class LogConfig:
    """日志配置"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: str = "logs/app.log"
    max_file_size: int = 10485760  # 10MB
    backup_count: int = 5
    console_output: bool = True

@dataclass
class EmailConfig:
    """邮件配置"""
    smtp_server: str = "smtp.gmail.com"
    smtp_port: int = 587
    username: str = ""
    password: str = ""
    use_tls: bool = True
    from_email: str = ""
    from_name: str = "健康管理系统"

@dataclass
class SystemConfig:
    """系统配置"""
    environment: Environment = Environment.DEVELOPMENT
    debug: bool = True
    timezone: str = "Asia/Shanghai"
    max_upload_size: int = 10485760  # 10MB
    session_timeout: int = 3600  # 1小时
    backup_enabled: bool = True
    backup_interval: int = 86400  # 24小时

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, project_root: Optional[Path] = None):
        """初始化配置管理器"""
        self.project_root = project_root or Path(__file__).parent
        self.backend_config_dir = self.project_root / "backend" / "app" / "core"
        self.frontend_config_dir = self.project_root / "frontend"
        
        # 确保配置目录存在
        self.backend_config_dir.mkdir(parents=True, exist_ok=True)
        self.frontend_config_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化配置对象
        self.database = DatabaseConfig()
        self.redis = RedisConfig()
        self.server = ServerConfig()
        self.security = SecurityConfig()
        self.frontend = FrontendConfig()
        self.log = LogConfig()
        self.email = EmailConfig()
        self.system = SystemConfig()
        
        # 加载现有配置
        self.load_configs()
    
    def load_configs(self):
        """加载所有配置文件"""
        try:
            self._load_backend_configs()
            self._load_frontend_configs()
            self._load_environment_configs()
            logger.info("配置加载完成")
        except Exception as e:
            logger.error(f"配置加载失败: {str(e)}")
    
    def _load_backend_configs(self):
        """加载后端配置"""
        try:
            # 加载环境配置
            env_file = self.project_root / "backend" / ".env"
            if env_file.exists():
                self._load_env_file(env_file)
            
            # 加载Python配置文件
            config_file = self.backend_config_dir / "config.py"
            if config_file.exists():
                spec = importlib.util.spec_from_file_location("config", config_file)
                config_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(config_module)
                self._update_from_backend_settings(config_module)
                
        except Exception as e:
            logger.warning(f"后端配置加载失败: {str(e)}")
    
    def _load_frontend_configs(self):
        """加载前端配置"""
        try:
            # 加载前端.env文件
            env_file = self.frontend_config_dir / ".env"
            if env_file.exists():
                with open(env_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#') and '=' in line:
                            key, value = line.split('=', 1)
                            self._update_frontend_from_env(key.strip(), value.strip())
            
            # 加载前端配置JSON
            config_file = self.frontend_config_dir / "public" / "config.json"
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    self._update_from_dict(config_data)
                    
        except Exception as e:
            logger.warning(f"前端配置加载失败: {str(e)}")
    
    def _load_environment_configs(self):
        """加载环境特定配置"""
        env_config_file = self.project_root / f"config.{self.system.environment.value}.yaml"
        if env_config_file.exists():
            with open(env_config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
                self._update_from_dict(config_data)
    
    def _load_env_file(self, env_file: Path):
        """加载.env文件"""
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
        except Exception as e:
            logger.warning(f"环境文件加载失败: {str(e)}")
    
    def _update_from_backend_settings(self, config_module):
        """从后端设置更新配置"""
        if hasattr(config_module, 'Settings'):
            settings = config_module.Settings()
            # 更新数据库配置
            if hasattr(settings, 'DATABASE_HOST'):
                self.database.host = settings.DATABASE_HOST
            if hasattr(settings, 'DATABASE_PORT'):
                self.database.port = settings.DATABASE_PORT
            # 添加其他配置更新...
    
    def _update_from_dict(self, config_dict: Dict[str, Any]):
        """从字典更新配置"""
        for key, value in config_dict.items():
            if hasattr(self, key) and isinstance(value, dict):
                config_obj = getattr(self, key)
                for sub_key, sub_value in value.items():
                    if hasattr(config_obj, sub_key):
                        setattr(config_obj, sub_key, sub_value)
    
    def _update_frontend_from_env(self, key: str, value: str):
        """从环境变量更新前端配置"""
        try:
            # 映射环境变量到前端配置
            if key.startswith('VUE_APP_'):
                # Vue.js 环境变量
                config_key = key.replace('VUE_APP_', '').lower()
                if hasattr(self.frontend, config_key):
                    setattr(self.frontend, config_key, value)
            elif key.startswith('REACT_APP_'):
                # React 环境变量
                config_key = key.replace('REACT_APP_', '').lower()
                if hasattr(self.frontend, config_key):
                    setattr(self.frontend, config_key, value)
            elif key in ['API_BASE_URL', 'BASE_URL']:
                self.frontend.api_base_url = value
            elif key == 'APP_TITLE':
                self.frontend.title = value
            elif key == 'APP_VERSION':
                self.frontend.version = value
        except Exception as e:
            logger.warning(f"前端环境变量更新失败 {key}={value}: {str(e)}")
    
    def save_configs(self):
        """保存所有配置"""
        try:
            self._save_unified_config()
            self._save_backend_configs()
            self._save_frontend_configs()
            logger.info("配置保存完成")
        except Exception as e:
            logger.error(f"配置保存失败: {str(e)}")
    
    def _save_unified_config(self):
        """保存统一配置文件"""
        try:
            config_data = {
                'database': asdict(self.database),
                'redis': asdict(self.redis),
                'server': asdict(self.server),
                'security': asdict(self.security),
                'frontend': asdict(self.frontend),
                'log': asdict(self.log),
                'email': asdict(self.email),
                'system': asdict(self.system)
            }
            
            # 保存为YAML格式
            config_file = self.project_root / "config.yaml"
            with open(config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
                
        except Exception as e:
            logger.error(f"统一配置保存失败: {str(e)}")
    
    def _save_backend_configs(self):
        """保存后端配置"""
        try:
            # 生成后端环境配置文件
            env_content = f"""# -*- coding: utf-8 -*-
# 后端环境配置
# 自动生成于: {datetime.now().isoformat()}

from pydantic import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # 数据库配置
    DATABASE_HOST: str = "{self.database.host}"
    DATABASE_PORT: int = {self.database.port}
    DATABASE_USER: str = "{self.database.username}"
    DATABASE_PASSWORD: str = "{self.database.password}"
    DATABASE_NAME: str = "{self.database.name}"
    
    # Redis配置
    REDIS_HOST: str = "{self.redis.host}"
    REDIS_PORT: int = {self.redis.port}
    REDIS_PASSWORD: str = "{self.redis.password}"
    REDIS_DB: int = {self.redis.db}
    
    # 服务器配置
    HOST: str = "{self.server.host}"
    PORT: int = {self.server.port}
    WORKERS: int = {self.server.workers}
    
    # 安全配置
    SECRET_KEY: str = "{self.security.secret_key}"
    ALGORITHM: str = "{self.security.algorithm}"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = {self.security.access_token_expire_minutes}
    
    # 系统配置
    ENVIRONMENT: str = "{self.system.environment.value}"
    DEBUG: bool = {str(self.system.debug).lower()}
    
    class Config:
        case_sensitive = True

def get_settings() -> Settings:
    return Settings()

settings = get_settings()
"""
            
            # 保存到后端配置目录
            backend_env_file = self.backend_config_dir / "environment.py"
            with open(backend_env_file, 'w', encoding='utf-8') as f:
                f.write(env_content)
            
            # 生成.env文件
            env_file_content = f"""# 数据库配置
DATABASE_HOST={self.database.host}
DATABASE_PORT={self.database.port}
DATABASE_USER={self.database.username}
DATABASE_PASSWORD={self.database.password}
DATABASE_NAME={self.database.name}

# Redis配置
REDIS_HOST={self.redis.host}
REDIS_PORT={self.redis.port}
REDIS_PASSWORD={self.redis.password}
REDIS_DB={self.redis.db}

# 安全配置
SECRET_KEY={self.security.secret_key}
ALGORITHM={self.security.algorithm}
ACCESS_TOKEN_EXPIRE_MINUTES={self.security.access_token_expire_minutes}

# 系统配置
ENVIRONMENT={self.system.environment.value}
DEBUG={str(self.server.debug).lower()}
"""
            
            backend_env_file = self.project_root / "backend" / ".env"
            with open(backend_env_file, 'w', encoding='utf-8') as f:
                f.write(env_file_content)
            
            logger.info("后端配置已保存")
            
        except Exception as e:
            logger.error(f"后端配置保存失败: {str(e)}")
    
    def _save_frontend_configs(self):
        """保存前端配置"""
        try:
            # 生成前端.env文件
            env_content = f"""# 前端环境配置
# 自动生成于: {datetime.now().isoformat()}

# API配置
VUE_APP_API_BASE_URL={self.frontend.api_base_url}
VUE_APP_TITLE={self.frontend.app_title}
VUE_APP_VERSION={self.frontend.app_version}

# 主题和语言
VUE_APP_THEME={self.frontend.theme}
VUE_APP_LANGUAGE={self.frontend.language}

# 开发配置
VUE_APP_DEBUG={str(self.frontend.debug).lower()}
VUE_APP_MOCK_API={str(self.frontend.mock_api).lower()}

# 系统配置
VUE_APP_ENVIRONMENT={self.system.environment.value}
VUE_APP_UPLOAD_MAX_SIZE={self.system.max_upload_size}
"""
            
            frontend_env_file = self.frontend_config_dir / ".env"
            with open(frontend_env_file, 'w', encoding='utf-8') as f:
                f.write(env_content)
            
            # 生成前端配置JSON文件
            frontend_config = {
                'api': {
                    'baseURL': self.frontend.api_base_url,
                    'timeout': 10000
                },
                'app': {
                    'title': self.frontend.app_title,
                    'version': self.frontend.app_version,
                    'theme': self.frontend.theme,
                    'language': self.frontend.language
                },
                'system': {
                    'environment': self.system.environment.value,
                    'debug': self.system.debug
                }
            }
            
            # 确保public目录存在
            public_dir = self.frontend_config_dir / "public"
            public_dir.mkdir(exist_ok=True)
            
            config_file = public_dir / "config.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(frontend_config, f, indent=2, ensure_ascii=False)
            
            logger.info("前端配置已保存")
            
        except Exception as e:
            logger.error(f"前端配置保存失败: {str(e)}")
    
    def validate_configs(self) -> List[str]:
        """验证配置"""
        errors = []
        
        # 验证数据库配置
        if not self.database.host:
            errors.append("数据库主机地址不能为空")
        if not self.database.username:
            errors.append("数据库用户名不能为空")
        if not self.database.name:
            errors.append("数据库名称不能为空")
        
        # 验证Redis配置
        if not self.redis.host:
            errors.append("Redis主机地址不能为空")
        
        # 验证服务器配置
        if self.server.port < 1 or self.server.port > 65535:
            errors.append("服务器端口必须在1-65535之间")
        
        # 验证安全配置
        if len(self.security.secret_key) < 32:
            errors.append("安全密钥长度至少32个字符")
        if self.security.access_token_expire_minutes < 1:
            errors.append("访问令牌过期时间必须大于0")
        
        # 验证前端配置
        if not self.frontend.api_base_url:
            errors.append("前端API基础URL不能为空")
        
        if errors:
            logger.warning(f"配置验证发现 {len(errors)} 个问题")
            for error in errors:
                logger.warning(f"  - {error}")
        else:
            logger.info("配置验证通过")
        
        return errors
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        return {
            'environment': self.system.environment.value,
            'database': {
                'host': self.database.host,
                'port': self.database.port,
                'name': self.database.name
            },
            'redis': {
                'host': self.redis.host,
                'port': self.redis.port
            },
            'server': {
                'host': self.server.host,
                'port': self.server.port
            },
            'frontend': {
                'api_base_url': self.frontend.api_base_url,
                'app_title': self.frontend.app_title
            }
        }

def main():
    """主函数"""
    print("\n" + "="*60)
    print("健康管理系统配置管理器")
    print("="*60)
    
    try:
        config_manager = ConfigManager()
        
        while True:
            print("\n请选择操作:")
            print("1. 查看配置摘要")
            print("2. 验证配置")
            print("3. 保存配置")
            print("4. 重新加载配置")
            print("5. 修改数据库配置")
            print("6. 修改前端配置")
            print("0. 退出")
            
            choice = input("\n请输入选择 (0-6): ").strip()
            
            if choice == "0":
                print("\n👋 再见！")
                break
            elif choice == "1":
                summary = config_manager.get_config_summary()
                print("\n📋 配置摘要:")
                print(json.dumps(summary, indent=2, ensure_ascii=False))
            elif choice == "2":
                errors = config_manager.validate_configs()
                if not errors:
                    print("\n✅ 配置验证通过")
                else:
                    print(f"\n❌ 发现 {len(errors)} 个配置问题:")
                    for error in errors:
                        print(f"  - {error}")
            elif choice == "3":
                config_manager.save_configs()
                print("\n💾 配置已保存")
            elif choice == "4":
                config_manager.load_configs()
                print("\n🔄 配置已重新加载")
            elif choice == "5":
                print("\n🗄️ 修改数据库配置")
                host = input(f"数据库主机 [{config_manager.database.host}]: ").strip()
                if host:
                    config_manager.database.host = host
                port = input(f"数据库端口 [{config_manager.database.port}]: ").strip()
                if port:
                    config_manager.database.port = int(port)
                print("✅ 数据库配置已更新")
            elif choice == "6":
                print("\n🎨 修改前端配置")
                title = input(f"应用标题 [{config_manager.frontend.app_title}]: ").strip()
                if title:
                    config_manager.frontend.app_title = title
                api_url = input(f"API基础URL [{config_manager.frontend.api_base_url}]: ").strip()
                if api_url:
                    config_manager.frontend.api_base_url = api_url
                print("✅ 前端配置已更新")
            else:
                print("\n❌ 无效选择，请重新输入")
    
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 运行错误: {str(e)}")
        logger.error(f"配置管理器运行错误: {str(e)}")

if __name__ == "__main__":
    main()