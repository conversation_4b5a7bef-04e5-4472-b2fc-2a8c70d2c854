#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查表结构
"""

import sqlite3
import os

def check_table_structure():
    """检查表结构"""
    print("=== 检查表结构 ===")
    
    # 数据库路径
    db_path = "c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db"
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查questionnaires表结构
        print("\n1. questionnaires表结构:")
        cursor.execute("PRAGMA table_info(questionnaires)")
        columns = cursor.fetchall()
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
        
        # 检查assessments表结构
        print("\n2. assessments表结构:")
        cursor.execute("PRAGMA table_info(assessments)")
        columns = cursor.fetchall()
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
        
        # 检查questionnaires数据
        print("\n3. questionnaires表数据样例:")
        cursor.execute("SELECT * FROM questionnaires LIMIT 2")
        questionnaires = cursor.fetchall()
        for q in questionnaires:
            print(f"  {q}")
        
        # 检查assessments数据
        print("\n4. assessments表数据样例:")
        cursor.execute("SELECT * FROM assessments LIMIT 2")
        assessments = cursor.fetchall()
        for a in assessments:
            print(f"  {a}")
        
        conn.close()
        
    except Exception as e:
        print(f"检查表结构时出错: {str(e)}")
    
    print("\n=== 检查完成 ===")

if __name__ == "__main__":
    check_table_structure()