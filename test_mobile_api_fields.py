#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试移动端API返回的description和instructions字段
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'YUN', 'backend'))

import requests
import json
from datetime import datetime

# API配置
BASE_URL = "http://localhost:8000"
USERNAME = "admin"
PASSWORD = "admin123"

def login():
    """登录获取token"""
    login_data = {
        "username": USERNAME,
        "password": PASSWORD
    }
    
    response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
    if response.status_code == 200:
        result = response.json()
        return result.get('access_token')
    else:
        print(f"登录失败: {response.status_code} - {response.text}")
        return None

def test_assessment_templates_api(token):
    """测试评估模板API"""
    print("\n=== 测试评估模板API ===")
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    response = requests.get(f"{BASE_URL}/api/mobile/templates/assessment-templates", headers=headers)
    
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"响应数据类型: {type(data)}")
        
        if isinstance(data, dict) and 'data' in data:
            templates = data['data']
            print(f"模板数量: {len(templates)}")
            
            for i, template in enumerate(templates[:2]):  # 只显示前2个
                print(f"\n模板 {i+1}:")
                print(f"  ID: {template.get('id')}")
                print(f"  名称: {template.get('name')}")
                print(f"  描述: {template.get('description')}")
                print(f"  说明: {template.get('instructions')}")
                print(f"  问题数量: {len(template.get('questions', []))}")
        else:
            print(f"响应数据: {data}")
    else:
        print(f"请求失败: {response.text}")

def test_assessments_api(token):
    """测试用户评估列表API"""
    print("\n=== 测试用户评估列表API ===")
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    response = requests.get(f"{BASE_URL}/api/mobile/assessments", headers=headers)
    
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"响应数据类型: {type(data)}")
        
        if isinstance(data, dict) and 'data' in data:
            assessments = data['data']
            print(f"评估数量: {len(assessments)}")
            
            for i, assessment in enumerate(assessments[:2]):  # 只显示前2个
                print(f"\n评估 {i+1}:")
                print(f"  ID: {assessment.get('id')}")
                print(f"  名称: {assessment.get('name')}")
                print(f"  状态: {assessment.get('status')}")
                
                # 检查assessment级别的字段
                print(f"  Assessment级别的description: {assessment.get('description')}")
                print(f"  Assessment级别的instructions: {assessment.get('instructions')}")
                
                # 检查template级别的字段
                template = assessment.get('template', {})
                print(f"  Template级别的description: {template.get('description')}")
                print(f"  Template级别的instructions: {template.get('instructions')}")
                print(f"  Template问题数量: {len(template.get('questions', []))}")
        else:
            print(f"响应数据: {data}")
    else:
        print(f"请求失败: {response.text}")

def main():
    print("开始测试移动端API的description和instructions字段")
    
    # 登录
    token = login()
    if not token:
        print("登录失败，无法继续测试")
        return
    
    print(f"登录成功，token: {token[:20]}...")
    
    # 测试评估模板API
    test_assessment_templates_api(token)
    
    # 测试用户评估列表API
    test_assessments_api(token)
    
    print("\n测试完成")

if __name__ == "__main__":
    main()