#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试前端API修复后的效果
验证评估记录API调用是否正确
"""

import requests
import json
from datetime import datetime

# API基础URL
BASE_URL = "http://localhost:8006"

def test_login():
    """测试登录并获取token"""
    login_url = f"{BASE_URL}/api/auth/login"
    login_data = {
        "username": "markey",
        "password": "markey0308@163"
    }
    
    print("正在测试登录...")
    # 使用表单数据而不是JSON
    response = requests.post(login_url, data=login_data)
    
    if response.status_code == 200:
        result = response.json()
        print(f"登录响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        # 尝试不同的token字段名
        token = result.get('access_token') or result.get('token') or result.get('data', {}).get('token')
        user_info = result.get('user', {}) or result.get('data', {}).get('user', {})
        custom_id = user_info.get('custom_id')
        
        print(f"✅ 登录成功")
        if token:
            print(f"Token: {token[:20]}...")
        else:
            print("⚠️ 未找到token")
        print(f"Custom ID: {custom_id}")
        
        return token, custom_id
    else:
        print(f"❌ 登录失败: {response.status_code}")
        print(f"响应: {response.text}")
        return None, None

def test_assessment_records_api(token, custom_id):
    """测试修复后的评估记录API"""
    if not token or not custom_id:
        print("❌ 缺少token或custom_id，跳过测试")
        return
    
    # 测试正确的API路径
    api_url = f"{BASE_URL}/api/user-health-records/{custom_id}?record_type=assessment"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    print(f"\n正在测试评估记录API: {api_url}")
    print(f"请求头: Authorization: Bearer {token[:20]}...")
    
    response = requests.get(api_url, headers=headers)
    
    print(f"响应状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ API调用成功")
        print(f"返回数据结构: {json.dumps(result, indent=2, ensure_ascii=False)[:500]}...")
        
        # 检查返回数据结构
        if result.get('status') == 'success':
            records = result.get('records', [])
            total = result.get('total', 0)
            print(f"✅ 数据结构正确")
            print(f"评估记录总数: {total}")
            print(f"当前页记录数量: {len(records)}")
            if records:
                print(f"第一条记录示例: {json.dumps(records[0], indent=2, ensure_ascii=False)}")
            else:
                print("📝 暂无评估记录")
        else:
            print("⚠️ 数据结构不符合预期 - API返回状态不是success")
            print(f"实际状态: {result.get('status', 'unknown')}")
            
    else:
        print(f"❌ API调用失败")
        print(f"错误响应: {response.text}")

def test_assessments_list_api(token):
    """测试评估量表列表API"""
    if not token:
        print("❌ 缺少token，跳过测试")
        return
    
    api_url = f"{BASE_URL}/api/assessments"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    print(f"\n正在测试评估量表列表API: {api_url}")
    
    response = requests.get(api_url, headers=headers)
    
    print(f"响应状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ API调用成功")
        print(f"评估量表数量: {len(result)}")
        if result:
            print(f"第一个量表示例: {json.dumps(result[0], indent=2, ensure_ascii=False)}")
    else:
        print(f"❌ API调用失败")
        print(f"错误响应: {response.text}")

def main():
    print("=== 前端API修复验证测试 ===")
    print(f"测试时间: {datetime.now()}")
    print(f"后端URL: {BASE_URL}")
    
    # 1. 登录获取token和custom_id
    token, custom_id = test_login()
    
    # 2. 测试评估量表列表API
    test_assessments_list_api(token)
    
    # 3. 测试修复后的评估记录API
    test_assessment_records_api(token, custom_id)
    
    print("\n=== 测试完成 ===")
    print("\n修复说明:")
    print("1. 修复了前端fetchAssessmentRecords函数中的API路径")
    print("2. 从 '/api/assessments/records' 改为 '/api/user-health-records/{custom_id}?record_type=assessment'")
    print("3. 添加了获取用户custom_id的逻辑")
    print("4. 确保所有API调用都包含正确的Authorization头")

if __name__ == "__main__":
    main()