{"summary": {"total_checks": 33, "successful": 21, "failed": 12, "success_rate": "63.6%", "total_time": "6.30s", "timestamp": "2025-07-01T12:53:01.206701"}, "available_routes": ["/api", "/api/questionnaires", "/api/dashboard", "/api/user-health-records"], "failed_checks": [{"fix_name": "登录端点 /api/auth/login", "success": false, "details": "状态码: 422, 格式: json", "timestamp": "2025-07-01T12:52:57.064695"}, {"fix_name": "路由检测 /api/v1", "success": false, "details": "状态码: 404", "timestamp": "2025-07-01T12:52:59.491958"}, {"fix_name": "路由检测 /api/v1/aggregated", "success": false, "details": "状态码: 404", "timestamp": "2025-07-01T12:52:59.514575"}, {"fix_name": "路由检测 /api/templates", "success": false, "details": "状态码: 404", "timestamp": "2025-07-01T12:52:59.592740"}, {"fix_name": "路由检测 /api/health-records", "success": false, "details": "状态码: 404", "timestamp": "2025-07-01T12:52:59.612309"}, {"fix_name": "路由检测 /api/medical-records", "success": false, "details": "状态码: 404", "timestamp": "2025-07-01T12:52:59.631230"}, {"fix_name": "路由检测 /api/data-management", "success": false, "details": "状态码: 404", "timestamp": "2025-07-01T12:52:59.708134"}, {"fix_name": "路由检测 /api/clinical-scales", "success": false, "details": "状态码: 404", "timestamp": "2025-07-01T12:52:59.733794"}, {"fix_name": "路由检测 /api/mobile", "success": false, "details": "状态码: 404", "timestamp": "2025-07-01T12:52:59.832218"}, {"fix_name": "聚合API /api/v1/aggregated/questionnaires", "success": false, "details": "状态码: 404", "timestamp": "2025-07-01T12:53:01.087753"}, {"fix_name": "聚合API /api/v1/aggregated/assessments", "success": false, "details": "状态码: 404", "timestamp": "2025-07-01T12:53:01.102369"}, {"fix_name": "数据库端点 /api/health/database", "success": false, "details": "状态码: 404", "timestamp": "2025-07-01T12:53:01.113092"}], "all_results": [{"fix_name": "登录端点 /api/auth/login", "success": false, "details": "状态码: 422, 格式: json", "timestamp": "2025-07-01T12:52:57.064695"}, {"fix_name": "登录端点 /api/auth/login", "success": true, "details": "成功获取令牌，格式: form", "timestamp": "2025-07-01T12:52:59.282107"}, {"fix_name": "路由检测 /api", "success": true, "details": "状态码: 200", "timestamp": "2025-07-01T12:52:59.328949"}, {"fix_name": "路由检测 /api/v1", "success": false, "details": "状态码: 404", "timestamp": "2025-07-01T12:52:59.491958"}, {"fix_name": "路由检测 /api/v1/aggregated", "success": false, "details": "状态码: 404", "timestamp": "2025-07-01T12:52:59.514575"}, {"fix_name": "路由检测 /api/questionnaires", "success": true, "details": "状态码: 200", "timestamp": "2025-07-01T12:52:59.564757"}, {"fix_name": "路由检测 /api/templates", "success": false, "details": "状态码: 404", "timestamp": "2025-07-01T12:52:59.592740"}, {"fix_name": "路由检测 /api/health-records", "success": false, "details": "状态码: 404", "timestamp": "2025-07-01T12:52:59.612309"}, {"fix_name": "路由检测 /api/medical-records", "success": false, "details": "状态码: 404", "timestamp": "2025-07-01T12:52:59.631230"}, {"fix_name": "路由检测 /api/dashboard", "success": true, "details": "状态码: 200", "timestamp": "2025-07-01T12:52:59.691504"}, {"fix_name": "路由检测 /api/data-management", "success": false, "details": "状态码: 404", "timestamp": "2025-07-01T12:52:59.708134"}, {"fix_name": "路由检测 /api/clinical-scales", "success": false, "details": "状态码: 404", "timestamp": "2025-07-01T12:52:59.733794"}, {"fix_name": "路由检测 /api/user-health-records", "success": true, "details": "状态码: 200", "timestamp": "2025-07-01T12:52:59.808791"}, {"fix_name": "路由检测 /api/mobile", "success": false, "details": "状态码: 404", "timestamp": "2025-07-01T12:52:59.832218"}, {"fix_name": "公开端点 /api/health", "success": true, "details": "状态码: 200", "timestamp": "2025-07-01T12:52:59.856700"}, {"fix_name": "公开端点 /api/health/ping", "success": true, "details": "状态码: 200", "timestamp": "2025-07-01T12:52:59.956424"}, {"fix_name": "公开端点 /api/health/check", "success": true, "details": "状态码: 200", "timestamp": "2025-07-01T12:52:59.997685"}, {"fix_name": "公开端点 /api/v1/aggregated/health", "success": true, "details": "状态码: 200", "timestamp": "2025-07-01T12:53:00.018736"}, {"fix_name": "公开端点 /docs", "success": true, "details": "状态码: 200", "timestamp": "2025-07-01T12:53:00.055027"}, {"fix_name": "公开端点 /openapi.json", "success": true, "details": "状态码: 200", "timestamp": "2025-07-01T12:53:00.776565"}, {"fix_name": "受保护端点 /api/users/me", "success": true, "details": "状态码: 200", "timestamp": "2025-07-01T12:53:00.799421"}, {"fix_name": "受保护端点 /api/v1/aggregated/data-sources/status", "success": true, "details": "状态码: 200", "timestamp": "2025-07-01T12:53:00.828825"}, {"fix_name": "受保护端点 /api/dashboard", "success": true, "details": "状态码: 200", "timestamp": "2025-07-01T12:53:00.889670"}, {"fix_name": "受保护端点 /api/questionnaires", "success": true, "details": "状态码: 200", "timestamp": "2025-07-01T12:53:00.926285"}, {"fix_name": "聚合API /api/v1/aggregated/health", "success": true, "details": "状态码: 200", "timestamp": "2025-07-01T12:53:00.942309"}, {"fix_name": "聚合API /api/v1/aggregated/data-sources/status", "success": true, "details": "状态码: 200", "timestamp": "2025-07-01T12:53:00.963838"}, {"fix_name": "聚合API /api/v1/aggregated/users/admin/data", "success": true, "details": "状态码: 200", "timestamp": "2025-07-01T12:53:01.074178"}, {"fix_name": "聚合API /api/v1/aggregated/questionnaires", "success": false, "details": "状态码: 404", "timestamp": "2025-07-01T12:53:01.087753"}, {"fix_name": "聚合API /api/v1/aggregated/assessments", "success": false, "details": "状态码: 404", "timestamp": "2025-07-01T12:53:01.102369"}, {"fix_name": "数据库端点 /api/health/database", "success": false, "details": "状态码: 404", "timestamp": "2025-07-01T12:53:01.113092"}, {"fix_name": "数据库端点 /api/v1/aggregated/health", "success": true, "details": "端点可访问，状态码: 200", "timestamp": "2025-07-01T12:53:01.128950"}, {"fix_name": "数据库端点 /api/dashboard/debug", "success": true, "details": "数据库连接正常", "timestamp": "2025-07-01T12:53:01.155779"}, {"fix_name": "路由映射生成", "success": true, "details": "发现 290 个端点，保存到 api_route_mapping_20250701_125301.json", "timestamp": "2025-07-01T12:53:01.203616"}], "recommendations": ["检查认证端点的请求格式，确保使用正确的字段名和数据格式", "检查API路由配置，确保所有模块都正确注册到主路由器", "检查数据库连接配置和数据库服务状态", "检查聚合API模块的导入和注册，确保所有依赖都正确安装"]}