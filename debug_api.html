<!DOCTYPE html>
<html>
<head>
    <title>API调试</title>
</head>
<body>
    <h1>API调试页面</h1>
    <button onclick="testAPIs()">测试API</button>
    <div id="results"></div>

    <script>
        async function testAPIs() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '正在测试API...';
            
            try {
                // 测试问卷API
                console.log('测试问卷API...');
                const questionnaireResponse = await fetch('/api/questionnaires?page=1&page_size=10', {
                    headers: {
                        'Authorization': 'Bearer test_token',
                        'Content-Type': 'application/json'
                    }
                });
                
                const questionnaireData = await questionnaireResponse.json();
                console.log('问卷API响应:', questionnaireData);
                
                // 测试标准问卷API
                console.log('测试标准问卷API...');
                const standardResponse = await fetch('/api/clinical-scales/standard-questionnaires', {
                    headers: {
                        'Authorization': 'Bearer test_token',
                        'Content-Type': 'application/json'
                    }
                });
                
                const standardData = await standardResponse.json();
                console.log('标准问卷API响应:', standardData);
                
                // 显示结果
                resultsDiv.innerHTML = `
                    <h2>API测试结果</h2>
                    <h3>问卷API (${questionnaireResponse.status})</h3>
                    <pre>${JSON.stringify(questionnaireData, null, 2)}</pre>
                    <h3>标准问卷API (${standardResponse.status})</h3>
                    <pre>${JSON.stringify(standardData, null, 2)}</pre>
                `;
                
            } catch (error) {
                console.error('API测试失败:', error);
                resultsDiv.innerHTML = `<p style="color: red;">API测试失败: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>