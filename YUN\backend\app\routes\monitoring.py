#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控相关API路由
提供量表问卷全链路监控的API接口
"""

import os
import sys
import json
import subprocess
from datetime import datetime
from flask import Blueprint, jsonify, request, send_file
from flask_jwt_extended import jwt_required
from werkzeug.exceptions import BadRequest

# 添加scripts目录到Python路径
scripts_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), '..', 'scripts')
sys.path.insert(0, scripts_dir)

try:
    from assessment_questionnaire_monitor import AssessmentQuestionnaireMonitor, MonitorStatus, WorkflowStage
except ImportError as e:
    print(f"Warning: Could not import monitoring modules: {e}")
    AssessmentQuestionnaireMonitor = None
    MonitorStatus = None
    WorkflowStage = None

monitoring_bp = Blueprint('monitoring', __name__, url_prefix='/api/monitoring')

@monitoring_bp.route('/assessment-questionnaire', methods=['GET'])
@jwt_required()
def get_assessment_questionnaire_monitoring():
    """
    获取量表问卷监控数据
    """
    try:
        if not AssessmentQuestionnaireMonitor:
            return jsonify({
                'error': '监控模块未正确加载',
                'total_checks': 0,
                'healthy_count': 0,
                'warning_count': 0,
                'error_count': 0,
                'stages': [],
                'logs': []
            }), 500
        
        # 创建监控实例
        monitor = AssessmentQuestionnaireMonitor()
        
        try:
            # 运行完整检查
            monitor.run_full_check()
            
            # 统计结果
            total_checks = len(monitor.monitor_results)
            healthy_count = sum(1 for r in monitor.monitor_results if r.status == MonitorStatus.HEALTHY)
            warning_count = sum(1 for r in monitor.monitor_results if r.status == MonitorStatus.WARNING)
            error_count = sum(1 for r in monitor.monitor_results if r.status == MonitorStatus.ERROR)
            
            # 按阶段组织结果
            stages_data = {}
            for result in monitor.monitor_results:
                stage_name = result.stage.value
                if stage_name not in stages_data:
                    stages_data[stage_name] = {
                        'name': stage_name,
                        'display_name': get_stage_display_name(result.stage),
                        'status': 'HEALTHY',
                        'description': get_stage_description(result.stage),
                        'issues': [],
                        'metrics': {}
                    }
                
                # 更新阶段状态（优先级：ERROR > WARNING > HEALTHY）
                current_status = stages_data[stage_name]['status']
                if result.status == MonitorStatus.ERROR or \
                   (result.status == MonitorStatus.WARNING and current_status == 'HEALTHY'):
                    stages_data[stage_name]['status'] = result.status.value
                
                # 添加问题描述
                if result.message and result.status in [MonitorStatus.WARNING, MonitorStatus.ERROR]:
                    stages_data[stage_name]['issues'].append(result.message)
                
                # 添加指标数据
                if result.details:
                    stages_data[stage_name]['metrics'].update(result.details)
            
            # 生成日志数据
            logs = []
            for result in monitor.monitor_results[-50:]:  # 最近50条
                logs.append({
                    'timestamp': result.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                    'stage': get_stage_display_name(result.stage),
                    'status': result.status.value,
                    'message': result.message or '检查完成'
                })
            
            return jsonify({
                'total_checks': total_checks,
                'healthy_count': healthy_count,
                'warning_count': warning_count,
                'error_count': error_count,
                'timestamp': datetime.now().isoformat(),
                'stages': list(stages_data.values()),
                'logs': logs
            })
            
        finally:
            monitor.close()
            
    except Exception as e:
        print(f"监控检查失败: {e}")
        return jsonify({
            'error': f'监控检查失败: {str(e)}',
            'total_checks': 0,
            'healthy_count': 0,
            'warning_count': 0,
            'error_count': 0,
            'stages': [],
            'logs': []
        }), 500

@monitoring_bp.route('/fix-issues', methods=['POST'])
@jwt_required()
def fix_monitoring_issues():
    """
    修复监控发现的问题
    """
    try:
        # 运行修复脚本
        script_path = os.path.join(scripts_dir, 'fix_monitoring_issues.py')
        
        if not os.path.exists(script_path):
            return jsonify({
                'error': '修复脚本不存在',
                'message': '未找到fix_monitoring_issues.py脚本'
            }), 404
        
        # 执行修复脚本
        result = subprocess.run(
            [sys.executable, script_path],
            cwd=scripts_dir,
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace'  # 处理编码错误
        )
        
        if result.returncode == 0:
            # 解析输出获取修复信息
            output_lines = result.stdout.strip().split('\n')
            fixed_count = 0
            
            for line in output_lines:
                if '修复了' in line and '条记录' in line:
                    try:
                        # 提取修复数量
                        import re
                        match = re.search(r'修复了(\d+)条记录', line)
                        if match:
                            fixed_count += int(match.group(1))
                    except:
                        pass
            
            return jsonify({
                'success': True,
                'message': f'修复完成，共修复 {fixed_count} 条记录',
                'output': result.stdout,
                'fixed_count': fixed_count
            })
        else:
            return jsonify({
                'error': '修复脚本执行失败',
                'message': result.stderr or result.stdout,
                'returncode': result.returncode
            }), 500
            
    except Exception as e:
        print(f"修复问题失败: {e}")
        return jsonify({
            'error': f'修复问题失败: {str(e)}'
        }), 500

@monitoring_bp.route('/export-report', methods=['GET'])
@jwt_required()
def export_monitoring_report():
    """
    导出监控报告
    """
    try:
        if not AssessmentQuestionnaireMonitor:
            return jsonify({'error': '监控模块未正确加载'}), 500
        
        # 创建监控实例
        monitor = AssessmentQuestionnaireMonitor()
        
        try:
            # 运行完整检查
            monitor.run_full_check()
            
            # 生成报告文件
            report_filename = f"monitor_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            report_path = os.path.join(scripts_dir, report_filename)
            
            # 导出报告
            monitor.export_report(report_path)
            
            # 检查文件是否存在
            if os.path.exists(report_path):
                return send_file(
                    report_path,
                    as_attachment=True,
                    download_name=report_filename,
                    mimetype='application/json'
                )
            else:
                return jsonify({'error': '报告文件生成失败'}), 500
                
        finally:
            monitor.close()
            
    except Exception as e:
        print(f"导出报告失败: {e}")
        return jsonify({'error': f'导出报告失败: {str(e)}'}), 500

@monitoring_bp.route('/check-user/<int:user_id>', methods=['GET'])
@jwt_required()
def check_user_monitoring(user_id):
    """
    检查指定用户的监控状态
    """
    try:
        if not AssessmentQuestionnaireMonitor:
            return jsonify({'error': '监控模块未正确加载'}), 500
        
        # 创建监控实例
        monitor = AssessmentQuestionnaireMonitor()
        
        try:
            # 运行用户特定检查
            monitor.run_full_check(user_id=user_id)
            
            # 返回结果
            results = []
            for result in monitor.monitor_results:
                results.append({
                    'stage': get_stage_display_name(result.stage),
                    'status': result.status.value,
                    'message': result.message,
                    'timestamp': result.timestamp.isoformat(),
                    'details': result.details
                })
            
            return jsonify({
                'user_id': user_id,
                'results': results,
                'total_checks': len(results),
                'issues_found': any(r['status'] in ['WARNING', 'ERROR'] for r in results)
            })
            
        finally:
            monitor.close()
            
    except Exception as e:
        print(f"检查用户监控失败: {e}")
        return jsonify({'error': f'检查用户监控失败: {str(e)}'}), 500

@monitoring_bp.route('/real-time', methods=['GET'])
@jwt_required()
def get_realtime_monitoring():
    """
    获取实时监控数据（简化版）
    """
    try:
        if not AssessmentQuestionnaireMonitor:
            return jsonify({'error': '监控模块未正确加载'}), 500
        
        # 创建监控实例
        monitor = AssessmentQuestionnaireMonitor()
        
        try:
            # 运行快速检查（只检查关键指标）
            monitor.check_template_loading()
            monitor.check_distribution_stage()
            monitor.check_mobile_fetch_stage()
            
            # 统计结果
            total_checks = len(monitor.monitor_results)
            healthy_count = sum(1 for r in monitor.monitor_results if r.status == MonitorStatus.HEALTHY)
            warning_count = sum(1 for r in monitor.monitor_results if r.status == MonitorStatus.WARNING)
            error_count = sum(1 for r in monitor.monitor_results if r.status == MonitorStatus.ERROR)
            
            return jsonify({
                'timestamp': datetime.now().isoformat(),
                'total_checks': total_checks,
                'healthy_count': healthy_count,
                'warning_count': warning_count,
                'error_count': error_count,
                'status': 'ERROR' if error_count > 0 else 'WARNING' if warning_count > 0 else 'HEALTHY'
            })
            
        finally:
            monitor.close()
            
    except Exception as e:
        print(f"获取实时监控数据失败: {e}")
        return jsonify({'error': f'获取实时监控数据失败: {str(e)}'}), 500

def get_stage_display_name(stage):
    """
    获取阶段显示名称
    """
    stage_names = {
        'template_loading': '模板加载',
        'distribution': '分发阶段',
        'mobile_fetch': '移动端获取',
        'mobile_submit': '移动端提交',
        'backend_calculation': '后端计算',
        'database_save': '数据库保存',
        'frontend_query': '前端查询',
        'mobile_query': '移动端查询'
    }
    
    if hasattr(stage, 'value'):
        return stage_names.get(stage.value, stage.value)
    return stage_names.get(str(stage), str(stage))

def get_stage_description(stage):
    """
    获取阶段描述
    """
    descriptions = {
        'template_loading': '检查量表问卷模板是否正确加载到数据库',
        'distribution': '检查量表问卷分发状态和配置',
        'mobile_fetch': '检查移动端获取量表问卷的状态',
        'mobile_submit': '检查移动端提交答案的状态',
        'backend_calculation': '检查后端计算和报告生成',
        'database_save': '检查数据保存和完整性',
        'frontend_query': '检查前端查询功能',
        'mobile_query': '检查移动端查询功能'
    }
    
    if hasattr(stage, 'value'):
        return descriptions.get(stage.value, '未知阶段')
    return descriptions.get(str(stage), '未知阶段')

# 错误处理
@monitoring_bp.errorhandler(400)
def bad_request(error):
    return jsonify({'error': '请求参数错误'}), 400

@monitoring_bp.errorhandler(404)
def not_found(error):
    return jsonify({'error': '资源未找到'}), 404

@monitoring_bp.errorhandler(500)
def internal_error(error):
    return jsonify({'error': '服务器内部错误'}), 500