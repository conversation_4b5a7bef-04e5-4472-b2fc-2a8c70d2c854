#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复评估模板计分规则
将标准模板中的计分规则更新到数据库中
"""

import sqlite3
import json
import os
import sys

# 添加项目路径到sys.path
sys.path.append(os.path.join(os.path.dirname(__file__), "YUN", "backend"))

from app.clinical_scales.assessment import ALL_ASSESSMENT_TEMPLATES

def main():
    # 数据库路径
    db_path = os.path.join(os.path.dirname(__file__), "YUN", "app.db")
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        print("=== 修复评估模板计分规则 ===")
        
        # 获取所有标准模板
        print(f"\n找到 {len(ALL_ASSESSMENT_TEMPLATES)} 个标准模板")
        
        for template_key, template_data in ALL_ASSESSMENT_TEMPLATES.items():
            print(f"\n处理模板: {template_data['name']} ({template_key})")
            
            # 查找对应的数据库模板
            cursor.execute("""
                SELECT id FROM assessment_templates 
                WHERE name = ? OR template_key = ?
            """, (template_data['name'], template_key))
            
            template_result = cursor.fetchone()
            if not template_result:
                print(f"  ⚠️ 数据库中未找到模板: {template_data['name']}")
                continue
            
            template_id = template_result[0]
            print(f"  找到数据库模板ID: {template_id}")
            
            # 更新每个问题的计分规则
            questions = template_data.get('questions', [])
            updated_count = 0
            
            for question in questions:
                question_id = question['question_id']
                options = question.get('options', [])
                
                if not options:
                    continue
                
                # 构建计分规则
                scoring_rules = {
                    "option_scores": {}
                }
                
                for option in options:
                    value = str(option['value'])
                    score = option['score']
                    scoring_rules["option_scores"][value] = score
                
                # 更新数据库
                cursor.execute("""
                    UPDATE assessment_template_questions 
                    SET scoring = ?
                    WHERE template_id = ? AND question_id = ?
                """, (json.dumps(scoring_rules), template_id, question_id))
                
                if cursor.rowcount > 0:
                    updated_count += 1
                    print(f"    更新问题 {question_id}: {scoring_rules}")
                else:
                    print(f"    ⚠️ 未找到问题: {question_id}")
            
            print(f"  ✅ 更新了 {updated_count} 个问题的计分规则")
        
        # 提交更改
        conn.commit()
        print("\n=== 修复完成 ===")
        
        # 验证修复结果
        print("\n=== 验证修复结果 ===")
        cursor.execute("""
            SELECT at.name, COUNT(*) as total_questions,
                   SUM(CASE WHEN atq.scoring IS NOT NULL AND atq.scoring != '' THEN 1 ELSE 0 END) as with_scoring
            FROM assessment_templates at
            LEFT JOIN assessment_template_questions atq ON at.id = atq.template_id
            GROUP BY at.id, at.name
            ORDER BY at.name
        """)
        
        results = cursor.fetchall()
        for result in results:
            name, total, with_scoring = result
            print(f"  {name}: {with_scoring}/{total} 个问题有计分规则")
        
    except Exception as e:
        print(f"修复过程中出错: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    main()