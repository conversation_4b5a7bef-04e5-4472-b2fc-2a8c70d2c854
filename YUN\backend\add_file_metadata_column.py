#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为documents表添加file_metadata列
"""

import sqlite3
import os
from pathlib import Path

def add_file_metadata_column():
    """为documents表添加file_metadata列"""
    
    # 数据库文件路径
    db_path = Path(__file__).parent / "app.db"
    
    if not db_path.exists():
        print(f"数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='documents'
        """)
        
        if not cursor.fetchone():
            print("documents表不存在")
            return False
        
        # 获取当前表结构
        cursor.execute("PRAGMA table_info(documents)")
        columns = [row[1] for row in cursor.fetchall()]
        print(f"当前documents表包含 {len(columns)} 个列")
        
        # 检查file_metadata列是否存在
        if 'file_metadata' not in columns:
            try:
                # 添加file_metadata列
                alter_sql = "ALTER TABLE documents ADD COLUMN file_metadata TEXT"
                cursor.execute(alter_sql)
                print("✓ 成功添加file_metadata列 (TEXT)")
                
                # 提交更改
                conn.commit()
                
                # 验证添加结果
                cursor.execute("PRAGMA table_info(documents)")
                updated_columns = [row[1] for row in cursor.fetchall()]
                print(f"更新后documents表包含 {len(updated_columns)} 个列")
                
                return True
                
            except sqlite3.Error as e:
                print(f"✗ 添加file_metadata列失败: {e}")
                return False
        else:
            print("○ file_metadata列已存在")
            return True
        
    except sqlite3.Error as e:
        print(f"数据库操作错误: {e}")
        return False
    except Exception as e:
        print(f"未知错误: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    print("开始为documents表添加file_metadata列...")
    success = add_file_metadata_column()
    
    if success:
        print("\n✓ file_metadata列添加完成")
    else:
        print("\n✗ file_metadata列添加失败")