<template>
  <div class="system-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><Setting /></el-icon>
        系统管理
      </h1>
      <p class="page-description">统一管理平台 - 项目、服务、配置、测试和部署管理</p>
    </div>

    <!-- 系统状态概览 -->
    <el-card class="status-overview" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>系统状态概览</span>
          <el-button 
            type="primary" 
            size="small" 
            @click="refreshStatus"
            :loading="statusLoading"
          >
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="status-item">
            <div class="status-icon backend" :class="systemStatus.backend?.status || 'unknown'">
              <el-icon><Monitor /></el-icon>
            </div>
            <div class="status-info">
              <h3>后端服务</h3>
              <p>{{ getStatusText(systemStatus.backend?.status) }}</p>
              <small>{{ systemStatus.backend?.uptime || '未知' }}</small>
            </div>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="status-item">
            <div class="status-icon frontend" :class="systemStatus.frontend?.status || 'unknown'">
              <el-icon><Monitor /></el-icon>
            </div>
            <div class="status-info">
              <h3>前端服务</h3>
              <p>{{ getStatusText(systemStatus.frontend?.status) }}</p>
              <small>{{ systemStatus.frontend?.uptime || '未知' }}</small>
            </div>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="status-item">
            <div class="status-icon database" :class="systemStatus.database?.status || 'unknown'">
              <el-icon><DataBoard /></el-icon>
            </div>
            <div class="status-info">
              <h3>数据库</h3>
              <p>{{ getStatusText(systemStatus.database?.status) }}</p>
              <small>{{ systemStatus.database?.connections || '0' }} 连接</small>
            </div>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="status-item">
            <div class="status-icon system" :class="systemStatus.system?.status || 'unknown'">
              <el-icon><Cpu /></el-icon>
            </div>
            <div class="status-info">
              <h3>系统资源</h3>
              <p>{{ getStatusText(systemStatus.system?.status) }}</p>
              <small>CPU: {{ systemStatus.system?.cpu || '0' }}%</small>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 管理功能模块 -->
    <div class="management-modules">
      <el-row :gutter="20">
        <el-col :span="8" v-for="module in managementModules" :key="module.name">
          <el-card class="module-card" shadow="hover" @click="navigateToModule(module.route)">
            <div class="module-content">
              <div class="module-icon" :class="module.color">
                <el-icon :size="32">
                  <component :is="module.icon" />
                </el-icon>
              </div>
              <div class="module-info">
                <h3>{{ module.title }}</h3>
                <p>{{ module.description }}</p>
                <div class="module-stats">
                  <span class="stat-item">
                    <el-icon><CircleCheck /></el-icon>
                    {{ module.stats.active || 0 }} 活跃
                  </span>
                  <span class="stat-item">
                    <el-icon><Warning /></el-icon>
                    {{ module.stats.warnings || 0 }} 警告
                  </span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 快速操作 -->
    <el-card class="quick-actions" shadow="hover">
      <template #header>
        <span>快速操作</span>
      </template>
      
      <div class="action-buttons">
        <el-button-group>
          <el-button 
            type="success" 
            @click="startAllServices"
            :loading="actionLoading.startAll"
          >
            <el-icon><VideoPlay /></el-icon>
            启动所有服务
          </el-button>
          
          <el-button 
            type="warning" 
            @click="restartAllServices"
            :loading="actionLoading.restartAll"
          >
            <el-icon><Refresh /></el-icon>
            重启所有服务
          </el-button>
          
          <el-button 
            type="danger" 
            @click="stopAllServices"
            :loading="actionLoading.stopAll"
          >
            <el-icon><VideoPause /></el-icon>
            停止所有服务
          </el-button>
        </el-button-group>
        
        <el-divider direction="vertical" />
        
        <el-button-group>
          <el-button 
            type="primary" 
            @click="runAllTests"
            :loading="actionLoading.runTests"
          >
            <el-icon><Checked /></el-icon>
            运行所有测试
          </el-button>
          
          <el-button 
            type="info" 
            @click="buildProject"
            :loading="actionLoading.build"
          >
            <el-icon><Tools /></el-icon>
            构建项目
          </el-button>
          
          <el-button 
            type="success" 
            @click="deployProject"
            :loading="actionLoading.deploy"
          >
            <el-icon><Upload /></el-icon>
            部署项目
          </el-button>
        </el-button-group>
      </div>
    </el-card>

    <!-- 系统日志 -->
    <el-card class="system-logs" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>系统日志</span>
          <div class="log-controls">
            <el-select v-model="logLevel" size="small" style="width: 100px; margin-right: 10px;">
              <el-option label="全部" value="all" />
              <el-option label="错误" value="error" />
              <el-option label="警告" value="warning" />
              <el-option label="信息" value="info" />
            </el-select>
            <el-button size="small" @click="clearLogs">
              <el-icon><Delete /></el-icon>
              清空
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="log-container">
        <div 
          v-for="(log, index) in filteredLogs" 
          :key="index"
          class="log-entry"
          :class="log.level"
        >
          <span class="log-time">{{ formatTime(log.timestamp) }}</span>
          <span class="log-level">{{ log.level.toUpperCase() }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
        
        <div v-if="filteredLogs.length === 0" class="no-logs">
          <el-empty description="暂无日志" />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Setting,
  Monitor,
  DataBoard,
  Cpu,
  Refresh,
  CircleCheck,
  Warning,
  VideoPlay,
  VideoPause,
  Checked,
  Tools,
  Upload,
  Delete
} from '@element-plus/icons-vue';
import axios from 'axios';

const router = useRouter();

// 响应式数据
const statusLoading = ref(false);
const logLevel = ref('all');
const systemStatus = reactive({
  backend: { status: 'unknown', uptime: '未知' },
  frontend: { status: 'unknown', uptime: '未知' },
  database: { status: 'unknown', connections: 0 },
  system: { status: 'unknown', cpu: 0 }
});

const actionLoading = reactive({
  startAll: false,
  restartAll: false,
  stopAll: false,
  runTests: false,
  build: false,
  deploy: false
});

const systemLogs = ref([]);
const statusCheckInterval = ref(null);

// 管理模块配置
const managementModules = ref([
  {
    name: 'project',
    title: '项目管理',
    description: '项目组件管理、状态监控',
    icon: 'FolderOpened',
    color: 'blue',
    route: '/admin/project-management',
    stats: { active: 3, warnings: 0 }
  },
  {
    name: 'service',
    title: '服务管理',
    description: '前后端服务启停、监控',
    icon: 'Monitor',
    color: 'green',
    route: '/admin/service-management',
    stats: { active: 2, warnings: 1 }
  },
  {
    name: 'config',
    title: '配置管理',
    description: '系统配置、环境变量管理',
    icon: 'Setting',
    color: 'orange',
    route: '/admin/config-management',
    stats: { active: 5, warnings: 0 }
  },
  {
    name: 'test',
    title: '测试管理',
    description: '自动化测试、测试报告',
    icon: 'Checked',
    color: 'purple',
    route: '/admin/test-management',
    stats: { active: 8, warnings: 2 }
  },
  {
    name: 'deployment',
    title: '部署管理',
    description: '构建部署、版本管理',
    icon: 'Upload',
    color: 'red',
    route: '/admin/deployment-management',
    stats: { active: 1, warnings: 0 }
  },
  {
    name: 'monitoring',
    title: '监控告警',
    description: '系统监控、性能分析',
    icon: 'DataAnalysis',
    color: 'cyan',
    route: '/admin/system-monitoring',
    stats: { active: 12, warnings: 3 }
  }
]);

// 计算属性
const filteredLogs = computed(() => {
  if (logLevel.value === 'all') {
    return systemLogs.value;
  }
  return systemLogs.value.filter(log => log.level === logLevel.value);
});

// 方法
const getStatusText = (status) => {
  const statusMap = {
    'healthy': '正常',
    'warning': '警告',
    'error': '错误',
    'unknown': '未知'
  };
  return statusMap[status] || '未知';
};

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString();
};

const navigateToModule = (route) => {
  router.push(route);
};

const refreshStatus = async () => {
  statusLoading.value = true;
  try {
    const response = await axios.get('/api/management/status');
    Object.assign(systemStatus, response.data);
    
    addLog('info', '系统状态已刷新');
  } catch (error) {
    console.error('获取系统状态失败:', error);
    addLog('error', `获取系统状态失败: ${error.message}`);
    ElMessage.error('获取系统状态失败');
  } finally {
    statusLoading.value = false;
  }
};

const startAllServices = async () => {
  try {
    await ElMessageBox.confirm('确定要启动所有服务吗？', '确认操作', {
      type: 'warning'
    });
    
    actionLoading.startAll = true;
    addLog('info', '正在启动所有服务...');
    
    const response = await axios.post('/api/management/services/start-all');
    
    addLog('info', '所有服务启动完成');
    ElMessage.success('所有服务启动成功');
    
    // 刷新状态
    await refreshStatus();
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('启动服务失败:', error);
      addLog('error', `启动服务失败: ${error.message}`);
      ElMessage.error('启动服务失败');
    }
  } finally {
    actionLoading.startAll = false;
  }
};

const restartAllServices = async () => {
  try {
    await ElMessageBox.confirm('确定要重启所有服务吗？', '确认操作', {
      type: 'warning'
    });
    
    actionLoading.restartAll = true;
    addLog('info', '正在重启所有服务...');
    
    const response = await axios.post('/api/management/services/restart-all');
    
    addLog('info', '所有服务重启完成');
    ElMessage.success('所有服务重启成功');
    
    // 刷新状态
    await refreshStatus();
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重启服务失败:', error);
      addLog('error', `重启服务失败: ${error.message}`);
      ElMessage.error('重启服务失败');
    }
  } finally {
    actionLoading.restartAll = false;
  }
};

const stopAllServices = async () => {
  try {
    await ElMessageBox.confirm('确定要停止所有服务吗？', '确认操作', {
      type: 'warning'
    });
    
    actionLoading.stopAll = true;
    addLog('info', '正在停止所有服务...');
    
    const response = await axios.post('/api/management/services/stop-all');
    
    addLog('info', '所有服务已停止');
    ElMessage.success('所有服务停止成功');
    
    // 刷新状态
    await refreshStatus();
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('停止服务失败:', error);
      addLog('error', `停止服务失败: ${error.message}`);
      ElMessage.error('停止服务失败');
    }
  } finally {
    actionLoading.stopAll = false;
  }
};

const runAllTests = async () => {
  actionLoading.runTests = true;
  addLog('info', '正在运行所有测试...');
  
  try {
    const response = await axios.post('/api/management/tests/run-all');
    
    addLog('info', '所有测试运行完成');
    ElMessage.success('测试运行完成');
    
  } catch (error) {
    console.error('运行测试失败:', error);
    addLog('error', `运行测试失败: ${error.message}`);
    ElMessage.error('运行测试失败');
  } finally {
    actionLoading.runTests = false;
  }
};

const buildProject = async () => {
  actionLoading.build = true;
  addLog('info', '正在构建项目...');
  
  try {
    const response = await axios.post('/api/management/deployment/build');
    
    addLog('info', '项目构建完成');
    ElMessage.success('项目构建成功');
    
  } catch (error) {
    console.error('构建项目失败:', error);
    addLog('error', `构建项目失败: ${error.message}`);
    ElMessage.error('构建项目失败');
  } finally {
    actionLoading.build = false;
  }
};

const deployProject = async () => {
  try {
    await ElMessageBox.confirm('确定要部署项目吗？', '确认操作', {
      type: 'warning'
    });
    
    actionLoading.deploy = true;
    addLog('info', '正在部署项目...');
    
    const response = await axios.post('/api/management/deployment/deploy');
    
    addLog('info', '项目部署完成');
    ElMessage.success('项目部署成功');
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('部署项目失败:', error);
      addLog('error', `部署项目失败: ${error.message}`);
      ElMessage.error('部署项目失败');
    }
  } finally {
    actionLoading.deploy = false;
  }
};

const addLog = (level, message) => {
  systemLogs.value.unshift({
    level,
    message,
    timestamp: new Date().toISOString()
  });
  
  // 保持日志数量
  if (systemLogs.value.length > 100) {
    systemLogs.value = systemLogs.value.slice(0, 50);
  }
};

const clearLogs = () => {
  systemLogs.value = [];
  ElMessage.success('日志已清空');
};

const startStatusCheck = () => {
  statusCheckInterval.value = setInterval(() => {
    refreshStatus();
  }, 30000); // 每30秒检查一次
};

const stopStatusCheck = () => {
  if (statusCheckInterval.value) {
    clearInterval(statusCheckInterval.value);
    statusCheckInterval.value = null;
  }
};

// 生命周期
onMounted(() => {
  addLog('info', '系统管理页面已加载');
  refreshStatus();
  startStatusCheck();
});

onUnmounted(() => {
  stopStatusCheck();
});
</script>

<style scoped>
.system-management {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.status-overview {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 10px;
  border-radius: 8px;
  background-color: #fafafa;
}

.status-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.status-icon.healthy {
  background-color: #67c23a;
}

.status-icon.warning {
  background-color: #e6a23c;
}

.status-icon.error {
  background-color: #f56c6c;
}

.status-icon.unknown {
  background-color: #909399;
}

.status-info h3 {
  margin: 0 0 5px 0;
  font-size: 16px;
  color: #303133;
}

.status-info p {
  margin: 0 0 5px 0;
  font-size: 14px;
  color: #606266;
}

.status-info small {
  color: #909399;
  font-size: 12px;
}

.management-modules {
  margin-bottom: 20px;
}

.module-card {
  cursor: pointer;
  transition: all 0.3s ease;
  height: 140px;
}

.module-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.module-content {
  display: flex;
  align-items: center;
  gap: 15px;
  height: 100%;
}

.module-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.module-icon.blue {
  background: linear-gradient(135deg, #409eff, #66b1ff);
}

.module-icon.green {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.module-icon.orange {
  background: linear-gradient(135deg, #e6a23c, #ebb563);
}

.module-icon.purple {
  background: linear-gradient(135deg, #9c27b0, #ba68c8);
}

.module-icon.red {
  background: linear-gradient(135deg, #f56c6c, #f78989);
}

.module-icon.cyan {
  background: linear-gradient(135deg, #00bcd4, #4dd0e1);
}

.module-info {
  flex: 1;
}

.module-info h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #303133;
}

.module-info p {
  margin: 0 0 10px 0;
  font-size: 13px;
  color: #606266;
  line-height: 1.4;
}

.module-stats {
  display: flex;
  gap: 15px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #909399;
}

.quick-actions {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.system-logs {
  margin-bottom: 20px;
}

.log-controls {
  display: flex;
  align-items: center;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  background-color: #fafafa;
  border-radius: 4px;
  padding: 10px;
}

.log-entry {
  display: flex;
  gap: 10px;
  padding: 5px 0;
  border-bottom: 1px solid #ebeef5;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-entry:last-child {
  border-bottom: none;
}

.log-time {
  color: #909399;
  min-width: 80px;
}

.log-level {
  min-width: 60px;
  font-weight: bold;
}

.log-entry.error .log-level {
  color: #f56c6c;
}

.log-entry.warning .log-level {
  color: #e6a23c;
}

.log-entry.info .log-level {
  color: #409eff;
}

.log-message {
  flex: 1;
  color: #303133;
}

.no-logs {
  text-align: center;
  padding: 40px 0;
}

@media (max-width: 768px) {
  .system-management {
    padding: 10px;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: stretch;
  }
  
  .action-buttons .el-button-group {
    width: 100%;
  }
  
  .action-buttons .el-button {
    flex: 1;
  }
}
</style>