#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试custom_id参数类型问题
"""

import requests
import json

def test_api_with_different_custom_id():
    """测试不同的custom_id参数格式"""
    print("测试不同的custom_id参数格式...")
    
    base_url = "http://localhost:8000"
    headers = {
        'Content-Type': 'application/json',
        'X-User-ID': 'SM_006'
    }
    
    # 测试1: 不传custom_id参数
    print("\n=== 测试1: 不传custom_id参数 ===")
    try:
        response = requests.get(
            f"{base_url}/api/mobile/assessments?limit=20", 
            headers=headers, 
            timeout=10
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            assessments = data.get('data', [])
            if isinstance(assessments, list):
                print(f"返回量表数量: {len(assessments)}")
            elif isinstance(assessments, dict) and 'assessments' in assessments:
                print(f"返回量表数量: {len(assessments['assessments'])}")
        else:
            print(f"请求失败: {response.text}")
    except Exception as e:
        print(f"测试1失败: {e}")
    
    # 测试2: 传入字符串custom_id
    print("\n=== 测试2: 传入字符串custom_id=SM_006 ===")
    try:
        response = requests.get(
            f"{base_url}/api/mobile/assessments?custom_id=SM_006&limit=20", 
            headers=headers, 
            timeout=10
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            assessments = data.get('data', [])
            if isinstance(assessments, list):
                print(f"返回量表数量: {len(assessments)}")
            elif isinstance(assessments, dict) and 'assessments' in assessments:
                print(f"返回量表数量: {len(assessments['assessments'])}")
        else:
            print(f"请求失败: {response.text}")
    except Exception as e:
        print(f"测试2失败: {e}")
    
    # 测试3: 尝试传入数字custom_id（如果SM_006对应某个数字ID）
    print("\n=== 测试3: 传入数字custom_id=6 ===")
    try:
        response = requests.get(
            f"{base_url}/api/mobile/assessments?custom_id=6&limit=20", 
            headers=headers, 
            timeout=10
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            assessments = data.get('data', [])
            if isinstance(assessments, list):
                print(f"返回量表数量: {len(assessments)}")
            elif isinstance(assessments, dict) and 'assessments' in assessments:
                print(f"返回量表数量: {len(assessments['assessments'])}")
        else:
            print(f"请求失败: {response.text}")
    except Exception as e:
        print(f"测试3失败: {e}")
    
    # 测试4: 检查所有量表的custom_id值
    print("\n=== 测试4: 获取所有量表查看custom_id值 ===")
    try:
        response = requests.get(
            f"{base_url}/api/mobile/assessments?limit=50", 
            headers=headers, 
            timeout=10
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            assessments_data = data.get('data', [])
            
            if isinstance(assessments_data, list):
                assessments = assessments_data
            elif isinstance(assessments_data, dict) and 'assessments' in assessments_data:
                assessments = assessments_data['assessments']
            else:
                print("无法解析量表数据")
                return
                
            print(f"总量表数量: {len(assessments)}")
            
            # 显示每个量表的详细信息
            for i, assessment in enumerate(assessments[:10]):  # 只显示前10个
                print(f"\n量表{i+1}:")
                print(f"  ID: {assessment.get('id')}")
                print(f"  名称: {assessment.get('name')}")
                print(f"  状态: {assessment.get('status')}")
                print(f"  创建时间: {assessment.get('created_at')}")
                # 注意：API返回的数据中可能没有custom_id字段
                if 'custom_id' in assessment:
                    print(f"  Custom ID: {assessment.get('custom_id')}")
                else:
                    print(f"  Custom ID: 未在返回数据中")
                    
        else:
            print(f"请求失败: {response.text}")
    except Exception as e:
        print(f"测试4失败: {e}")

if __name__ == "__main__":
    test_api_with_different_custom_id()