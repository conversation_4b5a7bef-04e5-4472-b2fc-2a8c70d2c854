#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_questionnaire_api():
    """调试问卷API查询逻辑"""
    
    # 数据库连接
    DATABASE_URL = "sqlite:///c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db"
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    db = SessionLocal()
    
    try:
        custom_id = "SM_006"
        
        logger.info("=== 调试问卷API查询逻辑 ===")
        
        # 1. 直接SQL查询所有问卷
        logger.info("\n1. 直接SQL查询所有问卷:")
        result = db.execute(text("""
            SELECT id, custom_id, name, status, created_at, questionnaire_type
            FROM questionnaires 
            WHERE custom_id = :custom_id
            ORDER BY id
        """), {"custom_id": custom_id})
        
        questionnaires = result.fetchall()
        logger.info(f"找到 {len(questionnaires)} 条问卷记录:")
        
        for questionnaire in questionnaires:
            logger.info(f"  ID={questionnaire.id}, 名称={questionnaire.name}, 状态={questionnaire.status}, 类型={questionnaire.questionnaire_type}")
        
        # 2. 模拟API查询逻辑
        logger.info("\n2. 模拟API查询逻辑:")
        
        # 导入模型
        sys.path.append('./YUN/backend')
        from app.models.questionnaire import Questionnaire
        
        # ORM查询
        query = db.query(Questionnaire).filter(Questionnaire.custom_id == custom_id)
        logger.info(f"ORM查询SQL: {query}")
        
        orm_questionnaires = query.all()
        logger.info(f"ORM查询结果: {len(orm_questionnaires)} 条记录")
        
        for questionnaire in orm_questionnaires:
            logger.info(f"  ORM - ID={questionnaire.id}, 名称={questionnaire.name}, 状态={questionnaire.status}")
        
        # 3. 检查API响应格式
        logger.info("\n3. 模拟API响应格式:")
        questionnaire_list = []
        for questionnaire in orm_questionnaires:
            questionnaire_data = {
                "id": questionnaire.id,
                "title": questionnaire.name,
                "status": questionnaire.status,
                "questionnaire_type": questionnaire.questionnaire_type,
                "created_at": questionnaire.created_at.isoformat() if questionnaire.created_at else None,
                "custom_id": questionnaire.custom_id
            }
            questionnaire_list.append(questionnaire_data)
        
        api_response = {
            "status": "success",
            "data": {
                "questionnaires": questionnaire_list,
                "total": len(questionnaire_list)
            }
        }
        
        logger.info(f"API响应格式: {api_response}")
        
        # 4. 统计状态
        logger.info("\n4. 状态统计:")
        pending_count = sum(1 for q in questionnaire_list if q.get('status') == 'pending')
        completed_count = sum(1 for q in questionnaire_list if q.get('status') == 'completed')
        other_count = len(questionnaire_list) - pending_count - completed_count
        
        logger.info(f"  - Pending: {pending_count}")
        logger.info(f"  - Completed: {completed_count}")
        logger.info(f"  - Other: {other_count}")
        
        # 5. 检查问卷表结构
        logger.info("\n5. 检查问卷表结构:")
        result = db.execute(text("PRAGMA table_info(questionnaires)"))
        columns = result.fetchall()
        for col in columns:
            logger.info(f"  {col.name}: {col.type}")
            
    except Exception as e:
        logger.error(f"调试过程中出错: {e}")
        import traceback
        logger.error(traceback.format_exc())
    finally:
        db.close()

if __name__ == "__main__":
    debug_questionnaire_api()