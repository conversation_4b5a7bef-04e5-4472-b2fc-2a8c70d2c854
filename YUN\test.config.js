/**
 * 测试配置文件
 * 统一管理前后端测试配置和参数
 * @version 1.0.0
 * <AUTHOR> Management System
 * @date 2024-01-15
 */

const path = require('path');
const fs = require('fs');

// 基础配置
const baseConfig = {
  // 项目根目录
  rootDir: __dirname,
  
  // 前端配置
  frontend: {
    srcDir: path.join(__dirname, 'frontend', 'src'),
    testDir: path.join(__dirname, 'frontend', 'tests'),
    coverageDir: path.join(__dirname, 'frontend', 'coverage'),
    nodeModules: path.join(__dirname, 'frontend', 'node_modules'),
    
    // 测试框架配置
    frameworks: {
      unit: 'vitest',
      e2e: 'cypress',
      component: 'vue-test-utils'
    },
    
    // 覆盖率配置
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/**',
        'tests/**',
        '**/*.config.js',
        '**/*.d.ts'
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    }
  },
  
  // 后端配置
  backend: {
    srcDir: path.join(__dirname, 'backend'),
    testDir: path.join(__dirname, 'backend', 'tests'),
    coverageDir: path.join(__dirname, 'backend', 'coverage'),
    
    // 测试框架配置
    frameworks: {
      unit: 'pytest',
      integration: 'pytest',
      api: 'pytest'
    },
    
    // 覆盖率配置
    coverage: {
      provider: 'coverage.py',
      reporter: ['term', 'json', 'html'],
      exclude: [
        '*/tests/*',
        '*/venv/*',
        '*/__pycache__/*',
        '*/migrations/*'
      ],
      thresholds: {
        global: {
          branches: 75,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    }
  },
  
  // 测试套件配置
  testSuites: {
    // 单元测试套件
    unit: {
      name: '单元测试',
      description: '测试独立的函数和组件',
      timeout: 30000,
      retries: 2,
      parallel: true,
      coverage: true
    },
    
    // 集成测试套件
    integration: {
      name: '集成测试',
      description: '测试模块间的交互',
      timeout: 60000,
      retries: 1,
      parallel: false,
      coverage: true
    },
    
    // API测试套件
    api: {
      name: 'API测试',
      description: '测试API接口功能',
      timeout: 45000,
      retries: 2,
      parallel: true,
      coverage: false
    },
    
    // E2E测试套件
    e2e: {
      name: 'E2E测试',
      description: '端到端功能测试',
      timeout: 120000,
      retries: 1,
      parallel: false,
      coverage: false
    }
  },
  
  // 报告配置
  reporting: {
    outputDir: path.join(__dirname, 'test-reports'),
    formats: ['json', 'html', 'xml'],
    
    // HTML报告配置
    html: {
      title: '健康管理系统测试报告',
      description: '自动化测试执行结果报告',
      includeConsoleLog: true,
      includeCoverage: true
    },
    
    // JSON报告配置
    json: {
      includeMetadata: true,
      includeTimestamps: true,
      prettyPrint: true
    }
  },
  
  // 环境配置
  environments: {
    development: {
      database: {
        host: 'localhost',
        port: 5432,
        name: 'health_test_dev'
      },
      api: {
        baseUrl: 'http://localhost:8000',
        timeout: 30000
      }
    },
    
    testing: {
      database: {
        host: 'localhost',
        port: 5432,
        name: 'health_test'
      },
      api: {
        baseUrl: 'http://localhost:8001',
        timeout: 30000
      }
    },
    
    ci: {
      database: {
        host: 'test-db',
        port: 5432,
        name: 'health_ci_test'
      },
      api: {
        baseUrl: 'http://test-api:8000',
        timeout: 60000
      }
    }
  },
  
  // 通知配置
  notifications: {
    enabled: true,
    channels: {
      email: {
        enabled: false,
        recipients: ['<EMAIL>']
      },
      slack: {
        enabled: false,
        webhook: process.env.SLACK_WEBHOOK_URL
      },
      console: {
        enabled: true,
        verbose: true
      }
    }
  },
  
  // 性能测试配置
  performance: {
    enabled: true,
    thresholds: {
      responseTime: 2000, // ms
      throughput: 100,    // requests/second
      errorRate: 0.01     // 1%
    },
    
    scenarios: {
      load: {
        users: 50,
        duration: '5m',
        rampUp: '1m'
      },
      stress: {
        users: 200,
        duration: '10m',
        rampUp: '2m'
      }
    }
  }
};

// 获取当前环境
const getCurrentEnvironment = () => {
  return process.env.NODE_ENV || 'development';
};

// 获取环境特定配置
const getEnvironmentConfig = (env = getCurrentEnvironment()) => {
  return baseConfig.environments[env] || baseConfig.environments.development;
};

// 合并配置
const getMergedConfig = (customConfig = {}) => {
  const envConfig = getEnvironmentConfig();
  
  return {
    ...baseConfig,
    environment: envConfig,
    ...customConfig
  };
};

// 验证配置
const validateConfig = (config) => {
  const errors = [];
  
  // 检查必需的目录
  const requiredDirs = [
    config.frontend.srcDir,
    config.backend.srcDir
  ];
  
  requiredDirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      errors.push(`Required directory does not exist: ${dir}`);
    }
  });
  
  // 检查覆盖率阈值
  const frontendThresholds = config.frontend.coverage.thresholds.global;
  const backendThresholds = config.backend.coverage.thresholds.global;
  
  [frontendThresholds, backendThresholds].forEach((thresholds, index) => {
    const type = index === 0 ? 'frontend' : 'backend';
    
    Object.entries(thresholds).forEach(([key, value]) => {
      if (value < 0 || value > 100) {
        errors.push(`Invalid ${type} coverage threshold for ${key}: ${value}`);
      }
    });
  });
  
  return errors;
};

// 创建测试目录
const createTestDirectories = (config) => {
  const dirs = [
    config.frontend.testDir,
    config.frontend.coverageDir,
    config.backend.testDir,
    config.backend.coverageDir,
    config.reporting.outputDir
  ];
  
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`Created directory: ${dir}`);
    }
  });
};

// 生成测试脚本
const generateTestScripts = (config) => {
  const scripts = {
    // 前端测试脚本
    'test:frontend:unit': 'cd frontend && npm run test:unit',
    'test:frontend:e2e': 'cd frontend && npm run test:e2e',
    'test:frontend:coverage': 'cd frontend && npm run test:coverage',
    
    // 后端测试脚本
    'test:backend:unit': 'cd backend && python -m pytest tests/unit/',
    'test:backend:integration': 'cd backend && python -m pytest tests/integration/',
    'test:backend:api': 'cd backend && python -m pytest tests/api/',
    'test:backend:coverage': 'cd backend && coverage run -m pytest && coverage report',
    
    // 全量测试脚本
    'test:all': 'npm run test:frontend:unit && npm run test:backend:unit',
    'test:coverage:all': 'npm run test:frontend:coverage && npm run test:backend:coverage',
    
    // 性能测试脚本
    'test:performance': 'k6 run tests/performance/load-test.js',
    
    // 清理脚本
    'test:clean': 'rm -rf frontend/coverage backend/coverage test-reports'
  };
  
  return scripts;
};

// 导出配置
module.exports = {
  baseConfig,
  getCurrentEnvironment,
  getEnvironmentConfig,
  getMergedConfig,
  validateConfig,
  createTestDirectories,
  generateTestScripts
};

// 如果直接运行此文件，执行配置验证
if (require.main === module) {
  const config = getMergedConfig();
  const errors = validateConfig(config);
  
  if (errors.length > 0) {
    console.error('Configuration validation failed:');
    errors.forEach(error => console.error(`  - ${error}`));
    process.exit(1);
  } else {
    console.log('Configuration validation passed!');
    createTestDirectories(config);
    
    const scripts = generateTestScripts(config);
    console.log('\nGenerated test scripts:');
    Object.entries(scripts).forEach(([name, command]) => {
      console.log(`  ${name}: ${command}`);
    });
  }
}