
// 在QuestionnairesTab.vue的viewOriginalAnswers函数中添加以下调试代码

const viewOriginalAnswers = async (item) => {
  try {
    console.log('=== 开始获取原始回答 ===');
    console.log('传入的item:', item);
    
    // 获取回答数据
    const endpoint = item.type === 'questionnaire'
      ? `/api/questionnaire-responses/user/${props.customId}`
      : `/api/assessment-responses/user/${props.customId}`
      
    console.log('回答数据API端点:', endpoint);
    
    const response = await axios.get(endpoint, {
      params: {
        [item.type === 'questionnaire' ? 'questionnaire_id' : 'assessment_id']: item.questionnaire_id || item.assessment_id
      }
    })
    
    console.log('回答数据响应:', response.data);
    
    if (response.data.data && response.data.data.length > 0) {
      const responseData = response.data.data[0]
      console.log('第一条回答数据:', responseData);
      
      // 获取模板信息以显示问题
      let templateData = null
      const templateId = item.template_id || (item.type === 'questionnaire' ? item.questionnaire_id : item.assessment_id)
      
      console.log('模板ID:', templateId);
      console.log('item.type:', item.type);
      
      if (templateId) {
        try {
          const templateEndpoint = item.type === 'questionnaire'
            ? `/api/templates/questionnaire-templates/${templateId}`
            : `/api/templates/assessment-templates/${templateId}`
          
          console.log('模板API端点:', templateEndpoint);
          
          const templateResponse = await axios.get(templateEndpoint)
          console.log('模板API响应状态:', templateResponse.status);
          console.log('模板API响应数据:', templateResponse.data);
          
          if (templateResponse.data.success || templateResponse.data.status === 'success') {
            templateData = templateResponse.data.data
            console.log('解析的模板数据:', templateData);
            
            // 检查questions字段
            if (templateData && templateData.questions) {
              console.log('模板包含questions字段，问题数量:', templateData.questions.length);
              console.log('前3个问题:', templateData.questions.slice(0, 3));
              
              // 确保问题数据完整，包含详细的问题文本和选项
              templateData.questions = templateData.questions.map(q => ({
                question_id: q.question_id,
                question_text: q.question_text,
                question_type: q.question_type,
                options: q.options || [],
                is_required: q.is_required,
                order: q.order
              }))
              
              console.log('处理后的问题数据:', templateData.questions.slice(0, 2));
            } else {
              console.warn('模板数据中不包含questions字段或questions为空');
              console.log('模板数据的所有字段:', Object.keys(templateData || {}));
            }
          } else {
            console.warn('模板API返回失败状态:', templateResponse.data);
          }
        } catch (templateError) {
          console.error('获取模板信息失败:', templateError);
          console.error('错误详情:', templateError.response?.data);
        }
      } else {
        console.warn('未找到有效的模板ID');
      }
      
      console.log('最终的templateData:', templateData);
      
      currentItem.value = {
        ...item,
        answers: responseData.answers,
        templateData: templateData
      }
      
      console.log('设置的currentItem:', currentItem.value);
      
      showAnswersDialog.value = true
    } else {
      console.warn('未找到原始回答数据');
      ElMessage.warning('未找到原始回答数据')
    }
  } catch (error) {
    console.error('获取原始回答失败:', error)
    console.error('错误详情:', error.response?.data);
    ElMessage.error('获取原始回答失败')
  }
}
