#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为admin用户创建测试数据
"""

import sqlite3
import os
from datetime import datetime
import sys

# 添加后端路径以导入MockDataManager
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
try:
    from backend.app.core.mock_data_manager import get_test_data_config
except ImportError:
    # 如果导入失败，使用默认配置
    def get_test_data_config():
        return {
            "database_path": "c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db",
            "admin_username": "admin",
            "test_response_data": '{"test": "data"}',
            "questionnaire_limit": 3,
            "assessment_limit": 3,
            "status_options": ["pending", "completed"]
        }

# 从MockDataManager获取配置
config = get_test_data_config()

def create_admin_test_data():
    """为admin用户创建测试数据"""
    print("=== 为admin用户创建测试数据 ===")
    
    # 从配置获取数据库路径
    db_path = config["database_path"]
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 检查admin用户的custom_id
        admin_username = config["admin_username"]
        cursor.execute("SELECT custom_id FROM users WHERE username = ?", (admin_username,))
        admin_result = cursor.fetchone()
        if not admin_result:
            print("未找到admin用户")
            return
        
        admin_custom_id = admin_result[0]
        print(f"Admin用户的custom_id: {admin_custom_id}")
        
        # 2. 为admin用户创建问卷分发记录
        print("\n创建问卷分发记录...")
        
        # 检查是否已有问卷分发记录
        cursor.execute("SELECT COUNT(*) FROM questionnaire_distributions WHERE custom_id = ?", (admin_custom_id,))
        existing_q_count = cursor.fetchone()[0]
        
        if existing_q_count == 0:
            # 获取可用的问卷
            questionnaire_limit = config["questionnaire_limit"]
            cursor.execute("SELECT id, title FROM questionnaires LIMIT ?", (questionnaire_limit,))
            questionnaires = cursor.fetchall()
            
            status_options = config["status_options"]
            for i, (q_id, q_name) in enumerate(questionnaires):
                status = status_options[0] if i < 2 else status_options[1]
                cursor.execute("""
                    INSERT INTO questionnaire_distributions 
                    (custom_id, questionnaire_id, status, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?)
                """, (admin_custom_id, q_id, status, datetime.now(), datetime.now()))
                print(f"  创建问卷分发: {q_name} ({status})")
        else:
            print(f"  admin用户已有 {existing_q_count} 条问卷分发记录")
        
        # 3. 为admin用户创建量表分发记录
        print("\n创建量表分发记录...")
        
        # 检查是否已有量表分发记录
        cursor.execute("SELECT COUNT(*) FROM assessment_distributions WHERE custom_id = ?", (admin_custom_id,))
        existing_a_count = cursor.fetchone()[0]
        
        if existing_a_count == 0:
            # 获取可用的量表
            assessment_limit = config["assessment_limit"]
            cursor.execute("SELECT id, name FROM assessments LIMIT ?", (assessment_limit,))
            assessments = cursor.fetchall()
            
            for i, (a_id, a_name) in enumerate(assessments):
                status = status_options[0] if i < 2 else status_options[1]
                cursor.execute("""
                    INSERT INTO assessment_distributions 
                    (custom_id, assessment_id, status, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?)
                """, (admin_custom_id, a_id, status, datetime.now(), datetime.now()))
                print(f"  创建量表分发: {a_name} ({status})")
        else:
            print(f"  admin用户已有 {existing_a_count} 条量表分发记录")
        
        # 4. 为已完成的记录创建回答数据
        print("\n创建回答数据...")
        
        # 问卷回答
        cursor.execute("""
            SELECT qd.id, qd.questionnaire_id, q.title 
            FROM questionnaire_distributions qd
            LEFT JOIN questionnaires q ON qd.questionnaire_id = q.id
            WHERE qd.custom_id = ? AND qd.status = 'completed'
        """, (admin_custom_id,))
        completed_questionnaires = cursor.fetchall()
        
        for dist_id, q_id, q_name in completed_questionnaires:
            # 检查是否已有回答记录
            cursor.execute("SELECT COUNT(*) FROM questionnaire_responses WHERE custom_id = ? AND questionnaire_id = ?", 
                         (admin_custom_id, q_id))
            if cursor.fetchone()[0] == 0:
                test_response_data = config["test_response_data"]
                cursor.execute("""
                    INSERT INTO questionnaire_responses 
                    (custom_id, questionnaire_id, responses, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?)
                """, (admin_custom_id, q_id, test_response_data, datetime.now(), datetime.now()))
                print(f"  创建问卷回答: {q_name}")
        
        # 量表回答
        cursor.execute("""
            SELECT ad.id, ad.assessment_id, a.name 
            FROM assessment_distributions ad
            LEFT JOIN assessments a ON ad.assessment_id = a.id
            WHERE ad.custom_id = ? AND ad.status = 'completed'
        """, (admin_custom_id,))
        completed_assessments = cursor.fetchall()
        
        for dist_id, a_id, a_name in completed_assessments:
            # 检查是否已有回答记录
            cursor.execute("SELECT COUNT(*) FROM assessment_responses WHERE custom_id = ? AND assessment_id = ?", 
                         (admin_custom_id, a_id))
            if cursor.fetchone()[0] == 0:
                cursor.execute("""
                    INSERT INTO assessment_responses 
                    (custom_id, assessment_id, responses, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?)
                """, (admin_custom_id, a_id, test_response_data, datetime.now(), datetime.now()))
                print(f"  创建量表回答: {a_name}")
        
        conn.commit()
        conn.close()
        
        print("\n=== 数据创建完成 ===")
        
    except Exception as e:
        print(f"创建数据时出错: {str(e)}")
        if conn:
            conn.rollback()
            conn.close()

if __name__ == "__main__":
    create_admin_test_data()