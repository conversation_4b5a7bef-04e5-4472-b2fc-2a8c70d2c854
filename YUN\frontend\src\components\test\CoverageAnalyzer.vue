<template>
  <div class="coverage-analyzer">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>测试覆盖率分析</h2>
      <p class="page-description">查看和分析代码测试覆盖率，识别测试盲点</p>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" @click="refreshCoverageData" :loading="isRefreshing">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button @click="generateCoverageReport" :loading="isGenerating">
          <el-icon><DocumentAdd /></el-icon>
          生成报告
        </el-button>
        <el-button @click="exportCoverageReport">
          <el-icon><Download /></el-icon>
          导出报告
        </el-button>
        <el-button @click="viewDetailedReport">
          <el-icon><View /></el-icon>
          详细报告
        </el-button>
        <el-button @click="showCoverageSettings = true">
          <el-icon><Setting /></el-icon>
          设置
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-select v-model="selectedProject" placeholder="选择项目" style="width: 200px;">
          <el-option label="前端项目" value="frontend" />
          <el-option label="后端项目" value="backend" />
          <el-option label="移动端项目" value="mobile" />
          <el-option label="全部项目" value="all" />
        </el-select>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="margin-left: 10px;"
        />
        <el-button-group style="margin-left: 10px;">
          <el-button :type="viewMode === 'grid' ? 'primary' : ''" @click="viewMode = 'grid'">
            <el-icon><Grid /></el-icon>
          </el-button>
          <el-button :type="viewMode === 'list' ? 'primary' : ''" @click="viewMode = 'list'">
            <el-icon><List /></el-icon>
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 覆盖率概览 -->
    <div class="coverage-overview">
      <div class="overview-cards">
        <el-card class="overview-card total-coverage">
          <div class="card-header">
            <div class="header-content">
              <h3>总体覆盖率</h3>
              <div class="coverage-trend" :class="overallCoverage.trend">
                <el-icon><component :is="getTrendIcon(overallCoverage.trend)" /></el-icon>
                <span>{{ overallCoverage.change }}%</span>
              </div>
            </div>
            <el-icon class="card-icon"><PieChart /></el-icon>
          </div>
          <div class="card-content">
            <div class="coverage-circle">
              <el-progress
                type="circle"
                :percentage="overallCoverage.percentage"
                :width="120"
                :stroke-width="8"
                :color="getCoverageColor(overallCoverage.percentage)"
              >
                <template #default="{ percentage }">
                  <span class="percentage-text">{{ percentage }}%</span>
                </template>
              </el-progress>
            </div>
            <div class="coverage-details">
              <div class="detail-item">
                <span class="detail-label">已覆盖行数:</span>
                <span class="detail-value">{{ overallCoverage.coveredLines.toLocaleString() }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">总行数:</span>
                <span class="detail-value">{{ overallCoverage.totalLines.toLocaleString() }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">最后更新:</span>
                <span class="detail-value">{{ formatDateTime(overallCoverage.lastUpdated) }}</span>
              </div>
            </div>
          </div>
        </el-card>

        <el-card class="overview-card branch-coverage">
          <div class="card-header">
            <div class="header-content">
              <h3>分支覆盖率</h3>
              <div class="coverage-trend" :class="branchCoverage.trend">
                <el-icon><component :is="getTrendIcon(branchCoverage.trend)" /></el-icon>
                <span>{{ branchCoverage.change }}%</span>
              </div>
            </div>
            <el-icon class="card-icon"><Share /></el-icon>
          </div>
          <div class="card-content">
            <div class="coverage-bar">
              <el-progress
                :percentage="branchCoverage.percentage"
                :stroke-width="20"
                :color="getCoverageColor(branchCoverage.percentage)"
              />
            </div>
            <div class="coverage-stats">
              <div class="stat-item">
                <span class="stat-value">{{ branchCoverage.covered }}</span>
                <span class="stat-label">已覆盖</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ branchCoverage.total }}</span>
                <span class="stat-label">总分支</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ branchCoverage.uncovered }}</span>
                <span class="stat-label">未覆盖</span>
              </div>
            </div>
          </div>
        </el-card>

        <el-card class="overview-card function-coverage">
          <div class="card-header">
            <div class="header-content">
              <h3>函数覆盖率</h3>
              <div class="coverage-trend" :class="functionCoverage.trend">
                <el-icon><component :is="getTrendIcon(functionCoverage.trend)" /></el-icon>
                <span>{{ functionCoverage.change }}%</span>
              </div>
            </div>
            <el-icon class="card-icon"><Operation /></el-icon>
          </div>
          <div class="card-content">
            <div class="coverage-bar">
              <el-progress
                :percentage="functionCoverage.percentage"
                :stroke-width="20"
                :color="getCoverageColor(functionCoverage.percentage)"
              />
            </div>
            <div class="coverage-stats">
              <div class="stat-item">
                <span class="stat-value">{{ functionCoverage.covered }}</span>
                <span class="stat-label">已覆盖</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ functionCoverage.total }}</span>
                <span class="stat-label">总函数</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ functionCoverage.uncovered }}</span>
                <span class="stat-label">未覆盖</span>
              </div>
            </div>
          </div>
        </el-card>

        <el-card class="overview-card statement-coverage">
          <div class="card-header">
            <div class="header-content">
              <h3>语句覆盖率</h3>
              <div class="coverage-trend" :class="statementCoverage.trend">
                <el-icon><component :is="getTrendIcon(statementCoverage.trend)" /></el-icon>
                <span>{{ statementCoverage.change }}%</span>
              </div>
            </div>
            <el-icon class="card-icon"><Document /></el-icon>
          </div>
          <div class="card-content">
            <div class="coverage-bar">
              <el-progress
                :percentage="statementCoverage.percentage"
                :stroke-width="20"
                :color="getCoverageColor(statementCoverage.percentage)"
              />
            </div>
            <div class="coverage-stats">
              <div class="stat-item">
                <span class="stat-value">{{ statementCoverage.covered }}</span>
                <span class="stat-label">已覆盖</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ statementCoverage.total }}</span>
                <span class="stat-label">总语句</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ statementCoverage.uncovered }}</span>
                <span class="stat-label">未覆盖</span>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 覆盖率趋势图 -->
    <div class="coverage-trends">
      <div class="section-header">
        <h3>覆盖率趋势</h3>
        <div class="trend-controls">
          <el-radio-group v-model="trendPeriod" size="small">
            <el-radio-button label="7d">7天</el-radio-button>
            <el-radio-button label="30d">30天</el-radio-button>
            <el-radio-button label="90d">90天</el-radio-button>
          </el-radio-group>
        </div>
      </div>
      <div class="trend-chart" ref="trendChartRef"></div>
    </div>

    <!-- 文件覆盖率详情 -->
    <div class="file-coverage">
      <div class="section-header">
        <h3>文件覆盖率详情</h3>
        <div class="file-controls">
          <el-input
            v-model="fileSearchKeyword"
            placeholder="搜索文件..."
            prefix-icon="Search"
            style="width: 300px;"
            clearable
          />
          <el-select v-model="coverageFilter" placeholder="覆盖率筛选" style="width: 150px; margin-left: 10px;">
            <el-option label="全部" value="" />
            <el-option label="高覆盖率 (>80%)" value="high" />
            <el-option label="中覆盖率 (50-80%)" value="medium" />
            <el-option label="低覆盖率 (<50%)" value="low" />
            <el-option label="未覆盖" value="none" />
          </el-select>
        </div>
      </div>
      
      <el-table :data="filteredFilesCoverage" style="width: 100%" stripe>
        <el-table-column prop="file" label="文件路径" min-width="300">
          <template #default="{ row }">
            <div class="file-path">
              <el-icon class="file-icon"><Document /></el-icon>
              <span class="file-name">{{ row.file }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="lines" label="行覆盖率" width="150" sortable>
          <template #default="{ row }">
            <div class="coverage-cell">
              <el-progress
                :percentage="row.lines.percentage"
                :stroke-width="6"
                :color="getCoverageColor(row.lines.percentage)"
                :show-text="false"
              />
              <span class="coverage-text">{{ row.lines.percentage }}%</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="branches" label="分支覆盖率" width="150" sortable>
          <template #default="{ row }">
            <div class="coverage-cell">
              <el-progress
                :percentage="row.branches.percentage"
                :stroke-width="6"
                :color="getCoverageColor(row.branches.percentage)"
                :show-text="false"
              />
              <span class="coverage-text">{{ row.branches.percentage }}%</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="functions" label="函数覆盖率" width="150" sortable>
          <template #default="{ row }">
            <div class="coverage-cell">
              <el-progress
                :percentage="row.functions.percentage"
                :stroke-width="6"
                :color="getCoverageColor(row.functions.percentage)"
                :show-text="false"
              />
              <span class="coverage-text">{{ row.functions.percentage }}%</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="statements" label="语句覆盖率" width="150" sortable>
          <template #default="{ row }">
            <div class="coverage-cell">
              <el-progress
                :percentage="row.statements.percentage"
                :stroke-width="6"
                :color="getCoverageColor(row.statements.percentage)"
                :show-text="false"
              />
              <span class="coverage-text">{{ row.statements.percentage }}%</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="viewFileDetail(row)">
              <el-icon><View /></el-icon>
              查看详情
            </el-button>
            <el-button type="text" size="small" @click="viewSourceCode(row)">
              <el-icon><Document /></el-icon>
              源码
            </el-button>
            <el-button type="text" size="small" @click="generateTestSuggestion(row)">
              <el-icon><Magic /></el-icon>
              建议
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 覆盖率热力图 -->
    <div class="coverage-heatmap">
      <div class="section-header">
        <h3>覆盖率热力图</h3>
        <p class="section-description">可视化显示项目文件的覆盖率分布</p>
      </div>
      <div class="heatmap-chart" ref="heatmapChartRef"></div>
    </div>

    <!-- 测试建议 -->
    <div class="test-suggestions">
      <div class="section-header">
        <h3>测试改进建议</h3>
        <p class="section-description">基于覆盖率分析的测试优化建议</p>
      </div>
      
      <div class="suggestions-grid">
        <div 
          v-for="suggestion in testSuggestions" 
          :key="suggestion.id" 
          class="suggestion-card"
          :class="suggestion.priority"
        >
          <div class="suggestion-header">
            <div class="suggestion-icon">
              <el-icon><component :is="suggestion.icon" /></el-icon>
            </div>
            <div class="suggestion-info">
              <h4 class="suggestion-title">{{ suggestion.title }}</h4>
              <span class="suggestion-priority">{{ getPriorityText(suggestion.priority) }}</span>
            </div>
          </div>
          <div class="suggestion-content">
            <p class="suggestion-description">{{ suggestion.description }}</p>
            <div class="suggestion-details">
              <div class="detail-item">
                <span class="detail-label">影响文件:</span>
                <span class="detail-value">{{ suggestion.affectedFiles }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">预期提升:</span>
                <span class="detail-value">{{ suggestion.expectedImprovement }}</span>
              </div>
            </div>
          </div>
          <div class="suggestion-actions">
            <el-button size="small" type="primary" @click="applySuggestion(suggestion)">
              应用建议
            </el-button>
            <el-button size="small" @click="dismissSuggestion(suggestion)">
              忽略
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件详情对话框 -->
    <el-dialog
      v-model="showFileDetailDialog"
      :title="`文件覆盖率详情 - ${selectedFile?.file}`"
      width="1000px"
    >
      <div v-if="selectedFile" class="file-detail">
        <!-- 文件覆盖率概览 -->
        <div class="file-overview">
          <div class="overview-item">
            <span class="overview-label">行覆盖率</span>
            <el-progress
              :percentage="selectedFile.lines.percentage"
              :color="getCoverageColor(selectedFile.lines.percentage)"
            />
          </div>
          <div class="overview-item">
            <span class="overview-label">分支覆盖率</span>
            <el-progress
              :percentage="selectedFile.branches.percentage"
              :color="getCoverageColor(selectedFile.branches.percentage)"
            />
          </div>
          <div class="overview-item">
            <span class="overview-label">函数覆盖率</span>
            <el-progress
              :percentage="selectedFile.functions.percentage"
              :color="getCoverageColor(selectedFile.functions.percentage)"
            />
          </div>
        </div>

        <!-- 未覆盖的行 -->
        <div class="uncovered-lines">
          <h4>未覆盖的行</h4>
          <div class="line-ranges">
            <el-tag 
              v-for="range in selectedFile.uncoveredLines" 
              :key="range" 
              type="danger" 
              size="small"
            >
              行 {{ range }}
            </el-tag>
          </div>
        </div>

        <!-- 函数覆盖率详情 -->
        <div class="function-coverage">
          <h4>函数覆盖率详情</h4>
          <el-table :data="selectedFile.functionDetails" size="small">
            <el-table-column prop="name" label="函数名" />
            <el-table-column prop="line" label="行号" width="80" />
            <el-table-column prop="covered" label="是否覆盖" width="100">
              <template #default="{ row }">
                <el-tag :type="row.covered ? 'success' : 'danger'" size="small">
                  {{ row.covered ? '已覆盖' : '未覆盖' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="calls" label="调用次数" width="100" />
          </el-table>
        </div>
      </div>
    </el-dialog>

    <!-- 源码查看对话框 -->
    <el-dialog
      v-model="showSourceCodeDialog"
      :title="`源码查看 - ${selectedFile?.file}`"
      width="1200px"
      fullscreen
    >
      <div class="source-code-viewer">
        <div class="code-toolbar">
          <el-switch
            v-model="showCoverageHighlight"
            active-text="显示覆盖率高亮"
            inactive-text="隐藏覆盖率高亮"
          />
        </div>
        <div class="code-content">
          <pre class="code-lines"><code v-html="highlightedSourceCode"></code></pre>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  PieChart, Share, Operation, Document, Refresh, Download, View, Search,
  Magic, Warning, InfoFilled, SuccessFilled
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// 响应式数据
const selectedProject = ref('all')
const dateRange = ref([])
const fileSearchKeyword = ref('')
const coverageFilter = ref('')
const trendPeriod = ref('30d')
const showFileDetailDialog = ref(false)
const showSourceCodeDialog = ref(false)
const showCoverageHighlight = ref(true)
const selectedFile = ref(null)

// 图表引用
const trendChartRef = ref()
const heatmapChartRef = ref()
let trendChart = null
let heatmapChart = null

// 覆盖率数据
const overallCoverage = reactive({
  percentage: 85.6,
  coveredLines: 4280,
  totalLines: 5000
})

const branchCoverage = reactive({
  percentage: 78.3,
  covered: 156,
  total: 199
})

const functionCoverage = reactive({
  percentage: 92.1,
  covered: 234,
  total: 254
})

const statementCoverage = reactive({
  percentage: 87.4,
  covered: 1748,
  total: 2000
})

// 文件覆盖率数据
const filesCoverage = ref([
  {
    file: 'src/components/TestManagement.vue',
    lines: { percentage: 95.2, covered: 120, total: 126 },
    branches: { percentage: 88.9, covered: 16, total: 18 },
    functions: { percentage: 100, covered: 12, total: 12 },
    statements: { percentage: 94.7, covered: 72, total: 76 },
    uncoveredLines: ['45-47', '89', '102-105'],
    functionDetails: [
      { name: 'refreshTestSuites', line: 25, covered: true, calls: 15 },
      { name: 'runTestSuite', line: 45, covered: false, calls: 0 },
      { name: 'generateReport', line: 89, covered: true, calls: 8 }
    ]
  },
  {
    file: 'src/utils/testUtils.js',
    lines: { percentage: 72.5, covered: 87, total: 120 },
    branches: { percentage: 65.0, covered: 13, total: 20 },
    functions: { percentage: 80.0, covered: 8, total: 10 },
    statements: { percentage: 75.8, covered: 50, total: 66 },
    uncoveredLines: ['12-18', '34', '67-72', '95-98'],
    functionDetails: [
      { name: 'formatTestResult', line: 12, covered: false, calls: 0 },
      { name: 'calculateCoverage', line: 34, covered: true, calls: 12 },
      { name: 'exportResults', line: 67, covered: false, calls: 0 }
    ]
  },
  {
    file: 'src/api/testApi.js',
    lines: { percentage: 45.8, covered: 33, total: 72 },
    branches: { percentage: 33.3, covered: 4, total: 12 },
    functions: { percentage: 60.0, covered: 6, total: 10 },
    statements: { percentage: 48.6, covered: 18, total: 37 },
    uncoveredLines: ['8-15', '28-35', '48-52', '65-72'],
    functionDetails: [
      { name: 'getTestResults', line: 8, covered: false, calls: 0 },
      { name: 'submitTestData', line: 28, covered: false, calls: 0 },
      { name: 'deleteTestSuite', line: 48, covered: true, calls: 3 }
    ]
  }
])

// 测试建议数据
const testSuggestions = ref([
  {
    id: 1,
    title: '增加API测试覆盖率',
    description: 'testApi.js文件的覆盖率较低，建议增加对API错误处理和边界情况的测试',
    priority: 'high',
    icon: 'Warning',
    affectedFiles: 3,
    expectedImprovement: '+15%'
  },
  {
    id: 2,
    title: '完善分支测试',
    description: '多个文件存在未覆盖的分支逻辑，建议添加条件分支测试用例',
    priority: 'medium',
    icon: 'InfoFilled',
    affectedFiles: 8,
    expectedImprovement: '+8%'
  },
  {
    id: 3,
    title: '优化测试性能',
    description: '当前测试套件运行时间较长，建议优化测试用例和并行执行',
    priority: 'low',
    icon: 'SuccessFilled',
    affectedFiles: 12,
    expectedImprovement: '-30s'
  }
])

// 计算属性
const filteredFilesCoverage = computed(() => {
  return filesCoverage.value.filter(file => {
    const matchesSearch = !fileSearchKeyword.value || 
      file.file.toLowerCase().includes(fileSearchKeyword.value.toLowerCase())
    
    let matchesFilter = true
    if (coverageFilter.value) {
      const linePercentage = file.lines.percentage
      switch (coverageFilter.value) {
        case 'high':
          matchesFilter = linePercentage > 80
          break
        case 'medium':
          matchesFilter = linePercentage >= 50 && linePercentage <= 80
          break
        case 'low':
          matchesFilter = linePercentage < 50 && linePercentage > 0
          break
        case 'none':
          matchesFilter = linePercentage === 0
          break
      }
    }
    
    return matchesSearch && matchesFilter
  })
})

const highlightedSourceCode = computed(() => {
  if (!selectedFile.value || !showCoverageHighlight.value) {
    return mockSourceCode.value
  }
  
  // 模拟源码高亮显示覆盖率
  return mockSourceCode.value.replace(
    /^(\d+)\s+(.*)$/gm,
    (match, lineNum, code) => {
      const isUncovered = selectedFile.value.uncoveredLines.some(range => {
        if (range.includes('-')) {
          const [start, end] = range.split('-').map(Number)
          return lineNum >= start && lineNum <= end
        }
        return lineNum == range
      })
      
      const className = isUncovered ? 'uncovered-line' : 'covered-line'
      return `<span class="line-number">${lineNum}</span><span class="${className}">${code}</span>`
    }
  )
})

const mockSourceCode = ref(`1   import { ref, reactive } from 'vue'
2   import { ElMessage } from 'element-plus'
3   
4   export default {
5     name: 'TestComponent',
6     setup() {
7       const testData = ref([])
8       const loading = ref(false)
9       
10      const fetchData = async () => {
11        loading.value = true
12        try {
13          const response = await api.getData()
14          testData.value = response.data
15        } catch (error) {
16          ElMessage.error('获取数据失败')
17        } finally {
18          loading.value = false
19        }
20      }
21      
22      return {
23        testData,
24        loading,
25        fetchData
26      }
27    }
28  }`)

// 方法
const refreshCoverageData = async () => {
  try {
    ElMessage.info('正在刷新覆盖率数据...')
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // 更新数据
    overallCoverage.percentage = Math.floor(Math.random() * 20) + 80
    branchCoverage.percentage = Math.floor(Math.random() * 25) + 70
    functionCoverage.percentage = Math.floor(Math.random() * 15) + 85
    statementCoverage.percentage = Math.floor(Math.random() * 20) + 75
    
    ElMessage.success('覆盖率数据已更新')
    
    // 更新图表
    updateTrendChart()
    updateHeatmapChart()
  } catch (error) {
    ElMessage.error('刷新失败: ' + error.message)
  }
}

const exportCoverageReport = async () => {
  try {
    const reportData = {
      timestamp: new Date().toISOString(),
      project: selectedProject.value,
      overall: overallCoverage,
      branch: branchCoverage,
      function: functionCoverage,
      statement: statementCoverage,
      files: filesCoverage.value
    }
    
    const blob = new Blob([JSON.stringify(reportData, null, 2)], {
      type: 'application/json'
    })
    
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `coverage-report-${new Date().toISOString().split('T')[0]}.json`
    a.click()
    URL.revokeObjectURL(url)
    
    ElMessage.success('覆盖率报告已导出')
  } catch (error) {
    ElMessage.error('导出失败: ' + error.message)
  }
}

const viewDetailedReport = () => {
  // 打开详细的HTML报告
  const reportWindow = window.open('', '_blank')
  reportWindow.document.write(`
    <html>
      <head><title>详细覆盖率报告</title></head>
      <body>
        <h1>测试覆盖率详细报告</h1>
        <p>这里将显示完整的覆盖率报告...</p>
      </body>
    </html>
  `)
}

const viewFileDetail = (file) => {
  selectedFile.value = file
  showFileDetailDialog.value = true
}

const viewSourceCode = (file) => {
  selectedFile.value = file
  showSourceCodeDialog.value = true
}

const generateTestSuggestion = (file) => {
  ElMessage.info(`正在为 ${file.file} 生成测试建议...`)
  
  setTimeout(() => {
    ElMessage.success('测试建议已生成，请查看建议面板')
  }, 1000)
}

const applySuggestion = (suggestion) => {
  ElMessage.success(`已应用建议: ${suggestion.title}`)
  
  // 从建议列表中移除
  const index = testSuggestions.value.findIndex(s => s.id === suggestion.id)
  if (index > -1) {
    testSuggestions.value.splice(index, 1)
  }
}

const dismissSuggestion = (suggestion) => {
  const index = testSuggestions.value.findIndex(s => s.id === suggestion.id)
  if (index > -1) {
    testSuggestions.value.splice(index, 1)
  }
}

// 工具函数
const getCoverageColor = (percentage) => {
  if (percentage >= 90) return '#67c23a'
  if (percentage >= 80) return '#e6a23c'
  if (percentage >= 60) return '#f56c6c'
  return '#909399'
}

const getPriorityText = (priority) => {
  const texts = {
    high: '高优先级',
    medium: '中优先级',
    low: '低优先级'
  }
  return texts[priority] || priority
}

// 图表相关方法
const initTrendChart = () => {
  if (!trendChartRef.value) return
  
  trendChart = echarts.init(trendChartRef.value)
  updateTrendChart()
}

const updateTrendChart = () => {
  if (!trendChart) return
  
  const dates = []
  const lineData = []
  const branchData = []
  const functionData = []
  
  // 生成模拟数据
  for (let i = 29; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    dates.push(date.toISOString().split('T')[0])
    
    lineData.push(Math.floor(Math.random() * 10) + 80)
    branchData.push(Math.floor(Math.random() * 15) + 70)
    functionData.push(Math.floor(Math.random() * 8) + 85)
  }
  
  const option = {
    title: {
      text: '覆盖率趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['行覆盖率', '分支覆盖率', '函数覆盖率'],
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100,
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [
      {
        name: '行覆盖率',
        type: 'line',
        data: lineData,
        smooth: true,
        itemStyle: { color: '#67c23a' }
      },
      {
        name: '分支覆盖率',
        type: 'line',
        data: branchData,
        smooth: true,
        itemStyle: { color: '#e6a23c' }
      },
      {
        name: '函数覆盖率',
        type: 'line',
        data: functionData,
        smooth: true,
        itemStyle: { color: '#409eff' }
      }
    ]
  }
  
  trendChart.setOption(option)
}

const initHeatmapChart = () => {
  if (!heatmapChartRef.value) return
  
  heatmapChart = echarts.init(heatmapChartRef.value)
  updateHeatmapChart()
}

const updateHeatmapChart = () => {
  if (!heatmapChart) return
  
  // 生成热力图数据
  const data = []
  const files = ['TestManagement.vue', 'testUtils.js', 'testApi.js', 'CoverageAnalyzer.vue', 'TestSuiteManager.vue']
  const metrics = ['Lines', 'Branches', 'Functions', 'Statements']
  
  files.forEach((file, i) => {
    metrics.forEach((metric, j) => {
      data.push([i, j, Math.floor(Math.random() * 40) + 60])
    })
  })
  
  const option = {
    title: {
      text: '文件覆盖率热力图',
      left: 'center'
    },
    tooltip: {
      position: 'top',
      formatter: function (params) {
        return `${files[params.data[0]]}<br/>${metrics[params.data[1]]}: ${params.data[2]}%`
      }
    },
    grid: {
      height: '50%',
      top: '10%'
    },
    xAxis: {
      type: 'category',
      data: files,
      splitArea: {
        show: true
      },
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'category',
      data: metrics,
      splitArea: {
        show: true
      }
    },
    visualMap: {
      min: 0,
      max: 100,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '5%',
      inRange: {
        color: ['#f56c6c', '#e6a23c', '#67c23a']
      }
    },
    series: [{
      name: '覆盖率',
      type: 'heatmap',
      data: data,
      label: {
        show: true,
        formatter: '{c}%'
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }
  
  heatmapChart.setOption(option)
}

// 生命周期
onMounted(async () => {
  await refreshCoverageData()
  
  nextTick(() => {
    initTrendChart()
    initHeatmapChart()
  })
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    trendChart?.resize()
    heatmapChart?.resize()
  })
})
</script>

<style scoped>
.coverage-analyzer {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.coverage-overview {
  margin-bottom: 32px;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.overview-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.overview-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.card-icon {
  font-size: 24px;
  color: #409eff;
}

.card-content {
  display: flex;
  align-items: center;
  gap: 20px;
}

.coverage-circle {
  flex-shrink: 0;
}

.percentage-text {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.coverage-details {
  flex: 1;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.detail-label {
  color: #606266;
}

.detail-value {
  font-weight: 600;
  color: #303133;
}

.coverage-bar {
  margin-bottom: 16px;
}

.coverage-stats {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.coverage-trends,
.file-coverage,
.coverage-heatmap,
.test-suggestions {
  margin-bottom: 32px;
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.section-description {
  margin: 4px 0 0 0;
  font-size: 14px;
  color: #606266;
}

.trend-chart,
.heatmap-chart {
  height: 400px;
  width: 100%;
}

.file-controls {
  display: flex;
  align-items: center;
}

.file-path {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-icon {
  color: #409eff;
}

.file-name {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

.coverage-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.coverage-text {
  font-size: 12px;
  font-weight: 600;
  min-width: 35px;
}

.suggestions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.suggestion-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s ease;
}

.suggestion-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.suggestion-card.high {
  border-left: 4px solid #f56c6c;
}

.suggestion-card.medium {
  border-left: 4px solid #e6a23c;
}

.suggestion-card.low {
  border-left: 4px solid #67c23a;
}

.suggestion-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.suggestion-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  color: #409eff;
}

.suggestion-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.suggestion-priority {
  font-size: 12px;
  color: #909399;
}

.suggestion-description {
  margin: 0 0 12px 0;
  color: #606266;
  line-height: 1.5;
}

.suggestion-details {
  margin-bottom: 16px;
}

.suggestion-actions {
  display: flex;
  gap: 8px;
}

.file-detail {
  padding: 20px 0;
}

.file-overview {
  margin-bottom: 24px;
}

.overview-item {
  margin-bottom: 16px;
}

.overview-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #303133;
}

.uncovered-lines {
  margin-bottom: 24px;
}

.uncovered-lines h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #303133;
}

.line-ranges {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.function-coverage h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #303133;
}

.source-code-viewer {
  height: 80vh;
  display: flex;
  flex-direction: column;
}

.code-toolbar {
  padding: 12px;
  border-bottom: 1px solid #e4e7ed;
  background: #f8f9fa;
}

.code-content {
  flex: 1;
  overflow: auto;
  background: #fafafa;
}

.code-lines {
  margin: 0;
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.line-number {
  display: inline-block;
  width: 40px;
  color: #909399;
  text-align: right;
  margin-right: 16px;
  user-select: none;
}

.covered-line {
  background: rgba(103, 194, 58, 0.1);
  padding: 0 4px;
}

.uncovered-line {
  background: rgba(245, 108, 108, 0.1);
  padding: 0 4px;
}
</style>