#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接在数据库中创建评估数据
"""

import sqlite3
import json
from datetime import datetime, timedelta
import uuid

def create_assessment_in_db():
    """直接在数据库中创建评估数据"""
    print("=== 直接在数据库中创建评估数据 ===")
    
    # 连接数据库
    db_path = "YUN/backend/app.db"
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 1. 检查assessments表结构
        print("\n1. 检查assessments表结构")
        cursor.execute("PRAGMA table_info(assessments)")
        columns = cursor.fetchall()
        print(f"assessments表字段: {[col[1] for col in columns]}")
        
        # 2. 检查现有评估数据
        print("\n2. 检查现有评估数据")
        cursor.execute("SELECT COUNT(*) FROM assessments")
        count = cursor.fetchone()[0]
        print(f"现有评估数量: {count}")
        
        # 3. 获取可用的模板
        print("\n3. 获取可用的模板")
        cursor.execute("SELECT id, name FROM assessment_templates LIMIT 3")
        templates = cursor.fetchall()
        print(f"可用模板: {templates}")
        
        if not templates:
            print("没有可用的评估模板")
            return
        
        # 4. 为SM_006用户创建评估数据
        print("\n4. 为SM_008用户创建评估数据")
        
        # 创建评估记录
        assessments_to_create = [
            {
                'custom_id': 'SM_006',
                'template_id': templates[0][0],
                'name': f'{templates[0][1]} - 评估1',
                'title': f'{templates[0][1]}',
                'status': 'pending',
                'assessment_type': 'psychological',
                'due_date': (datetime.now() + timedelta(days=7)).isoformat(),
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat()
            },
            {
                'custom_id': 'SM_006',
                'template_id': templates[1][0] if len(templates) > 1 else templates[0][0],
                'name': f'{templates[1][1] if len(templates) > 1 else templates[0][1]} - 评估2',
                'title': f'{templates[1][1] if len(templates) > 1 else templates[0][1]}',
                'status': 'pending',
                'assessment_type': 'cognitive',
                'due_date': (datetime.now() + timedelta(days=14)).isoformat(),
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat()
            }
        ]
        
        # 插入评估数据
        for assessment in assessments_to_create:
            try:
                # 检查assessments表的实际字段
                cursor.execute("PRAGMA table_info(assessments)")
                actual_columns = [col[1] for col in cursor.fetchall()]
                
                # 只使用存在的字段
                valid_data = {}
                for key, value in assessment.items():
                    if key in actual_columns:
                        valid_data[key] = value
                
                # 构建插入语句
                columns = ', '.join(valid_data.keys())
                placeholders = ', '.join(['?' for _ in valid_data])
                values = list(valid_data.values())
                
                insert_sql = f"INSERT INTO assessments ({columns}) VALUES ({placeholders})"
                print(f"插入SQL: {insert_sql}")
                print(f"插入数据: {valid_data}")
                
                cursor.execute(insert_sql, values)
                assessment_id = cursor.lastrowid
                print(f"成功创建评估，ID: {assessment_id}")
                
            except Exception as e:
                print(f"创建评估失败: {e}")
                continue
        
        # 提交事务
        conn.commit()
        
        # 5. 验证创建的数据
        print("\n5. 验证创建的数据")
        cursor.execute("""
            SELECT a.id, a.custom_id, a.name, a.title, a.status, a.template_id,
                   t.name as template_name, t.description, t.instructions
            FROM assessments a
            LEFT JOIN assessment_templates t ON a.template_id = t.id
            WHERE a.custom_id = 'SM_006'
        """)
        
        created_assessments = cursor.fetchall()
        print(f"为SM_008创建的评估数量: {len(created_assessments)}")
        
        for assessment in created_assessments:
            print(f"\n评估详情:")
            print(f"  ID: {assessment[0]}")
            print(f"  Custom ID: {assessment[1]}")
            print(f"  名称: {assessment[2]}")
            print(f"  标题: {assessment[3]}")
            print(f"  状态: {assessment[4]}")
            print(f"  模板ID: {assessment[5]}")
            print(f"  模板名称: {assessment[6]}")
            print(f"  模板描述: {assessment[7][:100] if assessment[7] else 'None'}...")
            print(f"  模板说明: {assessment[8][:100] if assessment[8] else 'None'}...")
        
        print("\n评估数据创建完成！")
        
    except Exception as e:
        print(f"操作失败: {e}")
        conn.rollback()
    finally:
        conn.close()

def test_mobile_api():
    """测试移动端API"""
    print("\n=== 测试移动端API ===")
    
    import requests
    
    # 获取token
    login_data = {
        "username": "admin",
        "password": "markey0308@163"
    }
    
    try:
        response = requests.post(
            "http://localhost:8006/api/auth/login_json",
            json=login_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            token = data.get('access_token')
            print(f"获取到token: {token[:20]}...")
            
            # 测试移动端API
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}",
                "X-User-ID": "SM_008"
            }
            
            response = requests.get(
                "http://localhost:8006/api/mobile/assessments",
                headers=headers,
                timeout=10
            )
            
            print(f"移动端API响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"API响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                
                # 检查template字段
                assessments = data.get('data', {}).get('assessments', [])
                print(f"\n返回 {len(assessments)} 个评估:")
                
                for assessment in assessments:
                    template = assessment.get('template')
                    print(f"\n评估: {assessment.get('name')}")
                    print(f"  Template存在: {template is not None}")
                    if template:
                        print(f"  Template描述: {template.get('description', 'None')[:100]}...")
                        print(f"  Template说明: {template.get('instructions', 'None')[:100]}...")
                    else:
                        print(f"  Template为空！")
            else:
                print(f"API调用失败: {response.status_code} - {response.text}")
        else:
            print(f"登录失败: {response.status_code}")
            
    except Exception as e:
        print(f"测试API异常: {e}")

def main():
    """主函数"""
    print("开始创建评估数据...")
    
    # 1. 在数据库中创建评估数据
    create_assessment_in_db()
    
    # 2. 测试移动端API
    test_mobile_api()
    
    print("\n操作完成！")

if __name__ == "__main__":
    main()