#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新backend目录下app.db中SAS问题的计分规则
"""

import sqlite3
import json
import os

def update_sas_scoring():
    # 使用backend目录下的app.db
    db_path = "app.db"
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 定义SAS问题的计分规则
        scoring_rule = {"1": 1, "2": 2, "3": 3, "4": 4}
        scoring_json = json.dumps(scoring_rule)
        
        print(f"准备更新SAS问题的计分规则为: {scoring_json}")
        
        # 查询所有SAS相关问题
        cursor.execute("""
            SELECT question_id, template_id, scoring 
            FROM assessment_template_questions 
            WHERE template_id = 3 AND question_id LIKE 'sas_%'
        """)
        
        sas_questions = cursor.fetchall()
        print(f"找到 {len(sas_questions)} 个SAS问题")
        
        if not sas_questions:
            print("未找到SAS问题，检查模板ID是否正确")
            return
        
        # 更新每个SAS问题的计分规则
        updated_count = 0
        for question_id, template_id, current_scoring in sas_questions:
            print(f"更新问题: {question_id}, 当前计分规则: {current_scoring}")
            
            cursor.execute("""
                UPDATE assessment_template_questions 
                SET scoring = ? 
                WHERE template_id = ? AND question_id = ?
            """, (scoring_json, template_id, question_id))
            
            updated_count += 1
        
        # 提交更改
        conn.commit()
        print(f"成功更新 {updated_count} 个SAS问题的计分规则")
        
        # 验证更新结果
        print("\n验证更新结果:")
        cursor.execute("""
            SELECT question_id, scoring 
            FROM assessment_template_questions 
            WHERE template_id = 3 AND question_id LIKE 'sas_%'
            ORDER BY question_id
            LIMIT 5
        """)
        
        updated_questions = cursor.fetchall()
        for question_id, scoring in updated_questions:
            print(f"  {question_id}: {scoring}")
        
    except Exception as e:
        print(f"更新过程中出现错误: {e}")
        if 'conn' in locals():
            conn.rollback()
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    update_sas_scoring()