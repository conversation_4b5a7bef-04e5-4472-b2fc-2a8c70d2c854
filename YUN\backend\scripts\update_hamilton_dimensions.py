#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新汉密尔顿抑郁量表的维度信息

此脚本将为汉密尔顿抑郁量表的所有问题添加维度归属信息
"""

import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def update_hamilton_depression_file():
    """更新汉密尔顿抑郁量表文件，为所有问题添加维度信息"""
    
    file_path = os.path.join(
        os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
        'app', 'clinical_scales', 'assessment', 'hamilton_depression.py'
    )
    
    # 问题ID到维度的映射
    question_dimension_mapping = {
        'hamd_1': 'mood_symptoms',     # 抑郁情绪
        'hamd_2': 'mood_symptoms',     # 内疚感
        'hamd_3': 'mood_symptoms',     # 自杀
        'hamd_4': 'sleep_symptoms',    # 入睡困难
        'hamd_5': 'sleep_symptoms',    # 睡眠中断
        'hamd_6': 'sleep_symptoms',    # 早醒
        'hamd_7': 'cognitive_symptoms', # 工作和活动
        'hamd_8': 'cognitive_symptoms', # 精神运动迟滞
        'hamd_9': 'anxiety_symptoms',   # 激越
        'hamd_10': 'anxiety_symptoms',  # 精神性焦虑
        'hamd_11': 'anxiety_symptoms',  # 躯体性焦虑
        'hamd_12': 'somatic_symptoms',  # 胃肠道躯体症状
        'hamd_13': 'somatic_symptoms',  # 一般躯体症状
        'hamd_14': 'somatic_symptoms',  # 生殖器症状
        'hamd_15': 'somatic_symptoms',  # 疑病
        'hamd_16': 'somatic_symptoms',  # 体重减轻
        'hamd_17': 'cognitive_symptoms' # 自知力
    }
    
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 为每个问题添加维度信息
        for question_id, dimension_key in question_dimension_mapping.items():
            # 查找问题定义的模式
            pattern = f'"question_id": "{question_id}"'
            if pattern in content:
                # 查找该问题的order行
                start_pos = content.find(pattern)
                if start_pos != -1:
                    # 找到order行的位置
                    order_pattern = '"order":'
                    order_pos = content.find(order_pattern, start_pos)
                    if order_pos != -1:
                        # 找到order行的结尾
                        line_end = content.find(',', order_pos)
                        if line_end != -1:
                            # 检查是否已经有dimension_key
                            next_line_start = line_end + 1
                            next_few_chars = content[next_line_start:next_line_start + 100]
                            if 'dimension_key' not in next_few_chars:
                                # 在order行后添加dimension_key
                                dimension_line = f',\n            "dimension_key": "{dimension_key}",  # 所属维度'
                                content = content[:line_end] + dimension_line + content[line_end:]
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"成功更新汉密尔顿抑郁量表文件: {file_path}")
        print("已为所有问题添加维度归属信息")
        
        return True
        
    except Exception as e:
        print(f"更新文件时发生错误: {e}")
        return False

def main():
    """主函数"""
    print("开始更新汉密尔顿抑郁量表的维度信息...")
    
    success = update_hamilton_depression_file()
    
    if success:
        print("\n=== 更新完成 ===")
        print("汉密尔顿抑郁量表的所有问题已添加维度归属信息：")
        print("- 情绪症状维度: hamd_1, hamd_2, hamd_3")
        print("- 睡眠症状维度: hamd_4, hamd_5, hamd_6")
        print("- 认知症状维度: hamd_7, hamd_8, hamd_17")
        print("- 焦虑症状维度: hamd_9, hamd_10, hamd_11")
        print("- 躯体症状维度: hamd_12, hamd_13, hamd_14, hamd_15, hamd_16")
    else:
        print("更新失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()