from sqlalchemy import create_engine, text, func
from sqlalchemy.orm import sessionmaker
import sys
sys.path.append('c:/Users/<USER>/Desktop/health-Trea/YUN/backend')

from app.models.assessment import Assessment
from app.models.distribution import AssessmentDistribution

# 连接数据库
engine = create_engine('sqlite:///c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db', echo=True)
Session = sessionmaker(bind=engine)
session = Session()

print("=== 测试Count查询问题 ===")

# 1. 测试原始的distinct count查询
print("\n1. 测试原始的distinct count查询:")
try:
    query = session.query(Assessment).outerjoin(AssessmentDistribution).distinct(Assessment.id).filter(Assessment.custom_id == 'SM_006')
    print(f"查询对象: {query}")
    
    # 手动构建count查询
    count_query = session.query(func.count(Assessment.id.distinct())).outerjoin(AssessmentDistribution).filter(Assessment.custom_id == 'SM_006')
    print(f"Count查询SQL: {count_query.statement.compile(compile_kwargs={'literal_binds': True})}")
    
    count = count_query.scalar()
    print(f"手动Count结果: {count}")
    
    # 使用原始的count方法
    original_count = query.count()
    print(f"原始Count结果: {original_count}")
    
except Exception as e:
    print(f"查询出错: {e}")

# 2. 测试子查询count
print("\n2. 测试子查询count:")
try:
    subquery = session.query(Assessment.id).outerjoin(AssessmentDistribution).distinct(Assessment.id).filter(Assessment.custom_id == 'SM_006').subquery()
    count_query2 = session.query(func.count()).select_from(subquery)
    print(f"子查询Count SQL: {count_query2.statement.compile(compile_kwargs={'literal_binds': True})}")
    
    count2 = count_query2.scalar()
    print(f"子查询Count结果: {count2}")
    
except Exception as e:
    print(f"查询出错: {e}")

# 3. 测试简单count
print("\n3. 测试简单count:")
try:
    simple_count = session.query(Assessment).filter(Assessment.custom_id == 'SM_006').count()
    print(f"简单Count结果: {simple_count}")
    
except Exception as e:
    print(f"查询出错: {e}")

# 4. 测试实际数据
print("\n4. 测试实际数据:")
try:
    query = session.query(Assessment).outerjoin(AssessmentDistribution).distinct(Assessment.id).filter(Assessment.custom_id == 'SM_006')
    results = query.all()
    print(f"实际查询结果数量: {len(results)}")
    for i, assessment in enumerate(results):
        print(f"  {i+1}. ID: {assessment.id}, Name: {assessment.name}, Status: {assessment.status}")
    
except Exception as e:
    print(f"查询出错: {e}")

session.close()