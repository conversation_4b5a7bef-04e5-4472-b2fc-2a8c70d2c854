
# -*- coding: utf-8 -*-
"""
数据完整性验证脚本
定期检查raw_answers数据的完整性
"""

import sqlite3
import os
import json
from datetime import datetime

def validate_raw_answers_integrity():
    """验证raw_answers数据完整性"""
    
    db_path = os.path.join(os.path.dirname(__file__), 'app.db')
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print(f"=== 数据完整性验证 - {datetime.now()} ===")
        
        # 检查assessment_results表
        cursor.execute("""
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN raw_answers IS NULL THEN 1 END) as missing,
                COUNT(CASE WHEN raw_answers = '' OR raw_answers = '{}' THEN 1 END) as empty,
                COUNT(CASE WHEN raw_answers IS NOT NULL AND raw_answers != '' AND raw_answers != '{}' THEN 1 END) as valid
            FROM assessment_results
        """)
        result = cursor.fetchone()
        total, missing, empty, valid = result
        
        if total > 0:
            integrity_score = (valid / total) * 100
            print(f"总评估结果: {total}")
            print(f"缺失raw_answers: {missing}")
            print(f"空raw_answers: {empty}")
            print(f"有效raw_answers: {valid}")
            print(f"完整性得分: {integrity_score:.1f}%")
            
            if integrity_score >= 95:
                print("✅ 数据完整性良好")
                return True
            elif integrity_score >= 80:
                print("⚠️ 数据完整性一般，建议检查")
                return False
            else:
                print("❌ 数据完整性较差，需要修复")
                return False
        else:
            print("📊 暂无评估结果数据")
            return True
            
    except Exception as e:
        print(f"验证过程中出现错误: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    validate_raw_answers_integrity()
