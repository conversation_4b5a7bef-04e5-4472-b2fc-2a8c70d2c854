#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
评估量表和问卷全链路监控工具

此工具提供对评估量表和问卷完整生命周期的监控功能，包括：
1. 标准模板数据库状态监控
2. 分发状态跟踪
3. 移动端获取状态监控
4. 提交状态跟踪
5. 后端计算和报告生成监控
6. 数据库保存状态验证
7. 前端和移动端查询状态监控
8. 问题诊断和修复建议

使用方法：
python assessment_questionnaire_monitor.py [options]

选项：
--user CUSTOM_ID           监控指定用户的评估和问卷（使用custom_id）
--assessment-id ID         监控指定评估的状态
--questionnaire-id ID      监控指定问卷的状态
--template-key KEY         监控指定模板的使用情况
--check-all               检查所有评估和问卷的状态
--fix-issues              自动修复发现的问题
--export-report           导出监控报告
--real-time               实时监控模式
--json                    以JSON格式输出监控结果
"""

import sys
import os
import sqlite3
import logging

if '--json' in sys.argv:
    # 强制所有日志输出到stderr
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        stream=sys.stderr
    )
    # 禁止print输出，防止误用
    import builtins
    builtins.print = lambda *args, **kwargs: None

# 修复 Python 3.12 sqlite3 datetime 适配器弃用警告
def setup_sqlite3_adapters_converters():
    """设置 Python 3.12 兼容的 sqlite3 适配器和转换器"""
    import datetime
    
    def adapt_date_iso(val):
        return val.isoformat()
    
    def adapt_datetime_iso(val):
        return val.isoformat()
    
    def convert_date(val):
        if isinstance(val, bytes):
            val = val.decode('utf-8')
        return datetime.date.fromisoformat(val)
    
    def convert_datetime(val):
        if isinstance(val, bytes):
            val = val.decode('utf-8')
        return datetime.datetime.fromisoformat(val)
    
    def convert_timestamp(val):
        if isinstance(val, bytes):
            val = val.decode('utf-8')
        try:
            return datetime.datetime.fromisoformat(val)
        except ValueError:
            try:
                return datetime.datetime.fromtimestamp(float(val))
            except (ValueError, OSError):
                return datetime.datetime.now()
    
    sqlite3.register_adapter(datetime.date, adapt_date_iso)
    sqlite3.register_adapter(datetime.datetime, adapt_datetime_iso)
    sqlite3.register_converter('date', convert_date)
    sqlite3.register_converter('datetime', convert_datetime)
    sqlite3.register_converter('timestamp', convert_timestamp)

# 应用修复
setup_sqlite3_adapters_converters()
import json
import logging
import argparse
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import traceback

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('assessment_monitor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 确保标准输出使用UTF-8编码
import sys
if hasattr(sys.stdout, 'reconfigure'):
    sys.stdout.reconfigure(encoding='utf-8', errors='replace')
if hasattr(sys.stderr, 'reconfigure'):
    sys.stderr.reconfigure(encoding='utf-8', errors='replace')
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
backend_dir = os.path.dirname(current_dir)
sys.path.append(backend_dir)

class MonitorStatus(Enum):
    """监控状态枚举"""
    HEALTHY = "healthy"
    WARNING = "warning"
    ERROR = "error"
    UNKNOWN = "unknown"

class WorkflowStage(Enum):
    """工作流程阶段枚举"""
    TEMPLATE_LOADING = "template_loading"
    DISTRIBUTION = "distribution"
    MOBILE_FETCH = "mobile_fetch"
    MOBILE_SUBMIT = "mobile_submit"
    BACKEND_CALCULATION = "backend_calculation"
    DATABASE_SAVE = "database_save"
    FRONTEND_QUERY = "frontend_query"
    MOBILE_QUERY = "mobile_query"

@dataclass
class MonitorResult:
    """监控结果数据类"""
    stage: WorkflowStage
    status: MonitorStatus
    message: str
    details: Dict[str, Any]
    timestamp: datetime
    suggestions: List[str]

@dataclass
class AssessmentInfo:
    """评估信息数据类"""
    id: int
    custom_id: str
    template_id: Optional[int]
    template_key: Optional[str]
    name: str
    status: str
    round_number: int
    sequence_number: int
    unique_identifier: str
    created_at: datetime
    completed_at: Optional[datetime]
    score: Optional[int]
    max_score: Optional[int]
    result: Optional[str]
    conclusion: Optional[str]

@dataclass
class QuestionnaireInfo:
    """问卷信息数据类"""
    id: int
    custom_id: str
    template_id: Optional[int]
    template_key: Optional[str]
    name: str
    status: str
    round_number: int
    sequence_number: int
    unique_identifier: str
    created_at: datetime
    completed_at: Optional[datetime]
    notes: Optional[str]

class AssessmentQuestionnaireMonitor:
    """评估量表和问卷监控器"""
    
    def __init__(self):
        self.db_connection = None
        self.monitor_results: List[MonitorResult] = []
        self.issues_found: List[Dict[str, Any]] = []
        
    def get_database_connection(self) -> sqlite3.Connection:
        """获取数据库连接"""
        if self.db_connection:
            return self.db_connection
            
        db_paths = [
            os.path.join(backend_dir, 'app.db'),
            os.path.join(backend_dir, 'healthapp.db'),
            os.path.join(backend_dir, 'health_app.db'),
            '/www/wwwroot/healthapp/app.db',
            '/www/wwwroot/healthapp/backend/app.db',
            '/healthapp/backend/app.db'
        ]
        
        for path in db_paths:
            if os.path.exists(path):
                logger.info(f"找到数据库文件: {path}")
                self.db_connection = sqlite3.connect(path, detect_types=sqlite3.PARSE_DECLTYPES | sqlite3.PARSE_COLNAMES)
                self.db_connection.row_factory = sqlite3.Row
                return self.db_connection
        
        raise FileNotFoundError("未找到数据库文件")
    
    def add_monitor_result(self, stage: WorkflowStage, status: MonitorStatus, 
                          message: str, details: Dict[str, Any] = None, 
                          suggestions: List[str] = None):
        """添加监控结果"""
        result = MonitorResult(
            stage=stage,
            status=status,
            message=message,
            details=details or {},
            timestamp=datetime.now(),
            suggestions=suggestions or []
        )
        self.monitor_results.append(result)
        
        # 记录问题
        if status in [MonitorStatus.WARNING, MonitorStatus.ERROR]:
            self.issues_found.append({
                'stage': stage.value,
                'status': status.value,
                'message': message,
                'details': details,
                'timestamp': result.timestamp.isoformat(),
                'suggestions': suggestions
            })
    
    def check_template_loading(self) -> bool:
        """检查模板加载状态"""
        logger.info("检查模板加载状态...")
        
        try:
            conn = self.get_database_connection()
            cursor = conn.cursor()
            
            # 检查评估模板表
            cursor.execute("SELECT COUNT(*) as count FROM assessment_templates")
            assessment_template_count = cursor.fetchone()['count']
            
            # 检查问卷模板表
            cursor.execute("SELECT COUNT(*) as count FROM questionnaire_templates")
            questionnaire_template_count = cursor.fetchone()['count']
            
            # 检查template_key字段是否存在
            cursor.execute("PRAGMA table_info(assessment_templates)")
            assessment_columns = [row[1] for row in cursor.fetchall()]
            has_assessment_template_key = 'template_key' in assessment_columns
            
            cursor.execute("PRAGMA table_info(questionnaire_templates)")
            questionnaire_columns = [row[1] for row in cursor.fetchall()]
            has_questionnaire_template_key = 'template_key' in questionnaire_columns
            
            # 检查有template_key的模板数量
            assessment_with_key = 0
            questionnaire_with_key = 0
            
            if has_assessment_template_key:
                cursor.execute("SELECT COUNT(*) as count FROM assessment_templates WHERE template_key IS NOT NULL")
                assessment_with_key = cursor.fetchone()['count']
            
            if has_questionnaire_template_key:
                cursor.execute("SELECT COUNT(*) as count FROM questionnaire_templates WHERE template_key IS NOT NULL")
                questionnaire_with_key = cursor.fetchone()['count']
            
            details = {
                'assessment_template_count': assessment_template_count,
                'questionnaire_template_count': questionnaire_template_count,
                'has_assessment_template_key': has_assessment_template_key,
                'has_questionnaire_template_key': has_questionnaire_template_key,
                'assessment_with_key': assessment_with_key,
                'questionnaire_with_key': questionnaire_with_key
            }
            
            # 判断状态
            if assessment_template_count == 0 and questionnaire_template_count == 0:
                self.add_monitor_result(
                    WorkflowStage.TEMPLATE_LOADING,
                    MonitorStatus.ERROR,
                    "数据库中没有任何模板",
                    details,
                    ["运行模板初始化脚本", "检查数据库迁移是否完成"]
                )
                return False
            
            if not has_assessment_template_key or not has_questionnaire_template_key:
                self.add_monitor_result(
                    WorkflowStage.TEMPLATE_LOADING,
                    MonitorStatus.WARNING,
                    "模板表缺少template_key字段",
                    details,
                    ["运行数据库迁移脚本添加template_key字段"]
                )
            
            if assessment_with_key == 0 and questionnaire_with_key == 0:
                self.add_monitor_result(
                    WorkflowStage.TEMPLATE_LOADING,
                    MonitorStatus.WARNING,
                    "没有模板设置了template_key",
                    details,
                    ["运行迁移脚本填充template_key值"]
                )
            
            self.add_monitor_result(
                WorkflowStage.TEMPLATE_LOADING,
                MonitorStatus.HEALTHY,
                f"模板加载正常: 评估模板{assessment_template_count}个, 问卷模板{questionnaire_template_count}个",
                details
            )
            return True
            
        except Exception as e:
            self.add_monitor_result(
                WorkflowStage.TEMPLATE_LOADING,
                MonitorStatus.ERROR,
                f"检查模板加载失败: {str(e)}",
                {'error': str(e), 'traceback': traceback.format_exc()},
                ["检查数据库连接", "验证表结构"]
            )
            return False
    
    def check_distribution_status(self, custom_id: Optional[str] = None) -> bool:
        """检查分发状态"""
        logger.info(f"检查分发状态... (用户Custom ID: {custom_id or '所有用户'})")
        
        try:
            conn = self.get_database_connection()
            cursor = conn.cursor()
            
            # 检查评估分发
            assessment_query = """
                SELECT ad.status, COUNT(*) as count
                FROM assessment_distributions ad
                JOIN users u ON ad.custom_id = u.custom_id
            """
            params = []
            
            if custom_id:
                assessment_query += " WHERE u.custom_id = ?"
                params.append(custom_id)
            
            assessment_query += " GROUP BY ad.status"
            
            cursor.execute(assessment_query, params)
            assessment_distributions = dict(cursor.fetchall())
            
            # 检查问卷分发
            questionnaire_query = """
                SELECT qd.status, COUNT(*) as count
                FROM questionnaire_distributions qd
                JOIN users u ON qd.custom_id = u.custom_id
            """
            params = []
            
            if custom_id:
                questionnaire_query += " WHERE u.custom_id = ?"
                params.append(custom_id)
            
            questionnaire_query += " GROUP BY qd.status"
            
            cursor.execute(questionnaire_query, params)
            questionnaire_distributions = dict(cursor.fetchall())
            
            # 检查过期的分发
            overdue_query = """
                SELECT 'assessment' as type, COUNT(*) as count
                FROM assessment_distributions ad
                JOIN users u ON ad.custom_id = u.custom_id
                WHERE ad.due_date < ? AND ad.status = 'distributed'
            """
            params = [datetime.now()]
            
            if custom_id:
                overdue_query += " AND u.custom_id = ?"
                params.append(custom_id)
            
            overdue_query += """
                UNION ALL
                SELECT 'questionnaire' as type, COUNT(*) as count
                FROM questionnaire_distributions qd
                JOIN users u ON qd.custom_id = u.custom_id
                WHERE qd.due_date < ? AND qd.status = 'distributed'
            """
            params.append(datetime.now())
            
            if custom_id:
                overdue_query += " AND u.custom_id = ?"
                params.append(custom_id)
            
            cursor.execute(overdue_query, params)
            overdue_results = cursor.fetchall()
            overdue_assessments = next((row['count'] for row in overdue_results if row['type'] == 'assessment'), 0)
            overdue_questionnaires = next((row['count'] for row in overdue_results if row['type'] == 'questionnaire'), 0)
            
            details = {
                'assessment_distributions': assessment_distributions,
                'questionnaire_distributions': questionnaire_distributions,
                'overdue_assessments': overdue_assessments,
                'overdue_questionnaires': overdue_questionnaires
            }
            
            # 判断状态
            total_overdue = overdue_assessments + overdue_questionnaires
            if total_overdue > 0:
                self.add_monitor_result(
                    WorkflowStage.DISTRIBUTION,
                    MonitorStatus.WARNING,
                    f"发现{total_overdue}个过期分发",
                    details,
                    ["提醒用户完成过期评估", "延长截止时间", "重新分发"]
                )
            
            pending_assessments = assessment_distributions.get('distributed', 0)
            pending_questionnaires = questionnaire_distributions.get('distributed', 0)
            
            if pending_assessments == 0 and pending_questionnaires == 0:
                self.add_monitor_result(
                    WorkflowStage.DISTRIBUTION,
                    MonitorStatus.HEALTHY,
                    "没有待完成的分发",
                    details
                )
            else:
                self.add_monitor_result(
                    WorkflowStage.DISTRIBUTION,
                    MonitorStatus.HEALTHY,
                    f"分发状态正常: 待完成评估{pending_assessments}个, 待完成问卷{pending_questionnaires}个",
                    details
                )
            
            return True
            
        except Exception as e:
            self.add_monitor_result(
                WorkflowStage.DISTRIBUTION,
                MonitorStatus.ERROR,
                f"检查分发状态失败: {str(e)}",
                {'error': str(e), 'traceback': traceback.format_exc()},
                ["检查分发表结构", "验证数据完整性"]
            )
            return False
    
    def check_mobile_fetch_status(self, custom_id: Optional[str] = None) -> bool:
        """检查移动端获取状态"""
        logger.info(f"检查移动端获取状态... (用户Custom ID: {custom_id or '所有用户'})")
        
        try:
            conn = self.get_database_connection()
            cursor = conn.cursor()
            
            # 检查评估的template_id回填情况
            assessment_backfill_query = """
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN template_id IS NULL THEN 1 ELSE 0 END) as missing_template_id
                FROM assessments
            """
            params = []
            
            if custom_id:
                assessment_backfill_query += " WHERE custom_id = ?"
                params.append(custom_id)
            
            cursor.execute(assessment_backfill_query, params)
            assessment_backfill = cursor.fetchone()
            
            # 检查问卷的template_id回填情况
            questionnaire_backfill_query = """
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN template_id IS NULL THEN 1 ELSE 0 END) as missing_template_id
                FROM questionnaires
            """
            params = []
            
            if custom_id:
                questionnaire_backfill_query += " WHERE custom_id = ?"
                params.append(custom_id)
            
            cursor.execute(questionnaire_backfill_query, params)
            questionnaire_backfill = cursor.fetchone()
            
            # 检查最近的API访问日志（如果有的话）
            # 这里可以扩展检查API访问日志
            
            details = {
                'assessment_total': assessment_backfill['total'],
                'assessment_missing_template_id': assessment_backfill['missing_template_id'],
                'questionnaire_total': questionnaire_backfill['total'],
                'questionnaire_missing_template_id': questionnaire_backfill['missing_template_id']
            }
            
            # 判断状态
            missing_total = assessment_backfill['missing_template_id'] + questionnaire_backfill['missing_template_id']
            
            if missing_total > 0:
                self.add_monitor_result(
                    WorkflowStage.MOBILE_FETCH,
                    MonitorStatus.WARNING,
                    f"发现{missing_total}个记录缺少template_id",
                    details,
                    ["运行template_id回填脚本", "检查模板匹配逻辑"]
                )
            else:
                self.add_monitor_result(
                    WorkflowStage.MOBILE_FETCH,
                    MonitorStatus.HEALTHY,
                    "移动端获取状态正常",
                    details
                )
            
            return True
            
        except Exception as e:
            self.add_monitor_result(
                WorkflowStage.MOBILE_FETCH,
                MonitorStatus.ERROR,
                f"检查移动端获取状态失败: {str(e)}",
                {'error': str(e), 'traceback': traceback.format_exc()},
                ["检查API端点", "验证数据库查询"]
            )
            return False
    
    def check_submission_status(self, assessment_id: Optional[int] = None, 
                               questionnaire_id: Optional[int] = None) -> bool:
        """检查提交状态"""
        logger.info(f"检查提交状态... (评估ID: {assessment_id}, 问卷ID: {questionnaire_id})")
        
        try:
            conn = self.get_database_connection()
            cursor = conn.cursor()
            
            details = {}
            
            # 检查评估提交状态
            if assessment_id:
                cursor.execute("""
                    SELECT id, status, score, max_score, result, conclusion, 
                           completed_at, notes
                    FROM assessments WHERE id = ?
                """, (assessment_id,))
                assessment = cursor.fetchone()
                
                if assessment:
                    details['assessment'] = dict(assessment)
                    
                    if assessment['status'] == 'completed':
                        if assessment['score'] is None:
                            self.add_monitor_result(
                                WorkflowStage.MOBILE_SUBMIT,
                                MonitorStatus.WARNING,
                                f"评估{assessment_id}已完成但缺少分数",
                                details,
                                ["检查计分逻辑", "重新计算分数"]
                            )
                        elif assessment['result'] is None:
                            self.add_monitor_result(
                                WorkflowStage.MOBILE_SUBMIT,
                                MonitorStatus.WARNING,
                                f"评估{assessment_id}已完成但缺少结果分析",
                                details,
                                ["检查结果分析逻辑", "重新生成结果"]
                            )
                        else:
                            self.add_monitor_result(
                                WorkflowStage.MOBILE_SUBMIT,
                                MonitorStatus.HEALTHY,
                                f"评估{assessment_id}提交状态正常",
                                details
                            )
                    elif assessment['status'] == 'pending':
                        # 检查是否有答案数据
                        notes_data = json.loads(assessment['notes']) if assessment['notes'] else {}
                        if 'answers' in notes_data:
                            self.add_monitor_result(
                                WorkflowStage.MOBILE_SUBMIT,
                                MonitorStatus.WARNING,
                                f"评估{assessment_id}有答案但状态仍为pending",
                                details,
                                ["检查状态更新逻辑", "手动更新状态"]
                            )
                        else:
                            self.add_monitor_result(
                                WorkflowStage.MOBILE_SUBMIT,
                                MonitorStatus.HEALTHY,
                                f"评估{assessment_id}等待提交",
                                details
                            )
                else:
                    self.add_monitor_result(
                        WorkflowStage.MOBILE_SUBMIT,
                        MonitorStatus.ERROR,
                        f"评估{assessment_id}不存在",
                        details,
                        ["检查评估ID", "验证数据库完整性"]
                    )
            
            # 检查问卷提交状态
            if questionnaire_id:
                cursor.execute("""
                    SELECT id, status, completed_at, notes
                    FROM questionnaires WHERE id = ?
                """, (questionnaire_id,))
                questionnaire = cursor.fetchone()
                
                if questionnaire:
                    details['questionnaire'] = dict(questionnaire)
                    
                    if questionnaire['status'] == 'completed':
                        notes_data = json.loads(questionnaire['notes']) if questionnaire['notes'] else {}
                        if 'answers' not in notes_data:
                            self.add_monitor_result(
                                WorkflowStage.MOBILE_SUBMIT,
                                MonitorStatus.WARNING,
                                f"问卷{questionnaire_id}已完成但缺少答案数据",
                                details,
                                ["检查答案保存逻辑", "重新提交答案"]
                            )
                        else:
                            self.add_monitor_result(
                                WorkflowStage.MOBILE_SUBMIT,
                                MonitorStatus.HEALTHY,
                                f"问卷{questionnaire_id}提交状态正常",
                                details
                            )
                    elif questionnaire['status'] == 'pending':
                        self.add_monitor_result(
                            WorkflowStage.MOBILE_SUBMIT,
                            MonitorStatus.HEALTHY,
                            f"问卷{questionnaire_id}等待提交",
                            details
                        )
                else:
                    self.add_monitor_result(
                        WorkflowStage.MOBILE_SUBMIT,
                        MonitorStatus.ERROR,
                        f"问卷{questionnaire_id}不存在",
                        details,
                        ["检查问卷ID", "验证数据库完整性"]
                    )
            
            # 如果没有指定具体ID，检查整体提交状态
            if not assessment_id and not questionnaire_id:
                # 检查最近24小时的提交情况
                yesterday = datetime.now() - timedelta(days=1)
                
                cursor.execute("""
                    SELECT COUNT(*) as count FROM assessment_results 
                    WHERE created_at > ? AND status = 'completed'
                """, (yesterday,))
                recent_assessments = cursor.fetchone()['count']
                
                cursor.execute("""
                    SELECT COUNT(*) as count FROM questionnaire_responses 
                    WHERE created_at > ? AND status = 'completed'
                """, (yesterday,))
                recent_questionnaires = cursor.fetchone()['count']
                
                details.update({
                    'recent_assessments': recent_assessments,
                    'recent_questionnaires': recent_questionnaires
                })
                
                self.add_monitor_result(
                    WorkflowStage.MOBILE_SUBMIT,
                    MonitorStatus.HEALTHY,
                    f"最近24小时提交: 评估{recent_assessments}个, 问卷{recent_questionnaires}个",
                    details
                )
            
            return True
            
        except Exception as e:
            self.add_monitor_result(
                WorkflowStage.MOBILE_SUBMIT,
                MonitorStatus.ERROR,
                f"检查提交状态失败: {str(e)}",
                {'error': str(e), 'traceback': traceback.format_exc()},
                ["检查数据库连接", "验证表结构"]
            )
            return False
    
    def check_calculation_status(self) -> bool:
        """检查后端计算状态"""
        logger.info("检查后端计算状态...")
        
        try:
            conn = self.get_database_connection()
            cursor = conn.cursor()
            
            # 检查已完成但缺少计算结果的评估
            cursor.execute("""
                SELECT COUNT(*) as count FROM assessments 
                WHERE status = 'completed' AND (score IS NULL OR result IS NULL)
            """)
            incomplete_calculations = cursor.fetchone()['count']
            
            # 检查分数异常的评估（分数超出最大分数）
            cursor.execute("""
                SELECT COUNT(*) as count FROM assessments 
                WHERE status = 'completed' AND score > max_score AND max_score IS NOT NULL
            """)
            invalid_scores = cursor.fetchone()['count']
            
            # 检查缺少结论的评估
            cursor.execute("""
                SELECT COUNT(*) as count FROM assessments 
                WHERE status = 'completed' AND (conclusion IS NULL OR conclusion = '')
            """)
            missing_conclusions = cursor.fetchone()['count']
            
            details = {
                'incomplete_calculations': incomplete_calculations,
                'invalid_scores': invalid_scores,
                'missing_conclusions': missing_conclusions
            }
            
            # 判断状态
            if incomplete_calculations > 0:
                self.add_monitor_result(
                    WorkflowStage.BACKEND_CALCULATION,
                    MonitorStatus.ERROR,
                    f"发现{incomplete_calculations}个评估缺少计算结果",
                    details,
                    ["检查计分算法", "重新运行计算", "验证模板配置"]
                )
            
            if invalid_scores > 0:
                self.add_monitor_result(
                    WorkflowStage.BACKEND_CALCULATION,
                    MonitorStatus.ERROR,
                    f"发现{invalid_scores}个评估分数异常",
                    details,
                    ["检查计分逻辑", "验证答案数据", "修正分数"]
                )
            
            if missing_conclusions > 0:
                self.add_monitor_result(
                    WorkflowStage.BACKEND_CALCULATION,
                    MonitorStatus.WARNING,
                    f"发现{missing_conclusions}个评估缺少结论",
                    details,
                    ["检查结论生成逻辑", "重新生成结论"]
                )
            
            if incomplete_calculations == 0 and invalid_scores == 0:
                self.add_monitor_result(
                    WorkflowStage.BACKEND_CALCULATION,
                    MonitorStatus.HEALTHY,
                    "后端计算状态正常",
                    details
                )
            
            return True
            
        except Exception as e:
            self.add_monitor_result(
                WorkflowStage.BACKEND_CALCULATION,
                MonitorStatus.ERROR,
                f"检查后端计算状态失败: {str(e)}",
                {'error': str(e), 'traceback': traceback.format_exc()},
                ["检查计算服务", "验证数据库查询"]
            )
            return False
    
    def check_database_save_status(self) -> bool:
        """检查数据库保存状态"""
        logger.info("检查数据库保存状态...")
        
        try:
            conn = self.get_database_connection()
            cursor = conn.cursor()
            
            # 检查数据完整性
            cursor.execute("""
                SELECT COUNT(*) as count FROM assessments 
                WHERE unique_identifier IS NULL OR unique_identifier = ''
            """)
            missing_unique_ids = cursor.fetchone()['count']
            
            # 检查重复的unique_identifier
            cursor.execute("""
                SELECT unique_identifier, COUNT(*) as count 
                FROM assessments 
                WHERE unique_identifier IS NOT NULL 
                GROUP BY unique_identifier 
                HAVING COUNT(*) > 1
            """)
            duplicate_unique_ids = cursor.fetchall()
            
            # 检查孤儿记录（评估没有对应的分发记录）
            cursor.execute("""
                SELECT COUNT(*) as count FROM assessments a
                LEFT JOIN assessment_distributions ad ON a.id = ad.assessment_id
                WHERE ad.id IS NULL
            """)
            orphan_assessments = cursor.fetchone()['count']
            
            cursor.execute("""
                SELECT COUNT(*) as count FROM questionnaires q
                LEFT JOIN questionnaire_distributions qd ON q.id = qd.questionnaire_id
                WHERE qd.id IS NULL
            """)
            orphan_questionnaires = cursor.fetchone()['count']
            
            details = {
                'missing_unique_ids': missing_unique_ids,
                'duplicate_unique_ids': len(duplicate_unique_ids),
                'orphan_assessments': orphan_assessments,
                'orphan_questionnaires': orphan_questionnaires
            }
            
            # 判断状态
            if missing_unique_ids > 0:
                self.add_monitor_result(
                    WorkflowStage.DATABASE_SAVE,
                    MonitorStatus.ERROR,
                    f"发现{missing_unique_ids}个记录缺少唯一标识符",
                    details,
                    ["重新生成唯一标识符", "检查数据创建逻辑"]
                )
            
            if len(duplicate_unique_ids) > 0:
                self.add_monitor_result(
                    WorkflowStage.DATABASE_SAVE,
                    MonitorStatus.ERROR,
                    f"发现{len(duplicate_unique_ids)}个重复的唯一标识符",
                    details,
                    ["修复重复标识符", "检查唯一性约束"]
                )
            
            if orphan_assessments > 0 or orphan_questionnaires > 0:
                self.add_monitor_result(
                    WorkflowStage.DATABASE_SAVE,
                    MonitorStatus.WARNING,
                    f"发现孤儿记录: 评估{orphan_assessments}个, 问卷{orphan_questionnaires}个",
                    details,
                    ["创建对应的分发记录", "清理孤儿记录"]
                )
            
            if (missing_unique_ids == 0 and len(duplicate_unique_ids) == 0 and 
                orphan_assessments == 0 and orphan_questionnaires == 0):
                self.add_monitor_result(
                    WorkflowStage.DATABASE_SAVE,
                    MonitorStatus.HEALTHY,
                    "数据库保存状态正常",
                    details
                )
            
            return True
            
        except Exception as e:
            self.add_monitor_result(
                WorkflowStage.DATABASE_SAVE,
                MonitorStatus.ERROR,
                f"检查数据库保存状态失败: {str(e)}",
                {'error': str(e), 'traceback': traceback.format_exc()},
                ["检查数据库完整性", "验证约束条件"]
            )
            return False
    
    def check_query_status(self) -> bool:
        """检查查询状态"""
        logger.info("检查查询状态...")
        
        try:
            conn = self.get_database_connection()
            cursor = conn.cursor()
            
            # 检查索引是否存在
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='index' AND name LIKE '%template_key%'
            """)
            template_key_indexes = [row[0] for row in cursor.fetchall()]
            
            # 检查查询性能（简单测试）
            start_time = time.time()
            cursor.execute("""
                SELECT COUNT(*) FROM assessments 
                WHERE status = 'completed'
            """)
            query_time = time.time() - start_time
            
            details = {
                'template_key_indexes': template_key_indexes,
                'query_time': query_time
            }
            
            # 判断状态
            if len(template_key_indexes) == 0:
                self.add_monitor_result(
                    WorkflowStage.FRONTEND_QUERY,
                    MonitorStatus.WARNING,
                    "缺少template_key索引",
                    details,
                    ["创建template_key索引以提高查询性能"]
                )
            
            if query_time > 1.0:  # 查询时间超过1秒
                self.add_monitor_result(
                    WorkflowStage.FRONTEND_QUERY,
                    MonitorStatus.WARNING,
                    f"查询性能较慢: {query_time:.2f}秒",
                    details,
                    ["优化数据库索引", "检查查询语句", "考虑数据分页"]
                )
            else:
                self.add_monitor_result(
                    WorkflowStage.FRONTEND_QUERY,
                    MonitorStatus.HEALTHY,
                    f"查询性能正常: {query_time:.3f}秒",
                    details
                )
            
            return True
            
        except Exception as e:
            self.add_monitor_result(
                WorkflowStage.FRONTEND_QUERY,
                MonitorStatus.ERROR,
                f"检查查询状态失败: {str(e)}",
                {'error': str(e), 'traceback': traceback.format_exc()},
                ["检查数据库连接", "验证查询语句"]
            )
            return False
    
    def get_assessment_info(self, assessment_id: int) -> Optional[AssessmentInfo]:
        """获取评估信息"""
        try:
            conn = self.get_database_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT a.*, at.template_key
                FROM assessments a
                LEFT JOIN assessment_templates at ON a.template_id = at.id
                WHERE a.id = ?
            """, (assessment_id,))
            
            row = cursor.fetchone()
            if row:
                return AssessmentInfo(
                    id=row['id'],
                    custom_id=row['custom_id'],
                    template_id=row['template_id'],
                    template_key=row['template_key'],
                    name=row['name'],
                    status=row['status'],
                    round_number=row.get('round_number', 1),
                    sequence_number=row.get('sequence_number', 1),
                    unique_identifier=row.get('unique_identifier', ''),
                    created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                    completed_at=datetime.fromisoformat(row['completed_at']) if row['completed_at'] else None,
                    score=row['score'],
                    max_score=row['max_score'],
                    result=row['result'],
                    conclusion=row['conclusion']
                )
            return None
            
        except Exception as e:
            logger.error(f"获取评估信息失败: {e}")
            return None
    
    def get_questionnaire_info(self, questionnaire_id: int) -> Optional[QuestionnaireInfo]:
        """获取问卷信息"""
        try:
            conn = self.get_database_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT q.*, qt.template_key
                FROM questionnaires q
                LEFT JOIN questionnaire_templates qt ON q.template_id = qt.id
                WHERE q.id = ?
            """, (questionnaire_id,))
            
            row = cursor.fetchone()
            if row:
                return QuestionnaireInfo(
                    id=row['id'],
                    custom_id=row['custom_id'],
                    template_id=row['template_id'],
                    template_key=row['template_key'],
                    name=row['name'],
                    status=row['status'],
                    round_number=row.get('round_number', 1),
                    sequence_number=row.get('sequence_number', 1),
                    unique_identifier=row.get('unique_identifier', ''),
                    created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                    completed_at=datetime.fromisoformat(row['completed_at']) if row['completed_at'] else None,
                    notes=row['notes']
                )
            return None
            
        except Exception as e:
            logger.error(f"获取问卷信息失败: {e}")
            return None
    
    def monitor_template_usage(self, template_key: str) -> bool:
        """监控模板使用情况"""
        logger.info(f"监控模板使用情况: {template_key}")
        
        try:
            conn = self.get_database_connection()
            cursor = conn.cursor()
            
            # 检查评估模板使用情况
            cursor.execute("""
                SELECT COUNT(*) as count FROM assessments a
                JOIN assessment_templates at ON a.template_id = at.id
                WHERE at.template_key = ?
            """, (template_key,))
            assessment_usage = cursor.fetchone()['count']
            
            # 检查问卷模板使用情况
            cursor.execute("""
                SELECT COUNT(*) as count FROM questionnaires q
                JOIN questionnaire_templates qt ON q.template_id = qt.id
                WHERE qt.template_key = ?
            """, (template_key,))
            questionnaire_usage = cursor.fetchone()['count']
            
            # 检查最近使用情况
            last_week = datetime.now() - timedelta(days=7)
            cursor.execute("""
                SELECT COUNT(*) as count FROM assessments a
                JOIN assessment_templates at ON a.template_id = at.id
                WHERE at.template_key = ? AND a.created_at > ?
            """, (template_key, last_week))
            recent_assessment_usage = cursor.fetchone()['count']
            
            cursor.execute("""
                SELECT COUNT(*) as count FROM questionnaires q
                JOIN questionnaire_templates qt ON q.template_id = qt.id
                WHERE qt.template_key = ? AND q.created_at > ?
            """, (template_key, last_week))
            recent_questionnaire_usage = cursor.fetchone()['count']
            
            details = {
                'template_key': template_key,
                'assessment_usage': assessment_usage,
                'questionnaire_usage': questionnaire_usage,
                'recent_assessment_usage': recent_assessment_usage,
                'recent_questionnaire_usage': recent_questionnaire_usage
            }
            
            total_usage = assessment_usage + questionnaire_usage
            recent_usage = recent_assessment_usage + recent_questionnaire_usage
            
            if total_usage == 0:
                self.add_monitor_result(
                    WorkflowStage.TEMPLATE_LOADING,
                    MonitorStatus.WARNING,
                    f"模板{template_key}从未被使用",
                    details,
                    ["检查模板配置", "验证模板可用性"]
                )
            elif recent_usage == 0:
                self.add_monitor_result(
                    WorkflowStage.TEMPLATE_LOADING,
                    MonitorStatus.WARNING,
                    f"模板{template_key}最近一周未被使用",
                    details,
                    ["检查模板是否仍然需要", "验证分发配置"]
                )
            else:
                self.add_monitor_result(
                    WorkflowStage.TEMPLATE_LOADING,
                    MonitorStatus.HEALTHY,
                    f"模板{template_key}使用正常: 总计{total_usage}次, 最近一周{recent_usage}次",
                    details
                )
            
            return True
            
        except Exception as e:
            self.add_monitor_result(
                WorkflowStage.TEMPLATE_LOADING,
                MonitorStatus.ERROR,
                f"监控模板使用情况失败: {str(e)}",
                {'error': str(e), 'traceback': traceback.format_exc()},
                ["检查模板配置", "验证数据库查询"]
            )
            return False
    
    def run_full_check(self, custom_id: Optional[str] = None, 
                      assessment_id: Optional[int] = None,
                      questionnaire_id: Optional[int] = None,
                      template_key: Optional[str] = None) -> bool:
        """运行完整检查"""
        logger.info("开始运行完整检查...")
        
        success = True
        
        # 1. 检查模板加载
        if not self.check_template_loading():
            success = False
        
        # 2. 检查分发状态
        if not self.check_distribution_status(custom_id):
            success = False
        
        # 3. 检查移动端获取状态
        if not self.check_mobile_fetch_status(custom_id):
            success = False
        
        # 4. 检查提交状态
        if not self.check_submission_status(assessment_id, questionnaire_id):
            success = False
        
        # 5. 检查后端计算状态
        if not self.check_calculation_status():
            success = False
        
        # 6. 检查数据库保存状态
        if not self.check_database_save_status():
            success = False
        
        # 7. 检查查询状态
        if not self.check_query_status():
            success = False
        
        # 8. 检查模板使用情况
        if template_key:
            if not self.monitor_template_usage(template_key):
                success = False
        
        logger.info(f"完整检查完成, 成功: {success}")
        return success
    
    def print_monitor_report(self):
        """打印监控报告"""
        print("\n" + "=" * 80)
        print("评估量表和问卷全链路监控报告")
        print("=" * 80)
        print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"总检查项: {len(self.monitor_results)}")
        print(f"发现问题: {len(self.issues_found)}")
        
        # 按阶段分组显示结果
        stages = {}
        for result in self.monitor_results:
            stage = result.stage.value
            if stage not in stages:
                stages[stage] = []
            stages[stage].append(result)
        
        for stage, results in stages.items():
            print(f"\n{'-' * 60}")
            print(f"阶段: {stage}")
            print(f"{'-' * 60}")
            
            for result in results:
                status_icon = {
                    MonitorStatus.HEALTHY: "✅",
                    MonitorStatus.WARNING: "⚠️",
                    MonitorStatus.ERROR: "❌",
                    MonitorStatus.UNKNOWN: "❓"
                }[result.status]
                
                print(f"{status_icon} [{result.status.value.upper()}] {result.message}")
                
                if result.details:
                    for key, value in result.details.items():
                        print(f"   {key}: {value}")
                
                if result.suggestions:
                    print("   建议:")
                    for suggestion in result.suggestions:
                        print(f"   - {suggestion}")
                print()
        
        # 显示问题汇总
        if self.issues_found:
            print(f"\n{'-' * 60}")
            print("问题汇总")
            print(f"{'-' * 60}")
            
            for i, issue in enumerate(self.issues_found, 1):
                print(f"{i}. [{issue['status'].upper()}] {issue['message']}")
                print(f"   阶段: {issue['stage']}")
                print(f"   时间: {issue['timestamp']}")
                if issue['suggestions']:
                    print("   建议: " + ", ".join(issue['suggestions']))
                print()
    
    def export_report(self, filename: str = None):
        """导出监控报告"""
        if not filename:
            filename = f"monitor_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'total_checks': len(self.monitor_results),
            'issues_count': len(self.issues_found),
            'results': [
                {
                    'stage': result.stage.value,
                    'status': result.status.value,
                    'message': result.message,
                    'details': result.details,
                    'timestamp': result.timestamp.isoformat(),
                    'suggestions': result.suggestions
                }
                for result in self.monitor_results
            ],
            'issues': self.issues_found
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"监控报告已导出到: {filename}")
    
    def fix_common_issues(self):
        """修复常见问题"""
        logger.info("开始修复常见问题...")
        
        try:
            conn = self.get_database_connection()
            cursor = conn.cursor()
            
            fixed_count = 0
            
            # 修复缺少unique_identifier的记录
            cursor.execute("""
                SELECT id, template_id, custom_id, round_number, sequence_number
                FROM assessments 
                WHERE unique_identifier IS NULL OR unique_identifier = ''
            """)
            
            for row in cursor.fetchall():
                template_id = row['template_id'] or 0
                custom_id = row['custom_id']
                round_number = row['round_number'] or 1
                sequence_number = row['sequence_number'] or 1
                unique_identifier = f"{template_id}_{custom_id}_{round_number}_{sequence_number}"
                
                cursor.execute("""
                    UPDATE assessments SET unique_identifier = ? WHERE id = ?
                """, (unique_identifier, row['id']))
                fixed_count += 1
            
            # 修复问卷的unique_identifier
            cursor.execute("""
                SELECT id, template_id, custom_id, round_number, sequence_number
                FROM questionnaires 
                WHERE unique_identifier IS NULL OR unique_identifier = ''
            """)
            
            for row in cursor.fetchall():
                template_id = row['template_id'] or 0
                custom_id = row['custom_id']
                round_number = row['round_number'] or 1
                sequence_number = row['sequence_number'] or 1
                unique_identifier = f"{template_id}_{custom_id}_{round_number}_{sequence_number}"
                
                cursor.execute("""
                    UPDATE questionnaires SET unique_identifier = ? WHERE id = ?
                """, (unique_identifier, row['id']))
                fixed_count += 1
            
            conn.commit()
            logger.info(f"修复了{fixed_count}个问题")
            
        except Exception as e:
            logger.error(f"修复问题失败: {e}")
            if conn:
                conn.rollback()
    
    def real_time_monitor(self, interval: int = 60):
        """实时监控模式"""
        logger.info(f"启动实时监控模式, 检查间隔: {interval}秒")
        
        try:
            while True:
                print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始检查...")
                
                # 清空之前的结果
                self.monitor_results.clear()
                self.issues_found.clear()
                
                # 运行检查
                self.run_full_check()
                
                # 显示简要结果
                error_count = sum(1 for r in self.monitor_results if r.status == MonitorStatus.ERROR)
                warning_count = sum(1 for r in self.monitor_results if r.status == MonitorStatus.WARNING)
                
                if error_count > 0:
                    print(f"❌ 发现{error_count}个错误")
                elif warning_count > 0:
                    print(f"⚠️ 发现{warning_count}个警告")
                else:
                    print("✅ 所有检查通过")
                
                # 等待下次检查
                time.sleep(interval)
                
        except KeyboardInterrupt:
            logger.info("实时监控已停止")
    
    def close(self):
        """关闭监控器"""
        if self.db_connection:
            self.db_connection.close()
            self.db_connection = None

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='评估量表和问卷全链路监控工具')
    parser.add_argument('--user', type=str, help='监控指定用户的评估和问卷（使用custom_id）')
    parser.add_argument('--assessment-id', type=int, help='监控指定评估的状态')
    parser.add_argument('--questionnaire-id', type=int, help='监控指定问卷的状态')
    parser.add_argument('--template-key', type=str, help='监控指定模板的使用情况')
    parser.add_argument('--check-all', action='store_true', help='检查所有评估和问卷的状态')
    parser.add_argument('--fix-issues', action='store_true', help='自动修复发现的问题')
    parser.add_argument('--export-report', type=str, help='导出监控报告到指定文件')
    parser.add_argument('--real-time', type=int, metavar='INTERVAL', help='实时监控模式，指定检查间隔（秒）')
    parser.add_argument('--json', action='store_true', help='以JSON格式输出监控结果')
    
    args = parser.parse_args()
    
    monitor = AssessmentQuestionnaireMonitor()
    
    try:
        if args.real_time:
            monitor.real_time_monitor(args.real_time)
        else:
            # 运行检查
            monitor.run_full_check(
                custom_id=args.user,
                assessment_id=args.assessment_id,
                questionnaire_id=args.questionnaire_id,
                template_key=args.template_key
            )
            
            # 修复问题
            if args.fix_issues:
                monitor.fix_common_issues()
            
            # JSON输出
            if args.json:
                report_data = {
                    'timestamp': datetime.now().isoformat(),
                    'total_checks': len(monitor.monitor_results),
                    'issues_count': len(monitor.issues_found),
                    'results': [
                        {
                            'stage': result.stage.value,
                            'status': result.status.value,
                            'message': result.message,
                            'details': result.details,
                            'timestamp': result.timestamp.isoformat(),
                            'suggestions': result.suggestions
                        }
                        for result in monitor.monitor_results
                    ],
                    'issues': monitor.issues_found
                }
                # 只输出JSON到stdout
                import sys
                sys.stdout.write(json.dumps(report_data, ensure_ascii=False) + '\n')
                sys.stdout.flush()
                return 1 if monitor.issues_found else 0
            
            # 显示报告
            monitor.print_monitor_report()
            
            # 导出报告
            if args.export_report:
                monitor.export_report(args.export_report)
            
            # 返回退出码
            return 1 if monitor.issues_found else 0
    
    except Exception as e:
        logger.error(f"监控工具执行失败: {e}")
        return 1
    
    finally:
        monitor.close()

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)