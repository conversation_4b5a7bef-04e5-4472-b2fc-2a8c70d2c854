#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速检查template_id字段情况
"""

import sqlite3

def quick_check():
    try:
        conn = sqlite3.connect('c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db')
        cursor = conn.cursor()
        
        tables = [
            'assessment_templates',
            'questionnaire_templates', 
            'assessments',
            'questionnaires',
            'assessment_distributions',
            'questionnaire_distributions'
        ]
        
        print("Template_ID字段检查结果:")
        print("=" * 50)
        
        for table in tables:
            # 检查表是否存在
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            if cursor.fetchone():
                # 检查是否有template_id字段
                cursor.execute(f"PRAGMA table_info({table})")
                columns = cursor.fetchall()
                has_template_id = any(col[1] == 'template_id' for col in columns)
                
                status = "✅ 有" if has_template_id else "❌ 无"
                print(f"{table}: {status} template_id字段")
            else:
                print(f"{table}: ❌ 表不存在")
        
        print("\n问题总结:")
        print("=" * 50)
        
        # 检查distributions表缺少template_id的具体影响
        cursor.execute("SELECT COUNT(*) FROM assessment_distributions")
        assess_dist_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM questionnaire_distributions")
        quest_dist_count = cursor.fetchone()[0]
        
        print(f"assessment_distributions表有{assess_dist_count}条记录，但缺少template_id字段")
        print(f"questionnaire_distributions表有{quest_dist_count}条记录，但缺少template_id字段")
        print("\n这可能导致前端分发时无法获取template_id，需要添加该字段。")
        
        conn.close()
        
    except Exception as e:
        print(f"检查过程中出错: {e}")

if __name__ == "__main__":
    quick_check()