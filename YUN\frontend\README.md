# 健康评估系统 - 前端应用

基于 Vue 3 + Vite + Element Plus 构建的现代化健康评估系统前端应用。

## 🚀 特性

- **现代化技术栈**: Vue 3 + Vite + Element Plus
- **TypeScript 支持**: 完整的类型检查和智能提示
- **响应式设计**: 适配桌面端和移动端
- **组件化开发**: 高度可复用的组件库
- **性能优化**: 虚拟滚动、懒加载、代码分割
- **错误处理**: 统一的错误处理和日志记录
- **测试覆盖**: 单元测试和集成测试
- **代码规范**: ESLint + Prettier 自动格式化

## 📁 项目结构

```
src/
├── api/                    # API 接口定义
│   ├── assessment.js       # 评估量表 API
│   ├── questionnaire.js    # 问卷调查 API
│   └── user.js            # 用户管理 API
├── assets/                 # 静态资源
├── components/             # 公共组件
│   ├── common/            # 通用组件
│   ├── forms/             # 表单组件
│   └── charts/            # 图表组件
├── composables/            # 组合式 API
│   ├── useApi.js          # API 请求封装
│   └── useAuth.js         # 认证相关
├── config/                 # 配置文件
│   ├── environment.js     # 环境配置
│   └── constants.js       # 常量定义
├── layouts/                # 布局组件
├── plugins/                # Vue 插件
│   └── performance.js     # 性能监控插件
├── router/                 # 路由配置
├── services/               # 业务服务
│   ├── errorHandler.js    # 错误处理服务
│   └── mockDataManager.js # 模拟数据管理
├── store/                  # 状态管理 (Pinia)
├── utils/                  # 工具函数
│   ├── businessLogic.js   # 业务逻辑
│   ├── performance.js     # 性能工具
│   ├── request.js         # 请求封装
│   └── testUtils.js       # 测试工具
└── views/                  # 页面组件
    ├── assessments/       # 评估相关页面
    ├── questionnaires/    # 问卷相关页面
    └── dashboard/         # 仪表板页面
```

## 🛠️ 开发环境

### 系统要求

- Node.js >= 16.0.0
- npm >= 8.0.0 或 yarn >= 1.22.0

### 安装依赖

```bash
# 使用 npm
npm install

# 或使用 yarn
yarn install
```

### 开发服务器

```bash
# 启动开发服务器
npm run dev

# 或
yarn dev
```

访问 [http://localhost:8080](http://localhost:8080) 查看应用。

### 构建生产版本

```bash
# 构建生产版本
npm run build

# 预览生产版本
npm run preview
```

## 🧪 测试

### 运行测试

```bash
# 运行单元测试
npm run test

# 运行测试并生成覆盖率报告
npm run test:coverage

# 监听模式运行测试
npm run test:watch
```

## 📋 代码规范

### 代码检查

```bash
# 运行 ESLint 检查
npm run lint

# 自动修复 ESLint 问题
npm run lint:fix

# 运行 Prettier 格式化
npm run format
```

### 提交规范

项目使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
feat: 新功能
fix: 修复问题
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建或辅助工具变动
```

## 🔧 配置

### 环境变量

创建 `.env.local` 文件配置本地环境变量：

```env
# API 基础地址
VITE_API_BASE_URL=http://localhost:8006

# 是否启用模拟数据
VITE_ENABLE_MOCK=false

# 是否启用调试模式
VITE_DEBUG_MODE=true

# 性能监控
VITE_ENABLE_PERFORMANCE_MONITORING=true
```

### API 配置

在 `src/config/environment.js` 中配置 API 相关设置：

```javascript
export const CONFIG = {
  api: {
    baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8006',
    timeout: 10000,
    retryAttempts: 3
  },
  // 其他配置...
}
```

## 📊 性能监控

### 启用性能监控

```javascript
// main.js
import performancePlugin from '@/plugins/performance'

app.use(performancePlugin, {
  enableComponentProfiling: true,
  enableLifecycleProfiling: true,
  slowRenderThreshold: 16
})
```

### 查看性能数据

在浏览器控制台中：

```javascript
// 查看组件性能统计
window.__VUE_PERFORMANCE__.getStats()

// 查看页面性能摘要
window.__VUE_PERFORMANCE__.performanceCollector.getPerformanceSummary()
```

## 🐛 错误处理

### 全局错误处理

系统提供统一的错误处理机制：

```javascript
import errorHandler from '@/services/errorHandler'

// 手动处理错误
errorHandler.handle(error, context, options)

// 获取错误统计
const stats = errorHandler.getErrorStats()
```

### 错误分类

- **网络错误**: 连接失败、超时等
- **服务器错误**: 5xx 状态码
- **客户端错误**: 4xx 状态码
- **验证错误**: 数据格式错误
- **认证错误**: 登录失效
- **权限错误**: 访问被拒绝

## 🧩 组件开发

### 组件规范

1. **命名**: 使用 PascalCase，多个单词组合
2. **文件结构**: 每个组件一个文件夹，包含 `.vue`、`.test.js` 等
3. **Props**: 定义明确的类型和默认值
4. **事件**: 使用 kebab-case 命名
5. **样式**: 使用 scoped 样式，避免全局污染

### 示例组件

```vue
<template>
  <div class="custom-component">
    <h2>{{ title }}</h2>
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'CustomComponent',
  props: {
    title: {
      type: String,
      required: true
    }
  },
  emits: ['update', 'close']
}
</script>

<style scoped>
.custom-component {
  padding: 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
}
</style>
```

## 🔄 状态管理

使用 Pinia 进行状态管理：

```javascript
// stores/user.js
import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
  state: () => ({
    currentUser: null,
    isAuthenticated: false
  }),
  
  getters: {
    userName: (state) => state.currentUser?.name || '未登录'
  },
  
  actions: {
    async login(credentials) {
      // 登录逻辑
    },
    
    logout() {
      this.currentUser = null
      this.isAuthenticated = false
    }
  }
})
```

## 🚀 部署

### 构建优化

```bash
# 分析构建包大小
npm run build:analyze

# 生成构建报告
npm run build:report
```

### Docker 部署

```dockerfile
# Dockerfile
FROM node:16-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## 📚 API 文档

### 主要 API 端点

- `GET /api/assessments` - 获取评估列表
- `POST /api/assessments` - 创建评估
- `GET /api/assessments/:id` - 获取评估详情
- `PUT /api/assessments/:id` - 更新评估
- `DELETE /api/assessments/:id` - 删除评估

### 请求格式

```javascript
// 标准请求格式
{
  "data": {
    // 请求数据
  },
  "meta": {
    "timestamp": "2024-01-01T00:00:00Z",
    "requestId": "uuid"
  }
}
```

### 响应格式

```javascript
// 成功响应
{
  "success": true,
  "data": {
    // 响应数据
  },
  "meta": {
    "timestamp": "2024-01-01T00:00:00Z",
    "requestId": "uuid"
  }
}

// 错误响应
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": {}
  }
}
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

### 开发流程

1. **需求分析**: 明确功能需求和技术方案
2. **设计评审**: 进行技术设计和代码评审
3. **开发实现**: 编写代码和单元测试
4. **集成测试**: 运行完整的测试套件
5. **代码审查**: 同行评审代码质量
6. **部署上线**: 发布到生产环境

## 📞 支持

- **问题反馈**: 通过项目管理系统提交问题
- **功能建议**: 联系项目负责人讨论新功能
- **技术文档**: 查看项目内部文档

## 📄 许可证

本项目采用内部许可证，仅供内部使用。

## 🙏 致谢

感谢以下开源项目：

- [Vue.js](https://vuejs.org/) - 渐进式 JavaScript 框架
- [Vite](https://vitejs.dev/) - 下一代前端构建工具
- [Element Plus](https://element-plus.org/) - Vue 3 组件库
- [Pinia](https://pinia.vuejs.org/) - Vue 状态管理
- [Axios](https://axios-http.com/) - HTTP 客户端

---

**注意**: 本项目为健康评估系统的核心组件，请遵循开发规范和安全要求。
