#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试数据库记录情况
检查Documents和HealthRecords表中的实际数据
"""

import requests
import json
from datetime import datetime

# 测试配置
BASE_URL = "http://localhost:8006"
TEST_USER = {
    "username": "admin",
    "password": "admin123"
}

def debug_database_records():
    """调试数据库记录情况"""
    print("=== 调试数据库记录情况 ===")
    
    # 1. 用户登录
    print("\n[1] 用户登录...")
    login_url = f"{BASE_URL}/api/auth/register/login"
    login_data = {
        "username": TEST_USER["username"],
        "password": TEST_USER["password"]
    }
    
    try:
        login_response = requests.post(login_url, json=login_data)
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.text}")
            return
        
        login_result = login_response.json()
        if login_result.get("status") != "success":
            print(f"❌ 登录失败: {login_result.get('message')}")
            return
        
        access_token = login_result.get("access_token")
        user_info = login_result.get("user", {})
        custom_id = user_info.get("custom_id")
        
        print(f"✅ 登录成功，用户custom_id: {custom_id}")
        
    except Exception as e:
        print(f"❌ 登录异常: {str(e)}")
        return
    
    headers = {
        'Authorization': f'Bearer {access_token}',
        'X-User-ID': str(custom_id)
    }
    
    # 2. 检查Documents表
    print(f"\n[2] 检查Documents表（用户: {custom_id}）...")
    docs_url = f"{BASE_URL}/api/documents"
    
    try:
        # 不传custom_id参数，看看能获取到什么
        docs_response = requests.get(docs_url, headers=headers)
        print(f"Documents查询状态码: {docs_response.status_code}")
        
        if docs_response.status_code == 200:
            docs_result = docs_response.json()
            print(f"Documents响应: {json.dumps(docs_result, indent=2, ensure_ascii=False)[:1000]}...")
            
            if docs_result.get("status") == "success":
                data = docs_result.get("data", {})
                documents = data.get("documents", [])
                total = data.get("total", 0)
                
                print(f"✅ Documents表查询成功，共找到 {total} 个文档")
                
                if documents:
                    print("\n📄 Documents表中的文档:")
                    for i, doc in enumerate(documents, 1):
                        print(f"  [{i}] ID: {doc.get('id')}")
                        print(f"      标题: {doc.get('title')}")
                        print(f"      文件名: {doc.get('filename')}")
                        print(f"      状态: {doc.get('status')}")
                        print(f"      来源: {doc.get('source')}")
                        print(f"      用户ID: {doc.get('custom_id')}")
                        print(f"      创建时间: {doc.get('created_at')}")
                        print()
                else:
                    print("❌ Documents表中没有找到文档")
            else:
                print(f"❌ Documents查询失败: {docs_result.get('message')}")
        else:
            print(f"❌ Documents查询请求失败: {docs_response.text[:500]}...")
            
    except Exception as e:
        print(f"❌ Documents查询异常: {str(e)}")
    
    # 3. 检查HealthRecords表（不带custom_id过滤）
    print(f"\n[3] 检查HealthRecords表（所有记录）...")
    health_url = f"{BASE_URL}/api/user-health-records/"
    
    try:
        # 查询所有健康记录
        health_response = requests.get(health_url, headers=headers)
        print(f"HealthRecords查询状态码: {health_response.status_code}")
        
        if health_response.status_code == 200:
            health_result = health_response.json()
            print(f"HealthRecords响应: {json.dumps(health_result, indent=2, ensure_ascii=False)[:1000]}...")
            
            if health_result.get("status") == "success":
                data = health_result.get("data", {})
                records = data.get("records", [])
                total = data.get("total", 0)
                
                print(f"✅ HealthRecords表查询成功，共找到 {total} 条记录")
                
                if records:
                    print("\n📋 HealthRecords表中的记录:")
                    for i, record in enumerate(records, 1):
                        print(f"  [{i}] ID: {record.get('id')}")
                        print(f"      标题: {record.get('title')}")
                        print(f"      类型: {record.get('record_type')}")
                        print(f"      状态: {record.get('status')}")
                        print(f"      用户ID: {record.get('custom_id')}")
                        print(f"      创建时间: {record.get('created_at')}")
                        
                        # 解析content
                        content = record.get('content')
                        if content:
                            try:
                                content_data = json.loads(content)
                                print(f"      内容: {json.dumps(content_data, ensure_ascii=False)[:200]}...")
                            except:
                                print(f"      内容: {content[:100]}...")
                        print()
                else:
                    print("❌ HealthRecords表中没有找到记录")
            else:
                print(f"❌ HealthRecords查询失败: {health_result.get('message')}")
        else:
            print(f"❌ HealthRecords查询请求失败: {health_response.text[:500]}...")
            
    except Exception as e:
        print(f"❌ HealthRecords查询异常: {str(e)}")
    
    # 4. 检查特定用户的健康记录
    print(f"\n[4] 检查特定用户的健康记录（用户: {custom_id}）...")
    user_health_url = f"{BASE_URL}/api/user-health-records/user/{custom_id}"
    
    params = {
        'page': 1,
        'page_size': 50,
        'custom_id': custom_id
    }
    
    try:
        user_health_response = requests.get(user_health_url, headers=headers, params=params)
        print(f"用户健康记录查询状态码: {user_health_response.status_code}")
        
        if user_health_response.status_code == 200:
            user_health_result = user_health_response.json()
            print(f"用户健康记录响应: {json.dumps(user_health_result, indent=2, ensure_ascii=False)[:1000]}...")
            
            if user_health_result.get("status") == "success":
                data = user_health_result.get("data", {})
                records = data.get("records", [])
                total = data.get("total", 0)
                
                print(f"✅ 用户健康记录查询成功，共找到 {total} 条记录")
                
                if records:
                    print(f"\n📋 用户{custom_id}的健康记录:")
                    for i, record in enumerate(records, 1):
                        print(f"  [{i}] ID: {record.get('id')}")
                        print(f"      标题: {record.get('title')}")
                        print(f"      类型: {record.get('record_type')}")
                        print(f"      状态: {record.get('status')}")
                        print(f"      用户ID: {record.get('custom_id')}")
                        print(f"      创建时间: {record.get('created_at')}")
                        print()
                else:
                    print(f"❌ 用户{custom_id}没有健康记录")
            else:
                print(f"❌ 用户健康记录查询失败: {user_health_result.get('message')}")
        else:
            print(f"❌ 用户健康记录查询请求失败: {user_health_response.text[:500]}...")
            
    except Exception as e:
        print(f"❌ 用户健康记录查询异常: {str(e)}")
    
    # 5. 测试移动端专用端点
    print(f"\n[5] 测试移动端专用端点...")
    mobile_health_url = f"{BASE_URL}/api/user-health-records"
    
    params = {
        'custom_id': custom_id,
        'page': 1,
        'page_size': 50
    }
    
    try:
        mobile_response = requests.get(mobile_health_url, headers=headers, params=params)
        print(f"移动端健康记录查询状态码: {mobile_response.status_code}")
        
        if mobile_response.status_code == 200:
            mobile_result = mobile_response.json()
            print(f"移动端健康记录响应: {json.dumps(mobile_result, indent=2, ensure_ascii=False)[:1000]}...")
        else:
            print(f"❌ 移动端健康记录查询失败: {mobile_response.text[:500]}...")
            
    except Exception as e:
        print(f"❌ 移动端健康记录查询异常: {str(e)}")

if __name__ == "__main__":
    debug_database_records()