#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复监控发现的问题脚本
解决以下问题：
1. 评估和问卷记录缺少template_id
2. 重复的唯一标识符
"""

import logging
import sqlite3
import sys
import os
import uuid
from datetime import datetime
from typing import Dict, List, Tuple

# 创建日志记录器
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_database_path():
    """获取数据库文件路径"""
    # 尝试多个可能的数据库位置
    possible_paths = [
        os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "app.db"),
        os.path.join(os.path.dirname(os.path.abspath(__file__)), "app.db"),
        "app.db"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            logger.info(f"找到数据库文件: {path}")
            return path
    
    raise FileNotFoundError("未找到数据库文件")

def fix_template_id_issues(conn: sqlite3.Connection) -> Dict[str, int]:
    """修复template_id缺失问题"""
    cursor = conn.cursor()
    results = {
        'assessments_fixed': 0,
        'questionnaires_fixed': 0
    }
    
    logger.info("=== 修复template_id缺失问题 ===")
    
    # 1. 修复评估记录的template_id
    logger.info("修复评估记录的template_id...")
    
    # 获取缺少template_id的评估记录
    cursor.execute("""
        SELECT a.id, a.name 
        FROM assessments a 
        WHERE a.template_id IS NULL
    """)
    missing_assessments = cursor.fetchall()
    
    logger.info(f"发现 {len(missing_assessments)} 个评估记录缺少template_id")
    
    for assessment_id, assessment_name in missing_assessments:
        # 尝试根据名称匹配模板
        cursor.execute("""
            SELECT id FROM assessment_templates 
            WHERE name = ? OR template_key = ?
            LIMIT 1
        """, (assessment_name, assessment_name))
        
        template_match = cursor.fetchone()
        if template_match:
            template_id = template_match[0]
            cursor.execute("""
                UPDATE assessments 
                SET template_id = ? 
                WHERE id = ?
            """, (template_id, assessment_id))
            results['assessments_fixed'] += 1
            logger.info(f"评估 {assessment_id} ({assessment_name}) 已匹配到模板 {template_id}")
        else:
            # 如果找不到匹配的模板，使用第一个可用的模板
            cursor.execute("SELECT id FROM assessment_templates LIMIT 1")
            default_template = cursor.fetchone()
            if default_template:
                template_id = default_template[0]
                cursor.execute("""
                    UPDATE assessments 
                    SET template_id = ? 
                    WHERE id = ?
                """, (template_id, assessment_id))
                results['assessments_fixed'] += 1
                logger.info(f"评估 {assessment_id} ({assessment_name}) 已设置为默认模板 {template_id}")
    
    # 2. 修复问卷记录的template_id
    logger.info("修复问卷记录的template_id...")
    
    # 获取缺少template_id的问卷记录
    cursor.execute("""
        SELECT q.id, q.title 
        FROM questionnaires q 
        WHERE q.template_id IS NULL
    """)
    missing_questionnaires = cursor.fetchall()
    
    logger.info(f"发现 {len(missing_questionnaires)} 个问卷记录缺少template_id")
    
    for questionnaire_id, questionnaire_name in missing_questionnaires:
        # 尝试根据名称匹配模板
        cursor.execute("""
            SELECT id FROM questionnaire_templates 
            WHERE name = ? OR template_key = ?
            LIMIT 1
        """, (questionnaire_name, questionnaire_name))
        
        template_match = cursor.fetchone()
        if template_match:
            template_id = template_match[0]
            cursor.execute("""
                UPDATE questionnaires 
                SET template_id = ? 
                WHERE id = ?
            """, (template_id, questionnaire_id))
            results['questionnaires_fixed'] += 1
            logger.info(f"问卷 {questionnaire_id} ({questionnaire_name}) 已匹配到模板 {template_id}")
        else:
            # 如果找不到匹配的模板，使用第一个可用的模板
            cursor.execute("SELECT id FROM questionnaire_templates LIMIT 1")
            default_template = cursor.fetchone()
            if default_template:
                template_id = default_template[0]
                cursor.execute("""
                    UPDATE questionnaires 
                    SET template_id = ? 
                    WHERE id = ?
                """, (template_id, questionnaire_id))
                results['questionnaires_fixed'] += 1
                logger.info(f"问卷 {questionnaire_id} ({questionnaire_name}) 已设置为默认模板 {template_id}")
    
    return results

def fix_duplicate_unique_ids(conn: sqlite3.Connection) -> Dict[str, int]:
    """修复重复的唯一标识符"""
    cursor = conn.cursor()
    results = {
        'assessments_fixed': 0,
        'questionnaires_fixed': 0
    }
    
    logger.info("=== 修复重复的唯一标识符 ===")
    
    # 1. 修复评估记录的重复unique_identifier
    logger.info("检查评估记录的重复unique_identifier...")
    
    cursor.execute("""
        SELECT unique_identifier, COUNT(*) as count
        FROM assessments 
        WHERE unique_identifier IS NOT NULL
        GROUP BY unique_identifier 
        HAVING COUNT(*) > 1
    """)
    duplicate_assessment_ids = cursor.fetchall()
    
    logger.info(f"发现 {len(duplicate_assessment_ids)} 个重复的评估unique_identifier")
    
    for unique_id, count in duplicate_assessment_ids:
        # 获取所有重复的记录
        cursor.execute("""
            SELECT id FROM assessments 
            WHERE unique_identifier = ?
            ORDER BY id
        """, (unique_id,))
        duplicate_records = cursor.fetchall()
        
        # 保留第一个记录，为其他记录生成新的unique_identifier
        for i, (record_id,) in enumerate(duplicate_records[1:], 1):
            new_unique_id = str(uuid.uuid4())
            cursor.execute("""
                UPDATE assessments 
                SET unique_identifier = ? 
                WHERE id = ?
            """, (new_unique_id, record_id))
            results['assessments_fixed'] += 1
            logger.info(f"评估记录 {record_id} 的unique_identifier已更新为 {new_unique_id}")
    
    # 2. 问卷表没有unique_identifier字段，跳过检查
    logger.info("问卷表没有unique_identifier字段，跳过重复检查")
    
    return results

def add_missing_unique_ids(conn: sqlite3.Connection) -> Dict[str, int]:
    """为缺少unique_identifier的记录添加唯一标识符"""
    cursor = conn.cursor()
    results = {
        'assessments_added': 0,
        'questionnaires_added': 0
    }
    
    logger.info("=== 为缺少unique_identifier的记录添加唯一标识符 ===")
    
    # 1. 为评估记录添加unique_identifier
    cursor.execute("""
        SELECT id FROM assessments 
        WHERE unique_identifier IS NULL
    """)
    missing_assessment_ids = cursor.fetchall()
    
    logger.info(f"发现 {len(missing_assessment_ids)} 个评估记录缺少unique_identifier")
    
    for (record_id,) in missing_assessment_ids:
        new_unique_id = str(uuid.uuid4())
        cursor.execute("""
            UPDATE assessments 
            SET unique_identifier = ? 
            WHERE id = ?
        """, (new_unique_id, record_id))
        results['assessments_added'] += 1
        logger.info(f"评估记录 {record_id} 已添加unique_identifier: {new_unique_id}")
    
    # 2. 问卷表没有unique_identifier字段，跳过添加
    logger.info("问卷表没有unique_identifier字段，跳过添加唯一标识符")
    
    return results

def main():
    """主函数"""
    logger.info("开始修复监控发现的问题...")
    
    try:
        # 获取数据库路径
        db_path = get_database_path()
        
        # 连接数据库
        conn = sqlite3.connect(db_path)
        
        # 修复template_id问题
        template_results = fix_template_id_issues(conn)
        
        # 修复重复唯一标识符问题
        duplicate_results = fix_duplicate_unique_ids(conn)
        
        # 添加缺少的唯一标识符
        missing_results = add_missing_unique_ids(conn)
        
        # 提交所有更改
        conn.commit()
        
        # 输出修复结果
        logger.info("\n=== 修复结果汇总 ===")
        logger.info(f"template_id修复:")
        logger.info(f"  - 评估记录: {template_results['assessments_fixed']} 个")
        logger.info(f"  - 问卷记录: {template_results['questionnaires_fixed']} 个")
        
        logger.info(f"重复unique_identifier修复:")
        logger.info(f"  - 评估记录: {duplicate_results['assessments_fixed']} 个")
        logger.info(f"  - 问卷记录: {duplicate_results['questionnaires_fixed']} 个")
        
        logger.info(f"缺少unique_identifier补充:")
        logger.info(f"  - 评估记录: {missing_results['assessments_added']} 个")
        logger.info(f"  - 问卷记录: {missing_results['questionnaires_added']} 个")
        
        total_fixed = (
            template_results['assessments_fixed'] + template_results['questionnaires_fixed'] +
            duplicate_results['assessments_fixed'] + duplicate_results['questionnaires_fixed'] +
            missing_results['assessments_added'] + missing_results['questionnaires_added']
        )
        
        logger.info(f"\n总计修复记录数: {total_fixed}")
        logger.info("所有问题修复完成！")
        
    except Exception as e:
        logger.error(f"修复过程中发生错误: {str(e)}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        logger.info("脚本执行成功")
        sys.exit(0)
    else:
        logger.error("脚本执行失败")
        sys.exit(1)