#!/usr/bin/env python3
"""
评估量表和问卷工作流程脚本

此脚本演示了评估量表和问卷从前端分发到移动端，
移动端完成填写后提交给后端，后端分析计算结果并生成报告，
最终保存到数据库供查询的完整工作流程。

功能特点：
1. 支持多次评估，通过round_number和sequence_number进行标记
2. 使用unique_identifier确保每次评估的唯一性
3. 自动计算分数和生成分析报告
4. 支持评估量表和问卷两种类型
"""

import sys
import os
import sqlite3
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
sys.path.append('/www/wwwroot/healthapp/backend')

def get_database_connection():
    """获取数据库连接"""
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    backend_dir = os.path.dirname(current_dir)
    
    db_paths = [
        # 本地开发环境路径
        os.path.join(backend_dir, 'app.db'),
        os.path.join(backend_dir, 'healthapp.db'),
        os.path.join(backend_dir, 'health_app.db'),
        # 生产环境路径
        '/www/wwwroot/healthapp/app.db',
        '/www/wwwroot/healthapp/backend/app.db',
        '/healthapp/backend/app.db'
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            print(f"找到数据库文件: {path}")
            return sqlite3.connect(path, detect_types=sqlite3.PARSE_DECLTYPES | sqlite3.PARSE_COLNAMES)
    
    print(f"尝试的数据库路径:")
    for path in db_paths:
        print(f"  - {path} (存在: {os.path.exists(path)})")
    
    raise FileNotFoundError("未找到数据库文件")

def create_assessment_workflow_demo():
    """创建评估量表工作流程演示"""
    print("\n=== 评估量表工作流程演示 ===")
    
    conn = get_database_connection()
    cursor = conn.cursor()
    
    try:
        # 1. 模拟前端分发评估量表
        print("\n1. 前端分发评估量表...")
        
        # 检查是否有用户
        cursor.execute("SELECT id, custom_id, username FROM users LIMIT 1")
        user = cursor.fetchone()
        if not user:
            print("错误：数据库中没有用户，请先创建用户")
            return
        
        user_id, custom_id, username = user
        print(f"   目标用户: {username} (ID: {user_id}, Custom ID: {custom_id})")
        
        # 创建评估量表记录
        assessment_name = "PHQ-9抑郁症筛查量表"
        template_id = 1  # 假设模板ID为1
        
        # 检查是否已有相同评估的记录
        cursor.execute("""
            SELECT round_number FROM assessments 
            WHERE custom_id = ? AND name = ? 
            ORDER BY round_number DESC LIMIT 1
        """, (custom_id, assessment_name))
        
        last_round = cursor.fetchone()
        round_number = (last_round[0] + 1) if last_round else 1
        sequence_number = 1
        
        # 生成唯一标识符
        unique_identifier = f"{template_id}_{custom_id}_{round_number}_{sequence_number}"
        
        # 插入评估记录
        cursor.execute("""
            INSERT INTO assessments (
                custom_id, template_id, assessment_type, name, version,
                round_number, sequence_number, unique_identifier,
                status, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            custom_id, template_id, 'MENTAL_HEALTH', assessment_name, '1.0',
            round_number, sequence_number, unique_identifier,
            'pending', datetime.now(), datetime.now()
        ))
        
        assessment_id = cursor.lastrowid
        print(f"   创建评估记录: ID={assessment_id}, 轮次={round_number}, 序号={sequence_number}")
        print(f"   唯一标识符: {unique_identifier}")
        
        # 创建分发记录
        due_date = datetime.now() + timedelta(days=7)
        cursor.execute("""
            INSERT INTO assessment_distributions (
                assessment_id, custom_id, distributor_custom_id, status,
                due_date, message, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            assessment_id, custom_id, custom_id, 'distributed',
            due_date, '请在一周内完成PHQ-9抑郁症筛查量表',
            datetime.now(), datetime.now()
        ))
        
        distribution_id = cursor.lastrowid
        print(f"   创建分发记录: ID={distribution_id}, 截止日期={due_date.strftime('%Y-%m-%d')}")
        
        # 2. 模拟移动端获取评估列表
        print("\n2. 移动端获取评估列表...")
        
        cursor.execute("""
            SELECT a.id, a.name, a.assessment_type, a.status, a.round_number,
                   a.sequence_number, a.unique_identifier, a.created_at,
                   ad.due_date, ad.id as distribution_id
            FROM assessments a
            JOIN assessment_distributions ad ON a.id = ad.assessment_id
            WHERE ad.custom_id = ? AND a.status = 'pending'
        """, (custom_id,))
        
        assessments = cursor.fetchall()
        print(f"   获取到 {len(assessments)} 个待完成的评估")
        
        for assessment in assessments:
            print(f"   - {assessment[1]} (轮次{assessment[4]}, 序号{assessment[5]})")
        
        # 3. 模拟移动端提交评估结果
        print("\n3. 移动端提交评估结果...")
        
        # 模拟PHQ-9的9个问题的答案
        answers = [
            {"question_id": 1, "answer": 2, "question_text": "做事时提不起劲或没有兴趣"},
            {"question_id": 2, "answer": 1, "question_text": "感到心情低落、沮丧或绝望"},
            {"question_id": 3, "answer": 2, "question_text": "入睡困难、睡不安稳或睡眠过多"},
            {"question_id": 4, "answer": 1, "question_text": "感觉疲倦或没有活力"},
            {"question_id": 5, "answer": 0, "question_text": "食欲不振或吃太多"},
            {"question_id": 6, "answer": 1, "question_text": "觉得自己很糟或觉得自己很失败"},
            {"question_id": 7, "answer": 0, "question_text": "对事物专注有困难"},
            {"question_id": 8, "answer": 0, "question_text": "动作或说话速度缓慢"},
            {"question_id": 9, "answer": 0, "question_text": "有不如死掉或伤害自己的念头"}
        ]
        
        total_score = sum(answer["answer"] for answer in answers)
        max_score = 27  # PHQ-9最高分
        
        print(f"   提交答案: {len(answers)} 个问题")
        print(f"   总分: {total_score}/{max_score}")
        
        # 4. 后端分析计算结果
        print("\n4. 后端分析计算结果...")
        
        # PHQ-9评分标准
        if total_score <= 4:
            result_category = "无抑郁症状"
            conclusion = "评估结果显示无明显抑郁症状，心理状态良好。"
        elif total_score <= 9:
            result_category = "轻度抑郁"
            conclusion = "评估结果显示轻度抑郁症状，建议关注心理健康状况。"
        elif total_score <= 14:
            result_category = "中度抑郁"
            conclusion = "评估结果显示中度抑郁症状，建议寻求专业心理咨询。"
        elif total_score <= 19:
            result_category = "中重度抑郁"
            conclusion = "评估结果显示中重度抑郁症状，强烈建议寻求专业医疗帮助。"
        else:
            result_category = "重度抑郁"
            conclusion = "评估结果显示重度抑郁症状，请立即寻求专业医疗帮助。"
        
        print(f"   分析结果: {result_category}")
        print(f"   结论: {conclusion}")
        
        # 5. 保存结果到数据库
        print("\n5. 保存结果到数据库...")
        
        completed_at = datetime.now()
        
        # 更新评估记录
        cursor.execute("""
            UPDATE assessments SET
                status = 'completed',
                completed_at = ?,
                score = ?,
                max_score = ?,
                result = ?,
                conclusion = ?,
                notes = ?,
                updated_at = ?
            WHERE id = ?
        """, (
            completed_at, total_score, max_score, result_category,
            conclusion, json.dumps({"answers": answers}),
            datetime.now(), assessment_id
        ))
        
        # 更新分发记录
        cursor.execute("""
            UPDATE assessment_distributions SET
                status = 'completed',
                completed_at = ?,
                updated_at = ?
            WHERE id = ?
        """, (completed_at, datetime.now(), distribution_id))
        
        print(f"   评估记录已更新: 状态=completed, 完成时间={completed_at.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   分发记录已更新: 状态=completed")
        
        # 6. 查询保存的结果
        print("\n6. 查询保存的结果...")
        
        cursor.execute("""
            SELECT a.id, a.name, a.round_number, a.sequence_number,
                   a.unique_identifier, a.score, a.max_score, a.result,
                   a.conclusion, a.completed_at
            FROM assessments a
            WHERE a.id = ?
        """, (assessment_id,))
        
        result = cursor.fetchone()
        if result:
            print(f"   评估ID: {result[0]}")
            print(f"   评估名称: {result[1]}")
            print(f"   评估轮次: {result[2]}")
            print(f"   评估序号: {result[3]}")
            print(f"   唯一标识: {result[4]}")
            print(f"   得分: {result[5]}/{result[6]}")
            print(f"   结果: {result[7]}")
            print(f"   结论: {result[8]}")
            print(f"   完成时间: {result[9]}")
        
        conn.commit()
        print("\n✅ 评估量表工作流程演示完成！")
        
    except Exception as e:
        print(f"❌ 工作流程执行失败: {e}")
        conn.rollback()
    finally:
        conn.close()

def create_questionnaire_workflow_demo():
    """创建问卷工作流程演示"""
    print("\n=== 问卷工作流程演示 ===")
    
    conn = get_database_connection()
    cursor = conn.cursor()
    
    try:
        # 1. 模拟前端分发问卷
        print("\n1. 前端分发问卷...")
        
        # 检查是否有用户
        cursor.execute("SELECT id, custom_id, username FROM users LIMIT 1")
        user = cursor.fetchone()
        if not user:
            print("错误：数据库中没有用户，请先创建用户")
            return
        
        user_id, custom_id, username = user
        print(f"   目标用户: {username} (ID: {user_id}, Custom ID: {custom_id})")
        
        # 创建问卷记录
        questionnaire_name = "健康状况调查问卷"
        template_id = 1  # 假设模板ID为1
        
        # 检查是否已有相同问卷的记录
        try:
            cursor.execute("""
                SELECT round_number FROM questionnaires 
                WHERE custom_id = ? AND name = ? 
                ORDER BY round_number DESC LIMIT 1
            """, (custom_id, questionnaire_name))
            
            last_round = cursor.fetchone()
            round_number = (last_round[0] + 1) if last_round else 1
        except sqlite3.OperationalError as e:
            if "no such column: round_number" in str(e):
                # 添加round_number字段到questionnaires表
                try:
                    cursor.execute("ALTER TABLE questionnaires ADD COLUMN round_number INTEGER DEFAULT 1")
                    conn.commit()
                    logger.info("成功添加round_number字段到questionnaires表")
                    round_number = 1
                except Exception as alter_error:
                    logger.error(f"添加round_number字段失败: {alter_error}")
                    return
            else:
                raise
        
        sequence_number = 1
        
        # 生成唯一标识符
        unique_identifier = f"{template_id}_{custom_id}_{round_number}_{sequence_number}"
        
        # 插入问卷记录
        try:
            cursor.execute("""
                INSERT INTO questionnaires (
                    custom_id, template_id, questionnaire_type, name, version,
                    round_number, sequence_number, unique_identifier,
                    status, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                custom_id, template_id, 'health', questionnaire_name, '1.0',
                round_number, sequence_number, unique_identifier,
                'pending', datetime.now(), datetime.now()
            ))
        except sqlite3.OperationalError as e:
            if "no such column: sequence_number" in str(e):
                # 添加sequence_number字段到questionnaires表
                try:
                    cursor.execute("ALTER TABLE questionnaires ADD COLUMN sequence_number INTEGER DEFAULT 1")
                    conn.commit()
                    logger.info("成功添加sequence_number字段到questionnaires表")
                    
                    # 重新尝试插入
                    cursor.execute("""
                        INSERT INTO questionnaires (
                            custom_id, template_id, questionnaire_type, name, version,
                            round_number, sequence_number, unique_identifier,
                            status, created_at, updated_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        custom_id, template_id, 'health', questionnaire_name, '1.0',
                        round_number, sequence_number, unique_identifier,
                        'pending', datetime.now(), datetime.now()
                    ))
                except Exception as alter_error:
                    logger.error(f"添加sequence_number字段失败: {alter_error}")
                    return
            elif "no such column: unique_identifier" in str(e):
                # 添加unique_identifier字段到questionnaires表
                try:
                    cursor.execute("ALTER TABLE questionnaires ADD COLUMN unique_identifier TEXT")
                    conn.commit()
                    logger.info("成功添加unique_identifier字段到questionnaires表")
                    
                    # 重新尝试插入
                    cursor.execute("""
                        INSERT INTO questionnaires (
                            custom_id, template_id, questionnaire_type, name, version,
                            round_number, sequence_number, unique_identifier,
                            status, created_at, updated_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        custom_id, template_id, 'health', questionnaire_name, '1.0',
                        round_number, sequence_number, unique_identifier,
                        'pending', datetime.now(), datetime.now()
                    ))
                except Exception as alter_error:
                    logger.error(f"添加unique_identifier字段失败: {alter_error}")
                    return
            elif "no such column: template_id" in str(e):
                # 添加template_id字段到questionnaires表
                try:
                    cursor.execute("ALTER TABLE questionnaires ADD COLUMN template_id INTEGER")
                    conn.commit()
                    logger.info("成功添加template_id字段到questionnaires表")
                    
                    # 重新尝试插入
                    cursor.execute("""
                        INSERT INTO questionnaires (
                            custom_id, template_id, questionnaire_type, name, version,
                            round_number, sequence_number, unique_identifier,
                            status, created_at, updated_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        custom_id, template_id, 'health', questionnaire_name, '1.0',
                        round_number, sequence_number, unique_identifier,
                        'pending', datetime.now(), datetime.now()
                    ))
                except Exception as alter_error:
                    logger.error(f"添加template_id字段失败: {alter_error}")
                    return
            else:
                raise
        
        questionnaire_id = cursor.lastrowid
        print(f"   创建问卷记录: ID={questionnaire_id}, 轮次={round_number}, 序号={sequence_number}")
        print(f"   唯一标识符: {unique_identifier}")
        
        # 创建分发记录
        due_date = datetime.now() + timedelta(days=3)
        cursor.execute("""
            INSERT INTO questionnaire_distributions (
                questionnaire_id, custom_id, distributor_custom_id, status,
                due_date, message, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            questionnaire_id, custom_id, custom_id, 'distributed',
            due_date, '请在三天内完成健康状况调查问卷',
            datetime.now(), datetime.now()
        ))
        
        distribution_id = cursor.lastrowid
        print(f"   创建分发记录: ID={distribution_id}, 截止日期={due_date.strftime('%Y-%m-%d')}")
        
        # 2. 模拟移动端获取问卷列表
        print("\n2. 移动端获取问卷列表...")
        
        cursor.execute("""
            SELECT q.id, q.name, q.questionnaire_type, q.status, q.round_number,
                   q.sequence_number, q.unique_identifier, q.created_at,
                   qd.due_date, qd.id as distribution_id
            FROM questionnaires q
            JOIN questionnaire_distributions qd ON q.id = qd.questionnaire_id
            WHERE qd.custom_id = ? AND q.status = 'pending'
        """, (custom_id,))
        
        questionnaires = cursor.fetchall()
        print(f"   获取到 {len(questionnaires)} 个待完成的问卷")
        
        for questionnaire in questionnaires:
            print(f"   - {questionnaire[1]} (轮次{questionnaire[4]}, 序号{questionnaire[5]})")
        
        # 3. 模拟移动端提交问卷结果
        print("\n3. 移动端提交问卷结果...")
        
        # 模拟健康问卷的答案
        answers = [
            {"question_id": 1, "answer": "175", "question_text": "您的身高是多少？(cm)"},
            {"question_id": 2, "answer": "70", "question_text": "您的体重是多少？(kg)"},
            {"question_id": 3, "answer": "良好", "question_text": "您认为自己的健康状况如何？"},
            {"question_id": 4, "answer": "偶尔", "question_text": "您是否经常感到疲劳？"},
            {"question_id": 5, "answer": "每周3-4次", "question_text": "您多久运动一次？"}
        ]
        
        print(f"   提交答案: {len(answers)} 个问题")
        
        # 4. 后端分析计算结果
        print("\n4. 后端分析计算结果...")
        
        # 计算完成率
        completion_rate = 100.0  # 所有问题都已回答
        
        # 简单的健康状况分析
        height = float(answers[0]["answer"])
        weight = float(answers[1]["answer"])
        bmi = weight / ((height / 100) ** 2)
        
        if bmi < 18.5:
            health_status = "体重偏轻"
        elif bmi < 24:
            health_status = "体重正常"
        elif bmi < 28:
            health_status = "体重超重"
        else:
            health_status = "肥胖"
        
        conclusion = f"根据您的身高体重计算，BMI为{bmi:.1f}，属于{health_status}范围。建议保持健康的生活方式。"
        
        print(f"   BMI计算: {bmi:.1f}")
        print(f"   健康状况: {health_status}")
        print(f"   完成率: {completion_rate}%")
        
        # 5. 保存结果到数据库
        print("\n5. 保存结果到数据库...")
        
        completed_at = datetime.now()
        
        # 更新问卷记录
        cursor.execute("""
            UPDATE questionnaires SET
                status = 'completed',
                completed_at = ?,
                notes = ?,
                updated_at = ?
            WHERE id = ?
        """, (
            completed_at, json.dumps({"answers": answers, "bmi": bmi, "conclusion": conclusion}),
            datetime.now(), questionnaire_id
        ))
        
        # 更新分发记录
        cursor.execute("""
            UPDATE questionnaire_distributions SET
                status = 'completed',
                completed_at = ?,
                updated_at = ?
            WHERE id = ?
        """, (completed_at, datetime.now(), distribution_id))
        
        print(f"   问卷记录已更新: 状态=completed, 完成时间={completed_at.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   分发记录已更新: 状态=completed")
        
        # 6. 查询保存的结果
        print("\n6. 查询保存的结果...")
        
        cursor.execute("""
            SELECT q.id, q.name, q.round_number, q.sequence_number,
                   q.unique_identifier, q.notes, q.completed_at
            FROM questionnaires q
            WHERE q.id = ?
        """, (questionnaire_id,))
        
        result = cursor.fetchone()
        if result:
            print(f"   问卷ID: {result[0]}")
            print(f"   问卷名称: {result[1]}")
            print(f"   问卷轮次: {result[2]}")
            print(f"   问卷序号: {result[3]}")
            print(f"   唯一标识: {result[4]}")
            # 从notes字段中解析结论
            notes_data = json.loads(result[5]) if result[5] else {}
            conclusion_text = notes_data.get('conclusion', '无结论')
            print(f"   结论: {conclusion_text}")
            print(f"   完成时间: {result[6]}")
        
        conn.commit()
        print("\n✅ 问卷工作流程演示完成！")
        
    except Exception as e:
        print(f"❌ 工作流程执行失败: {e}")
        conn.rollback()
    finally:
        conn.close()

def show_workflow_summary():
    """显示工作流程总结"""
    print("\n=== 工作流程总结 ===")
    print("""
完整的评估量表和问卷工作流程包括以下步骤：

1. 前端分发阶段：
   - 创建评估/问卷记录，包含round_number、sequence_number和unique_identifier
   - 创建分发记录，指定目标用户和截止时间
   - 支持多次评估，避免结果覆盖

2. 移动端获取阶段：
   - 通过API获取分配给用户的评估/问卷列表
   - 显示详细的模板信息和问题列表
   - 支持按状态筛选（待完成、已完成等）

3. 移动端填写阶段：
   - 用户在移动端逐一回答问题
   - 支持多种问题类型（单选、多选、文本、数字等）
   - 实时保存答案，支持暂存和继续填写

4. 移动端提交阶段：
   - 验证答案完整性和格式
   - 提交答案到后端API
   - 获取提交确认和初步结果

5. 后端分析阶段：
   - 根据模板规则计算总分
   - 进行结果分析和分类
   - 生成个性化的结论和建议
   - 创建健康记录

6. 结果保存阶段：
   - 更新评估/问卷状态为已完成
   - 保存分数、结果分类和结论
   - 更新分发记录状态
   - 记录完成时间

7. 结果查询阶段：
   - 支持按用户、时间、类型等条件查询
   - 提供历史记录对比
   - 生成趋势分析报告

关键特性：
- 唯一标识符：确保每次评估的唯一性，格式为 template_id_custom_id_round_number_sequence_number
- 多次评估支持：通过round_number区分不同轮次的评估
- 序号管理：通过sequence_number支持同一轮次的多个评估
- 状态管理：完整的状态流转（pending -> in_progress -> completed）
- 数据完整性：严格的数据验证和错误处理
    """)

def main():
    """主函数"""
    print("评估量表和问卷工作流程演示脚本")
    print("=" * 50)
    
    try:
        # 运行评估量表工作流程演示
        create_assessment_workflow_demo()
        
        # 运行问卷工作流程演示
        create_questionnaire_workflow_demo()
        
        # 显示工作流程总结
        show_workflow_summary()
        
    except Exception as e:
        print(f"❌ 脚本执行失败: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)