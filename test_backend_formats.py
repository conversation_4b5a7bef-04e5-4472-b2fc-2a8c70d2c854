#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
from urllib.parse import urlencode

def test_different_formats():
    """测试不同的请求格式"""
    print("=== 测试不同的请求格式 ===")
    
    base_url = "http://8.138.188.26/api/direct-login"
    
    # 测试数据
    username = "admin"
    password = "admin123"
    
    # 1. 测试JSON格式
    print("\n1. 测试JSON格式")
    try:
        response = requests.post(
            base_url,
            json={"username": username, "password": password},
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        if response.status_code == 200:
            result = response.json()
            if result.get('access_token'):
                print("✓ JSON格式登录成功")
            else:
                print(f"✗ JSON格式登录失败: {result.get('error')}")
    except Exception as e:
        print(f"JSON格式请求异常: {e}")
    
    # 2. 测试表单格式
    print("\n2. 测试表单格式")
    try:
        response = requests.post(
            base_url,
            data={"username": username, "password": password},
            headers={'Content-Type': 'application/x-www-form-urlencoded'},
            timeout=10
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        if response.status_code == 200:
            result = response.json()
            if result.get('access_token'):
                print("✓ 表单格式登录成功")
            else:
                print(f"✗ 表单格式登录失败: {result.get('error')}")
    except Exception as e:
        print(f"表单格式请求异常: {e}")
    
    # 3. 测试multipart/form-data格式
    print("\n3. 测试multipart/form-data格式")
    try:
        response = requests.post(
            base_url,
            files={"username": (None, username), "password": (None, password)},
            timeout=10
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        if response.status_code == 200:
            result = response.json()
            if result.get('access_token'):
                print("✓ multipart格式登录成功")
            else:
                print(f"✗ multipart格式登录失败: {result.get('error')}")
    except Exception as e:
        print(f"multipart格式请求异常: {e}")
    
    # 4. 测试URL编码格式（手动构造）
    print("\n4. 测试URL编码格式（手动构造）")
    try:
        data = urlencode({"username": username, "password": password})
        response = requests.post(
            base_url,
            data=data,
            headers={'Content-Type': 'application/x-www-form-urlencoded'},
            timeout=10
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        if response.status_code == 200:
            result = response.json()
            if result.get('access_token'):
                print("✓ URL编码格式登录成功")
            else:
                print(f"✗ URL编码格式登录失败: {result.get('error')}")
    except Exception as e:
        print(f"URL编码格式请求异常: {e}")
    
    # 5. 测试原始字节数据
    print("\n5. 测试原始字节数据")
    try:
        data = f"username={username}&password={password}"
        response = requests.post(
            base_url,
            data=data.encode('utf-8'),
            headers={'Content-Type': 'application/x-www-form-urlencoded'},
            timeout=10
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        if response.status_code == 200:
            result = response.json()
            if result.get('access_token'):
                print("✓ 原始字节数据登录成功")
            else:
                print(f"✗ 原始字节数据登录失败: {result.get('error')}")
    except Exception as e:
        print(f"原始字节数据请求异常: {e}")

def test_local_server():
    """测试本地服务器"""
    print("\n\n=== 测试本地服务器 ===")
    
    base_url = "http://localhost:8006/api/direct-login"
    
    # 测试数据
    username = "admin"
    password = "admin123"
    
    # 测试JSON格式
    print("\n测试本地服务器JSON格式")
    try:
        response = requests.post(
            base_url,
            json={"username": username, "password": password},
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        if response.status_code == 200:
            result = response.json()
            if result.get('access_token'):
                print("✓ 本地服务器JSON格式登录成功")
            else:
                print(f"✗ 本地服务器JSON格式登录失败: {result.get('error')}")
    except Exception as e:
        print(f"本地服务器请求异常: {e}")

if __name__ == "__main__":
    test_different_formats()
    test_local_server()