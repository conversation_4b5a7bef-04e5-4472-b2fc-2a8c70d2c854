#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
硬编码数据迁移脚本
用于识别和迁移项目中的硬编码数据到模拟数据管理器
"""

import os
import re
import json
from pathlib import Path
from typing import List, Dict, Tuple


class HardcodedDataMigrator:
    """硬编码数据迁移器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.patterns = {
            # Python 硬编码模式
            'python_dict': r'\{[^}]*["\'](?:total|count|status|data|records)["\'][^}]*\}',
            'python_list': r'\[[^\]]*["\'](?:name|id|status|type)["\'][^\]]*\]',
            'python_number': r'(?:total|count|size|length)\s*=\s*\d+',
            
            # JavaScript 硬编码模式
            'js_object': r'\{[^}]*["\'](?:total|count|status|data|records)["\'][^}]*\}',
            'js_array': r'\[[^\]]*["\'](?:name|id|status|type)["\'][^\]]*\]',
            'js_number': r'(?:total|count|size|length)\s*[:=]\s*\d+',
            
            # Vue 模板硬编码模式
            'vue_data': r'data\(\)\s*\{[^}]*return\s*\{[^}]*\}',
            'vue_computed': r'computed:\s*\{[^}]*\}',
            
            # SQL 硬编码模式
            'sql_values': r'VALUES\s*\([^)]*\)',
            'sql_insert': r'INSERT\s+INTO\s+\w+\s*\([^)]*\)\s*VALUES',
        }
        
        self.exclude_dirs = {
            '.git', '__pycache__', 'node_modules', '.vscode', 
            'dist', 'build', '.pytest_cache', 'venv', 'env'
        }
        
        self.include_extensions = {
            '.py', '.js', '.vue', '.ts', '.jsx', '.tsx', '.sql'
        }
    
    def scan_files(self) -> List[Path]:
        """扫描项目文件"""
        files = []
        
        for root, dirs, filenames in os.walk(self.project_root):
            # 排除不需要的目录
            dirs[:] = [d for d in dirs if d not in self.exclude_dirs]
            
            for filename in filenames:
                file_path = Path(root) / filename
                if file_path.suffix in self.include_extensions:
                    files.append(file_path)
        
        return files
    
    def analyze_file(self, file_path: Path) -> Dict[str, List[Dict]]:
        """分析单个文件中的硬编码数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except (UnicodeDecodeError, PermissionError):
            return {}
        
        results = {}
        
        for pattern_name, pattern in self.patterns.items():
            matches = re.finditer(pattern, content, re.IGNORECASE | re.MULTILINE)
            
            pattern_results = []
            for match in matches:
                # 计算行号
                line_num = content[:match.start()].count('\n') + 1
                
                pattern_results.append({
                    'line': line_num,
                    'match': match.group(),
                    'start': match.start(),
                    'end': match.end()
                })
            
            if pattern_results:
                results[pattern_name] = pattern_results
        
        return results
    
    def generate_migration_suggestions(self, file_path: Path, matches: Dict) -> List[str]:
        """生成迁移建议"""
        suggestions = []
        
        for pattern_name, pattern_matches in matches.items():
            for match_info in pattern_matches:
                if pattern_name.startswith('python_'):
                    suggestions.append(
                        f"行 {match_info['line']}: 考虑将 '{match_info['match'][:50]}...' "
                        f"迁移到 MockDataManager.get_mock_*() 方法"
                    )
                elif pattern_name.startswith('js_') or pattern_name.startswith('vue_'):
                    suggestions.append(
                        f"行 {match_info['line']}: 考虑使用 dataMode.js 中的 API 替换 "
                        f"'{match_info['match'][:50]}...'"
                    )
                elif pattern_name.startswith('sql_'):
                    suggestions.append(
                        f"行 {match_info['line']}: 考虑将 SQL 数据迁移到模拟数据管理器或配置文件"
                    )
        
        return suggestions
    
    def run_migration_analysis(self) -> Dict[str, Dict]:
        """运行迁移分析"""
        print("开始扫描硬编码数据...")
        
        files = self.scan_files()
        print(f"找到 {len(files)} 个文件需要分析")
        
        results = {}
        total_matches = 0
        
        for file_path in files:
            matches = self.analyze_file(file_path)
            if matches:
                relative_path = file_path.relative_to(self.project_root)
                suggestions = self.generate_migration_suggestions(file_path, matches)
                
                results[str(relative_path)] = {
                    'matches': matches,
                    'suggestions': suggestions
                }
                
                match_count = sum(len(pattern_matches) for pattern_matches in matches.values())
                total_matches += match_count
        
        print(f"\n分析完成！共发现 {total_matches} 个潜在的硬编码数据")
        return results
    
    def generate_report(self, results: Dict[str, Dict]) -> str:
        """生成迁移报告"""
        report = ["# 硬编码数据迁移报告\n"]
        
        if not results:
            report.append("✅ 未发现需要迁移的硬编码数据\n")
            return "\n".join(report)
        
        report.append(f"## 概述\n")
        report.append(f"- 扫描文件数: {len(results)}")
        total_matches = sum(
            sum(len(pattern_matches) for pattern_matches in file_data['matches'].values())
            for file_data in results.values()
        )
        report.append(f"- 发现硬编码数据: {total_matches} 处\n")
        
        report.append("## 详细分析\n")
        
        for file_path, file_data in results.items():
            report.append(f"### {file_path}\n")
            
            for suggestion in file_data['suggestions']:
                report.append(f"- {suggestion}")
            
            report.append("\n")
        
        report.append("## 迁移建议\n")
        report.append("1. **后端 Python 文件**: 使用 `MockDataManager` 类的相应方法")
        report.append("2. **前端 JavaScript/Vue 文件**: 使用 `dataMode.js` 中的 API")
        report.append("3. **SQL 文件**: 考虑迁移到配置文件或模拟数据管理器")
        report.append("4. **测试文件**: 可以保留硬编码数据，但建议使用工厂模式")
        
        return "\n".join(report)
    
    def save_report(self, results: Dict[str, Dict], output_path: str = None):
        """保存迁移报告"""
        if output_path is None:
            output_path = self.project_root / "MIGRATION_REPORT.md"
        
        report = self.generate_report(results)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"迁移报告已保存到: {output_path}")
        
        # 同时保存 JSON 格式的详细数据
        json_path = str(output_path).replace('.md', '.json')
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"详细数据已保存到: {json_path}")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='硬编码数据迁移分析工具')
    parser.add_argument('--project-root', default='.', help='项目根目录路径')
    parser.add_argument('--output', help='输出报告路径')
    
    args = parser.parse_args()
    
    migrator = HardcodedDataMigrator(args.project_root)
    results = migrator.run_migration_analysis()
    migrator.save_report(results, args.output)
    
    if results:
        print("\n⚠️  发现需要迁移的硬编码数据，请查看生成的报告")
        print("💡 建议优先迁移高频使用的数据")
    else:
        print("\n✅ 恭喜！未发现需要迁移的硬编码数据")


if __name__ == '__main__':
    main()