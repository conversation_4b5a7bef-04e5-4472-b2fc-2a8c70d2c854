<template>
  <div class="questionnaires-tab">
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <!-- 未完成Tab -->
      <el-tab-pane label="未完成" name="pending">
        <div class="tab-content">
          <div class="tab-header">
            <h4>未完成的量表与问卷</h4>
            <div class="filter-container">
              <el-select v-model="pendingFilterType" placeholder="选择类型" clearable style="width: 120px; margin-right: 10px;">
                <el-option label="调查问卷" value="questionnaire"></el-option>
                <el-option label="评估量表" value="assessment"></el-option>
              </el-select>
              <el-button @click="refreshPendingData" type="primary">刷新</el-button>
            </div>
          </div>

          <el-table
            :data="filteredPendingItems"
            v-loading="pendingLoading"
            :empty-text="pendingEmptyText"
            stripe
            style="width: 100%"
          >
            <el-table-column prop="created_at" label="分发日期" width="120" sortable>
              <template #default="scope">
                {{ formatDate(scope.row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column prop="due_date" label="截止日期" width="120" sortable>
              <template #default="scope">
                {{ formatDate(scope.row.due_date) }}
              </template>
            </el-table-column>
            <el-table-column label="类型" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.questionnaire ? 'primary' : 'success'">
                  {{ scope.row.questionnaire ? '调查问卷' : '评估量表' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="标题" min-width="200" show-overflow-tooltip>
              <template #default="scope">
                {{ scope.row.questionnaire ? scope.row.questionnaire.title : scope.row.assessment.title }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag type="warning">{{ scope.row.status === 'pending' ? '待完成' : scope.row.status }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="message" label="备注" width="150" show-overflow-tooltip />
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="scope">
                <el-button size="small" @click="previewPendingItem(scope.row)">预览</el-button>
                <el-button size="small" type="danger" @click="deletePendingItem(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>

      <!-- 已完成Tab -->
      <el-tab-pane label="已完成" name="completed">
        <div class="tab-content">
          <div class="tab-header">
            <h4>已完成的量表与问卷</h4>
            <div class="filter-container">
              <el-select v-model="completedFilterType" placeholder="选择类型" clearable style="width: 120px; margin-right: 10px;">
                <el-option label="调查问卷" value="questionnaire"></el-option>
                <el-option label="评估量表" value="assessment"></el-option>
              </el-select>
              <el-button @click="refreshCompletedData" type="primary">刷新</el-button>
            </div>
          </div>

          <el-table
            :data="filteredCompletedItems"
            v-loading="completedLoading"
            :empty-text="completedEmptyText"
            stripe
            style="width: 100%"
          >
            <el-table-column prop="created_at" label="完成日期" width="120" sortable>
              <template #default="scope">
                {{ formatDate(scope.row.calculated_at || scope.row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column label="类型" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.type === 'questionnaire' ? 'primary' : 'success'">
                  {{ scope.row.type === 'questionnaire' ? '调查问卷' : '评估量表' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="title" label="标题" min-width="200" show-overflow-tooltip />
            <el-table-column prop="total_score" label="总分" width="100">
              <template #default="scope">
                {{ scope.row.total_score || '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="result_level" label="结果等级" width="120">
              <template #default="scope">
                <el-tag v-if="scope.row.result_level" :type="getResultLevelType(scope.row.result_level)">
                  {{ scope.row.result_level }}
                </el-tag>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="300" fixed="right">
              <template #default="scope">
                <el-button size="small" @click="viewOriginalAnswers(scope.row)">原始回答</el-button>
                <el-button size="small" type="primary" @click="viewReport(scope.row)">查看报告</el-button>
                <el-button size="small" type="danger" @click="deleteCompletedItem(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="showPreviewDialog"
      :title="currentItem?.title || '预览'"
      width="70%"
      :fullscreen="previewFullscreen"
      :before-close="() => showPreviewDialog = false"
    >
      <template #header="{ close, titleId, titleClass }">
        <div class="dialog-header-custom">
          <span :id="titleId" :class="titleClass">{{ currentItem?.title || '预览' }}</span>
          <div class="dialog-header-actions">
            <el-button 
              :icon="previewFullscreen ? 'Minus' : 'FullScreen'" 
              @click="previewFullscreen = !previewFullscreen"
              circle
              size="small"
            />
            <el-button 
              icon="Close" 
              @click="close"
              circle
              size="small"
            />
          </div>
        </div>
      </template>
      <div v-if="currentItem">
        <div class="item-info">
          <p><strong>类型:</strong> {{ currentItem.type === 'questionnaire' ? '调查问卷' : '评估量表' }}</p>
          <p><strong>分发日期:</strong> {{ formatDate(currentItem.created_at) }}</p>
          <p v-if="currentItem.due_date"><strong>截止日期:</strong> {{ formatDate(currentItem.due_date) }}</p>
          <p v-if="currentItem.message"><strong>备注:</strong> {{ currentItem.message }}</p>
          <p v-if="currentItem.description"><strong>描述:</strong> {{ currentItem.description }}</p>
        </div>
        
        <!-- 显示模板详细信息 -->
        <div v-if="currentItem.templateData" class="template-preview">
          <el-divider>问题预览</el-divider>
          <div class="questions-preview">
            <div v-for="(question, index) in currentItem.templateData.questions" :key="index" class="question-preview-item">
              <div class="question-header">
                <span class="question-number">{{ index + 1 }}.</span>
                <span class="question-text">{{ question.question_text || question.text }}</span>
                <el-tag v-if="question.required" size="small" type="danger">必填</el-tag>
              </div>
              
              <!-- 选择题选项 -->
              <div v-if="question.question_type === 'choice' || question.question_type === 'single_choice' || question.answer_type === 'choice'" class="question-options">
                <div v-for="(option, optIndex) in getFormattedOptions(question)" :key="optIndex" class="option-item">
                  <el-radio :model-value="false" disabled>{{ option.text || option.label || option }}</el-radio>
                  <span v-if="option.score !== undefined" class="option-score">({{ option.score }}分)</span>
                </div>
              </div>
              
              <!-- 量表题 -->
              <div v-else-if="question.question_type === 'scale' || question.answer_type === 'scale'" class="question-scale">
                <div class="scale-info">
                  <span>量表范围: {{ question.options?.scale_min || question.scale_min || 1 }} - {{ question.options?.scale_max || question.scale_max || 5 }}</span>
                  <div class="scale-labels">
                    <span v-if="question.options?.scale_min_label || question.scale_min_label">{{ question.options?.scale_min_label || question.scale_min_label }}</span>
                    <span v-if="question.options?.scale_max_label || question.scale_max_label">{{ question.options?.scale_max_label || question.scale_max_label }}</span>
                  </div>
                  <div class="scale-options">
                    <el-radio-group :model-value="null" disabled>
                      <el-radio v-for="i in ((question.options?.scale_max || question.scale_max || 5) - (question.options?.scale_min || question.scale_min || 1) + 1)" :key="i" :label="(question.options?.scale_min || question.scale_min || 1) + i - 1">
                        {{ (question.options?.scale_min || question.scale_min || 1) + i - 1 }}
                      </el-radio>
                    </el-radio-group>
                  </div>
                </div>
              </div>
              
              <!-- 文本题 -->
              <div v-else-if="question.question_type === 'text' || question.answer_type === 'text'" class="question-text">
                <el-input type="textarea" placeholder="文本输入框" disabled rows="2"></el-input>
              </div>
              
              <!-- 多选题 -->
              <div v-else-if="question.question_type === 'multiple_choice'" class="question-options">
                <div v-for="(option, optIndex) in getFormattedOptions(question)" :key="optIndex" class="option-item">
                  <el-checkbox :model-value="false" disabled>{{ option.text || option.label || option }}</el-checkbox>
                  <span v-if="option.score !== undefined" class="option-score">({{ option.score }}分)</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div v-else class="no-template-data">
          <el-empty description="无法获取详细问题信息" />
        </div>
      </div>
    </el-dialog>

    <!-- 原始回答对话框 -->
    <el-dialog
      v-model="showAnswersDialog"
      :title="'原始回答 - ' + (currentItem?.title || '')"
      width="75%"
      :before-close="() => showAnswersDialog = false"
    >
      <div v-if="currentItem">
        <!-- 基本信息显示 -->
        <div class="answer-basic-info">
          <p><strong>名称:</strong> {{ currentItem.title }}</p>
          <p><strong>类型:</strong> {{ currentItem.type === 'questionnaire' ? '调查问卷' : '评估量表' }}</p>
          <p><strong>完成时间:</strong> {{ formatDate(currentItem.calculated_at || currentItem.created_at) }}</p>
          <p v-if="currentItem.total_score !== undefined"><strong>总分:</strong> {{ currentItem.total_score }}</p>
        </div>
        
        <el-divider>回答详情</el-divider>
        
        <!-- 回答内容 -->
        <div v-if="currentItem.answers && currentItem.answers.length > 0" class="answers-content">
          <div v-for="(answer, index) in currentItem.answers" :key="index" class="answer-item">
            <div class="question">
              <strong>{{ index + 1 }}. {{ getQuestionText(answer, index) }}</strong>
            </div>
            
            <!-- 显示问题选项（如果有） -->
            <div v-if="getQuestionOptions(answer, index)" class="question-options-display">
              <div class="options-label">选项:</div>
              <div class="options-list">
                <div v-for="(option, optIndex) in getQuestionOptions(answer, index)" :key="optIndex" class="option-display">
                  <span class="option-text">{{ getOptionText(option) }}</span>
                  <span v-if="option.score !== undefined || option.value !== undefined" class="option-score">({{ option.score || option.value }}分)</span>
                </div>
              </div>
            </div>
            
            <div class="answer">
              <div class="answer-label">回答:</div>
              <div class="answer-value">
                <span v-if="answer.answer !== undefined">{{ answer.answer }}</span>
                <span v-else-if="answer.value !== undefined">
                  <span v-if="typeof answer.value === 'string'">{{ answer.value }}</span>
                  <span v-else-if="typeof answer.value === 'number'">{{ answer.value }}</span>
                  <span v-else-if="Array.isArray(answer.value)">{{ answer.value.join(', ') }}</span>
                  <span v-else>{{ JSON.stringify(answer.value) }}</span>
                </span>
                <span v-else>{{ JSON.stringify(answer) }}</span>
              </div>
              <div v-if="answer.score !== undefined" class="answer-score">
                得分: {{ answer.score }}
              </div>
            </div>
          </div>
        </div>
        
        <!-- 无回答数据时的显示 -->
        <div v-else class="no-answers-data">
          <el-alert
            title="数据加载提示"
            type="warning"
            :closable="false"
            show-icon>
            <template #default>
              <p>未找到详细的回答数据，可能的原因：</p>
              <ul>
                <li>数据格式不兼容</li>
                <li>回答数据未正确保存</li>
                <li>数据加载过程中出现问题</li>
              </ul>
              <p>请检查控制台日志获取更多信息。</p>
            </template>
          </el-alert>
          
          <!-- 显示原始数据用于调试 -->
          <div v-if="currentItem" class="debug-info" style="margin-top: 20px;">
            <el-collapse>
              <el-collapse-item title="查看原始数据（调试用）" name="debug">
                <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; overflow: auto;">{{ JSON.stringify(currentItem, null, 2) }}</pre>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
      </div>
      <div v-else>
        <el-empty description="未找到数据" />
      </div>
    </el-dialog>

    <!-- 报告对话框 -->
    <el-dialog
      v-model="showReportDialog"
      :title="'报告 - ' + (currentItem?.title || '')"
      width="80%"
      :before-close="() => showReportDialog = false"
    >
      <div v-if="currentItem">
        <div class="report-content">
          <div class="report-header">
            <h3>{{ currentItem.title }}</h3>
            <p><strong>完成时间:</strong> {{ formatDate(currentItem.calculated_at || currentItem.created_at) }}</p>
            <p v-if="currentItem.total_score"><strong>总分:</strong> {{ currentItem.total_score }}</p>
            <p v-if="currentItem.result_level"><strong>结果等级:</strong> 
              <el-tag :type="getResultLevelType(currentItem.result_level)">{{ currentItem.result_level }}</el-tag>
            </p>
          </div>
          
          <!-- 总结 -->
          <div class="report-summary">
            <h4>总结</h4>
            <div v-if="currentItem.summary" class="summary-content">
              <p>{{ currentItem.summary }}</p>
            </div>
            <div v-else class="summary-content">
              <p>根据您的回答，总分为 {{ currentItem.total_score || 0 }} 分，结果等级为 {{ currentItem.result_level || '未评级' }}。</p>
              <p v-if="currentItem.dimension_scores">各维度得分情况如下所示，请参考维度分析了解详细情况。</p>
            </div>
          </div>
          
          <!-- 结果解释 -->
          <div v-if="currentItem.interpretation" class="report-interpretation">
            <h4>结果解释</h4>
            <p>{{ currentItem.interpretation }}</p>
          </div>
          
          <!-- 结论 -->
          <div class="report-conclusion">
            <h4>结论</h4>
            <div v-if="currentItem.conclusion" class="conclusion-content">
              <p>{{ currentItem.conclusion }}</p>
            </div>
            <div v-else class="conclusion-content">
              <p v-if="currentItem.result_level === '优秀' || currentItem.result_level === '良好'">您在此次评估中表现良好，各项指标基本达到预期水平。</p>
              <p v-else-if="currentItem.result_level === '一般' || currentItem.result_level === '中等'">您在此次评估中表现一般，部分指标需要关注和改善。</p>
              <p v-else-if="currentItem.result_level === '较差' || currentItem.result_level === '差'">您在此次评估中存在一些问题，建议重点关注相关方面的改善。</p>
              <p v-else>根据您的回答情况，建议关注各维度的平衡发展。</p>
            </div>
          </div>
          
          <!-- 建议 -->
          <div class="report-suggestions">
            <h4>建议</h4>
            <div v-if="currentItem.suggestions" class="suggestions-content">
              <p>{{ currentItem.suggestions }}</p>
            </div>
            <div v-else class="suggestions-content">
              <ul>
                <li v-if="currentItem.total_score && currentItem.total_score < 60">建议加强相关方面的学习和实践，提升整体水平。</li>
                <li v-if="currentItem.dimension_scores">关注得分较低的维度，制定针对性的改善计划。</li>
                <li>定期进行自我评估，跟踪改善进展。</li>
                <li>如有需要，可寻求专业指导和帮助。</li>
              </ul>
            </div>
          </div>
          
          <div v-if="currentItem.dimension_scores" class="report-dimensions">
            <h4>维度得分</h4>
            
            <!-- 维度分值列表 -->
            <div class="dimensions-grid">
              <div v-for="(score, dimension) in getDimensionScoresList(currentItem.dimension_scores)" :key="dimension" class="dimension-item">
                <span class="dimension-name">{{ score.name || dimension }}:</span>
                <span class="dimension-score">{{ score.score || score }}</span>
                <span v-if="score.max_score" class="dimension-max">/{{ score.max_score }}</span>
              </div>
            </div>
            
            <!-- 雷达图 -->
            <div v-if="shouldShowRadarChart(currentItem.dimension_scores)" class="radar-chart-container">
              <h5>维度分析雷达图</h5>
              <div ref="radarChart" class="radar-chart" style="width: 100%; height: 400px;"></div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 填写问卷/评估对话框 -->
    <el-dialog
      v-model="completeDialogVisible"
      :title="currentItem ? `填写${getTypeLabel(currentItem.item_type)}: ${currentItem.title}` : '填写'"
      width="70%"
    >
      <div v-if="currentItem" class="complete-form">
        <p class="description">{{ currentItem.description }}</p>
        
        <el-form :model="answerForm" label-position="top" ref="answerFormRef">
          <div v-for="(question, index) in currentItem.questions" :key="index" class="question-item">
            <el-divider>问题 {{ index + 1 }}</el-divider>
            
            <el-form-item :label="question.question_text" :prop="`answers.${index}`" :rules="{ required: true, message: '请回答此问题', trigger: 'blur' }">
              <!-- 选择题 -->
              <el-radio-group v-if="question.answer_type === 'choice' || question.question_type === 'choice' || question.question_type === 'single_choice'" v-model="answerForm.answers[index]">
                <el-radio v-for="(option, optIndex) in getFormattedOptions(question)" :key="optIndex" :label="option.value || option.text || option.label || option">
                  {{ getOptionText(option) }}
                </el-radio>
              </el-radio-group>
              
              <!-- 多选题 -->
              <el-checkbox-group 
                v-else-if="question.question_type === 'multiple_choice'" 
                v-model="answerForm.answers[index]"
                :aria-label="`多选题: ${question.question_text || question.text || '问题'}`"
                role="group"
              >
                <el-checkbox v-for="(option, optIndex) in getFormattedOptions(question)" :key="optIndex" :value="option.value || option.text || option.label || option">
                  {{ getOptionText(option) }}
                </el-checkbox>
              </el-checkbox-group>
              
              <!-- 文本题 -->
              <el-input v-else-if="question.answer_type === 'text'" v-model="answerForm.answers[index]" type="textarea" rows="3" placeholder="请输入您的回答"></el-input>
              
              <!-- 量表题 -->
              <div v-else-if="question.answer_type === 'scale' || question.question_type === 'scale'" class="scale-answer">
                <el-radio-group v-if="getFormattedOptions(question).length > 0" v-model="answerForm.answers[index]" class="scale-options">
                  <el-radio v-for="(option, optIndex) in getFormattedOptions(question)" :key="optIndex" :label="option.value || option.score || optIndex + 1">
                    {{ getOptionText(option) }}
                  </el-radio>
                </el-radio-group>
                <div v-else>
                  <el-slider v-model="answerForm.answers[index]" :min="question.scale_min || 1" :max="question.scale_max || 5" :step="1" show-stops show-input></el-slider>
                  <div class="scale-labels">
                    <span>{{ question.scale_min_label || '最低' }}</span>
                    <span>{{ question.scale_max_label || '最高' }}</span>
                  </div>
                </div>
              </div>
            </el-form-item>
          </div>
        </el-form>
        
        <div class="form-actions">
          <el-button @click="completeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitAnswers">提交</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, reactive, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import axios from 'axios';
import * as echarts from 'echarts';
import aggregatedApiService from '@/services/aggregatedApi';
import { useApi } from '@/composables/useApi';

const props = defineProps({
  customId: {
    type: [Number, String],
    required: true
  }
});

// Tab相关
const activeTab = ref('pending');

// 未完成Tab数据
const pendingItems = ref([]);
const pendingLoading = ref(false);
const pendingFilterType = ref('');

// 已完成Tab数据
const completedItems = ref([]);
const completedLoading = ref(false);
const completedFilterType = ref('');

// 对话框相关
const viewDialogVisible = ref(false);
const completeDialogVisible = ref(false);
const currentItem = ref(null);
const answerFormRef = ref(null);
const answerForm = reactive({
  answers: []
});

// 计算属性
const filteredPendingItems = computed(() => {
  let filtered = pendingItems.value
  
  if (pendingFilterType.value) {
    if (pendingFilterType.value === 'questionnaire') {
      filtered = filtered.filter(item => item.questionnaire)
    } else if (pendingFilterType.value === 'assessment') {
      filtered = filtered.filter(item => item.assessment)
    }
  }
  
  return filtered
})

const filteredCompletedItems = computed(() => {
  let filtered = completedItems.value
  
  if (completedFilterType.value) {
    filtered = filtered.filter(item => item.type === completedFilterType.value)
  }
  
  return filtered
})

const pendingEmptyText = computed(() => {
  if (pendingLoading.value) return '加载中...'
  if (pendingItems.value.length === 0) return '暂无待完成的问卷或评估'
  if (filteredPendingItems.value.length === 0) return '没有符合条件的数据'
  return '暂无数据'
})

const completedEmptyText = computed(() => {
  if (completedLoading.value) return '加载中...'
  if (completedItems.value.length === 0) return '暂无已完成的问卷或评估'
  if (filteredCompletedItems.value.length === 0) return '没有符合条件的数据'
  return '暂无数据'
})

// 获取未完成的分发数据
const fetchPendingData = async () => {
  if (!props.customId) return
  
  pendingLoading.value = true
  try {
    // 使用聚合API获取未完成的问卷和评估数据
    const response = await aggregatedApiService.getUserQuestionnaires(props.customId, {
      status: 'pending',
      include_assessments: true
    })
    
    console.log('获取未完成数据API响应:', response)
    
    // 修正数据解析逻辑，适配聚合API返回格式
    // 聚合API返回格式: { data: [...], metadata: {...}, statistics: {...}, total_count: 0, source_counts: {...} }
    const data = response.data || []
    console.log('解析的未完成数据:', data)
    
    // 确保data是数组
    if (Array.isArray(data)) {
      pendingItems.value = data.map(item => ({
        ...item,
        type: item.item_type || item.type || (item.questionnaire ? 'questionnaire' : 'assessment')
      }))
      console.log('未完成数据加载成功，共', data.length, '条记录')
    } else {
      console.warn('返回的数据不是数组格式:', data)
      pendingItems.value = []
    }
  } catch (error) {
    console.error('获取未完成数据失败:', error)
    ElMessage.error('获取未完成数据失败: ' + error.message)
    pendingItems.value = []
  } finally {
    pendingLoading.value = false
  }
}

// 获取已完成的结果数据
const fetchCompletedData = async () => {
  if (!props.customId) return
  
  completedLoading.value = true
  try {
    // 使用聚合API获取已完成的问卷和评估数据
    const response = await aggregatedApiService.getUserQuestionnaires(props.customId, {
      status: 'completed',
      include_assessments: true,
      include_results: true
    })
    
    console.log('获取已完成数据API响应:', response)
    
    // 修正数据解析逻辑，适配聚合API返回格式
    // 聚合API返回格式: { data: [...], metadata: {...}, statistics: {...}, total_count: 0, source_counts: {...} }
    const data = response.data || []
    console.log('解析的已完成数据:', data)
    
    // 确保data是数组
    if (Array.isArray(data)) {
      completedItems.value = data.map(item => ({
        ...item,
        type: item.item_type || item.type || (item.questionnaire ? 'questionnaire' : 'assessment'),
        title: item.title || item.questionnaire_name || item.assessment_name || item.template_name
      }))
      console.log('已完成数据加载成功，共', data.length, '条记录')
    } else {
      console.warn('返回的数据不是数组格式:', data)
      completedItems.value = []
    }
  } catch (error) {
    console.error('获取已完成数据失败:', error)
    ElMessage.error('获取已完成数据失败: ' + error.message)
    completedItems.value = []
  } finally {
    completedLoading.value = false
  }
}

watch(() => props.customId, (newVal) => {
  if (newVal) {
    if (activeTab.value === 'pending') {
      fetchPendingData();
    } else {
      fetchCompletedData();
    }
  } else {
    pendingItems.value = [];
    completedItems.value = [];
  }
}, { immediate: true });

onMounted(() => {
  if (props.customId) {
    fetchPendingData();
  }
});



// 刷新未完成数据
const refreshPendingData = () => {
  fetchPendingData()
}

// 刷新已完成数据
const refreshCompletedData = () => {
  fetchCompletedData()
}

// 刷新所有数据
const refreshData = () => {
  if (activeTab.value === 'pending') {
    fetchPendingData()
  } else {
    fetchCompletedData()
  }
}

// 预览未完成项目
const previewPendingItem = async (item) => {
  try {
    // 使用聚合API获取模板详细信息
    let templateData = null
    let templateId = null
    
    if (item.questionnaire || item.type === 'questionnaire') {
      // 问卷
      templateId = item.questionnaire?.template_id || item.questionnaire_id || item.template_id
      if (templateId) {
        const templateResponse = await aggregatedApiService.getQuestionnaireTemplate(templateId)
        if (templateResponse.success) {
          templateData = templateResponse.data
          // 确保问题数据完整
          if (templateData && templateData.questions) {
            templateData.questions = templateData.questions.map(q => ({
              question_id: q.question_id,
              question_text: q.question_text,
              question_type: q.question_type,
              options: q.options || [],
              is_required: q.is_required,
              order: q.order
            }))
          }
        }
      }
    } else if (item.assessment || item.type === 'assessment') {
      // 评估量表
      templateId = item.assessment?.template_id || item.assessment_id || item.template_id
      if (templateId) {
        const templateResponse = await aggregatedApiService.getAssessmentTemplate(templateId)
        if (templateResponse.success) {
          templateData = templateResponse.data
          // 确保问题数据完整
          if (templateData && templateData.questions) {
            templateData.questions = templateData.questions.map(q => ({
              question_id: q.question_id,
              question_text: q.question_text,
              question_type: q.question_type,
              options: q.options || [],
              is_required: q.is_required,
              order: q.order
            }))
          }
        }
      }
    }
    
    currentItem.value = {
      ...item,
      title: item.title || item.questionnaire?.title || item.assessment?.title,
      description: item.description || item.questionnaire?.description || item.assessment?.description,
      type: item.type || (item.questionnaire ? 'questionnaire' : 'assessment'),
      templateData: templateData
    }
    showPreviewDialog.value = true
  } catch (error) {
    console.error('预览失败:', error)
    ElMessage.error('预览失败')
  }
}

// 删除未完成项目
const deletePendingItem = async (item) => {
  try {
    await ElMessageBox.confirm('确定要删除这个分发记录吗？', '确认删除', {
      type: 'warning'
    })
    
    const response = item.questionnaire
      ? await aggregatedApiService.deleteQuestionnaireDistribution(item.id)
      : await aggregatedApiService.deleteAssessmentDistribution(item.id)
      
    if (response.success || response.data?.success) {
      ElMessage.success('删除成功')
      fetchPendingData()
    } else {
      throw new Error(response.message || response.data?.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 查看原始回答
const viewOriginalAnswers = async (item) => {
  try {
    console.log('查看原始回答 - 传入的item:', item)
    
    // 使用聚合API获取回答数据
    const itemId = item.questionnaire_id || item.assessment_id || item.id
    const response = item.type === 'questionnaire'
      ? await aggregatedApiService.getQuestionnaireData(props.customId, { questionnaire_id: itemId })
      : await aggregatedApiService.getAssessmentData(props.customId, { assessment_id: itemId })

    console.log('聚合API返回结果:', response)

    console.log('API返回结果:', response.data)

    if (response.data.data && response.data.data.length > 0) {
      const responseData = response.data.data[0]
      console.log('回答数据:', responseData)
      
      // 获取模板信息以显示问题
      let templateData = null
      let templateId = null
      
      if (item.type === 'questionnaire') {
        templateId = item.template_id || item.questionnaire_id || (item.questionnaire && item.questionnaire.template_id)
      } else {
        templateId = item.template_id || item.assessment_id || (item.assessment && item.assessment.template_id)
        if (!templateId && item.assessment) {
          templateId = item.assessment.template_key ? `standard_${item.assessment.template_key}` : item.assessment.id
        }
      }
      
      console.log('模板ID:', templateId)
      
      if (templateId) {
        try {
          console.log('获取模板数据，模板ID:', templateId)
          
          const templateResponse = item.type === 'questionnaire'
            ? await aggregatedApiService.getQuestionnaireTemplate(templateId)
            : await aggregatedApiService.getAssessmentTemplate(templateId)
          
          console.log('聚合API模板返回:', templateResponse)
          
          if (templateResponse.data.success || templateResponse.data.status === 'success') {
            templateData = templateResponse.data.data
            console.log('原始模板数据:', templateData)
            
            // 统一 questions 字段结构
            if (templateData && templateData.questions && Array.isArray(templateData.questions)) {
              templateData.questions = templateData.questions.map((q, idx) => ({
                question_id: q.question_id || q.id || `q_${idx + 1}`,
                question_text: q.question_text || q.text || q.question || `问题${idx + 1}`,
                question_type: q.question_type || q.type || 'single_choice',
                answer_type: q.answer_type || q.question_type || q.type || 'single_choice',
                options: q.options || q.choices || [],
                is_required: q.is_required !== undefined ? q.is_required : (q.required !== undefined ? q.required : true),
                order: q.order !== undefined ? q.order : idx + 1
              }))
              console.log('处理后的模板questions:', templateData.questions)
            } else {
              console.log('模板数据中没有questions字段，从回答数据构建')
              // 如果没有 questions 字段，尝试从回答数据构建
              if (responseData.answers && Array.isArray(responseData.answers)) {
                templateData = templateData || {}
                templateData.questions = responseData.answers.map((answer, index) => ({
                  question_id: answer.question_id || `q_${index + 1}`,
                  question_text: answer.question || answer.question_text || `问题${index + 1}`,
                  question_type: answer.question_type || 'single_choice',
                  answer_type: answer.answer_type || answer.question_type || 'single_choice',
                  options: answer.options || [],
                  is_required: true,
                  order: index + 1
                }))
                console.log('从回答数据构建的questions:', templateData.questions)
              }
            }
          }
        } catch (templateError) {
          console.error('获取模板数据失败:', templateError)
          // 模板接口失败时，尝试从回答数据构建
          if (responseData.answers && Array.isArray(responseData.answers)) {
            templateData = templateData || {}
            templateData.questions = responseData.answers.map((answer, index) => ({
              question_id: answer.question_id || `q_${index + 1}`,
              question_text: answer.question || answer.question_text || `问题${index + 1}`,
              question_type: answer.question_type || 'single_choice',
              answer_type: answer.answer_type || answer.question_type || 'single_choice',
              options: answer.options || [],
              is_required: true,
              order: index + 1
            }))
            console.log('模板接口失败，从回答数据构建的questions:', templateData.questions)
          }
        }
      } else {
        console.log('没有模板ID，从回答数据构建')
        // 没有模板ID时，尝试从回答数据构建
        if (responseData.answers && Array.isArray(responseData.answers)) {
          templateData = templateData || {}
          templateData.questions = responseData.answers.map((answer, index) => ({
            question_id: answer.question_id || `q_${index + 1}`,
            question_text: answer.question || answer.question_text || `问题${index + 1}`,
            question_type: answer.question_type || 'single_choice',
            answer_type: answer.answer_type || answer.question_type || 'single_choice',
            options: answer.options || [],
            is_required: true,
            order: index + 1
          }))
          console.log('没有模板ID，从回答数据构建的questions:', templateData.questions)
        }
      }
      
      // 兼容 answers 字段结构（量表和问卷都转成统一格式）
      let answers = responseData.answers
      
      // 处理不同格式的answers字段
      if (Array.isArray(answers)) {
        // 如果answers是数组格式，直接处理
        answers = answers.map((a, idx) => ({
          ...a,
          question_id: a.question_id || (templateData && templateData.questions && templateData.questions[idx] ? templateData.questions[idx].question_id : `q_${idx + 1}`),
          question: a.question || a.question_text || (templateData && templateData.questions && templateData.questions[idx] ? templateData.questions[idx].question_text : `问题${idx + 1}`),
          score: a.score !== undefined ? a.score : (a.value !== undefined && typeof a.value === 'number' ? a.value : undefined)
        }))
      } else if (answers && typeof answers === 'object') {
        // 如果answers是对象格式，转换为数组格式
        console.log('检测到对象格式的answers，正在转换为数组格式:', answers)
        const answersArray = []
        
        // 尝试从对象中提取答案数据
        if (answers.responses && Array.isArray(answers.responses)) {
          // 如果有responses字段且是数组
          answersArray.push(...answers.responses)
        } else {
          // 遍历对象的所有键值对
          Object.keys(answers).forEach((key, idx) => {
            const value = answers[key]
            if (value !== null && value !== undefined) {
              answersArray.push({
                question_id: key,
                question: templateData && templateData.questions && templateData.questions[idx] ? templateData.questions[idx].question_text : `问题${idx + 1}`,
                answer: value,
                value: value,
                score: typeof value === 'number' ? value : undefined
              })
            }
          })
        }
        
        // 格式化转换后的数组
        answers = answersArray.map((a, idx) => ({
          ...a,
          question_id: a.question_id || (templateData && templateData.questions && templateData.questions[idx] ? templateData.questions[idx].question_id : `q_${idx + 1}`),
          question: a.question || a.question_text || (templateData && templateData.questions && templateData.questions[idx] ? templateData.questions[idx].question_text : `问题${idx + 1}`),
          score: a.score !== undefined ? a.score : (a.value !== undefined && typeof a.value === 'number' ? a.value : undefined)
        }))
        
        console.log('转换后的answers数组:', answers)
      } else {
        // 如果answers格式不支持，创建空数组
        console.warn('不支持的answers格式:', answers)
        answers = []
      }
      
      console.log('最终的answers:', answers)
      console.log('最终的templateData:', templateData)
      
      currentItem.value = {
        ...item,
        answers,
        templateData
      }
      
      console.log('最终的currentItem:', currentItem.value)
      
      showAnswersDialog.value = true
    } else {
      console.log('未找到回答数据')
      ElMessage.warning('未找到原始回答数据')
    }
  } catch (error) {
    console.error('获取原始回答失败:', error)
    ElMessage.error('获取原始回答失败')
  }
}

// 查看报告
const viewReport = async (item) => {
  currentItem.value = item
  showReportDialog.value = true
  
  // 等待DOM更新后渲染雷达图
  await nextTick()
  if (shouldShowRadarChart(item.dimension_scores)) {
    renderRadarChart(item.dimension_scores)
  }
}

// 删除已完成项目
const deleteCompletedItem = async (item) => {
  try {
    await ElMessageBox.confirm('确定要删除这个结果记录吗？', '确认删除', {
      type: 'warning'
    })
    
    const response = item.type === 'questionnaire'
      ? await aggregatedApiService.deleteQuestionnaireResult(item.id)
      : await aggregatedApiService.deleteAssessmentResult(item.id)
      
    if (response.success) {
      ElMessage.success('删除成功')
      fetchCompletedData()
    } else {
      throw new Error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 获取结果等级类型
const getResultLevelType = (level) => {
  const levelMap = {
    '正常': 'success',
    '轻度': 'warning',
    '中度': 'danger',
    '重度': 'danger'
  }
  return levelMap[level] || 'info'
}

// Tab切换处理
const handleTabClick = (tab) => {
  if (tab.name === 'pending') {
    fetchPendingData()
  } else {
    fetchCompletedData()
  }
}

// 获取用户健康记录（兼容旧方法名）
const fetchUserHealthRecords = () => {
  if (activeTab.value === 'pending') {
    fetchPendingData()
  } else {
    fetchCompletedData()
  }
}

// 对话框状态
const showDialog = ref(false)
const showPreviewDialog = ref(false)
const showAnswersDialog = ref(false)
const showReportDialog = ref(false)
const previewFullscreen = ref(false)

// 雷达图相关
const radarChart = ref(null)
let radarChartInstance = null

const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString();
};

const getTypeLabel = (type) => {
  const typeMap = {
    'questionnaire': '调查问卷',
    'assessment': '评估量表'
  };
  return typeMap[type] || type;
};

// 获取问题文本
const getQuestionText = (answer, index) => {
  // 如果答案中有问题文本，直接使用
  if (answer.question) {
    return answer.question
  }
  
  // 如果有问题ID，优先尝试匹配
  if (answer.question_id && currentItem.value?.templateData?.questions) {
    const question = currentItem.value.templateData.questions.find(q => 
      q.question_id === answer.question_id || 
      q.id === answer.question_id ||
      q.question_id === answer.question_id.toString() ||
      q.id === answer.question_id.toString()
    )
    if (question) {
      return question.question_text || question.text || question.question
    }
  }
  
  // 如果有模板数据，从模板中按索引获取问题文本
  if (currentItem.value?.templateData?.questions && currentItem.value.templateData.questions[index]) {
    const question = currentItem.value.templateData.questions[index]
    return question.question_text || question.text || question.question
  }
  
  // 如果答案对象中有question_text字段
  if (answer.question_text) {
    return answer.question_text
  }
  
  // 默认显示
  return `问题 ${index + 1}`
}

// 获取格式化的选项数据
const getFormattedOptions = (question) => {
  console.log('=== getFormattedOptions 调试 ===');
  console.log('输入的question:', question);
  
  if (!question) {
    console.log('question为空，返回空数组');
    return []
  }
  
  // 检查不同的选项格式
  let options = []
  
  console.log('question.options类型:', typeof question.options);
  console.log('question.options值:', question.options);
  console.log('question.options是否为数组:', Array.isArray(question.options));
  
  if (question.options?.choices) {
    console.log('使用question.options.choices格式');
    options = question.options.choices
  } else if (question.options && Array.isArray(question.options)) {
    console.log('使用question.options数组格式');
    options = question.options
  } else if (question.choices) {
    console.log('使用question.choices格式');
    options = question.choices
  } else if (question.options && typeof question.options === 'string') {
    console.log('尝试解析question.options字符串');
    // 如果options是字符串，尝试解析JSON
    try {
      const parsed = JSON.parse(question.options)
      if (parsed.choices) {
        options = parsed.choices
      } else if (Array.isArray(parsed)) {
        options = parsed
      }
    } catch (e) {
      console.warn('Failed to parse options:', question.options)
      return []
    }
  } else {
    console.log('未匹配到任何选项格式');
  }
  
  console.log('最终返回的options:', options);
  return options || []
}

// 获取选项文本
const getOptionText = (option) => {
  if (typeof option === 'string') {
    return option
  }
  if (typeof option === 'object' && option !== null) {
    return option.text || option.label || option.value || JSON.stringify(option)
  }
  return String(option)
}

// 获取问题选项
const getQuestionOptions = (answer, index) => {
  console.log('=== getQuestionOptions 调试 ===');
  console.log('answer:', answer);
  console.log('index:', index);
  console.log('currentItem.value?.templateData?.questions:', currentItem.value?.templateData?.questions);
  
  // 如果有问题ID，优先尝试匹配
  if (answer.question_id && currentItem.value?.templateData?.questions) {
    console.log('尝试通过question_id匹配:', answer.question_id);
    const question = currentItem.value.templateData.questions.find(q => 
      q.question_id === answer.question_id || 
      q.id === answer.question_id ||
      q.question_id === answer.question_id.toString() ||
      q.id === answer.question_id.toString()
    )
    console.log('匹配到的问题:', question);
    if (question) {
      console.log('问题的原始options:', question.options);
      const options = getFormattedOptions(question)
      console.log('格式化后的options:', options);
      if (options && options.length > 0) {
        return options
      }
    }
  }
  
  // 从模板数据中按索引获取问题选项
  if (currentItem.value?.templateData?.questions && currentItem.value.templateData.questions[index]) {
    const question = currentItem.value.templateData.questions[index]
    const options = getFormattedOptions(question)
    if (options && options.length > 0) {
      return options
    }
  }
  
  // 如果答案对象中直接包含选项
  if (answer.options && Array.isArray(answer.options)) {
    return answer.options
  }
  
  return null
}

// 获取维度分值列表
const getDimensionScoresList = (dimensionScores) => {
  if (!dimensionScores) return []
  
  // 如果是对象格式
  if (typeof dimensionScores === 'object' && !Array.isArray(dimensionScores)) {
    return Object.entries(dimensionScores).map(([key, value]) => {
      if (typeof value === 'object' && value !== null) {
        return {
          name: value.name || key,
          score: value.score || value.weighted_score || 0,
          max_score: value.max_score,
          weight: value.weight
        }
      } else {
        return {
          name: key,
          score: value || 0
        }
      }
    })
  }
  
  // 如果是数组格式
  if (Array.isArray(dimensionScores)) {
    return dimensionScores
  }
  
  return []
}

// 判断是否应该显示雷达图
const shouldShowRadarChart = (dimensionScores) => {
  if (!dimensionScores) return false
  
  const scoresList = getDimensionScoresList(dimensionScores)
  return scoresList.length >= 3 // 至少3个维度才显示雷达图
}

// 渲染雷达图
const renderRadarChart = (dimensionScores) => {
  if (!radarChart.value || !dimensionScores) return
  
  // 销毁之前的图表实例
  if (radarChartInstance) {
    radarChartInstance.dispose()
  }
  
  const scoresList = getDimensionScoresList(dimensionScores)
  if (scoresList.length < 3) return
  
  // 准备雷达图数据
  const indicators = scoresList.map(item => ({
    name: item.name,
    max: item.max_score || Math.max(item.score * 1.2, 100) // 动态设置最大值
  }))
  
  const data = scoresList.map(item => item.score || 0)
  
  // 创建图表实例
  radarChartInstance = echarts.init(radarChart.value)
  
  const option = {
    title: {
      text: '维度分析',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        const dataIndex = params.dataIndex
        const score = scoresList[dataIndex]
        return `${score.name}: ${score.score}${score.max_score ? '/' + score.max_score : ''}分`
      }
    },
    radar: {
      indicator: indicators,
      radius: '70%',
      splitNumber: 5,
      axisName: {
        color: '#666',
        fontSize: 12
      },
      splitLine: {
        lineStyle: {
          color: '#e6e6e6'
        }
      },
      splitArea: {
        areaStyle: {
          color: ['rgba(114, 172, 209, 0.1)', 'rgba(114, 172, 209, 0.05)']
        }
      }
    },
    series: [{
      name: '维度得分',
      type: 'radar',
      data: [{
        value: data,
        name: '当前得分',
        areaStyle: {
          color: 'rgba(64, 158, 255, 0.3)'
        },
        lineStyle: {
          color: '#409EFF',
          width: 2
        },
        itemStyle: {
          color: '#409EFF'
        }
      }]
    }]
  }
  
  radarChartInstance.setOption(option)
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    if (radarChartInstance) {
      radarChartInstance.resize()
    }
  })
}
</script>

<style scoped>
.questionnaires-tab {
  padding: 20px;
}

.tab-content {
  padding: 20px 0;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.tab-header h4 {
  margin: 0;
  color: #303133;
}

.filter-container {
  display: flex;
  gap: 10px;
  align-items: center;
}

.item-info {
  padding: 20px 0;
}

.item-info p {
  margin: 10px 0;
  line-height: 1.6;
}

.answers-content {
  max-height: 500px;
  overflow-y: auto;
}

.answer-item {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #fafafa;
}

.answer-item .question {
  margin-bottom: 10px;
  color: #303133;
  font-size: 14px;
  line-height: 1.5;
}

.answer-item .answer {
  color: #606266;
}

.question-options-display {
  margin: 10px 0;
  padding: 10px;
  background-color: #f0f9ff;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.options-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
  font-weight: 500;
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.option-display {
  font-size: 13px;
  color: #606266;
  padding: 2px 0;
}

.option-display .option-score {
  color: #909399;
  font-size: 11px;
  margin-left: 8px;
}

.answer-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 5px;
  font-weight: 500;
}

.answer-value {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.4;
}

.answer-score {
  font-size: 12px;
  color: #409eff;
  font-weight: 500;
}

.report-content {
  padding: 20px 0;
}

.report-header {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.report-header h3 {
  margin: 0 0 15px 0;
  color: #303133;
}

.report-header p {
  margin: 8px 0;
  line-height: 1.6;
}

.report-summary,
.report-interpretation,
.report-conclusion,
.report-suggestions {
  margin: 25px 0;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.report-summary h4,
.report-interpretation h4,
.report-conclusion h4,
.report-suggestions h4 {
  margin: 0 0 15px 0;
  color: #409eff;
  font-size: 16px;
  font-weight: 600;
}

.summary-content,
.conclusion-content,
.suggestions-content {
  color: #303133;
  line-height: 1.8;
}

.suggestions-content ul {
  margin: 0;
  padding-left: 20px;
}

.suggestions-content li {
  margin-bottom: 8px;
  color: #606266;
}

.report-interpretation p,
.report-suggestions p {
  margin: 0;
  line-height: 1.8;
  color: #606266;
}

.report-dimensions {
  margin: 25px 0;
}

.report-dimensions h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.dimensions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.dimension-item {
  padding: 15px;
  background-color: #f0f9ff;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.dimension-name {
  font-weight: 600;
  color: #303133;
}

.dimension-score {
  float: right;
  font-weight: 700;
  color: #409eff;
}

.complete-form {
  padding: 10px;
}

.description {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  color: #606266;
}

.question-item {
  margin-bottom: 20px;
}

.scale-answer {
  padding: 10px;
}

.scale-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
  color: #909399;
  font-size: 12px;
}

.form-actions {
  margin-top: 30px;
  text-align: right;
}

/* 预览样式 */
.template-preview {
  margin-top: 20px;
}

.questions-preview {
  max-height: 500px;
  overflow-y: auto;
}

.question-preview-item {
  margin-bottom: 25px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background-color: #fafafa;
}

.question-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.question-number {
  font-weight: bold;
  color: #409eff;
  min-width: 30px;
}

.question-text {
  flex: 1;
  font-weight: 500;
  color: #303133;
  line-height: 1.5;
}

.question-options {
  margin-left: 40px;
}

.option-item {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.option-score {
  color: #909399;
  font-size: 12px;
}

.question-scale {
  margin-left: 40px;
}

.scale-info {
  padding: 10px;
  background-color: #f0f9ff;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.scale-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

.scale-options {
  margin-top: 10px;
}

.scale-options .el-radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.scale-options .el-radio {
  margin-right: 0;
}

.question-text {
  margin-left: 40px;
}

.no-template-data {
  margin-top: 20px;
  text-align: center;
}

/* 原始回答对话框样式 */
.answer-basic-info {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.answer-basic-info p {
  margin: 8px 0;
  color: #606266;
}

.answer-basic-info strong {
  color: #303133;
  margin-right: 8px;
}

.no-answers-data {
  margin-top: 20px;
}

.debug-info {
  margin-top: 20px;
}

.debug-info pre {
  max-height: 300px;
  overflow: auto;
  font-size: 12px;
  line-height: 1.4;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.dialog-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.dialog-actions {
  display: flex;
  gap: 8px;
}

.dialog-actions .el-button {
  padding: 8px;
  min-width: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tab-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .filter-container {
    width: 100%;
    justify-content: flex-start;
  }
  
  .dimensions-grid {
    grid-template-columns: 1fr;
  }
}
</style>
