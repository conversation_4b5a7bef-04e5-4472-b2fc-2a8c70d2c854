# -*- coding: utf-8 -*-

import requests
import json
import time

# 配置
BACKEND_URL = "http://localhost:8006"
FRONTEND_URL = "http://localhost:8080"

# 测试用户
TEST_USER = "admin"
TEST_PASSWORD = "admin123"

def get_auth_token():
    """获取认证令牌"""
    login_url = f"{BACKEND_URL}/auth/login"
    login_data = {
        "username": TEST_USER,
        "password": TEST_PASSWORD
    }
    
    try:
        response = requests.post(login_url, data=login_data)
        response.raise_for_status()
        token_data = response.json()
        return token_data.get("access_token")
    except Exception as e:
        print(f"登录失败: {e}")
        return None

def test_questionnaire_api():
    """测试问卷API"""
    token = get_auth_token()
    if not token:
        print("无法获取认证令牌，测试终止")
        return
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 测试1: 获取SM_008用户的未完成问卷
    print("\n=== 测试1: 获取SM_008用户的未完成问卷 ===")
    pending_url = f"{BACKEND_URL}/api/v1/aggregated/users/SM_008/questionnaires?status=pending&include_assessments=true"
    try:
        response = requests.get(pending_url, headers=headers)
        response.raise_for_status()
        pending_data = response.json()
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"数据条数: {len(pending_data)}")
        print("数据内容:")
        print(json.dumps(pending_data, indent=2, ensure_ascii=False)[:500] + "..." if len(json.dumps(pending_data, indent=2, ensure_ascii=False)) > 500 else json.dumps(pending_data, indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"请求失败: {e}")
    
    # 测试2: 获取SM_008用户的已完成问卷
    print("\n=== 测试2: 获取SM_008用户的已完成问卷 ===")
    completed_url = f"{BACKEND_URL}/api/v1/aggregated/users/SM_008/questionnaires?status=completed&include_assessments=true"
    try:
        response = requests.get(completed_url, headers=headers)
        response.raise_for_status()
        completed_data = response.json()
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"数据条数: {len(completed_data)}")
        print("数据内容:")
        print(json.dumps(completed_data, indent=2, ensure_ascii=False)[:500] + "..." if len(json.dumps(completed_data, indent=2, ensure_ascii=False)) > 500 else json.dumps(completed_data, indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"请求失败: {e}")
    
    # 测试3: 获取问卷模板详情
    print("\n=== 测试3: 获取问卷模板详情 ===")
    # 假设从前面的响应中获取了一个问卷ID为6
    template_url = f"{BACKEND_URL}/api/v1/questionnaires/templates/6"
    try:
        response = requests.get(template_url, headers=headers)
        response.raise_for_status()
        template_data = response.json()
        print(f"状态码: {response.status_code}")
        print(f"问卷标题: {template_data.get('title')}")
        print(f"问题数量: {len(template_data.get('questions', []))}")
        print("问题示例:")
        if template_data.get('questions'):
            sample_question = template_data.get('questions')[0]
            print(json.dumps(sample_question, indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"请求失败: {e}")
    
    # 测试4: 测试其他用户的问卷数据
    print("\n=== 测试4: 测试其他用户的问卷数据 ===")
    other_user = "SM_001"  # 尝试另一个用户ID
    other_url = f"{BACKEND_URL}/api/v1/aggregated/users/{other_user}/questionnaires?include_assessments=true"
    try:
        response = requests.get(other_url, headers=headers)
        response.raise_for_status()
        other_data = response.json()
        print(f"用户 {other_user} 的问卷数据条数: {len(other_data)}")
    except Exception as e:
        print(f"请求失败: {e}")

    # 测试5: 检查前端API调用参数
    print("\n=== 测试5: 检查前端API调用参数 ===")
    print("前端调用的API URL格式:")
    print(f"未完成问卷: {BACKEND_URL}/api/v1/aggregated/users/{{customId}}/questionnaires?status=pending&include_assessments=true")
    print(f"已完成问卷: {BACKEND_URL}/api/v1/aggregated/users/{{customId}}/questionnaires?status=completed&include_assessments=true")
    print("\n前端传递的参数:")
    print("customId: 用户的custom_id (例如: SM_008)")
    print("status: pending 或 completed")
    print("include_assessments: true (包含评估数据)")

if __name__ == "__main__":
    test_questionnaire_api()