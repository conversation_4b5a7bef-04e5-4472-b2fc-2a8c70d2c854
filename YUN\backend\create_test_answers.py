#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为SM_008用户创建测试答案数据
"""

import sqlite3
import json

def create_test_answers():
    """创建测试答案数据"""
    
    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()
    
    try:
        print("=== 为SM_008创建抑郁自评量表测试答案 ===")
        
        # 创建SDS测试答案（20个问题，每个问题答案1-4分）
        sds_answers = {}
        
        # 抑郁情绪维度问题：sds_1, sds_2, sds_3, sds_6, sds_11, sds_12, sds_14, sds_18
        # 给这些问题较高的分数（表示抑郁程度较高）
        depressive_emotion_questions = ['sds_1', 'sds_2', 'sds_3', 'sds_6', 'sds_11', 'sds_12', 'sds_14', 'sds_18']
        for q_id in depressive_emotion_questions:
            sds_answers[q_id] = 3  # 中等程度的抑郁
        
        # 躯体症状维度问题：sds_4, sds_5, sds_7, sds_8, sds_9, sds_15, sds_16, sds_17
        somatic_symptoms_questions = ['sds_4', 'sds_5', 'sds_7', 'sds_8', 'sds_9', 'sds_15', 'sds_16', 'sds_17']
        for q_id in somatic_symptoms_questions:
            sds_answers[q_id] = 2  # 轻度躯体症状
        
        # 精神运动性症状维度问题：sds_10, sds_13, sds_19, sds_20
        psychomotor_symptoms_questions = ['sds_10', 'sds_13', 'sds_19', 'sds_20']
        for q_id in psychomotor_symptoms_questions:
            sds_answers[q_id] = 2  # 轻度精神运动性症状
        
        sds_answers_json = json.dumps(sds_answers, ensure_ascii=False)
        
        print(f"创建的SDS答案: {sds_answers}")
        print(f"答案数量: {len(sds_answers)}")
        
        # 更新抑郁自评量表的答案数据
        cursor.execute("""
            UPDATE assessment_results 
            SET raw_answers = ?
            WHERE custom_id = 'SM_008' AND template_id = (
                SELECT id FROM assessment_templates WHERE name = '抑郁自评量表'
            )
        """, (sds_answers_json,))
        
        # 同时更新assessment_responses表
        cursor.execute("""
            UPDATE assessment_responses 
            SET answers = ?
            WHERE custom_id = 'SM_008' AND assessment_id = (
                SELECT id FROM assessments WHERE template_id = (
                    SELECT id FROM assessment_templates WHERE name = '抑郁自评量表'
                )
            )
        """, (sds_answers_json,))
        
        conn.commit()
        
        print("\n=== 验证答案数据更新 ===")
        
        # 验证更新结果
        cursor.execute("""
            SELECT ar.id, ar.raw_answers, at.name
            FROM assessment_results ar
            JOIN assessment_templates at ON ar.template_id = at.id
            WHERE ar.custom_id = 'SM_008' AND at.name = '抑郁自评量表'
        """)
        
        result = cursor.fetchone()
        if result:
            result_id, raw_answers, template_name = result
            print(f"评估结果 {result_id} ({template_name}): {raw_answers}")
            
            # 验证答案解析
            if raw_answers:
                try:
                    parsed_answers = json.loads(raw_answers)
                    print(f"解析成功，答案数量: {len(parsed_answers)}")
                except Exception as e:
                    print(f"答案解析失败: {e}")
        else:
            print("未找到抑郁自评量表结果")
        
        print("\n=== 测试答案创建完成 ===")
        
    except Exception as e:
        print(f"创建测试答案失败: {e}")
        import traceback
        traceback.print_exc()
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    create_test_answers()