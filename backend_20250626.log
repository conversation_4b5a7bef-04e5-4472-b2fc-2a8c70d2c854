2025-06-26 09:20:21,530 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-26 09:20:21,538 - auth_service - INFO - 统一认证服务初始化完成
2025-06-26 09:20:21,691 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-26 09:20:21,696 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-26 10:18:28,455 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-26 10:18:28,462 - auth_service - INFO - 统一认证服务初始化完成
2025-06-26 10:18:28,543 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-26 10:18:28,545 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-26 21:13:59,575 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-26 21:13:59,581 - auth_service - INFO - 统一认证服务初始化完成
2025-06-26 21:13:59,670 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-26 21:13:59,672 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-26 21:14:00,602 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-06-26 21:14:00,603 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-06-26 21:14:00,747 - health_monitor - INFO - 健康监控器初始化完成
2025-06-26 21:14:00,752 - app.core.system_monitor - INFO - 已加载 16 个历史数据点
2025-06-26 21:14:00,756 - app.core.alert_detector - INFO - 已加载 6 个告警规则
2025-06-26 21:14:00,757 - app.core.alert_detector - INFO - 已加载 0 个当前告警
2025-06-26 21:14:00,758 - app.core.alert_detector - INFO - 已加载 3 个历史告警
2025-06-26 21:14:00,759 - app.core.alert_detector - INFO - 已加载 1 个通知渠道
2025-06-26 21:14:00,761 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-26 21:14:00,770 - alert_manager - INFO - 已初始化默认告警规则
2025-06-26 21:14:00,771 - alert_manager - INFO - 已初始化默认通知渠道
2025-06-26 21:14:00,772 - alert_manager - INFO - 告警管理器初始化完成
2025-06-26 21:14:01,278 - db_service - INFO - 数据库引擎和会话工厂创建成功
2025-06-26 21:14:01,279 - db_service - INFO - 数据库服务初始化完成
2025-06-26 21:14:01,285 - notification_service - INFO - 通知服务初始化完成
2025-06-26 21:14:01,286 - main - INFO - 错误处理模块导入成功
2025-06-26 21:14:01,337 - main - INFO - 监控模块导入成功
2025-06-26 21:14:01,338 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-06-26 21:14:08,555 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-26 21:14:08,560 - auth_service - INFO - 统一认证服务初始化完成
2025-06-26 21:14:08,661 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-26 21:14:08,663 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-26 21:14:09,587 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-06-26 21:14:09,589 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-06-26 21:14:09,743 - health_monitor - INFO - 健康监控器初始化完成
2025-06-26 21:14:09,746 - app.core.system_monitor - INFO - 已加载 16 个历史数据点
2025-06-26 21:14:09,749 - app.core.alert_detector - INFO - 已加载 6 个告警规则
2025-06-26 21:14:09,751 - app.core.alert_detector - INFO - 已加载 0 个当前告警
2025-06-26 21:14:09,752 - app.core.alert_detector - INFO - 已加载 3 个历史告警
2025-06-26 21:14:09,753 - app.core.alert_detector - INFO - 已加载 1 个通知渠道
2025-06-26 21:14:09,756 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-26 21:14:09,765 - alert_manager - INFO - 已初始化默认告警规则
2025-06-26 21:14:09,768 - alert_manager - INFO - 已初始化默认通知渠道
2025-06-26 21:14:09,770 - alert_manager - INFO - 告警管理器初始化完成
2025-06-26 21:14:10,327 - db_service - INFO - 数据库引擎和会话工厂创建成功
2025-06-26 21:14:10,327 - db_service - INFO - 数据库服务初始化完成
2025-06-26 21:14:10,333 - notification_service - INFO - 通知服务初始化完成
2025-06-26 21:14:10,334 - main - INFO - 错误处理模块导入成功
2025-06-26 21:14:10,370 - main - INFO - 监控模块导入成功
2025-06-26 21:14:10,370 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-06-26 21:14:12,104 - main - INFO - 错误处理模块导入成功
2025-06-26 21:14:12,105 - main - INFO - 监控模块导入成功
2025-06-26 21:14:12,105 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-06-26 21:14:12,684 - main - INFO - 应用启动中...
2025-06-26 21:14:12,684 - error_handling - INFO - 错误处理已设置
2025-06-26 21:14:12,685 - main - INFO - 错误处理系统初始化完成
2025-06-26 21:14:12,686 - monitoring - INFO - 添加指标端点成功: /metrics
2025-06-26 21:14:12,690 - monitoring - INFO - 添加健康检查端点成功: /health
2025-06-26 21:14:12,692 - monitoring - INFO - 添加详细健康检查端点成功: /api/health/detailed
2025-06-26 21:14:12,694 - monitoring - INFO - 系统信息初始化完成: Markey, Windows-10-10.0.19045-SP0, Python 3.13.2, CPU核心数: 4
2025-06-26 21:14:12,699 - monitoring - INFO - 启动资源监控线程成功
2025-06-26 21:14:12,703 - monitoring - INFO - 监控系统初始化成功（不使用中间件）
2025-06-26 21:14:12,707 - monitoring - INFO - 监控系统初始化完成
2025-06-26 21:14:12,708 - main - INFO - 监控系统初始化完成
2025-06-26 21:14:12,711 - app.db.init_db - INFO - 所有模型导入成功
2025-06-26 21:14:12,725 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-06-26 21:14:12,745 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:14:12,746 - app.db.init_db - INFO - 所有模型导入成功
2025-06-26 21:14:12,747 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-06-26 21:14:12,747 - app.db.init_db - INFO - 正在运行数据库迁移...
2025-06-26 21:14:12,748 - app.db.init_db - INFO - 正在检查并更新数据库表结构...
2025-06-26 21:14:12,750 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-26 21:14:12,753 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alerts")
2025-06-26 21:14:12,756 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:12,768 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_rules")
2025-06-26 21:14:12,777 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:12,783 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_channels")
2025-06-26 21:14:12,786 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:12,795 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-06-26 21:14:12,824 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:12,827 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_records")
2025-06-26 21:14:12,828 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:12,833 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_overviews")
2025-06-26 21:14:12,834 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:12,836 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medical_records")
2025-06-26 21:14:12,840 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:12,845 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("inpatient_records")
2025-06-26 21:14:12,848 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:12,850 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("surgery_records")
2025-06-26 21:14:12,852 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:12,862 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_reports")
2025-06-26 21:14:12,866 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:12,871 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_report_items")
2025-06-26 21:14:12,873 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:12,875 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_reports")
2025-06-26 21:14:12,885 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:12,888 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("examination_reports")
2025-06-26 21:14:12,897 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:12,901 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("documents")
2025-06-26 21:14:12,910 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:12,935 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("other_records")
2025-06-26 21:14:12,944 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:12,961 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("operation_logs")
2025-06-26 21:14:12,991 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:12,994 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("follow_up_records")
2025-06-26 21:14:12,998 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:13,000 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_diaries")
2025-06-26 21:14:13,003 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:13,019 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("registration_records")
2025-06-26 21:14:13,035 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:13,051 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("prescription_records")
2025-06-26 21:14:13,059 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:13,065 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("laboratory_records")
2025-06-26 21:14:13,071 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:13,074 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_records")
2025-06-26 21:14:13,083 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:13,086 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medications")
2025-06-26 21:14:13,089 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:13,094 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medication_usages")
2025-06-26 21:14:13,098 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:13,100 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("role_applications")
2025-06-26 21:14:13,103 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:13,106 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessments")
2025-06-26 21:14:13,107 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:13,109 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_items")
2025-06-26 21:14:13,111 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:13,114 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_responses")
2025-06-26 21:14:13,115 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:13,117 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_templates")
2025-06-26 21:14:13,118 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:13,119 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_template_questions")
2025-06-26 21:14:13,122 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:13,123 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaires")
2025-06-26 21:14:13,125 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:13,129 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_items")
2025-06-26 21:14:13,135 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:13,140 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_responses")
2025-06-26 21:14:13,142 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:13,144 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_answers")
2025-06-26 21:14:13,145 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:13,146 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_templates")
2025-06-26 21:14:13,148 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:13,150 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_template_questions")
2025-06-26 21:14:13,151 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:13,153 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_distributions")
2025-06-26 21:14:13,156 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:13,157 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_distributions")
2025-06-26 21:14:13,158 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:13,160 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_results")
2025-06-26 21:14:13,161 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:13,162 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_results")
2025-06-26 21:14:13,170 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:13,175 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("report_templates")
2025-06-26 21:14:13,177 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:13,178 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("service_stats")
2025-06-26 21:14:13,179 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:14:13,181 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-26 21:14:13,181 - app.db.init_db - INFO - 数据库表结构检查和更新完成
2025-06-26 21:14:13,185 - app.db.init_db - INFO - 模型关系初始化完成
2025-06-26 21:14:13,186 - app.db.init_db - INFO - 模型关系设置完成
2025-06-26 21:14:13,186 - main - INFO - 数据库初始化完成（强制重建）
2025-06-26 21:14:13,189 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:14:13,192 - main - INFO - 数据库连接正常
2025-06-26 21:14:13,193 - main - INFO - 开始初始化模板数据
2025-06-26 21:14:13,194 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:14:13,393 - app.db.init_templates - INFO - 开始初始化评估量表模板...
2025-06-26 21:14:13,428 - app.db.init_templates - INFO - 成功初始化 5 个评估量表模板
2025-06-26 21:14:13,440 - app.db.init_templates - INFO - 开始初始化调查问卷模板...
2025-06-26 21:14:13,493 - app.db.init_templates - INFO - 成功初始化 5 个调查问卷模板
2025-06-26 21:14:13,494 - main - INFO - 模板数据初始化完成
2025-06-26 21:14:13,494 - main - INFO - 数据库表结构已经标准化，不需要迁移
2025-06-26 21:14:13,495 - main - INFO - 应用启动完成
2025-06-26 21:23:19,560 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-26 21:23:19,569 - auth_service - INFO - 统一认证服务初始化完成
2025-06-26 21:23:19,645 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-26 21:23:19,647 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-26 21:23:20,511 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-06-26 21:23:20,511 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-06-26 21:23:20,665 - health_monitor - INFO - 健康监控器初始化完成
2025-06-26 21:23:20,668 - app.core.system_monitor - INFO - 已加载 16 个历史数据点
2025-06-26 21:23:20,672 - app.core.alert_detector - INFO - 已加载 6 个告警规则
2025-06-26 21:23:20,674 - app.core.alert_detector - INFO - 已加载 0 个当前告警
2025-06-26 21:23:20,675 - app.core.alert_detector - INFO - 已加载 3 个历史告警
2025-06-26 21:23:20,676 - app.core.alert_detector - INFO - 已加载 1 个通知渠道
2025-06-26 21:23:20,678 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-26 21:23:20,685 - alert_manager - INFO - 已初始化默认告警规则
2025-06-26 21:23:20,687 - alert_manager - INFO - 已初始化默认通知渠道
2025-06-26 21:23:20,688 - alert_manager - INFO - 告警管理器初始化完成
2025-06-26 21:23:21,195 - db_service - INFO - 数据库引擎和会话工厂创建成功
2025-06-26 21:23:21,195 - db_service - INFO - 数据库服务初始化完成
2025-06-26 21:23:21,201 - notification_service - INFO - 通知服务初始化完成
2025-06-26 21:23:21,203 - main - INFO - 错误处理模块导入成功
2025-06-26 21:23:21,250 - main - INFO - 监控模块导入成功
2025-06-26 21:23:21,252 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-06-26 21:23:27,392 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-26 21:23:27,397 - auth_service - INFO - 统一认证服务初始化完成
2025-06-26 21:23:27,489 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-26 21:23:27,491 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-26 21:23:28,322 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-06-26 21:23:28,322 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-06-26 21:23:28,474 - health_monitor - INFO - 健康监控器初始化完成
2025-06-26 21:23:28,477 - app.core.system_monitor - INFO - 已加载 16 个历史数据点
2025-06-26 21:23:28,481 - app.core.alert_detector - INFO - 已加载 6 个告警规则
2025-06-26 21:23:28,482 - app.core.alert_detector - INFO - 已加载 0 个当前告警
2025-06-26 21:23:28,484 - app.core.alert_detector - INFO - 已加载 3 个历史告警
2025-06-26 21:23:28,485 - app.core.alert_detector - INFO - 已加载 1 个通知渠道
2025-06-26 21:23:28,486 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-26 21:23:28,494 - alert_manager - INFO - 已初始化默认告警规则
2025-06-26 21:23:28,495 - alert_manager - INFO - 已初始化默认通知渠道
2025-06-26 21:23:28,495 - alert_manager - INFO - 告警管理器初始化完成
2025-06-26 21:23:29,174 - db_service - INFO - 数据库引擎和会话工厂创建成功
2025-06-26 21:23:29,175 - db_service - INFO - 数据库服务初始化完成
2025-06-26 21:23:29,181 - notification_service - INFO - 通知服务初始化完成
2025-06-26 21:23:29,182 - main - INFO - 错误处理模块导入成功
2025-06-26 21:23:29,232 - main - INFO - 监控模块导入成功
2025-06-26 21:23:29,232 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-06-26 21:23:30,973 - main - INFO - 错误处理模块导入成功
2025-06-26 21:23:30,975 - main - INFO - 监控模块导入成功
2025-06-26 21:23:30,976 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-06-26 21:23:31,756 - main - INFO - 应用启动中...
2025-06-26 21:23:31,757 - error_handling - INFO - 错误处理已设置
2025-06-26 21:23:31,757 - main - INFO - 错误处理系统初始化完成
2025-06-26 21:23:31,758 - monitoring - INFO - 添加指标端点成功: /metrics
2025-06-26 21:23:31,758 - monitoring - INFO - 添加健康检查端点成功: /health
2025-06-26 21:23:31,759 - monitoring - INFO - 添加详细健康检查端点成功: /api/health/detailed
2025-06-26 21:23:31,760 - monitoring - INFO - 系统信息初始化完成: Markey, Windows-10-10.0.19045-SP0, Python 3.13.2, CPU核心数: 4
2025-06-26 21:23:31,769 - monitoring - INFO - 启动资源监控线程成功
2025-06-26 21:23:31,769 - monitoring - INFO - 监控系统初始化成功（不使用中间件）
2025-06-26 21:23:31,770 - monitoring - INFO - 监控系统初始化完成
2025-06-26 21:23:31,771 - main - INFO - 监控系统初始化完成
2025-06-26 21:23:31,772 - app.db.init_db - INFO - 所有模型导入成功
2025-06-26 21:23:31,773 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-06-26 21:23:31,776 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:23:31,776 - app.db.init_db - INFO - 所有模型导入成功
2025-06-26 21:23:31,776 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-06-26 21:23:31,778 - app.db.init_db - INFO - 正在运行数据库迁移...
2025-06-26 21:23:31,778 - app.db.init_db - INFO - 正在检查并更新数据库表结构...
2025-06-26 21:23:31,780 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-26 21:23:31,782 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alerts")
2025-06-26 21:23:31,783 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,787 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_rules")
2025-06-26 21:23:31,788 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,790 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_channels")
2025-06-26 21:23:31,791 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,793 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-06-26 21:23:31,794 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,796 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_records")
2025-06-26 21:23:31,797 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,798 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_overviews")
2025-06-26 21:23:31,800 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,803 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medical_records")
2025-06-26 21:23:31,804 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,806 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("inpatient_records")
2025-06-26 21:23:31,807 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,810 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("surgery_records")
2025-06-26 21:23:31,813 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,815 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_reports")
2025-06-26 21:23:31,819 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,820 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_report_items")
2025-06-26 21:23:31,821 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,822 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_reports")
2025-06-26 21:23:31,823 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,825 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("examination_reports")
2025-06-26 21:23:31,828 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,830 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("documents")
2025-06-26 21:23:31,832 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,834 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("other_records")
2025-06-26 21:23:31,836 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,838 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("operation_logs")
2025-06-26 21:23:31,839 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,841 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("follow_up_records")
2025-06-26 21:23:31,842 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,844 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_diaries")
2025-06-26 21:23:31,853 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,857 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("registration_records")
2025-06-26 21:23:31,859 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,864 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("prescription_records")
2025-06-26 21:23:31,866 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,867 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("laboratory_records")
2025-06-26 21:23:31,869 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,871 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_records")
2025-06-26 21:23:31,872 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,874 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medications")
2025-06-26 21:23:31,876 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,879 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medication_usages")
2025-06-26 21:23:31,881 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,882 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("role_applications")
2025-06-26 21:23:31,887 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,889 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessments")
2025-06-26 21:23:31,891 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,894 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_items")
2025-06-26 21:23:31,897 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,901 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_responses")
2025-06-26 21:23:31,902 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,903 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_templates")
2025-06-26 21:23:31,904 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,905 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_template_questions")
2025-06-26 21:23:31,908 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,910 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaires")
2025-06-26 21:23:31,912 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,915 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_items")
2025-06-26 21:23:31,916 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,917 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_responses")
2025-06-26 21:23:31,918 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,919 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_answers")
2025-06-26 21:23:31,920 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,921 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_templates")
2025-06-26 21:23:31,922 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,924 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_template_questions")
2025-06-26 21:23:31,925 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,926 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_distributions")
2025-06-26 21:23:31,928 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,929 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_distributions")
2025-06-26 21:23:31,930 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,932 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_results")
2025-06-26 21:23:31,933 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,935 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_results")
2025-06-26 21:23:31,936 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,937 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("report_templates")
2025-06-26 21:23:31,939 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,940 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("service_stats")
2025-06-26 21:23:31,941 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-26 21:23:31,945 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-26 21:23:31,947 - app.db.init_db - INFO - 数据库表结构检查和更新完成
2025-06-26 21:23:31,953 - app.db.init_db - INFO - 模型关系初始化完成
2025-06-26 21:23:31,954 - app.db.init_db - INFO - 模型关系设置完成
2025-06-26 21:23:31,954 - main - INFO - 数据库初始化完成（强制重建）
2025-06-26 21:23:31,955 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:23:31,956 - main - INFO - 数据库连接正常
2025-06-26 21:23:31,957 - main - INFO - 开始初始化模板数据
2025-06-26 21:23:31,958 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:23:32,133 - app.db.init_templates - INFO - 开始初始化评估量表模板...
2025-06-26 21:23:32,167 - app.db.init_templates - INFO - 成功初始化 5 个评估量表模板
2025-06-26 21:23:32,180 - app.db.init_templates - INFO - 开始初始化调查问卷模板...
2025-06-26 21:23:32,213 - app.db.init_templates - INFO - 成功初始化 5 个调查问卷模板
2025-06-26 21:23:32,217 - main - INFO - 模板数据初始化完成
2025-06-26 21:23:32,217 - main - INFO - 数据库表结构已经标准化，不需要迁移
2025-06-26 21:23:32,218 - main - INFO - 应用启动完成
2025-06-26 21:23:44,511 - main - INFO - 
--- 请求开始: GET /api/alerts/ ---
2025-06-26 21:23:44,518 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:23:44,520 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-26 21:23:44,521 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:23:44,549 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-26 21:23:44,560 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-26 21:23:44,564 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:44,572 - app.core.auth - INFO - 检测到X-User-ID头部: SM_001
2025-06-26 21:23:44,642 - app.core.auth - INFO - 使用X-User-ID认证: SM_001
2025-06-26 21:23:44,658 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-26 21:23:44,679 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-06-26 21:23:44,696 - sqlalchemy.engine.Engine - INFO - [generated in 0.01723s] ('SM_001', 1, 0)
2025-06-26 21:23:44,709 - app.core.auth - INFO - 通过custom_id找到用户: admin
2025-06-26 21:23:46,604 - main - INFO - 
--- 请求开始: GET /api/users ---
2025-06-26 21:23:46,619 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:23:46,608 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-26 21:23:46,646 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-26 21:23:46,650 - sqlalchemy.engine.Engine - INFO - SELECT 1
2025-06-26 21:23:46,670 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:23:46,737 - sqlalchemy.engine.Engine - INFO - [generated in 0.12953s] ()
2025-06-26 21:23:46,757 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-26 21:23:46,787 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-26 21:23:46,780 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-26 21:23:46,854 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:46,924 - app.core.auth - INFO - 检测到X-User-ID头部: SM_001
2025-06-26 21:23:46,925 - app.core.auth - INFO - 使用X-User-ID认证: SM_001
2025-06-26 21:23:46,936 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-26 21:23:46,939 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-06-26 21:23:46,965 - sqlalchemy.engine.Engine - INFO - [cached since 2.286s ago] ('SM_001', 1, 0)
2025-06-26 21:23:46,982 - app.core.auth - INFO - 通过custom_id找到用户: admin
2025-06-26 21:23:46,989 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.username = ?
 LIMIT ? OFFSET ?
2025-06-26 21:23:46,989 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-26 21:23:47,074 - sqlalchemy.engine.Engine - INFO - [generated in 0.08510s] ('admin', 1, 0)
2025-06-26 21:23:47,109 - main - INFO - --- 请求结束: 200 ---

2025-06-26 21:23:47,123 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-26 21:23:47,155 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-26 21:23:47,184 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.username = ?
 LIMIT ? OFFSET ?
2025-06-26 21:23:47,216 - sqlalchemy.engine.Engine - INFO - [cached since 0.227s ago] ('markey001', 1, 0)
2025-06-26 21:23:47,218 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.username = ?
 LIMIT ? OFFSET ?
2025-06-26 21:23:47,255 - sqlalchemy.engine.Engine - INFO - [cached since 0.2665s ago] ('markey', 1, 0)
2025-06-26 21:23:47,262 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-26 21:23:47,276 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-26 21:23:47,283 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.id = ?
2025-06-26 21:23:47,306 - sqlalchemy.engine.Engine - INFO - [generated in 0.02318s] (1,)
2025-06-26 21:23:47,375 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users
 LIMIT ? OFFSET ?
2025-06-26 21:23:47,406 - sqlalchemy.engine.Engine - INFO - [generated in 0.03208s] (10, 0)
2025-06-26 21:23:47,458 - sqlalchemy.engine.Engine - INFO - SELECT count(*) AS count_1 
FROM (SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users) AS anon_1
2025-06-26 21:23:47,540 - sqlalchemy.engine.Engine - INFO - [generated in 0.08227s] ()
2025-06-26 21:23:47,606 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-26 21:23:47,608 - main - INFO - --- 请求结束: 200 ---

2025-06-26 21:23:49,817 - main - INFO - 
--- 请求开始: GET /api/users ---
2025-06-26 21:23:49,823 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:23:49,824 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-26 21:23:49,836 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:23:49,842 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-26 21:23:49,870 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-26 21:23:49,891 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:49,913 - app.core.auth - INFO - 检测到X-User-ID头部: SM_001
2025-06-26 21:23:49,919 - app.core.auth - INFO - 使用X-User-ID认证: SM_001
2025-06-26 21:23:49,923 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-26 21:23:49,955 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-06-26 21:23:50,003 - sqlalchemy.engine.Engine - INFO - [cached since 5.324s ago] ('SM_001', 1, 0)
2025-06-26 21:23:50,080 - app.core.auth - INFO - 通过custom_id找到用户: admin
2025-06-26 21:23:50,125 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.username = ?
 LIMIT ? OFFSET ?
2025-06-26 21:23:50,252 - sqlalchemy.engine.Engine - INFO - [cached since 3.263s ago] ('admin', 1, 0)
2025-06-26 21:23:50,286 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-26 21:23:50,289 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-26 21:23:50,296 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.username = ?
 LIMIT ? OFFSET ?
2025-06-26 21:23:50,319 - sqlalchemy.engine.Engine - INFO - [cached since 3.33s ago] ('markey001', 1, 0)
2025-06-26 21:23:50,330 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.username = ?
 LIMIT ? OFFSET ?
2025-06-26 21:23:50,353 - sqlalchemy.engine.Engine - INFO - [cached since 3.364s ago] ('markey', 1, 0)
2025-06-26 21:23:50,375 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-26 21:23:50,384 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-26 21:23:50,386 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.id = ?
2025-06-26 21:23:50,420 - sqlalchemy.engine.Engine - INFO - [cached since 3.137s ago] (1,)
2025-06-26 21:23:50,435 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users
 LIMIT ? OFFSET ?
2025-06-26 21:23:50,473 - sqlalchemy.engine.Engine - INFO - [cached since 3.098s ago] (10, 0)
2025-06-26 21:23:50,476 - sqlalchemy.engine.Engine - INFO - SELECT count(*) AS count_1 
FROM (SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users) AS anon_1
2025-06-26 21:23:50,504 - sqlalchemy.engine.Engine - INFO - [cached since 3.046s ago] ()
2025-06-26 21:23:50,512 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-26 21:23:50,521 - main - INFO - --- 请求结束: 200 ---

2025-06-26 21:23:53,178 - main - INFO - 
--- 请求开始: GET /api/dashboard/stats/SM_008 ---
2025-06-26 21:23:53,191 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:23:53,192 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-26 21:23:53,201 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:23:53,222 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-26 21:23:53,224 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-26 21:23:53,225 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:53,251 - app.core.auth - INFO - 检测到X-User-ID头部: SM_001
2025-06-26 21:23:53,254 - app.core.auth - INFO - 使用X-User-ID认证: SM_001
2025-06-26 21:23:53,256 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-26 21:23:53,258 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-06-26 21:23:53,273 - sqlalchemy.engine.Engine - INFO - [cached since 8.594s ago] ('SM_001', 1, 0)
2025-06-26 21:23:53,277 - app.core.auth - INFO - 通过custom_id找到用户: admin
2025-06-26 21:23:53,286 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-06-26 21:23:53,342 - sqlalchemy.engine.Engine - INFO - [cached since 8.663s ago] ('SM_008', 1, 0)
2025-06-26 21:23:53,354 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-26 21:23:53,392 - main - INFO - --- 请求结束: 200 ---

2025-06-26 21:23:53,486 - main - INFO - 
--- 请求开始: GET /api/user-health-records/SM_008 ---
2025-06-26 21:23:53,493 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:23:53,505 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-26 21:23:53,519 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:23:53,521 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-26 21:23:53,523 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-26 21:23:53,523 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:53,529 - main - INFO - 
--- 请求开始: GET /api/user-health-records/SM_008 ---
2025-06-26 21:23:53,539 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:23:53,540 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-26 21:23:53,545 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:23:53,557 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-26 21:23:53,573 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-26 21:23:53,575 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:53,581 - main - INFO - 
--- 请求开始: GET /api/user-health-records/SM_008 ---
2025-06-26 21:23:53,588 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:23:53,589 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-26 21:23:53,590 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:23:53,591 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-26 21:23:53,608 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-26 21:23:53,619 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:53,622 - main - INFO - 
--- 请求开始: GET /api/user-health-records/SM_008 ---
2025-06-26 21:23:53,625 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:23:53,636 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-26 21:23:53,642 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:23:53,651 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-26 21:23:53,653 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-26 21:23:53,654 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:53,662 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-06-26 21:23:53,662 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-06-26 21:23:53,667 - app.core.db_connection - INFO - 数据库连接已创建
2025-06-26 21:23:53,672 - app.core.db_connection - INFO - 数据库连接已创建
2025-06-26 21:23:53,672 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:23:53,673 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-06-26 21:23:53,674 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:23:53,675 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-06-26 21:23:53,681 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-06-26 21:23:53,683 - app.core.db_connection - INFO - 数据库连接已创建
2025-06-26 21:23:53,685 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-06-26 21:23:53,687 - auth - INFO - 数据库连接有效
2025-06-26 21:23:53,693 - auth - INFO - 检测到X-User-ID头部: SM_001
2025-06-26 21:23:53,690 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:23:53,688 - app.core.db_connection - INFO - 数据库连接已创建
2025-06-26 21:23:53,711 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:23:53,713 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-06-26 21:23:53,695 - auth - INFO - 检测到Authorization头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:23:53,700 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-06-26 21:23:53,722 - auth - INFO - 请求路径: /api/user-health-records/SM_008
2025-06-26 21:23:53,725 - auth - INFO - 请求方法: GET
2025-06-26 21:23:53,734 - auth - INFO - 客户端IP: 127.0.0.1
2025-06-26 21:23:53,739 - auth - INFO - 请求来源: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-06-26 21:23:53,747 - auth - INFO - 客户端类型: 前端
2025-06-26 21:23:53,748 - auth - INFO - 尝试使用JWT令牌认证
2025-06-26 21:23:53,798 - auth - INFO - JWT认证成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:53,799 - auth - INFO - 尝试使用X-User-ID认证: SM_001
2025-06-26 21:23:53,806 - auth - INFO - X-User-ID认证成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:53,807 - auth - INFO - JWT认证成功，返回用户: admin
2025-06-26 21:23:53,818 - auth - INFO - 数据库连接有效
2025-06-26 21:23:53,822 - auth - INFO - 检测到X-User-ID头部: SM_001
2025-06-26 21:23:53,823 - auth - INFO - 检测到Authorization头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:23:53,829 - auth - INFO - 请求路径: /api/user-health-records/SM_008
2025-06-26 21:23:53,855 - auth - INFO - 请求方法: GET
2025-06-26 21:23:53,861 - auth - INFO - 客户端IP: 127.0.0.1
2025-06-26 21:23:53,867 - auth - INFO - 请求来源: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-06-26 21:23:53,873 - auth - INFO - 客户端类型: 前端
2025-06-26 21:23:53,874 - auth - INFO - 尝试使用JWT令牌认证
2025-06-26 21:23:53,909 - auth - INFO - JWT认证成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:53,917 - auth - INFO - 尝试使用X-User-ID认证: SM_001
2025-06-26 21:23:53,948 - auth - INFO - X-User-ID认证成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:53,949 - auth - INFO - JWT认证成功，返回用户: admin
2025-06-26 21:23:53,951 - auth - INFO - 数据库连接有效
2025-06-26 21:23:53,959 - auth - INFO - 检测到X-User-ID头部: SM_001
2025-06-26 21:23:53,965 - auth - INFO - 检测到Authorization头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:23:53,967 - auth - INFO - 请求路径: /api/user-health-records/SM_008
2025-06-26 21:23:53,970 - auth - INFO - 请求方法: GET
2025-06-26 21:23:53,976 - auth - INFO - 客户端IP: 127.0.0.1
2025-06-26 21:23:53,990 - auth - INFO - 请求来源: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-06-26 21:23:53,994 - auth - INFO - 客户端类型: 前端
2025-06-26 21:23:53,999 - auth - INFO - 尝试使用JWT令牌认证
2025-06-26 21:23:54,018 - auth - INFO - JWT认证成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:54,019 - auth - INFO - 尝试使用X-User-ID认证: SM_001
2025-06-26 21:23:54,035 - auth - INFO - X-User-ID认证成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:54,040 - auth - INFO - JWT认证成功，返回用户: admin
2025-06-26 21:23:54,041 - auth - INFO - 数据库连接有效
2025-06-26 21:23:54,042 - auth - INFO - 检测到X-User-ID头部: SM_001
2025-06-26 21:23:54,044 - auth - INFO - 检测到Authorization头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:23:54,071 - auth - INFO - 请求路径: /api/user-health-records/SM_008
2025-06-26 21:23:54,073 - auth - INFO - 请求方法: GET
2025-06-26 21:23:54,073 - auth - INFO - 客户端IP: 127.0.0.1
2025-06-26 21:23:54,074 - auth - INFO - 请求来源: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-06-26 21:23:54,076 - auth - INFO - 客户端类型: 前端
2025-06-26 21:23:54,082 - auth - INFO - 尝试使用JWT令牌认证
2025-06-26 21:23:54,106 - auth - INFO - JWT认证成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:54,114 - auth - INFO - 尝试使用X-User-ID认证: SM_001
2025-06-26 21:23:54,150 - auth - INFO - X-User-ID认证成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:54,152 - auth - INFO - JWT认证成功，返回用户: admin
2025-06-26 21:23:54,163 - main - INFO - 
--- 请求开始: GET /api/dashboard/weight-trend/SM_008 ---
2025-06-26 21:23:54,167 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:23:54,168 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-26 21:23:54,169 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:23:54,170 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-26 21:23:54,172 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-26 21:23:54,172 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:54,174 - main - INFO - --- 请求结束: 422 ---

2025-06-26 21:23:54,183 - main - INFO - --- 请求结束: 422 ---

2025-06-26 21:23:54,190 - main - INFO - --- 请求结束: 422 ---

2025-06-26 21:23:54,195 - main - INFO - --- 请求结束: 422 ---

2025-06-26 21:23:54,255 - app.core.auth - INFO - 检测到X-User-ID头部: SM_001
2025-06-26 21:23:54,263 - app.core.auth - INFO - 使用X-User-ID认证: SM_001
2025-06-26 21:23:54,281 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-26 21:23:54,336 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-06-26 21:23:54,370 - sqlalchemy.engine.Engine - INFO - [cached since 9.691s ago] ('SM_001', 1, 0)
2025-06-26 21:23:54,405 - app.core.auth - INFO - 通过custom_id找到用户: admin
2025-06-26 21:23:54,449 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-06-26 21:23:54,472 - sqlalchemy.engine.Engine - INFO - [cached since 9.792s ago] ('SM_008', 1, 0)
2025-06-26 21:23:54,509 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-26 21:23:54,534 - main - INFO - --- 请求结束: 200 ---

2025-06-26 21:23:54,607 - main - INFO - 
--- 请求开始: GET /api/user-health-records/SM_008 ---
2025-06-26 21:23:54,608 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:23:54,609 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-26 21:23:54,620 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:23:54,631 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-26 21:23:54,636 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-26 21:23:54,638 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:54,641 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-06-26 21:23:54,650 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:23:54,657 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-06-26 21:23:54,659 - auth - INFO - 数据库连接有效
2025-06-26 21:23:54,680 - auth - INFO - 检测到X-User-ID头部: SM_001
2025-06-26 21:23:54,690 - auth - INFO - 检测到Authorization头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:23:54,702 - auth - INFO - 请求路径: /api/user-health-records/SM_008
2025-06-26 21:23:54,714 - auth - INFO - 请求方法: GET
2025-06-26 21:23:54,723 - auth - INFO - 客户端IP: 127.0.0.1
2025-06-26 21:23:54,724 - auth - INFO - 请求来源: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-06-26 21:23:54,742 - auth - INFO - 客户端类型: 前端
2025-06-26 21:23:54,745 - auth - INFO - 尝试使用JWT令牌认证
2025-06-26 21:23:54,758 - auth - INFO - JWT认证成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:54,767 - auth - INFO - 尝试使用X-User-ID认证: SM_001
2025-06-26 21:23:54,791 - auth - INFO - X-User-ID认证成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:54,792 - auth - INFO - JWT认证成功，返回用户: admin
2025-06-26 21:23:54,807 - main - INFO - --- 请求结束: 422 ---

2025-06-26 21:23:54,918 - main - INFO - 
--- 请求开始: GET /api/dashboard/bp-trend/SM_008 ---
2025-06-26 21:23:54,932 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:23:54,932 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-26 21:23:54,933 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:23:54,944 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-26 21:23:54,949 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-26 21:23:54,950 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:54,952 - app.core.auth - INFO - 检测到X-User-ID头部: SM_001
2025-06-26 21:23:54,959 - app.core.auth - INFO - 使用X-User-ID认证: SM_001
2025-06-26 21:23:54,968 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-26 21:23:54,995 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-06-26 21:23:55,017 - sqlalchemy.engine.Engine - INFO - [cached since 10.34s ago] ('SM_001', 1, 0)
2025-06-26 21:23:55,025 - app.core.auth - INFO - 通过custom_id找到用户: admin
2025-06-26 21:23:55,045 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-06-26 21:23:55,070 - sqlalchemy.engine.Engine - INFO - [cached since 10.39s ago] ('SM_008', 1, 0)
2025-06-26 21:23:55,091 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-26 21:23:55,105 - main - INFO - --- 请求结束: 200 ---

2025-06-26 21:23:55,144 - main - INFO - 
--- 请求开始: GET /api/user-health-records/SM_008 ---
2025-06-26 21:23:55,151 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:23:55,154 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-26 21:23:55,155 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:23:55,166 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-26 21:23:55,170 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-26 21:23:55,173 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:55,179 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-06-26 21:23:55,182 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:23:55,188 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-06-26 21:23:55,205 - auth - INFO - 数据库连接有效
2025-06-26 21:23:55,221 - auth - INFO - 检测到X-User-ID头部: SM_001
2025-06-26 21:23:55,222 - auth - INFO - 检测到Authorization头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:23:55,223 - auth - INFO - 请求路径: /api/user-health-records/SM_008
2025-06-26 21:23:55,225 - auth - INFO - 请求方法: GET
2025-06-26 21:23:55,225 - auth - INFO - 客户端IP: 127.0.0.1
2025-06-26 21:23:55,235 - auth - INFO - 请求来源: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-06-26 21:23:55,236 - auth - INFO - 客户端类型: 前端
2025-06-26 21:23:55,236 - auth - INFO - 尝试使用JWT令牌认证
2025-06-26 21:23:55,274 - auth - INFO - JWT认证成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:55,281 - auth - INFO - 尝试使用X-User-ID认证: SM_001
2025-06-26 21:23:55,303 - auth - INFO - X-User-ID认证成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:55,342 - auth - INFO - JWT认证成功，返回用户: admin
2025-06-26 21:23:55,347 - main - INFO - --- 请求结束: 422 ---

2025-06-26 21:23:55,464 - main - INFO - 
--- 请求开始: GET /api/dashboard/exam-distribution/SM_008 ---
2025-06-26 21:23:55,464 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:23:55,465 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-26 21:23:55,466 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:23:55,467 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-26 21:23:55,469 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-26 21:23:55,470 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:55,473 - app.core.auth - INFO - 检测到X-User-ID头部: SM_001
2025-06-26 21:23:55,477 - app.core.auth - INFO - 使用X-User-ID认证: SM_001
2025-06-26 21:23:55,479 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-26 21:23:55,481 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-06-26 21:23:55,495 - sqlalchemy.engine.Engine - INFO - [cached since 10.82s ago] ('SM_001', 1, 0)
2025-06-26 21:23:55,500 - app.core.auth - INFO - 通过custom_id找到用户: admin
2025-06-26 21:23:55,504 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-06-26 21:23:55,518 - sqlalchemy.engine.Engine - INFO - [cached since 10.84s ago] ('SM_008', 1, 0)
2025-06-26 21:23:55,525 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-26 21:23:55,533 - main - INFO - --- 请求结束: 200 ---

2025-06-26 21:23:55,682 - main - INFO - 
--- 请求开始: GET /api/user-health-records/SM_008 ---
2025-06-26 21:23:55,682 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:23:55,683 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-26 21:23:55,683 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:23:55,684 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-26 21:23:55,686 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-26 21:23:55,690 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:55,694 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-06-26 21:23:55,695 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:23:55,696 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-06-26 21:23:55,699 - auth - INFO - 数据库连接有效
2025-06-26 21:23:55,700 - auth - INFO - 检测到X-User-ID头部: SM_001
2025-06-26 21:23:55,700 - auth - INFO - 检测到Authorization头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:23:55,703 - auth - INFO - 请求路径: /api/user-health-records/SM_008
2025-06-26 21:23:55,704 - auth - INFO - 请求方法: GET
2025-06-26 21:23:55,705 - auth - INFO - 客户端IP: 127.0.0.1
2025-06-26 21:23:55,705 - auth - INFO - 请求来源: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-06-26 21:23:55,707 - auth - INFO - 客户端类型: 前端
2025-06-26 21:23:55,707 - auth - INFO - 尝试使用JWT令牌认证
2025-06-26 21:23:55,717 - auth - INFO - JWT认证成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:55,718 - auth - INFO - 尝试使用X-User-ID认证: SM_001
2025-06-26 21:23:55,725 - auth - INFO - X-User-ID认证成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:55,725 - auth - INFO - JWT认证成功，返回用户: admin
2025-06-26 21:23:55,730 - main - INFO - --- 请求结束: 422 ---

2025-06-26 21:23:55,864 - main - INFO - 
--- 请求开始: GET /api/dashboard/health-index/SM_008 ---
2025-06-26 21:23:55,865 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:23:55,866 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-26 21:23:55,867 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:23:55,868 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-26 21:23:55,870 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-26 21:23:55,871 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:55,874 - app.core.auth - INFO - 检测到X-User-ID头部: SM_001
2025-06-26 21:23:55,875 - app.core.auth - INFO - 使用X-User-ID认证: SM_001
2025-06-26 21:23:55,878 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-26 21:23:55,880 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-06-26 21:23:55,891 - sqlalchemy.engine.Engine - INFO - [cached since 11.21s ago] ('SM_001', 1, 0)
2025-06-26 21:23:55,894 - app.core.auth - INFO - 通过custom_id找到用户: admin
2025-06-26 21:23:55,896 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-06-26 21:23:55,908 - sqlalchemy.engine.Engine - INFO - [cached since 11.23s ago] ('SM_008', 1, 0)
2025-06-26 21:23:55,911 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-26 21:23:55,916 - main - INFO - --- 请求结束: 200 ---

2025-06-26 21:23:56,048 - main - INFO - 
--- 请求开始: GET /api/user-health-records/SM_008 ---
2025-06-26 21:23:56,049 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:23:56,050 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-26 21:23:56,051 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:23:56,052 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-26 21:23:56,054 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-26 21:23:56,062 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:56,065 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-06-26 21:23:56,067 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:23:56,073 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-06-26 21:23:56,075 - auth - INFO - 数据库连接有效
2025-06-26 21:23:56,075 - auth - INFO - 检测到X-User-ID头部: SM_001
2025-06-26 21:23:56,078 - auth - INFO - 检测到Authorization头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:23:56,084 - auth - INFO - 请求路径: /api/user-health-records/SM_008
2025-06-26 21:23:56,085 - auth - INFO - 请求方法: GET
2025-06-26 21:23:56,085 - auth - INFO - 客户端IP: 127.0.0.1
2025-06-26 21:23:56,086 - auth - INFO - 请求来源: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-06-26 21:23:56,087 - auth - INFO - 客户端类型: 前端
2025-06-26 21:23:56,087 - auth - INFO - 尝试使用JWT令牌认证
2025-06-26 21:23:56,097 - auth - INFO - JWT认证成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:56,098 - auth - INFO - 尝试使用X-User-ID认证: SM_001
2025-06-26 21:23:56,104 - auth - INFO - X-User-ID认证成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:56,105 - auth - INFO - JWT认证成功，返回用户: admin
2025-06-26 21:23:56,107 - main - INFO - --- 请求结束: 422 ---

2025-06-26 21:23:56,230 - main - INFO - 
--- 请求开始: GET /api/dashboard/timeline/SM_008 ---
2025-06-26 21:23:56,231 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:23:56,231 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-26 21:23:56,232 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:23:56,233 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-26 21:23:56,235 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-26 21:23:56,235 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:56,238 - app.core.auth - INFO - 检测到X-User-ID头部: SM_001
2025-06-26 21:23:56,238 - app.core.auth - INFO - 使用X-User-ID认证: SM_001
2025-06-26 21:23:56,240 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-26 21:23:56,243 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-06-26 21:23:56,256 - sqlalchemy.engine.Engine - INFO - [cached since 11.58s ago] ('SM_001', 1, 0)
2025-06-26 21:23:56,260 - app.core.auth - INFO - 通过custom_id找到用户: admin
2025-06-26 21:23:56,264 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-06-26 21:23:56,272 - sqlalchemy.engine.Engine - INFO - [cached since 11.59s ago] ('SM_008', 1, 0)
2025-06-26 21:23:56,275 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-26 21:23:56,278 - main - INFO - --- 请求结束: 200 ---

2025-06-26 21:23:56,433 - main - INFO - 
--- 请求开始: GET /api/user-health-records/SM_008 ---
2025-06-26 21:23:56,434 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:23:56,435 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-26 21:23:56,436 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:23:56,437 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-26 21:23:56,439 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-26 21:23:56,447 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:56,454 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-06-26 21:23:56,470 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:23:56,473 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-06-26 21:23:56,489 - auth - INFO - 数据库连接有效
2025-06-26 21:23:56,490 - auth - INFO - 检测到X-User-ID头部: SM_001
2025-06-26 21:23:56,490 - auth - INFO - 检测到Authorization头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:23:56,522 - auth - INFO - 请求路径: /api/user-health-records/SM_008
2025-06-26 21:23:56,524 - auth - INFO - 请求方法: GET
2025-06-26 21:23:56,524 - auth - INFO - 客户端IP: 127.0.0.1
2025-06-26 21:23:56,525 - auth - INFO - 请求来源: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-06-26 21:23:56,527 - auth - INFO - 客户端类型: 前端
2025-06-26 21:23:56,542 - auth - INFO - 尝试使用JWT令牌认证
2025-06-26 21:23:56,570 - auth - INFO - JWT认证成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:56,572 - auth - INFO - 尝试使用X-User-ID认证: SM_001
2025-06-26 21:23:56,582 - auth - INFO - X-User-ID认证成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:56,584 - auth - INFO - JWT认证成功，返回用户: admin
2025-06-26 21:23:56,586 - main - INFO - --- 请求结束: 422 ---

2025-06-26 21:23:56,908 - main - INFO - 
--- 请求开始: GET /api/user-health-records/SM_008 ---
2025-06-26 21:23:56,908 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:23:56,912 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-26 21:23:56,913 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:23:56,914 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-26 21:23:56,916 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-26 21:23:56,918 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:56,922 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-06-26 21:23:56,924 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:23:56,925 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-06-26 21:23:56,933 - auth - INFO - 数据库连接有效
2025-06-26 21:23:56,936 - auth - INFO - 检测到X-User-ID头部: SM_001
2025-06-26 21:23:56,938 - auth - INFO - 检测到Authorization头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:23:56,941 - auth - INFO - 请求路径: /api/user-health-records/SM_008
2025-06-26 21:23:56,942 - auth - INFO - 请求方法: GET
2025-06-26 21:23:56,956 - auth - INFO - 客户端IP: 127.0.0.1
2025-06-26 21:23:56,956 - auth - INFO - 请求来源: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-06-26 21:23:56,957 - auth - INFO - 客户端类型: 前端
2025-06-26 21:23:56,958 - auth - INFO - 尝试使用JWT令牌认证
2025-06-26 21:23:57,081 - auth - INFO - JWT认证成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:57,158 - auth - INFO - 尝试使用X-User-ID认证: SM_001
2025-06-26 21:23:57,539 - auth - INFO - X-User-ID认证成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:23:57,551 - auth - INFO - JWT认证成功，返回用户: admin
2025-06-26 21:23:57,565 - main - INFO - --- 请求结束: 422 ---

2025-06-26 21:24:04,377 - main - INFO - 
--- 请求开始: GET /api/users ---
2025-06-26 21:24:04,378 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:24:04,391 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-26 21:24:04,413 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:24:04,439 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-26 21:24:04,443 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-26 21:24:04,444 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:24:04,450 - main - INFO - 
--- 请求开始: GET /api/documents ---
2025-06-26 21:24:04,452 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:24:04,455 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-26 21:24:04,456 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:24:04,458 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-26 21:24:04,460 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-26 21:24:04,461 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:24:04,467 - main - INFO - --- 请求结束: 307 ---

2025-06-26 21:24:04,472 - app.core.auth - INFO - 检测到X-User-ID头部: SM_001
2025-06-26 21:24:04,475 - app.core.auth - INFO - 使用X-User-ID认证: SM_001
2025-06-26 21:24:04,477 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-26 21:24:04,479 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-06-26 21:24:04,497 - sqlalchemy.engine.Engine - INFO - [cached since 19.82s ago] ('SM_001', 1, 0)
2025-06-26 21:24:04,503 - app.core.auth - INFO - 通过custom_id找到用户: admin
2025-06-26 21:24:04,505 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.username = ?
 LIMIT ? OFFSET ?
2025-06-26 21:24:04,518 - main - INFO - 
--- 请求开始: GET /api/documents/ ---
2025-06-26 21:24:04,525 - main - INFO - 请求没有认证头部
2025-06-26 21:24:04,524 - sqlalchemy.engine.Engine - INFO - [cached since 17.54s ago] ('admin', 1, 0)
2025-06-26 21:24:04,526 - main - INFO - 没有认证头部，设置用户为None
2025-06-26 21:24:04,530 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-26 21:24:04,538 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-06-26 21:24:04,535 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-26 21:24:04,540 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:24:04,541 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.username = ?
 LIMIT ? OFFSET ?
2025-06-26 21:24:04,546 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-06-26 21:24:04,553 - sqlalchemy.engine.Engine - INFO - [cached since 17.56s ago] ('markey001', 1, 0)
2025-06-26 21:24:04,556 - app.core.auth - INFO - 检测到X-User-ID头部: SM_001
2025-06-26 21:24:04,563 - app.core.auth - INFO - 使用X-User-ID认证: SM_001
2025-06-26 21:24:04,562 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.username = ?
 LIMIT ? OFFSET ?
2025-06-26 21:24:04,583 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-26 21:24:04,591 - sqlalchemy.engine.Engine - INFO - [cached since 17.6s ago] ('markey', 1, 0)
2025-06-26 21:24:04,593 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-06-26 21:24:04,608 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-26 21:24:04,613 - sqlalchemy.engine.Engine - INFO - [cached since 19.93s ago] ('SM_001', 1, 0)
2025-06-26 21:24:04,636 - app.core.auth - INFO - 通过custom_id找到用户: admin
2025-06-26 21:24:04,628 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-26 21:24:04,650 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.id = ?
2025-06-26 21:24:04,657 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-26 21:24:04,670 - sqlalchemy.engine.Engine - INFO - [cached since 17.39s ago] (1,)
2025-06-26 21:24:04,672 - main - INFO - --- 请求结束: 200 ---

2025-06-26 21:24:04,677 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users
 LIMIT ? OFFSET ?
2025-06-26 21:24:04,690 - sqlalchemy.engine.Engine - INFO - [cached since 17.32s ago] (100, 0)
2025-06-26 21:24:04,695 - sqlalchemy.engine.Engine - INFO - SELECT count(*) AS count_1 
FROM (SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users) AS anon_1
2025-06-26 21:24:04,710 - sqlalchemy.engine.Engine - INFO - [cached since 17.25s ago] ()
2025-06-26 21:24:04,717 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-26 21:24:04,721 - main - INFO - --- 请求结束: 200 ---

2025-06-26 21:24:15,256 - main - INFO - 
--- 请求开始: GET /api/templates/assessment-templates ---
2025-06-26 21:24:15,260 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:24:15,261 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-26 21:24:15,262 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:24:15,273 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-26 21:24:15,275 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-26 21:24:15,283 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:24:15,313 - app.core.auth - INFO - 检测到X-User-ID头部: SM_001
2025-06-26 21:24:15,370 - app.core.auth - INFO - 使用X-User-ID认证: SM_001
2025-06-26 21:24:15,439 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-26 21:24:15,466 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-06-26 21:24:15,516 - sqlalchemy.engine.Engine - INFO - [cached since 30.84s ago] ('SM_001', 1, 0)
2025-06-26 21:24:15,566 - app.core.auth - INFO - 通过custom_id找到用户: admin
2025-06-26 21:24:15,589 - main - INFO - 
--- 请求开始: GET /api/clinical-scales/standard-assessments ---
2025-06-26 21:24:15,607 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:24:15,630 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-26 21:24:15,647 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:24:15,671 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-26 21:24:15,689 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-26 21:24:15,704 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:24:15,723 - main - INFO - 
--- 请求开始: GET /api/assessments ---
2025-06-26 21:24:15,766 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:24:15,787 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-26 21:24:15,810 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:24:15,837 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-26 21:24:15,849 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-26 21:24:15,852 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:24:15,864 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-06-26 21:24:15,868 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:24:15,871 - app.core.auth - INFO - 检测到X-User-ID头部: SM_001
2025-06-26 21:24:15,872 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-06-26 21:24:15,884 - app.core.auth - INFO - 使用X-User-ID认证: SM_001
2025-06-26 21:24:15,906 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-26 21:24:15,938 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-06-26 21:24:15,973 - sqlalchemy.engine.Engine - INFO - [cached since 31.29s ago] ('SM_001', 1, 0)
2025-06-26 21:24:16,003 - app.core.auth - INFO - 通过custom_id找到用户: admin
2025-06-26 21:24:16,006 - app.core.auth - INFO - 检测到X-User-ID头部: SM_001
2025-06-26 21:24:16,016 - app.core.auth - INFO - 使用X-User-ID认证: SM_001
2025-06-26 21:24:16,015 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-26 21:24:16,018 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-26 21:24:16,022 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-06-26 21:24:16,037 - sqlalchemy.engine.Engine - INFO - [cached since 31.36s ago] ('SM_001', 1, 0)
2025-06-26 21:24:16,059 - app.core.auth - INFO - 通过custom_id找到用户: admin
2025-06-26 21:24:16,083 - main - INFO - --- 请求结束: 200 ---

2025-06-26 21:24:16,086 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-26 21:24:16,123 - main - INFO - --- 请求结束: 200 ---

2025-06-26 21:24:16,132 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-26 21:24:16,138 - main - INFO - --- 请求结束: 200 ---

2025-06-26 21:24:18,400 - main - INFO - 
--- 请求开始: GET /api/questionnaires ---
2025-06-26 21:24:18,401 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:24:18,401 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-26 21:24:18,402 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:24:18,403 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-26 21:24:18,405 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-26 21:24:18,405 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:24:18,407 - main - INFO - 
--- 请求开始: GET /api/clinical-scales/standard-questionnaires ---
2025-06-26 21:24:18,412 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:24:18,413 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-26 21:24:18,414 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:24:18,416 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-26 21:24:18,418 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-26 21:24:18,419 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:24:18,421 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-06-26 21:24:18,422 - app.core.auth - INFO - 检测到X-User-ID头部: SM_001
2025-06-26 21:24:18,423 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:24:18,429 - app.core.auth - INFO - 使用X-User-ID认证: SM_001
2025-06-26 21:24:18,431 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-06-26 21:24:18,435 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-26 21:24:18,437 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-06-26 21:24:18,455 - sqlalchemy.engine.Engine - INFO - [cached since 33.78s ago] ('SM_001', 1, 0)
2025-06-26 21:24:18,462 - app.core.auth - INFO - 通过custom_id找到用户: admin
2025-06-26 21:24:18,463 - app.core.auth - INFO - 检测到X-User-ID头部: SM_001
2025-06-26 21:24:18,465 - app.core.auth - INFO - 使用X-User-ID认证: SM_001
2025-06-26 21:24:18,467 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-26 21:24:18,471 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-06-26 21:24:18,482 - sqlalchemy.engine.Engine - INFO - [cached since 33.8s ago] ('SM_001', 1, 0)
2025-06-26 21:24:18,485 - app.core.auth - INFO - 通过custom_id找到用户: admin
2025-06-26 21:24:18,504 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-26 21:24:18,522 - main - INFO - --- 请求结束: 200 ---

2025-06-26 21:24:18,506 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-26 21:24:18,534 - main - INFO - --- 请求结束: 200 ---

2025-06-26 21:24:20,108 - main - INFO - 
--- 请求开始: GET /api/templates/assessment-templates ---
2025-06-26 21:24:20,109 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:24:20,110 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-26 21:24:20,116 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:24:20,120 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-26 21:24:20,124 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-26 21:24:20,128 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:24:20,135 - app.core.auth - INFO - 检测到X-User-ID头部: SM_001
2025-06-26 21:24:20,135 - app.core.auth - INFO - 使用X-User-ID认证: SM_001
2025-06-26 21:24:20,137 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-26 21:24:20,153 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-06-26 21:24:20,169 - sqlalchemy.engine.Engine - INFO - [cached since 35.49s ago] ('SM_001', 1, 0)
2025-06-26 21:24:20,172 - app.core.auth - INFO - 通过custom_id找到用户: admin
2025-06-26 21:24:20,181 - sqlalchemy.engine.Engine - INFO - SELECT assessment_templates.id AS assessment_templates_id, assessment_templates.template_key AS assessment_templates_template_key, assessment_templates.assessment_type AS assessment_templates_assessment_type, assessment_templates.sub_type AS assessment_templates_sub_type, assessment_templates.name AS assessment_templates_name, assessment_templates.version AS assessment_templates_version, assessment_templates.description AS assessment_templates_description, assessment_templates.instructions AS assessment_templates_instructions, assessment_templates.scoring_method AS assessment_templates_scoring_method, assessment_templates.max_score AS assessment_templates_max_score, assessment_templates.result_ranges AS assessment_templates_result_ranges, assessment_templates.dimensions AS assessment_templates_dimensions, assessment_templates.is_active AS assessment_templates_is_active, assessment_templates.status AS assessment_templates_status, assessment_templates.created_by AS assessment_templates_created_by, assessment_templates.created_at AS assessment_templates_created_at 
FROM assessment_templates 
WHERE assessment_templates.created_by = ?
2025-06-26 21:24:20,200 - sqlalchemy.engine.Engine - INFO - [generated in 0.01914s] (1,)
2025-06-26 21:24:20,218 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-26 21:24:20,219 - main - INFO - --- 请求结束: 200 ---

2025-06-26 21:24:20,410 - main - INFO - 
--- 请求开始: GET /api/templates/questionnaire-templates ---
2025-06-26 21:24:20,411 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-26 21:24:20,412 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-26 21:24:20,412 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-26 21:24:20,413 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-26 21:24:20,415 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-26 21:24:20,415 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-26 21:24:20,418 - app.core.auth - INFO - 检测到X-User-ID头部: SM_001
2025-06-26 21:24:20,419 - app.core.auth - INFO - 使用X-User-ID认证: SM_001
2025-06-26 21:24:20,420 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-26 21:24:20,426 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-06-26 21:24:20,435 - sqlalchemy.engine.Engine - INFO - [cached since 35.76s ago] ('SM_001', 1, 0)
2025-06-26 21:24:20,437 - app.core.auth - INFO - 通过custom_id找到用户: admin
2025-06-26 21:24:20,442 - sqlalchemy.engine.Engine - INFO - SELECT questionnaire_templates.id AS questionnaire_templates_id, questionnaire_templates.template_key AS questionnaire_templates_template_key, questionnaire_templates.name AS questionnaire_templates_name, questionnaire_templates.questionnaire_type AS questionnaire_templates_questionnaire_type, questionnaire_templates.version AS questionnaire_templates_version, questionnaire_templates.description AS questionnaire_templates_description, questionnaire_templates.instructions AS questionnaire_templates_instructions, questionnaire_templates.dimensions AS questionnaire_templates_dimensions, questionnaire_templates.is_active AS questionnaire_templates_is_active, questionnaire_templates.status AS questionnaire_templates_status, questionnaire_templates.created_at AS questionnaire_templates_created_at, questionnaire_templates.updated_at AS questionnaire_templates_updated_at, questionnaire_templates.created_by AS questionnaire_templates_created_by 
FROM questionnaire_templates
2025-06-26 21:24:20,451 - sqlalchemy.engine.Engine - INFO - [generated in 0.00897s] ()
2025-06-26 21:24:20,457 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-26 21:24:20,460 - main - INFO - --- 请求结束: 200 ---

2025-06-26 21:29:20,696 - alert_manager - WARNING - 触发告警: disk_free_space, 当前值: 0.0, 阈值: 1024
2025-06-26 21:29:28,500 - alert_manager - WARNING - 触发告警: disk_free_space, 当前值: 0.0, 阈值: 1024
