# -*- coding: utf-8 -*-

import requests
import json

# 配置
BACKEND_URL = "http://localhost:8006"

def test_api_routes():
    """测试API路由"""
    print("=== 测试API路由 ===")
    
    # 测试根路径
    try:
        response = requests.get(f"{BACKEND_URL}/")
        print(f"根路径状态码: {response.status_code}")
    except Exception as e:
        print(f"根路径请求失败: {e}")
    
    # 测试健康检查
    try:
        response = requests.get(f"{BACKEND_URL}/health")
        print(f"健康检查状态码: {response.status_code}")
        if response.status_code == 200:
            print(f"健康检查响应: {response.json()}")
    except Exception as e:
        print(f"健康检查请求失败: {e}")
    
    # 测试API文档
    try:
        response = requests.get(f"{BACKEND_URL}/docs")
        print(f"API文档状态码: {response.status_code}")
    except Exception as e:
        print(f"API文档请求失败: {e}")
    
    # 测试登录路径
    login_paths = [
        "/api/v1/auth/login",
        "/auth/login",
        "/api/auth/login",
        "/login"
    ]
    
    for path in login_paths:
        try:
            # 使用POST请求测试登录路径
            response = requests.post(f"{BACKEND_URL}{path}", data={
                "username": "admin",
                "password": "admin123"
            })
            print(f"登录路径 {path} 状态码: {response.status_code}")
            if response.status_code != 404:
                print(f"  响应内容: {response.text[:200]}...")
        except Exception as e:
            print(f"登录路径 {path} 请求失败: {e}")
    
    # 测试聚合API路径
    aggregated_paths = [
        "/api/v1/aggregated/users/SM_008/questionnaires",
        "/aggregated/users/SM_008/questionnaires",
        "/api/aggregated/users/SM_008/questionnaires"
    ]
    
    for path in aggregated_paths:
        try:
            response = requests.get(f"{BACKEND_URL}{path}")
            print(f"聚合API路径 {path} 状态码: {response.status_code}")
            if response.status_code == 401:
                print(f"  需要认证 (401)")
            elif response.status_code != 404:
                print(f"  响应内容: {response.text[:200]}...")
        except Exception as e:
            print(f"聚合API路径 {path} 请求失败: {e}")

if __name__ == "__main__":
    test_api_routes()