import asyncio
from playwright.async_api import async_playwright
import json
import time

class FrontendQuestionnaireTest:
    def __init__(self):
        self.frontend_url = "http://localhost:8080"
        self.backend_url = "http://localhost:8006"
        self.username = "admin"
        self.password = "admin123"
        self.test_user_id = "SM_008"
        
    async def run_tests(self):
        """运行所有前端问卷测试"""
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False, slow_mo=1000)
            context = await browser.new_context()
            page = await context.new_page()
            
            try:
                # 监听网络请求
                await self.setup_network_monitoring(page)
                
                # 测试1: 登录
                await self.test_login(page)
                
                # 测试2: 导航到问卷管理页面
                await self.test_navigate_to_questionnaires(page)
                
                # 测试3: 测试问卷列表加载
                await self.test_questionnaire_list(page)
                
                # 测试4: 测试用户问卷查询
                await self.test_user_questionnaire_search(page)
                
                # 测试5: 测试问卷详情查看
                await self.test_questionnaire_details(page)
                
                # 测试6: 测试API调用监控
                await self.test_api_calls_monitoring(page)
                
                print("\n=== 所有前端测试完成 ===")
                
            except Exception as e:
                print(f"测试过程中发生错误: {e}")
                await page.screenshot(path="error_screenshot.png")
                
            finally:
                await browser.close()
    
    async def setup_network_monitoring(self, page):
        """设置网络请求监控"""
        self.api_calls = []
        
        async def handle_request(request):
            if self.backend_url in request.url:
                self.api_calls.append({
                    'url': request.url,
                    'method': request.method,
                    'headers': dict(request.headers),
                    'timestamp': time.time()
                })
                print(f"API请求: {request.method} {request.url}")
        
        async def handle_response(response):
            if self.backend_url in response.url:
                try:
                    status = response.status
                    print(f"API响应: {response.status} {response.url}")
                    if status >= 400:
                        print(f"  错误响应: {status}")
                except Exception as e:
                    print(f"处理响应时出错: {e}")
        
        page.on("request", handle_request)
        page.on("response", handle_response)
    
    async def test_login(self, page):
        """测试登录功能"""
        print("\n=== 测试1: 登录功能 ===")
        
        await page.goto(self.frontend_url)
        await page.wait_for_load_state('networkidle')
        
        # 查找登录表单
        username_input = page.locator('input[type="text"], input[name="username"], input[placeholder*="用户名"], input[placeholder*="账号"]').first
        password_input = page.locator('input[type="password"], input[name="password"], input[placeholder*="密码"]').first
        login_button = page.locator('button:has-text("登录"), button:has-text("登陆"), button[type="submit"]').first
        
        if await username_input.count() > 0:
            await username_input.fill(self.username)
            await password_input.fill(self.password)
            await login_button.click()
            
            # 等待登录完成
            await page.wait_for_load_state('networkidle')
            await asyncio.sleep(2)
            
            # 检查是否登录成功
            current_url = page.url
            if "login" not in current_url.lower():
                print("✓ 登录成功")
            else:
                print("✗ 登录可能失败，仍在登录页面")
        else:
            print("✓ 可能已经登录或找不到登录表单")
    
    async def test_navigate_to_questionnaires(self, page):
        """测试导航到问卷管理页面"""
        print("\n=== 测试2: 导航到问卷管理 ===")
        
        # 尝试多种可能的导航方式
        navigation_selectors = [
            'a:has-text("问卷")',
            'a:has-text("调查")',
            'a:has-text("量表")',
            'a:has-text("评估")',
            '[href*="questionnaire"]',
            '[href*="survey"]',
            '.menu-item:has-text("问卷")',
            '.nav-item:has-text("问卷")'
        ]
        
        for selector in navigation_selectors:
            try:
                element = page.locator(selector).first
                if await element.count() > 0:
                    await element.click()
                    await page.wait_for_load_state('networkidle')
                    await asyncio.sleep(1)
                    print(f"✓ 通过选择器 {selector} 成功导航")
                    break
            except Exception as e:
                continue
        else:
            print("✗ 未找到问卷管理导航链接")
            # 尝试直接访问可能的问卷页面URL
            possible_urls = [
                f"{self.frontend_url}/questionnaires",
                f"{self.frontend_url}/questionnaire",
                f"{self.frontend_url}/surveys",
                f"{self.frontend_url}/assessments"
            ]
            
            for url in possible_urls:
                try:
                    await page.goto(url)
                    await page.wait_for_load_state('networkidle')
                    if page.url == url:
                        print(f"✓ 直接访问 {url} 成功")
                        break
                except Exception:
                    continue
    
    async def test_questionnaire_list(self, page):
        """测试问卷列表加载"""
        print("\n=== 测试3: 问卷列表加载 ===")
        
        await asyncio.sleep(2)
        
        # 查找问卷列表相关元素
        list_selectors = [
            'table',
            '.questionnaire-list',
            '.survey-list',
            '.data-table',
            '[class*="table"]',
            '[class*="list"]'
        ]
        
        for selector in list_selectors:
            try:
                element = page.locator(selector).first
                if await element.count() > 0:
                    print(f"✓ 找到列表元素: {selector}")
                    # 获取列表项数量
                    rows = page.locator(f"{selector} tr, {selector} .list-item, {selector} .item")
                    count = await rows.count()
                    print(f"  列表项数量: {count}")
                    break
            except Exception as e:
                continue
        else:
            print("✗ 未找到问卷列表")
    
    async def test_user_questionnaire_search(self, page):
        """测试用户问卷查询功能"""
        print("\n=== 测试4: 用户问卷查询 ===")
        
        # 查找搜索框或用户ID输入框
        search_selectors = [
            'input[placeholder*="用户"]',
            'input[placeholder*="ID"]',
            'input[placeholder*="搜索"]',
            'input[type="search"]',
            '.search-input',
            '.user-search'
        ]
        
        for selector in search_selectors:
            try:
                search_input = page.locator(selector).first
                if await search_input.count() > 0:
                    await search_input.fill(self.test_user_id)
                    await search_input.press('Enter')
                    await page.wait_for_load_state('networkidle')
                    await asyncio.sleep(2)
                    print(f"✓ 通过 {selector} 搜索用户 {self.test_user_id}")
                    break
            except Exception as e:
                continue
        else:
            print("✗ 未找到搜索功能")
    
    async def test_questionnaire_details(self, page):
        """测试问卷详情查看"""
        print("\n=== 测试5: 问卷详情查看 ===")
        
        # 查找详情按钮或链接
        detail_selectors = [
            'button:has-text("详情")',
            'button:has-text("查看")',
            'a:has-text("详情")',
            '.detail-btn',
            '.view-btn',
            '[class*="detail"]',
            '[class*="view"]'
        ]
        
        for selector in detail_selectors:
            try:
                detail_btn = page.locator(selector).first
                if await detail_btn.count() > 0:
                    await detail_btn.click()
                    await page.wait_for_load_state('networkidle')
                    await asyncio.sleep(2)
                    print(f"✓ 通过 {selector} 打开详情")
                    break
            except Exception as e:
                continue
        else:
            print("✗ 未找到详情查看功能")
    
    async def test_api_calls_monitoring(self, page):
        """测试API调用监控"""
        print("\n=== 测试6: API调用监控 ===")
        
        print(f"总共监控到 {len(self.api_calls)} 个API调用:")
        
        questionnaire_calls = []
        auth_calls = []
        other_calls = []
        
        for call in self.api_calls:
            url = call['url']
            if 'questionnaire' in url or 'aggregated' in url:
                questionnaire_calls.append(call)
            elif 'auth' in url or 'login' in url:
                auth_calls.append(call)
            else:
                other_calls.append(call)
        
        print(f"\n认证相关API调用 ({len(auth_calls)} 个):")
        for call in auth_calls:
            print(f"  {call['method']} {call['url']}")
        
        print(f"\n问卷相关API调用 ({len(questionnaire_calls)} 个):")
        for call in questionnaire_calls:
            print(f"  {call['method']} {call['url']}")
        
        print(f"\n其他API调用 ({len(other_calls)} 个):")
        for call in other_calls:
            print(f"  {call['method']} {call['url']}")
        
        # 检查是否有聚合API调用
        aggregated_calls = [call for call in self.api_calls if 'aggregated' in call['url']]
        if aggregated_calls:
            print(f"\n✓ 检测到聚合API调用 ({len(aggregated_calls)} 个)")
            for call in aggregated_calls:
                print(f"  {call['method']} {call['url']}")
        else:
            print("\n✗ 未检测到聚合API调用")

async def main():
    """主函数"""
    print("开始前端问卷自动化测试...")
    print(f"前端地址: http://localhost:8080")
    print(f"后端地址: http://localhost:8006")
    print(f"测试用户: admin")
    print(f"测试用户ID: SM_008")
    
    tester = FrontendQuestionnaireTest()
    await tester.run_tests()

if __name__ == "__main__":
    asyncio.run(main())