#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 medication_management_screen 模块是否能正常加载
"""

import sys
import os

# 添加项目路径到 Python 路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'mobile'))

def test_medication_screen_import():
    """测试 medication_management_screen 模块导入"""
    try:
        print("正在测试 medication_management_screen 模块导入...")
        
        # 测试模块导入
        from mobile.screens.medication_management_screen import MedicationManagementScreen
        print("✓ MedicationManagementScreen 类导入成功")
        
        # 测试类实例化
        screen = MedicationManagementScreen()
        print("✓ MedicationManagementScreen 实例化成功")
        
        # 检查必要的属性
        if hasattr(screen, 'current_medications'):
            print("✓ current_medications 属性存在")
        else:
            print("✗ current_medications 属性不存在")
            
        if hasattr(screen, 'current_tab'):
            print("✓ current_tab 属性存在")
        else:
            print("✗ current_tab 属性不存在")
            
        # 检查必要的方法
        if hasattr(screen, 'switch_tab'):
            print("✓ switch_tab 方法存在")
        else:
            print("✗ switch_tab 方法不存在")
            
        print("\n✓ medication_management_screen 模块测试通过")
        return True
        
    except Exception as e:
        print(f"✗ medication_management_screen 模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_screen_loader_config():
    """测试 screen_loader 中的配置"""
    try:
        print("\n正在测试 screen_loader 配置...")
        
        from mobile.utils.screen_loader import ScreenLoader
        loader = ScreenLoader()
        
        # 检查 medication_management_screen 是否在配置中
        if 'medication_management_screen' in loader._screen_configs:
            print("✓ medication_management_screen 在 screen_loader 配置中")
            config = loader._screen_configs['medication_management_screen']
            print(f"  模块路径: {config['module']}")
            print(f"  类名: {config['class']}")
            print(f"  显示名称: {config['name']}")
            return True
        else:
            print("✗ medication_management_screen 不在 screen_loader 配置中")
            return False
            
    except Exception as e:
        print(f"✗ screen_loader 配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试 medication_management_screen...\n")
    
    # 测试模块导入
    import_success = test_medication_screen_import()
    
    # 测试 screen_loader 配置
    config_success = test_screen_loader_config()
    
    print("\n=== 测试结果 ===")
    if import_success and config_success:
        print("✓ 所有测试通过，medication_management_screen 可以正常使用")
        sys.exit(0)
    else:
        print("✗ 测试失败，存在问题需要修复")
        sys.exit(1)