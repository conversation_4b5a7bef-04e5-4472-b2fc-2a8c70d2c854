import json
import logging
import os
from datetime import datetime
from .database import get_db_manager

# 配置日志
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('db_service')

class DBService:
    """数据库服务类，提供对数据库管理器的高级封装"""
    
    def __init__(self):
        """初始化数据库服务"""
        self.db_manager = get_db_manager()
        self.current_user_id = None
    
    def connect_user(self, user_id):
        """连接到指定用户的数据库"""
        if not user_id:
            logger.error("用户ID不能为空")
            return False
        
        result = self.db_manager.connect(user_id)
        if result:
            self.current_user_id = user_id
            # 确保基础表结构存在
            self._ensure_base_tables()
        
        return result
    
    def disconnect(self):
        """断开当前数据库连接"""
        self.db_manager.disconnect()
        self.current_user_id = None
    
    def _ensure_base_tables(self):
        """确保基础表结构存在"""
        # 导入DBInitializer和数据库迁移工具
        from .db_models import DBInitializer
        from .db_migration import migrate_current_user_database
        
        # 首先执行数据库迁移，确保现有表结构包含custom_id字段
        try:
            logger.info(f"开始为用户 {self.current_user_id} 执行数据库迁移")
            if not migrate_current_user_database(self.current_user_id):
                logger.warning(f"用户 {self.current_user_id} 数据库迁移失败，继续创建表")
            else:
                logger.info(f"用户 {self.current_user_id} 数据库迁移成功")
        except Exception as e:
            logger.error(f"数据库迁移时出错: {str(e)}，继续创建表")
        
        # 使用DBInitializer创建所有表
        if not DBInitializer.create_tables(self.current_user_id):
            logger.error(f"为用户 {self.current_user_id} 创建数据表失败")
            return False
        
        return True
    
    def create_table_if_not_exists(self, table_name, columns):
        """如果表不存在，创建表"""
        return self.db_manager.create_table(table_name, columns, self.current_user_id)
    
    def table_exists(self, table_name):
        """检查表是否存在"""
        return self.db_manager.table_exists(table_name, self.current_user_id)
    
    def insert(self, table_name, data):
        """向表中插入数据
        
        Args:
            table_name: 表名
            data: 字典形式的数据
            
        Returns:
            int: 插入的记录ID
        """
        if not data:
            logger.error("插入数据不能为空")
            return None
        
        # 添加创建和更新时间
        now = datetime.now().isoformat()
        if 'created_at' not in data:
            data['created_at'] = now
        if 'updated_at' not in data:
            data['updated_at'] = now
        
        # 构建插入语句
        columns = list(data.keys())
        placeholders = ['?' for _ in columns]
        values = [data[col] for col in columns]
        
        query = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"
        
        return self.db_manager.execute_insert(query, values, self.current_user_id)
    
    def update(self, table_name, data, condition):
        """更新表中的数据
        
        Args:
            table_name: 表名
            data: 字典形式的更新数据
            condition: 字典形式的条件
            
        Returns:
            int: 更新的记录数
        """
        if not data:
            logger.error("更新数据不能为空")
            return 0
        
        # 添加更新时间
        now = datetime.now().isoformat()
        if 'updated_at' not in data:
            data['updated_at'] = now
        
        # 构建更新语句
        set_clause = []
        values = []
        
        for key, value in data.items():
            set_clause.append(f"{key} = ?")
            values.append(value)
        
        # 构建条件语句
        where_clause = []
        for key, value in condition.items():
            where_clause.append(f"{key} = ?")
            values.append(value)
        
        query = f"UPDATE {table_name} SET {', '.join(set_clause)} WHERE {' AND '.join(where_clause)}"
        
        return self.db_manager.execute_update(query, values, self.current_user_id)
    
    def delete(self, table_name, condition):
        """从表中删除数据
        
        Args:
            table_name: 表名
            condition: 字典形式的条件
            
        Returns:
            int: 删除的记录数
        """
        # 构建条件语句
        where_clause = []
        values = []
        
        for key, value in condition.items():
            where_clause.append(f"{key} = ?")
            values.append(value)
        
        query = f"DELETE FROM {table_name} WHERE {' AND '.join(where_clause)}"
        
        return self.db_manager.execute_update(query, values, self.current_user_id)
    
    def select(self, table_name, columns="*", condition=None, order_by=None, limit=None):
        """从表中查询数据
        
        Args:
            table_name: 表名
            columns: 查询的列，默认为所有列
            condition: 字典形式的条件
            order_by: 排序子句
            limit: 限制返回的记录数
            
        Returns:
            list: 查询结果
        """
        # 构建查询语句
        if isinstance(columns, list):
            columns = ", ".join(columns)
        
        query = f"SELECT {columns} FROM {table_name}"
        
        values = []
        
        # 添加条件
        if condition:
            where_clause = []
            for key, value in condition.items():
                where_clause.append(f"{key} = ?")
                values.append(value)
            
            query += f" WHERE {' AND '.join(where_clause)}"
        
        # 添加排序
        if order_by:
            query += f" ORDER BY {order_by}"
        
        # 添加限制
        if limit:
            query += f" LIMIT {limit}"
        
        return self.db_manager.execute_query(query, values, self.current_user_id)
    
    def execute_custom_query(self, query, params=()):
        """执行自定义查询"""
        return self.db_manager.execute_query(query, params, self.current_user_id)
    
    def execute_custom_update(self, query, params=()):
        """执行自定义更新"""
        return self.db_manager.execute_update(query, params, self.current_user_id)
    
    def get_record_by_id(self, table_name, record_id):
        """根据ID获取单条记录
        
        Args:
            table_name: 表名
            record_id: 记录ID
            
        Returns:
            dict: 记录数据，如果不存在返回None
        """
        if not record_id:
            logger.error("记录ID不能为空")
            return None
        
        results = self.select(table_name, condition={"id": record_id}, limit=1)
        return results[0] if results else None
    
    def insert_record(self, table_name, data, sync=True):
        """插入记录（兼容性方法）
        
        Args:
            table_name: 表名
            data: 记录数据
            sync: 是否需要同步（暂时忽略）
            
        Returns:
            int: 插入的记录ID
        """
        return self.insert(table_name, data)
    
    def update_record(self, table_name, record_id, data, sync=True):
        """更新记录（兼容性方法）
        
        Args:
            table_name: 表名
            record_id: 记录ID
            data: 更新数据
            sync: 是否需要同步（暂时忽略）
            
        Returns:
            int: 更新的记录数
        """
        return self.update(table_name, data, {"id": record_id})
    
    def delete_record(self, table_name, record_id, sync=True):
        """删除记录（兼容性方法）
        
        Args:
            table_name: 表名
            record_id: 记录ID
            sync: 是否需要同步（暂时忽略）
            
        Returns:
            int: 删除的记录数
        """
        return self.delete(table_name, {"id": record_id})
    
    def backup_database(self, backup_dir=None):
        """备份当前用户的数据库"""
        if not self.current_user_id:
            logger.error("未连接到用户数据库，无法备份")
            return False
        
        return self.db_manager.backup_database(self.current_user_id, backup_dir)
    
    def get_records(self, table_name, condition=None, order_by=None, limit=None):
        """获取记录列表（兼容性方法）
        
        Args:
            table_name: 表名
            condition: 字典形式的条件
            order_by: 排序子句
            limit: 限制返回的记录数
            
        Returns:
            list: 记录列表
        """
        return self.select(table_name, condition=condition, order_by=order_by, limit=limit)
    
    def get_timestamp(self):
        """获取当前时间戳"""
        return datetime.now().isoformat()
    
    def get_database_path(self):
        """获取当前用户数据库的路径"""
        if not self.current_user_id:
            return None
        
        return self.db_manager.get_db_path(self.current_user_id)

# 单例模式
_db_service_instance = None

def get_db_service():
    """获取数据库服务实例（单例模式）"""
    global _db_service_instance
    if _db_service_instance is None:
        _db_service_instance = DBService()
    return _db_service_instance

# 为了兼容性，提供BaseDBService别名
BaseDBService = DBService