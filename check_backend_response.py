#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def check_backend_response():
    """检查后端实际返回的响应格式"""
    print("=== 检查后端响应格式 ===")
    
    base_url = "http://8.138.188.26/api/direct-login"
    
    # 测试数据
    username = "admin"
    password = "admin123"
    
    try:
        response = requests.post(
            base_url,
            data={"username": username, "password": password},
            headers={'Content-Type': 'application/x-www-form-urlencoded'},
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"原始响应: {response.text}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"\n解析后的JSON:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
                
                # 检查关键字段
                print(f"\n字段检查:")
                print(f"access_token: {result.get('access_token', 'NOT_FOUND')}")
                print(f"token_type: {result.get('token_type', 'NOT_FOUND')}")
                print(f"user: {result.get('user', 'NOT_FOUND')}")
                print(f"error: {result.get('error', 'NOT_FOUND')}")
                
                # 检查是否有access_token但没有token_type
                if result.get('access_token') and not result.get('token_type'):
                    print("\n⚠️  发现问题: 有access_token但缺少token_type字段")
                    print("移动端期望token_type为'bearer'")
                    
            except json.JSONDecodeError as e:
                print(f"JSON解析失败: {e}")
        else:
            print(f"请求失败，状态码: {response.status_code}")
            
    except Exception as e:
        print(f"请求异常: {e}")

if __name__ == "__main__":
    check_backend_response()