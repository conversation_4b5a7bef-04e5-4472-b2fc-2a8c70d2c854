#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复前端获取模板详情时缺少问题信息的问题
主要针对用户SM_008的简易精神状态检查量表数据显示问题
"""

import sqlite3
import json
import os
from datetime import datetime

def check_template_api_response():
    """检查模板API应该返回的数据结构"""
    print("=== 检查模板API应该返回的数据结构 ===")
    
    db_path = None
    for path in ['YUN/backend/app.db', 'app.db']:
        if os.path.exists(path):
            db_path = path
            break
    
    if not db_path:
        print("未找到数据库文件")
        return None
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取MMSE模板（ID=4）的完整信息
        print("\n1. 获取MMSE模板的完整信息...")
        cursor.execute("""
            SELECT id, template_key, name, description, assessment_type, 
                   scoring_method, max_score, result_ranges, dimensions
            FROM assessment_templates 
            WHERE id = 4
        """)
        template = cursor.fetchone()
        
        if not template:
            print("未找到MMSE模板")
            conn.close()
            return None
        
        template_data = {
            'id': template[0],
            'template_key': template[1],
            'name': template[2],
            'description': template[3],
            'assessment_type': template[4],
            'scoring_method': template[5],
            'max_score': template[6],
            'result_ranges': template[7],
            'dimensions': template[8]
        }
        
        print(f"模板基本信息: {template_data['name']} (ID: {template_data['id']})")
        
        # 获取模板的所有问题
        print("\n2. 获取模板问题...")
        cursor.execute("""
            SELECT question_id, question_text, question_type, options, 
                   scoring, is_required, "order"
            FROM assessment_template_questions 
            WHERE template_id = 4
            ORDER BY "order"
        """)
        questions = cursor.fetchall()
        
        questions_data = []
        for q in questions:
            question_data = {
                'question_id': q[0],
                'question_text': q[1],
                'question_type': q[2],
                'options': json.loads(q[3]) if q[3] else [],
                'scoring': json.loads(q[4]) if q[4] else {},
                'is_required': bool(q[5]),
                'order': q[6]
            }
            questions_data.append(question_data)
        
        template_data['questions'] = questions_data
        
        print(f"找到 {len(questions_data)} 个问题")
        if questions_data:
            first_q = questions_data[0]
            print(f"第一个问题: {first_q['question_id']} - {first_q['question_text'][:50]}...")
            print(f"问题类型: {first_q['question_type']}")
            print(f"选项数量: {len(first_q['options'])}")
        
        conn.close()
        return template_data
        
    except Exception as e:
        print(f"检查模板数据失败: {e}")
        return None

def create_template_api_test():
    """创建模板API测试脚本"""
    print("\n=== 创建模板API测试脚本 ===")
    
    test_script = '''
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试后端模板API是否正确返回问题数据
"""

import requests
import json

def test_backend_template_api():
    """测试后端模板API"""
    base_url = "http://localhost:8000"
    
    # 测试获取MMSE模板详情
    template_id = 4  # MMSE模板ID
    
    print(f"测试获取模板 {template_id} 的详情...")
    
    try:
        response = requests.get(f"{base_url}/api/templates/assessment-templates/{template_id}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应结构: {list(data.keys())}")
            
            if 'data' in data:
                template_data = data['data']
                print(f"模板字段: {list(template_data.keys())}")
                print(f"模板名称: {template_data.get('name')}")
                
                if 'questions' in template_data:
                    questions = template_data['questions']
                    print(f"问题数量: {len(questions)}")
                    
                    if questions:
                        first_q = questions[0]
                        print(f"第一个问题字段: {list(first_q.keys())}")
                        print(f"第一个问题文本: {first_q.get('question_text')}")
                        print(f"第一个问题选项: {first_q.get('options')}")
                        
                        # 保存完整响应用于分析
                        with open('template_api_response.json', 'w', encoding='utf-8') as f:
                            json.dump(data, f, ensure_ascii=False, indent=2)
                        print("完整响应已保存到 template_api_response.json")
                    else:
                        print("问题列表为空")
                else:
                    print("响应中不包含questions字段")
                    print(f"完整响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
            else:
                print(f"响应中不包含data字段: {data}")
        else:
            print(f"API调用失败: {response.text}")
            
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == "__main__":
    test_backend_template_api()
'''
    
    with open('test_backend_template_api.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("已创建 test_backend_template_api.py")

def create_frontend_fix():
    """创建前端修复方案"""
    print("\n=== 创建前端修复方案 ===")
    
    fix_suggestions = {
        'issue_analysis': {
            'problem': '前端获取模板详情时缺少问题信息',
            'root_cause': '可能的原因包括API调用错误、数据处理错误或后端返回数据不完整',
            'affected_component': 'QuestionnairesTab.vue中的viewOriginalAnswers函数'
        },
        'fix_steps': [
            {
                'step': 1,
                'description': '在前端添加调试日志',
                'code_location': 'QuestionnairesTab.vue viewOriginalAnswers函数',
                'modification': '在模板API调用前后添加console.log输出'
            },
            {
                'step': 2,
                'description': '验证模板ID的正确性',
                'code_location': 'QuestionnairesTab.vue viewOriginalAnswers函数',
                'modification': '确保使用正确的template_id进行API调用'
            },
            {
                'step': 3,
                'description': '检查API响应数据结构',
                'code_location': 'QuestionnairesTab.vue viewOriginalAnswers函数',
                'modification': '添加对API响应数据的详细检查和错误处理'
            },
            {
                'step': 4,
                'description': '确保问题数据的正确映射',
                'code_location': 'QuestionnairesTab.vue viewOriginalAnswers函数',
                'modification': '验证questions字段的存在和正确性'
            }
        ],
        'test_plan': [
            '使用浏览器开发者工具监控网络请求',
            '检查模板API的实际响应内容',
            '验证前端JavaScript控制台的调试输出',
            '测试用户SM_008的MMSE数据显示'
        ]
    }
    
    with open('frontend_fix_plan.json', 'w', encoding='utf-8') as f:
        json.dump(fix_suggestions, f, ensure_ascii=False, indent=2)
    
    print("已创建 frontend_fix_plan.json")

def create_enhanced_frontend_debug():
    """创建增强的前端调试版本"""
    print("\n=== 创建增强的前端调试版本 ===")
    
    debug_code = '''
// 在QuestionnairesTab.vue的viewOriginalAnswers函数中添加以下调试代码

const viewOriginalAnswers = async (item) => {
  try {
    console.log('=== 开始获取原始回答 ===');
    console.log('传入的item:', item);
    
    // 获取回答数据
    const endpoint = item.type === 'questionnaire'
      ? `/api/questionnaire-responses/user/${props.customId}`
      : `/api/assessment-responses/user/${props.customId}`
      
    console.log('回答数据API端点:', endpoint);
    
    const response = await axios.get(endpoint, {
      params: {
        [item.type === 'questionnaire' ? 'questionnaire_id' : 'assessment_id']: item.questionnaire_id || item.assessment_id
      }
    })
    
    console.log('回答数据响应:', response.data);
    
    if (response.data.data && response.data.data.length > 0) {
      const responseData = response.data.data[0]
      console.log('第一条回答数据:', responseData);
      
      // 获取模板信息以显示问题
      let templateData = null
      const templateId = item.template_id || (item.type === 'questionnaire' ? item.questionnaire_id : item.assessment_id)
      
      console.log('模板ID:', templateId);
      console.log('item.type:', item.type);
      
      if (templateId) {
        try {
          const templateEndpoint = item.type === 'questionnaire'
            ? `/api/templates/questionnaire-templates/${templateId}`
            : `/api/templates/assessment-templates/${templateId}`
          
          console.log('模板API端点:', templateEndpoint);
          
          const templateResponse = await axios.get(templateEndpoint)
          console.log('模板API响应状态:', templateResponse.status);
          console.log('模板API响应数据:', templateResponse.data);
          
          if (templateResponse.data.success || templateResponse.data.status === 'success') {
            templateData = templateResponse.data.data
            console.log('解析的模板数据:', templateData);
            
            // 检查questions字段
            if (templateData && templateData.questions) {
              console.log('模板包含questions字段，问题数量:', templateData.questions.length);
              console.log('前3个问题:', templateData.questions.slice(0, 3));
              
              // 确保问题数据完整，包含详细的问题文本和选项
              templateData.questions = templateData.questions.map(q => ({
                question_id: q.question_id,
                question_text: q.question_text,
                question_type: q.question_type,
                options: q.options || [],
                is_required: q.is_required,
                order: q.order
              }))
              
              console.log('处理后的问题数据:', templateData.questions.slice(0, 2));
            } else {
              console.warn('模板数据中不包含questions字段或questions为空');
              console.log('模板数据的所有字段:', Object.keys(templateData || {}));
            }
          } else {
            console.warn('模板API返回失败状态:', templateResponse.data);
          }
        } catch (templateError) {
          console.error('获取模板信息失败:', templateError);
          console.error('错误详情:', templateError.response?.data);
        }
      } else {
        console.warn('未找到有效的模板ID');
      }
      
      console.log('最终的templateData:', templateData);
      
      currentItem.value = {
        ...item,
        answers: responseData.answers,
        templateData: templateData
      }
      
      console.log('设置的currentItem:', currentItem.value);
      
      showAnswersDialog.value = true
    } else {
      console.warn('未找到原始回答数据');
      ElMessage.warning('未找到原始回答数据')
    }
  } catch (error) {
    console.error('获取原始回答失败:', error)
    console.error('错误详情:', error.response?.data);
    ElMessage.error('获取原始回答失败')
  }
}
'''
    
    with open('enhanced_frontend_debug.js', 'w', encoding='utf-8') as f:
        f.write(debug_code)
    
    print("已创建 enhanced_frontend_debug.js")
    print("请将此代码替换到 QuestionnairesTab.vue 中的 viewOriginalAnswers 函数")

def main():
    """主函数"""
    print("=== 修复模板问题信息缺失问题 ===")
    print(f"修复时间: {datetime.now()}")
    
    # 1. 检查模板API应该返回的数据结构
    template_data = check_template_api_response()
    
    # 2. 创建后端API测试脚本
    create_template_api_test()
    
    # 3. 创建前端修复方案
    create_frontend_fix()
    
    # 4. 创建增强的前端调试版本
    create_enhanced_frontend_debug()
    
    print("\n=== 修复方案总结 ===")
    print("1. 已分析数据库中的模板数据结构")
    print("2. 已创建后端API测试脚本: test_backend_template_api.py")
    print("3. 已创建前端修复计划: frontend_fix_plan.json")
    print("4. 已创建增强调试代码: enhanced_frontend_debug.js")
    
    print("\n下一步操作建议:")
    print("1. 运行 test_backend_template_api.py 测试后端API")
    print("2. 将 enhanced_frontend_debug.js 中的代码应用到前端")
    print("3. 使用浏览器开发者工具监控网络请求和控制台输出")
    print("4. 测试用户SM_008的MMSE数据显示")
    
    if template_data:
        print(f"\n模板数据验证: MMSE模板包含 {len(template_data['questions'])} 个问题")
        print("数据库中的模板数据是完整的，问题可能在API调用或前端处理环节")
    
    print("\n=== 修复完成 ===")

if __name__ == "__main__":
    main()