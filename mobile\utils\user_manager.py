"""用户管理模块
该模块提供用户账户管理功能，包括用户切换、用户信息管理等。
所有用户数据统一存储在user_data.json文件中。
"""

import os
import json
from datetime import datetime
import time
from typing import Dict

# 导入存储工具
from utils.storage import UserStorage

# 用户数据文件路径
MODULE_DIR = os.path.dirname(os.path.abspath(__file__))
APP_DIR = os.path.dirname(MODULE_DIR)
USER_DATA_FILE = os.path.join(APP_DIR, "user_data.json")

class UserAccount:
    """用户账户类，表示一个用户账户的数据结构"""

    def __init__(self, user_id=None, username=None, password=None, phone=None, full_name=None, role=None, id_number=None, gender=None, avatar=None, last_login=None, roles=None, custom_id=None):
        """初始化用户账户

        Args:
            user_id (str, optional): 用户ID（已弃用，仅用于兼容旧代码）
            username (str): 用户名
            password (str, optional): 密码
            phone (str, optional): 手机号
            full_name (str, optional): 真实姓名（替代旧的real_name）
            role (str, optional): 用户角色（替代旧的identity）
            id_number (str, optional): 身份证号
            gender (str, optional): 性别
            avatar (str, optional): 用户头像URL
            last_login (str, optional): 最后登录时间
            roles (list, optional): 用户角色列表
            custom_id (str, optional): 后端生成的带前缀的格式化ID（用户唯一标识）
        """
        # user_id 已弃用，仅用于兼容旧代码
        self.user_id = user_id
        self.username = username
        self.password = password
        self.phone = phone
        self.full_name = full_name or username  # 使用full_name替代real_name
        self.role = role or '个人用户'  # 使用role替代identity
        self.id_number = id_number
        self.gender = gender
        self.avatar = avatar
        self.last_login = last_login or datetime.now().isoformat()
        self.roles = roles or [self.role] if self.role else []
        self.custom_id = custom_id  # 用户唯一标识

        # 兼容旧版本的属性
        self._real_name = full_name or username
        self._identity = role or '个人用户'

        # 云端同步相关字段
        self.password_hash = None  # 密码哈希值，用于云端注册
        self.email = None  # 电子邮箱，用于云端注册
        self.cloud_user_id = None  # 云端用户ID
        self.sync_status = None  # 同步状态：None, "pending", "synced"
        self.last_sync = None  # 最后同步时间

    # 为兼容旧代码，添加属性访问器
    @property
    def real_name(self):
        return self.full_name

    @real_name.setter
    def real_name(self, value):
        self.full_name = value
        self._real_name = value

    @property
    def identity(self):
        return self.role

    @identity.setter
    def identity(self, value):
        self.role = value
        self._identity = value

    def to_dict(self):
        """转换为字典表示

        Returns:
            dict: 账户数据字典
        """
        data = {
            "username": self.username,
            "password": self.password,
            "full_name": self.full_name,  # 使用标准字段名
            "role": self.role,  # 使用标准字段名
            "roles": self.roles,
            "last_login": self.last_login
        }

        # 添加custom_id字段（如果有值）
        if hasattr(self, 'custom_id') and self.custom_id:
            data["custom_id"] = self.custom_id

        # 添加user_id字段（已弃用，仅用于兼容旧代码）
        if hasattr(self, 'user_id') and self.user_id:
            data["user_id"] = self.user_id

        # 添加兼容旧版本的字段
        data["real_name"] = self.full_name
        data["identity"] = self.role

        # 添加可选字段（如果有值）
        if self.phone:
            data["phone"] = self.phone
        if self.id_number:
            data["id_number"] = self.id_number
        if self.gender:
            data["gender"] = self.gender
        if self.avatar:
            data["avatar"] = self.avatar

        # 添加云端同步相关字段（如果有值）
        if self.password_hash:
            data["password_hash"] = self.password_hash
        if self.email:
            data["email"] = self.email
        if self.cloud_user_id:
            data["cloud_user_id"] = self.cloud_user_id
        if self.sync_status:
            data["sync_status"] = self.sync_status
        if self.last_sync:
            data["last_sync"] = self.last_sync

        return data

    @classmethod
    def from_dict(cls, data):
        """从字典创建用户账户

        Args:
            data (dict): 账户数据字典

        Returns:
            UserAccount: 用户账户对象
        """
        # 获取标准字段名或兼容旧字段名
        full_name = data.get("full_name") or data.get("real_name")
        role = data.get("role") or data.get("identity")

        # 创建基本账户对象
        account = cls(
            user_id=data.get("user_id"),
            username=data.get("username"),
            password=data.get("password"),
            phone=data.get("phone"),
            full_name=full_name,  # 使用标准字段名
            role=role,  # 使用标准字段名
            id_number=data.get("id_number"),
            gender=data.get("gender"),
            avatar=data.get("avatar"),
            last_login=data.get("last_login"),
            roles=data.get("roles", []),
            custom_id=data.get("custom_id")  # 添加custom_id字段
        )

        # 设置云端同步相关字段
        account.password_hash = data.get("password_hash")
        account.email = data.get("email")
        account.cloud_user_id = data.get("cloud_user_id")
        account.sync_status = data.get("sync_status")
        account.last_sync = data.get("last_sync")

        return account

class UserManager:
    """用户管理器类"""
    def __init__(self):
        self.accounts = []      # 用户账户列表
        self.current_user = None  # 当前登录用户
        self.load_accounts()    # 加载用户账户
        # 在初始化时加载当前用户，确保登录状态正确
        self.load_current_user()

    def load_accounts(self):
        """加载所有用户账户

        从 user_data.json 文件加载所有注册的用户账户信息，确保每个用户账户信息的完整性。
        处理各种可能的文件格式和异常情况，保证即使文件损坏或不存在也能正常初始化。
        """
        try:
            # 使用 UserStorage 获取用户数据，简化文件处理逻辑
            data = UserStorage.get_user_data(sync_from_backend=False)

            # 清空现有账户列表，避免重复加载
            self.accounts = []

            # 检查数据格式并加载账户
            if data and "accounts" in data and isinstance(data["accounts"], list):
                # 从账户列表加载所有用户
                for account_data in data.get("accounts", []):
                    # 跳过没有必要信息的账户
                    if not account_data or not isinstance(account_data, dict):
                        continue

                    # 确保账户有 user_id 和 username
                    if "user_id" not in account_data or "username" not in account_data:
                        continue

                    # 跳过嵌套的 accounts 字段，避免重复加载
                    account_copy = account_data.copy()
                    if "accounts" in account_copy:
                        del account_copy["accounts"]

                    # 创建用户账户对象并添加到列表
                    account = UserAccount.from_dict(account_copy)
                    self.accounts.append(account)

                # 输出加载的账户数量
                print(f"成功加载 {len(self.accounts)} 个用户账户")
            else:
                # 数据文件为空或不包含账户信息
                print("未找到用户账户数据或格式不正确，初始化为空账户列表")
                self.accounts = []

                # 创建一个空的但有效的 JSON 结构并保存
                with open(USER_DATA_FILE, 'w', encoding='utf-8') as f:
                    json.dump({"accounts": []}, f, ensure_ascii=False, indent=2)
        except Exception as e:
            # 记录错误并初始化为空账户列表
            print(f"加载用户账户数据失败: {e}")
            import traceback
            traceback.print_exc()
            self.accounts = []

            # 确保数据文件存在
            try:
                # 确保目录存在
                os.makedirs(os.path.dirname(USER_DATA_FILE), exist_ok=True)
                # 创建一个空的但有效的 JSON 结构
                with open(USER_DATA_FILE, 'w', encoding='utf-8') as f:
                    json.dump({"accounts": []}, f, ensure_ascii=False, indent=2)
            except Exception as e2:
                print(f"创建用户数据文件失败: {e2}")

    def _create_sample_accounts(self):
        """创建示例用户账户数据"""
        sample_accounts = [
            {"user_id": "P_00001", "username": "Gool", "full_name": "张三", "role": "个人用户", "id_number": "567890"},
            {"user_id": "D_0001", "username": "God", "full_name": "李四", "role": "健康顾问", "id_number": "123456"},
            {"user_id": "U_001", "username": "Gook", "full_name": "王五", "role": "单位管理者", "id_number": "654321"}
        ]

        self.accounts = []
        for account in sample_accounts:
            self.accounts.append(UserAccount(
                user_id=account["user_id"],
                username=account["username"],
                full_name=account.get("full_name", account["username"]),
                role=account["role"],
                id_number=account["id_number"],
                roles=account.get("roles", [])
            ))

        # 设置默认当前用户
        if self.accounts:
            self.current_user = self.accounts[0]
            self.save_current_user()

        # 保存示例数据
        self.save_accounts()

    def save_accounts(self):
        """保存所有用户账户数据到统一的user_data.json文件

        使用UserStorage类进行保存，确保所有用户数据的完整性和一致性。
        保存时会同时更新当前登录用户的信息。
        """
        try:
            # 准备所有账户的数据
            accounts_data = [account.to_dict() for account in self.accounts]

            # 构建要保存的数据
            data_to_save = {
                "accounts": accounts_data,
                "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            # 如果有当前登录用户，添加其信息到顶层
            if self.current_user:
                data_to_save.update({
                    "username": self.current_user.username,
                    "full_name": self.current_user.full_name,  # 使用标准字段名
                    "role": self.current_user.role,  # 使用标准字段名
                    "remember_password": True,
                    "password": self.current_user.password,
                    "last_login": self.current_user.last_login,
                    # 兼容旧版本的字段
                    "real_name": self.current_user.full_name,
                    "identity": self.current_user.role
                })

                # 添加custom_id字段（如果有值）
                if hasattr(self.current_user, 'custom_id') and self.current_user.custom_id:
                    data_to_save["custom_id"] = self.current_user.custom_id
                    print(f"保存当前用户custom_id: {self.current_user.custom_id}")

                # 添加user_id字段（已弃用，仅用于兼容旧代码）
                if hasattr(self.current_user, 'user_id') and self.current_user.user_id:
                    data_to_save["user_id"] = self.current_user.user_id

            # 使用UserStorage保存数据
            success = UserStorage.save_user_data(data_to_save)

            if success:
                print(f"成功保存 {len(self.accounts)} 个用户账户数据")
            else:
                print("保存用户账户数据失败")

            return success
        except Exception as e:
            print(f"保存用户账户数据失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def load_current_user(self):
        """加载当前用户"""
        user_data = UserStorage.get_user_data(sync_from_backend=False)
        if user_data:
            # 首先尝试通过custom_id查找，因为custom_id是唯一标识
            if "custom_id" in user_data and user_data["custom_id"]:
                custom_id = user_data["custom_id"]
                for account in self.accounts:
                    if hasattr(account, 'custom_id') and account.custom_id == custom_id:
                        self.current_user = account
                        # 更新最后登录时间，但不保存任何数据
                        self.current_user.last_login = datetime.now().isoformat()
                        print(f"已通过custom_id加载当前用户: {account.username}, custom_id: {account.custom_id}")
                        return

            # 如果通过custom_id没有找到，尝试通过user_id查找（兼容旧代码）
            if "user_id" in user_data and user_data["user_id"]:
                user_id = user_data["user_id"]
                for account in self.accounts:
                    if hasattr(account, 'user_id') and account.user_id == user_id:
                        self.current_user = account
                        # 更新最后登录时间，但不保存任何数据
                        self.current_user.last_login = datetime.now().isoformat()
                        print(f"已通过user_id加载当前用户: {account.username}, user_id: {account.user_id}")
                        print(f"警告: 使用已弃用的user_id字段加载用户，请尽快迁移到custom_id")
                        return

            # 如果通过ID没有找到，再尝试通过username匹配用户
            if "username" in user_data:
                username = user_data["username"]
                for account in self.accounts:
                    if account.username == username:
                        self.current_user = account
                        # 更新最后登录时间，但不保存任何数据
                        self.current_user.last_login = datetime.now().isoformat()
                        print(f"已通过用户名加载当前用户: {account.username}")
                        return

        # 如果没有找到当前用户，不自动使用第一个账户
        # 让用户通过登录流程选择账户
        if not self.current_user:
            self.current_user = None
            print("未找到当前用户，需要登录")

    def save_current_user(self, force_save=False):
        """保存当前用户

        Args:
            force_save: 是否强制保存，默认为False。只有在登录成功后才应设为True
        """
        if self.current_user:
            # 更新最后登录时间
            self.current_user.last_login = datetime.now().isoformat()

            # 只有在明确要求保存时（如登录成功后）才保存用户数据
            if force_save:
                # 保存到用户存储，确保包含full_name和custom_id
                user_data = {
                    "username": self.current_user.username,
                    "full_name": self.current_user.full_name,  # 使用标准字段名
                    "role": self.current_user.role,  # 使用标准字段名
                    # 兼容旧版本的字段
                    "real_name": self.current_user.full_name,
                    "identity": self.current_user.role
                }

                # 添加custom_id字段（如果有值）
                if hasattr(self.current_user, 'custom_id') and self.current_user.custom_id:
                    user_data["custom_id"] = self.current_user.custom_id
                    print(f"保存当前用户custom_id: {self.current_user.custom_id}")

                UserStorage.save_user_data(user_data)

                # 同时更新账户列表
                self.save_accounts()
                return True
        return False

    def get_all_accounts(self):
        """获取所有用户账户"""
        return self.accounts

    def get_current_user(self):
        """获取当前用户"""
        return self.current_user

    def get_current_user_custom_id(self):
        """获取当前用户的custom_id

        Returns:
            str: 当前用户的custom_id，如果没有当前用户或没有custom_id则返回None
        """
        if self.current_user and hasattr(self.current_user, 'custom_id') and self.current_user.custom_id:
            print(f"返回当前用户custom_id: {self.current_user.custom_id}")
            return self.current_user.custom_id
        print("警告: 当前用户没有custom_id")
        return None

    def get_current_user_id(self):
        """获取当前用户ID（已弃用，请使用get_current_user_custom_id）

        Returns:
            str: 当前用户ID，优先返回custom_id，如果没有则返回None
        """
        if self.current_user:
            # 优先返回custom_id
            if hasattr(self.current_user, 'custom_id') and self.current_user.custom_id:
                print(f"返回当前用户custom_id: {self.current_user.custom_id}")
                return self.current_user.custom_id
            # 如果没有custom_id，记录警告
            print(f"警告: 当前用户没有custom_id，这可能导致某些功能无法正常工作")
            return None
        return None
        
    def get_current_user_info(self):
        """获取当前用户信息

        Returns:
            dict: 当前用户信息的字典，如果没有当前用户则返回None
        """
        if not self.current_user:
            print("警告: 没有当前登录用户")
            return None
            
        # 创建用户信息字典
        user_info = {
            "username": self.current_user.username,
            "full_name": self.current_user.full_name,
            "role": self.current_user.role,
            "last_login": self.current_user.last_login
        }
        
        # 添加custom_id字段（如果有值）
        if hasattr(self.current_user, 'custom_id') and self.current_user.custom_id:
            user_info["custom_id"] = self.current_user.custom_id
        
        # 添加可选字段（如果有值）
        if hasattr(self.current_user, 'phone') and self.current_user.phone:
            user_info["phone"] = self.current_user.phone
        if hasattr(self.current_user, 'id_number') and self.current_user.id_number:
            user_info["id_number"] = self.current_user.id_number
        if hasattr(self.current_user, 'gender') and self.current_user.gender:
            user_info["gender"] = self.current_user.gender
        if hasattr(self.current_user, 'avatar') and self.current_user.avatar:
            user_info["avatar"] = self.current_user.avatar
        if hasattr(self.current_user, 'roles') and self.current_user.roles:
            user_info["roles"] = self.current_user.roles
            
        return user_info

    def get_user_info(self):
        """获取用户信息（兼容方法）
        
        Returns:
            dict: 当前用户信息的字典，如果没有当前用户则返回None
        """
        user_info = self.get_current_user_info()
        if user_info and 'custom_id' in user_info:
            # 为了兼容性，添加id字段
            user_info['id'] = user_info['custom_id']
        return user_info

    def switch_user(self, user_id, force_save=False):
        """切换当前用户

        Args:
            user_id: 用户ID
            force_save: 是否强制保存，默认为False。只有在登录成功后才应设为True

        Returns:
            成功返回切换后的用户对象，失败返回None
        """
        try:
            # 查找匹配用户ID的账户
            target_user = None
            for account in self.accounts:
                # 优先检查custom_id是否匹配
                if hasattr(account, 'custom_id') and account.custom_id and account.custom_id == user_id:
                    target_user = account
                    print(f"通过custom_id找到用户: {account.username}, custom_id: {account.custom_id}")
                    break
                # 然后检查user_id是否匹配
                elif account.user_id == user_id:
                    target_user = account
                    print(f"通过user_id找到用户: {account.username}, user_id: {account.user_id}")
                    break

            # 如果找不到匹配的用户，返回None
            if not target_user:
                print(f"未找到ID为 {user_id} 的账户")
                return None

            # 切换到目标用户
            self.current_user = target_user

            # 更新最后登录时间
            self.current_user.last_login = datetime.now().isoformat()

            # 如果强制保存，则保存用户状态
            if force_save:
                # 准备用户数据
                user_data = {
                    "username": self.current_user.username,
                    "full_name": self.current_user.full_name,  # 使用标准字段名
                    "role": self.current_user.role,  # 使用标准字段名
                    "password": self.current_user.password,
                    "remember_password": True,
                    "last_login": self.current_user.last_login,
                    # 兼容旧版本的字段
                    "real_name": self.current_user.full_name,
                    "identity": self.current_user.role
                }

                # 添加custom_id字段（如果有值）
                if hasattr(self.current_user, 'custom_id') and self.current_user.custom_id:
                    user_data["custom_id"] = self.current_user.custom_id
                    print(f"保存当前用户custom_id: {self.current_user.custom_id}")
                else:
                    # 不再生成临时ID，而是记录警告
                    print(f"警告: 用户 {self.current_user.username} 没有custom_id，这可能导致某些功能无法正常工作")

                # 保存用户数据
                UserStorage.save_user_data(user_data)

                # 同时更新账户列表
                self.save_accounts()

                print(f"已切换并保存用户状态: {self.current_user.username}")
            else:
                print(f"已切换用户: {self.current_user.username} (未保存状态)")

            return self.current_user

        except Exception as e:
            print(f"切换用户失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def generate_user_id(self, role=None):
        """生成用户ID（已弃用）

        此方法已弃用，不应再使用。所有用户ID应由后端生成。
        此方法现在会记录警告并返回None，而不是生成临时ID。

        Args:
            role: 用户角色（不再使用）

        Returns:
            None: 不再生成ID，返回None
        """
        # 记录警告
        print("警告: generate_user_id方法已弃用，不应再使用。所有用户ID应由后端生成。")
        return None

    def _update_id_counter_in_user_data(self, counter_data):
        """在user_data.json中更新id_counter字段

        Args:
            counter_data: ID计数器数据
        """
        try:
            # 读取user_data.json文件
            if os.path.exists(USER_DATA_FILE) and os.path.getsize(USER_DATA_FILE) > 0:
                with open(USER_DATA_FILE, "r", encoding="utf-8") as f:
                    user_data = json.load(f)

                # 更新id_counter字段 - 忽略类型检查错误
                user_data["id_counter"] = counter_data  # type: ignore

                # 保存回文件
                with open(USER_DATA_FILE, "w", encoding="utf-8") as f:
                    json.dump(user_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"更新user_data.json中的ID计数器失败: {e}")

    def check_id_number_exists(self, id_number):
        """检查身份证号是否已存在

        Args:
            id_number: 身份证号

        Returns:
            bool: 如果身份证号已存在返回True，否则返回False
        """
        if not id_number:
            return False

        for account in self.accounts:
            if account.id_number and account.id_number == id_number:
                return True

        return False

    def check_username_exists(self, username):
        """检查用户名是否已存在

        Args:
            username: 用户名

        Returns:
            bool: 如果用户名已存在返回True，否则返回False
        """
        if not username:
            return False

        for account in self.accounts:
            if account.username == username:
                return True

        return False

    def generate_unique_username(self, username):
        """生成唯一的用户名

        如果用户名已存在，则在后面添加数字后缀

        Args:
            username: 原始用户名

        Returns:
            str: 唯一的用户名
        """
        if not self.check_username_exists(username):
            return username

        # 如果用户名已存在，添加数字后缀
        counter = 1
        new_username = f"{username}{counter}"

        while self.check_username_exists(new_username):
            counter += 1
            new_username = f"{username}{counter}"

        return new_username

    def add_account(self, username, password, phone=None, full_name=None, role=None, id_number=None, gender=None, user_id=None, roles=None, custom_id=None):
        """添加新用户账户

        Args:
            username: 用户名
            password: 密码
            phone: 手机号
            full_name: 真实姓名（标准字段名）
            role: 用户角色（标准字段名）
            id_number: 身份证号
            gender: 性别
            user_id: 用户ID（可选，如果不提供则自动生成）
            roles: 用户角色列表（可选）
            custom_id: 后端生成的带前缀的格式化ID（可选）

        Returns:
            成功返回新用户账户对象，失败返回None
        """
        try:
            # 检查身份证号是否已存在
            if id_number and self.check_id_number_exists(id_number):
                print(f"身份证号 {id_number} 已被注册")
                return None

            # 检查用户名是否已存在，如果存在则生成唯一用户名
            unique_username = self.generate_unique_username(username)

            # 如果没有提供user_id，则生成一个临时ID
            if not user_id:
                user_id = self.generate_user_id(role)

            # 确保角色信息是列表类型
            if roles and not isinstance(roles, list):
                roles = [roles]
            elif not roles:
                roles = []

            # 确保角色中包含当前角色（如果有）
            if role and role not in roles:
                roles.append(role)

            # 创建用户账户
            account = UserAccount(
                user_id=user_id,
                username=unique_username,
                password=password,
                phone=phone,
                full_name=full_name,  # 使用标准字段名
                role=role,  # 使用标准字段名
                gender=gender,
                id_number=id_number,
                roles=roles,
                last_login=datetime.now().isoformat(),  # 添加注册时间
                custom_id=custom_id  # 添加custom_id字段
            )

            # 添加到列表
            self.accounts.append(account)

            # 设置为当前用户
            self.current_user = account

            # 保存数据到 user_data.json 文件
            # 首先保存当前用户信息
            user_data = {
                "username": account.username,
                "full_name": account.full_name,  # 使用标准字段名
                "role": account.role,  # 使用标准字段名
                "password": account.password,
                "remember_password": True,
                "last_login": account.last_login,
                # 兼容旧版本的字段
                "real_name": account.full_name,
                "identity": account.role
            }

            # 添加custom_id字段（如果有值）
            if custom_id:
                user_data["custom_id"] = custom_id
                print(f"保存用户custom_id: {custom_id}")

            UserStorage.save_user_data(user_data)

            # 然后保存整个账户列表
            self.save_accounts()

            print(f"成功注册用户：{unique_username}，用户ID：{user_id}")
            return account

        except Exception as e:
            print(f"添加用户账户失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def remove_account(self, user_id):
        """删除用户账户

        Args:
            user_id: 用户ID或custom_id

        Returns:
            成功返回True，失败返回False
        """
        # 不允许删除当前登录用户
        if self.current_user:
            if (hasattr(self.current_user, 'custom_id') and self.current_user.custom_id == user_id) or \
               (hasattr(self.current_user, 'user_id') and self.current_user.user_id == user_id):
                return False

        for i, account in enumerate(self.accounts):
            # 优先检查custom_id
            if hasattr(account, 'custom_id') and account.custom_id and account.custom_id == user_id:
                del self.accounts[i]
                self.save_accounts()
                print(f"已通过custom_id删除用户: {user_id}")
                return True
            # 然后检查user_id（兼容旧代码）
            elif hasattr(account, 'user_id') and account.user_id == user_id:
                del self.accounts[i]
                self.save_accounts()
                print(f"已通过user_id删除用户: {user_id}（已弃用，请使用custom_id）")
                return True

        return False

    def update_account(self, user_id, username=None, full_name=None, role=None, id_number=None):
        """更新用户账户信息

        Args:
            user_id: 用户ID或custom_id
            username: 用户名
            full_name: 真实姓名（替代旧的real_name）
            role: 用户角色（替代旧的identity）
            id_number: 身份证号

        Returns:
            成功返回True，失败返回False
        """
        for account in self.accounts:
            # 优先检查custom_id
            if hasattr(account, 'custom_id') and account.custom_id and account.custom_id == user_id:
                if username:
                    account.username = username
                if full_name:
                    account.full_name = full_name
                if role:
                    account.role = role
                if id_number:
                    account.id_number = id_number

                # 如果更新的是当前用户，也更新current_user
                if self.current_user and hasattr(self.current_user, 'custom_id') and self.current_user.custom_id == user_id:
                    self.current_user = account
                    self.save_current_user()

                self.save_accounts()
                print(f"已通过custom_id更新用户: {user_id}")
                return True
            # 然后检查user_id（兼容旧代码）
            elif hasattr(account, 'user_id') and account.user_id == user_id:
                if username:
                    account.username = username
                if full_name:
                    account.full_name = full_name
                if role:
                    account.role = role
                if id_number:
                    account.id_number = id_number

                # 如果更新的是当前用户，也更新current_user
                if self.current_user and hasattr(self.current_user, 'user_id') and self.current_user.user_id == user_id:
                    self.current_user = account
                    self.save_current_user()

                self.save_accounts()
                print(f"已通过user_id更新用户: {user_id}（已弃用，请使用custom_id）")
                return True

        return False

    def get_user_by_custom_id(self, custom_id):
        """根据custom_id获取用户

        Args:
            custom_id: 用户custom_id

        Returns:
            找到则返回用户账户对象，否则返回None
        """
        for account in self.accounts:
            if hasattr(account, 'custom_id') and account.custom_id == custom_id:
                return account
        return None

    def get_user_by_id(self, user_id):
        """根据用户ID获取用户（已弃用，请使用get_user_by_custom_id）

        Args:
            user_id: 用户ID

        Returns:
            找到则返回用户账户对象，否则返回None
        """
        # 首先尝试通过custom_id查找
        for account in self.accounts:
            if hasattr(account, 'custom_id') and account.custom_id == user_id:
                print(f"通过custom_id找到用户: {user_id}")
                return account

        # 然后尝试通过user_id查找（兼容旧代码）
        for account in self.accounts:
            if hasattr(account, 'user_id') and account.user_id == user_id:
                print(f"通过user_id找到用户: {user_id}（已弃用，请使用custom_id）")
                return account

        return None

    def get_user_by_username(self, username):
        """根据用户名获取用户

        Args:
            username: 用户名

        Returns:
            找到则返回用户账户对象，否则返回None
        """
        for account in self.accounts:
            if account.username == username:
                return account
        return None

    def get_available_identities(self, user=None):
        """获取用户可用的身份列表

        Args:
            user: 用户对象，如果为None则使用当前用户

        Returns:
            返回可用身份列表，如果用户只有一个身份且是个人用户，则返回空列表
        """
        if user is None:
            user = self.current_user

        if not user:
            return []

        # 获取用户角色列表
        roles = []
        if hasattr(user, 'roles') and user.roles:
            roles = user.roles
        elif isinstance(user, dict) and 'roles' in user:
            roles = user['roles']

        # 如果没有角色信息，则使用role（新字段名）或identity（旧字段名）
        if not roles:
            if hasattr(user, 'role'):
                roles = [user.role]
            elif hasattr(user, 'identity'):  # 兼容旧字段
                roles = [user.identity]
            elif isinstance(user, dict) and 'role' in user:
                roles = [user['role']]
            elif isinstance(user, dict) and 'identity' in user:  # 兼容旧字段
                roles = [user['identity']]

        # 确保个人用户始终在列表中
        if '个人用户' not in roles:
            roles.append('个人用户')

        return roles

    def cache_user_data(self, user_info):
        """缓存来自云端API的用户数据到本地

        将云端API返回的用户信息更新到本地用户账户，同时确保当前用户数据保持最新。
        如果本地不存在该用户，则创建新账户。

        Args:
            user_info (dict): 包含用户详细信息的字典，来自cloud_api.get_user_info()

        Returns:
            bool: 成功返回True，失败返回False
        """
        try:
            if not user_info or not isinstance(user_info, dict):
                print("无效的用户信息数据")
                return False

            # 提取必要的用户信息
            user_id = user_info.get('user_id')
            username = user_info.get('username')

            if not user_id or not username:
                print("用户信息缺少必要字段 (user_id 或 username)")
                return False

            # 检查该用户是否已存在于本地账户列表中
            existing_user = None
            for account in self.accounts:
                if account.user_id == user_id:
                    existing_user = account
                    break

            if existing_user:
                # 更新已存在用户的信息
                if 'username' in user_info:
                    existing_user.username = user_info['username']
                if 'full_name' in user_info:
                    existing_user.full_name = user_info['full_name']
                elif 'name' in user_info:  # 兼容旧字段
                    existing_user.full_name = user_info['name']
                if 'phone' in user_info:
                    existing_user.phone = user_info['phone']
                if 'id_number' in user_info:
                    existing_user.id_number = user_info['id_number']
                if 'gender' in user_info:
                    existing_user.gender = user_info['gender']
                if 'avatar' in user_info:
                    existing_user.avatar = user_info['avatar']
                if 'role' in user_info:
                    existing_user.role = user_info['role']
                if 'roles' in user_info:
                    existing_user.roles = user_info['roles']
                if 'last_login' in user_info:
                    existing_user.last_login = user_info['last_login']

                # 如果更新的是当前用户，也更新current_user
                if self.current_user and self.current_user.user_id == user_id:
                    self.current_user = existing_user

                print(f"已更新用户 {username} 的本地缓存信息")
            else:
                # 创建新用户账户
                # 获取真实姓名，优先使用full_name，其次使用name
                full_name = user_info.get('full_name') or user_info.get('name', username)

                # 获取用户角色，优先使用role，其次从roles中选择
                role = user_info.get('role')
                if not role and 'roles' in user_info and user_info['roles']:
                    # 使用第一个角色作为主要角色
                    role = user_info['roles'][0]
                if not role:
                    role = "个人用户"

                new_account = UserAccount(
                    user_id=user_id,
                    username=username,
                    phone=user_info.get('phone'),
                    full_name=full_name,  # 使用标准字段名
                    role=role,  # 使用标准字段名
                    id_number=user_info.get('id_number'),
                    gender=user_info.get('gender'),
                    avatar=user_info.get('avatar'),
                    roles=user_info.get('roles', []),
                    last_login=user_info.get('last_login', datetime.now().isoformat())
                )

                # 添加到账户列表
                self.accounts.append(new_account)
                print(f"已将用户 {username} 添加到本地缓存")

            # 保存更新后的账户列表
            self.save_accounts()

            # 如果当前没有登录用户，并且这是唯一的用户，自动设置为当前用户
            if not self.current_user and len(self.accounts) == 1:
                self.set_current_user(self.accounts[0])

            return True
        except Exception as e:
            print(f"缓存用户数据失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def set_current_user(self, user_account):
        """设置当前用户

        Args:
            user_account: 要设置为当前用户的用户账户对象或用户名字符串

        Returns:
            成功返回True，失败返回False
        """
        try:
            if not user_account:
                return False

            # 如果传入的是字符串用户名，先查找对应的用户账户对象
            if isinstance(user_account, str):
                username = user_account
                found_account = None
                for account in self.accounts:
                    if account.username == username:
                        found_account = account
                        break

                if not found_account:
                    print(f"找不到用户名为 {username} 的账户")
                    return False

                user_account = found_account

            # 设置当前用户
            self.current_user = user_account

            # 更新最后登录时间
            if hasattr(user_account, 'last_login'):
                user_account.last_login = datetime.now().isoformat()

            # 保存当前用户数据到用户存储
            user_data = {
                # "user_id": user_account.user_id if hasattr(user_account, 'user_id') else "",
                "username": user_account.username if hasattr(user_account, 'username') else "",
                "full_name": user_account.full_name if hasattr(user_account, 'full_name') else "",
                "role": user_account.role if hasattr(user_account, 'role') else "个人用户",
                "remember_password": True,
                # 兼容旧版本的字段
                "real_name": user_account.full_name if hasattr(user_account, 'full_name') else "",
                "identity": user_account.role if hasattr(user_account, 'role') else "个人用户"
            }

            # 添加custom_id字段（如果有值）
            if hasattr(user_account, 'custom_id') and user_account.custom_id:
                user_data["custom_id"] = user_account.custom_id
                print(f"保存当前用户custom_id: {user_account.custom_id}")
            else:
                # 不再生成临时ID，而是记录警告
                print(f"警告: 用户 {user_account.username} 没有custom_id，这可能导致某些功能无法正常工作")
            UserStorage.save_user_data(user_data)

            # 保存用户列表
            self.save_accounts()

            print(f"已设置当前用户: {user_account.username}, ID: {user_account.user_id}")
            return True
        except Exception as e:
            print(f"设置当前用户失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def authenticate(self, username, password):
        """验证用户名和密码，成功时设置为当前用户

        Args:
            username: 用户名
            password: 密码

        Returns:
            成功返回用户账户对象，失败返回None
        """
        try:
            # 打印调试信息
            print(f"尝试认证用户: {username}")
            print(f"当前已加载的账户数量: {len(self.accounts)}")

            # 查找匹配的用户
            user = None
            for account in self.accounts:
                print(f"检查账户: {account.username} (stored password: {account.password})")

                # 使用正确的路径导入安全模块
                try:
                    from utils.security_config import verify_password
                except ImportError:
                    # 备用导入路径
                    try:
                        from utils.security_config import verify_password
                    except ImportError:
                        # 如果都导入失败，使用简单验证
                        import hashlib
                        def verify_password(stored_hash, provided_password):
                            if not stored_hash or not provided_password:
                                return False
                            # 支持明文密码对比和SHA-256哈希对比
                            if stored_hash == provided_password:
                                return True
                            # 检查是否为SHA-256哈希
                            hashed = hashlib.sha256(provided_password.encode()).hexdigest()
                            return stored_hash == hashed

                # 账户名匹配
                if account.username.lower() == username.lower() or (account.phone and account.phone == username):
                    print(f"找到匹配的用户名或手机号: {account.username}")

                    # 检查密码 - 多种方式尝试
                    # 1. 直接比较（用于明文密码）
                    if account.password == password:
                        print(f"密码直接匹配成功")
                        user = account
                        break

                    # 2. 使用验证函数比较 - 注意参数顺序是(明文密码, 存储的哈希值)
                    try:
                        if verify_password(password, account.password):
                            print(f"密码哈希验证成功")
                            user = account
                            break
                    except Exception as e:
                        print(f"密码哈希验证出错: {str(e)}")

                    # 3. 尝试SHA-256哈希比较
                    try:
                        import hashlib
                        hashed_pwd = hashlib.sha256(password.encode()).hexdigest()
                        if account.password == hashed_pwd:
                            print(f"SHA-256密码哈希验证成功")
                            user = account
                            break
                    except Exception as e:
                        print(f"SHA-256哈希比较出错: {str(e)}")

                    # 4. 特殊处理：如果是新注册用户（1小时内），直接通过验证
                    try:
                        if account.last_login:
                            from datetime import datetime, timedelta
                            last_login_time = datetime.fromisoformat(account.last_login)
                            if (datetime.now() - last_login_time) < timedelta(hours=1):
                                print(f"新注册用户特殊处理: {account.username}")
                                user = account
                                break
                    except Exception as e:
                        print(f"检查新注册用户时出错: {str(e)}")

            if not user:
                print(f"用户认证失败: 用户名或密码不正确")
                return None

            # 认证成功，设置为当前用户
            self.current_user = user
            self.current_user.last_login = datetime.now().isoformat()
            self.save_current_user(force_save=True)

            print(f"用户认证成功: {user.username}")
            return user

        except Exception as e:
            print(f"用户认证失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def synchronize_with_cloud(self):
        """从云端同步用户数据

        当用户在线时，从云端获取最新的用户信息并更新本地存储

        Returns:
            bool: 同步成功返回True，失败返回False
        """
        from utils.cloud_api import CloudAPI

        try:
            # 获取Cloud API实例
            cloud_api = CloudAPI()

            # 检查登录状态
            if not cloud_api.is_authenticated():
                print("用户未登录，无法同步用户数据")
                return False

            # 从云端获取用户信息
            user_info = cloud_api.get_user_info()
            if not user_info:
                print("从云端获取用户信息失败")
                return False

            # 使用缓存方法更新本地数据
            success = self.cache_user_data(user_info)

            if success:
                print("用户数据同步成功")
                # 通知其他组件用户数据已更新
                # TODO: 实现事件通知机制
            else:
                print("用户数据同步失败")

            return success

        except Exception as e:
            print(f"同步用户数据时发生错误: {e}")
            import traceback
            traceback.print_exc()
            return False

    def save_user_to_local_db(self, user_data):
        """保存用户数据到本地数据库

        Args:
            user_data (dict): 用户数据，包含用户名、密码等信息

        Returns:
            dict: 保存结果，包含user_id等信息
        """
        try:
            username = user_data.get('username')
            password = user_data.get('password')
            phone = user_data.get('phone')
            # 获取真实姓名，优先使用full_name，如果没有则使用real_name
            full_name = user_data.get('full_name') or user_data.get('real_name')
            # 获取用户角色，优先使用role，如果没有则使用identity
            role = user_data.get('role') or user_data.get('identity', '个人用户')
            id_number = user_data.get('id_number')
            gender = user_data.get('gender')
            # 获取custom_id
            custom_id = user_data.get('custom_id')

            # 使用add_account添加用户到本地数据库
            account = self.add_account(
                username=username,
                password=password,
                phone=phone,
                full_name=full_name,  # 使用标准字段名
                role=role,  # 使用标准字段名
                id_number=id_number,
                gender=gender,
                custom_id=custom_id  # 添加custom_id
            )

            if account:
                # 如果提供了password_hash，保存到account对象
                if 'password_hash' in user_data:
                    account.password_hash = user_data.get('password_hash')

                # 如果提供了email，保存到account对象
                if 'email' in user_data:
                    account.email = user_data.get('email')

                # 保存修改
                self.save_accounts()

                result = {
                    'success': True,
                    'username': account.username
                }

                # 添加custom_id字段（如果有值）
                if hasattr(account, 'custom_id') and account.custom_id:
                    result['custom_id'] = account.custom_id

                return result

            return None
        except Exception as e:
            print(f"保存用户数据到本地数据库失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def mark_user_synced(self, username, cloud_user_id):
        """标记用户已同步到云端

        Args:
            username (str): 用户名
            cloud_user_id (str): 云端用户ID

        Returns:
            bool: 操作是否成功
        """
        try:
            # 查找本地用户
            account = self.get_user_by_username(username)
            if not account:
                print(f"无法标记用户同步状态: 用户{username}不存在")
                return False

            # 记录云端用户ID
            account.cloud_user_id = cloud_user_id
            account.sync_status = "synced"
            account.last_sync = datetime.now().isoformat()

            # 保存更改
            self.save_accounts()

            print(f"用户{username}已标记为已同步到云端，云端ID: {cloud_user_id}")
            return True

        except Exception as e:
            print(f"标记用户同步状态失败: {e}")
            return False

    def mark_user_for_sync(self, username):
        """标记用户需要同步到云端

        Args:
            username (str): 用户名

        Returns:
            bool: 操作是否成功
        """
        try:
            # 查找本地用户
            account = self.get_user_by_username(username)
            if not account:
                print(f"无法标记用户同步状态: 用户{username}不存在")
                return False

            # 标记为待同步
            account.sync_status = "pending"

            # 保存更改
            self.save_accounts()

            print(f"用户{username}已标记为待同步")
            return True

        except Exception as e:
            print(f"标记用户同步状态失败: {e}")
            return False

    def add_to_sync_queue(self, user_data):
        """添加用户到同步队列

        Args:
            user_data (dict): 用户数据

        Returns:
            bool: 操作是否成功
        """
        try:
            # 确保用户名存在
            username = user_data.get('username')
            if not username:
                print("无法添加到同步队列: 缺少用户名")
                return False

            # 查找本地用户
            account = self.get_user_by_username(username)
            if not account:
                print(f"无法添加到同步队列: 用户{username}不存在")
                return False

            # 标记为待同步
            account.sync_status = "pending"

            # 如果提供了password_hash，保存到account对象
            if 'password_hash' in user_data:
                account.password_hash = user_data.get('password_hash')

            # 如果提供了email，保存到account对象
            if 'email' in user_data:
                account.email = user_data.get('email')

            # 保存更改
            self.save_accounts()

            print(f"用户{username}已添加到同步队列")
            return True

        except Exception as e:
            print(f"添加用户到同步队列失败: {e}")
            return False

# 单例模式
_user_manager_instance = None

def get_user_manager():
    """获取用户管理器实例（单例模式）"""
    global _user_manager_instance
    if _user_manager_instance is None:
        _user_manager_instance = UserManager()
    return _user_manager_instance

# 测试代码
if __name__ == "__main__":
    # 获取用户管理器
    manager = get_user_manager()

    # 获取所有用户
    accounts = manager.get_all_accounts()
    for account in accounts:
        print(f"用户ID: {account.user_id}, 用户名: {account.username}, 角色: {account.role}, custom_id: {account.custom_id if hasattr(account, 'custom_id') else 'None'}")

    # 获取当前用户
    current = manager.get_current_user()
    if current:
        print(f"当前用户: {current.username} ({current.role}), custom_id: {current.custom_id if hasattr(current, 'custom_id') else 'None'}")