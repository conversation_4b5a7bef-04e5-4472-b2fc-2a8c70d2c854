#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API响应格式
"""

import requests
import json

def test_api_response():
    """测试API响应格式"""
    base_urls = [
        'http://localhost:8006',
        'http://localhost:8080',
        'http://localhost:8000'
    ]
    
    # 尝试获取认证token
    token = None
    for base_url in base_urls:
        try:
            print(f"\n=== 测试登录 {base_url} ===")
            # 尝试多个登录接口
            login_endpoints = [
                ('/api/auth/login_json', {'username': 'admin', 'password': 'admin'}),
                ('/api/auth/frontend_login_json', {'username': 'admin', 'password': 'admin'}),
                ('/api/auth/login', {'username': 'admin', 'password': 'admin'})
            ]
            
            login_response = None
            for endpoint, credentials in login_endpoints:
                try:
                    if 'json' in endpoint:
                        login_response = requests.post(
                            f'{base_url}{endpoint}',
                            json=credentials,
                            timeout=5
                        )
                    else:
                        login_response = requests.post(
                            f'{base_url}{endpoint}',
                            data=credentials,
                            timeout=5
                        )
                    print(f"尝试登录接口: {endpoint}, 状态码: {login_response.status_code}")
                    if login_response.status_code == 200:
                        break
                except Exception as e:
                    print(f"登录接口 {endpoint} 失败: {e}")
                    continue
            
            if login_response and login_response.status_code == 200:
                print(f"登录成功: {login_response.text}")
                try:
                    login_data = login_response.json()
                    if 'access_token' in login_data:
                        token = login_data['access_token']
                        print(f"获取到access_token: {token[:20]}...")
                        break
                    elif 'token' in login_data:
                        token = login_data['token']
                        print(f"获取到token: {token[:20]}...")
                        break
                    else:
                        print(f"登录响应中没有找到token，响应内容: {login_data}")
                except Exception as e:
                    print(f"解析登录响应失败: {e}")
            else:
                print(f"登录失败")
                if login_response:
                    print(f"登录失败响应: {login_response.text}")
        except Exception as e:
            print(f"登录请求失败: {e}")
    
    if not token:
        print("\n无法获取认证token，尝试不带认证的请求...")
    
def test_api_endpoint(url, endpoint_name, token=None):
    """测试API端点"""
    print(f"\n=== 测试{endpoint_name} {url} ===")
    try:
        headers = {}
        if token:
            headers['Authorization'] = f'Bearer {token}'
            print(f"使用token: {token[:20]}...")
        
        response = requests.get(url, headers=headers, timeout=5)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"数据类型: {type(data)}")
                if isinstance(data, list):
                    print(f"数组长度: {len(data)}")
                    if data:
                        print(f"第一个元素: {json.dumps(data[0], indent=2, ensure_ascii=False)}")
                elif isinstance(data, dict):
                    print(f"对象键: {list(data.keys())}")
                    print(f"响应内容: {json.dumps(data, indent=2, ensure_ascii=False)}")
            except Exception as e:
                print(f"解析JSON失败: {e}")
                print(f"原始响应: {response.text}")
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {e}")

    # 测试评估记录API
    for base_url in base_urls:
        test_api_endpoint(f'{base_url}/api/assessments/records', '评估记录API', token)
    
    # 测试评估列表API
    for base_url in base_urls:
        test_api_endpoint(f'{base_url}/api/assessments', '评估列表API', token)

if __name__ == '__main__':
    test_api_response()