## 1. 评估量表 (Assessment)

### 1.1 数据表 (Models)

- **AssessmentTemplate** (<mcfile name="assessment.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\models\assessment.py"></mcfile>)
  - **功能**: 定义评估量表的模板结构，包含量表名称、描述、说明、版本、分类、类型、状态、是否激活等。
  - **字段**: `id`, `template_key`, `name`, `name_en`, `description`, `instructions`, `category`, `type`, `status`, `version`, `is_active`, `created_at`, `updated_at`。
- **AssessmentTemplateQuestion** (<mcfile name="assessment.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\models\assessment.py"></mcfile>)
  - **功能**: 定义评估量表模板中的具体问题，包含问题ID、文本、类型、选项、顺序、是否必填、跳转逻辑等。
  - **字段**: `id`, `template_id` (外键), `question_id`, `question_text`, `question_type`, `options`, `order`, `is_required`, `jump_logic`, `created_at`, `updated_at`。
- **Assessment** (<mcfile name="assessment.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\models\assessment.py"></mcfile>)
  - **功能**: 存储用户创建的评估量表实例，基于模板生成，包含量表名称、描述、类型、版本、用户ID等。
  - **字段**: `id`, `template_id` (外键), `custom_id` (用户ID), `name`, `notes`, `assessment_type`, `version`, `created_at`, `updated_at`。
- **AssessmentItem** (<mcfile name="assessment.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\models\assessment.py"></mcfile>)
  - **功能**: 存储评估量表实例中的具体题目，包含问题ID、文本、类型、选项、顺序、是否必填、跳转逻辑等。
  - **字段**: `id`, `assessment_id` (外键), `question_id`, `question_text`, `question_type`, `options`, `order_num`, `is_required`, `jump_logic`, `created_at`, `updated_at`。
- **AssessmentResponse** (<mcfile name="assessment.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\models\assessment.py"></mcfile>)
  - **功能**: 存储用户对评估量表的回答记录，包含评估ID、用户ID、答案、得分、结果等。
  - **字段**: `id`, `assessment_id` (外键), `custom_id` (用户ID), `answers`, `score`, `result`, `status`, `created_at`, `updated_at`。
- **AssessmentDistribution** (<mcfile name="distribution.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\models\distribution.py"></mcfile>)
  - **功能**: 存储评估量表的分发记录，包含评估ID、用户ID、分发者ID、状态、截止日期等。
  - **字段**: `id`, `assessment_id` (外键), `custom_id` (被分发用户ID), `distributor_custom_id` (分发者用户ID), `status`, `due_date`, `completed_at`, `message`, `created_at`, `updated_at`。
- **AssessmentResult** (<mcfile name="result.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\models\result.py"></mcfile>)
  - **功能**: 存储评估量表的计算结果，包含总分、最高分、百分比、结果等级、解释、建议等。
  - **字段**: `id`, `assessment_id` (外键), `custom_id` (用户ID), `template_id` (外键), `total_score`, `max_score`, `percentage`, `result_level`, `result_category`, `interpretation`, `recommendations`, `dimension_scores`, `calculation_details`, `raw_answers`, `report_generated`, `report_content`, `report_format`, `report_template`, `status`, `calculated_at`, `created_at`, `updated_at`。

### 1.2 API端口 (Endpoints) - `backend/app/api/endpoints/assessments.py`

- **GET /** (<mcsymbol name="get_assessments" filename="assessments.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\api\endpoints\assessments.py" startline="100" type="function"></mcsymbol>)
  - **功能**: 获取系统预置和用户自定义的评估量表列表。
  - **通讯链条**: 前端 `AssessmentManagement.vue` -> 后端 `/assessments/` (GET)
- **GET /templates** (<mcsymbol name="get_assessment_templates" filename="assessments.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\api\endpoints\assessments.py" startline="20" type="function"></mcsymbol>)
  - **功能**: 获取评估量表模板列表。
  - **通讯链条**: 前端 `AssessmentManagement.vue` -> 后端 `/assessments/templates` (GET)
- **GET /{assessment_id}** (<mcsymbol name="get_assessment" filename="assessments.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\api\endpoints\assessments.py" startline="200" type="function"></mcsymbol>)
  - **功能**: 获取指定评估量表的详细信息。
  - **通讯链条**: 前端 `AssessmentEditor.vue` (或其他详情页) -> 后端 `/assessments/{assessment_id}` (GET)
- **POST /** (<mcsymbol name="create_assessment" filename="assessments.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\api\endpoints\assessments.py" startline="250" type="function"></mcsymbol>)
  - **功能**: 创建新的评估量表。
  - **通讯链条**: 前端 `AssessmentEditor.vue` -> 后端 `/assessments/` (POST)
- **PUT /{assessment_id}** (<mcsymbol name="update_assessment" filename="assessments.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\api\endpoints\assessments.py" startline="300" type="function"></mcsymbol>)
  - **功能**: 更新评估量表信息。
  - **通讯链条**: 前端 `AssessmentEditor.vue` -> 后端 `/assessments/{assessment_id}` (PUT)
- **DELETE /{assessment_id}** (<mcsymbol name="delete_assessment" filename="assessments.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\api\endpoints\assessments.py" startline="350" type="function"></mcsymbol>)
  - **功能**: 删除评估量表。
  - **通讯链条**: 前端 `AssessmentManagement.vue` -> 后端 `/assessments/{assessment_id}` (DELETE)

### 1.3 API端口 (Endpoints) - `backend/app/api/endpoints/assessment_distributions.py`

- **GET /user/{custom_id}** (<mcsymbol name="get_user_assessment_distributions" filename="assessment_distributions.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\api\endpoints\assessment_distributions.py" startline="20" type="function"></mcsymbol>)
  - **功能**: 获取指定用户的评估分发列表。
  - **通讯链条**: 前端 `UserDashboard.vue` -> 后端 `/assessment_distributions/user/{custom_id}` (GET)
- **DELETE /{distribution_id}** (<mcsymbol name="delete_assessment_distribution" filename="assessment_distributions.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\api\endpoints\assessment_distributions.py" startline="50" type="function"></mcsymbol>)
  - **功能**: 删除指定ID的评估分发记录。
  - **通讯链条**: 前端 `UserDashboard.vue` -> 后端 `/assessment_distributions/{distribution_id}` (DELETE)

### 1.4 API端口 (Endpoints) - `backend/app/api/endpoints/assessment_responses.py`

- **GET /{response_id}** (<mcsymbol name="get_assessment_response" filename="assessment_responses.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\api\endpoints\assessment_responses.py" startline="20" type="function"></mcsymbol>)
  - **功能**: 获取指定评估回答的详细信息。
  - **通讯链条**: 前端 `AssessmentResponseDetail.vue` -> 后端 `/assessment_responses/{response_id}` (GET)
- **GET /user/{custom_id}** (<mcsymbol name="get_user_assessment_responses" filename="assessment_responses.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\api\endpoints\assessment_responses.py" startline="50" type="function"></mcsymbol>)
  - **功能**: 获取指定用户的评估回答列表。
  - **通讯链条**: 前端 `UserDashboard.vue` -> 后端 `/assessment_responses/user/{custom_id}` (GET)
- **DELETE /{response_id}** (<mcsymbol name="delete_assessment_response" filename="assessment_responses.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\api\endpoints\assessment_responses.py" startline="80" type="function"></mcsymbol>)
  - **功能**: 删除指定的评估回答记录。
  - **通讯链条**: 前端 `UserDashboard.vue` -> 后端 `/assessment_responses/{response_id}` (DELETE)

### 1.5 API端口 (Endpoints) - `backend/app/api/endpoints/assessment_results.py`

- **GET /user/{custom_id}** (<mcsymbol name="get_user_assessment_results" filename="assessment_results.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\api\endpoints\assessment_results.py" startline="20" type="function"></mcsymbol>)
  - **功能**: 获取指定用户的评估量表结果列表。
  - **通讯链条**: 前端 `UserDashboard.vue` -> 后端 `/assessment_results/user/{custom_id}` (GET)
- **POST /** (<mcsymbol name="create_assessment_result" filename="assessment_results.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\api\endpoints\assessment_results.py" startline="50" type="function"></mcsymbol>)
  - **功能**: 创建评估量表结果。
  - **通讯链条**: 后端服务 (如评估计算服务) -> 后端 `/assessment_results/` (POST)
- **GET /{result_id}** (<mcsymbol name="get_assessment_result" filename="assessment_results.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\api\endpoints\assessment_results.py" startline="80" type="function"></mcsymbol>)
  - **功能**: 获取指定评估结果的详细信息。
  - **通讯链条**: 前端 `AssessmentResultDetail.vue` -> 后端 `/assessment_results/{result_id}` (GET)

## 2. 调查问卷 (Questionnaire)

### 2.1 数据表 (Models)

- **QuestionnaireTemplate** (<mcfile name="questionnaire.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\models\questionnaire.py"></mcfile>)
  - **功能**: 定义调查问卷的模板结构，包含问卷名称、描述、说明、版本、分类、类型、状态、是否激活等。
  - **字段**: `id`, `template_key`, `name`, `name_en`, `description`, `instructions`, `category`, `type`, `status`, `version`, `is_active`, `created_at`, `updated_at`。
- **QuestionnaireTemplateQuestion** (<mcfile name="questionnaire.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\models\questionnaire.py"></mcfile>)
  - **功能**: 定义调查问卷模板中的具体问题，包含问题ID、文本、类型、选项、顺序、是否必填、跳转逻辑等。
  - **字段**: `id`, `template_id` (外键), `question_id`, `question_text`, `question_type`, `options`, `order`, `is_required`, `jump_logic`, `created_at`, `updated_at`。
- **Questionnaire** (<mcfile name="questionnaire.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\models\questionnaire.py"></mcfile>)
  - **功能**: 存储用户创建的调查问卷实例，基于模板生成，包含问卷名称、描述、类型、版本、用户ID等。
  - **字段**: `id`, `template_id` (外键), `custom_id` (用户ID), `name`, `notes`, `questionnaire_type`, `version`, `created_at`, `updated_at`。
- **QuestionnaireItem** (<mcfile name="questionnaire.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\models\questionnaire.py"></mcfile>)
  - **功能**: 存储调查问卷实例中的具体题目，包含问题ID、文本、类型、选项、顺序、是否必填、跳转逻辑等。
  - **字段**: `id`, `questionnaire_id` (外键), `question_id`, `question_text`, `question_type`, `options`, `order_num`, `is_required`, `jump_logic`, `created_at`, `updated_at`。
- **QuestionnaireResponse** (<mcfile name="questionnaire.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\models\questionnaire.py"></mcfile>)
  - **功能**: 存储用户对调查问卷的回答记录，包含问卷ID、用户ID、答案、得分、结果等。
  - **字段**: `id`, `questionnaire_id` (外键), `custom_id` (用户ID), `answers`, `score`, `result`, `status`, `created_at`, `updated_at`。
- **QuestionnaireDistribution** (<mcfile name="distribution.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\models\distribution.py"></mcfile>)
  - **功能**: 存储调查问卷的分发记录，包含问卷ID、用户ID、分发者ID、状态、截止日期等。
  - **字段**: `id`, `questionnaire_id` (外键), `custom_id` (被分发用户ID), `distributor_custom_id` (分发者用户ID), `status`, `due_date`, `completed_at`, `message`, `created_at`, `updated_at`。
- **QuestionnaireResult** (<mcfile name="result.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\models\result.py"></mcfile>)
  - **功能**: 存储调查问卷的计算结果，包含总分、最高分、百分比、结果等级、解释、建议等。
  - **字段**: `id`, `questionnaire_id` (外键), `custom_id` (用户ID), `template_id` (外键), `total_score`, `max_score`, `percentage`, `result_level`, `result_category`, `interpretation`, `recommendations`, `dimension_scores`, `calculation_details`, `raw_answers`, `report_generated`, `report_content`, `report_format`, `report_template`, `status`, `calculated_at`, `created_at`, `updated_at`。

### 2.2 API端口 (Endpoints) - `backend/app/api/endpoints/questionnaires.py`

- **GET /** (<mcsymbol name="get_questionnaires" filename="questionnaires.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\api\endpoints\questionnaires.py" startline="100" type="function"></mcsymbol>)
  - **功能**: 获取系统预置和用户自定义的调查问卷列表。
  - **通讯链条**: 前端 `QuestionnaireManagement.vue` -> 后端 `/questionnaires/` (GET)
- **GET /templates** (<mcsymbol name="get_questionnaire_templates" filename="questionnaires.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\api\endpoints\questionnaires.py" startline="20" type="function"></mcsymbol>)
  - **功能**: 获取调查问卷模板列表。
  - **通讯链条**: 前端 `QuestionnaireManagement.vue` -> 后端 `/questionnaires/templates` (GET)
- **GET /{questionnaire_id}** (<mcsymbol name="get_questionnaire" filename="questionnaires.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\api\endpoints\questionnaires.py" startline="200" type="function"></mcsymbol>)
  - **功能**: 获取指定调查问卷的详细信息。
  - **通讯链条**: 前端 `QuestionnaireEditor.vue` (或其他详情页) -> 后端 `/questionnaires/{questionnaire_id}` (GET)
- **POST /** (<mcsymbol name="create_questionnaire" filename="questionnaires.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\api\endpoints\questionnaires.py" startline="250" type="function"></mcsymbol>)
  - **功能**: 创建新的调查问卷。
  - **通讯链条**: 前端 `QuestionnaireEditor.vue` -> 后端 `/questionnaires/` (POST)
- **PUT /{questionnaire_id}** (<mcsymbol name="update_questionnaire" filename="questionnaires.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\api\endpoints\questionnaires.py" startline="300" type="function"></mcsymbol>)
  - **功能**: 更新调查问卷信息。
  - **通讯链条**: 前端 `QuestionnaireEditor.vue` -> 后端 `/questionnaires/{questionnaire_id}` (PUT)
- **DELETE /{questionnaire_id}** (<mcsymbol name="delete_questionnaire" filename="questionnaires.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\api\endpoints\questionnaires.py" startline="350" type="function"></mcsymbol>)
  - **功能**: 删除调查问卷。
  - **通讯链条**: 前端 `QuestionnaireManagement.vue` -> 后端 `/questionnaires/{questionnaire_id}` (DELETE)

### 2.3 API端口 (Endpoints) - `backend/app/api/endpoints/questionnaire_distributions.py`

- **GET /user/{custom_id}** (<mcsymbol name="get_user_questionnaire_distributions" filename="questionnaire_distributions.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\api\endpoints\questionnaire_distributions.py" startline="20" type="function"></mcsymbol>)
  - **功能**: 获取指定用户的问卷分发列表。
  - **通讯链条**: 前端 `UserDashboard.vue` -> 后端 `/questionnaire_distributions/user/{custom_id}` (GET)
- **DELETE /{distribution_id}** (<mcsymbol name="delete_questionnaire_distribution" filename="questionnaire_distributions.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\api\endpoints\questionnaire_distributions.py" startline="50" type="function"></mcsymbol>)
  - **功能**: 删除指定ID的问卷分发记录。
  - **通讯链条**: 前端 `UserDashboard.vue` -> 后端 `/questionnaire_distributions/{distribution_id}` (DELETE)

### 2.4 API端口 (Endpoints) - `backend/app/api/endpoints/questionnaire_responses.py`

- **GET /{response_id}** (<mcsymbol name="get_questionnaire_response" filename="questionnaire_responses.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\api\endpoints\questionnaire_responses.py" startline="20" type="function"></mcsymbol>)
  - **功能**: 获取指定问卷回答的详细信息。
  - **通讯链条**: 前端 `QuestionnaireResponseDetail.vue` -> 后端 `/questionnaire_responses/{response_id}` (GET)
- **GET /user/{custom_id}** (<mcsymbol name="get_user_questionnaire_responses" filename="questionnaire_responses.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\api\endpoints\questionnaire_responses.py" startline="50" type="function"></mcsymbol>)
  - **功能**: 获取指定用户的问卷回答列表。
  - **通讯链条**: 前端 `UserDashboard.vue` -> 后端 `/questionnaire_responses/user/{custom_id}` (GET)
- **DELETE /{response_id}** (<mcsymbol name="delete_questionnaire_response" filename="questionnaire_responses.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\api\endpoints\questionnaire_responses.py" startline="80" type="function"></mcsymbol>)
  - **功能**: 删除指定的问卷回答记录。
  - **通讯链条**: 前端 `UserDashboard.vue` -> 后端 `/questionnaire_responses/{response_id}` (DELETE)

### 2.5 API端口 (Endpoints) - `backend/app/api/endpoints/questionnaire_results.py`

- **GET /user/{custom_id}** (<mcsymbol name="get_user_questionnaire_results" filename="questionnaire_results.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\api\endpoints\questionnaire_results.py" startline="20" type="function"></mcsymbol>)
  - **功能**: 获取指定用户的问卷调查结果列表。
  - **通讯链条**: 前端 `UserDashboard.vue` -> 后端 `/questionnaire_results/user/{custom_id}` (GET)
- **POST /** (<mcsymbol name="create_questionnaire_result" filename="questionnaire_results.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\api\endpoints\questionnaire_results.py" startline="50" type="function"></mcsymbol>)
  - **功能**: 创建问卷调查结果。
  - **通讯链条**: 后端服务 (如问卷计算服务) -> 后端 `/questionnaire_results/` (POST)
- **GET /{result_id}** (<mcsymbol name="get_questionnaire_result" filename="questionnaire_results.py" path="c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app\api\endpoints\questionnaire_results.py" startline="80" type="function"></mcsymbol>)
  - **功能**: 获取指定问卷结果的详细信息。
  - **通讯链条**: 前端 `QuestionnaireResultDetail.vue` -> 后端 `/questionnaire_results/{result_id}` (GET)

### 标准量表和标准问卷的保存与字段记录

标准量表（如汉密尔顿抑郁量表）和标准问卷（如健康状况调查问卷）的定义，例如在 <mcfile name="hamilton_depression.py" path="YUN\backend\app\clinical_scales\assessment\hamilton_depression.py"></mcfile> 和 <mcfile name="health_questionnaire.py" path="YUN\backend\app\clinical_scales\questionnaire\health_questionnaire.py"></mcfile> 中定义的 `HAMILTON_DEPRESSION_TEMPLATE` 和 `HEALTH_QUESTIONNAIRE_TEMPLATE`，它们的数据结构是预定义的Python字典。

这些预定义的模板数据并**不会直接**作为独立的表存储在 `app.db` 数据库中。相反，它们通过以下方式保存和使用：

1.  **模板信息保存到 `AssessmentTemplate` 和 `QuestionnaireTemplate` 表中**：
    - 当系统初始化或通过管理界面导入新的标准量表/问卷时，这些模板的元数据（如 `id`, `template_key`, `name`, `version`, `description`, `instructions`, `category`, `type`, `status`, `scoring_method`, `max_score`, `dimensions`, `result_ranges` 等）会被提取并作为一条记录插入到 `AssessmentTemplate` 表（对于量表）或 `QuestionnaireTemplate` 表（对于问卷）中。
    - 例如，`HAMILTON_DEPRESSION_TEMPLATE` 中的 `id`, `template_key`, `name`, `version`, `description`, `instructions`, `category`, `type`, `status`, `scoring_method`, `max_score`, `dimensions`, `result_ranges` 等字段会直接映射到 `AssessmentTemplate` 表的相应列。
    - 同样，`HEALTH_QUESTIONNAIRE_TEMPLATE` 中的 `id`, `template_key`, `name`, `version`, `description`, `instructions`, `category`, `type`, `status`, `dimensions` 等字段会映射到 `QuestionnaireTemplate` 表的相应列。

2.  **问题信息保存到 `AssessmentTemplateQuestion` 和 `QuestionnaireTemplateQuestion` 表中**：
    - 模板中定义的 `questions` 列表中的每一个问题（包括 `question_id`, `question_text`, `question_type`, `options`, `scoring`, `order`, `is_required`, `dimension_key` 等）都会作为一条独立的记录插入到 `AssessmentTemplateQuestion` 表（对于量表问题）或 `QuestionnaireTemplateQuestion` 表（对于问卷问题）中。
    - 这些问题记录通过 `template_id` 字段与对应的 `AssessmentTemplate` 或 `QuestionnaireTemplate` 记录关联起来。

3.  **自动生成逻辑**：
    - 在 <mcfile name="assessment_generator.py" path="YUN\backend\app\clinical_scales\generators\assessment_generator.py"></mcfile> 和 <mcfile name="questionnaire_generator.py" path="YUN\backend\app\clinical_scales\generators\questionnaire_generator.py"></mcfile> 文件中，`AssessmentGenerator` 和 `QuestionnaireGenerator` 类提供了 `create_assessment_template` 和 `create_questionnaire_template` 方法。
    - 这些方法接收Python字典形式的模板数据，并负责将其解析、验证，然后将数据持久化到上述的 `AssessmentTemplate` / `QuestionnaireTemplate` 和 `AssessmentTemplateQuestion` / `QuestionnaireTemplateQuestion` 数据库表中。
    - 这意味着，无论是预定义的标准模板还是用户通过前端界面创建的自定义模板，它们最终都会以相同的结构存储在这些数据库表中。

4.  **所有字段是否全部记录在数据表中？**
    - **大部分核心字段都会被记录**。从 `AssessmentTemplate`、`AssessmentTemplateQuestion`、`QuestionnaireTemplate` 和 `QuestionnaireTemplateQuestion` 的模型定义来看，这些表的设计旨在存储模板的所有关键信息，包括元数据、维度信息、结果范围以及每个问题的详细属性（如文本、类型、选项、评分规则等）。
    - 某些在Python字典中可能存在的、仅用于代码逻辑或临时处理的辅助性字段（如果存在的话），可能不会直接映射到数据库表中，但核心的、用于定义量表/问卷结构和行为的字段都会被妥善存储。
    - 例如，`HAMILTON_DEPRESSION_TEMPLATE` 和 `HEALTH_QUESTIONNAIRE_TEMPLATE` 中定义的 `id`, `template_key`, `name`, `version`, `description`, `instructions`, `category`, `type`, `status` 等顶级字段，以及 `dimensions` 和 `result_ranges` （作为JSON类型）都会被保存。
    - `questions` 列表中的每个问题对象的所有字段（`question_id`, `question_text`, `question_type`, `options`, `scoring`, `order`, `is_required`, `dimension_key`）也都会被保存到对应的 `*TemplateQuestion` 表中。

总结来说，标准量表和标准问卷的定义以Python字典的形式存在于代码库中，但它们的核心结构和内容会通过 `AssessmentGenerator` 和 `QuestionnaireGenerator` 类的处理，被完整地解析并存储到 `AssessmentTemplate`、`AssessmentTemplateQuestion`、`QuestionnaireTemplate` 和 `QuestionnaireTemplateQuestion` 这些数据库表中。这意味着数据库中保存了构建和使用这些量表/问卷所需的所有必要信息。