# 评估量表和问卷全链路监控工具

## 概述

`assessment_questionnaire_monitor.py` 是一个全面的监控工具，用于跟踪评估量表和问卷的完整生命周期，从标准数据库到分发、状态标记、移动端获取、结果提交、后端计算报告、结果保存以及前端和移动端调阅报告和原始数据等各个环节。

## 主要功能

### 1. 全链路监控

工具监控以下8个关键阶段：

- **模板加载 (Template Loading)**: 检查标准模板数据库状态
- **分发 (Distribution)**: 跟踪评估和问卷的分发状态
- **移动端获取 (Mobile Fetch)**: 监控移动端获取状态
- **移动端提交 (Mobile Submit)**: 跟踪提交状态
- **后端计算 (Backend Calculation)**: 监控计算和报告生成
- **数据库保存 (Database Save)**: 验证数据保存状态
- **前端查询 (Frontend Query)**: 监控查询性能
- **移动端查询 (Mobile Query)**: 监控移动端查询状态

### 2. 问题诊断

- 自动检测常见问题
- 提供详细的错误信息和诊断建议
- 支持问题分类（错误、警告、正常）
- 生成问题修复建议

### 3. 数据完整性检查

- 检查缺失的字段和数据
- 验证数据一致性
- 检测重复记录
- 识别孤儿记录

### 4. 性能监控

- 查询性能分析
- 索引使用情况检查
- 数据库连接状态监控

## 安装和配置

### 前置条件

- Python 3.7+
- SQLite3 数据库
- 项目后端环境

### 使用方法

```bash
# 基本用法 - 运行完整检查
python assessment_questionnaire_monitor.py

# 检查指定用户的评估和问卷
python assessment_questionnaire_monitor.py --user-id 123

# 检查指定评估的状态
python assessment_questionnaire_monitor.py --assessment-id 456

# 检查指定问卷的状态
python assessment_questionnaire_monitor.py --questionnaire-id 789

# 监控指定模板的使用情况
python assessment_questionnaire_monitor.py --template-key "phq9"

# 自动修复发现的问题
python assessment_questionnaire_monitor.py --fix-issues

# 导出监控报告
python assessment_questionnaire_monitor.py --export-report report.json

# 实时监控模式（每60秒检查一次）
python assessment_questionnaire_monitor.py --real-time 60
```

## 命令行参数

| 参数 | 类型 | 描述 |
|------|------|------|
| `--user-id` | int | 监控指定用户的评估和问卷 |
| `--assessment-id` | int | 监控指定评估的状态 |
| `--questionnaire-id` | int | 监控指定问卷的状态 |
| `--template-key` | str | 监控指定模板的使用情况 |
| `--check-all` | flag | 检查所有评估和问卷的状态 |
| `--fix-issues` | flag | 自动修复发现的问题 |
| `--export-report` | str | 导出监控报告到指定文件 |
| `--real-time` | int | 实时监控模式，指定检查间隔（秒） |

## 监控检查项

### 1. 模板加载检查

- ✅ 评估模板数量
- ✅ 问卷模板数量
- ✅ template_key字段存在性
- ✅ template_key值完整性
- ⚠️ 缺少template_key字段
- ❌ 没有任何模板

### 2. 分发状态检查

- ✅ 分发状态统计
- ✅ 待完成分发数量
- ⚠️ 过期分发检测
- ❌ 分发表结构问题

### 3. 移动端获取检查

- ✅ template_id回填状态
- ⚠️ 缺少template_id的记录
- ❌ API端点问题

### 4. 提交状态检查

- ✅ 提交完成状态
- ✅ 答案数据完整性
- ⚠️ 状态不一致问题
- ❌ 记录不存在

### 5. 后端计算检查

- ✅ 计算结果完整性
- ⚠️ 缺少结论的评估
- ❌ 分数异常
- ❌ 缺少计算结果

### 6. 数据库保存检查

- ✅ 数据完整性
- ⚠️ 孤儿记录
- ❌ 缺少唯一标识符
- ❌ 重复标识符

### 7. 查询性能检查

- ✅ 查询响应时间
- ⚠️ 缺少索引
- ⚠️ 查询性能较慢
- ❌ 查询失败

## 输出格式

### 控制台输出

```
================================================================================
评估量表和问卷全链路监控报告
================================================================================
检查时间: 2024-01-15 14:30:25
总检查项: 15
发现问题: 3

------------------------------------------------------------
阶段: template_loading
------------------------------------------------------------
✅ [HEALTHY] 模板加载正常: 评估模板5个, 问卷模板3个
   assessment_template_count: 5
   questionnaire_template_count: 3
   has_assessment_template_key: True
   has_questionnaire_template_key: True

⚠️ [WARNING] 没有模板设置了template_key
   assessment_with_key: 0
   questionnaire_with_key: 0
   建议:
   - 运行迁移脚本填充template_key值
```

### JSON报告格式

```json
{
  "timestamp": "2024-01-15T14:30:25.123456",
  "total_checks": 15,
  "issues_count": 3,
  "results": [
    {
      "stage": "template_loading",
      "status": "healthy",
      "message": "模板加载正常: 评估模板5个, 问卷模板3个",
      "details": {
        "assessment_template_count": 5,
        "questionnaire_template_count": 3
      },
      "timestamp": "2024-01-15T14:30:25.123456",
      "suggestions": []
    }
  ],
  "issues": [
    {
      "stage": "template_loading",
      "status": "warning",
      "message": "没有模板设置了template_key",
      "details": {...},
      "timestamp": "2024-01-15T14:30:25.123456",
      "suggestions": ["运行迁移脚本填充template_key值"]
    }
  ]
}
```

## 自动修复功能

工具支持自动修复以下常见问题：

1. **缺少unique_identifier**: 自动生成唯一标识符
2. **数据不一致**: 修复状态和数据的不一致
3. **索引缺失**: 提供索引创建建议

使用 `--fix-issues` 参数启用自动修复：

```bash
python assessment_questionnaire_monitor.py --fix-issues
```

## 实时监控

实时监控模式可以持续监控系统状态：

```bash
# 每60秒检查一次
python assessment_questionnaire_monitor.py --real-time 60

# 每5分钟检查一次
python assessment_questionnaire_monitor.py --real-time 300
```

实时监控输出：

```
[2024-01-15 14:30:25] 开始检查...
✅ 所有检查通过

[2024-01-15 14:31:25] 开始检查...
⚠️ 发现2个警告

[2024-01-15 14:32:25] 开始检查...
❌ 发现1个错误
```

## 日志记录

工具会生成详细的日志文件 `assessment_monitor.log`：

```
2024-01-15 14:30:25,123 - __main__ - INFO - 开始运行完整检查...
2024-01-15 14:30:25,124 - __main__ - INFO - 检查模板加载状态...
2024-01-15 14:30:25,125 - __main__ - INFO - 找到数据库文件: /path/to/app.db
2024-01-15 14:30:25,130 - __main__ - INFO - 检查分发状态... (用户ID: 所有用户)
```

## 集成建议

### 1. 定期监控

建议设置定期任务（如cron job）来运行监控：

```bash
# 每小时运行一次完整检查
0 * * * * /path/to/python /path/to/assessment_questionnaire_monitor.py --export-report /path/to/reports/hourly_$(date +\%Y\%m\%d_\%H).json

# 每天运行一次并修复问题
0 2 * * * /path/to/python /path/to/assessment_questionnaire_monitor.py --fix-issues --export-report /path/to/reports/daily_$(date +\%Y\%m\%d).json
```

### 2. 告警集成

可以结合监控结果设置告警：

```bash
#!/bin/bash
# 运行监控并检查退出码
python assessment_questionnaire_monitor.py
if [ $? -ne 0 ]; then
    # 发送告警邮件或通知
    echo "发现系统问题，请检查监控报告" | mail -s "健康评估系统告警" <EMAIL>
fi
```

### 3. 性能优化

根据监控结果优化系统性能：

- 创建建议的索引
- 优化慢查询
- 清理冗余数据
- 修复数据完整性问题

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库文件路径
   - 验证文件权限
   - 确认数据库文件存在

2. **模块导入错误**
   - 确认Python路径配置
   - 检查依赖包安装
   - 验证项目结构

3. **权限问题**
   - 检查文件读写权限
   - 确认数据库访问权限
   - 验证日志文件写入权限

### 调试模式

启用详细日志记录：

```python
# 在脚本开头修改日志级别
logging.basicConfig(level=logging.DEBUG)
```

## 扩展开发

### 添加新的检查项

```python
def check_custom_feature(self) -> bool:
    """检查自定义功能"""
    try:
        # 实现检查逻辑
        conn = self.get_database_connection()
        cursor = conn.cursor()
        
        # 执行检查
        # ...
        
        self.add_monitor_result(
            WorkflowStage.CUSTOM_STAGE,
            MonitorStatus.HEALTHY,
            "自定义功能正常",
            details
        )
        return True
        
    except Exception as e:
        self.add_monitor_result(
            WorkflowStage.CUSTOM_STAGE,
            MonitorStatus.ERROR,
            f"检查自定义功能失败: {str(e)}",
            {'error': str(e)},
            ["检查自定义功能配置"]
        )
        return False
```

### 添加新的修复功能

```python
def fix_custom_issue(self):
    """修复自定义问题"""
    try:
        conn = self.get_database_connection()
        cursor = conn.cursor()
        
        # 实现修复逻辑
        # ...
        
        conn.commit()
        logger.info("自定义问题修复完成")
        
    except Exception as e:
        logger.error(f"修复自定义问题失败: {e}")
        if conn:
            conn.rollback()
```

## 版本历史

- **v1.0.0** (2024-01-15)
  - 初始版本
  - 支持8个阶段的全链路监控
  - 基本的问题检测和修复功能
  - 实时监控模式
  - JSON报告导出

## 许可证

本工具遵循项目的许可证协议。

## 支持

如有问题或建议，请联系开发团队或提交issue。