2025-06-30 21:23:03,945 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 21:23:03,951 - auth_service - INFO - 统一认证服务初始化完成
2025-06-30 21:23:04,107 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 21:23:04,110 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 21:23:06,169 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-06-30 21:23:06,172 - fallback_manager - DEBUG - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-06-30 21:23:06,181 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-06-30 21:23:06,377 - health_monitor - INFO - 健康监控器初始化完成
2025-06-30 21:23:06,383 - app.core.system_monitor - INFO - 已加载 16 个历史数据点
2025-06-30 21:23:06,391 - app.core.alert_detector - INFO - 已加载 6 个告警规则
2025-06-30 21:23:06,402 - app.core.alert_detector - INFO - 已加载 0 个当前告警
2025-06-30 21:23:06,404 - app.core.alert_detector - INFO - 已加载 3 个历史告警
2025-06-30 21:23:06,406 - app.core.alert_detector - INFO - 已加载 1 个通知渠道
2025-06-30 21:23:06,411 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 21:23:06,419 - alert_manager - INFO - 已初始化默认告警规则
2025-06-30 21:23:06,421 - alert_manager - INFO - 已初始化默认通知渠道
2025-06-30 21:23:06,423 - alert_manager - INFO - 告警管理器初始化完成
2025-06-30 21:23:07,193 - db_service - INFO - 数据库引擎和会话工厂创建成功
2025-06-30 21:23:07,196 - db_service - INFO - 数据库服务初始化完成
2025-06-30 21:23:07,232 - notification_service - INFO - 通知服务初始化完成
2025-06-30 21:23:07,261 - main - INFO - 错误处理模块导入成功
2025-06-30 21:23:07,331 - main - INFO - 监控模块导入成功
2025-06-30 21:23:07,362 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-06-30 21:23:18,890 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 21:23:18,901 - auth_service - INFO - 统一认证服务初始化完成
2025-06-30 21:23:19,069 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 21:23:19,074 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 21:23:21,652 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-06-30 21:23:21,689 - fallback_manager - DEBUG - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-06-30 21:23:21,743 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-06-30 21:23:22,237 - health_monitor - INFO - 健康监控器初始化完成
2025-06-30 21:23:22,249 - app.core.system_monitor - INFO - 已加载 16 个历史数据点
2025-06-30 21:23:22,267 - app.core.alert_detector - INFO - 已加载 6 个告警规则
2025-06-30 21:23:22,279 - app.core.alert_detector - INFO - 已加载 0 个当前告警
2025-06-30 21:23:22,282 - app.core.alert_detector - INFO - 已加载 3 个历史告警
2025-06-30 21:23:22,284 - app.core.alert_detector - INFO - 已加载 1 个通知渠道
2025-06-30 21:23:22,289 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 21:23:22,302 - alert_manager - INFO - 已初始化默认告警规则
2025-06-30 21:23:22,305 - alert_manager - INFO - 已初始化默认通知渠道
2025-06-30 21:23:22,323 - alert_manager - INFO - 告警管理器初始化完成
2025-06-30 21:23:24,305 - db_service - INFO - 数据库引擎和会话工厂创建成功
2025-06-30 21:23:24,325 - db_service - INFO - 数据库服务初始化完成
2025-06-30 21:23:24,349 - notification_service - INFO - 通知服务初始化完成
2025-06-30 21:23:24,371 - main - INFO - 错误处理模块导入成功
2025-06-30 21:23:24,493 - main - INFO - 监控模块导入成功
2025-06-30 21:23:24,524 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-06-30 21:23:28,222 - asyncio - DEBUG - Using selector: SelectSelector
2025-06-30 21:23:28,362 - main - INFO - 错误处理模块导入成功
2025-06-30 21:23:28,364 - main - INFO - 监控模块导入成功
2025-06-30 21:23:28,365 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-06-30 21:23:29,692 - main - INFO - 应用启动中...
2025-06-30 21:23:29,694 - error_handling - INFO - 错误处理已设置
2025-06-30 21:23:29,696 - main - INFO - 错误处理系统初始化完成
2025-06-30 21:23:29,698 - monitoring - INFO - 添加指标端点成功: /metrics
2025-06-30 21:23:29,699 - monitoring - INFO - 添加健康检查端点成功: /health
2025-06-30 21:23:29,701 - monitoring - INFO - 添加详细健康检查端点成功: /api/health/detailed
2025-06-30 21:23:29,703 - monitoring - INFO - 系统信息初始化完成: Markey, Windows-10-10.0.19045-SP0, Python 3.13.2, CPU核心数: 4
2025-06-30 21:23:29,712 - monitoring - INFO - 启动资源监控线程成功
2025-06-30 21:23:29,714 - monitoring - INFO - 监控系统初始化成功（不使用中间件）
2025-06-30 21:23:29,715 - monitoring - INFO - 监控系统初始化完成
2025-06-30 21:23:29,716 - main - INFO - 监控系统初始化完成
2025-06-30 21:23:29,719 - app.db.init_db - INFO - 所有模型导入成功
2025-06-30 21:23:29,720 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-06-30 21:23:29,725 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-30 21:23:29,726 - app.db.init_db - INFO - 所有模型导入成功
2025-06-30 21:23:29,728 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-06-30 21:23:29,730 - app.db.init_db - INFO - 正在运行数据库迁移...
2025-06-30 21:23:29,731 - app.db.init_db - INFO - 正在检查并更新数据库表结构...
2025-06-30 21:23:29,734 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-30 21:23:29,737 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alerts")
2025-06-30 21:23:29,739 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,743 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_rules")
2025-06-30 21:23:29,745 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,748 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_channels")
2025-06-30 21:23:29,749 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,751 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-06-30 21:23:29,753 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,756 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_records")
2025-06-30 21:23:29,759 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,761 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_overviews")
2025-06-30 21:23:29,764 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,767 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medical_records")
2025-06-30 21:23:29,771 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,774 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("inpatient_records")
2025-06-30 21:23:29,777 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,779 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("surgery_records")
2025-06-30 21:23:29,782 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,785 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_reports")
2025-06-30 21:23:29,789 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,791 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_report_items")
2025-06-30 21:23:29,794 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,795 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_reports")
2025-06-30 21:23:29,798 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,800 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("examination_reports")
2025-06-30 21:23:29,802 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,805 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("documents")
2025-06-30 21:23:29,807 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,810 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("other_records")
2025-06-30 21:23:29,812 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,814 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.5%, CPU使用率 75.0%
2025-06-30 21:23:29,816 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("operation_logs")
2025-06-30 21:23:29,818 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,820 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("follow_up_records")
2025-06-30 21:23:29,822 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,824 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_diaries")
2025-06-30 21:23:29,827 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,829 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("registration_records")
2025-06-30 21:23:29,831 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,833 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("prescription_records")
2025-06-30 21:23:29,835 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,837 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("laboratory_records")
2025-06-30 21:23:29,840 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,845 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_records")
2025-06-30 21:23:29,848 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,852 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medications")
2025-06-30 21:23:29,859 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,861 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medication_usages")
2025-06-30 21:23:29,864 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,866 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("role_applications")
2025-06-30 21:23:29,868 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,870 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessments")
2025-06-30 21:23:29,872 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,875 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_items")
2025-06-30 21:23:29,877 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,879 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_responses")
2025-06-30 21:23:29,881 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,884 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_templates")
2025-06-30 21:23:29,886 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,888 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_template_questions")
2025-06-30 21:23:29,891 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,893 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaires")
2025-06-30 21:23:29,895 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,899 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_items")
2025-06-30 21:23:29,900 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,902 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_responses")
2025-06-30 21:23:29,904 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,907 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_answers")
2025-06-30 21:23:29,909 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,911 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_templates")
2025-06-30 21:23:29,914 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,916 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_template_questions")
2025-06-30 21:23:29,918 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,920 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_distributions")
2025-06-30 21:23:29,922 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,925 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_distributions")
2025-06-30 21:23:29,927 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,929 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_results")
2025-06-30 21:23:29,932 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,934 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_results")
2025-06-30 21:23:29,936 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,938 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("report_templates")
2025-06-30 21:23:29,941 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,943 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("service_stats")
2025-06-30 21:23:29,946 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:23:29,952 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-30 21:23:29,969 - app.db.init_db - INFO - 数据库表结构检查和更新完成
2025-06-30 21:23:29,974 - app.db.init_db - INFO - 模型关系初始化完成
2025-06-30 21:23:29,975 - app.db.init_db - INFO - 模型关系设置完成
2025-06-30 21:23:29,977 - main - INFO - 数据库初始化完成（强制重建）
2025-06-30 21:23:29,979 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-30 21:23:29,981 - main - INFO - 数据库连接正常
2025-06-30 21:23:29,983 - main - INFO - 开始初始化模板数据
2025-06-30 21:23:29,984 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-30 21:23:30,261 - app.db.init_templates - INFO - 开始初始化评估量表模板...
2025-06-30 21:23:30,311 - app.db.init_templates - INFO - 成功初始化 5 个评估量表模板
2025-06-30 21:23:30,336 - app.db.init_templates - INFO - 开始初始化调查问卷模板...
2025-06-30 21:23:30,410 - app.db.init_templates - INFO - 成功初始化 5 个调查问卷模板
2025-06-30 21:23:30,415 - main - INFO - 模板数据初始化完成
2025-06-30 21:23:30,419 - main - INFO - 数据库表结构已经标准化，不需要迁移
2025-06-30 21:23:30,424 - main - INFO - 应用启动完成
2025-06-30 21:23:45,042 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.5%, CPU使用率 100.0%
2025-06-30 21:23:50,277 - main - INFO - 
--- 请求开始: GET /api/management/status ---
2025-06-30 21:23:50,280 - main - INFO - 请求没有认证头部
2025-06-30 21:23:50,281 - main - INFO - 没有认证头部，设置用户为None
2025-06-30 21:23:50,285 - main - INFO - --- 请求结束: 404 ---

2025-06-30 21:23:56,001 - main - INFO - 
--- 请求开始: GET /openapi.json ---
2025-06-30 21:23:56,003 - main - INFO - 请求没有认证头部
2025-06-30 21:23:56,005 - main - INFO - 没有认证头部，设置用户为None
2025-06-30 21:23:57,657 - main - INFO - --- 请求结束: 200 ---

2025-06-30 21:24:00,152 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.7%, CPU使用率 67.9%
2025-06-30 21:24:03,277 - main - INFO - 
--- 请求开始: GET /openapi.json ---
2025-06-30 21:24:03,309 - main - INFO - 请求没有认证头部
2025-06-30 21:24:03,313 - main - INFO - 没有认证头部，设置用户为None
2025-06-30 21:24:03,327 - main - INFO - --- 请求结束: 200 ---

2025-06-30 21:24:03,769 - main - INFO - 
--- 请求开始: GET /favicon.ico ---
2025-06-30 21:24:03,796 - main - INFO - 请求没有认证头部
2025-06-30 21:24:03,813 - main - INFO - 没有认证头部，设置用户为None
2025-06-30 21:24:03,817 - main - INFO - --- 请求结束: 404 ---

2025-06-30 21:24:07,242 - health_monitor - DEBUG - 系统指标 - CPU: 80.0%, 内存: 55.5%, 磁盘: 76.4%
2025-06-30 21:24:15,262 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.0%, CPU使用率 71.4%
2025-06-30 21:24:18,035 - main - INFO - 
--- 请求开始: GET /api/management/alerts ---
2025-06-30 21:24:18,040 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-30 21:24:18,046 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-30 21:24:18,047 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-30 21:24:18,077 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-30 21:24:18,086 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-30 21:24:18,088 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-30 21:24:18,094 - main - INFO - --- 请求结束: 404 ---

2025-06-30 21:24:18,378 - main - INFO - 
--- 请求开始: GET /api/management/metrics ---
2025-06-30 21:24:18,380 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-30 21:24:18,381 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-30 21:24:18,382 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-30 21:24:18,383 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-30 21:24:18,386 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-30 21:24:18,387 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-30 21:24:18,393 - main - INFO - 
--- 请求开始: GET /api/management/status ---
2025-06-30 21:24:18,394 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-30 21:24:18,395 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-30 21:24:18,396 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-30 21:24:18,398 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-30 21:24:18,400 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-30 21:24:18,401 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-30 21:24:18,404 - main - INFO - --- 请求结束: 404 ---

2025-06-30 21:24:18,410 - main - INFO - --- 请求结束: 404 ---

2025-06-30 21:24:18,722 - main - INFO - 
--- 请求开始: GET /api/management/status ---
2025-06-30 21:24:18,724 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-30 21:24:18,725 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-30 21:24:18,726 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-30 21:24:18,727 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-30 21:24:18,729 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-30 21:24:18,730 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-30 21:24:18,734 - main - INFO - --- 请求结束: 404 ---

2025-06-30 21:24:23,014 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 56.1%, 磁盘: 76.4%
2025-06-30 21:24:30,371 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.0%, CPU使用率 72.0%
2025-06-30 21:24:45,477 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.0%, CPU使用率 85.7%
2025-06-30 21:25:00,625 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.6%, CPU使用率 100.0%
2025-06-30 21:25:08,362 - health_monitor - DEBUG - 系统指标 - CPU: 77.6%, 内存: 55.8%, 磁盘: 76.4%
2025-06-30 21:25:15,799 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.7%, CPU使用率 64.5%
2025-06-30 21:25:18,327 - main - INFO - 
--- 请求开始: GET /api/management/alerts ---
2025-06-30 21:25:18,329 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-30 21:25:18,330 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-30 21:25:18,331 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-30 21:25:18,335 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-30 21:25:18,338 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-30 21:25:18,339 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-30 21:25:18,341 - main - INFO - 
--- 请求开始: GET /api/management/metrics ---
2025-06-30 21:25:18,342 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-30 21:25:18,343 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-30 21:25:18,345 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-30 21:25:18,347 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-30 21:25:18,348 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-30 21:25:18,350 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-30 21:25:18,353 - main - INFO - --- 请求结束: 404 ---

2025-06-30 21:25:18,356 - main - INFO - --- 请求结束: 404 ---

2025-06-30 21:25:18,360 - main - INFO - 
--- 请求开始: GET /api/management/status ---
2025-06-30 21:25:18,365 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-30 21:25:18,368 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-30 21:25:18,371 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-30 21:25:18,381 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-30 21:25:18,385 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-30 21:25:18,386 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-30 21:25:18,391 - main - INFO - --- 请求结束: 404 ---

2025-06-30 21:25:18,620 - main - INFO - 
--- 请求开始: GET /api/management/status ---
2025-06-30 21:25:18,688 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-30 21:25:18,691 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-30 21:25:18,693 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-30 21:25:18,695 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-30 21:25:18,698 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-30 21:25:18,699 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-30 21:25:18,703 - main - INFO - --- 请求结束: 404 ---

2025-06-30 21:25:24,120 - health_monitor - DEBUG - 系统指标 - CPU: 83.8%, 内存: 55.6%, 磁盘: 76.4%
2025-06-30 21:25:30,906 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.9%, CPU使用率 92.3%
2025-06-30 21:25:46,125 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.7%, CPU使用率 100.0%
2025-06-30 21:25:59,523 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 21:25:59,529 - auth_service - INFO - 统一认证服务初始化完成
2025-06-30 21:25:59,617 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 21:25:59,620 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 21:26:00,949 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-06-30 21:26:00,951 - fallback_manager - DEBUG - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-06-30 21:26:00,957 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-06-30 21:26:01,144 - health_monitor - INFO - 健康监控器初始化完成
2025-06-30 21:26:01,150 - app.core.system_monitor - INFO - 已加载 16 个历史数据点
2025-06-30 21:26:01,157 - app.core.alert_detector - INFO - 已加载 6 个告警规则
2025-06-30 21:26:01,159 - app.core.alert_detector - INFO - 已加载 0 个当前告警
2025-06-30 21:26:01,161 - app.core.alert_detector - INFO - 已加载 3 个历史告警
2025-06-30 21:26:01,162 - app.core.alert_detector - INFO - 已加载 1 个通知渠道
2025-06-30 21:26:01,167 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 21:26:01,174 - alert_manager - INFO - 已初始化默认告警规则
2025-06-30 21:26:01,175 - alert_manager - INFO - 已初始化默认通知渠道
2025-06-30 21:26:01,176 - alert_manager - INFO - 告警管理器初始化完成
2025-06-30 21:26:02,421 - db_service - INFO - 数据库引擎和会话工厂创建成功
2025-06-30 21:26:02,423 - db_service - INFO - 数据库服务初始化完成
2025-06-30 21:26:02,435 - notification_service - INFO - 通知服务初始化完成
2025-06-30 21:26:02,438 - main - INFO - 错误处理模块导入成功
2025-06-30 21:26:02,510 - main - INFO - 监控模块导入成功
2025-06-30 21:26:02,512 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-06-30 21:26:15,317 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 21:26:15,340 - auth_service - INFO - 统一认证服务初始化完成
2025-06-30 21:26:15,654 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 21:26:15,710 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 21:26:20,268 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-06-30 21:26:20,318 - fallback_manager - DEBUG - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-06-30 21:26:20,343 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-06-30 21:26:20,942 - health_monitor - INFO - 健康监控器初始化完成
2025-06-30 21:26:20,982 - app.core.system_monitor - INFO - 已加载 16 个历史数据点
2025-06-30 21:26:21,073 - app.core.alert_detector - INFO - 已加载 6 个告警规则
2025-06-30 21:26:21,171 - app.core.alert_detector - INFO - 已加载 0 个当前告警
2025-06-30 21:26:21,325 - app.core.alert_detector - INFO - 已加载 3 个历史告警
2025-06-30 21:26:21,433 - app.core.alert_detector - INFO - 已加载 1 个通知渠道
2025-06-30 21:26:21,524 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 21:26:21,533 - alert_manager - INFO - 已初始化默认告警规则
2025-06-30 21:26:21,534 - alert_manager - INFO - 已初始化默认通知渠道
2025-06-30 21:26:21,562 - alert_manager - INFO - 告警管理器初始化完成
2025-06-30 21:26:23,255 - db_service - INFO - 数据库引擎和会话工厂创建成功
2025-06-30 21:26:23,289 - db_service - INFO - 数据库服务初始化完成
2025-06-30 21:26:23,326 - notification_service - INFO - 通知服务初始化完成
2025-06-30 21:26:23,416 - main - INFO - 错误处理模块导入成功
2025-06-30 21:26:23,544 - main - INFO - 监控模块导入成功
2025-06-30 21:26:23,555 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-06-30 21:26:29,056 - asyncio - DEBUG - Using selector: SelectSelector
2025-06-30 21:26:29,273 - main - INFO - 错误处理模块导入成功
2025-06-30 21:26:29,339 - main - INFO - 监控模块导入成功
2025-06-30 21:26:29,429 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-06-30 21:26:31,479 - main - INFO - 应用启动中...
2025-06-30 21:26:31,514 - error_handling - INFO - 错误处理已设置
2025-06-30 21:26:31,554 - main - INFO - 错误处理系统初始化完成
2025-06-30 21:26:31,654 - monitoring - INFO - 添加指标端点成功: /metrics
2025-06-30 21:26:31,757 - monitoring - INFO - 添加健康检查端点成功: /health
2025-06-30 21:26:31,805 - monitoring - INFO - 添加详细健康检查端点成功: /api/health/detailed
2025-06-30 21:26:31,862 - monitoring - INFO - 系统信息初始化完成: Markey, Windows-10-10.0.19045-SP0, Python 3.13.2, CPU核心数: 4
2025-06-30 21:26:31,953 - monitoring - INFO - 启动资源监控线程成功
2025-06-30 21:26:31,979 - monitoring - INFO - 监控系统初始化成功（不使用中间件）
2025-06-30 21:26:32,003 - monitoring - INFO - 监控系统初始化完成
2025-06-30 21:26:32,053 - main - INFO - 监控系统初始化完成
2025-06-30 21:26:32,084 - app.db.init_db - INFO - 所有模型导入成功
2025-06-30 21:26:32,220 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.3%, CPU使用率 100.0%
2025-06-30 21:26:32,267 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-06-30 21:26:32,618 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-30 21:26:32,779 - app.db.init_db - INFO - 所有模型导入成功
2025-06-30 21:26:32,981 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-06-30 21:26:33,070 - app.db.init_db - INFO - 正在运行数据库迁移...
2025-06-30 21:26:33,172 - app.db.init_db - INFO - 正在检查并更新数据库表结构...
2025-06-30 21:26:33,263 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-30 21:26:33,374 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alerts")
2025-06-30 21:26:33,441 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:33,569 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_rules")
2025-06-30 21:26:33,674 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:33,828 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_channels")
2025-06-30 21:26:33,999 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:34,182 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-06-30 21:26:34,316 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:34,450 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_records")
2025-06-30 21:26:34,623 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:34,845 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_overviews")
2025-06-30 21:26:34,960 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:35,123 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medical_records")
2025-06-30 21:26:35,353 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:35,844 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("inpatient_records")
2025-06-30 21:26:36,005 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,058 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("surgery_records")
2025-06-30 21:26:36,240 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,297 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_reports")
2025-06-30 21:26:36,318 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,338 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_report_items")
2025-06-30 21:26:36,350 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,363 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_reports")
2025-06-30 21:26:36,380 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,388 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("examination_reports")
2025-06-30 21:26:36,391 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,398 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("documents")
2025-06-30 21:26:36,401 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,404 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("other_records")
2025-06-30 21:26:36,416 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,419 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("operation_logs")
2025-06-30 21:26:36,422 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,430 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("follow_up_records")
2025-06-30 21:26:36,438 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,442 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_diaries")
2025-06-30 21:26:36,452 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,463 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("registration_records")
2025-06-30 21:26:36,470 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,474 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("prescription_records")
2025-06-30 21:26:36,480 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,483 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("laboratory_records")
2025-06-30 21:26:36,487 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,499 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_records")
2025-06-30 21:26:36,501 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,508 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medications")
2025-06-30 21:26:36,515 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,519 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medication_usages")
2025-06-30 21:26:36,522 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,529 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("role_applications")
2025-06-30 21:26:36,532 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,535 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessments")
2025-06-30 21:26:36,538 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,541 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_items")
2025-06-30 21:26:36,546 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,551 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_responses")
2025-06-30 21:26:36,554 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,564 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_templates")
2025-06-30 21:26:36,568 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,571 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_template_questions")
2025-06-30 21:26:36,573 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,579 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaires")
2025-06-30 21:26:36,583 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,586 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_items")
2025-06-30 21:26:36,589 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,595 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_responses")
2025-06-30 21:26:36,599 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,602 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_answers")
2025-06-30 21:26:36,605 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,614 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_templates")
2025-06-30 21:26:36,616 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,619 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_template_questions")
2025-06-30 21:26:36,623 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,629 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_distributions")
2025-06-30 21:26:36,632 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,638 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_distributions")
2025-06-30 21:26:36,641 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,649 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_results")
2025-06-30 21:26:36,654 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,657 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_results")
2025-06-30 21:26:36,663 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,667 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("report_templates")
2025-06-30 21:26:36,698 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,702 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("service_stats")
2025-06-30 21:26:36,706 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:26:36,713 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-30 21:26:36,715 - app.db.init_db - INFO - 数据库表结构检查和更新完成
2025-06-30 21:26:36,751 - app.db.init_db - INFO - 模型关系初始化完成
2025-06-30 21:26:36,802 - app.db.init_db - INFO - 模型关系设置完成
2025-06-30 21:26:36,816 - main - INFO - 数据库初始化完成（强制重建）
2025-06-30 21:26:36,822 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-30 21:26:36,829 - main - INFO - 数据库连接正常
2025-06-30 21:26:36,832 - main - INFO - 开始初始化模板数据
2025-06-30 21:26:36,834 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-30 21:26:37,302 - app.db.init_templates - INFO - 开始初始化评估量表模板...
2025-06-30 21:26:37,377 - app.db.init_templates - INFO - 成功初始化 5 个评估量表模板
2025-06-30 21:26:37,408 - app.db.init_templates - INFO - 开始初始化调查问卷模板...
2025-06-30 21:26:37,652 - app.db.init_templates - INFO - 成功初始化 5 个调查问卷模板
2025-06-30 21:26:37,732 - main - INFO - 模板数据初始化完成
2025-06-30 21:26:37,767 - main - INFO - 数据库表结构已经标准化，不需要迁移
2025-06-30 21:26:37,871 - main - INFO - 应用启动完成
2025-06-30 21:27:36,770 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 21:27:36,777 - auth_service - INFO - 统一认证服务初始化完成
2025-06-30 21:27:36,879 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 21:27:36,881 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 21:27:38,835 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-06-30 21:27:38,836 - fallback_manager - DEBUG - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-06-30 21:27:38,841 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-06-30 21:27:38,998 - health_monitor - INFO - 健康监控器初始化完成
2025-06-30 21:27:39,001 - app.core.system_monitor - INFO - 已加载 16 个历史数据点
2025-06-30 21:27:39,005 - app.core.alert_detector - INFO - 已加载 6 个告警规则
2025-06-30 21:27:39,007 - app.core.alert_detector - INFO - 已加载 0 个当前告警
2025-06-30 21:27:39,008 - app.core.alert_detector - INFO - 已加载 3 个历史告警
2025-06-30 21:27:39,009 - app.core.alert_detector - INFO - 已加载 1 个通知渠道
2025-06-30 21:27:39,012 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 21:27:39,020 - alert_manager - INFO - 已初始化默认告警规则
2025-06-30 21:27:39,021 - alert_manager - INFO - 已初始化默认通知渠道
2025-06-30 21:27:39,022 - alert_manager - INFO - 告警管理器初始化完成
2025-06-30 21:27:39,788 - db_service - INFO - 数据库引擎和会话工厂创建成功
2025-06-30 21:27:39,796 - db_service - INFO - 数据库服务初始化完成
2025-06-30 21:27:39,832 - notification_service - INFO - 通知服务初始化完成
2025-06-30 21:27:39,863 - main - INFO - 错误处理模块导入成功
2025-06-30 21:27:39,917 - main - INFO - 监控模块导入成功
2025-06-30 21:27:39,918 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-06-30 21:27:50,061 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 21:27:50,075 - auth_service - INFO - 统一认证服务初始化完成
2025-06-30 21:27:50,335 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 21:27:50,397 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 21:27:52,939 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-06-30 21:27:53,041 - fallback_manager - DEBUG - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-06-30 21:27:53,086 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-06-30 21:27:53,366 - health_monitor - INFO - 健康监控器初始化完成
2025-06-30 21:27:53,371 - app.core.system_monitor - INFO - 已加载 16 个历史数据点
2025-06-30 21:27:53,379 - app.core.alert_detector - INFO - 已加载 6 个告警规则
2025-06-30 21:27:53,385 - app.core.alert_detector - INFO - 已加载 0 个当前告警
2025-06-30 21:27:53,386 - app.core.alert_detector - INFO - 已加载 3 个历史告警
2025-06-30 21:27:53,389 - app.core.alert_detector - INFO - 已加载 1 个通知渠道
2025-06-30 21:27:53,402 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 21:27:53,413 - alert_manager - INFO - 已初始化默认告警规则
2025-06-30 21:27:53,419 - alert_manager - INFO - 已初始化默认通知渠道
2025-06-30 21:27:53,421 - alert_manager - INFO - 告警管理器初始化完成
2025-06-30 21:27:55,034 - db_service - INFO - 数据库引擎和会话工厂创建成功
2025-06-30 21:27:55,054 - db_service - INFO - 数据库服务初始化完成
2025-06-30 21:27:55,118 - notification_service - INFO - 通知服务初始化完成
2025-06-30 21:27:55,166 - main - INFO - 错误处理模块导入成功
2025-06-30 21:27:55,293 - main - INFO - 监控模块导入成功
2025-06-30 21:27:55,385 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-06-30 21:27:58,572 - asyncio - DEBUG - Using selector: SelectSelector
2025-06-30 21:27:58,715 - main - INFO - 错误处理模块导入成功
2025-06-30 21:27:58,717 - main - INFO - 监控模块导入成功
2025-06-30 21:27:58,718 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-06-30 21:28:00,535 - main - INFO - 应用启动中...
2025-06-30 21:28:00,586 - error_handling - INFO - 错误处理已设置
2025-06-30 21:28:00,610 - main - INFO - 错误处理系统初始化完成
2025-06-30 21:28:00,635 - monitoring - INFO - 添加指标端点成功: /metrics
2025-06-30 21:28:00,642 - monitoring - INFO - 添加健康检查端点成功: /health
2025-06-30 21:28:00,655 - monitoring - INFO - 添加详细健康检查端点成功: /api/health/detailed
2025-06-30 21:28:00,658 - monitoring - INFO - 系统信息初始化完成: Markey, Windows-10-10.0.19045-SP0, Python 3.13.2, CPU核心数: 4
2025-06-30 21:28:00,676 - monitoring - INFO - 启动资源监控线程成功
2025-06-30 21:28:00,699 - monitoring - INFO - 监控系统初始化成功（不使用中间件）
2025-06-30 21:28:00,703 - monitoring - INFO - 监控系统初始化完成
2025-06-30 21:28:00,705 - main - INFO - 监控系统初始化完成
2025-06-30 21:28:00,738 - app.db.init_db - INFO - 所有模型导入成功
2025-06-30 21:28:00,743 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-06-30 21:28:00,754 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-30 21:28:00,784 - app.db.init_db - INFO - 所有模型导入成功
2025-06-30 21:28:00,788 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-06-30 21:28:00,790 - app.db.init_db - INFO - 正在运行数据库迁移...
2025-06-30 21:28:00,793 - app.db.init_db - INFO - 正在检查并更新数据库表结构...
2025-06-30 21:28:00,805 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.4%, CPU使用率 100.0%
2025-06-30 21:28:00,807 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-30 21:28:00,817 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alerts")
2025-06-30 21:28:00,822 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:00,828 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_rules")
2025-06-30 21:28:00,835 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:00,838 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_channels")
2025-06-30 21:28:00,841 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:00,843 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-06-30 21:28:00,850 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:00,859 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_records")
2025-06-30 21:28:00,869 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:00,872 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_overviews")
2025-06-30 21:28:00,875 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:00,883 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medical_records")
2025-06-30 21:28:00,888 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:00,891 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("inpatient_records")
2025-06-30 21:28:00,901 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:00,904 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("surgery_records")
2025-06-30 21:28:00,908 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:00,921 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_reports")
2025-06-30 21:28:00,925 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:00,932 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_report_items")
2025-06-30 21:28:00,946 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:00,956 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_reports")
2025-06-30 21:28:00,961 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:00,971 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("examination_reports")
2025-06-30 21:28:00,986 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:00,989 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("documents")
2025-06-30 21:28:01,006 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:01,039 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("other_records")
2025-06-30 21:28:01,067 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:01,076 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("operation_logs")
2025-06-30 21:28:01,086 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:01,090 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("follow_up_records")
2025-06-30 21:28:01,093 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:01,102 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_diaries")
2025-06-30 21:28:01,108 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:01,110 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("registration_records")
2025-06-30 21:28:01,119 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:01,122 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("prescription_records")
2025-06-30 21:28:01,124 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:01,127 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("laboratory_records")
2025-06-30 21:28:01,173 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:01,205 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_records")
2025-06-30 21:28:01,209 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:01,214 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medications")
2025-06-30 21:28:01,219 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:01,222 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medication_usages")
2025-06-30 21:28:01,224 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:01,227 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("role_applications")
2025-06-30 21:28:01,235 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:01,238 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessments")
2025-06-30 21:28:01,241 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:01,244 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_items")
2025-06-30 21:28:01,252 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:01,255 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_responses")
2025-06-30 21:28:01,257 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:01,260 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_templates")
2025-06-30 21:28:01,270 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:01,273 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_template_questions")
2025-06-30 21:28:01,276 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:01,284 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaires")
2025-06-30 21:28:01,287 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:01,290 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_items")
2025-06-30 21:28:01,292 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:01,304 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_responses")
2025-06-30 21:28:01,306 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:01,310 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_answers")
2025-06-30 21:28:01,318 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:01,320 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_templates")
2025-06-30 21:28:01,357 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:01,360 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_template_questions")
2025-06-30 21:28:01,367 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:01,374 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_distributions")
2025-06-30 21:28:01,377 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:01,386 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_distributions")
2025-06-30 21:28:01,390 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:01,392 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_results")
2025-06-30 21:28:01,400 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:01,403 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_results")
2025-06-30 21:28:01,406 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:01,408 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("report_templates")
2025-06-30 21:28:01,451 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:01,472 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("service_stats")
2025-06-30 21:28:01,475 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-30 21:28:01,483 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-30 21:28:01,487 - app.db.init_db - INFO - 数据库表结构检查和更新完成
2025-06-30 21:28:01,492 - app.db.init_db - INFO - 模型关系初始化完成
2025-06-30 21:28:01,493 - app.db.init_db - INFO - 模型关系设置完成
2025-06-30 21:28:01,500 - main - INFO - 数据库初始化完成（强制重建）
2025-06-30 21:28:01,503 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-30 21:28:01,505 - main - INFO - 数据库连接正常
2025-06-30 21:28:01,507 - main - INFO - 开始初始化模板数据
2025-06-30 21:28:01,509 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-30 21:28:02,016 - app.db.init_templates - INFO - 开始初始化评估量表模板...
2025-06-30 21:28:02,172 - app.db.init_templates - INFO - 成功初始化 5 个评估量表模板
2025-06-30 21:28:02,210 - app.db.init_templates - INFO - 开始初始化调查问卷模板...
2025-06-30 21:28:02,350 - app.db.init_templates - INFO - 成功初始化 5 个调查问卷模板
2025-06-30 21:28:02,356 - main - INFO - 模板数据初始化完成
2025-06-30 21:28:02,358 - main - INFO - 数据库表结构已经标准化，不需要迁移
2025-06-30 21:28:02,360 - main - INFO - 应用启动完成
2025-06-30 21:28:02,785 - main - INFO - 
--- 请求开始: POST /api/management/health-report ---
2025-06-30 21:28:02,796 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-06-30 21:28:02,798 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-06-30 21:28:02,811 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-06-30 21:28:02,832 - main - INFO - 令牌验证成功，用户ID: 1
2025-06-30 21:28:02,846 - main - INFO - 通过用户ID 1 获取用户成功: admin
2025-06-30 21:28:02,853 - main - INFO - 获取用户成功: admin, ID: 1, 角色: super_admin
2025-06-30 21:28:02,871 - main - INFO - --- 请求结束: 404 ---

