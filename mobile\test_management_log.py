# -*- coding: utf-8 -*-
"""
管理日志屏幕测试脚本

用于测试管理日志屏幕的功能
"""

import os
import sys

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from kivymd.app import MDApp
from kivy.core.window import Window
from kivy.metrics import dp
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.label import Label
from kivy.uix.button import Button

# 导入主题
from theme import AppTheme, AppMetrics, FontStyles

# 简单的测试屏幕
class SimpleTestScreen(BoxLayout):
    """简单的测试屏幕"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.orientation = 'vertical'
        self.padding = dp(16)
        self.spacing = dp(8)
        
        # 添加标题
        title = Label(
            text="管理日志屏幕测试",
            font_size=dp(24),
            size_hint_y=None,
            height=dp(48)
        )
        self.add_widget(title)
        
        # 添加说明
        info = Label(
            text="管理日志屏幕组件测试成功加载。\n由于KV语言解析问题，无法直接加载完整屏幕。",
            font_size=dp(16),
            size_hint_y=None,
            height=dp(80)
        )
        self.add_widget(info)
        
        # 添加按钮
        btn = Button(
            text="关闭",
            size_hint_y=None,
            height=dp(48),
            on_release=lambda x: MDApp.get_running_app().stop()
        )
        self.add_widget(btn)

class TestApp(MDApp):
    """测试应用程序"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 初始化主题
        self.theme = AppTheme()
        self.metrics = AppMetrics()
        self.font_styles = FontStyles()
    
    def build(self):
        # 设置窗口大小
        Window.size = (dp(400), dp(700))
        
        # 创建简单测试屏幕
        return SimpleTestScreen()


if __name__ == '__main__':
    TestApp().run()