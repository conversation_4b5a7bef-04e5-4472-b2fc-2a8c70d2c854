#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Mobile应用修复测试脚本

这个脚本测试所有应用的修复和优化是否正常工作。
"""

import os
import sys
import time
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MobileFixTester:
    """Mobile应用修复测试器"""
    
    def __init__(self, mobile_dir):
        self.mobile_dir = Path(mobile_dir)
        self.test_results = []
        self.errors = []
        
    def test_import_compatibility(self):
        """测试导入兼容性"""
        logger.info("测试模块导入兼容性...")
        
        modules_to_test = [
            'utils.app_config',
            'utils.cloud_api',
            'utils.screen_loader',
            'utils.memory_utils',
            'utils.performance_monitor',
            'theme',
            'logging_config',
        ]
        
        success_count = 0
        for module_name in modules_to_test:
            try:
                start_time = time.time()
                __import__(module_name)
                import_time = (time.time() - start_time) * 1000
                
                self.test_results.append(f"✅ {module_name}: 导入成功 ({import_time:.2f}ms)")
                success_count += 1
                
            except ImportError as e:
                if "kivy" in str(e).lower() or "kivymd" in str(e).lower():
                    self.test_results.append(f"⚠️ {module_name}: 需要Kivy/KivyMD环境")
                else:
                    self.test_results.append(f"❌ {module_name}: 导入失败 - {e}")
                    self.errors.append(f"导入失败: {module_name} - {e}")
            except Exception as e:
                self.test_results.append(f"❌ {module_name}: 错误 - {e}")
                self.errors.append(f"模块错误: {module_name} - {e}")
        
        logger.info(f"导入测试完成: {success_count}/{len(modules_to_test)} 成功")
        return success_count > len(modules_to_test) * 0.7  # 70%成功率算通过
    
    def test_new_modules(self):
        """测试新创建的模块"""
        logger.info("测试新创建的模块...")
        
        new_modules = [
            'utils.common_components',
            'utils.api_manager',
            'utils.performance_optimizer',
            'utils.error_handler',
        ]
        
        success_count = 0
        for module_name in new_modules:
            try:
                # 修复路径拼接问题
                module_path = self.mobile_dir / module_name.replace('.', os.sep)
                module_path = module_path.with_suffix('.py')

                if module_path.exists():
                    # 尝试导入模块
                    __import__(module_name)
                    self.test_results.append(f"✅ {module_name}: 模块存在且可导入")
                    success_count += 1
                else:
                    self.test_results.append(f"❌ {module_name}: 模块文件不存在")
                    self.errors.append(f"模块文件不存在: {module_path}")
                    
            except ImportError as e:
                if "kivy" in str(e).lower() or "kivymd" in str(e).lower():
                    self.test_results.append(f"⚠️ {module_name}: 需要Kivy/KivyMD环境")
                    success_count += 0.5  # 部分成功
                else:
                    self.test_results.append(f"❌ {module_name}: 导入失败 - {e}")
                    self.errors.append(f"新模块导入失败: {module_name} - {e}")
            except Exception as e:
                self.test_results.append(f"❌ {module_name}: 错误 - {e}")
                self.errors.append(f"新模块错误: {module_name} - {e}")
        
        logger.info(f"新模块测试完成: {success_count}/{len(new_modules)} 成功")
        return success_count > len(new_modules) * 0.7
    
    def test_file_structure(self):
        """测试文件结构完整性"""
        logger.info("测试文件结构完整性...")
        
        required_files = [
            'main.py',
            'theme.py',
            'requirements.txt',
            'utils/__init__.py',
            'utils/app_config.py',
            'utils/cloud_api.py',
            'screens/__init__.py',
            'screens/login_screen.py',
            'widgets/__init__.py',
            'widgets/logo.py',
        ]
        
        missing_files = []
        for file_path in required_files:
            full_path = self.mobile_dir / file_path
            if not full_path.exists():
                missing_files.append(file_path)
        
        if missing_files:
            self.test_results.append(f"❌ 缺少文件: {', '.join(missing_files)}")
            self.errors.extend([f"缺少文件: {f}" for f in missing_files])
            return False
        else:
            self.test_results.append("✅ 所有必需文件都存在")
            return True
    
    def test_kivymd_compatibility(self):
        """测试KivyMD兼容性修复"""
        logger.info("测试KivyMD兼容性修复...")
        
        try:
            # 检查login_screen.py中的修复
            login_screen_path = self.mobile_dir / "screens" / "login_screen.py"
            
            if not login_screen_path.exists():
                self.test_results.append("❌ login_screen.py 不存在")
                return False
            
            with open(login_screen_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否包含修复的方法
            if '_on_password_icon_release' in content:
                self.test_results.append("✅ 密码图标事件修复已应用")
                return True
            else:
                self.test_results.append("⚠️ 密码图标事件修复可能未完全应用")
                return False
                
        except Exception as e:
            self.test_results.append(f"❌ KivyMD兼容性测试失败: {e}")
            self.errors.append(f"KivyMD兼容性测试错误: {e}")
            return False
    
    def test_performance_optimizations(self):
        """测试性能优化"""
        logger.info("测试性能优化...")
        
        try:
            # 测试启动时间（模拟）
            start_time = time.time()
            
            # 模拟导入主要模块
            import utils.app_config
            import theme as theme_optimized as theme
            
            startup_time = (time.time() - start_time) * 1000
            
            if startup_time < 1000:  # 小于1秒
                self.test_results.append(f"✅ 启动性能良好 ({startup_time:.2f}ms)")
                return True
            else:
                self.test_results.append(f"⚠️ 启动时间较长 ({startup_time:.2f}ms)")
                return False
                
        except Exception as e:
            self.test_results.append(f"❌ 性能测试失败: {e}")
            self.errors.append(f"性能测试错误: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始Mobile应用修复测试...")
        
        # 执行所有测试
        tests = [
            ("文件结构完整性", self.test_file_structure),
            ("导入兼容性", self.test_import_compatibility),
            ("新模块功能", self.test_new_modules),
            ("KivyMD兼容性", self.test_kivymd_compatibility),
            ("性能优化", self.test_performance_optimizations),
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"正在执行测试: {test_name}")
            try:
                if test_func():
                    passed_tests += 1
                    logger.info(f"✅ {test_name}: 通过")
                else:
                    logger.warning(f"⚠️ {test_name}: 未通过")
            except Exception as e:
                logger.error(f"❌ {test_name}: 测试异常 - {e}")
                self.errors.append(f"测试异常: {test_name} - {e}")
        
        # 输出测试结果
        logger.info(f"\n测试完成！通过: {passed_tests}/{total_tests}")
        
        if self.test_results:
            logger.info("\n详细测试结果:")
            for result in self.test_results:
                logger.info(f"  {result}")
        
        if self.errors:
            logger.error("\n测试中的错误:")
            for error in self.errors:
                logger.error(f"  {error}")
        
        # 计算总体评分
        score = (passed_tests / total_tests) * 100
        
        if score >= 80:
            logger.info(f"\n🎉 测试评分: {score:.1f}% - 优秀！")
            return True
        elif score >= 60:
            logger.info(f"\n👍 测试评分: {score:.1f}% - 良好")
            return True
        else:
            logger.warning(f"\n⚠️ 测试评分: {score:.1f}% - 需要改进")
            return False

def main():
    """主函数"""
    print("Mobile应用修复测试")
    print("=" * 50)
    
    # 获取mobile目录路径
    current_dir = Path(__file__).parent
    mobile_dir = current_dir
    
    # 创建测试器并运行
    tester = MobileFixTester(mobile_dir)
    success = tester.run_all_tests()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 测试通过！Mobile应用修复效果良好。")
        print("\n建议:")
        print("1. 运行 python fix_kivymd_compatibility.py 应用KivyMD修复")
        print("2. 运行 python optimize_mobile_app.py 应用性能优化")
        print("3. 安装依赖: pip install -r requirements.txt")
        print("4. 启动应用测试实际效果")
    else:
        print("⚠️ 测试未完全通过，建议检查错误信息并修复。")
        print("\n建议:")
        print("1. 检查上述错误信息")
        print("2. 确保所有必需文件存在")
        print("3. 运行修复脚本")
        print("4. 重新运行测试")
    
    return success

if __name__ == "__main__":
    main()
