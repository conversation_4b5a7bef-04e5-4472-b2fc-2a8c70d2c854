#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试问卷和评估提交逻辑
验证修改后的代码是否能正确创建records到results表中
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'YUN', 'backend'))

import sqlite3
from datetime import datetime
import json

def check_database_before_after():
    """
    检查数据库修改前后的状态
    """
    db_paths = [
        "YUN/backend/health_management.db",
        "YUN/backend/app/health_management.db",
        "health_management.db"
    ]
    
    db_path = None
    for path in db_paths:
        if os.path.exists(path):
            db_path = path
            break
    
    if not db_path:
        print("❌ 未找到数据库文件")
        return
    
    print(f"📊 使用数据库: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查各表的记录数
        tables = [
            'assessment_responses',
            'questionnaire_responses', 
            'assessment_results',
            'questionnaire_results'
        ]
        
        print("\n=== 数据库当前状态 ===")
        for table in tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"{table}: {count} 条记录")
                
                # 如果有记录，显示最新的几条
                if count > 0:
                    cursor.execute(f"SELECT * FROM {table} ORDER BY id DESC LIMIT 3")
                    records = cursor.fetchall()
                    
                    # 获取列名
                    cursor.execute(f"PRAGMA table_info({table})")
                    columns = [col[1] for col in cursor.fetchall()]
                    
                    print(f"  最新的记录:")
                    for record in records:
                        record_dict = dict(zip(columns, record))
                        print(f"    ID: {record_dict.get('id', 'N/A')}, 创建时间: {record_dict.get('created_at', record_dict.get('submitted_at', record_dict.get('calculated_at', 'N/A')))}")
                        
            except sqlite3.Error as e:
                print(f"{table}: 表不存在或查询失败 - {e}")
        
        # 检查是否有待处理的问卷和评估
        print("\n=== 待处理的问卷和评估 ===")
        try:
            cursor.execute("""
                SELECT q.id, q.name, qd.custom_id, qd.status 
                FROM questionnaires q 
                JOIN questionnaire_distributions qd ON q.id = qd.questionnaire_id 
                WHERE qd.status = 'pending' 
                ORDER BY q.id DESC LIMIT 5
            """)
            pending_questionnaires = cursor.fetchall()
            print(f"待完成问卷: {len(pending_questionnaires)} 个")
            for q in pending_questionnaires:
                print(f"  问卷ID: {q[0]}, 名称: {q[1]}, 用户: {q[2]}, 状态: {q[3]}")
                
        except sqlite3.Error as e:
            print(f"查询待处理问卷失败: {e}")
            
        try:
            cursor.execute("""
                SELECT a.id, a.name, ad.custom_id, ad.status 
                FROM assessments a 
                JOIN assessment_distributions ad ON a.id = ad.assessment_id 
                WHERE ad.status = 'pending' 
                ORDER BY a.id DESC LIMIT 5
            """)
            pending_assessments = cursor.fetchall()
            print(f"待完成评估: {len(pending_assessments)} 个")
            for a in pending_assessments:
                print(f"  评估ID: {a[0]}, 名称: {a[1]}, 用户: {a[2]}, 状态: {a[3]}")
                
        except sqlite3.Error as e:
            print(f"查询待处理评估失败: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {str(e)}")

def main():
    print("🔍 测试提交逻辑修改效果")
    print("=" * 50)
    
    # 检查当前数据库状态
    check_database_before_after()
    
    print("\n📝 修改说明:")
    print("1. 在问卷提交逻辑中添加了自动创建QuestionnaireResult记录的代码")
    print("2. 在评估提交逻辑中添加了自动创建AssessmentResponse和AssessmentResult记录的代码")
    print("3. 添加了generate_assessment_analysis_report函数")
    
    print("\n✅ 下一步测试建议:")
    print("1. 重启后端服务器")
    print("2. 从移动端提交一个新的问卷或评估")
    print("3. 再次运行check_response_tables.py查看结果")
    print("4. 检查前端历史查询tab是否能显示数据")

if __name__ == "__main__":
    main()