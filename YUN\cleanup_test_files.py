#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理不必要的测试文件
删除重复的、过时的或者不再使用的测试文件
"""

import os
import shutil
from pathlib import Path
from datetime import datetime

def cleanup_test_files():
    """清理测试文件"""
    
    # 项目根目录
    project_root = Path(__file__).parent
    backend_dir = project_root / "backend"
    
    # 需要删除的测试文件列表
    files_to_delete = [
        # YUN根目录下的测试文件
        "test_frontend_api_calls.py",
        "test_questionnaire_assessment_data.py", 
        "test_comprehensive_system.py",
        "test_frontend_fix.py",
        "test_api_fix.py",
        "test_frontend_clinical_scales.py",
        "test_data_management_import.py",
        "test_frontend_simple.py",
        "test_frontend_backend_integration.py",
        "test_api_connection.py",
        "test_frontend_questionnaire_comprehensive.py",
        "test_api_direct.py",
        "test_questionnaire_frontend.py",
        "test_assessment_responses.py",
        "test_authenticated_api.py",
        "test_answers_field.py",
        "test_aggregated_health.py",
        "test_health_endpoint.py",
        "test_frontend_assessments.py",
        "test_frontend_api_direct.py",
        "test_frontend_login_simple.py",
        "test_frontend_enhanced.py",
        "test_api_paths_comparison.py",
        "test_token_auth.py",
        "test_clinical_scales_page.py",
        "test_all_api_routes.py",
        "test_api_comprehensive.py",
        "simple_health_test.py",
        "playwright_test.py",
        
        # Backend目录下的测试文件
        "backend/test_404_fix.py",
        "backend/test_aggregated_api_simple.py",
        "backend/test_aggregated_endpoint.py",
        "backend/test_aggregated_questionnaire.py",
        "backend/test_aggregated_response.py",
        "backend/test_api_fields.py",
        "backend/test_api_full.py",
        "backend/test_api_restart.py",
        "backend/test_assessment_api.py",
        "backend/test_assessment_submit.py",
        "backend/test_backend_fixes.py",
        "backend/test_clinical_scales_debug.py",
        "backend/test_correct_aggregated_paths.py",
        "backend/test_db_insert.py",
        "backend/test_direct_login_simple.py",
        "backend/test_error_fixes.py",
        "backend/test_final_api.py",
        "backend/test_fix.py",
        "backend/test_fixed_aggregated_api.py",
        "backend/test_frontend_api.py",
        "backend/test_frontend_field_fix.py",
        "backend/test_health_profile_tabs.py",
        "backend/test_history_api.py",
        "backend/test_login.py",
        "backend/test_login_fix.py",
        "backend/test_mobile_api_fields.py",
        "backend/test_questionnaire_api.py",
        "backend/test_questionnaire_result.py",
        "backend/test_questionnaire_result_creation.py",
        "backend/test_questionnaire_submit.py",
        "backend/test_reload.py",
        "backend/test_result_saving_fix.py",
        "backend/test_route_debug.py",
        "backend/test_route_registration.py",
        "backend/test_simple_aggregated.py",
        "backend/test_simple_api.py",
        "backend/test_simple_import.py",
        "backend/test_sm008_api.py",
        "backend/test_template_debug.py",
        "backend/test_templates.py",
        
        # 保留的重要测试文件:
        # - test_aggregated_api.py (聚合API测试)
        # - test_data_export.py (数据导出测试)
        # - test_manager.py (测试管理器)
        # - basic_connectivity_test.py (基础连通性测试)
    ]
    
    # 需要删除的调试和检查文件
    debug_files_to_delete = [
        # Backend目录下的调试文件
        "backend/debug_aggregated_api.py",
        "backend/debug_aggregated_import.py",
        "backend/debug_api.py",
        "backend/debug_api_context.py",
        "backend/debug_api_functions.py",
        "backend/debug_api_inline.py",
        "backend/debug_api_registration.py",
        "backend/debug_assessment_mapping.py",
        "backend/debug_assessment_params.py",
        "backend/debug_dimension_calculation.py",
        "backend/debug_dimension_scores.py",
        "backend/debug_full_aggregation.py",
        "backend/debug_import_process.py",
        "backend/debug_login.py",
        "backend/debug_runtime_import.py",
        "backend/debug_scoring_format.py",
        "backend/debug_sm008_scoring.py",
        "backend/debug_template3.py",
        "backend/debug_template_id.py",
        "backend/debug_template_import.py",
        
        # 重复的检查文件
        "backend/check_actual_questions.py",
        "backend/check_all_table_structures.py",
        "backend/check_all_tables.py",
        "backend/check_and_fix_template_id.py",
        "backend/check_answers_data.py",
        "backend/check_assessment_result_4.py",
        "backend/check_assessment_results.py",
        "backend/check_assessment_results_detailed.py",
        "backend/check_assessment_template.py",
        "backend/check_assessment_templates.py",
        "backend/check_constraints.py",
        "backend/check_custom_id.py",
        "backend/check_data_format.py",
        "backend/check_db_assessment.py",
        "backend/check_db_integrity.py",
        "backend/check_db_schema.py",
        "backend/check_db_structure.py",
        "backend/check_db_templates.py",
        "backend/check_dimension_scores.py",
        "backend/check_dimension_scores_final.py",
        "backend/check_duplicate_tables.py",
        "backend/check_health_records.py",
        "backend/check_history_data.py",
        "backend/check_questionnaire_result.py",
        "backend/check_questionnaire_submission.py",
        "backend/check_questionnaire_template.py",
        "backend/check_questionnaires_schema.py",
        "backend/check_redis.py",
        "backend/check_registered_routes.py",
        "backend/check_response_tables.py",
        "backend/check_routes.py",
        "backend/check_routes_debug.py",
        "backend/check_routes_simple.py",
        "backend/check_scoring_data.py",
        "backend/check_scoring_logic.py",
        "backend/check_scoring_rules.py",
        "backend/check_scoring_rules_detailed.py",
        "backend/check_sm008_answers.py",
        "backend/check_sm008_assessment_results.py",
        "backend/check_sm008_data_structure.py",
        "backend/check_sm008_dimension_data.py",
        "backend/check_sm008_final.py",
        "backend/check_sm008_questionnaire_data.py",
        "backend/check_sm008_results.py",
        "backend/check_sm008_simple.py",
        "backend/check_table_schema.py",
        "backend/check_table_structure.py",
        "backend/check_template_mapping.py",
        "backend/check_template_questions_structure.py",
        "backend/check_template_scoring.py",
        "backend/check_test_data.py",
        "backend/check_user_password.py",
        "backend/check_user_passwords.py",
        "backend/check_users.py",
        "backend/check_users_and_scoring.py",
        
        # 保留的重要检查文件:
        # - check_tables.py (数据库表结构检查)
    ]
    
    # 需要删除的修复文件
    fix_files_to_delete = [
        "backend/fix_all_issues.py",
        "backend/fix_api_raw_answers_sync.py",
        "backend/fix_assessment_scoring.py",
        "backend/fix_assessment_type_enum.py",
        "backend/fix_database.py",
        "backend/fix_database_type_errors.py",
        "backend/fix_dimension_scores.py",
        "backend/fix_dimensions.py",
        "backend/fix_dimensions_column.py",
        "backend/fix_distributions_template_id.py",
        "backend/fix_empty_answers.py",
        "backend/fix_imports.py",
        "backend/fix_jump_logic.py",
        "backend/fix_missing_records.py",
        "backend/fix_mobile_scoring.py",
        "backend/fix_psychological_data.py",
        "backend/fix_questionnaire_scoring.py",
        "backend/fix_questionnaire_template.py",
        "backend/fix_questionnaire_template_id.py",
        "backend/fix_scoring_complete.py",
        "backend/fix_scoring_rules.py",
        "backend/fix_scoring_system.py",
        "backend/fix_sm008_data.py",
        "backend/fix_source_files_scoring.py",
        "backend/fix_template3_format.py",
        "backend/fix_template_id.py",
        "backend/fix_template_scoring.py",
        "backend/fix_user_id_to_custom_id.py",
    ]
    
    # 合并所有要删除的文件
    all_files_to_delete = files_to_delete + debug_files_to_delete + fix_files_to_delete
    
    deleted_files = []
    failed_deletions = []
    
    print(f"开始清理测试文件 - {datetime.now()}")
    print(f"计划删除 {len(all_files_to_delete)} 个文件")
    
    for file_path in all_files_to_delete:
        full_path = project_root / file_path
        
        try:
            if full_path.exists():
                full_path.unlink()
                deleted_files.append(str(file_path))
                print(f"✓ 已删除: {file_path}")
            else:
                print(f"- 文件不存在: {file_path}")
        except Exception as e:
            failed_deletions.append((str(file_path), str(e)))
            print(f"✗ 删除失败: {file_path} - {e}")
    
    # 删除旧的测试报告文件
    report_patterns = [
        "*test_report*.json",
        "*api_test_report*.json",
        "*frontend_test_report*.json",
        "*database_check_report*.json",
        "*aggregated_api_test_report*.json"
    ]
    
    for pattern in report_patterns:
        for report_file in project_root.glob(pattern):
            try:
                report_file.unlink()
                deleted_files.append(str(report_file.relative_to(project_root)))
                print(f"✓ 已删除报告文件: {report_file.name}")
            except Exception as e:
                failed_deletions.append((str(report_file), str(e)))
                print(f"✗ 删除报告文件失败: {report_file.name} - {e}")
        
        for report_file in backend_dir.glob(pattern):
            try:
                report_file.unlink()
                deleted_files.append(str(report_file.relative_to(project_root)))
                print(f"✓ 已删除后端报告文件: {report_file.name}")
            except Exception as e:
                failed_deletions.append((str(report_file), str(e)))
                print(f"✗ 删除后端报告文件失败: {report_file.name} - {e}")
    
    # 生成清理报告
    cleanup_report = {
        "cleanup_time": datetime.now().isoformat(),
        "total_planned": len(all_files_to_delete),
        "successfully_deleted": len(deleted_files),
        "failed_deletions": len(failed_deletions),
        "deleted_files": deleted_files,
        "failed_files": failed_deletions,
        "remaining_important_files": [
            "test_aggregated_api.py",
            "test_data_export.py", 
            "test_manager.py",
            "basic_connectivity_test.py",
            "backend/check_tables.py"
        ]
    }
    
    # 保存清理报告
    import json
    report_file = project_root / f"test_cleanup_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(cleanup_report, f, ensure_ascii=False, indent=2)
    
    print(f"\n=== 清理完成 ===")
    print(f"成功删除: {len(deleted_files)} 个文件")
    print(f"删除失败: {len(failed_deletions)} 个文件")
    print(f"清理报告已保存到: {report_file}")
    
    if failed_deletions:
        print("\n删除失败的文件:")
        for file_path, error in failed_deletions:
            print(f"  - {file_path}: {error}")
    
    print("\n保留的重要测试文件:")
    for important_file in cleanup_report["remaining_important_files"]:
        print(f"  - {important_file}")
    
    return cleanup_report

if __name__ == "__main__":
    cleanup_test_files()