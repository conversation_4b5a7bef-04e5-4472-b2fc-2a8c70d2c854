/**
 * 测试工具和辅助函数
 * 提供组件测试、API测试、性能测试、覆盖率分析等功能
 * 
 * 版本: 2.0
 * 更新时间: 2024-12-30
 */
import { mount, shallowMount } from '@vue/test-utils'
import { createPinia } from 'pinia'
import { ElMessage } from 'element-plus'
import axios from 'axios'

/**
 * 创建测试用的 Vue 实例配置
 */
export function createTestConfig(options = {}) {
  const {
    global = {},
    props = {},
    slots = {},
    attachTo = null
  } = options

  return {
    global: {
      plugins: [createPinia()],
      stubs: {
        'el-button': true,
        'el-input': true,
        'el-form': true,
        'el-form-item': true,
        'el-table': true,
        'el-dialog': true,
        'router-link': true,
        'router-view': true,
        ...global.stubs
      },
      mocks: {
        $message: ElMessage,
        $route: {
          path: '/',
          params: {},
          query: {},
          ...global.mocks?.$route
        },
        $router: {
          push: jest.fn(),
          replace: jest.fn(),
          go: jest.fn(),
          back: jest.fn(),
          forward: jest.fn(),
          ...global.mocks?.$router
        },
        ...global.mocks
      },
      ...global
    },
    props,
    slots,
    attachTo
  }
}

/**
 * 组件测试辅助类
 */
export class ComponentTester {
  constructor(component, options = {}) {
    this.component = component
    this.options = options
    this.wrapper = null
  }

  /**
   * 挂载组件
   */
  mount(mountOptions = {}) {
    const config = createTestConfig({
      ...this.options,
      ...mountOptions
    })
    
    this.wrapper = mount(this.component, config)
    return this.wrapper
  }

  /**
   * 浅挂载组件
   */
  shallowMount(mountOptions = {}) {
    const config = createTestConfig({
      ...this.options,
      ...mountOptions
    })
    
    this.wrapper = shallowMount(this.component, config)
    return this.wrapper
  }

  /**
   * 等待异步操作完成
   */
  async waitForAsync() {
    if (this.wrapper) {
      await this.wrapper.vm.$nextTick()
      await new Promise(resolve => setTimeout(resolve, 0))
    }
  }

  /**
   * 模拟用户输入
   */
  async simulateInput(selector, value) {
    if (!this.wrapper) {
      throw new Error('组件未挂载')
    }

    const input = this.wrapper.find(selector)
    if (!input.exists()) {
      throw new Error(`找不到元素: ${selector}`)
    }

    await input.setValue(value)
    await this.waitForAsync()
  }

  /**
   * 模拟用户点击
   */
  async simulateClick(selector) {
    if (!this.wrapper) {
      throw new Error('组件未挂载')
    }

    const element = this.wrapper.find(selector)
    if (!element.exists()) {
      throw new Error(`找不到元素: ${selector}`)
    }

    await element.trigger('click')
    await this.waitForAsync()
  }

  /**
   * 模拟表单提交
   */
  async simulateSubmit(formSelector = 'form') {
    if (!this.wrapper) {
      throw new Error('组件未挂载')
    }

    const form = this.wrapper.find(formSelector)
    if (!form.exists()) {
      throw new Error(`找不到表单: ${formSelector}`)
    }

    await form.trigger('submit')
    await this.waitForAsync()
  }

  /**
   * 检查元素是否存在
   */
  hasElement(selector) {
    if (!this.wrapper) {
      throw new Error('组件未挂载')
    }
    return this.wrapper.find(selector).exists()
  }

  /**
   * 获取元素文本
   */
  getElementText(selector) {
    if (!this.wrapper) {
      throw new Error('组件未挂载')
    }

    const element = this.wrapper.find(selector)
    if (!element.exists()) {
      throw new Error(`找不到元素: ${selector}`)
    }

    return element.text()
  }

  /**
   * 检查组件属性
   */
  checkProp(propName, expectedValue) {
    if (!this.wrapper) {
      throw new Error('组件未挂载')
    }

    const actualValue = this.wrapper.props(propName)
    expect(actualValue).toBe(expectedValue)
  }

  /**
   * 检查组件事件
   */
  checkEmitted(eventName, expectedArgs = null) {
    if (!this.wrapper) {
      throw new Error('组件未挂载')
    }

    const emitted = this.wrapper.emitted(eventName)
    expect(emitted).toBeTruthy()
    
    if (expectedArgs !== null) {
      expect(emitted[emitted.length - 1]).toEqual(expectedArgs)
    }
  }

  /**
   * 销毁组件
   */
  destroy() {
    if (this.wrapper) {
      this.wrapper.unmount()
      this.wrapper = null
    }
  }
}

/**
 * API 测试辅助类
 */
export class ApiTester {
  constructor(baseURL = 'http://localhost:8006') {
    this.baseURL = baseURL
    this.defaultHeaders = {
      'Content-Type': 'application/json'
    }
  }

  /**
   * 设置认证令牌
   */
  setAuthToken(token) {
    this.defaultHeaders.Authorization = `Bearer ${token}`
  }

  /**
   * 设置用户ID
   */
  setUserId(userId) {
    this.defaultHeaders['X-User-ID'] = userId
  }

  /**
   * 发送 GET 请求
   */
  async get(endpoint, options = {}) {
    return this.request('GET', endpoint, null, options)
  }

  /**
   * 发送 POST 请求
   */
  async post(endpoint, data = null, options = {}) {
    return this.request('POST', endpoint, data, options)
  }

  /**
   * 发送 PUT 请求
   */
  async put(endpoint, data = null, options = {}) {
    return this.request('PUT', endpoint, data, options)
  }

  /**
   * 发送 DELETE 请求
   */
  async delete(endpoint, options = {}) {
    return this.request('DELETE', endpoint, null, options)
  }

  /**
   * 通用请求方法
   */
  async request(method, endpoint, data = null, options = {}) {
    const {
      headers = {},
      timeout = 10000,
      expectStatus = 200
    } = options

    const url = `${this.baseURL}${endpoint}`
    const requestOptions = {
      method,
      headers: {
        ...this.defaultHeaders,
        ...headers
      }
    }

    if (data) {
      requestOptions.body = JSON.stringify(data)
    }

    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)
    requestOptions.signal = controller.signal

    try {
      const response = await fetch(url, requestOptions)
      clearTimeout(timeoutId)

      // 检查状态码
      if (Array.isArray(expectStatus)) {
        expect(expectStatus).toContain(response.status)
      } else {
        expect(response.status).toBe(expectStatus)
      }

      // 解析响应
      const contentType = response.headers.get('content-type')
      let responseData
      
      if (contentType && contentType.includes('application/json')) {
        responseData = await response.json()
      } else {
        responseData = await response.text()
      }

      return {
        status: response.status,
        headers: response.headers,
        data: responseData
      }
    } catch (error) {
      clearTimeout(timeoutId)
      throw error
    }
  }

  /**
   * 测试 API 端点可用性
   */
  async testEndpoint(endpoint, method = 'GET', data = null) {
    try {
      const response = await this.request(method, endpoint, data, {
        expectStatus: [200, 201, 204, 400, 401, 403, 404, 422, 500]
      })
      
      return {
        available: true,
        status: response.status,
        responseTime: Date.now() // 简化实现
      }
    } catch (error) {
      return {
        available: false,
        error: error.message
      }
    }
  }

  /**
   * 批量测试 API 端点
   */
  async testEndpoints(endpoints) {
    const results = []
    
    for (const endpoint of endpoints) {
      const { path, method = 'GET', data = null, name } = endpoint
      const result = await this.testEndpoint(path, method, data)
      
      results.push({
        name: name || `${method} ${path}`,
        path,
        method,
        ...result
      })
    }
    
    return results
  }
}

/**
 * 性能测试辅助类
 */
export class PerformanceTester {
  constructor() {
    this.measurements = new Map()
  }

  /**
   * 开始性能测量
   */
  start(name) {
    this.measurements.set(name, {
      startTime: performance.now(),
      name
    })
  }

  /**
   * 结束性能测量
   */
  end(name) {
    const measurement = this.measurements.get(name)
    if (!measurement) {
      throw new Error(`未找到性能测量: ${name}`)
    }

    const endTime = performance.now()
    const duration = endTime - measurement.startTime
    
    const result = {
      name,
      duration,
      startTime: measurement.startTime,
      endTime
    }
    
    this.measurements.set(name, result)
    return result
  }

  /**
   * 测量函数执行时间
   */
  async measureFunction(name, fn) {
    this.start(name)
    
    try {
      const result = await fn()
      const measurement = this.end(name)
      
      return {
        result,
        measurement
      }
    } catch (error) {
      this.end(name)
      throw error
    }
  }

  /**
   * 批量性能测试
   */
  async runBenchmark(tests, iterations = 1) {
    const results = []
    
    for (const test of tests) {
      const { name, fn } = test
      const measurements = []
      
      for (let i = 0; i < iterations; i++) {
        const { measurement } = await this.measureFunction(`${name}_${i}`, fn)
        measurements.push(measurement.duration)
      }
      
      const avgDuration = measurements.reduce((sum, d) => sum + d, 0) / measurements.length
      const minDuration = Math.min(...measurements)
      const maxDuration = Math.max(...measurements)
      
      results.push({
        name,
        iterations,
        avgDuration,
        minDuration,
        maxDuration,
        measurements
      })
    }
    
    return results
  }

  /**
   * 获取所有测量结果
   */
  getAllMeasurements() {
    return Array.from(this.measurements.values())
  }

  /**
   * 清除测量结果
   */
  clear() {
    this.measurements.clear()
  }
}

/**
 * 数据生成器
 */
export class DataGenerator {
  /**
   * 生成随机字符串
   */
  static randomString(length = 10) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }

  /**
   * 生成随机数字
   */
  static randomNumber(min = 0, max = 100) {
    return Math.floor(Math.random() * (max - min + 1)) + min
  }

  /**
   * 生成随机邮箱
   */
  static randomEmail() {
    const domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'example.com']
    const username = this.randomString(8)
    const domain = domains[Math.floor(Math.random() * domains.length)]
    return `${username}@${domain}`
  }

  /**
   * 生成随机日期
   */
  static randomDate(start = new Date(2020, 0, 1), end = new Date()) {
    return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()))
  }

  /**
   * 生成测试用户数据
   */
  static generateUser(overrides = {}) {
    return {
      id: this.randomNumber(1, 10000),
      username: this.randomString(8),
      email: this.randomEmail(),
      name: `测试用户${this.randomNumber(1, 999)}`,
      age: this.randomNumber(18, 80),
      phone: `1${this.randomNumber(3, 9)}${this.randomString(9)}`,
      createdAt: this.randomDate(),
      ...overrides
    }
  }

  /**
   * 生成测试评估数据
   */
  static generateAssessment(overrides = {}) {
    return {
      id: this.randomNumber(1, 10000),
      title: `测试评估${this.randomNumber(1, 999)}`,
      description: `这是一个测试评估的描述 ${this.randomString(20)}`,
      type: ['anxiety', 'depression', 'stress'][Math.floor(Math.random() * 3)],
      questions: this.generateQuestions(this.randomNumber(5, 15)),
      createdAt: this.randomDate(),
      ...overrides
    }
  }

  /**
   * 生成测试问题数据
   */
  static generateQuestions(count = 10) {
    const questions = []
    for (let i = 0; i < count; i++) {
      questions.push({
        id: i + 1,
        text: `测试问题 ${i + 1}: ${this.randomString(30)}`,
        type: ['single', 'multiple', 'text'][Math.floor(Math.random() * 3)],
        options: this.generateOptions(this.randomNumber(2, 5)),
        required: Math.random() > 0.3
      })
    }
    return questions
  }

  /**
   * 生成测试选项数据
   */
  static generateOptions(count = 4) {
    const options = []
    for (let i = 0; i < count; i++) {
      options.push({
        id: i + 1,
        text: `选项 ${i + 1}`,
        value: i + 1
      })
    }
    return options
  }

  /**
   * 生成批量测试数据
   */
  static generateBatch(generator, count = 10, overrides = {}) {
    const items = []
    for (let i = 0; i < count; i++) {
      items.push(generator(overrides))
    }
    return items
  }
}

/**
 * 断言辅助函数
 */
export const assertions = {
  /**
   * 断言 API 响应格式
   */
  assertApiResponse(response, expectedFields = []) {
    expect(response).toBeDefined()
    expect(response.data).toBeDefined()
    
    if (expectedFields.length > 0) {
      expectedFields.forEach(field => {
        expect(response.data).toHaveProperty(field)
      })
    }
  },

  /**
   * 断言组件渲染
   */
  assertComponentRender(wrapper, expectedElements = []) {
    expect(wrapper.exists()).toBe(true)
    
    expectedElements.forEach(selector => {
      expect(wrapper.find(selector).exists()).toBe(true)
    })
  },

  /**
   * 断言性能指标
   */
  assertPerformance(measurement, maxDuration = 1000) {
    expect(measurement).toBeDefined()
    expect(measurement.duration).toBeLessThan(maxDuration)
  },

  /**
   * 断言数据格式
   */
  assertDataFormat(data, schema) {
    expect(data).toBeDefined()
    
    Object.keys(schema).forEach(key => {
      expect(data).toHaveProperty(key)
      
      const expectedType = schema[key]
      const actualValue = data[key]
      
      if (expectedType === 'string') {
        expect(typeof actualValue).toBe('string')
      } else if (expectedType === 'number') {
        expect(typeof actualValue).toBe('number')
      } else if (expectedType === 'boolean') {
        expect(typeof actualValue).toBe('boolean')
      } else if (expectedType === 'array') {
        expect(Array.isArray(actualValue)).toBe(true)
      } else if (expectedType === 'object') {
        expect(typeof actualValue).toBe('object')
        expect(actualValue).not.toBeNull()
      }
    })
  }
}

/**
 * 测试套件辅助函数
 */
export function createTestSuite(name, tests) {
  describe(name, () => {
    tests.forEach(test => {
      const { name: testName, fn, skip = false, only = false } = test
      
      if (skip) {
        it.skip(testName, fn)
      } else if (only) {
        it.only(testName, fn)
      } else {
        it(testName, fn)
      }
    })
  })
}

/**
 * 测试管理器类
 * 提供统一的测试执行、结果分析和报告生成功能
 */
export class TestManager {
  constructor() {
    this.baseURL = '/api/test-management'
    this.testResults = new Map()
    this.coverageData = null
    this.testHistory = []
  }

  /**
   * 获取所有测试套件
   */
  async getTestSuites() {
    try {
      const response = await axios.get(`${this.baseURL}/suites`)
      return response.data.data || []
    } catch (error) {
      console.error('获取测试套件失败:', error)
      throw error
    }
  }

  /**
   * 运行指定测试套件
   */
  async runTestSuite(suiteId) {
    try {
      const response = await axios.post(`${this.baseURL}/suites/${suiteId}/run`)
      return response.data
    } catch (error) {
      console.error('运行测试套件失败:', error)
      throw error
    }
  }

  /**
   * 运行所有测试
   */
  async runAllTests(testTypes = []) {
    try {
      const params = testTypes.length > 0 ? { test_types: testTypes } : {}
      const response = await axios.post(`${this.baseURL}/run-all`, null, { params })
      return response.data
    } catch (error) {
      console.error('运行所有测试失败:', error)
      throw error
    }
  }

  /**
   * 获取测试结果
   */
  async getTestResults(suiteId = null, limit = 100) {
    try {
      const params = { limit }
      if (suiteId) params.suite_id = suiteId
      
      const response = await axios.get(`${this.baseURL}/results`, { params })
      return response.data.data || []
    } catch (error) {
      console.error('获取测试结果失败:', error)
      throw error
    }
  }

  /**
   * 获取覆盖率报告
   */
  async getCoverageReport() {
    try {
      const response = await axios.get(`${this.baseURL}/coverage`)
      this.coverageData = response.data.data
      return this.coverageData
    } catch (error) {
      console.error('获取覆盖率报告失败:', error)
      throw error
    }
  }

  /**
   * 获取测试统计信息
   */
  async getTestStatistics(days = 30) {
    try {
      const response = await axios.get(`${this.baseURL}/statistics`, {
        params: { days }
      })
      return response.data.data
    } catch (error) {
      console.error('获取测试统计信息失败:', error)
      throw error
    }
  }

  /**
   * 导出覆盖率报告
   */
  async exportCoverageReport(format = 'html') {
    try {
      const response = await axios.get(`${this.baseURL}/export/coverage`, {
        params: { format },
        responseType: format === 'html' ? 'blob' : 'json'
      })
      
      if (format === 'html') {
        // 下载HTML文件
        const blob = new Blob([response.data], { type: 'text/html' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `coverage_report_${new Date().toISOString().slice(0, 10)}.html`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
      }
      
      return response.data
    } catch (error) {
      console.error('导出覆盖率报告失败:', error)
      throw error
    }
  }

  /**
   * 清理测试结果
   */
  async clearTestResults(olderThanDays = 30) {
    try {
      const response = await axios.delete(`${this.baseURL}/results`, {
        params: { older_than_days: olderThanDays }
      })
      return response.data
    } catch (error) {
      console.error('清理测试结果失败:', error)
      throw error
    }
  }

  /**
   * 分析测试趋势
   */
  analyzeTestTrends(results) {
    if (!results || results.length === 0) return null

    // 按日期分组
    const groupedByDate = results.reduce((acc, result) => {
      const date = new Date(result.start_time).toDateString()
      if (!acc[date]) {
        acc[date] = { total: 0, passed: 0, failed: 0, error: 0 }
      }
      acc[date].total++
      acc[date][result.status]++
      return acc
    }, {})

    // 计算趋势
    const dates = Object.keys(groupedByDate).sort()
    const trends = dates.map(date => ({
      date,
      ...groupedByDate[date],
      successRate: (groupedByDate[date].passed / groupedByDate[date].total) * 100
    }))

    return {
      trends,
      summary: {
        totalDays: dates.length,
        averageSuccessRate: trends.reduce((sum, t) => sum + t.successRate, 0) / trends.length,
        bestDay: trends.reduce((best, current) => 
          current.successRate > best.successRate ? current : best
        ),
        worstDay: trends.reduce((worst, current) => 
          current.successRate < worst.successRate ? current : worst
        )
      }
    }
  }

  /**
   * 生成测试报告
   */
  generateTestReport(suites, results, coverage) {
    const totalTests = suites.reduce((sum, suite) => sum + (suite.total_tests || 0), 0)
    const passedTests = suites.reduce((sum, suite) => sum + (suite.passed_tests || 0), 0)
    const failedTests = suites.reduce((sum, suite) => sum + (suite.failed_tests || 0), 0)
    const skippedTests = suites.reduce((sum, suite) => sum + (suite.skipped_tests || 0), 0)

    return {
      summary: {
        totalTests,
        passedTests,
        failedTests,
        skippedTests,
        successRate: totalTests > 0 ? (passedTests / totalTests) * 100 : 0,
        overallCoverage: coverage?.overall_coverage || 0
      },
      suites: suites.map(suite => ({
        ...suite,
        successRate: suite.total_tests > 0 ? 
          (suite.passed_tests / suite.total_tests) * 100 : 0
      })),
      coverage,
      trends: this.analyzeTestTrends(results),
      generatedAt: new Date().toISOString()
    }
  }

  /**
   * 实时监控测试状态
   */
  startTestMonitoring(callback, interval = 5000) {
    const monitor = async () => {
      try {
        const suites = await this.getTestSuites()
        const runningSuites = suites.filter(suite => suite.status === 'running')
        
        if (runningSuites.length > 0) {
          callback({
            type: 'running',
            suites: runningSuites,
            timestamp: new Date().toISOString()
          })
        } else {
          callback({
            type: 'idle',
            suites,
            timestamp: new Date().toISOString()
          })
        }
      } catch (error) {
        callback({
          type: 'error',
          error: error.message,
          timestamp: new Date().toISOString()
        })
      }
    }

    // 立即执行一次
    monitor()
    
    // 设置定时器
    const intervalId = setInterval(monitor, interval)
    
    // 返回停止函数
    return () => clearInterval(intervalId)
  }
}

/**
 * 覆盖率分析器
 */
export class CoverageAnalyzer {
  constructor() {
    this.thresholds = {
      high: 80,
      medium: 60,
      low: 40
    }
  }

  /**
   * 分析覆盖率等级
   */
  analyzeCoverageLevel(coverage) {
    if (coverage >= this.thresholds.high) return 'high'
    if (coverage >= this.thresholds.medium) return 'medium'
    if (coverage >= this.thresholds.low) return 'low'
    return 'critical'
  }

  /**
   * 生成覆盖率建议
   */
  generateCoverageSuggestions(coverageData) {
    const suggestions = []
    
    if (!coverageData) return suggestions

    const overall = coverageData.overall_coverage || 0
    
    if (overall < this.thresholds.low) {
      suggestions.push({
        type: 'critical',
        message: '整体覆盖率过低，建议优先编写核心功能的单元测试',
        priority: 'high'
      })
    }

    if (coverageData.backend?.percent_covered < this.thresholds.medium) {
      suggestions.push({
        type: 'warning',
        message: '后端覆盖率偏低，建议增加API和业务逻辑测试',
        priority: 'medium'
      })
    }

    if (coverageData.frontend?.percent_covered < this.thresholds.medium) {
      suggestions.push({
        type: 'warning',
        message: '前端覆盖率偏低，建议增加组件和工具函数测试',
        priority: 'medium'
      })
    }

    return suggestions
  }

  /**
   * 比较覆盖率变化
   */
  compareCoverage(current, previous) {
    if (!previous) return null

    const change = current.overall_coverage - previous.overall_coverage
    
    return {
      change,
      trend: change > 0 ? 'improving' : change < 0 ? 'declining' : 'stable',
      percentage: Math.abs(change),
      backend: {
        change: (current.backend?.percent_covered || 0) - (previous.backend?.percent_covered || 0)
      },
      frontend: {
        change: (current.frontend?.percent_covered || 0) - (previous.frontend?.percent_covered || 0)
      }
    }
  }
}

/**
 * 测试套件构建器
 */
export class TestSuiteBuilder {
  constructor() {
    this.suite = {
      name: '',
      description: '',
      type: 'unit',
      tests: [],
      config: {}
    }
  }

  /**
   * 设置套件基本信息
   */
  setBasicInfo(name, description, type = 'unit') {
    this.suite.name = name
    this.suite.description = description
    this.suite.type = type
    return this
  }

  /**
   * 添加测试用例
   */
  addTest(name, testFunction, options = {}) {
    this.suite.tests.push({
      name,
      testFunction,
      ...options
    })
    return this
  }

  /**
   * 设置配置
   */
  setConfig(config) {
    this.suite.config = { ...this.suite.config, ...config }
    return this
  }

  /**
   * 构建测试套件
   */
  build() {
    return { ...this.suite }
  }
}

export {
  ComponentTester,
  ApiTester,
  PerformanceTester,
  DataGenerator,
  TestManager,
  CoverageAnalyzer,
  TestSuiteBuilder
}

export default {
  createTestConfig,
  ComponentTester,
  ApiTester,
  PerformanceTester,
  DataGenerator,
  assertions,
  createTestSuite
}