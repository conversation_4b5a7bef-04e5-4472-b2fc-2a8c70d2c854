#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确创建评估数据
"""

import sqlite3
import json
from datetime import datetime, timedelta
import requests

def create_assessment_correctly():
    """正确创建评估数据"""
    print("=== 正确创建评估数据 ===")
    
    conn = sqlite3.connect('YUN/backend/app.db')
    cursor = conn.cursor()
    
    try:
        # 1. 获取可用的模板
        cursor.execute("SELECT id, name FROM assessment_templates LIMIT 3")
        templates = cursor.fetchall()
        print(f"可用模板: {templates}")
        
        if not templates:
            print("没有可用的评估模板")
            return
        
        # 2. 为SM_006用户创建评估数据
        print("\n为SM_006用户创建评估数据")
        
        # 检查SM_006是否已有评估
        cursor.execute("SELECT COUNT(*) FROM assessments WHERE custom_id = 'SM_006'")
        existing_count = cursor.fetchone()[0]
        print(f"SM_006现有评估数量: {existing_count}")
        
        # 创建评估记录
        assessments_to_create = [
            {
                'custom_id': 'SM_006',
                'template_id': templates[0][0],
                'assessment_type': 'SELF',
                'name': templates[0][1],
                'version': '1.0',
                'round_number': 1,
                'sequence_number': 1,
                'unique_identifier': f'standard_standard_{templates[0][0]}_SM_006_1_1',
                'score': 0.0,
                'max_score': 100.0,
                'result': '待完成',
                'conclusion': '评估尚未完成',
                'status': 'pending',
                'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'updated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            },
            {
                'custom_id': 'SM_006',
                'template_id': templates[1][0] if len(templates) > 1 else templates[0][0],
                'assessment_type': 'SELF',
                'name': templates[1][1] if len(templates) > 1 else templates[0][1],
                'version': '1.0',
                'round_number': 1,
                'sequence_number': 2,
                'unique_identifier': f'standard_standard_{templates[1][0] if len(templates) > 1 else templates[0][0]}_SM_006_1_2',
                'score': 0.0,
                'max_score': 100.0,
                'result': '待完成',
                'conclusion': '评估尚未完成',
                'status': 'pending',
                'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'updated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        ]
        
        # 插入评估数据
        for assessment in assessments_to_create:
            try:
                columns = ', '.join(assessment.keys())
                placeholders = ', '.join(['?' for _ in assessment])
                values = list(assessment.values())
                
                insert_sql = f"INSERT INTO assessments ({columns}) VALUES ({placeholders})"
                print(f"\n插入评估: {assessment['name']}")
                
                cursor.execute(insert_sql, values)
                assessment_id = cursor.lastrowid
                print(f"成功创建评估，ID: {assessment_id}")
                
            except Exception as e:
                print(f"创建评估失败: {e}")
                continue
        
        # 提交事务
        conn.commit()
        
        # 3. 验证创建的数据
        print("\n=== 验证创建的数据 ===")
        cursor.execute("""
            SELECT a.id, a.custom_id, a.name, a.status, a.template_id,
                   t.name as template_name, t.description, t.instructions
            FROM assessments a
            LEFT JOIN assessment_templates t ON a.template_id = t.id
            WHERE a.custom_id = 'SM_006'
        """)
        
        created_assessments = cursor.fetchall()
        print(f"为SM_006创建的评估数量: {len(created_assessments)}")
        
        for assessment in created_assessments:
            print(f"\n评估详情:")
            print(f"  ID: {assessment[0]}")
            print(f"  Custom ID: {assessment[1]}")
            print(f"  名称: {assessment[2]}")
            print(f"  状态: {assessment[3]}")
            print(f"  模板ID: {assessment[4]}")
            print(f"  模板名称: {assessment[5]}")
            print(f"  模板描述存在: {assessment[6] is not None}")
            print(f"  模板说明存在: {assessment[7] is not None}")
            if assessment[6]:
                print(f"  模板描述: {assessment[6][:100]}...")
            if assessment[7]:
                print(f"  模板说明: {assessment[7][:100]}...")
        
        print("\n评估数据创建完成！")
        
    except Exception as e:
        print(f"操作失败: {e}")
        conn.rollback()
    finally:
        conn.close()

def test_mobile_api_final():
    """最终测试移动端API"""
    print("\n=== 最终测试移动端API ===")
    
    # 获取token
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(
            "http://localhost:8006/api/auth/login_json",
            json=login_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            token = data.get('access_token')
            print(f"获取到token: {token[:20]}...")
            
            # 测试移动端API - 使用多种方式
            test_cases = [
                {
                    "name": "使用X-User-ID头部",
                    "headers": {
                        "Content-Type": "application/json",
                        "Authorization": f"Bearer {token}",
                        "X-User-ID": "SM_006"
                    },
                    "params": {}
                },
                {
                    "name": "使用custom_id参数",
                    "headers": {
                        "Content-Type": "application/json",
                        "Authorization": f"Bearer {token}"
                    },
                    "params": {"custom_id": "SM_006"}
                }
            ]
            
            for test_case in test_cases:
                print(f"\n--- {test_case['name']} ---")
                
                response = requests.get(
                    "http://localhost:8006/api/mobile/assessments",
                    headers=test_case['headers'],
                    params=test_case['params'],
                    timeout=10
                )
                
                print(f"响应状态码: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"响应数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")
                    
                    # 检查assessments数据
                    if isinstance(data, dict) and 'data' in data:
                        assessments_data = data['data']
                        if isinstance(assessments_data, dict) and 'assessments' in assessments_data:
                            assessments = assessments_data['assessments']
                        else:
                            assessments = assessments_data if isinstance(assessments_data, list) else []
                    else:
                        assessments = data if isinstance(data, list) else []
                    
                    print(f"返回 {len(assessments)} 个评估")
                    
                    for i, assessment in enumerate(assessments):
                        print(f"\n评估 {i+1}:")
                        print(f"  ID: {assessment.get('id')}")
                        print(f"  名称: {assessment.get('name')}")
                        print(f"  状态: {assessment.get('status')}")
                        
                        template = assessment.get('template')
                        print(f"  Template存在: {template is not None}")
                        
                        if template:
                            print(f"  Template ID: {template.get('id')}")
                            print(f"  Template名称: {template.get('name')}")
                            description = template.get('description')
                            instructions = template.get('instructions')
                            print(f"  Description存在: {description is not None and description != ''}")
                            print(f"  Instructions存在: {instructions is not None and instructions != ''}")
                            
                            if description:
                                print(f"  Description: {description[:100]}...")
                            if instructions:
                                print(f"  Instructions: {instructions[:100]}...")
                        else:
                            print(f"  ❌ Template为空！这是问题所在！")
                    
                    if len(assessments) > 0 and all(a.get('template') for a in assessments):
                        print(f"\n✅ 成功！所有评估都包含template信息，包括description和instructions")
                    elif len(assessments) > 0:
                        print(f"\n⚠️ 部分评估缺少template信息")
                    else:
                        print(f"\n❌ 没有返回任何评估数据")
                        
                else:
                    print(f"API调用失败: {response.status_code} - {response.text}")
        else:
            print(f"登录失败: {response.status_code}")
            
    except Exception as e:
        print(f"测试API异常: {e}")

def main():
    """主函数"""
    print("开始修复移动端template字段问题...")
    
    # 1. 创建评估数据
    create_assessment_correctly()
    
    # 2. 测试移动端API
    test_mobile_api_final()
    
    print("\n修复完成！")

if __name__ == "__main__":
    main()