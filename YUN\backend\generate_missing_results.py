#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为已有的响应数据生成缺失的结果记录
这将解决移动端历史记录Tab无法获取量表和问卷报告的问题
"""

import sqlite3
import sys
import os
import json
from datetime import datetime

def generate_missing_results():
    try:
        # 连接数据库
        db_path = "app.db"
        if not os.path.exists(db_path):
            print(f"❌ 数据库文件不存在: {db_path}")
            return
            
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("=== 生成缺失的结果记录 ===")
        print(f"数据库路径: {os.path.abspath(db_path)}")
        print(f"处理时间: {datetime.now()}")
        
        # 1. 处理questionnaire_responses中的数据
        print("\n1. 处理问卷响应数据:")
        cursor.execute("""
            SELECT id, questionnaire_id, custom_id, total_score, status, answers, report, created_at
            FROM questionnaire_responses
            WHERE status = 'completed' OR total_score IS NOT NULL
        """)
        questionnaire_responses = cursor.fetchall()
        
        print(f"   找到 {len(questionnaire_responses)} 条问卷响应记录")
        
        for response in questionnaire_responses:
            response_id, questionnaire_id, custom_id, total_score, status, answers, report, created_at = response
            
            # 检查是否已经有对应的结果记录
            cursor.execute("""
                SELECT id FROM questionnaire_results 
                WHERE response_id = ? OR (questionnaire_id = ? AND custom_id = ?)
            """, (response_id, questionnaire_id, custom_id))
            
            existing_result = cursor.fetchone()
            if existing_result:
                print(f"   跳过响应ID {response_id}，已有结果记录")
                continue
            
            # 获取问卷模板信息
            cursor.execute("""
                SELECT id, title, template_id FROM questionnaires WHERE id = ?
            """, (questionnaire_id,))
            questionnaire_info = cursor.fetchone()
            
            if not questionnaire_info:
                print(f"   ⚠️ 找不到问卷ID {questionnaire_id} 的信息")
                continue
            
            q_id, q_title, template_id = questionnaire_info
            
            # 生成结果记录
            now = datetime.now().isoformat()
            
            # 计算分数和百分比
            if total_score is None:
                total_score = 0
            
            max_score = 100  # 默认最大分数
            percentage = (total_score / max_score * 100) if max_score > 0 else 0
            
            # 生成结果等级和分类
            if percentage >= 80:
                result_level = "优秀"
                result_category = "良好"
            elif percentage >= 60:
                result_level = "良好"
                result_category = "中等"
            else:
                result_level = "需要改善"
                result_category = "较低"
            
            # 生成解释和建议
            interpretation = f"根据{q_title}的评估结果，您的得分为{total_score}分（{percentage:.1f}%），属于{result_level}水平。"
            recommendations = f"建议您继续保持良好的健康习惯，定期进行健康评估。"
            
            # 生成报告内容
            report_content = {
                "title": f"{q_title}评估报告",
                "score": total_score,
                "percentage": percentage,
                "level": result_level,
                "category": result_category,
                "interpretation": interpretation,
                "recommendations": recommendations,
                "generated_at": now
            }
            
            # 插入结果记录
            cursor.execute("""
                INSERT INTO questionnaire_results (
                    questionnaire_id, response_id, custom_id, template_id,
                    total_score, max_score, percentage, result_level, result_category,
                    interpretation, recommendations, dimension_scores, calculation_details,
                    raw_answers, report_generated, report_content, report_format, report_template,
                    status, calculated_at, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                questionnaire_id, response_id, custom_id, template_id,
                total_score, max_score, percentage, result_level, result_category,
                interpretation, recommendations, json.dumps({}), json.dumps({}),
                answers, 1, json.dumps(report_content), 'json', 'default',
                'completed', now, created_at, now
            ))
            
            print(f"   ✅ 为响应ID {response_id} 生成了结果记录（问卷: {q_title}，用户: {custom_id}）")
        
        # 2. 处理assessment_responses中的数据
        print("\n2. 处理量表响应数据:")
        cursor.execute("""
            SELECT id, assessment_id, custom_id, score, result, created_at
            FROM assessment_responses
            WHERE score IS NOT NULL OR result IS NOT NULL
        """)
        assessment_responses = cursor.fetchall()
        
        print(f"   找到 {len(assessment_responses)} 条量表响应记录")
        
        for response in assessment_responses:
            response_id, assessment_id, custom_id, score, result, created_at = response
            
            # 检查是否已经有对应的结果记录
            cursor.execute("""
                SELECT id FROM assessment_results 
                WHERE response_id = ? OR (assessment_id = ? AND custom_id = ?)
            """, (response_id, assessment_id, custom_id))
            
            existing_result = cursor.fetchone()
            if existing_result:
                print(f"   跳过响应ID {response_id}，已有结果记录")
                continue
            
            # 获取量表信息
            cursor.execute("""
                SELECT id, title, template_id FROM assessments WHERE id = ?
            """, (assessment_id,))
            assessment_info = cursor.fetchone()
            
            if not assessment_info:
                print(f"   ⚠️ 找不到量表ID {assessment_id} 的信息")
                continue
            
            a_id, a_title, template_id = assessment_info
            
            # 生成结果记录
            now = datetime.now().isoformat()
            
            # 计算分数和百分比
            if score is None:
                score = 0
            
            max_score = 100  # 默认最大分数
            percentage = (score / max_score * 100) if max_score > 0 else 0
            
            # 生成结果等级和分类
            if percentage >= 80:
                result_level = "优秀"
                result_category = "良好"
            elif percentage >= 60:
                result_level = "良好"
                result_category = "中等"
            else:
                result_level = "需要改善"
                result_category = "较低"
            
            # 生成解释和建议
            interpretation = f"根据{a_title}的评估结果，您的得分为{score}分（{percentage:.1f}%），属于{result_level}水平。"
            recommendations = f"建议您继续保持良好的健康习惯，定期进行健康评估。"
            
            # 生成报告内容
            report_content = {
                "title": f"{a_title}评估报告",
                "score": score,
                "percentage": percentage,
                "level": result_level,
                "category": result_category,
                "interpretation": interpretation,
                "recommendations": recommendations,
                "generated_at": now
            }
            
            # 插入结果记录
            cursor.execute("""
                INSERT INTO assessment_results (
                    assessment_id, response_id, custom_id, template_id,
                    total_score, max_score, percentage, result_level, result_category,
                    interpretation, recommendations, dimension_scores, calculation_details,
                    raw_answers, report_generated, report_content, report_format, report_template,
                    status, calculated_at, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                assessment_id, response_id, custom_id, template_id,
                score, max_score, percentage, result_level, result_category,
                interpretation, recommendations, json.dumps({}), json.dumps({}),
                result, 1, json.dumps(report_content), 'json', 'default',
                'completed', now, created_at, now
            ))
            
            print(f"   ✅ 为响应ID {response_id} 生成了结果记录（量表: {a_title}，用户: {custom_id}）")
        
        # 提交更改
        conn.commit()
        
        # 3. 验证生成的结果
        print("\n3. 验证生成的结果:")
        
        cursor.execute("SELECT COUNT(*) FROM questionnaire_results")
        q_result_count = cursor.fetchone()[0]
        print(f"   questionnaire_results表记录数: {q_result_count}")
        
        cursor.execute("SELECT COUNT(*) FROM assessment_results")
        a_result_count = cursor.fetchone()[0]
        print(f"   assessment_results表记录数: {a_result_count}")
        
        cursor.execute("SELECT COUNT(*) FROM questionnaire_results WHERE report_generated = 1")
        q_report_count = cursor.fetchone()[0]
        print(f"   有报告的问卷结果数: {q_report_count}")
        
        cursor.execute("SELECT COUNT(*) FROM assessment_results WHERE report_generated = 1")
        a_report_count = cursor.fetchone()[0]
        print(f"   有报告的量表结果数: {a_report_count}")
        
        print("\n=== 处理完成 ===")
        print("现在移动端历史记录Tab应该能够获取到量表和问卷报告了！")
        
        conn.close()
        
    except Exception as e:
        print(f"生成结果记录时出错: {e}")
        import traceback
        traceback.print_exc()
        if 'conn' in locals():
            conn.rollback()
            conn.close()

if __name__ == "__main__":
    generate_missing_results()