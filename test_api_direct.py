import sys
sys.path.append('c:/Users/<USER>/Desktop/health-Trea/YUN/backend')

from sqlalchemy.orm import Session
from app.db.base_session import SessionLocal
from app.models.assessment import Assessment
from app.models.distribution import AssessmentDistribution
from sqlalchemy import desc

def test_assessment_query():
    """直接测试数据库查询逻辑"""
    db = SessionLocal()
    try:
        print("=== 测试修复前的查询逻辑（inner join）===")
        # 原来的查询逻辑（inner join）
        query_inner = db.query(Assessment).join(AssessmentDistribution)
        query_inner = query_inner.filter(Assessment.custom_id == 'SM_006')
        query_inner = query_inner.order_by(desc(Assessment.created_at))
        assessments_inner = query_inner.all()
        print(f"Inner join结果: {len(assessments_inner)} 条记录")
        for i, assessment in enumerate(assessments_inner, 1):
            print(f"  {i}. {assessment.name} - {assessment.status} - {assessment.created_at}")
        
        print("\n=== 测试修复后的查询逻辑（outer join）===")
        # 修复后的查询逻辑（outer join）
        query_outer = db.query(Assessment).outerjoin(AssessmentDistribution)
        query_outer = query_outer.filter(Assessment.custom_id == 'SM_006')
        query_outer = query_outer.order_by(desc(Assessment.created_at))
        assessments_outer = query_outer.all()
        print(f"Outer join结果: {len(assessments_outer)} 条记录")
        for i, assessment in enumerate(assessments_outer, 1):
            print(f"  {i}. {assessment.name} - {assessment.status} - {assessment.created_at}")
        
        print("\n=== 直接查询Assessment表 ===")
        # 直接查询Assessment表
        query_direct = db.query(Assessment)
        query_direct = query_direct.filter(Assessment.custom_id == 'SM_006')
        query_direct = query_direct.order_by(desc(Assessment.created_at))
        assessments_direct = query_direct.all()
        print(f"直接查询结果: {len(assessments_direct)} 条记录")
        for i, assessment in enumerate(assessments_direct, 1):
            print(f"  {i}. {assessment.name} - {assessment.status} - {assessment.created_at}")
            
    finally:
        db.close()

if __name__ == "__main__":
    test_assessment_query()