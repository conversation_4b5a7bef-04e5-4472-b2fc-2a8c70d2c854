<template>
  <div class="data-mode-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <i class="el-icon-switch-button"></i>
            数据模式管理
          </h1>
          <p class="page-description">
            管理系统的数据模式，在模拟数据和生产数据之间进行切换
          </p>
        </div>
        
        <div class="header-right">
          <el-button 
            type="primary" 
            icon="el-icon-refresh"
            @click="refreshAll"
            :loading="refreshing"
          >
            刷新状态
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 数据模式切换组件 -->
      <div class="switch-section">
        <DataModeSwitch 
          @mode-changed="handleModeChanged"
          ref="dataModeSwitch"
        />
      </div>

      <!-- 系统影响说明 -->
      <div class="impact-section">
        <el-card class="impact-card">
          <div slot="header" class="card-header">
            <span class="card-title">
              <i class="el-icon-warning-outline"></i>
              模式切换影响说明
            </span>
          </div>
          
          <div class="impact-content">
            <div class="impact-item">
              <div class="impact-mode">
                <el-tag type="primary" size="small">自动模式</el-tag>
              </div>
              <div class="impact-description">
                <h4>自动模式 (推荐)</h4>
                <ul>
                  <li>根据环境变量 <code>ENABLE_MOCK_DATA</code> 自动选择数据源</li>
                  <li>开发环境默认使用模拟数据，生产环境使用真实数据</li>
                  <li>适合大多数使用场景，无需手动干预</li>
                </ul>
              </div>
            </div>
            
            <div class="impact-item">
              <div class="impact-mode">
                <el-tag type="warning" size="small">模拟数据模式</el-tag>
              </div>
              <div class="impact-description">
                <h4>模拟数据模式</h4>
                <ul>
                  <li>强制使用模拟数据，忽略环境变量设置</li>
                  <li>适用于开发、测试和演示场景</li>
                  <li>数据安全，不会影响真实业务数据</li>
                  <li>响应速度快，无需依赖外部数据源</li>
                </ul>
              </div>
            </div>
            
            <div class="impact-item">
              <div class="impact-mode">
                <el-tag type="success" size="small">生产数据模式</el-tag>
              </div>
              <div class="impact-description">
                <h4>生产数据模式</h4>
                <ul>
                  <li>强制使用真实生产数据，忽略环境变量设置</li>
                  <li>适用于生产环境和真实业务场景</li>
                  <li><strong>注意：</strong>需要确保数据库连接正常</li>
                  <li><strong>警告：</strong>操作将影响真实业务数据</li>
                </ul>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 快速操作面板 -->
      <div class="quick-actions">
        <el-card class="actions-card">
          <div slot="header" class="card-header">
            <span class="card-title">
              <i class="el-icon-magic-stick"></i>
              快速操作
            </span>
          </div>
          
          <div class="actions-content">
            <div class="action-group">
              <h4>开发场景</h4>
              <div class="action-buttons">
                <el-button 
                  type="warning" 
                  size="small"
                  @click="quickSwitch('mock', '开发测试需要')"
                  :loading="quickSwitching"
                >
                  <i class="el-icon-cpu"></i>
                  切换到模拟数据
                </el-button>
                
                <el-button 
                  type="primary" 
                  size="small"
                  @click="quickSwitch('auto', '恢复自动模式')"
                  :loading="quickSwitching"
                >
                  <i class="el-icon-magic-stick"></i>
                  恢复自动模式
                </el-button>
              </div>
            </div>
            
            <div class="action-group">
              <h4>生产场景</h4>
              <div class="action-buttons">
                <el-button 
                  type="success" 
                  size="small"
                  @click="confirmProductionSwitch"
                  :loading="quickSwitching"
                >
                  <i class="el-icon-connection"></i>
                  切换到生产数据
                </el-button>
                
                <el-button 
                  type="info" 
                  size="small"
                  @click="testConnection"
                  :loading="testing"
                >
                  <i class="el-icon-cpu"></i>
                  测试数据连接
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 监控信息 -->
      <div class="monitoring-section">
        <el-card class="monitoring-card">
          <div slot="header" class="card-header">
            <span class="card-title">
              <i class="el-icon-monitor"></i>
              系统监控
            </span>
            <el-button 
              type="text" 
              size="mini"
              @click="refreshMonitoring"
              :loading="monitoringLoading"
            >
              <i class="el-icon-refresh"></i>
            </el-button>
          </div>
          
          <div class="monitoring-content">
            <div class="monitoring-grid">
              <div class="monitoring-item">
                <div class="item-label">API响应时间</div>
                <div class="item-value" :class="getResponseTimeClass(monitoring.responseTime)">
                  {{ monitoring.responseTime || '-' }}ms
                </div>
              </div>
              
              <div class="monitoring-item">
                <div class="item-label">数据库连接</div>
                <div class="item-value">
                  <el-tag 
                    :type="monitoring.dbConnection ? 'success' : 'danger'" 
                    size="mini"
                  >
                    {{ monitoring.dbConnection ? '正常' : '异常' }}
                  </el-tag>
                </div>
              </div>
              
              <div class="monitoring-item">
                <div class="item-label">模拟数据状态</div>
                <div class="item-value">
                  <el-tag 
                    :type="monitoring.mockDataStatus ? 'success' : 'info'" 
                    size="mini"
                  >
                    {{ monitoring.mockDataStatus ? '可用' : '不可用' }}
                  </el-tag>
                </div>
              </div>
              
              <div class="monitoring-item">
                <div class="item-label">最后检查时间</div>
                <div class="item-value">{{ formatTime(monitoring.lastCheck) }}</div>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
import DataModeSwitch from '@/components/common/DataModeSwitch.vue'
import { testDataMode } from '@/api/dataMode'

export default {
  name: 'DataModeManagement',
  components: {
    DataModeSwitch
  },
  
  data() {
    return {
      refreshing: false,
      quickSwitching: false,
      testing: false,
      monitoringLoading: false,
      
      // 监控数据
      monitoring: {
        responseTime: null,
        dbConnection: true,
        mockDataStatus: true,
        lastCheck: null
      }
    }
  },
  
  mounted() {
    this.initPage()
  },
  
  methods: {
    // 初始化页面
    async initPage() {
      await this.refreshMonitoring()
    },
    
    // 刷新所有数据
    async refreshAll() {
      this.refreshing = true
      try {
        await Promise.all([
          this.$refs.dataModeSwitch?.refreshStatus(),
          this.refreshMonitoring()
        ])
        this.$message.success('所有数据已刷新')
      } catch (error) {
        console.error('刷新失败:', error)
        this.$message.error('刷新失败')
      } finally {
        this.refreshing = false
      }
    },
    
    // 处理模式切换事件
    handleModeChanged(event) {
      this.$message.success(
        `数据模式已从 "${event.oldMode}" 切换到 "${event.newMode}"`
      )
      
      // 刷新监控数据
      setTimeout(() => {
        this.refreshMonitoring()
      }, 1000)
    },
    
    // 快速切换
    async quickSwitch(mode, reason) {
      this.quickSwitching = true
      try {
        await this.$refs.dataModeSwitch?.performSwitch({
          mode,
          reason
        })
      } catch (error) {
        console.error('快速切换失败:', error)
      } finally {
        this.quickSwitching = false
      }
    },
    
    // 确认切换到生产模式
    confirmProductionSwitch() {
      this.$confirm(
        '切换到生产数据模式将使用真实的业务数据，请确认您了解此操作的影响。',
        '切换到生产数据模式',
        {
          confirmButtonText: '确认切换',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: true,
          message: `
            <div style="margin: 16px 0;">
              <p><strong>注意事项：</strong></p>
              <ul style="margin: 8px 0; padding-left: 20px;">
                <li>将使用真实的生产数据库</li>
                <li>所有操作将影响真实业务数据</li>
                <li>请确保数据库连接正常</li>
                <li>建议在非业务高峰期进行切换</li>
              </ul>
            </div>
          `
        }
      ).then(() => {
        this.quickSwitch('production', '手动切换到生产模式')
      }).catch(() => {
        this.$message.info('已取消切换')
      })
    },
    
    // 测试连接
    async testConnection() {
      this.testing = true
      try {
        await this.$refs.dataModeSwitch?.testCurrentMode()
      } catch (error) {
        console.error('测试连接失败:', error)
      } finally {
        this.testing = false
      }
    },
    
    // 刷新监控数据
    async refreshMonitoring() {
      this.monitoringLoading = true
      try {
        // 模拟监控数据获取
        const startTime = Date.now()
        
        // 测试API响应时间
        try {
          await testDataMode()
          this.monitoring.responseTime = Date.now() - startTime
          this.monitoring.dbConnection = true
          this.monitoring.mockDataStatus = true
        } catch (error) {
          this.monitoring.responseTime = Date.now() - startTime
          this.monitoring.dbConnection = false
          this.monitoring.mockDataStatus = false
        }
        
        this.monitoring.lastCheck = new Date().toISOString()
      } catch (error) {
        console.error('刷新监控数据失败:', error)
      } finally {
        this.monitoringLoading = false
      }
    },
    
    // 获取响应时间样式类
    getResponseTimeClass(responseTime) {
      if (!responseTime) return ''
      if (responseTime < 200) return 'response-good'
      if (responseTime < 500) return 'response-normal'
      return 'response-slow'
    },
    
    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return '-'
      try {
        const date = new Date(timeStr)
        return date.toLocaleString('zh-CN')
      } catch (error) {
        return timeStr
      }
    }
  }
}
</script>

<style scoped>
.data-mode-management {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background: #fff;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title i {
  font-size: 28px;
  color: #409eff;
}

.page-description {
  font-size: 14px;
  color: #606266;
  margin: 0;
  line-height: 1.5;
}

.header-right {
  margin-left: 24px;
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.switch-section {
  margin-bottom: 20px;
}

.impact-section,
.quick-actions,
.monitoring-section {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-title i {
  font-size: 18px;
  color: #409eff;
}

.impact-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.impact-item {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.impact-mode {
  min-width: 120px;
  padding-top: 4px;
}

.impact-description {
  flex: 1;
}

.impact-description h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.impact-description ul {
  margin: 0;
  padding-left: 20px;
  color: #606266;
  font-size: 14px;
  line-height: 1.6;
}

.impact-description li {
  margin-bottom: 4px;
}

.impact-description code {
  background-color: #f5f7fa;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #e6a23c;
}

.actions-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.action-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-group h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.monitoring-content {
  padding: 16px 0;
}

.monitoring-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.monitoring-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px;
  background-color: #f9fafc;
  border-radius: 6px;
  border: 1px solid #ebeef5;
}

.item-label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.item-value {
  font-size: 14px;
  color: #303133;
  font-weight: 600;
}

.item-value.response-good {
  color: #67c23a;
}

.item-value.response-normal {
  color: #e6a23c;
}

.item-value.response-slow {
  color: #f56c6c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .data-mode-management {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-right {
    margin-left: 0;
    align-self: flex-start;
  }
  
  .impact-item {
    flex-direction: column;
    gap: 8px;
  }
  
  .impact-mode {
    min-width: auto;
    padding-top: 0;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .monitoring-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 20px;
  }
  
  .page-title i {
    font-size: 24px;
  }
}
</style>