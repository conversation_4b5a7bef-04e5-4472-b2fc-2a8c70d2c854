<template>
  <div class="other-records-tab">
    <div class="tab-header">
      <h3>其它记录</h3>
      <div class="action-buttons">
        <el-button type="primary" size="small" @click="openAddDialog" v-if="editable">添加记录</el-button>
        <el-button type="primary" size="small" @click="refreshData">刷新数据</el-button>
      </div>
    </div>

    <el-table
      :data="records"
      style="width: 100%"
      v-loading="loading"
      :empty-text="emptyText"
    >
      <el-table-column prop="record_date" label="记录日期" width="120">
        <template #default="scope">
          {{ formatDate(scope.row.record_date) }}
        </template>
      </el-table-column>
      <el-table-column prop="record_type" label="记录类型" width="150">
        <template #default="scope">
          {{ getRecordTypeLabel(scope.row.record_type) }}
        </template>
      </el-table-column>
      <el-table-column prop="title" label="标题" width="200" />
      <el-table-column prop="is_important" label="重要标记" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.is_important ? 'danger' : 'info'">
            {{ scope.row.is_important ? '重要' : '普通' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="content" label="内容" show-overflow-tooltip />
      <el-table-column label="操作" width="180" fixed="right">
        <template #default="scope">
          <el-button type="primary" link @click="viewRecord(scope.row)">查看</el-button>
          <el-button type="success" link @click="editRecord(scope.row)" v-if="editable">编辑</el-button>
          <el-button type="danger" link @click="deleteRecord(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 记录详情对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      :title="currentRecord ? currentRecord.title : '记录详情'"
      width="60%"
    >
      <div v-if="currentRecord" class="record-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="记录日期">{{ formatDate(currentRecord.record_date) }}</el-descriptions-item>
          <el-descriptions-item label="记录类型">{{ getRecordTypeLabel(currentRecord.record_type) }}</el-descriptions-item>
          <el-descriptions-item label="重要标记">
            <el-tag :type="currentRecord.is_important ? 'danger' : 'info'">
              {{ currentRecord.is_important ? '重要' : '普通' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="相关人员" v-if="currentRecord.related_person">{{ currentRecord.related_person }}</el-descriptions-item>
          <el-descriptions-item label="相关机构" v-if="currentRecord.related_organization">{{ currentRecord.related_organization }}</el-descriptions-item>
          <el-descriptions-item label="相关地点" v-if="currentRecord.related_location">{{ currentRecord.related_location }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="record-content">
          <h4>记录内容</h4>
          <div class="content-text">{{ currentRecord.content }}</div>
        </div>
        
        <div class="record-notes" v-if="currentRecord.notes">
          <h4>备注</h4>
          <div class="notes-text">{{ currentRecord.notes }}</div>
        </div>
      </div>
    </el-dialog>

    <!-- 添加/编辑记录对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      :title="isEditing ? '编辑记录' : '添加记录'"
      width="60%"
    >
      <el-form :model="recordForm" label-width="120px" :rules="rules" ref="recordFormRef">
        <el-form-item label="记录日期" prop="record_date">
          <el-date-picker v-model="recordForm.record_date" type="date" placeholder="选择记录日期" style="width: 100%" />
        </el-form-item>
        <el-form-item label="记录类型" prop="record_type">
          <el-select v-model="recordForm.record_type" placeholder="请选择记录类型" style="width: 100%">
            <el-option label="过敏记录" value="allergy" />
            <el-option label="疫苗接种" value="vaccination" />
            <el-option label="家族病史" value="family_history" />
            <el-option label="生活事件" value="life_event" />
            <el-option label="营养补充" value="supplement" />
            <el-option label="其他记录" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="标题" prop="title">
          <el-input v-model="recordForm.title" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item label="重要标记">
          <el-switch v-model="recordForm.is_important" />
        </el-form-item>
        <el-form-item label="相关人员">
          <el-input v-model="recordForm.related_person" placeholder="请输入相关人员（可选）" />
        </el-form-item>
        <el-form-item label="相关机构">
          <el-input v-model="recordForm.related_organization" placeholder="请输入相关机构（可选）" />
        </el-form-item>
        <el-form-item label="相关地点">
          <el-input v-model="recordForm.related_location" placeholder="请输入相关地点（可选）" />
        </el-form-item>
        <el-form-item label="记录内容" prop="content">
          <el-input v-model="recordForm.content" type="textarea" rows="6" placeholder="请输入记录内容" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="recordForm.notes" type="textarea" rows="2" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveRecord">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import axios from 'axios';

const props = defineProps({
  customId: {
    type: [Number, String],
    required: true
  },
  editable: {
    type: Boolean,
    default: false
  }
});

const loading = ref(false);
const records = ref([]);
const viewDialogVisible = ref(false);
const editDialogVisible = ref(false);
const currentRecord = ref(null);
const isEditing = ref(false);
const recordFormRef = ref(null);

const recordForm = ref({
  record_date: '',
  record_type: '',
  title: '',
  is_important: false,
  related_person: '',
  related_organization: '',
  related_location: '',
  content: '',
  notes: ''
});

const rules = {
  record_date: [
    { required: true, message: '请选择记录日期', trigger: 'change' }
  ],
  record_type: [
    { required: true, message: '请选择记录类型', trigger: 'change' }
  ],
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入记录内容', trigger: 'blur' }
  ]
};

const emptyText = computed(() => {
  return loading.value ? '加载中...' : '暂无记录数据';
});

// 统一聚合接口请求
const fetchUserHealthRecords = async () => {
  if (!props.customId) return;
  loading.value = true;
  try {
    const response = await axios.get(`/api/v1/aggregated/health-profile/${props.customId}`, {
      params: {
        include_other_records: true
      }
    });
    const profileData = response.data.profile_data || {};
    records.value = profileData.other_records || [];
  } catch (error) {
    console.error('获取其他记录失败:', error);
    records.value = [];
  } finally {
    loading.value = false;
  }
};

watch(() => props.customId, (newVal) => {
  if (newVal) {
    fetchUserHealthRecords();
  } else {
    records.value = [];
  }
}, { immediate: true });

onMounted(() => {
  if (props.customId) {
    fetchUserHealthRecords();
  }
});



// 刷新数据
const refreshData = () => {
  fetchUserHealthRecords();
};

// 查看记录详情
const viewRecord = (record) => {
  currentRecord.value = record;
  viewDialogVisible.value = true;
};

// 打开添加记录对话框
const openAddDialog = () => {
  isEditing.value = false;
  recordForm.value = {
    record_date: new Date(),
    record_type: '',
    title: '',
    is_important: false,
    related_person: '',
    related_organization: '',
    related_location: '',
    content: '',
    notes: ''
  };
  editDialogVisible.value = true;
};

// 编辑记录
const editRecord = (record) => {
  isEditing.value = true;
  recordForm.value = { ...record };
  
  // 转换日期字符串为Date对象
  if (recordForm.value.record_date) {
    recordForm.value.record_date = new Date(recordForm.value.record_date);
  }
  
  editDialogVisible.value = true;
};

// 保存记录
const saveRecord = async () => {
  if (!recordFormRef.value) return;
  
  await recordFormRef.value.validate(async (valid) => {
    if (!valid) {
      ElMessage.warning('请完善表单信息');
      return;
    }
    
    try {
      const formData = { ...recordForm.value };
      formData.custom_id = props.customId;
      
      // 转换日期对象为ISO字符串
      if (formData.record_date instanceof Date) {
        formData.record_date = formData.record_date.toISOString().split('T')[0];
      }
      
      if (isEditing.value) {
        // 更新现有记录
        await axios.put(`/api/other-records/${formData.id}`, formData);
        ElMessage.success('记录更新成功');
      } else {
        // 添加新记录
        await axios.post('/api/other-records', formData);
        ElMessage.success('记录添加成功');
      }
      
      editDialogVisible.value = false;
      fetchUserHealthRecords(); // 刷新数据
      
      // 模拟保存成功（实际项目中应删除）
      if (isEditing.value) {
        const index = records.value.findIndex(item => item.id === formData.id);
        if (index !== -1) {
          records.value[index] = formData;
        }
      } else {
        formData.id = Date.now(); // 模拟生成ID
        records.value.push(formData);
      }
    } catch (error) {
      console.error('保存记录失败:', error);
      ElMessage.error('保存记录失败，请稍后重试');
    }
  });
};

// 删除记录
const deleteRecord = (record) => {
  ElMessageBox.confirm(
    `确定要删除 ${record.title} 的记录吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await axios.delete(`/api/other-records/${record.id}`);
      ElMessage.success('删除成功');
      fetchUserHealthRecords(); // 刷新数据
    } catch (error) {
      console.error('删除记录失败:', error);
      ElMessage.error('删除记录失败，请稍后重试');
      
      // 模拟删除成功（实际项目中应删除）
      records.value = records.value.filter(item => item.id !== record.id);
      ElMessage.success('删除成功');
    }
  }).catch(() => {
    // 用户取消删除
  });
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  return date.toLocaleDateString();
};

// 获取记录类型标签
const getRecordTypeLabel = (type) => {
  const typeMap = {
    'allergy': '过敏记录',
    'vaccination': '疫苗接种',
    'family_history': '家族病史',
    'life_event': '生活事件',
    'supplement': '营养补充',
    'other': '其他记录'
  };
  
  return typeMap[type] || type;
};
</script>

<style scoped>
.other-records-tab {
  padding: 10px;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.tab-header h3 {
  margin: 0;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.record-detail {
  padding: 10px;
}

.record-content,
.record-notes {
  margin-top: 20px;
}

.record-content h4,
.record-notes h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #303133;
  font-size: 16px;
}

.content-text,
.notes-text {
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  white-space: pre-line;
}
</style>
