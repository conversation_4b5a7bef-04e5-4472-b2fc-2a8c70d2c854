from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 创建数据库连接
engine = create_engine('sqlite:///c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db')
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
db = SessionLocal()

try:
    # 测试API的完整查询逻辑
    print("=== 测试API的完整查询逻辑 ===")
    
    # 1. 测试不带状态过滤的查询
    print("\n1. 不带状态过滤的查询:")
    result = db.execute(text("""
        SELECT DISTINCT a.id, a.name, a.status, a.created_at
        FROM assessments a
        LEFT JOIN assessment_distributions ad ON a.id = ad.assessment_id
        WHERE a.custom_id = 'SM_006'
        ORDER BY a.created_at DESC
    """))
    
    rows = result.fetchall()
    print(f'查询结果数量: {len(rows)}')
    
    for row in rows:
        print(f'  ID: {row[0]}, 名称: {row[1]}, 状态: {row[2]}, 创建时间: {row[3]}')
    
    # 2. 测试只查询pending状态的量表
    print("\n2. 只查询pending状态的量表:")
    result2 = db.execute(text("""
        SELECT DISTINCT a.id, a.name, a.status, a.created_at
        FROM assessments a
        LEFT JOIN assessment_distributions ad ON a.id = ad.assessment_id
        WHERE a.custom_id = 'SM_006' AND a.status = 'pending'
        ORDER BY a.created_at DESC
    """))
    
    rows2 = result2.fetchall()
    print(f'查询结果数量: {len(rows2)}')
    
    for row in rows2:
        print(f'  ID: {row[0]}, 名称: {row[1]}, 状态: {row[2]}, 创建时间: {row[3]}')
    
    # 3. 测试只查询completed状态的量表
    print("\n3. 只查询completed状态的量表:")
    result3 = db.execute(text("""
        SELECT DISTINCT a.id, a.name, a.status, a.created_at
        FROM assessments a
        LEFT JOIN assessment_distributions ad ON a.id = ad.assessment_id
        WHERE a.custom_id = 'SM_006' AND a.status = 'completed'
        ORDER BY a.created_at DESC
    """))
    
    rows3 = result3.fetchall()
    print(f'查询结果数量: {len(rows3)}')
    
    for row in rows3:
        print(f'  ID: {row[0]}, 名称: {row[1]}, 状态: {row[2]}, 创建时间: {row[3]}')
    
    # 4. 测试分页逻辑
    print("\n4. 测试分页逻辑 (limit 2, offset 0):")
    result4 = db.execute(text("""
        SELECT DISTINCT a.id, a.name, a.status, a.created_at
        FROM assessments a
        LEFT JOIN assessment_distributions ad ON a.id = ad.assessment_id
        WHERE a.custom_id = 'SM_006'
        ORDER BY a.created_at DESC
        LIMIT 2 OFFSET 0
    """))
    
    rows4 = result4.fetchall()
    print(f'查询结果数量: {len(rows4)}')
    
    for row in rows4:
        print(f'  ID: {row[0]}, 名称: {row[1]}, 状态: {row[2]}, 创建时间: {row[3]}')
    
    # 5. 测试API中的count逻辑
    print("\n5. 测试count逻辑:")
    result5 = db.execute(text("""
        SELECT COUNT(DISTINCT a.id)
        FROM assessments a
        LEFT JOIN assessment_distributions ad ON a.id = ad.assessment_id
        WHERE a.custom_id = 'SM_006'
    """))
    
    count = result5.scalar()
    print(f'总记录数: {count}')
    
finally:
    db.close()