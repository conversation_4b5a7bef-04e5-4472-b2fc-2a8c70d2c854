#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接查询数据库中SM_006用户的量表数据
"""

import sys
import os

# 添加后端路径到Python路径
backend_path = os.path.join(os.path.dirname(__file__), 'YUN', 'backend')
sys.path.insert(0, backend_path)

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import logging

# 直接使用数据库URL
DATABASE_URL = "sqlite:///./YUN/backend/app.db"

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_database_data():
    """
    直接查询数据库中的量表和问卷数据
    """
    try:
        # 创建数据库连接
        engine = create_engine(DATABASE_URL)
        Session = sessionmaker(bind=engine)
        db = Session()
        
        logger.info("=== 直接查询数据库中SM_006用户的数据 ===")
        
        # 查询SM_006用户的所有量表
        result = db.execute(text("SELECT * FROM assessments WHERE custom_id = 'SM_006'"))
        all_assessments = result.fetchall()
        logger.info(f"SM_006用户总量表数量: {len(all_assessments)}")
        
        # 按状态分组统计
        pending_count = 0
        completed_count = 0
        
        logger.info("\n=== SM_006用户量表详情 ===")
        for i, assessment in enumerate(all_assessments, 1):
            # 获取列名和值
            row_dict = dict(assessment._mapping)
            status = row_dict.get('status', 'unknown')
            if status == 'pending':
                pending_count += 1
            elif status == 'completed':
                completed_count += 1
            
            logger.info(f"{i}. ID: {row_dict.get('id')}, Name: {row_dict.get('name')}, Status: {status}, Created: {row_dict.get('created_at')}")
        
        logger.info(f"\nPending状态量表数量: {pending_count}")
        logger.info(f"Completed状态量表数量: {completed_count}")
        
        # 查询SM_006用户的所有问卷
        result = db.execute(text("SELECT * FROM questionnaires WHERE custom_id = 'SM_006'"))
        all_questionnaires = result.fetchall()
        logger.info(f"\nSM_006用户总问卷数量: {len(all_questionnaires)}")
        
        # 按状态分组统计问卷
        q_pending_count = 0
        q_completed_count = 0
        
        logger.info("\n=== SM_006用户问卷详情 ===")
        for i, questionnaire in enumerate(all_questionnaires, 1):
            row_dict = dict(questionnaire._mapping)
            status = row_dict.get('status', 'unknown')
            if status == 'pending':
                q_pending_count += 1
            elif status == 'completed':
                q_completed_count += 1
            
            logger.info(f"{i}. ID: {row_dict.get('id')}, Name: {row_dict.get('name')}, Status: {status}, Created: {row_dict.get('created_at')}")
        
        logger.info(f"\nPending状态问卷数量: {q_pending_count}")
        logger.info(f"Completed状态问卷数量: {q_completed_count}")
        
        # 查询所有用户的量表数据统计
        logger.info("\n=== 所有用户的量表统计 ===")
        result = db.execute(text("SELECT custom_id, status, COUNT(*) as count FROM assessments GROUP BY custom_id, status ORDER BY custom_id, status"))
        stats = result.fetchall()
        
        custom_id_stats = {}
        for row in stats:
            row_dict = dict(row._mapping)
            custom_id = row_dict.get('custom_id') or 'None'
            status = row_dict.get('status', 'unknown')
            count = row_dict.get('count', 0)
            
            if custom_id not in custom_id_stats:
                custom_id_stats[custom_id] = {'total': 0, 'pending': 0, 'completed': 0}
            
            custom_id_stats[custom_id]['total'] += count
            if status == 'pending':
                custom_id_stats[custom_id]['pending'] = count
            elif status == 'completed':
                custom_id_stats[custom_id]['completed'] = count
        
        logger.info("\n=== 按用户统计量表数据 ===")
        for custom_id, stats in custom_id_stats.items():
            logger.info(f"Custom_ID: {custom_id} - 总数: {stats['total']}, Pending: {stats['pending']}, Completed: {stats['completed']}")
        
        db.close()
        
    except Exception as e:
        logger.error(f"查询数据库失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_database_data()