#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库中的assessment相关数据
特别关注SM_008用户的蒙特利尔认知评估量表回答情况
"""

import sqlite3
import os
import json

def check_assessment_data():
    """检查assessment相关的数据"""
    print("=== 检查Assessment数据 ===")
    
    # 数据库路径
    db_path = "c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db"
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 检查assessments表结构
        print("\n1. 检查assessments表结构...")
        cursor.execute("PRAGMA table_info(assessments)")
        columns = cursor.fetchall()
        print("assessments表字段:")
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
        
        cursor.execute("SELECT COUNT(*) FROM assessments")
        assessment_count = cursor.fetchone()[0]
        print(f"\nassessments表中有 {assessment_count} 条记录")
        
        if assessment_count > 0:
            cursor.execute("SELECT * FROM assessments LIMIT 3")
            assessments = cursor.fetchall()
            print("\n前3条assessment记录:")
            for i, assessment in enumerate(assessments):
                print(f"  记录 {i+1}: {assessment}")
        
        # 2. 检查assessment_distributions表
        print("\n2. 检查assessment_distributions表...")
        cursor.execute("SELECT COUNT(*) FROM assessment_distributions")
        distribution_count = cursor.fetchone()[0]
        print(f"assessment_distributions表中有 {distribution_count} 条记录")
        
        if distribution_count > 0:
            cursor.execute("""
                SELECT ad.id, ad.custom_id, ad.assessment_id, ad.status, a.name 
                FROM assessment_distributions ad 
                LEFT JOIN assessments a ON ad.assessment_id = a.id 
                LIMIT 5
            """)
            distributions = cursor.fetchall()
            print("\n前5条distribution记录:")
            for dist in distributions:
                print(f"  ID: {dist[0]}, 用户: {dist[1]}, 评估ID: {dist[2]}, 状态: {dist[3]}, 名称: {dist[4]}")
        
        # 3. 检查SM_008用户的assessment_distributions
        print("\n3. 检查SM_008用户的assessment_distributions...")
        cursor.execute("""
            SELECT ad.id, ad.assessment_id, ad.status, a.name 
            FROM assessment_distributions ad 
            LEFT JOIN assessments a ON ad.assessment_id = a.id 
            WHERE ad.custom_id = 'SM_008'
        """)
        sm008_distributions = cursor.fetchall()
        print(f"SM_008用户有 {len(sm008_distributions)} 条assessment分发记录")
        
        for dist in sm008_distributions:
            print(f"  ID: {dist[0]}, 评估ID: {dist[1]}, 状态: {dist[2]}, 名称: {dist[3]}")
        
        # 4. 检查assessment_responses表
        print("\n4. 检查assessment_responses表...")
        cursor.execute("SELECT COUNT(*) FROM assessment_responses")
        response_count = cursor.fetchone()[0]
        print(f"assessment_responses表中有 {response_count} 条记录")
        
        if response_count > 0:
            cursor.execute("""
                SELECT ar.id, ar.custom_id, ar.assessment_id, a.name 
                FROM assessment_responses ar 
                LEFT JOIN assessments a ON ar.assessment_id = a.id 
                LIMIT 5
            """)
            responses = cursor.fetchall()
            print("\n前5条response记录:")
            for resp in responses:
                print(f"  ID: {resp[0]}, 用户: {resp[1]}, 评估ID: {resp[2]}, 名称: {resp[3]}")
        
        # 5. 检查SM_008用户的assessment_responses
        print("\n5. 检查SM_008用户的assessment_responses...")
        cursor.execute("""
            SELECT ar.id, ar.assessment_id, a.name 
            FROM assessment_responses ar 
            LEFT JOIN assessments a ON ar.assessment_id = a.id 
            WHERE ar.custom_id = 'SM_008'
        """)
        sm008_responses = cursor.fetchall()
        print(f"SM_008用户有 {len(sm008_responses)} 条assessment回答记录")
        
        for resp in sm008_responses:
            print(f"  ID: {resp[0]}, 评估ID: {resp[1]}, 名称: {resp[2]}")
        
        # 6. 检查所有用户的assessment数据
        print("\n6. 检查所有用户的assessment数据分布...")
        cursor.execute("""
            SELECT custom_id, COUNT(*) as count
            FROM assessment_distributions 
            GROUP BY custom_id
        """)
        user_distributions = cursor.fetchall()
        print("\n各用户的assessment分发数量:")
        for user_dist in user_distributions:
            print(f"  用户 {user_dist[0]}: {user_dist[1]} 条")
        
        conn.close()
        
    except Exception as e:
        print(f"检查数据库时出错: {str(e)}")
    
    print("\n=== 检查完成 ===")

def check_sm008_moca_responses():
    """专门检查SM_008用户的蒙特利尔认知评估量表回答情况"""
    print("\n=== 检查SM_008用户蒙特利尔认知评估量表回答情况 ===")
    
    # 初始化变量
    answered_questions = set()
    
    # 数据库路径
    db_path = "c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db"
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 查找蒙特利尔认知评估量表的assessment_id
        print("\n1. 查找蒙特利尔认知评估量表...")
        cursor.execute("""
            SELECT id, name 
            FROM assessments 
            WHERE name LIKE '%蒙特利尔%' OR name LIKE '%MoCA%' OR name LIKE '%Montreal%'
        """)
        moca_assessments = cursor.fetchall()
        
        if not moca_assessments:
            print("未找到蒙特利尔认知评估量表")
            return
        
        for assessment in moca_assessments:
            print(f"  找到评估: ID={assessment[0]}, 名称={assessment[1]}")
        
        moca_assessment_id = moca_assessments[0][0]  # 使用第一个找到的
        
        # 2. 检查SM_008用户是否有该评估的分发记录
        print(f"\n2. 检查SM_008用户的评估分发记录...")
        cursor.execute("""
            SELECT id, status, created_at, updated_at 
            FROM assessment_distributions 
            WHERE custom_id = 'SM_008' AND assessment_id = ?
        """, (moca_assessment_id,))
        distributions = cursor.fetchall()
        
        if not distributions:
            print("SM_008用户没有蒙特利尔认知评估量表的分发记录")
            return
        
        for dist in distributions:
            print(f"  分发记录: ID={dist[0]}, 状态={dist[1]}, 创建时间={dist[2]}, 更新时间={dist[3]}")
        
        # 3. 检查assessment_responses表结构
        print("\n3. 检查assessment_responses表结构...")
        cursor.execute("PRAGMA table_info(assessment_responses)")
        columns = cursor.fetchall()
        print("assessment_responses表字段:")
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
        
        # 4. 检查SM_008用户的回答记录
        print(f"\n4. 检查SM_008用户的蒙特利尔认知评估量表回答记录...")
        cursor.execute("""
            SELECT * FROM assessment_responses 
            WHERE custom_id = 'SM_008' AND assessment_id = ?
        """, (moca_assessment_id,))
        responses = cursor.fetchall()
        
        if not responses:
            print("SM_008用户没有蒙特利尔认知评估量表的回答记录")
            print("\n可能的原因分析:")
            print("1. 用户还未开始填写问卷")
            print("2. 前端页面存在问题，无法正常提交回答")
            print("3. 后端API接口存在问题")
            print("4. 数据库写入过程中出现错误")
            
            # 检查其他用户是否有该评估的回答记录
            cursor.execute("""
                SELECT custom_id, COUNT(*) as count
                FROM assessment_responses 
                WHERE assessment_id = ?
                GROUP BY custom_id
            """, (moca_assessment_id,))
            other_responses = cursor.fetchall()
            
            if other_responses:
                print("\n其他用户的回答情况:")
                for resp in other_responses:
                    print(f"  用户 {resp[0]}: {resp[1]} 条回答记录")
            else:
                print("\n所有用户都没有该评估的回答记录，可能是系统性问题")
            
            return
        
        print(f"找到 {len(responses)} 条回答记录")
        
        # 5. 分析每条回答记录的详细内容
        print("\n5. 分析回答记录详细内容...")
        
        # 获取列名
        column_names = [description[0] for description in cursor.description]
        
        for i, response in enumerate(responses):
            print(f"\n  回答记录 {i+1}:")
            response_dict = dict(zip(column_names, response))
            
            for key, value in response_dict.items():
                if key == 'answers' and value:
                    try:
                        answers_data = json.loads(value)
                        print(f"    {key}: {json.dumps(answers_data, ensure_ascii=False, indent=6)}")
                    except:
                        print(f"    {key}: {value}")
                else:
                    print(f"    {key}: {value}")
        
        # 6. 检查问题模板和回答的对应关系
        print("\n6. 检查问题模板和回答的对应关系...")
        
        # 先查看数据库中存在的表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print("数据库中的表:")
        for table in tables:
            print(f"  {table[0]}")
        
        # 尝试不同的表名来获取问题模板
        questions = []
        possible_tables = ['questionnaire_questions', 'assessment_template_questions', 'questionnaire_template_questions', 'questionnaire_items']
        
        for table_name in possible_tables:
            try:
                cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
                if cursor.fetchone():
                    print(f"\n尝试从表 {table_name} 获取问题...")
                    cursor.execute(f"PRAGMA table_info({table_name})")
                    columns = cursor.fetchall()
                    print(f"{table_name}表字段:")
                    for col in columns:
                        print(f"  {col[1]} ({col[2]})")
                    
                    # 根据表结构调整查询
                    if table_name == 'assessment_template_questions':
                        cursor.execute(f"""
                            SELECT question_id, question_text, question_type, options, order_num
                            FROM {table_name} 
                            WHERE assessment_id = ?
                            ORDER BY order_num
                        """, (moca_assessment_id,))
                    elif table_name == 'questionnaire_template_questions':
                        cursor.execute(f"""
                            SELECT question_id, question_text, question_type, options, order_num
                            FROM {table_name} 
                            WHERE template_id = ?
                            ORDER BY order_num
                        """, (moca_assessment_id,))
                    else:
                        cursor.execute(f"""
                            SELECT * FROM {table_name} 
                            WHERE questionnaire_id = ?
                            ORDER BY id
                        """, (moca_assessment_id,))
                    
                    questions = cursor.fetchall()
                    if questions:
                        print(f"从 {table_name} 找到 {len(questions)} 个问题")
                        break
            except Exception as e:
                print(f"查询表 {table_name} 时出错: {e}")
                continue
        
        if questions:
            print(f"找到 {len(questions)} 个问题模板")
            
            # MoCA量表应该有22个问题
            expected_questions = [
                "moca_1", "moca_2", "moca_3", "moca_4", "moca_5", "moca_6",
                "moca_7", "moca_8", "moca_9", "moca_10", "moca_11", "moca_12",
                "moca_13", "moca_14", "moca_15", "moca_16", "moca_17", "moca_18",
                "moca_19", "moca_20", "moca_21", "moca_22"
            ]
            
            found_questions = [q[0] for q in questions]
            missing_questions = [q for q in expected_questions if q not in found_questions]
            
            if missing_questions:
                print(f"\n缺失的问题: {missing_questions}")
            else:
                print("\n所有22个问题都存在于模板中")
            
            # 分析每个问题的回答情况
            if responses and len(responses) > 0:
                response_dict = dict(zip(column_names, responses[0]))
                if 'answers' in response_dict and response_dict['answers']:
                    try:
                        answers_data = json.loads(response_dict['answers'])
                        print("\n各问题回答情况分析:")
                        
                        # 重置 answered_questions 集合
                        answered_questions = set()
                        
                        for question in questions:
                            question_id = question[0]
                            question_text = question[1]
                            
                            if question_id in answers_data:
                                answer_value = answers_data[question_id]
                                answered_questions.add(question_id)
                                print(f"  {question_id}: 已回答 (值: {answer_value})")
                                print(f"    问题: {question_text}")
                            else:
                                print(f"  {question_id}: 未回答")
                                print(f"    问题: {question_text}")
                        
                        # 统计回答情况
                        answered_count = len([q for q in expected_questions if q in answers_data])
                        total_count = len(expected_questions)
                        
                        print(f"\n回答统计: {answered_count}/{total_count} 个问题已回答")
                        
                        if answered_count == 0:
                            print("\n所有问题都没有回答，可能的原因:")
                            print("1. 前端页面问题导致无法正常选择选项")
                            print("2. JavaScript代码问题导致选项值无法正确传递")
                            print("3. 后端API接收数据时出现问题")
                            print("4. 数据格式转换问题")
                        elif answered_count < total_count:
                            print(f"\n部分问题未回答，缺失 {total_count - answered_count} 个问题的回答")
                            print("建议检查前端页面的问题显示和交互逻辑")
                        else:
                            print("\n所有问题都已回答，进行计分分析...")
                            analyze_moca_scoring(answers_data, questions)
                    
                    except Exception as e:
                        print(f"解析回答数据时出错: {e}")
        else:
            print("未找到问题模板，可能是模板导入问题")
            
            # 即使没找到模板，也要分析MoCA量表的标准问题
            print("\n分析MoCA量表标准问题结构...")
            moca_questions = [
                'moca_1', 'moca_2', 'moca_3', 'moca_4', 'moca_5', 'moca_6', 'moca_7', 'moca_8',
                'moca_9', 'moca_10', 'moca_11', 'moca_12', 'moca_13', 'moca_14', 'moca_15',
                'moca_16', 'moca_17', 'moca_18', 'moca_19', 'moca_20', 'moca_21', 'moca_22'
            ]
            
            answered_questions = set()
            for response in responses:
                if response[1]:  # question_id
                    answered_questions.add(response[1])
            
            print(f"\nMoCA量表回答分析:")
            print(f"  标准问题数: {len(moca_questions)}")
            print(f"  实际回答数: {len(answered_questions)}")
            print(f"  已回答问题: {list(answered_questions)}")
            
            unanswered = set(moca_questions) - answered_questions
            if unanswered:
                print(f"  未回答问题: {list(unanswered)}")
            
            if len(answered_questions) == len(moca_questions):
                print("\n✓ 所有问题都有回答，需要检查前端显示问题")
            elif len(answered_questions) == 0:
                print("\n✗ 所有问题都没有回答，需要检查移动端或后端问题")
            else:
                print(f"\n⚠ 部分问题有回答 ({len(answered_questions)}/{len(moca_questions)})")
                print("这表明可能存在以下问题:")
                print("1. 用户未完成全部问题")
                print("2. 移动端提交数据不完整")
                print("3. 后端保存逻辑有问题")
        
        # 7. 检查维度分值计算
        print("\n7. 检查维度分值计算...")
        cursor.execute("""
            SELECT dimension_scores FROM assessment_responses 
            WHERE custom_id = 'SM_008' AND assessment_id = ?
        """, (moca_assessment_id,))
        dimension_result = cursor.fetchone()
        
        if dimension_result and dimension_result[0]:
            try:
                dimension_scores = json.loads(dimension_result[0])
                print("维度分值:")
                for dimension, score in dimension_scores.items():
                    print(f"  {dimension}: {score}")
            except:
                print(f"维度分值数据格式错误: {dimension_result[0]}")
        else:
            print("未找到维度分值数据")
        
        conn.close()
        
    except Exception as e:
        print(f"检查数据库时出错: {str(e)}")
    
    print("\n=== SM_008蒙特利尔认知评估量表检查完成 ===")
        
    # 8. 问题诊断和建议
    print("\n8. 问题诊断和建议...")
    
    if len(answered_questions) == 3 and all(q.startswith('moca_') for q in answered_questions):
        print("\n🔍 问题诊断:")
        print("SM_008用户只完成了MoCA量表的前3个问题，可能的原因包括:")
        print("\n前端问题:")
        print("1. 前端页面在第3题后出现JavaScript错误")
        print("2. 页面跳转逻辑有问题，未能显示后续问题")
        print("3. 表单验证逻辑阻止了后续问题的显示")
        print("4. CSS样式问题导致后续问题不可见")
        
        print("\n移动端问题:")
        print("1. 移动端网络连接在提交过程中断开")
        print("2. 移动端浏览器兼容性问题")
        print("3. 移动端内存不足导致页面崩溃")
        print("4. 移动端应用在后台被系统杀死")
        
        print("\n后端问题:")
        print("1. 后端API在处理部分数据时出现异常")
        print("2. 数据库事务在中途失败")
        print("3. 后端验证逻辑有问题")
        print("4. 服务器资源不足导致请求超时")
        
        print("\n数据问题:")
        print("1. MoCA量表模板数据不完整")
        print("2. 问题选项配置有误")
        print("3. 计分逻辑配置错误")
        
        print("\n🔧 建议的修复步骤:")
        print("1. 检查前端控制台是否有JavaScript错误")
        print("2. 检查移动端网络日志和错误日志")
        print("3. 检查后端API日志，查找异常信息")
        print("4. 验证MoCA量表模板的完整性")
        print("5. 测试完整的MoCA量表提交流程")
        print("6. 检查数据库约束和触发器")
        
    elif len(answered_questions) == 0:
        print("\n🔍 问题诊断:")
        print("SM_008用户完全没有回答MoCA量表，可能的原因:")
        print("1. 用户从未真正开始填写量表")
        print("2. 前端页面完全无法加载")
        print("3. 用户权限问题")
        print("4. 量表分发记录有问题")
        
    print("\n=== 分析完成 ===")

def analyze_moca_scoring(answers_data, questions):
    """分析MoCA量表的计分情况"""
    print("\n=== MoCA量表计分分析 ===")
    
    # 首先检查后端计分逻辑的实现
    print("\n1. 检查后端计分逻辑...")
    
    # MoCA量表标准计分规则
    moca_scoring_rules = {
        'moca_1': {'dimension': '视空间/执行功能', 'max_score': 1, 'description': '立方体复制'},
        'moca_2': {'dimension': '视空间/执行功能', 'max_score': 1, 'description': '钟表绘制'},
        'moca_3': {'dimension': '视空间/执行功能', 'max_score': 3, 'description': '连线测试'},
        'moca_4': {'dimension': '命名', 'max_score': 1, 'description': '狮子命名'},
        'moca_5': {'dimension': '命名', 'max_score': 1, 'description': '犀牛命名'},
        'moca_6': {'dimension': '记忆', 'max_score': 0, 'description': '记忆注册'},
        'moca_7': {'dimension': '注意力', 'max_score': 1, 'description': '数字广度'},
        'moca_8': {'dimension': '注意力', 'max_score': 1, 'description': '警觉性'},
        'moca_9': {'dimension': '注意力', 'max_score': 1, 'description': '连续减7'},
        'moca_10': {'dimension': '语言', 'max_score': 1, 'description': '句子重复1'},
        'moca_11': {'dimension': '语言', 'max_score': 1, 'description': '句子重复2'},
        'moca_12': {'dimension': '抽象思维', 'max_score': 2, 'description': '抽象思维'},
        'moca_13': {'dimension': '延迟回忆', 'max_score': 1, 'description': '延迟回忆1'},
        'moca_14': {'dimension': '延迟回忆', 'max_score': 1, 'description': '延迟回忆2'},
        'moca_15': {'dimension': '延迟回忆', 'max_score': 1, 'description': '延迟回忆3'},
        'moca_16': {'dimension': '延迟回忆', 'max_score': 1, 'description': '延迟回忆4'},
        'moca_17': {'dimension': '延迟回忆', 'max_score': 1, 'description': '延迟回忆5'},
        'moca_18': {'dimension': '定向力', 'max_score': 1, 'description': '日期'},
        'moca_19': {'dimension': '定向力', 'max_score': 1, 'description': '月份'},
        'moca_20': {'dimension': '定向力', 'max_score': 1, 'description': '年份'},
        'moca_21': {'dimension': '定向力', 'max_score': 1, 'description': '星期'},
        'moca_22': {'dimension': '定向力', 'max_score': 2, 'description': '地点和城市'}
    }
    
    # 创建问题ID到选项的映射
    question_options = {}
    for question in questions:
        question_id = question[0]
        try:
            options = json.loads(question[3]) if question[3] else []
            question_options[question_id] = {opt['value']: opt['score'] for opt in options}
        except:
            question_options[question_id] = {}
    
    total_score = 0
    dimension_scores = {
        '视空间/执行功能': 0,
        '命名': 0, 
        '记忆': 0,
        '注意力': 0,
        '语言': 0,
        '抽象思维': 0,
        '延迟回忆': 0,
        '定向力': 0
    }
    
    print("\n2. 分析每个问题的计分情况...")
    for question_id in moca_scoring_rules:
        rule = moca_scoring_rules[question_id]
        dimension = rule['dimension']
        max_score = rule['max_score']
        description = rule['description']
        
        if question_id in answers_data:
            answer_value = answers_data[question_id]
            actual_score = 0
            
            if question_id in question_options and answer_value in question_options[question_id]:
                actual_score = question_options[question_id][answer_value]
            
            print(f"问题 {question_id} ({description}):")
            print(f"  回答: '{answer_value}'")
            print(f"  实际得分: {actual_score}")
            print(f"  最大分值: {max_score}")
            print(f"  维度: {dimension}")
            
            if actual_score > max_score:
                print(f"  ⚠ 警告: 实际得分({actual_score})超过最大分值({max_score})")
            
            total_score += actual_score
            dimension_scores[dimension] += actual_score
        else:
            print(f"问题 {question_id} ({description}): 未回答")
    
    print(f"\n3. 计分结果汇总:")
    print(f"  总分: {total_score}/30")
    print("  各维度得分:")
    for dim, score in dimension_scores.items():
        print(f"    {dim}: {score}")
    
    # 检查计分逻辑问题
    print("\n4. 计分逻辑检查:")
    if total_score == 0:
        print("  ⚠ 总分为0，可能存在以下问题:")
        print("    - 回答选项没有正确赋分")
        print("    - 后端计分逻辑有误")
        print("    - 数据库中score字段未正确更新")
    elif total_score > 30:
        print(f"  ⚠ 总分({total_score})超过MoCA量表最大分值(30)")
    else:
        print(f"  ✓ 总分在正常范围内")
    
    # 检查是否有教育程度加分
    print("\n5. 教育程度调整检查:")
    print("  MoCA量表规定: 教育年限≤12年者，总分+1分")
    print("  需要检查用户教育背景和是否应用了教育调整")
    
    # 根据总分给出评估结果
    if total_score >= 26:
        result = "认知功能正常"
    elif total_score >= 18:
        result = "轻度认知障碍"
    elif total_score >= 10:
        result = "中度认知障碍"
    else:
        result = "重度认知障碍"
    
    print(f"\n评估结果: {result}")
    
    return dimension_scores, total_score

if __name__ == "__main__":
    check_assessment_data()
    print("\n" + "="*50)
    check_sm008_moca_responses()