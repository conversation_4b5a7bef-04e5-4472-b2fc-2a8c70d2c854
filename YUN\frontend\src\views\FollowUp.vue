<template>
  <div class="follow-up-container">
    <h1>随访记录管理</h1>
    
    <el-card class="filter-card">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="用户">
          <el-select v-model="filterForm.customId" placeholder="选择用户" clearable>
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="user.username"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="类型">
          <el-select v-model="filterForm.followUpType" placeholder="随访类型" clearable>
            <el-option label="常规随访" value="regular" />
            <el-option label="出院后随访" value="post_discharge" />
            <el-option label="用药随访" value="medication" />
            <el-option label="慢性病随访" value="chronic_disease" />
            <el-option label="康复随访" value="rehabilitation" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleFilter">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <el-card class="records-card">
      <template #header>
        <div class="card-header">
          <span>随访记录列表</span>
          <el-button type="primary" @click="handleCreate">新增记录</el-button>
        </div>
      </template>
      
      <el-table
        v-loading="loading"
        :data="records"
        style="width: 100%"
        border
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="follow_up_type" label="类型" width="120">
          <template #default="scope">
            <el-tag :type="getFollowUpTypeTag(scope.row.follow_up_type)">
              {{ getFollowUpTypeLabel(scope.row.follow_up_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="标题" width="180" />
        <el-table-column prop="follow_up_date" label="随访日期" width="180" />
        <el-table-column prop="next_follow_up_date" label="下次随访日期" width="180" />
        <el-table-column prop="consultant_name" label="随访人" width="120" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="handleView(scope.row)">查看</el-button>
            <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 查看/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'view' ? '查看随访记录' : (dialogType === 'create' ? '新增随访记录' : '编辑随访记录')"
      width="60%"
    >
      <el-form
        ref="recordForm"
        :model="currentRecord"
        :rules="formRules"
        label-width="100px"
        :disabled="dialogType === 'view'"
      >
        <el-form-item label="随访类型" prop="follow_up_type">
          <el-select v-model="currentRecord.follow_up_type" placeholder="请选择随访类型">
            <el-option label="常规随访" value="regular" />
            <el-option label="出院后随访" value="post_discharge" />
            <el-option label="用药随访" value="medication" />
            <el-option label="慢性病随访" value="chronic_disease" />
            <el-option label="康复随访" value="rehabilitation" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="标题" prop="title">
          <el-input v-model="currentRecord.title" placeholder="请输入标题" />
        </el-form-item>
        
        <el-form-item label="随访日期" prop="follow_up_date">
          <el-date-picker
            v-model="currentRecord.follow_up_date"
            type="datetime"
            placeholder="请选择随访日期"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        
        <el-form-item label="下次随访日期">
          <el-date-picker
            v-model="currentRecord.next_follow_up_date"
            type="datetime"
            placeholder="请选择下次随访日期"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        
        <el-form-item label="随访人">
          <el-select v-model="currentRecord.consultant_id" placeholder="请选择随访人" clearable>
            <el-option
              v-for="consultant in consultants"
              :key="consultant.id"
              :label="consultant.username"
              :value="consultant.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="内容">
          <el-input
            v-model="currentRecord.content"
            type="textarea"
            :rows="4"
            placeholder="请输入随访内容"
          />
        </el-form-item>
        
        <el-form-item label="建议">
          <el-input
            v-model="currentRecord.recommendations"
            type="textarea"
            :rows="3"
            placeholder="请输入建议"
          />
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input
            v-model="currentRecord.notes"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveRecord" v-if="dialogType !== 'view'">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import axios from 'axios'

const users = ref([])
const consultants = ref([])
const records = ref([])
const loading = ref(false)
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})
const filterForm = reactive({
  customId: '',
  followUpType: '',
  dateRange: []
})

// 统一聚合接口请求
const fetchUserHealthRecords = async (customId) => {
  loading.value = true
  try {
    const response = await axios.get(`/api/user-health-records/user/${customId}`)
    // 分类处理
    const allRecords = response.data || []
    let followUpRecords = allRecords.filter(r => r.type === 'follow_up')
    if (filterForm.followUpType) {
      followUpRecords = followUpRecords.filter(r => r.follow_up_type === filterForm.followUpType)
    }
    if (filterForm.dateRange && filterForm.dateRange.length === 2) {
      followUpRecords = followUpRecords.filter(r => r.follow_up_date >= filterForm.dateRange[0] && r.follow_up_date <= filterForm.dateRange[1])
    }
    pagination.total = followUpRecords.length
    const start = (pagination.currentPage - 1) * pagination.pageSize
    const end = start + pagination.pageSize
    records.value = followUpRecords.slice(start, end)
  } catch (error) {
    ElMessage.error('获取随访记录失败')
  } finally {
    loading.value = false
  }
}

const fetchUsers = async () => {
  try {
    const response = await axios.get('/api/users')
    users.value = response.data
  } catch (error) {
    ElMessage.error('获取用户列表失败')
  }
}

const fetchConsultants = async () => {
  // 这里应该调用后端API获取健康顾问列表
  // 暂时使用模拟数据
  consultants.value = [
    { id: 1, username: '张医生' },
    { id: 2, username: '李医生' },
    { id: 3, username: '王医生' }
  ]
}

const handleFilter = () => {
  pagination.currentPage = 1
  if (filterForm.customId) fetchUserHealthRecords(filterForm.customId)
}
const resetFilter = () => {
  filterForm.followUpType = ''
  filterForm.dateRange = []
  pagination.currentPage = 1
  if (filterForm.customId) fetchUserHealthRecords(filterForm.customId)
}
const handleSizeChange = (size) => {
  pagination.pageSize = size
  if (filterForm.customId) fetchUserHealthRecords(filterForm.customId)
}
const handleCurrentChange = (page) => {
  pagination.currentPage = page
  if (filterForm.customId) fetchUserHealthRecords(filterForm.customId)
}

// 获取随访类型标签类型
const getFollowUpTypeTag = (type) => {
  const typeMap = {
    regular: '',
    post_discharge: 'success',
    medication: 'warning',
    chronic_disease: 'info',
    rehabilitation: 'primary',
    other: ''
  }
  return typeMap[type] || ''
}

// 获取随访类型标签文本
const getFollowUpTypeLabel = (type) => {
  const typeMap = {
    regular: '常规随访',
    post_discharge: '出院后随访',
    medication: '用药随访',
    chronic_disease: '慢性病随访',
    rehabilitation: '康复随访',
    other: '其他'
  }
  return typeMap[type] || type
}

// 查看随访记录
const handleView = (row) => {
  Object.assign(currentRecord, row)
  dialogType.value = 'view'
  dialogVisible.value = true
}

// 编辑随访记录
const handleEdit = (row) => {
  Object.assign(currentRecord, row)
  dialogType.value = 'edit'
  dialogVisible.value = true
}

// 新增随访记录
const handleCreate = () => {
  // 重置表单
  Object.assign(currentRecord, {
    id: null,
    follow_up_type: '',
    title: '',
    content: '',
    follow_up_date: '',
    next_follow_up_date: '',
    consultant_id: null,
    recommendations: '',
    notes: ''
  })
  dialogType.value = 'create'
  dialogVisible.value = true
}

// 删除随访记录
const handleDelete = (row) => {
  ElMessageBox.confirm(
    '确定要删除这条随访记录吗？此操作不可恢复。',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      // 这里应该调用后端API删除随访记录
      // 暂时只显示成功消息
      ElMessage.success('删除成功')
      if (filterForm.customId) fetchUserHealthRecords(filterForm.customId)
    } catch (error) {
      console.error('删除随访记录失败:', error)
      ElMessage.error('删除随访记录失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 保存随访记录
const saveRecord = async () => {
  try {
    await recordForm.value.validate()
    
    if (dialogType.value === 'create') {
      // 创建新随访记录
      // 这里应该调用后端API创建随访记录
      // 暂时只显示成功消息
      ElMessage.success('创建成功')
    } else {
      // 更新随访记录
      // 这里应该调用后端API更新随访记录
      // 暂时只显示成功消息
      ElMessage.success('更新成功')
    }
    
    dialogVisible.value = false
    if (filterForm.customId) fetchUserHealthRecords(filterForm.customId)
  } catch (error) {
    console.error('保存随访记录失败:', error)
    ElMessage.error('保存随访记录失败')
  }
}

// 当前随访记录
const currentRecord = reactive({
  id: null,
  follow_up_type: '',
  title: '',
  content: '',
  follow_up_date: '',
  next_follow_up_date: '',
  consultant_id: null,
  recommendations: '',
  notes: ''
})

// 对话框
const dialogVisible = ref(false)
const dialogType = ref('view') // view, edit, create
const recordForm = ref(null)

// 表单验证规则
const formRules = {
  follow_up_type: [
    { required: true, message: '请选择随访类型', trigger: 'change' }
  ],
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' }
  ],
  follow_up_date: [
    { required: true, message: '请选择随访日期', trigger: 'change' }
  ]
}

// 组件挂载时获取数据
onMounted(() => {
  fetchUsers()
  fetchConsultants()
})
</script>

<style scoped>
.follow-up-container {
  padding: 20px;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
