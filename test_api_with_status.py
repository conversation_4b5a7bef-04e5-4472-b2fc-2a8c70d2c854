#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试移动端API，指定status参数
"""

import requests
import json

def test_mobile_api_with_status():
    """测试移动端API，指定不同的status参数"""
    print("=== 测试移动端API，指定status参数 ===")
    
    # 获取token
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(
            "http://localhost:8006/api/auth/login_json",
            json=login_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            token = data.get('access_token')
            print(f"获取到token: {token[:20]}...")
            
            # 测试不同的status参数
            test_cases = [
                {
                    "name": "不指定status（获取所有）",
                    "params": {"custom_id": "SM_006"}
                },
                {
                    "name": "status=pending",
                    "params": {"custom_id": "SM_006", "status": "pending"}
                },
                {
                    "name": "status=completed",
                    "params": {"custom_id": "SM_006", "status": "completed"}
                },
                {
                    "name": "测试SM_008的数据",
                    "params": {"custom_id": "SM_008"}
                },
                {
                    "name": "SM_008的pending状态",
                    "params": {"custom_id": "SM_008", "status": "pending"}
                }
            ]
            
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}"
            }
            
            for test_case in test_cases:
                print(f"\n--- {test_case['name']} ---")
                
                response = requests.get(
                    "http://localhost:8006/api/mobile/assessments",
                    headers=headers,
                    params=test_case['params'],
                    timeout=10
                )
                
                print(f"请求参数: {test_case['params']}")
                print(f"响应状态码: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    
                    # 检查assessments数据
                    if isinstance(data, dict) and 'data' in data:
                        assessments_data = data['data']
                        if isinstance(assessments_data, dict) and 'assessments' in assessments_data:
                            assessments = assessments_data['assessments']
                            total = assessments_data.get('total', 0)
                        else:
                            assessments = assessments_data if isinstance(assessments_data, list) else []
                            total = len(assessments)
                    else:
                        assessments = data if isinstance(data, list) else []
                        total = len(assessments)
                    
                    print(f"总数: {total}, 返回: {len(assessments)} 个评估")
                    
                    for i, assessment in enumerate(assessments):
                        print(f"\n  评估 {i+1}:")
                        print(f"    ID: {assessment.get('id')}")
                        print(f"    名称: {assessment.get('name')}")
                        print(f"    状态: {assessment.get('status')}")
                        print(f"    Custom ID: {assessment.get('custom_id')}")
                        
                        template = assessment.get('template')
                        print(f"    Template存在: {template is not None}")
                        
                        if template:
                            print(f"    Template ID: {template.get('id')}")
                            print(f"    Template名称: {template.get('name')}")
                            description = template.get('description')
                            instructions = template.get('instructions')
                            print(f"    Description存在: {description is not None and description != ''}")
                            print(f"    Instructions存在: {instructions is not None and instructions != ''}")
                            
                            if description:
                                print(f"    Description: {description[:100]}...")
                            if instructions:
                                print(f"    Instructions: {instructions[:100]}...")
                        else:
                            print(f"    ❌ Template为空！")
                    
                    if len(assessments) > 0:
                        templates_with_data = [a for a in assessments if a.get('template') and a.get('template').get('description')]
                        if len(templates_with_data) == len(assessments):
                            print(f"\n  ✅ 成功！所有评估都包含完整的template信息")
                        elif len(templates_with_data) > 0:
                            print(f"\n  ⚠️ 部分评估包含template信息 ({len(templates_with_data)}/{len(assessments)})")
                        else:
                            print(f"\n  ❌ 所有评估都缺少template信息")
                    else:
                        print(f"\n  ❌ 没有返回任何评估数据")
                        
                else:
                    print(f"API调用失败: {response.status_code} - {response.text}")
        else:
            print(f"登录失败: {response.status_code}")
            
    except Exception as e:
        print(f"测试异常: {e}")

def main():
    """主函数"""
    test_mobile_api_with_status()

if __name__ == "__main__":
    main()