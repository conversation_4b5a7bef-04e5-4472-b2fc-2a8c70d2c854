#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修正后的历史记录API
"""

import requests
import json

# 配置
BASE_URL = "http://localhost:8006"
USERNAME = "markey"
PASSWORD = "markey0308@163"
TEST_USER_ID = "SM_008"

def login():
    """登录获取token"""
    login_data = {
        "username": USERNAME,
        "password": PASSWORD
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", data=login_data)
    print(f"登录状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        token = result.get("access_token")
        print(f"登录成功，获取到token: {token[:20]}...")
        return token
    else:
        print(f"登录失败: {response.text}")
        return None

def test_history_apis(token):
    """测试历史记录API"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 测试的API端点
    endpoints = [
        (f"/api/assessment-results/user/{TEST_USER_ID}", "量表结果API"),
        (f"/api/questionnaire-results/user/{TEST_USER_ID}", "问卷结果API")
    ]
    
    for endpoint, name in endpoints:
        print(f"\n=== 测试 {name} ===")
        print(f"请求URL: {BASE_URL}{endpoint}")
        
        try:
            response = requests.get(f"{BASE_URL}{endpoint}", headers=headers)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                
                # 检查数据结构
                if isinstance(data, dict):
                    if "results" in data:
                        print(f"返回记录数: {len(data['results'])}")
                    elif "data" in data:
                        print(f"返回记录数: {len(data['data'])}")
                    else:
                        print("响应格式未知")
                elif isinstance(data, list):
                    print(f"返回记录数: {len(data)}")
            else:
                print(f"请求失败: {response.text}")
                
        except Exception as e:
            print(f"请求异常: {e}")

def main():
    print("开始测试历史记录API修正...")
    
    # 登录
    token = login()
    if not token:
        print("登录失败，无法继续测试")
        return
    
    # 测试API
    test_history_apis(token)
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()