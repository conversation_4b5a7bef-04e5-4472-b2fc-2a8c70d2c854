#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import sys
import os

def check_users():
    """检查数据库中的用户信息"""
    db_path = 'YUN/backend/app.db'
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查询所有用户
        cursor.execute('SELECT id, username, email, hashed_password, role, is_active, custom_id FROM users')
        users = cursor.fetchall()
        
        print("数据库中的用户信息:")
        print("-" * 80)
        for user in users:
            user_id, username, email, hashed_password, role, is_active, custom_id = user
            print(f"ID: {user_id}")
            print(f"用户名: {username}")
            print(f"邮箱: {email}")
            print(f"密码哈希: {hashed_password[:50]}..." if hashed_password else "无密码")
            print(f"角色: {role}")
            print(f"激活状态: {is_active}")
            print(f"自定义ID: {custom_id}")
            print("-" * 80)
        
        # 特别检查admin用户
        cursor.execute('SELECT username, hashed_password, role FROM users WHERE username=?', ('admin',))
        admin_user = cursor.fetchone()
        
        if admin_user:
            username, hashed_password, role = admin_user
            print(f"\nAdmin用户详情:")
            print(f"用户名: {username}")
            print(f"密码哈希: {hashed_password}")
            print(f"角色: {role}")
        else:
            print("\n未找到admin用户")
        
        conn.close()
        
    except Exception as e:
        print(f"检查用户信息时出错: {e}")

if __name__ == "__main__":
    check_users()