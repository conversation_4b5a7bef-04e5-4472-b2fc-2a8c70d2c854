/**
 * 聚合API服务
 * 统一管理所有健康资料、调查问卷和评估量表的API调用
 * 替换旧的分散式API调用
 */

import axios from 'axios'
import { ElMessage } from 'element-plus'
import { getToken } from '@/utils/auth'

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API || '/api',
  timeout: 15000
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    const token = getToken()
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    console.error('响应错误:', error)
    
    // 处理401错误（未授权）
    if (error.response && error.response.status === 401) {
      // 清除token和用户信息
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      
      // 重定向到登录页
      window.location.href = '/login'
    }
    
    return Promise.reject(error)
  }
)

// 缓存管理
const cache = new Map()
const DEFAULT_CACHE_TIME = 5 * 60 * 1000 // 5分钟

/**
 * 聚合API服务
 */
class AggregatedApiService {
  constructor() {
    this.baseUrl = '/api'
    this.aggregatedUrl = '/v1/aggregated'
  }

  /**
   * 设置缓存
   * @param {string} key - 缓存键
   * @param {any} data - 缓存数据
   * @param {number} expireTime - 过期时间（毫秒）
   */
  setCache(key, data, expireTime = DEFAULT_CACHE_TIME) {
    cache.set(key, {
      data,
      expire: Date.now() + expireTime
    })
  }

  /**
   * 获取缓存
   * @param {string} key - 缓存键
   * @returns {any} 缓存数据或null
   */
  getCache(key) {
    const cached = cache.get(key)
    if (!cached) return null
    
    const { data, expire } = cached
    if (Date.now() > expire) {
      cache.delete(key)
      return null
    }
    
    return data
  }

  /**
   * 清除缓存
   * @param {string} pattern - 缓存键模式（可选）
   */
  clearCache(pattern) {
    if (pattern) {
      for (const key of cache.keys()) {
        if (key.includes(pattern)) {
          cache.delete(key)
        }
      }
    } else {
      cache.clear()
    }
  }

  /**
   * 获取用户聚合数据
   * @param {string} customId - 用户自定义ID
   * @param {Object} options - 选项
   * @returns {Promise} 聚合数据
   */
  async getUserData(customId, options = {}) {
    const {
      dataSources = null,
      filters = {},
      pagination = { page: 1, page_size: 20 },
      strategy = 'merge_all',
      useCache = true,
      cacheTime = DEFAULT_CACHE_TIME
    } = options
    
    const cacheKey = `user_data_${customId}_${JSON.stringify({ dataSources, filters, pagination, strategy })}`
    
    // 检查缓存
    if (useCache) {
      const cachedData = this.getCache(cacheKey)
      if (cachedData) return cachedData
    }
    
    try {
      const response = await service.get(`${this.aggregatedUrl}/users/${customId}/data`, {
        params: {
          data_sources: dataSources,
          filters: JSON.stringify(filters),
          pagination: JSON.stringify(pagination),
          strategy
        }
      })
      
      // 设置缓存
      if (useCache) {
        this.setCache(cacheKey, response, cacheTime)
      }
      
      return response
    } catch (error) {
      console.error('获取用户聚合数据失败:', error)
      throw error
    }
  }

  /**
   * 获取前端用户数据
   * @param {string} customId - 用户自定义ID
   * @param {Object} options - 选项
   * @returns {Promise} 前端用户数据
   */
  async getFrontendUserData(customId, options = {}) {
    const {
      dataSources = null,
      filters = {},
      includeStats = true,
      useCache = true,
      cacheTime = DEFAULT_CACHE_TIME
    } = options
    
    const cacheKey = `frontend_data_${customId}_${JSON.stringify({ dataSources, filters, includeStats })}`
    
    // 检查缓存
    if (useCache) {
      const cachedData = this.getCache(cacheKey)
      if (cachedData) return cachedData
    }
    
    try {
      const response = await service.get(`${this.aggregatedUrl}/users/${customId}/frontend`, {
        params: {
          data_sources: dataSources,
          filters: JSON.stringify(filters),
          include_stats: includeStats
        }
      })
      
      // 设置缓存
      if (useCache) {
        this.setCache(cacheKey, response, cacheTime)
      }
      
      return response
    } catch (error) {
      console.error('获取前端用户数据失败:', error)
      throw error
    }
  }

  /**
   * 获取移动端用户数据
   * @param {string} customId - 用户自定义ID
   * @param {Object} options - 选项
   * @returns {Promise} 移动端用户数据
   */
  async getMobileUserData(customId, options = {}) {
    const {
      useCache = true,
      cacheTime = DEFAULT_CACHE_TIME
    } = options
    
    const cacheKey = `mobile_data_${customId}`
    
    // 检查缓存
    if (useCache) {
      const cachedData = this.getCache(cacheKey)
      if (cachedData) return cachedData
    }
    
    try {
      const response = await service.get(`${this.aggregatedUrl}/users/${customId}/mobile`)
      
      // 设置缓存
      if (useCache) {
        this.setCache(cacheKey, response, cacheTime)
      }
      
      return response
    } catch (error) {
      console.error('获取移动端用户数据失败:', error)
      throw error
    }
  }

  /**
   * 导出用户数据
   * @param {string} customId - 用户自定义ID
   * @param {string} format - 导出格式（csv, excel, json）
   * @param {Object} options - 选项
   * @returns {Promise} 导出数据
   */
  async exportUserData(customId, format = 'excel', options = {}) {
    const {
      dataSources = null,
      filters = {}
    } = options
    
    try {
      const response = await service.get(`${this.aggregatedUrl}/users/${customId}/export`, {
        params: {
          format,
          data_sources: dataSources,
          filters: JSON.stringify(filters)
        },
        responseType: 'blob'
      })
      
      return response
    } catch (error) {
      console.error('导出用户数据失败:', error)
      throw error
    }
  }

  /**
   * 获取健康记录
   * @param {string} customId - 用户自定义ID
   * @param {Object} filters - 过滤条件
   * @returns {Promise} 健康记录数据
   */
  async getHealthRecords(customId, filters = {}) {
    return this.getUserData(customId, {
      dataSources: ['health_record'],
      filters,
      strategy: 'merge_all'
    })
  }

  /**
   * 获取问卷数据
   * @param {string} customId - 用户自定义ID
   * @param {string} status - 状态（pending, completed, all）
   * @param {Object} filters - 过滤条件
   * @returns {Promise} 问卷数据
   */
  async getQuestionnaireData(customId, status = 'all', filters = {}) {
    const statusFilters = status !== 'all' ? { status } : {}
    
    return this.getUserData(customId, {
      dataSources: ['questionnaire'],
      filters: { ...filters, ...statusFilters },
      strategy: 'merge_all'
    })
  }

  /**
   * 获取用户问卷列表（包含问卷和评估）
   * @param {string} customId - 用户自定义ID
   * @param {Object} options - 选项
   * @param {string} options.status - 状态过滤 (pending, completed)
   * @param {boolean} options.includeAssessments - 是否包含评估
   * @param {boolean} options.includeResults - 是否包含结果详情
   * @returns {Promise<Object>} 问卷列表数据
   */
  async getUserQuestionnaires(customId, options = {}) {
    const cacheKey = `user_questionnaires_${customId}_${JSON.stringify(options)}`
    
    // 检查缓存
    const cached = this.getCache(cacheKey)
    if (cached) {
      return cached
    }
    
    try {
      const params = new URLSearchParams()
      if (options.status) params.append('status', options.status)
      if (options.includeAssessments !== undefined) params.append('include_assessments', options.includeAssessments)
      if (options.includeResults !== undefined) params.append('include_results', options.includeResults)
      
      const response = await service.get(`${this.aggregatedUrl}/users/${customId}/questionnaires?${params}`)
      
      // 缓存结果
      this.setCache(cacheKey, response.data, 300) // 5分钟缓存
      
      return response.data
    } catch (error) {
      console.error('获取用户问卷列表失败:', error)
      throw error
    }
  }

  /**
   * 获取评估数据
   * @param {string} customId - 用户自定义ID
   * @param {string} status - 状态（pending, completed, all）
   * @param {Object} filters - 过滤条件
   * @returns {Promise} 评估数据
   */
  async getAssessmentData(customId, status = 'all', filters = {}) {
    const statusFilters = status !== 'all' ? { status } : {}
    
    return this.getUserData(customId, {
      dataSources: ['assessment'],
      filters: { ...filters, ...statusFilters },
      strategy: 'merge_all'
    })
  }

  /**
   * 获取统计数据
   * @param {string} customId - 用户自定义ID
   * @param {Array} dataSources - 数据源类型
   * @returns {Promise} 统计数据
   */
  async getStatistics(customId, dataSources = null) {
    return this.getFrontendUserData(customId, {
      dataSources,
      includeStats: true,
      cacheTime: 60 * 1000 // 1分钟缓存
    })
  }

  /**
   * 获取问卷模板
   * @param {string} templateId - 模板ID
   * @returns {Promise} 问卷模板
   */
  async getQuestionnaireTemplate(templateId) {
    const cacheKey = `questionnaire_template_${templateId}`
    
    // 检查缓存
    const cachedData = this.getCache(cacheKey)
    if (cachedData) return cachedData
    
    try {
      const response = await service.get(`${this.baseUrl}/templates/questionnaire-templates/${templateId}`)
      
      // 设置缓存（长时间缓存，因为模板不常变化）
      this.setCache(cacheKey, response, 30 * 60 * 1000) // 30分钟
      
      return response
    } catch (error) {
      console.error('获取问卷模板失败:', error)
      throw error
    }
  }

  /**
   * 获取评估模板
   * @param {string} templateId - 模板ID
   * @returns {Promise} 评估模板
   */
  async getAssessmentTemplate(templateId) {
    const cacheKey = `assessment_template_${templateId}`
    
    // 检查缓存
    const cachedData = this.getCache(cacheKey)
    if (cachedData) return cachedData
    
    try {
      const response = await service.get(`${this.baseUrl}/templates/assessment-templates/${templateId}`)
      
      // 设置缓存（长时间缓存，因为模板不常变化）
      this.setCache(cacheKey, response, 30 * 60 * 1000) // 30分钟
      
      return response
    } catch (error) {
      console.error('获取评估模板失败:', error)
      throw error
    }
  }

  /**
   * 删除问卷结果
   * @param {string} resultId - 结果ID
   * @returns {Promise} 删除结果
   */
  async deleteQuestionnaireResult(resultId) {
    try {
      const response = await service.delete(`${this.baseUrl}/questionnaire-results/${resultId}`)
      
      // 清除相关缓存
      this.clearCacheByPattern('questionnaire_data_')
      this.clearCacheByPattern('user_data_')
      this.clearCacheByPattern('frontend_data_')
      
      return response
    } catch (error) {
      console.error('删除问卷结果失败:', error)
      throw error
    }
  }

  /**
   * 删除评估结果
   * @param {string} resultId - 结果ID
   * @returns {Promise} 删除结果
   */
  async deleteAssessmentResult(resultId) {
    try {
      const response = await service.delete(`${this.baseUrl}/assessment-results/${resultId}`)
      
      // 清除相关缓存
      this.clearCacheByPattern('assessment_data_')
      this.clearCacheByPattern('user_data_')
      this.clearCacheByPattern('frontend_data_')
      
      return response
    } catch (error) {
      console.error('删除评估结果失败:', error)
      throw error
    }
  }

  /**
   * 删除问卷分发记录
   * @param {string} distributionId - 分发记录ID
   * @returns {Promise} 删除结果
   */
  async deleteQuestionnaireDistribution(distributionId) {
    try {
      const response = await service.delete(`${this.baseUrl}/questionnaire-distributions/${distributionId}`)
      
      // 清除相关缓存
      this.clearCacheByPattern('questionnaire_data_')
      this.clearCacheByPattern('user_data_')
      this.clearCacheByPattern('frontend_data_')
      
      return response
    } catch (error) {
      console.error('删除问卷分发记录失败:', error)
      throw error
    }
  }

  /**
   * 删除评估分发记录
   * @param {string} distributionId - 分发记录ID
   * @returns {Promise} 删除结果
   */
  async deleteAssessmentDistribution(distributionId) {
    try {
      const response = await service.delete(`${this.baseUrl}/assessment-distributions/${distributionId}`)
      
      // 清除相关缓存
      this.clearCacheByPattern('assessment_data_')
      this.clearCacheByPattern('user_data_')
      this.clearCacheByPattern('frontend_data_')
      
      return response
    } catch (error) {
      console.error('删除评估分发记录失败:', error)
      throw error
    }
  }

  /**
   * 根据模式清除缓存
   * @param {string} pattern - 缓存键模式
   */
  clearCacheByPattern(pattern) {
    Object.keys(this.cache).forEach(key => {
      if (key.includes(pattern)) {
        delete this.cache[key]
      }
    })
  }
}

// 导出单例实例
const aggregatedApiService = new AggregatedApiService()
export default aggregatedApiService