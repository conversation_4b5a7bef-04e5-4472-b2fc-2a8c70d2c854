<template>
  <div class="documents-container">
    <h1>文档管理</h1>

    <el-card class="filter-card">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="用户">
          <el-select 
            v-model="filterForm.customId" 
            placeholder="选择用户" 
            clearable
            aria-label="选择用户"
            id="filter-user-select"
          >
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="user.username"
              :value="user.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="类型">
          <el-select 
            v-model="filterForm.type" 
            placeholder="文档类型" 
            clearable
            aria-label="选择文档类型"
            id="filter-type-select"
          >
            <el-option label="医疗报告" value="medical_report" />
            <el-option label="检查结果" value="test_result" />
            <el-option label="处方" value="prescription" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>

        <el-form-item label="上传日期">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            aria-label="选择日期范围"
            :id="['filter-date-range']"
          />
        </el-form-item>

        <el-form-item>
          <el-button 
            type="primary" 
            @click="handleFilter"
            aria-label="查询文档"
          >查询</el-button>
          <el-button 
            @click="resetFilter"
            aria-label="重置筛选条件"
          >重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="documents-card">
      <template #header>
        <div class="card-header">
          <span>文档列表</span>
          <el-button 
            type="primary" 
            size="small" 
            @click="handleUpload"
            aria-label="上传新文档"
          >上传文档</el-button>
        </div>
      </template>

      <el-table 
        :data="documents" 
        style="width: 100%" 
        v-loading="loading"
        role="table"
        aria-label="文档列表表格"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column label="用户">
          <template #default="scope">
            {{ scope.row.user?.username || '未知用户' }}
          </template>
        </el-table-column>
        <el-table-column label="标题">
          <template #default="scope">
            {{ scope.row.title || scope.row.file_name || '未命名文档' }}
          </template>
        </el-table-column>
        <el-table-column label="类型">
          <template #default="scope">
            <el-tag :type="getDocumentTypeTag(scope.row.type)">{{ getDocumentTypeLabel(scope.row.type) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="文件名">
          <template #default="scope">
            {{ scope.row.file_name || scope.row.filename || '未知文件' }}
          </template>
        </el-table-column>
        <el-table-column label="大小">
          <template #default="scope">
            {{ formatFileSize(scope.row.file_size) }}
          </template>
        </el-table-column>
        <el-table-column label="上传时间">
          <template #default="scope">
            {{ scope.row.uploaded_at || scope.row.created_at || '未知时间' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250">
          <template #default="scope">
            <el-button 
              size="small" 
              @click="handleView(scope.row)"
              :aria-label="`查看文档 ${scope.row.title || scope.row.file_name || '未命名文档'}`"
            >查看</el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="handleDelete(scope.row)"
              :aria-label="`删除文档 ${scope.row.title || scope.row.file_name || '未命名文档'}`"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 上传文档对话框 -->
    <el-dialog
      title="上传文档"
      v-model="uploadDialogVisible"
      width="500px"
    >
      <el-form
        ref="uploadFormRef"
        :model="uploadForm"
        :rules="uploadRules"
        label-width="100px"
      >
        <el-form-item label="用户" prop="customId">
          <el-select 
            v-model="uploadForm.customId" 
            placeholder="选择用户"
            aria-label="选择上传文档的用户"
            id="upload-user-select"
          >
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="user.username"
              :value="user.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="标题" prop="title">
          <el-input 
            v-model="uploadForm.title" 
            aria-label="输入文档标题"
            id="upload-title-input"
            placeholder="请输入文档标题"
          />
        </el-form-item>

        <el-form-item label="类型" prop="type">
          <el-select 
            v-model="uploadForm.type" 
            placeholder="文档类型"
            aria-label="选择文档类型"
            id="upload-type-select"
          >
            <el-option label="医疗报告" value="medical_report" />
            <el-option label="检查结果" value="test_result" />
            <el-option label="处方" value="prescription" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="uploadForm.description" 
            type="textarea" 
            :rows="3"
            aria-label="输入文档描述"
            id="upload-description-textarea"
            placeholder="请输入文档描述"
          />
        </el-form-item>

        <el-form-item label="文件" prop="file">
          <el-upload
            class="upload-demo"
            action="#"
            :http-request="handleFileUpload"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            :before-upload="beforeUpload"
            :limit="1"
            :auto-upload="false"
          >
            <template #trigger>
              <el-button 
                type="primary"
                aria-label="选择要上传的文件"
              >选择文件</el-button>
            </template>
            <template #tip>
              <div class="el-upload__tip">
                支持各种文档格式，单个文件不超过10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button 
            @click="uploadDialogVisible = false"
            aria-label="取消上传"
          >取消</el-button>
          <el-button 
            type="primary" 
            @click="submitUploadForm" 
            :loading="uploading"
            aria-label="确认上传文档"
          >上传</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 查看文档对话框 -->
    <el-dialog v-model="viewDialogVisible" title="文档详情" width="800px">
      <div v-if="currentDocument" class="document-details">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="标题">{{ currentDocument.title }}</el-descriptions-item>
          <el-descriptions-item label="用户">{{ currentDocument.user?.username }}</el-descriptions-item>
          <el-descriptions-item label="类型">
            <el-tag :type="getDocumentTypeTag(currentDocument.type)">
              {{ getDocumentTypeLabel(currentDocument.type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="文件名">{{ currentDocument.file_name }}</el-descriptions-item>
          <el-descriptions-item label="大小">{{ formatFileSize(currentDocument.file_size) }}</el-descriptions-item>
          <el-descriptions-item label="上传时间">{{ currentDocument.uploaded_at }}</el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">{{ currentDocument.description }}</el-descriptions-item>
        </el-descriptions>
        <div class="document-preview" v-if="previewUrl">
          <object v-if="isPreviewPdf" :data="previewUrl" type="application/pdf" width="100%" height="500">
            <p>无法预览PDF，请<a :href="previewUrl" target="_blank">点击下载</a></p>
          </object>
          <img v-else-if="isPreviewImage" :src="previewUrl" style="max-width:100%;max-height:500px;" />
          <iframe v-else :src="previewUrl" width="100%" height="500"></iframe>
        </div>
        <div v-else class="no-preview">
          <p>无法预览此类型的文件</p>
          <el-button 
            type="primary" 
            @click="handleDownload(currentDocument)"
            aria-label="下载当前文档"
          >下载文件</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from 'axios'

// 用户列表
const users = ref([])

// 文档列表
const documents = ref([])
const loading = ref(false)

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 筛选表单
const filterForm = reactive({
  customId: '',
  type: '',
  dateRange: []
})

// 上传对话框
const uploadDialogVisible = ref(false)
const uploadFormRef = ref(null)
const uploading = ref(false)
const uploadFile = ref(null)

// 上传表单
const uploadForm = reactive({
  customId: '',
  title: '',
  type: '',
  description: ''
})

// 上传表单验证规则
const uploadRules = {
  customId: [
    { required: true, message: '请选择用户', trigger: 'change' }
  ],
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择文档类型', trigger: 'change' }
  ]
}

// 查看文档对话框
const viewDialogVisible = ref(false)
const currentDocument = ref(null)
const previewUrl = ref('')
const isPreviewPdf = ref(false)
const isPreviewImage = ref(false)

// 获取用户列表
const fetchUsers = async () => {
  try {
    const response = await axios.get('/api/users')

    // 验证并处理不同的响应格式
    if (response.data && response.data.data && Array.isArray(response.data.data)) {
      // 新的API格式
      users.value = response.data.data
    } else if (response.data && Array.isArray(response.data)) {
      // 旧的API格式
      users.value = response.data
    } else if (response.data && response.data.users && Array.isArray(response.data.users)) {
      // 另一种可能的格式
      users.value = response.data.users
    } else {
      console.warn('用户数据格式不正确:', response.data)
      users.value = []
      ElMessage.warning('用户数据格式异常，已重置为空列表')
    }

    console.log('获取到的用户列表:', users.value)
  } catch (error) {
    console.error('获取用户列表失败:', error)
    const errorMsg = error.response?.data?.message || error.message || '未知错误'
    ElMessage.error(`获取用户列表失败: ${errorMsg}`)
    users.value = []
  }
}

// 获取文档列表
const fetchDocuments = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params = {
      page: pagination.currentPage,
      page_size: pagination.pageSize
    }

    if (filterForm.customId) {
      params.custom_id = filterForm.customId
    }

    if (filterForm.type) {
      params.type = filterForm.type
    }

    if (filterForm.dateRange && filterForm.dateRange.length === 2) {
      params.start_date = filterForm.dateRange[0]
      params.end_date = filterForm.dateRange[1]
    }

    const response = await axios.get('/api/documents/', { params })

    // 处理不同的响应格式
    if (response.data.data && response.data.data.documents) {
      // 新的API格式
      documents.value = response.data.data.documents
      pagination.total = response.data.data.total
    } else if (response.data.items) {
      // 旧的API格式
      documents.value = response.data.items
      pagination.total = response.data.total
    } else {
      // 默认处理
      documents.value = []
      pagination.total = 0
    }

    console.log('获取到的文档列表:', documents.value)
  } catch (error) {
    console.error('获取文档列表失败:', error)
    ElMessage.error('获取文档列表失败')
  } finally {
    loading.value = false
  }
}

// 处理筛选
const handleFilter = () => {
  pagination.currentPage = 1
  fetchDocuments()
}

// 重置筛选
const resetFilter = () => {
  filterForm.customId = ''
  filterForm.type = ''
  filterForm.dateRange = []
  pagination.currentPage = 1
  fetchDocuments()
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pagination.pageSize = size
  fetchDocuments()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  pagination.currentPage = page
  fetchDocuments()
}

// 打开上传对话框
const handleUpload = () => {
  resetUploadForm()
  uploadDialogVisible.value = true
}

// 文件上传前的验证
const beforeUpload = (file) => {
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isLt10M) {
    ElMessage.error('文件大小不能超过10MB!')
    return false
  }

  return true
}

// 处理文件选择
const handleFileChange = (file) => {
  uploadFile.value = file.raw
}

// 处理文件移除
const handleFileRemove = () => {
  uploadFile.value = null
}

// 处理文件上传
const handleFileUpload = () => {
  // 这个函数不会被调用，因为我们设置了auto-upload为false
  // 我们会在submitUploadForm中手动处理文件上传
}

// 提交上传表单
const submitUploadForm = async () => {
  if (!uploadFormRef.value) return

  if (!uploadFile.value) {
    ElMessage.error('请选择要上传的文件')
    return
  }

  try {
    await uploadFormRef.value.validate()

    uploading.value = true

    // 创建FormData对象
    const formData = new FormData()
    formData.append('custom_id', uploadForm.customId)
    formData.append('title', uploadForm.title)
    formData.append('type', uploadForm.type)
    formData.append('description', uploadForm.description)
    formData.append('file', uploadFile.value)

    // 发送上传请求
    await axios.post('/api/documents/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })

    ElMessage.success('文档上传成功')
    uploadDialogVisible.value = false
    fetchDocuments()
  } catch (error) {
    console.error('上传文档失败:', error)
    ElMessage.error('上传文档失败，请稍后重试')
  } finally {
    uploading.value = false
  }
}

// 重置上传表单
const resetUploadForm = () => {
  uploadForm.customId = ''
  uploadForm.title = ''
  uploadForm.type = ''
  uploadForm.description = ''
  uploadFile.value = null

  if (uploadFormRef.value) {
    uploadFormRef.value.resetFields()
  }
}

// 查看文档
const handleView = (document) => {
  currentDocument.value = document
  // 直接拼接预览URL，带token防止认证丢失
  const token = localStorage.getItem('token') || ''
  previewUrl.value = `/api/documents/${document.id}/preview${token ? `?token=${token}` : ''}`
  // 判断类型
  const filename = (document.file_name || document.filename || '').toLowerCase()
  isPreviewPdf.value = filename.endsWith('.pdf')
  isPreviewImage.value = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'].some(ext => filename.endsWith(ext))
  viewDialogVisible.value = true
}

// 删除文档
const handleDelete = (document) => {
  ElMessageBox.confirm(
    `确定要删除文档 "${document.title}" 吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await axios.delete(`/api/documents/${document.id}`)
      ElMessage.success('删除成功')
      fetchDocuments()
    } catch (error) {
      console.error('删除文档失败:', error)
      ElMessage.error('删除文档失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return '0 B'

  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let i = 0
  while (size >= 1024 && i < units.length - 1) {
    size /= 1024
    i++
  }

  return `${size.toFixed(2)} ${units[i]}`
}

// 获取文档类型标签
const getDocumentTypeLabel = (type) => {
  // 处理可能的null或undefined
  if (!type) return '未知类型';

  // 统一转换为小写
  const typeStr = String(type).toLowerCase();

  const typeMap = {
    // 文档分类
    'medical_report': '医疗报告',
    'test_result': '检查结果',
    'prescription': '处方',
    'lab_report': '检验报告',
    'examination_report': '检查报告',
    'imaging_report': '影像报告',
    'discharge_summary': '出院小结',
    'referral': '转诊单',
    'consultation': '会诊意见',
    'certificate': '证明',
    'other': '其他',

    // 文件类型
    'pdf': 'PDF文档',
    'image': '图片',
    'doc': 'Word文档',
    'docx': 'Word文档',
    'xls': 'Excel表格',
    'xlsx': 'Excel表格',
    'ppt': 'PPT演示文稿',
    'pptx': 'PPT演示文稿',
    'txt': '文本文件',
    'csv': 'CSV表格',
    'zip': '压缩文件',
    'rar': '压缩文件'
  };

  // 检查完整匹配
  if (typeMap[typeStr]) {
    return typeMap[typeStr];
  }

  // 检查部分匹配
  for (const key in typeMap) {
    if (typeStr.includes(key)) {
      return typeMap[key];
    }
  }

  return typeStr || '未知类型';
}

// 获取文档类型标签样式
const getDocumentTypeTag = (type) => {
  // 处理可能的null或undefined
  if (!type) return '';

  // 统一转换为小写
  const typeStr = String(type).toLowerCase();

  const tagMap = {
    // 文档分类
    'medical_report': 'primary',
    'test_result': 'success',
    'prescription': 'warning',
    'lab_report': 'success',
    'examination_report': 'primary',
    'imaging_report': 'info',
    'discharge_summary': 'danger',
    'referral': 'warning',
    'consultation': 'primary',
    'certificate': 'info',
    'other': 'info',

    // 文件类型
    'pdf': 'danger',
    'image': 'success',
    'doc': 'primary',
    'docx': 'primary',
    'xls': 'warning',
    'xlsx': 'warning',
    'ppt': 'info',
    'pptx': 'info',
    'txt': '',
    'csv': 'warning',
    'zip': 'info',
    'rar': 'info'
  };

  // 检查完整匹配
  if (tagMap[typeStr]) {
    return tagMap[typeStr];
  }

  // 检查部分匹配
  for (const key in tagMap) {
    if (typeStr.includes(key)) {
      return tagMap[key];
    }
  }

  return '';
}

// 页面加载时获取数据
onMounted(() => {
  fetchUsers()
  fetchDocuments()
})
</script>

<style scoped>
.documents-container {
  padding: 20px;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
}

.documents-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.document-details {
  margin-top: 20px;
}

.document-preview {
  margin-top: 20px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.no-preview {
  margin-top: 20px;
  padding: 20px;
  text-align: center;
  background-color: #f5f7fa;
  border-radius: 4px;
}

/* 修复Element UI组件的兼容性问题 */
:deep(.el-button) {
  /* 移除webkit-appearance，使用标准的appearance属性 */
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  
  /* 确保按钮样式一致性 */
  border: 1px solid transparent;
  border-radius: 4px;
  cursor: pointer;
  display: inline-block;
  font-weight: 500;
  line-height: 1;
  outline: none;
  padding: 12px 20px;
  text-align: center;
  text-decoration: none;
  transition: all 0.3s;
  user-select: none;
  vertical-align: middle;
  white-space: nowrap;
}

:deep(.el-input__inner) {
  /* 修复输入框的webkit-appearance问题 */
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

:deep(.el-select .el-input__inner) {
  /* 修复选择框的webkit-appearance问题 */
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

:deep(.el-textarea__inner) {
  /* 修复文本域的webkit-appearance问题 */
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* 提升可访问性 */
:deep(.el-button:focus) {
  outline: 2px solid #409eff;
  outline-offset: 2px;
}

:deep(.el-input:focus-within) {
  outline: 2px solid #409eff;
  outline-offset: 1px;
}

:deep(.el-select:focus-within) {
  outline: 2px solid #409eff;
  outline-offset: 1px;
}
</style>
