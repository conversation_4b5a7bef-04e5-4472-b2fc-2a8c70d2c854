import logging
import json
import re
from datetime import datetime
from .db_service import BaseDBService
from kivy.logger import Logger as KivyLogger

# 配置日志
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('health_service')

class HealthDBService(BaseDBService):
    """健康信息数据库服务类"""
    
    # 文档类型和目标表映射
    DOC_TYPE_TABLE_MAP = {
        'lab_report': {
            'keywords': ['检验', '化验', '实验室', '血常规', '尿常规', '生化', '免疫'],
            'table': 'lab_report'
        },
        'examination_report': {
            'keywords': ['检查', '影像', 'CT', 'MRI', '超声', '心电图', 'X线', '内镜'],
            'table': 'examination_report'
        },
        'hospital_record': {
            'keywords': ['住院', '入院', '出院', '病历', '手术'],
            'table': 'hospital_record'
        },
        'outpatient_record': {
            'keywords': ['门诊', '就诊', '处方', '病历'],
            'table': 'outpatient_record'
        }
    }
    
    # 化验报告子类型
    LAB_REPORT_SUBTYPES = {
        'blood': ['血常规', '血液', '血球', '血细胞', '凝血'],
        'urine': ['尿常规', '尿液', '尿分析'],
        'biochemistry': ['生化', '肝功能', '肾功能', '血糖', '血脂', '电解质'],
        'immunology': ['免疫', '抗体', '病毒', 'HIV', '乙肝', '丙肝'],
        'microbiology': ['微生物', '培养', '细菌', '真菌'],
        'pathology': ['病理', '细胞学', '组织学']
    }
    
    # 检查报告子类型
    EXAM_REPORT_SUBTYPES = {
        'imaging': ['CT', 'MRI', 'X线', '超声', '彩超', '造影'],
        'endoscopy': ['内镜', '胃镜', '肠镜', '支气管镜', '膀胱镜'],
        'cardiology': ['心电图', '心脏彩超', '冠脉造影', '动脉硬化'],
        'neurology': ['脑电图', '神经', '肌电图'],
        'respiratory': ['肺功能', '呼吸', '通气'],
        'others': ['骨密度', '眼科', '听力']
    }
    
    def __init__(self, custom_id=None):
        """初始化健康数据库服务"""
        super().__init__()
        self.custom_id = None
        if custom_id:
            success = self.connect_user(custom_id)
            if success:
                self.custom_id = custom_id
            else:
                KivyLogger.error(f"初始化HealthDBService失败：无法连接到用户 {custom_id} 的数据库")
                raise Exception(f"无法连接到用户 {custom_id} 的数据库")
    
    def _ensure_connection(self):
        """确保数据库连接有效"""
        try:
            if not self.custom_id:
                KivyLogger.error("数据库未连接且未提供用户ID")
                return False
                
            if not hasattr(self, 'db_manager') or not self.db_manager:
                KivyLogger.info(f"数据库管理器不存在，尝试重新连接用户 {self.custom_id}")
                return self.connect_user(self.custom_id)
            
            # 测试连接是否有效
            test_query = "SELECT 1"
            result = self.db_manager.execute_query(test_query, [], self.custom_id)
            if result is not None:
                return True
            else:
                KivyLogger.warning("数据库连接测试失败，尝试重新连接")
                return self.connect_user(self.custom_id)
        except Exception as e:
            KivyLogger.warning(f"数据库连接无效: {e}，尝试重新连接")
            return self.connect_user(self.custom_id)
    
    def _reconnect_and_retry(self):
        """重连数据库"""
        try:
            if not self.custom_id:
                KivyLogger.error("数据库重连失败：未提供用户ID")
                return False
                
            KivyLogger.info(f"尝试重新连接用户 {self.custom_id} 的数据库...")
            
            # 先断开当前连接
            if hasattr(self, 'db_manager') and self.db_manager:
                try:
                    self.disconnect()
                except:
                    pass
            
            # 重新连接
            success = self.connect_user(self.custom_id)
            if success:
                KivyLogger.info(f"用户 {self.custom_id} 数据库重连成功")
                return True
            else:
                KivyLogger.error(f"用户 {self.custom_id} 数据库重连失败")
                return False
        except Exception as e:
            KivyLogger.error(f"数据库重连过程中出错: {e}")
            return False
    
    def save_user_info(self, user_data):
        """保存用户基本信息"""
        if not self.custom_id:
            logger.error("未连接到用户数据库")
            return False
        
        # 检查用户信息是否已存在
        existing = self.get_records('user_info', {'custom_id': self.custom_id})
        
        # 准备数据
        data = {
            'custom_id': self.custom_id,
            'username': user_data.get('username', ''),
            'real_name': user_data.get('real_name', ''),
            'gender': user_data.get('gender', ''),
            'birth_date': user_data.get('birth_date', ''),
            'id_card': user_data.get('id_card', ''),
            'phone': user_data.get('phone', ''),
            'email': user_data.get('email', ''),
            'address': user_data.get('address', ''),
            'ethnic': user_data.get('ethnic', ''),
            'education': user_data.get('education', ''),
            'emergency_contact': user_data.get('emergency_contact', ''),
            'emergency_phone': user_data.get('emergency_phone', ''),
            'last_update_time': self.get_timestamp()
        }
        
        if existing:
            # 更新现有记录
            return self.update_record('user_info', existing[0]['id'], data)
        else:
            # 插入新记录
            data['registration_time'] = self.get_timestamp()
            return self.insert_record('user_info', data) is not None
    
    def get_user_info(self):
        """获取用户基本信息"""
        if not self.custom_id:
            logger.error("未连接到用户数据库")
            return None
        
        records = self.get_records('user_info', {'custom_id': self.custom_id})
        return records[0] if records else None
    
    def save_health_info(self, health_data):
        """保存健康基本信息"""
        if not self.custom_id:
            logger.error("未连接到用户数据库")
            return False
        
        # 检查健康信息是否已存在
        existing = self.get_records('health_info', {'custom_id': self.custom_id})
        
        # 准备数据
        data = {
            'custom_id': self.custom_id,
            'height': health_data.get('height'),
            'weight': health_data.get('weight'),
            'blood_type': health_data.get('blood_type', ''),
            'rh_factor': health_data.get('rh_factor', ''),
            'last_update_time': self.get_timestamp()
        }
        
        if existing:
            # 更新现有记录
            return self.update_record('health_info', existing[0]['id'], data)
        else:
            # 插入新记录
            return self.insert_record('health_info', data) is not None
    
    def get_health_info(self):
        """获取健康基本信息"""
        if not self.custom_id:
            logger.error("未连接到用户数据库")
            return None
        
        # 获取基本健康信息
        health_info = {}
        records = self.get_records('health_info', {'custom_id': self.custom_id})
        if records:
            health_info = records[0]
        
        # 获取疾病史
        health_info['diseases'] = self.get_records('disease_history', {'custom_id': self.custom_id})
        
        # 获取家族病史
        health_info['family_diseases'] = self.get_records('family_disease', {'custom_id': self.custom_id})
        
        # 获取药物过敏史
        health_info['drug_allergies'] = [
            allergy['drug_name'] for allergy in 
            self.get_records('drug_allergy', {'custom_id': self.custom_id})
        ]
        
        # 获取基因信息
        health_info['gene_info'] = [
            gene['gene_name'] for gene in 
            self.get_records('gene_info', {'custom_id': self.custom_id})
        ]
        
        return health_info
    
    def add_disease(self, disease_data):
        """添加疾病史"""
        if not self.custom_id:
            logger.error("未连接到用户数据库")
            return None
        
        # 准备数据
        data = {
            'custom_id': self.custom_id,
            'disease_name': disease_data.get('disease_name', ''),
            'onset_time': disease_data.get('onset_time', ''),
            'duration': disease_data.get('duration', ''),
            'treatment': disease_data.get('treatment', ''),
            'medication': disease_data.get('medication', ''),
            'control_effect': disease_data.get('control_effect', ''),
            'hospital': disease_data.get('hospital', ''),
            'doctor': disease_data.get('doctor', ''),
            'notes': disease_data.get('notes', ''),
            'record_time': self.get_timestamp(),
            'last_update_time': self.get_timestamp()
        }
        
        return self.insert_record('disease_history', data)
    
    def add_family_disease(self, disease_data):
        """添加家族病史"""
        if not self.custom_id:
            logger.error("未连接到用户数据库")
            return None
        
        # 准备数据
        data = {
            'custom_id': self.custom_id,
            'disease_name': disease_data.get('disease_name', ''),
            'relation': disease_data.get('relation', ''),
            'age_of_onset': disease_data.get('age_of_onset'),
            'treatment': disease_data.get('treatment', ''),
            'notes': disease_data.get('notes', ''),
            'record_time': self.get_timestamp(),
            'last_update_time': self.get_timestamp()
        }
        
        return self.insert_record('family_disease', data)
    
    def add_drug_allergy(self, allergy_data):
        """添加药物过敏史"""
        if not self.custom_id:
            logger.error("未连接到用户数据库")
            return None
        
        # 准备数据
        data = {
            'custom_id': self.custom_id,
            'drug_name': allergy_data.get('drug_name', ''),
            'reaction': allergy_data.get('reaction', ''),
            'severity': allergy_data.get('severity', ''),
            'diagnosis_time': allergy_data.get('diagnosis_time', ''),
            'hospital': allergy_data.get('hospital', ''),
            'doctor': allergy_data.get('doctor', ''),
            'notes': allergy_data.get('notes', ''),
            'record_time': self.get_timestamp(),
            'last_update_time': self.get_timestamp()
        }
        
        return self.insert_record('drug_allergy', data)
    
    def add_lab_report(self, report_data, items_data):
        """添加化验报告及明细"""
        if not self.custom_id:
            logger.error("未连接到用户数据库")
            return None
        
        # 准备报告数据
        report = {
            'custom_id': self.custom_id,
            'hospital_name': report_data.get('hospital_name', ''),
            'report_type': report_data.get('report_type', ''),
            'specimen_type': report_data.get('specimen_type', ''),
            'collection_time': report_data.get('collection_time', ''),
            'report_time': report_data.get('report_time', ''),
            'doctor': report_data.get('doctor', ''),
            'notes': report_data.get('notes', ''),
            'local_file_path': report_data.get('local_file_path', ''),
            'cloud_file_id': report_data.get('cloud_file_id', ''),
            'record_time': self.get_timestamp(),
            'last_update_time': self.get_timestamp()
        }
        
        # 插入报告
        report_id = self.insert_record('lab_report', report)
        if not report_id:
            logger.error("插入化验报告失败")
            return None
        
        # 插入报告明细
        for item in items_data:
            item_data = {
                'lab_report_id': report_id,
                'item_name': item.get('item_name', ''),
                'item_value': item.get('item_value', ''),
                'unit': item.get('unit', ''),
                'reference_range': item.get('reference_range', ''),
                'abnormal': 1 if item.get('abnormal') else 0,
                'notes': item.get('notes', '')
            }
            self.insert_record('lab_report_item', item_data, sync=False)
        
        return report_id
    
    def add_examination_report(self, report_data):
        """添加技诊检查报告"""
        if not self.custom_id:
            logger.error("未连接到用户数据库")
            return None
        
        # 准备报告数据
        report = {
            'custom_id': self.custom_id,
            'hospital_name': report_data.get('hospital_name', ''),
            'exam_type': report_data.get('exam_type', ''),
            'exam_part': report_data.get('exam_part', ''),
            'exam_time': report_data.get('exam_time', ''),
            'report_time': report_data.get('report_time', ''),
            'device': report_data.get('device', ''),
            'doctor': report_data.get('doctor', ''),
            'description': report_data.get('description', ''),
            'conclusion': report_data.get('conclusion', ''),
            'notes': report_data.get('notes', ''),
            'local_file_path': report_data.get('local_file_path', ''),
            'cloud_file_id': report_data.get('cloud_file_id', ''),
            'record_time': self.get_timestamp(),
            'last_update_time': self.get_timestamp()
        }
        
        return self.insert_record('examination_report', report)
    
    def add_hospital_record(self, record_data):
        """添加住院记录"""
        if not self.custom_id:
            logger.error("未连接到用户数据库")
            return None
        
        # 准备记录数据
        record = {
            'custom_id': self.custom_id,
            'hospital_name': record_data.get('hospital_name', ''),
            'department': record_data.get('department', ''),
            'admission_time': record_data.get('admission_time', ''),
            'discharge_time': record_data.get('discharge_time', ''),
            'admission_diagnosis': record_data.get('admission_diagnosis', ''),
            'discharge_diagnosis': record_data.get('discharge_diagnosis', ''),
            'chief_doctor': record_data.get('chief_doctor', ''),
            'treatment': record_data.get('treatment', ''),
            'notes': record_data.get('notes', ''),
            'local_file_path': record_data.get('local_file_path', ''),
            'cloud_file_id': record_data.get('cloud_file_id', ''),
            'record_time': self.get_timestamp(),
            'last_update_time': self.get_timestamp()
        }
        
        return self.insert_record('hospital_record', record)
    
    def add_outpatient_record(self, record_data):
        """添加门诊记录"""
        if not self.custom_id:
            logger.error("未连接到用户数据库")
            return None
        
        # 准备记录数据
        record = {
            'custom_id': self.custom_id,
            'hospital_name': record_data.get('hospital_name', ''),
            'department': record_data.get('department', ''),
            'visit_time': record_data.get('visit_time', ''),
            'doctor': record_data.get('doctor', ''),
            'chief_complaint': record_data.get('chief_complaint', ''),
            'diagnosis': record_data.get('diagnosis', ''),
            'treatment': record_data.get('treatment', ''),
            'prescription': record_data.get('prescription', ''),
            'follow_up': record_data.get('follow_up', ''),
            'notes': record_data.get('notes', ''),
            'local_file_path': record_data.get('local_file_path', ''),
            'cloud_file_id': record_data.get('cloud_file_id', ''),
            'record_time': self.get_timestamp(),
            'last_update_time': self.get_timestamp()
        }
        
        return self.insert_record('outpatient_record', record)
    
    def add_medication(self, medication_data):
        """添加药物使用信息"""
        if not self.custom_id:
            logger.error("未连接到用户数据库")
            return None
        
        # 确保数据库连接有效
        if not self._ensure_connection():
            KivyLogger.error("数据库连接无效，无法添加药物记录")
            return None
        
        try:
            # 准备药物数据
            med = {
                'custom_id': self.custom_id,
                'name': medication_data.get('name', ''),
                'dosage': medication_data.get('dosage', ''),
                'frequency': medication_data.get('frequency', ''),
                'start_date': medication_data.get('start_date', ''),
                'end_date': medication_data.get('end_date', ''),
                'instructions': medication_data.get('instructions', ''),
                'prescription_required': 1 if medication_data.get('prescription_required', False) else 0,
                'notes': medication_data.get('notes', ''),
                'medication_type': medication_data.get('medication_type', ''),
                'is_current': 1 if medication_data.get('is_current', True) else 0,
                'prescriber': medication_data.get('prescriber', ''),
                'hospital': medication_data.get('hospital', ''),
                'purpose': medication_data.get('purpose', ''),
                'side_effects': medication_data.get('side_effects', ''),
                'specification': medication_data.get('specification', ''),
                'created_at': self.get_timestamp(),
                'updated_at': self.get_timestamp()
            }
            
            result = self.insert_record('medication', med)
            if result:
                KivyLogger.info(f"药物记录添加成功: {medication_data.get('name', '')}")
            return result
            
        except Exception as e:
            KivyLogger.error(f"添加药物记录时发生错误: {e}")
            # 尝试重连并重试一次
            if self._reconnect_and_retry():
                try:
                    result = self.insert_record('medication', med)
                    if result:
                        KivyLogger.info(f"重试后药物记录添加成功: {medication_data.get('name', '')}")
                    return result
                except Exception as retry_e:
                    KivyLogger.error(f"重试添加药物记录失败: {retry_e}")
            return None
    
    def get_current_medications(self):
        """获取当前正在服用的药物"""
        if not self.custom_id:
            logger.error("未连接到用户数据库")
            return []
        
        # 确保数据库连接有效
        if not self._ensure_connection():
            KivyLogger.error("数据库连接无效，无法获取药物记录")
            return []
        
        try:
            return self.get_records('medication', {'custom_id': self.custom_id, 'is_current': 1})
        except Exception as e:
            KivyLogger.error(f"获取当前药物记录时发生错误: {e}")
            # 尝试重连并重试一次
            if self._reconnect_and_retry():
                try:
                    return self.get_records('medication', {'custom_id': self.custom_id, 'is_current': 1})
                except Exception as retry_e:
                    KivyLogger.error(f"重试获取药物记录失败: {retry_e}")
            return []
    
    def get_medication_history(self, start_date=None, end_date=None, medication_name=None):
        """获取历史用药记录"""
        if not self.custom_id:
            logger.error("未连接到用户数据库")
            return []
        
        conditions = {'custom_id': self.custom_id, 'is_current': 0}
        
        # 如果指定了药物名称，添加模糊搜索条件
        if medication_name:
            # 这里需要使用SQL的LIKE查询，但由于BaseDBService的限制，我们先获取所有记录再过滤
            all_records = self.get_records('medication', conditions)
            filtered_records = []
            for record in all_records:
                if medication_name.lower() in record.get('name', '').lower():
                    filtered_records.append(record)
            return filtered_records
        
        return self.get_records('medication', conditions)
    
    def stop_medication(self, medication_id, stop_reason):
        """停用药物"""
        if not self.custom_id:
            logger.error("未连接到用户数据库")
            return False
        
        # 更新药物状态
        update_data = {
            'is_current': 0,
            'end_date': self.get_timestamp().split(' ')[0],  # 只保留日期部分
            'stop_reason': stop_reason,
            'updated_at': self.get_timestamp()
        }
        
        result = self.update_record('medication', medication_id, update_data)
        
        # 如果停药原因是"药物过敏"，自动添加到过敏记录
        if result and stop_reason == "药物过敏":
            # 获取药物信息
            medication = self.get_record_by_id('medication', medication_id)
            if medication:
                allergy_data = {
                    'drug_name': medication.get('name', ''),
                    'reaction': '过敏反应',
                    'severity': '未知',
                    'diagnosis_time': self.get_timestamp().split(' ')[0],
                    'hospital': '',
                    'doctor': '',
                    'notes': f"停药原因：{stop_reason}"
                }
                self.add_drug_allergy(allergy_data)
        
        return result
    
    def update_medication(self, medication_id, medication_data):
        """更新药物信息"""
        if not self.custom_id:
            logger.error("未连接到用户数据库")
            return False
        
        medication_data['updated_at'] = self.get_timestamp()
        return self.update_record('medication', medication_id, medication_data)
    
    def delete_medication(self, medication_id):
        """删除药物记录"""
        if not self.custom_id:
            logger.error("未连接到用户数据库")
            return False
        
        return self.delete_record('medication', medication_id)
    
    def search_medications(self, keyword, include_history=True):
        """搜索药物记录"""
        if not self.custom_id:
            logger.error("未连接到用户数据库")
            return []
        
        conditions = {'custom_id': self.custom_id}
        if not include_history:
            conditions['is_current'] = 1
        
        all_records = self.get_records('medication', conditions)
        filtered_records = []
        
        for record in all_records:
            if (keyword.lower() in record.get('name', '').lower() or
                keyword.lower() in record.get('purpose', '').lower()):
                filtered_records.append(record)
        
        return filtered_records
    
    def record_medication_usage(self, medication_id, usage_data):
        """记录药物服用情况"""
        if not self.custom_id:
            logger.error("未连接到用户数据库")
            return None
        
        # 准备服用记录数据
        record = {
            'medication_id': medication_id,
            'taken_at': usage_data.get('taken_at', self.get_timestamp()),
            'dosage_taken': usage_data.get('dosage_taken', ''),
            'notes': usage_data.get('notes', ''),
            'created_at': self.get_timestamp()
        }
        
        return self.insert_record('medication_usage', record)
    
    def add_health_log(self, log_data):
        """添加健康日志"""
        if not self.custom_id:
            logger.error("未连接到用户数据库")
            return None
        
        # 准备日志数据
        log = {
            'custom_id': self.custom_id,
            'log_type': log_data.get('log_type', ''),
            'log_time': log_data.get('log_time', self.get_timestamp()),
            'title': log_data.get('title', ''),
            'content': log_data.get('content', ''),
            'mood': log_data.get('mood', ''),
            'symptoms': log_data.get('symptoms', ''),
            'tags': json.dumps(log_data.get('tags', []), ensure_ascii=False) if isinstance(log_data.get('tags'), list) else '',
            'images': json.dumps(log_data.get('images', []), ensure_ascii=False) if isinstance(log_data.get('images'), list) else '',
            'created_at': self.get_timestamp(),
            'updated_at': self.get_timestamp()
        }
        
        return self.insert_record('health_log', log)
    
    def add_file_upload(self, file_data, ocr_result=None):
        """添加文件上传记录"""
        if not self.custom_id:
            logger.error("未连接到用户数据库")
            return None
        
        # 准备文件上传数据
        data = {
            'custom_id': self.custom_id,
            'file_name': file_data.get('file_name', ''),
            'file_type': file_data.get('file_type', ''),
            'upload_type': file_data.get('upload_type', ''),
            'upload_time': file_data.get('upload_time', self.get_timestamp()),
            'local_path': file_data.get('local_path', ''),
            'cloud_id': file_data.get('cloud_id', ''),
            'ocr_result': json.dumps(ocr_result, ensure_ascii=False) if ocr_result else '',
            'related_table': file_data.get('related_table', ''),
            'related_id': file_data.get('related_id'),
            'notes': file_data.get('notes', ''),
            'processing_status': file_data.get('processing_status', 'pending'),
            'created_at': self.get_timestamp(),
            'updated_at': self.get_timestamp()
        }
        
        return self.insert_record('file_upload', data)
    
    def classify_document(self, title, content):
        """自动分类文档类型"""
        # 初始化结果
        result = {
            'doc_type': 'unknown',
            'table': None,
            'subtype': None,
            'confidence': 0
        }
        
        # 标题和内容合并
        text = (title or '') + ' ' + (content or '')
        
        # 遍历文档类型，寻找匹配的关键词
        max_matches = 0
        for doc_type, info in self.DOC_TYPE_TABLE_MAP.items():
            matches = 0
            for keyword in info['keywords']:
                if keyword in text:
                    matches += 1
            
            if matches > max_matches:
                max_matches = matches
                result['doc_type'] = doc_type
                result['table'] = info['table']
                result['confidence'] = min(matches / len(info['keywords']), 0.9)
        
        # 如果是化验报告，进一步确定子类型
        if result['doc_type'] == 'lab_report' and result['confidence'] > 0.3:
            for subtype, keywords in self.LAB_REPORT_SUBTYPES.items():
                for keyword in keywords:
                    if keyword in text:
                        result['subtype'] = subtype
                        result['confidence'] = min(result['confidence'] + 0.1, 0.95)
                        break
                if result['subtype']:
                    break
        
        # 如果是检查报告，进一步确定子类型
        elif result['doc_type'] == 'examination_report' and result['confidence'] > 0.3:
            for subtype, keywords in self.EXAM_REPORT_SUBTYPES.items():
                for keyword in keywords:
                    if keyword in text:
                        result['subtype'] = subtype
                        result['confidence'] = min(result['confidence'] + 0.1, 0.95)
                        break
                if result['subtype']:
                    break
        
        return result
    
    def process_ocr_result(self, ocr_result, file_data):
        """处理OCR识别结果，自动分类并保存"""
        if not ocr_result or not isinstance(ocr_result, dict):
            logger.error("OCR结果为空或格式不正确")
            return None
        
        # 提取标题和内容
        title = ocr_result.get('title', '')
        content = ocr_result.get('content', '')
        
        # 分类文档
        classification = self.classify_document(title, content)
        
        # 根据分类结果决定保存到哪个表
        record_id = None
        
        if classification['table'] == 'lab_report' and classification['confidence'] > 0.5:
            # 保存化验报告
            report_data = {
                'hospital_name': ocr_result.get('hospital', ''),
                'report_type': classification['subtype'] or '未知',
                'specimen_type': ocr_result.get('specimen_type', ''),
                'collection_time': ocr_result.get('collection_time', ''),
                'report_time': ocr_result.get('report_time', ''),
                'doctor': ocr_result.get('doctor', ''),
                'notes': '',
                'local_file_path': file_data.get('local_path', ''),
                'cloud_file_id': file_data.get('cloud_id', '')
            }
            
            # 提取化验项目
            items_data = []
            if 'items' in ocr_result and isinstance(ocr_result['items'], list):
                for item in ocr_result['items']:
                    items_data.append({
                        'item_name': item.get('name', ''),
                        'item_value': item.get('value', ''),
                        'unit': item.get('unit', ''),
                        'reference_range': item.get('reference', ''),
                        'abnormal': item.get('abnormal', False),
                        'notes': ''
                    })
            
            record_id = self.add_lab_report(report_data, items_data)
            
        elif classification['table'] == 'examination_report' and classification['confidence'] > 0.5:
            # 保存检查报告
            report_data = {
                'hospital_name': ocr_result.get('hospital', ''),
                'exam_type': classification['subtype'] or '未知',
                'exam_part': ocr_result.get('exam_part', ''),
                'exam_time': ocr_result.get('exam_time', ''),
                'report_time': ocr_result.get('report_time', ''),
                'device': ocr_result.get('device', ''),
                'doctor': ocr_result.get('doctor', ''),
                'description': ocr_result.get('description', ''),
                'conclusion': ocr_result.get('conclusion', ''),
                'notes': '',
                'local_file_path': file_data.get('local_path', ''),
                'cloud_file_id': file_data.get('cloud_id', '')
            }
            
            record_id = self.add_examination_report(report_data)
            
        elif classification['table'] == 'hospital_record' and classification['confidence'] > 0.5:
            # 保存住院记录
            record_data = {
                'hospital_name': ocr_result.get('hospital', ''),
                'department': ocr_result.get('department', ''),
                'admission_time': ocr_result.get('admission_time', ''),
                'discharge_time': ocr_result.get('discharge_time', ''),
                'admission_diagnosis': ocr_result.get('admission_diagnosis', ''),
                'discharge_diagnosis': ocr_result.get('discharge_diagnosis', ''),
                'chief_doctor': ocr_result.get('doctor', ''),
                'treatment': ocr_result.get('treatment', ''),
                'notes': '',
                'local_file_path': file_data.get('local_path', ''),
                'cloud_file_id': file_data.get('cloud_id', '')
            }
            
            record_id = self.add_hospital_record(record_data)
            
        elif classification['table'] == 'outpatient_record' and classification['confidence'] > 0.5:
            # 保存门诊记录
            record_data = {
                'hospital_name': ocr_result.get('hospital', ''),
                'department': ocr_result.get('department', ''),
                'visit_time': ocr_result.get('visit_time', ''),
                'doctor': ocr_result.get('doctor', ''),
                'chief_complaint': ocr_result.get('chief_complaint', ''),
                'diagnosis': ocr_result.get('diagnosis', ''),
                'treatment': ocr_result.get('treatment', ''),
                'prescription': ocr_result.get('prescription', ''),
                'follow_up': ocr_result.get('follow_up', ''),
                'notes': '',
                'local_file_path': file_data.get('local_path', ''),
                'cloud_file_id': file_data.get('cloud_id', '')
            }
            
            record_id = self.add_outpatient_record(record_data)
        
        # 更新文件上传记录
        if record_id:
            # 更新文件上传记录
            update_data = {
                'related_table': classification['table'],
                'related_id': record_id,
                'processing_status': 'processed'
            }
            
            file_id = file_data.get('id')
            if file_id:
                self.update_record('file_upload', file_id, update_data)
        
        return {
            'classification': classification,
            'record_id': record_id
        }