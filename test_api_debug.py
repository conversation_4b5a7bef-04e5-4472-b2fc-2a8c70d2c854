import requests
import json

# 测试API的查询逻辑
base_url = "http://localhost:8000"

# 添加认证头部（模拟移动端请求）
headers = {
    "X-User-ID": "SM_006",
    "Content-Type": "application/json"
}

def test_assessments_api():
    print("=== 测试量表API ===")
    
    # 1. 测试不带状态过滤的查询
    print("\n1. 不带状态过滤的查询:")
    response = requests.get(f"{base_url}/api/mobile/assessments?custom_id=SM_006&limit=20", headers=headers)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        if 'data' in data:
            assessments = data['data']
            total = data.get('total', len(assessments))
            print(f"返回的量表数量: {len(assessments)}")
            print(f"API返回的total: {total}")
            
            for i, assessment in enumerate(assessments, 1):
                print(f"  量表{i}: ID={assessment.get('id')}, 名称={assessment.get('name')}, 状态={assessment.get('status')}")
        else:
            print("响应中没有data字段")
    else:
        print(f"请求失败: {response.text}")
    
    # 2. 测试只查询pending状态的量表
    print("\n2. 只查询pending状态的量表:")
    response = requests.get(f"{base_url}/api/mobile/assessments?custom_id=SM_006&status=pending&limit=20", headers=headers)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        if 'data' in data:
            assessments = data['data']
            total = data.get('total', len(assessments))
            print(f"返回的量表数量: {len(assessments)}")
            print(f"API返回的total: {total}")
            
            for i, assessment in enumerate(assessments, 1):
                print(f"  量表{i}: ID={assessment.get('id')}, 名称={assessment.get('name')}, 状态={assessment.get('status')}")
        else:
            print("响应中没有data字段")
    else:
        print(f"请求失败: {response.text}")
    
    # 3. 测试只查询completed状态的量表
    print("\n3. 只查询completed状态的量表:")
    response = requests.get(f"{base_url}/api/mobile/assessments?custom_id=SM_006&status=completed&limit=20", headers=headers)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        if 'data' in data:
            assessments = data['data']
            total = data.get('total', len(assessments))
            print(f"返回的量表数量: {len(assessments)}")
            print(f"API返回的total: {total}")
            
            for i, assessment in enumerate(assessments, 1):
                print(f"  量表{i}: ID={assessment.get('id')}, 名称={assessment.get('name')}, 状态={assessment.get('status')}")
        else:
            print("响应中没有data字段")
    else:
        print(f"请求失败: {response.text}")
    
    # 4. 测试分页逻辑
    print("\n4. 测试分页逻辑 (limit=2):")
    response = requests.get(f"{base_url}/api/mobile/assessments?custom_id=SM_006&limit=2", headers=headers)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        if 'data' in data:
            assessments = data['data']
            total = data.get('total', len(assessments))
            print(f"返回的量表数量: {len(assessments)}")
            print(f"API返回的total: {total}")
            
            for i, assessment in enumerate(assessments, 1):
                print(f"  量表{i}: ID={assessment.get('id')}, 名称={assessment.get('name')}, 状态={assessment.get('status')}")
        else:
            print("响应中没有data字段")
    else:
        print(f"请求失败: {response.text}")

if __name__ == "__main__":
    test_assessments_api()