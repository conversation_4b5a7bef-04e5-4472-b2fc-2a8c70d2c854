import { createApp } from "vue";
import { createPinia } from "pinia";
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import zhCn from "element-plus/es/locale/lang/zh-cn";
import axios from "axios";
import App from "./App.vue";
import router from "./router";
import Toast from "vue-toastification";
import "vue-toastification/dist/index.css";
import "bootstrap/dist/css/bootstrap.min.css";
import "bootstrap/dist/js/bootstrap.bundle.min.js";

// 统一管理平台集成
import { SystemManager } from "./utils/system-manager";
import { HealthMonitor } from "./utils/health-monitor";

// 导入 ElementPlus 图标
import * as ElementPlusIconsVue from "@element-plus/icons-vue";

// 导入自定义样式
import "./assets/styles/index.js";

// 导入可访问性修复插件
import AccessibilityFixes from "./utils/accessibility-fixes.js";

// 自动选择 API 地址
const getApiUrl = () => {
  // 检测是否为本地环境
  const isLocalhost =
    window.location.hostname === "localhost" ||
    window.location.hostname === "127.0.0.1";

  // 根据环境选择 API 地址
  return isLocalhost
    ? import.meta.env.VITE_LOCAL_API_URL
    : import.meta.env.VITE_PUBLIC_API_URL;
};

// 配置axios
axios.defaults.baseURL = ""; // 使用相对路径，让代理处理
axios.defaults.withCredentials = true;

// 统一管理平台配置
const systemManager = new SystemManager();
const healthMonitor = new HealthMonitor();

// 系统状态监控
let systemStatusInterval = null;

// 导入标准认证模块
import { initAuth, getToken } from "./utils/auth-standard";

// 全局认证处理函数
function setupAuthGlobally() {
  console.log("设置全局认证...");

  // 初始化认证状态
  initAuth();

  console.log("全局认证已设置");
}

// 系统管理功能
function setupSystemManagement() {
  console.log("初始化系统管理功能...");
  
  // 启动系统监控
  systemManager.initialize();
  healthMonitor.startMonitoring();
  
  // 定期检查系统状态
  systemStatusInterval = setInterval(async () => {
    try {
      const status = await systemManager.getSystemStatus();
      if (status.error) {
        console.warn("系统状态检查发现问题:", status.error);
      }
    } catch (error) {
      console.error("系统状态检查失败:", error);
    }
  }, 30000); // 每30秒检查一次
  
  console.log("系统管理功能已初始化");
}

// 应用全局认证设置
setupAuthGlobally();
setupAuthGlobally();

// 添加请求拦截器
axios.interceptors.request.use(
  (config) => {
    console.log("发送请求:", config.url, config.method);

    // 添加 token 到请求头
    let token = getToken();
    if (token) {
      // 使用标准认证模块获取令牌，不需要修复令牌格式

      // 如果令牌无效，直接返回配置
      if (!token) {
        console.log(`axios拦截器 - 令牌无效，已清除`);
        return config;
      }

      // 确保headers对象存在
      config.headers = config.headers || {};

      // 设置认证头部
      config.headers.Authorization = `Bearer ${token}`;

      // 调试信息
      console.log(`已添加认证头部到请求: ${config.url}`);
      console.log(`认证头部值: Bearer ${token.substring(0, 10)}...`);
    } else {
      console.warn(`请求 ${config.url} 没有认证令牌`);
    }

    // 添加X-User-ID头部，确保问卷相关API能正常工作
    let customId = localStorage.getItem("custom_id");

    // 如果没有custom_id，尝试从用户信息中获取
    if (!customId) {
      const userStr = localStorage.getItem("user");
      if (userStr) {
        try {
          const user = JSON.parse(userStr);
          if (user && user.custom_id) {
            customId = user.custom_id;
            localStorage.setItem("custom_id", customId);
          } else if (user && user.id) {
            customId = user.id.toString();
          }
        } catch (e) {
          console.warn('解析用户信息失败:', e);
        }
      }
    }

    // 对于问卷相关API，如果没有customId则使用默认值
    if (!customId && (config.url.includes('/questionnaires') || config.url.includes('/templates/questionnaire-templates'))) {
      customId = '1';
      console.log('为问卷相关API使用默认X-User-ID: 1');
    }

    if (customId) {
      config.headers = config.headers || {};
      config.headers["X-User-ID"] = customId;
      console.log(`已添加X-User-ID头: ${customId}`);
    }

    return config;
  },
  (error) => {
    console.error("请求错误:", error);
    return Promise.reject(error);
  }
);

// 添加响应拦截器
axios.interceptors.response.use(
  (response) => {
    console.log("收到响应:", response.status, response.config.url);
    return response;
  },
  (error) => {
    console.error("响应错误:", error);
    if (error.response) {
      console.error("错误状态码:", error.response.status);
      console.error("错误数据:", error.response.data);
      console.error("请求URL:", error.config.url);
      console.error("请求方法:", error.config.method);
      console.error("请求头:", error.config.headers);

      // 处理 401 未授权错误
      if (error.response.status === 401) {
        console.warn("收到401未授权错误，正在重定向到登录页面");
        localStorage.removeItem("token");
        router.push("/login");
      }

      // 处理 403 禁止访问错误
      if (error.response.status === 403) {
        console.warn("收到403禁止访问错误，可能是认证令牌无效");

        // 检查认证头部
        const authHeader = error.config.headers.Authorization;
        if (!authHeader) {
          console.error("请求没有包含认证头部");
        } else {
          console.error("认证头部:", authHeader);
        }

        // 尝试重新获取令牌
        const token = localStorage.getItem("token");
        if (token) {
          console.log("本地存储中的令牌:", token.substring(0, 10) + "...");
        } else {
          console.error("本地存储中没有令牌");
          // 重定向到登录页面
          router.push("/login");
        }
      }
    }
    return Promise.reject(error);
  }
);

// 创建应用实例
const app = createApp(App);

// 使用Pinia状态管理
app.use(createPinia());

// 使用Vue Router
app.use(router);

// 使用Element Plus
app.use(ElementPlus, {
  locale: zhCn,
});

// 注册所有 Element Plus 图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(`ElIcon${key}`, component);
}

// 使用Vue Toastification
app.use(Toast, {
  position: "top-right",
  timeout: 3000,
  closeOnClick: true,
  pauseOnFocusLoss: true,
  pauseOnHover: true,
  draggable: true,
  draggablePercent: 0.6,
  showCloseButtonOnHover: false,
  hideProgressBar: false,
  closeButton: "button",
  icon: true,
  rtl: false,
});

// 使用可访问性修复插件
app.use(AccessibilityFixes);

// 全局错误处理
app.config.errorHandler = (err, instance, info) => {
  console.error("Vue 错误:", err);
  console.error(
    "错误组件:",
    instance ? instance.$options.name || "未命名组件" : "未知组件"
  );
  console.error("错误信息:", info);
};

// 导入模拟API设置
import { setupMocks } from "./mocks";

// 导入诊断工具
import { runApiDiagnostic } from "./utils/api-diagnostic";

// 设置模拟API
setupMocks();

// 添加诊断工具到全局
window.runApiDiagnostic = runApiDiagnostic;
console.log(
  "%c[API诊断] 使用 window.runApiDiagnostic() 可以运行API连通性诊断",
  "background: #007bff; color: white; padding: 2px 5px; border-radius: 3px;"
);

// 初始化认证
initAuth();
console.log("认证已初始化");

// 挂载应用
const appInstance = app.mount("#app");

// 初始化系统管理功能
setupSystemManagement();

// 将Vue实例和路由器暴露给全局，以便调试和修复脚本使用
window.app = appInstance;
window.$router = router;
window.axios = axios;
window.systemManager = systemManager;
window.healthMonitor = healthMonitor;
console.log("Vue应用、路由器和系统管理器已暴露为全局变量");

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
  if (systemStatusInterval) {
    clearInterval(systemStatusInterval);
  }
  healthMonitor.stopMonitoring();
  systemManager.cleanup();
});
