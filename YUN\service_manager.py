# -*- coding: utf-8 -*-
"""
健康管理系统统一服务管理器
统一管理前端、后端服务的启动、停止、重启和监控

版本: 1.0
作者: Health Management System
创建时间: 2024-12-30
"""

import os
import sys
import json
import asyncio
import subprocess
import psutil
import time
import signal
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from enum import Enum

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入配置管理器
try:
    from config_manager import ConfigManager, ConfigType
except ImportError:
    print("警告：无法导入配置管理器，使用默认配置")
    class ConfigManager:
        def get_config(self, config_type): return {}

# 导入后端核心模块
try:
    from backend.app.core.logging_utils import get_logger
except ImportError:
    import logging
    def get_logger(name):
        return logging.getLogger(name)

logger = get_logger(__name__)

class ServiceType(Enum):
    """服务类型"""
    BACKEND = "backend"
    FRONTEND = "frontend"
    DATABASE = "database"
    REDIS = "redis"
    NGINX = "nginx"

class ServiceStatus(Enum):
    """服务状态"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"
    UNKNOWN = "unknown"

@dataclass
class ServiceConfig:
    """服务配置"""
    name: str
    type: ServiceType
    command: str
    working_dir: Path
    port: Optional[int] = None
    env_vars: Dict[str, str] = field(default_factory=dict)
    dependencies: List[str] = field(default_factory=list)
    health_check_url: Optional[str] = None
    auto_restart: bool = True
    max_restart_attempts: int = 3
    restart_delay: int = 5

@dataclass
class ServiceInstance:
    """服务实例"""
    config: ServiceConfig
    process: Optional[subprocess.Popen] = None
    pid: Optional[int] = None
    status: ServiceStatus = ServiceStatus.STOPPED
    start_time: Optional[datetime] = None
    restart_count: int = 0
    last_error: Optional[str] = None
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    
class ServiceManager:
    """服务管理器"""
    
    def __init__(self):
        """初始化服务管理器"""
        self.project_root = project_root
        self.backend_dir = self.project_root / "backend"
        self.frontend_dir = self.project_root / "frontend"
        self.services = {}
        self.config_manager = ConfigManager()
        self.monitoring_task = None
        self.is_monitoring = False
        
        logger.info(f"服务管理器初始化完成，项目根目录: {self.project_root}")
        
        # 初始化服务配置
        self._initialize_services()
    
    def _initialize_services(self):
        """初始化服务配置"""
        try:
            # 后端服务配置
            backend_config = ServiceConfig(
                name="backend",
                type=ServiceType.BACKEND,
                command="python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload",
                working_dir=self.backend_dir,
                port=8000,
                health_check_url="http://localhost:8000/",
                env_vars={
                    "PYTHONPATH": str(self.backend_dir),
                    "ENV": "development"
                }
            )
            
            self.services["backend"] = ServiceInstance(config=backend_config)
            
            # 前端服务配置
            frontend_config = ServiceConfig(
                name="frontend",
                type=ServiceType.FRONTEND,
                command="npm run serve",
                working_dir=self.frontend_dir,
                port=8080,
                health_check_url="http://localhost:8080/",
                dependencies=["backend"]
            )
            
            self.services["frontend"] = ServiceInstance(config=frontend_config)
            
            logger.info(f"已初始化 {len(self.services)} 个服务")
            
        except Exception as e:
            logger.error(f"初始化服务配置失败: {str(e)}")
    
    def _get_process_info(self, pid: int) -> Dict[str, Any]:
        """获取进程信息"""
        try:
            process = psutil.Process(pid)
            return {
                'cpu_percent': process.cpu_percent(),
                'memory_percent': process.memory_percent(),
                'memory_info': process.memory_info()._asdict(),
                'status': process.status(),
                'create_time': process.create_time()
            }
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            return {}
    
    def _is_port_in_use(self, port: int) -> bool:
        """检查端口是否被占用"""
        try:
            for conn in psutil.net_connections():
                if conn.laddr.port == port and conn.status == psutil.CONN_LISTEN:
                    return True
            return False
        except Exception:
            return False
    
    async def _health_check(self, service_instance: ServiceInstance) -> bool:
        """健康检查"""
        try:
            if not service_instance.config.health_check_url:
                # 如果没有健康检查URL，只检查进程是否存在
                return service_instance.process and service_instance.process.poll() is None
            
            # 使用HTTP请求进行健康检查
            import aiohttp
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    service_instance.config.health_check_url,
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    return response.status == 200
                    
        except Exception as e:
            logger.debug(f"健康检查失败 {service_instance.config.name}: {str(e)}")
            return False
    
    async def start_service(self, service_name: str) -> bool:
        """启动服务"""
        try:
            if service_name not in self.services:
                logger.error(f"服务不存在: {service_name}")
                return False
            
            service_instance = self.services[service_name]
            config = service_instance.config
            
            # 检查服务是否已经运行
            if service_instance.status == ServiceStatus.RUNNING:
                logger.info(f"服务 {service_name} 已经在运行")
                return True
            
            # 检查依赖服务
            for dep in config.dependencies:
                if dep in self.services:
                    dep_service = self.services[dep]
                    if dep_service.status != ServiceStatus.RUNNING:
                        logger.info(f"启动依赖服务: {dep}")
                        if not await self.start_service(dep):
                            logger.error(f"启动依赖服务失败: {dep}")
                            return False
            
            # 检查端口占用
            if config.port and self._is_port_in_use(config.port):
                logger.warning(f"端口 {config.port} 已被占用")
            
            service_instance.status = ServiceStatus.STARTING
            logger.info(f"正在启动服务: {service_name}")
            
            # 准备环境变量
            env = os.environ.copy()
            env.update(config.env_vars)
            
            # 启动进程
            if os.name == 'nt':  # Windows
                # 在Windows上使用shell=True
                process = subprocess.Popen(
                    config.command,
                    cwd=config.working_dir,
                    env=env,
                    shell=True,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
                )
            else:  # Unix/Linux
                process = subprocess.Popen(
                    config.command.split(),
                    cwd=config.working_dir,
                    env=env,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    preexec_fn=os.setsid
                )
            
            service_instance.process = process
            service_instance.pid = process.pid
            service_instance.start_time = datetime.now()
            
            # 等待服务启动
            await asyncio.sleep(2)
            
            # 检查进程是否还在运行
            if process.poll() is not None:
                # 进程已退出
                stdout, stderr = process.communicate()
                error_msg = stderr.decode() if stderr else "进程意外退出"
                service_instance.status = ServiceStatus.ERROR
                service_instance.last_error = error_msg
                logger.error(f"服务 {service_name} 启动失败: {error_msg}")
                return False
            
            # 进行健康检查
            health_check_attempts = 0
            max_attempts = 10
            
            while health_check_attempts < max_attempts:
                if await self._health_check(service_instance):
                    service_instance.status = ServiceStatus.RUNNING
                    logger.info(f"✅ 服务 {service_name} 启动成功 (PID: {process.pid})")
                    if config.port:
                        logger.info(f"   端口: {config.port}")
                    return True
                
                health_check_attempts += 1
                await asyncio.sleep(1)
            
            # 健康检查失败，但进程仍在运行
            logger.warning(f"服务 {service_name} 启动但健康检查失败")
            service_instance.status = ServiceStatus.RUNNING  # 假设运行正常
            return True
            
        except Exception as e:
            service_instance.status = ServiceStatus.ERROR
            service_instance.last_error = str(e)
            logger.error(f"启动服务 {service_name} 失败: {str(e)}")
            return False
    
    async def stop_service(self, service_name: str) -> bool:
        """停止服务"""
        try:
            if service_name not in self.services:
                logger.error(f"服务不存在: {service_name}")
                return False
            
            service_instance = self.services[service_name]
            
            if service_instance.status == ServiceStatus.STOPPED:
                logger.info(f"服务 {service_name} 已经停止")
                return True
            
            service_instance.status = ServiceStatus.STOPPING
            logger.info(f"正在停止服务: {service_name}")
            
            if service_instance.process:
                try:
                    # 优雅停止
                    if os.name == 'nt':  # Windows
                        service_instance.process.send_signal(signal.CTRL_BREAK_EVENT)
                    else:  # Unix/Linux
                        os.killpg(os.getpgid(service_instance.process.pid), signal.SIGTERM)
                    
                    # 等待进程结束
                    try:
                        service_instance.process.wait(timeout=10)
                    except subprocess.TimeoutExpired:
                        # 强制终止
                        logger.warning(f"强制终止服务: {service_name}")
                        if os.name == 'nt':
                            service_instance.process.kill()
                        else:
                            os.killpg(os.getpgid(service_instance.process.pid), signal.SIGKILL)
                        service_instance.process.wait()
                    
                except Exception as e:
                    logger.warning(f"停止服务进程失败: {str(e)}")
            
            service_instance.process = None
            service_instance.pid = None
            service_instance.status = ServiceStatus.STOPPED
            service_instance.start_time = None
            
            logger.info(f"✅ 服务 {service_name} 已停止")
            return True
            
        except Exception as e:
            logger.error(f"停止服务 {service_name} 失败: {str(e)}")
            return False
    
    async def restart_service(self, service_name: str) -> bool:
        """重启服务"""
        logger.info(f"重启服务: {service_name}")
        
        # 先停止服务
        if not await self.stop_service(service_name):
            return False
        
        # 等待一段时间
        await asyncio.sleep(2)
        
        # 再启动服务
        return await self.start_service(service_name)
    
    def get_service_status(self, service_name: str) -> Dict[str, Any]:
        """获取服务状态"""
        if service_name not in self.services:
            return {'error': f'服务不存在: {service_name}'}
        
        service_instance = self.services[service_name]
        config = service_instance.config
        
        status_info = {
            'name': config.name,
            'type': config.type.value,
            'status': service_instance.status.value,
            'pid': service_instance.pid,
            'port': config.port,
            'start_time': service_instance.start_time.isoformat() if service_instance.start_time else None,
            'restart_count': service_instance.restart_count,
            'last_error': service_instance.last_error,
            'uptime': None,
            'cpu_usage': service_instance.cpu_usage,
            'memory_usage': service_instance.memory_usage
        }
        
        # 计算运行时间
        if service_instance.start_time and service_instance.status == ServiceStatus.RUNNING:
            uptime = datetime.now() - service_instance.start_time
            status_info['uptime'] = str(uptime).split('.')[0]  # 去掉微秒
        
        # 获取进程信息
        if service_instance.pid:
            process_info = self._get_process_info(service_instance.pid)
            if process_info:
                status_info['cpu_usage'] = process_info.get('cpu_percent', 0)
                status_info['memory_usage'] = process_info.get('memory_percent', 0)
                service_instance.cpu_usage = status_info['cpu_usage']
                service_instance.memory_usage = status_info['memory_usage']
        
        return status_info
    
    def get_all_services_status(self) -> Dict[str, Any]:
        """获取所有服务状态"""
        status = {
            'timestamp': datetime.now().isoformat(),
            'services': {},
            'summary': {
                'total': len(self.services),
                'running': 0,
                'stopped': 0,
                'error': 0
            }
        }
        
        for service_name in self.services:
            service_status = self.get_service_status(service_name)
            status['services'][service_name] = service_status
            
            # 更新统计
            if service_status.get('status') == 'running':
                status['summary']['running'] += 1
            elif service_status.get('status') == 'stopped':
                status['summary']['stopped'] += 1
            elif service_status.get('status') == 'error':
                status['summary']['error'] += 1
        
        return status
    
    async def start_monitoring(self):
        """启动监控"""
        if self.is_monitoring:
            logger.info("监控已经在运行")
            return
        
        self.is_monitoring = True
        logger.info("启动服务监控")
        
        while self.is_monitoring:
            try:
                for service_name, service_instance in self.services.items():
                    if service_instance.status == ServiceStatus.RUNNING:
                        # 检查进程是否还存在
                        if service_instance.process and service_instance.process.poll() is not None:
                            logger.warning(f"检测到服务 {service_name} 进程已退出")
                            service_instance.status = ServiceStatus.ERROR
                            
                            # 自动重启
                            if (service_instance.config.auto_restart and 
                                service_instance.restart_count < service_instance.config.max_restart_attempts):
                                
                                logger.info(f"尝试自动重启服务 {service_name} (第 {service_instance.restart_count + 1} 次)")
                                service_instance.restart_count += 1
                                
                                await asyncio.sleep(service_instance.config.restart_delay)
                                await self.start_service(service_name)
                        
                        # 更新进程信息
                        if service_instance.pid:
                            process_info = self._get_process_info(service_instance.pid)
                            if process_info:
                                service_instance.cpu_usage = process_info.get('cpu_percent', 0)
                                service_instance.memory_usage = process_info.get('memory_percent', 0)
                
                await asyncio.sleep(10)  # 每10秒检查一次
                
            except Exception as e:
                logger.error(f"监控过程中发生错误: {str(e)}")
                await asyncio.sleep(5)
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        logger.info("停止服务监控")
    
    async def start_all_services(self) -> bool:
        """启动所有服务"""
        logger.info("启动所有服务")
        
        # 按依赖关系排序启动
        started_services = set()
        all_success = True
        
        def can_start(service_name):
            config = self.services[service_name].config
            return all(dep in started_services for dep in config.dependencies)
        
        while len(started_services) < len(self.services):
            progress_made = False
            
            for service_name in self.services:
                if service_name not in started_services and can_start(service_name):
                    success = await self.start_service(service_name)
                    if success:
                        started_services.add(service_name)
                    else:
                        all_success = False
                    progress_made = True
            
            if not progress_made:
                # 无法继续启动，可能存在循环依赖或其他问题
                logger.error("无法启动剩余服务，可能存在依赖问题")
                break
        
        return all_success
    
    async def stop_all_services(self) -> bool:
        """停止所有服务"""
        logger.info("停止所有服务")
        
        all_success = True
        
        # 反向停止服务（先停止依赖者）
        for service_name in reversed(list(self.services.keys())):
            success = await self.stop_service(service_name)
            if not success:
                all_success = False
        
        return all_success

# 主要功能函数
async def main():
    """主函数"""
    manager = ServiceManager()
    
    try:
        print("\n" + "="*60)
        print("健康管理系统服务管理器")
        print("="*60)
        
        # 交互式菜单
        while True:
            print("\n" + "-"*40)
            print("请选择操作:")
            print("1. 启动后端服务")
            print("2. 启动前端服务")
            print("3. 启动所有服务")
            print("4. 停止后端服务")
            print("5. 停止前端服务")
            print("6. 停止所有服务")
            print("7. 重启后端服务")
            print("8. 重启前端服务")
            print("9. 查看服务状态")
            print("10. 启动监控")
            print("11. 停止监控")
            print("0. 退出")
            
            choice = input("\n请输入选择 (0-11): ").strip()
            
            if choice == '1':
                print("\n🚀 启动后端服务...")
                success = await manager.start_service('backend')
                print(f"结果: {'成功' if success else '失败'}")
                
            elif choice == '2':
                print("\n🚀 启动前端服务...")
                success = await manager.start_service('frontend')
                print(f"结果: {'成功' if success else '失败'}")
                
            elif choice == '3':
                print("\n🚀 启动所有服务...")
                success = await manager.start_all_services()
                print(f"结果: {'全部成功' if success else '部分失败'}")
                
            elif choice == '4':
                print("\n🛑 停止后端服务...")
                success = await manager.stop_service('backend')
                print(f"结果: {'成功' if success else '失败'}")
                
            elif choice == '5':
                print("\n🛑 停止前端服务...")
                success = await manager.stop_service('frontend')
                print(f"结果: {'成功' if success else '失败'}")
                
            elif choice == '6':
                print("\n🛑 停止所有服务...")
                success = await manager.stop_all_services()
                print(f"结果: {'全部成功' if success else '部分失败'}")
                
            elif choice == '7':
                print("\n🔄 重启后端服务...")
                success = await manager.restart_service('backend')
                print(f"结果: {'成功' if success else '失败'}")
                
            elif choice == '8':
                print("\n🔄 重启前端服务...")
                success = await manager.restart_service('frontend')
                print(f"结果: {'成功' if success else '失败'}")
                
            elif choice == '9':
                print("\n📊 服务状态:")
                status = manager.get_all_services_status()
                
                print(f"\n总览: {status['summary']['total']} 个服务")
                print(f"运行中: {status['summary']['running']}")
                print(f"已停止: {status['summary']['stopped']}")
                print(f"错误: {status['summary']['error']}")
                
                print("\n详细状态:")
                for name, info in status['services'].items():
                    status_icon = {
                        'running': '🟢',
                        'stopped': '🔴',
                        'error': '🟡',
                        'starting': '🟡',
                        'stopping': '🟡'
                    }.get(info['status'], '⚪')
                    
                    print(f"  {status_icon} {name}: {info['status']}")
                    if info.get('port'):
                        print(f"    端口: {info['port']}")
                    if info.get('uptime'):
                        print(f"    运行时间: {info['uptime']}")
                    if info.get('cpu_usage'):
                        print(f"    CPU: {info['cpu_usage']:.1f}%")
                    if info.get('memory_usage'):
                        print(f"    内存: {info['memory_usage']:.1f}%")
                
            elif choice == '10':
                print("\n👁️ 启动监控...")
                if not manager.is_monitoring:
                    asyncio.create_task(manager.start_monitoring())
                    print("监控已启动")
                else:
                    print("监控已在运行")
                
            elif choice == '11':
                print("\n⏹️ 停止监控...")
                manager.stop_monitoring()
                print("监控已停止")
                
            elif choice == '0':
                print("\n👋 退出服务管理器")
                # 停止监控
                manager.stop_monitoring()
                break
                
            else:
                print("\n❌ 无效选择，请重新输入")
    
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
        manager.stop_monitoring()
    except Exception as e:
        print(f"\n❌ 运行错误: {str(e)}")
        logger.error(f"主函数运行错误: {str(e)}")
    finally:
        print("\n🧹 清理完成")

if __name__ == "__main__":
    asyncio.run(main())