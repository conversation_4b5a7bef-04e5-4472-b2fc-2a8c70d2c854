#!/usr/bin/env python3
"""
强制重置数据库脚本
"""
import os
import time
import shutil
from datetime import datetime

def force_reset_database():
    """强制重置数据库"""
    db_path = 'app.db'
    
    print("开始强制重置数据库...")
    
    # 1. 备份现有数据库（如果存在）
    if os.path.exists(db_path):
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_path = f"{db_path}.backup_{timestamp}"
        
        try:
            shutil.copy2(db_path, backup_path)
            print(f"已备份数据库到: {backup_path}")
        except Exception as e:
            print(f"备份数据库失败: {e}")
    
    # 2. 尝试多种方法删除数据库文件
    max_attempts = 5
    for attempt in range(max_attempts):
        try:
            if os.path.exists(db_path):
                # 尝试修改文件权限
                os.chmod(db_path, 0o777)
                
                # 尝试删除文件
                os.remove(db_path)
                print(f"成功删除数据库文件 (尝试 {attempt + 1})")
                break
        except PermissionError:
            print(f"权限错误，尝试 {attempt + 1}/{max_attempts}")
            time.sleep(1)
        except FileNotFoundError:
            print("数据库文件不存在")
            break
        except Exception as e:
            print(f"删除数据库文件失败 (尝试 {attempt + 1}/{max_attempts}): {e}")
            time.sleep(1)
    
    # 3. 如果无法删除，尝试重命名
    if os.path.exists(db_path):
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            old_path = f"{db_path}.old_{timestamp}"
            os.rename(db_path, old_path)
            print(f"无法删除，已重命名为: {old_path}")
        except Exception as e:
            print(f"重命名失败: {e}")
            # 如果还是失败，使用不同的数据库名
            db_path = f"app_new_{timestamp}.db"
            print(f"将使用新的数据库文件: {db_path}")
    
    # 4. 创建新的数据库文件
    try:
        import sqlite3
        
        print(f"创建新数据库: {db_path}")
        conn = sqlite3.connect(db_path)
        
        # 执行基本测试
        conn.execute('CREATE TABLE test_table (id INTEGER PRIMARY KEY)')
        conn.execute('INSERT INTO test_table (id) VALUES (1)')
        result = conn.execute('SELECT * FROM test_table').fetchone()
        conn.execute('DROP TABLE test_table')
        
        # 检查完整性
        integrity = conn.execute('PRAGMA integrity_check').fetchone()
        print(f"数据库完整性检查: {integrity[0]}")
        
        conn.close()
        
        if integrity[0] == 'ok':
            print("新数据库创建成功并通过完整性检查")
            
            # 更新配置文件中的数据库路径（如果需要）
            if db_path != 'app.db':
                print(f"注意: 新数据库文件名为 {db_path}")
                print("可能需要更新配置文件中的数据库路径")
            
            return True
        else:
            print("新数据库完整性检查失败")
            return False
            
    except Exception as e:
        print(f"创建新数据库失败: {e}")
        return False

def update_database_config(new_db_path):
    """更新数据库配置"""
    config_files = [
        'app/core/config.py',
        '.env',
        'app/.env'
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 替换数据库路径
                updated_content = content.replace('app.db', new_db_path)
                
                if updated_content != content:
                    with open(config_file, 'w', encoding='utf-8') as f:
                        f.write(updated_content)
                    print(f"已更新配置文件: {config_file}")
                    
            except Exception as e:
                print(f"更新配置文件 {config_file} 失败: {e}")

def main():
    """主函数"""
    print("数据库强制重置工具")
    print("警告: 此操作将删除所有现有数据")
    
    # 确认操作
    response = input("确定要继续吗？(y/n): ")
    if response.lower() != 'y':
        print("操作已取消")
        return
    
    if force_reset_database():
        print("\n数据库重置成功！")
        print("现在可以重新启动应用程序")
    else:
        print("\n数据库重置失败")

if __name__ == "__main__":
    main()