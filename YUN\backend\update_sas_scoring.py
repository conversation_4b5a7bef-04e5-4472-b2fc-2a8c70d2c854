# -*- coding: utf-8 -*-
import sqlite3
import json

def update_sas_scoring():
    try:
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # 为所有SAS问题设置计分规则
        scoring_rule = '{"1": 1, "2": 2, "3": 3, "4": 4}'
        
        # 获取所有SAS问题
        cursor.execute('SELECT question_id FROM assessment_template_questions WHERE template_id = 3 AND question_id LIKE "sas_%"')
        sas_questions = cursor.fetchall()
        
        print(f'找到 {len(sas_questions)} 个SAS问题')
        
        # 更新每个问题的计分规则
        for question in sas_questions:
            question_id = question[0]
            cursor.execute('UPDATE assessment_template_questions SET scoring = ? WHERE template_id = 3 AND question_id = ?', 
                         (scoring_rule, question_id))
            print(f'已更新 {question_id} 的计分规则')
        
        conn.commit()
        print('\n所有SAS问题的计分规则已更新')
        
        # 验证更新结果
        cursor.execute('SELECT question_id, scoring FROM assessment_template_questions WHERE template_id = 3 AND question_id LIKE "sas_%" LIMIT 3')
        updated_questions = cursor.fetchall()
        
        print('\n验证更新结果:')
        for q in updated_questions:
            print(f'  {q[0]}: {q[1]}')
        
        conn.close()
        
    except Exception as e:
        print(f'更新时出错: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    update_sas_scoring()