from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import sys
sys.path.append('c:/Users/<USER>/Desktop/health-Trea/YUN/backend')

from app.models.assessment import Assessment
from app.models.distribution import AssessmentDistribution

# 连接数据库
engine = create_engine('sqlite:///c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db', echo=True)
Session = sessionmaker(bind=engine)
session = Session()

print("=== 测试SQL查询问题 ===")

# 1. 测试原始的distinct查询
print("\n1. 测试原始的distinct查询:")
try:
    query = session.query(Assessment).outerjoin(AssessmentDistribution).distinct(Assessment.id).filter(Assessment.custom_id == 'SM_006')
    print(f"查询对象: {query}")
    print(f"生成的SQL: {query.statement.compile(compile_kwargs={'literal_binds': True})}")
    
    # 执行count查询
    count = query.count()
    print(f"Count结果: {count}")
    
    # 执行实际查询
    results = query.all()
    print(f"实际结果数量: {len(results)}")
    
except Exception as e:
    print(f"查询出错: {e}")

# 2. 测试不使用distinct的查询
print("\n2. 测试不使用distinct的查询:")
try:
    query2 = session.query(Assessment).outerjoin(AssessmentDistribution).filter(Assessment.custom_id == 'SM_006')
    print(f"生成的SQL: {query2.statement.compile(compile_kwargs={'literal_binds': True})}")
    
    count2 = query2.count()
    print(f"Count结果: {count2}")
    
    results2 = query2.all()
    print(f"实际结果数量: {len(results2)}")
    
except Exception as e:
    print(f"查询出错: {e}")

# 3. 测试简单查询
print("\n3. 测试简单查询:")
try:
    query3 = session.query(Assessment).filter(Assessment.custom_id == 'SM_006')
    print(f"生成的SQL: {query3.statement.compile(compile_kwargs={'literal_binds': True})}")
    
    count3 = query3.count()
    print(f"Count结果: {count3}")
    
    results3 = query3.all()
    print(f"实际结果数量: {len(results3)}")
    
except Exception as e:
    print(f"查询出错: {e}")

session.close()