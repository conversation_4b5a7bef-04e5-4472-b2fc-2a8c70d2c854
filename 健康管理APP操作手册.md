# 健康管理APP操作手册

## 目录
1. [项目概述](#项目概述)
2. [环境配置](#环境配置)
3. [项目启动](#项目启动)
4. [代码质量检查](#代码质量检查)
5. [测试运行](#测试运行)
6. [功能模块操作](#功能模块操作)
7. [开发调试](#开发调试)
8. [部署指南](#部署指南)
9. [常见问题](#常见问题)

## 项目概述

健康管理APP是一个基于React Native开发的移动端健康管理应用，提供健康评估、数据记录、报告生成等功能。

### 技术栈
- **前端框架**: React Native 0.80.0
- **状态管理**: Redux Toolkit + Zustand
- **导航**: React Navigation 6.x
- **UI组件**: React Native Paper + React Native Elements
- **网络请求**: Axios + React Query
- **表单处理**: React Hook Form + Yup
- **图表**: React Native Chart Kit
- **国际化**: i18next

## 环境配置

### 系统要求
- Node.js >= 18
- npm >= 8
- React Native CLI
- Android Studio (Android开发)
- Xcode (iOS开发)

### 安装依赖
```bash
# 进入项目目录
cd "c:\Users\<USER>\Desktop\health-Trea\mobile_React Native"

# 安装依赖
npm install

# iOS额外步骤（仅macOS）
cd ios && pod install && cd ..
```

## 项目启动

### 1. 启动Metro服务器
```bash
# 在mobile_React Native目录下执行
npm start
```

### 2. 运行Android应用
```bash
# 确保Android模拟器或设备已连接
npm run android
```

### 3. 运行iOS应用
```bash
# 仅在macOS上可用
npm run ios
```

## 代码质量检查

### 完整质量检查
```bash
# 运行完整的代码质量检查
node scripts/code-quality-monitor.js
```

### 分项检查

#### TypeScript类型检查
```bash
# 开发模式（跳过严格检查）
npm run type-check

# 完整类型检查
npx tsc --noEmit
```

#### ESLint代码规范检查
```bash
# 检查代码规范
npm run lint

# 自动修复可修复的问题
npm run lint:fix
```

#### Prettier代码格式化
```bash
# 检查代码格式
npm run prettier:check

# 自动格式化代码
npm run prettier
```

#### 一键修复质量问题
```bash
# 自动修复ESLint和Prettier问题
npm run quality:fix
```

## 测试运行

### 单元测试
```bash
# 运行所有测试
npm test

# 监听模式运行测试
npm run test:watch

# 生成测试覆盖率报告
npm run test:coverage

# CI环境测试
npm run test:ci
```

### E2E测试（Detox）

#### iOS E2E测试
```bash
# 构建iOS测试应用
npm run e2e:build:ios

# 运行iOS E2E测试
npm run e2e:test:ios
```

#### Android E2E测试
```bash
# 构建Android测试应用
npm run e2e:build:android

# 运行Android E2E测试
npm run e2e:test:android
```

### 测试过程运行动作

#### 1. 准备测试环境
```bash
# 清理缓存
npm run clean

# 重新安装依赖
npm install

# 检查依赖安全性
npm run audit:security
```

#### 2. 执行测试流程
```bash
# 步骤1: 代码质量检查
node scripts/code-quality-monitor.js

# 步骤2: 运行单元测试
npm run test:ci

# 步骤3: 构建应用
npm run android  # 或 npm run ios

# 步骤4: 运行E2E测试
npm run e2e:build:android
npm run e2e:test:android
```

#### 3. 验证测试结果
- 检查测试覆盖率报告（coverage/目录）
- 查看代码质量报告
- 验证E2E测试截图和日志

## 功能模块操作

### 用户认证模块
- **登录**: 支持用户名/密码登录
- **注册**: 新用户注册功能
- **第三方登录**: Google、Apple登录集成
- **密码重置**: 忘记密码功能

### 健康评估模块
- **问卷填写**: 多种健康评估问卷
- **评估历史**: 查看历史评估记录
- **报告生成**: 自动生成健康报告
- **数据导出**: 支持PDF导出

### 健康数据管理
- **数据录入**: 手动录入健康数据
- **数据同步**: 与健康设备同步
- **图表展示**: 数据可视化展示
- **趋势分析**: 健康趋势分析

### 设置与配置
- **个人信息**: 用户资料管理
- **通知设置**: 推送通知配置
- **隐私设置**: 数据隐私控制
- **语言切换**: 多语言支持

## 开发调试

### 调试工具
```bash
# 启动React Native调试器
npx react-native start --reset-cache

# 启用远程调试
# 在模拟器中按 Cmd+D (iOS) 或 Ctrl+M (Android)
# 选择 "Debug with Chrome"
```

### 日志查看
```bash
# Android日志
npx react-native log-android

# iOS日志
npx react-native log-ios
```

### 性能监控
- 使用Flipper进行性能调试
- React DevTools集成
- Redux DevTools支持

## 部署指南

### Android部署
```bash
# 生成签名APK
cd android
./gradlew assembleRelease

# 生成AAB包（Google Play）
./gradlew bundleRelease
```

### iOS部署
```bash
# 在Xcode中配置签名
# Product -> Archive
# 上传到App Store Connect
```

### 代码签名配置
1. 配置Android签名密钥
2. 设置iOS证书和描述文件
3. 配置环境变量

## 常见问题

### 1. Metro服务器启动失败
```bash
# 清理缓存
npm run clean
npx react-native start --reset-cache
```

### 2. 依赖安装问题
```bash
# 删除node_modules重新安装
rm -rf node_modules
npm install

# iOS pod安装问题
cd ios && pod deintegrate && pod install
```

### 3. 构建错误
```bash
# Android清理
cd android && ./gradlew clean

# iOS清理
npm run clean:ios
```

### 4. 类型检查错误
- 检查tsconfig.json配置
- 确认路径别名设置正确
- 验证依赖包类型定义

### 5. ESLint规则冲突
- 查看.eslintrc.json配置
- 使用npm run lint:fix自动修复
- 必要时禁用特定规则

### 6. 测试覆盖率不足
- 添加更多单元测试
- 配置jest.config.js
- 设置合理的覆盖率阈值

## 开发最佳实践

### 代码规范
1. 遵循ESLint和Prettier配置
2. 使用TypeScript严格模式
3. 编写有意义的注释
4. 保持组件单一职责

### 测试策略
1. 单元测试覆盖核心逻辑
2. 集成测试验证模块交互
3. E2E测试覆盖关键用户流程
4. 定期运行性能测试

### 版本控制
1. 使用语义化版本号
2. 编写清晰的提交信息
3. 使用分支策略管理功能开发
4. 定期进行代码审查

---

**注意**: 本手册基于当前项目配置编写，如有配置变更请及时更新文档。

**联系方式**: 如有问题请联系开发团队。