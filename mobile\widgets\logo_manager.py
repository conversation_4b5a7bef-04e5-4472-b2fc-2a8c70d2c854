"""
Logo管理器模块

这个模块提供了一个单例的Logo管理器，用于确保每个屏幕中只有一个Logo实例。
"""
from kivy.app import App
from kivy.uix.widget import Widget
from kivy.properties import ObjectProperty
from kivy.clock import Clock

from .logo import HealthLogo

class LogoManager:
    """Logo管理器，确保每个屏幕中只有一个Logo实例"""
    _instance = None
    _logo_instances = []
    _screen_logos = {}  # 按屏幕分组的Logo实例

    @classmethod
    def get_instance(cls):
        """获取LogoManager的单例实例"""
        if cls._instance is None:
            cls._instance = LogoManager()
        return cls._instance

    def register_logo(self, logo_instance):
        """注册Logo实例，确保每个屏幕只有一个Logo"""
        # 如果已经注册过这个实例，直接返回
        if logo_instance in self._logo_instances:
            return False

        # 添加到全局实例列表
        self._logo_instances.append(logo_instance)

        # 尝试获取Logo所属的屏幕
        screen = self._find_parent_screen(logo_instance)
        if not screen:
            return True

        screen_name = screen.__class__.__name__
        
        # 检查是否已有Logo
        if screen_name in self._screen_logos:
            existing_logos = self._screen_logos[screen_name]
            if existing_logos:
                # 移除新的Logo，保留第一个
                if logo_instance.parent:
                    logo_instance.parent.remove_widget(logo_instance)
                print(f"警告: 屏幕 {screen_name} 已有Logo，移除重复实例")
                return False
        
        # 注册新Logo
        if screen_name not in self._screen_logos:
            self._screen_logos[screen_name] = []
        self._screen_logos[screen_name].append(logo_instance)
        return True
        
    def _find_parent_screen(self, widget):
        """查找组件所属的屏幕

        Args:
            widget: 要查找的组件

        Returns:
            Screen: 组件所属的屏幕，如果找不到则返回None
        """
        from kivy.uix.screenmanager import Screen

        # 如果widget为None或没有parent属性，直接返回None
        if widget is None or not hasattr(widget, 'parent'):
            return None

        # 递归查找父组件
        parent = widget.parent
        while parent:
            if isinstance(parent, Screen):
                return parent
            parent = parent.parent

        return None

    def unregister_logo(self, logo_instance):
        """注销一个Logo实例

        Args:
            logo_instance: HealthLogo实例

        Returns:
            bool: 如果成功注销，返回True；否则返回False
        """
        if logo_instance in self._logo_instances:
            self._logo_instances.remove(logo_instance)

            # 从屏幕分组中移除
            for screen_name, logos in self._screen_logos.items():
                if logo_instance in logos:
                    logos.remove(logo_instance)
                    break

            return True
        return False

    def get_all_logos(self):
        """获取所有注册的Logo实例

        Returns:
            list: 所有注册的HealthLogo实例列表
        """
        return self._logo_instances.copy()

    def get_screen_logos(self, screen_name):
        """获取指定屏幕中的所有Logo实例

        Args:
            screen_name: 屏幕名称

        Returns:
            list: 指定屏幕中的所有Logo实例列表
        """
        return self._screen_logos.get(screen_name, []).copy()

    def update_logo_screen_assignments(self):
        """更新Logo的屏幕分配

        这个方法会重新检查所有Logo的屏幕归属，并更新分组。
        主要用于处理初始化时屏幕还未分配的Logo。

        Returns:
            int: 更新的Logo数量
        """
        updated_count = 0

        # 创建新的屏幕分组字典
        new_screen_logos = {}

        # 遍历所有Logo实例
        for logo in list(self._logo_instances):
            # 尝试获取Logo所属的屏幕
            screen = self._find_parent_screen(logo)

            # 如果找到了屏幕
            if screen:
                screen_name = screen.__class__.__name__

                # 初始化屏幕列表（如果不存在）
                if screen_name not in new_screen_logos:
                    new_screen_logos[screen_name] = []

                # 添加Logo到对应屏幕的列表
                new_screen_logos[screen_name].append(logo)
                updated_count += 1

        # 更新屏幕分组字典
        self._screen_logos = new_screen_logos

        return updated_count

    def cleanup_duplicate_logos(self):
        """清理重复的Logo实例

        这个方法会保留每个屏幕中第一个注册的Logo实例，移除其他重复的实例。
        优化版本：只在必要时执行清理，减少不必要的操作。

        Returns:
            int: 移除的Logo实例数量
        """
        import logging
        logger = logging.getLogger(__name__)

        # 首先更新Logo的屏幕分配
        self.update_logo_screen_assignments()

        removed_count = 0

        # 获取应用实例
        from kivy.app import App
        app = App.get_running_app()
        if not app or not hasattr(app, 'root'):
            return 0

        # 获取屏幕管理器
        screen_manager = app.root
        from kivy.uix.screenmanager import ScreenManager
        if not isinstance(screen_manager, ScreenManager):
            return 0

        # 导入HealthLogo类
        from widgets.logo import HealthLogo

        # 遍历所有屏幕，确保每个屏幕只有一个Logo
        for screen_name in screen_manager.screen_names:
            screen = screen_manager.get_screen(screen_name)
            if not screen:
                continue

            # 获取屏幕类名
            screen_class_name = screen.__class__.__name__

            # 查找当前屏幕中的所有Logo
            found_logos = []
            for child in screen.walk():
                if isinstance(child, HealthLogo):
                    found_logos.append(child)

            # 如果没有Logo，跳过
            if not found_logos:
                continue

            # 如果只有一个Logo，确保它被注册
            if len(found_logos) == 1:
                logo = found_logos[0]
                # 确保这个Logo被注册到正确的屏幕
                if screen_class_name not in self._screen_logos or logo not in self._screen_logos[screen_class_name]:
                    # 注册Logo
                    if screen_class_name not in self._screen_logos:
                        self._screen_logos[screen_class_name] = []
                    if logo not in self._screen_logos[screen_class_name]:
                        self._screen_logos[screen_class_name].append(logo)
                    # 确保Logo在全局列表中
                    if logo not in self._logo_instances:
                        self._logo_instances.append(logo)
                continue

            # 如果有多个Logo，只保留第一个
            if len(found_logos) > 1:
                # 保留第一个Logo
                first_logo = found_logos[0]
                # 移除其他Logo
                for logo in found_logos[1:]:
                    if logo.parent:
                        logo.parent.remove_widget(logo)
                        removed_count += 1
                        # 从全局列表中移除
                        if logo in self._logo_instances:
                            self._logo_instances.remove(logo)
                        # 从屏幕Logo列表中移除
                        if screen_class_name in self._screen_logos and logo in self._screen_logos[screen_class_name]:
                            self._screen_logos[screen_class_name].remove(logo)

                # 确保第一个Logo被正确注册
                if screen_class_name not in self._screen_logos:
                    self._screen_logos[screen_class_name] = [first_logo]
                elif first_logo not in self._screen_logos[screen_class_name]:
                    self._screen_logos[screen_class_name] = [first_logo]

                # 只有当有多个重复Logo时才记录日志
                if len(found_logos) > 2:
                    logger.info(f"已清理屏幕 {screen_name} 中的 {len(found_logos)-1} 个重复Logo")

        # 清理没有屏幕归属的Logo
        orphan_logos = [logo for logo in list(self._logo_instances) if not self._find_parent_screen(logo)]
        for logo in orphan_logos:
            if logo.parent:
                logo.parent.remove_widget(logo)
                removed_count += 1
            # 从全局列表中移除
            self._logo_instances.remove(logo)

        if orphan_logos and len(orphan_logos) > 0:
            logger.debug(f"已清理 {len(orphan_logos)} 个无屏幕归属的Logo")

        # 只有当移除了Logo时才记录日志
        if removed_count > 0:
            logger.info(f"Logo清理: 移除了{removed_count}个重复Logo")

        return removed_count

# 不再需要修改HealthLogo类的初始化方法
# 因为我们已经在HealthLogo类中添加了自动注册到LogoManager的功能

# 获取LogoManager实例的便捷函数
def get_logo_manager():
    """获取LogoManager的单例实例"""
    return LogoManager.get_instance()
