import sqlite3
import os
import json
from datetime import datetime

# 数据库路径
db_path = 'c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db'

if not os.path.exists(db_path):
    print(f"数据库文件不存在: {db_path}")
    exit(1)

try:
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    print(f'使用数据库: {db_path}')
    print('='*60)
    
    # 检查assessment_responses表
    print('\n1. assessment_responses表:')
    cursor.execute("SELECT COUNT(*) FROM assessment_responses")
    count = cursor.fetchone()[0]
    print(f'   数据行数: {count}')
    
    if count > 0:
        print('\n   最近的5条记录:')
        cursor.execute("""
            SELECT id, assessment_id, custom_id, total_score, status, created_at
            FROM assessment_responses 
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        records = cursor.fetchall()
        for record in records:
            print(f'     ID: {record[0]}, 评估ID: {record[1]}, 用户ID: {record[2]}, 分数: {record[3]}, 状态: {record[4]}, 创建时间: {record[5]}')
    
    # 检查questionnaire_responses表
    print('\n2. questionnaire_responses表:')
    cursor.execute("SELECT COUNT(*) FROM questionnaire_responses")
    count = cursor.fetchone()[0]
    print(f'   数据行数: {count}')
    
    if count > 0:
        print('\n   最近的5条记录:')
        cursor.execute("""
            SELECT id, questionnaire_id, custom_id, total_score, status, created_at
            FROM questionnaire_responses 
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        records = cursor.fetchall()
        for record in records:
            print(f'     ID: {record[0]}, 问卷ID: {record[1]}, 用户ID: {record[2]}, 分数: {record[3]}, 状态: {record[4]}, 创建时间: {record[5]}')
    
    # 检查assessment_results表
    print('\n3. assessment_results表:')
    cursor.execute("SELECT COUNT(*) FROM assessment_results")
    count = cursor.fetchone()[0]
    print(f'   数据行数: {count}')
    
    # 检查questionnaire_results表
    print('\n4. questionnaire_results表:')
    cursor.execute("SELECT COUNT(*) FROM questionnaire_results")
    count = cursor.fetchone()[0]
    print(f'   数据行数: {count}')
    
    # 检查用户表中是否有测试用户
    print('\n5. 用户信息:')
    cursor.execute("SELECT COUNT(*) FROM users")
    user_count = cursor.fetchone()[0]
    print(f'   用户总数: {user_count}')
    
    if user_count > 0:
        cursor.execute("""
            SELECT custom_id, username, created_at
            FROM users 
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        users = cursor.fetchall()
        print('   最近的5个用户:')
        for user in users:
            print(f'     用户ID: {user[0]}, 用户名: {user[1]}, 创建时间: {user[2]}')
    
    # 检查问卷和评估模板
    print('\n6. 问卷模板:')
    cursor.execute("SELECT COUNT(*) FROM questionnaires")
    q_count = cursor.fetchone()[0]
    print(f'   问卷模板数: {q_count}')
    
    print('\n7. 评估模板:')
    cursor.execute("SELECT COUNT(*) FROM assessments")
    a_count = cursor.fetchone()[0]
    print(f'   评估模板数: {a_count}')
    
    conn.close()
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()