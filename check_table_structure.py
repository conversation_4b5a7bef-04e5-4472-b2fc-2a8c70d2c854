#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def check_assessments_table():
    """检查assessments表结构"""
    conn = sqlite3.connect('YUN/backend/app.db')
    cursor = conn.cursor()
    
    try:
        # 检查assessments表结构
        cursor.execute('PRAGMA table_info(assessments)')
        columns = cursor.fetchall()
        
        print('assessments表完整结构:')
        for col in columns:
            col_name = col[1]
            col_type = col[2]
            not_null = 'NOT NULL' if col[3] else ''
            default_val = f'DEFAULT {col[4]}' if col[4] is not None else ''
            print(f'  {col_name} {col_type} {not_null} {default_val}')
        
        # 检查现有数据
        cursor.execute('SELECT COUNT(*) FROM assessments')
        count = cursor.fetchone()[0]
        print(f'\n现有assessments记录数: {count}')
        
        if count > 0:
            cursor.execute('SELECT * FROM assessments LIMIT 1')
            sample = cursor.fetchone()
            print(f'示例记录: {sample}')
            
    except Exception as e:
        print(f'检查失败: {e}')
    finally:
        conn.close()

if __name__ == '__main__':
    check_assessments_table()