# -*- coding: utf-8 -*-
"""
创建分发数据 - 为用户SM_001添加问卷和评估分发记录
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from app.db.base_session import SessionLocal
from app.models.user import User
from app.models.distribution import QuestionnaireDistribution, AssessmentDistribution
from app.models.questionnaire import QuestionnaireTemplate
from app.models.assessment import AssessmentTemplate

def create_distribution_data():
    """创建分发数据"""
    db: Session = SessionLocal()
    
    try:
        # 查找用户SM_001
        user = db.query(User).filter(User.custom_id == "SM_001").first()
        if not user:
            print("用户SM_001不存在")
            return
        
        print(f"找到用户: {user.custom_id} - {user.full_name}")
        
        # 创建问卷分发记录
        questionnaire_distributions = [
            {
                "title": "健康状况调查问卷",
                "message": "请完成健康状况调查问卷",
                "status": "pending",
                "due_date": datetime.now() + timedelta(days=7)
            },
            {
                "title": "生活方式问卷",
                "message": "请完成生活方式问卷",
                "status": "pending",
                "due_date": datetime.now() + timedelta(days=5)
            },
            {
                "title": "饮食习惯问卷",
                "message": "请完成饮食习惯问卷",
                "status": "pending",
                "due_date": datetime.now() + timedelta(days=3)
            }
        ]
        
        for i, q_dist in enumerate(questionnaire_distributions):
            distribution = QuestionnaireDistribution(
                custom_id=user.custom_id,
                title=q_dist["title"],
                message=q_dist["message"],
                status=q_dist["status"],
                due_date=q_dist["due_date"],
                created_at=datetime.now() - timedelta(days=i),
                updated_at=datetime.now() - timedelta(days=i)
            )
            db.add(distribution)
            print(f"创建问卷分发: {q_dist['title']}")
        
        # 获取用户的评估记录
        assessments = db.query(Assessment).filter(Assessment.custom_id == user.custom_id).all()
        
        # 为每个评估创建分发记录
        for i, assessment in enumerate(assessments):
            distribution = AssessmentDistribution(
                assessment_id=assessment.id,
                custom_id=user.custom_id,
                message=f"请完成{assessment.name}",
                status="pending",
                due_date=datetime.now() + timedelta(days=10-i*2),
                created_at=datetime.now() - timedelta(days=i),
                updated_at=datetime.now() - timedelta(days=i)
            )
            db.add(distribution)
            print(f"创建评估分发: {assessment.name}")
        
        # 提交事务
        db.commit()
        
        # 验证数据
        q_dist_count = db.query(QuestionnaireDistribution).filter(QuestionnaireDistribution.custom_id == user.custom_id).count()
        a_dist_count = db.query(AssessmentDistribution).filter(AssessmentDistribution.custom_id == user.custom_id).count()
        
        print(f"用户 {user.custom_id} 的问卷分发记录数: {q_dist_count}")
        print(f"用户 {user.custom_id} 的评估分发记录数: {a_dist_count}")
        
    except Exception as e:
        print(f"创建分发数据时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_distribution_data()