/**
 * 模拟数据管理器
 * 统一管理所有模拟数据，支持环境控制
 */
import { ENABLE_MOCK_DATA } from '@/config/environment'

// 模拟用户数据
const mockUsers = [
  {
    id: 1,
    custom_id: 'user001',
    username: 'admin',
    name: '管理员',
    email: '<EMAIL>',
    role: 'admin',
    status: 'active',
    created_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 2,
    custom_id: 'user002',
    username: 'doctor1',
    name: '张医生',
    email: '<EMAIL>',
    role: 'doctor',
    status: 'active',
    created_at: '2024-01-02T00:00:00Z'
  },
  {
    id: 3,
    custom_id: 'user003',
    username: 'patient1',
    name: '李患者',
    email: '<EMAIL>',
    role: 'patient',
    status: 'active',
    created_at: '2024-01-03T00:00:00Z'
  }
]

// 模拟检验报告数据
const mockLabReports = [
  {
    id: 1,
    custom_id: 'SM_001',
    test_type: 'blood',
    test_name: '血常规',
    specimen: '静脉血',
    hospital_name: '北京协和医院',
    department: '检验科',
    test_date: '2025-04-15',
    result_date: '2025-04-16',
    is_abnormal: false,
    result_items: [
      { item_name: '白细胞计数', item_value: '6.5', reference_range: '4.0-10.0', unit: '10^9/L', is_abnormal: false },
      { item_name: '红细胞计数', item_value: '4.8', reference_range: '4.0-5.5', unit: '10^12/L', is_abnormal: false },
      { item_name: '血红蛋白', item_value: '145', reference_range: '120-160', unit: 'g/L', is_abnormal: false },
      { item_name: '血小板计数', item_value: '230', reference_range: '100-300', unit: '10^9/L', is_abnormal: false }
    ],
    result_summary: '血常规各项指标均在正常范围内',
    notes: '无特殊情况'
  },
  {
    id: 2,
    custom_id: 'SM_001',
    test_type: 'biochemical',
    test_name: '肝功能',
    specimen: '静脉血',
    hospital_name: '上海第一人民医院',
    department: '检验科',
    test_date: '2025-03-10',
    result_date: '2025-03-11',
    is_abnormal: true,
    result_items: [
      { item_name: 'ALT', item_value: '65', reference_range: '0-40', unit: 'U/L', is_abnormal: true },
      { item_name: 'AST', item_value: '45', reference_range: '0-40', unit: 'U/L', is_abnormal: true },
      { item_name: '总胆红素', item_value: '15', reference_range: '3.4-20.5', unit: 'μmol/L', is_abnormal: false },
      { item_name: '直接胆红素', item_value: '5', reference_range: '0-6.8', unit: 'μmol/L', is_abnormal: false }
    ],
    result_summary: 'ALT和AST轻度升高，建议复查',
    notes: '近期服用过药物，可能影响肝功能指标'
  }
]

// 模拟医疗记录数据
const mockMedicalRecords = [
  {
    id: 1,
    custom_id: 'SM_001',
    record_type: 'outpatient',
    hospital_name: '北京协和医院',
    department: '内科',
    doctor_name: '张医生',
    visit_date: '2025-04-15',
    diagnosis: '上呼吸道感染',
    treatment: '对症治疗，多休息，多饮水',
    prescription: '1. 头孢克洛胶囊 0.25g 每日三次\n2. 布洛芬缓释胶囊 0.3g 必要时服用\n3. 氯雷他定片 10mg 每日一次',
    notes: '症状未缓解请及时复诊',
    is_important: false
  },
  {
    id: 2,
    custom_id: 'SM_001',
    record_type: 'admission',
    hospital_name: '上海第一人民医院',
    department: '外科',
    doctor_name: '李医生',
    visit_date: '2025-03-10',
    diagnosis: '急性阑尾炎',
    treatment: '手术治疗，阑尾切除术',
    prescription: '术后抗生素治疗，详见医嘱单',
    notes: '术后恢复良好，无并发症',
    is_important: true
  },
  {
    id: 3,
    custom_id: 'SM_001',
    record_type: 'discharge',
    hospital_name: '上海第一人民医院',
    department: '外科',
    doctor_name: '李医生',
    visit_date: '2025-03-15',
    diagnosis: '急性阑尾炎术后',
    treatment: '伤口护理，定期复查',
    prescription: '口服抗生素继续7天，详见出院医嘱',
    notes: '两周后门诊复查',
    is_important: true
  }
]

// 模拟仪表盘统计数据
const mockDashboardStats = {
  totalUsers: 1258,
  userGrowth: 5.2,
  newUsers: 48,
  newUserGrowth: 12.7,
  activeUsers: 723,
  activeUserGrowth: -2.3,
  totalDocuments: 3542,
  documentGrowth: 8.1
}

// 模拟系统性能数据
const mockSystemPerformance = {
  cpu_usage: 45.2,
  memory_usage: 68.5,
  disk_usage: 32.1,
  request_rate: 85.3,
  error_rate: 0.8,
  response_time: 125.6
}

// 模拟评估分析数据生成器
const generateMockAssessmentData = (assessment) => {
  const mockData = []
  const now = new Date()
  const questions = assessment.questions || []
  const interpretations = assessment.interpretation || []

  for (let i = 0; i < 50; i++) {
    const date = new Date(now)
    date.setDate(date.getDate() - Math.floor(Math.random() * 90))

    const answers = []
    let totalScore = 0

    questions.forEach((question, index) => {
      const score = Math.floor(Math.random() * 4)
      totalScore += score

      let answer
      if (question.question_type === 'scale') {
        answer = score.toString()
      } else {
        answer = ['从不', '偶尔', '经常', '总是'][score]
      }

      answers.push({
        question: question.question_text,
        answer,
        score
      })
    })

    let result = '未分类'
    if (interpretations.length > 0) {
      for (const interpretation of interpretations) {
        if (totalScore >= interpretation.min_score && totalScore <= interpretation.max_score) {
          result = interpretation.result
          break
        }
      }
    }

    mockData.push({
      id: i + 1,
      user_name: `用户${i + 1}`,
      created_at: date.toISOString().split('T')[0],
      total_score: totalScore,
      result,
      answers
    })
  }

  return mockData
}

// 模拟评估量表数据
const mockAssessments = [
  {
    id: 1,
    name: 'PHQ-9抑郁量表',
    category: '心理评估',
    description: '用于筛查和评估抑郁症状的标准化量表',
    item_count: 9,
    target: '成人',
    scoring_method: '0-3分制',
    created_at: '2024-01-01T00:00:00Z',
    items: [
      {
        question: '做事时提不起劲或没有兴趣',
        options: [
          { label: '完全没有', value: 0, score: 0 },
          { label: '几天', value: 1, score: 1 },
          { label: '一半以上的天数', value: 2, score: 2 },
          { label: '几乎每天', value: 3, score: 3 }
        ]
      },
      {
        question: '感到心情低落、沮丧或绝望',
        options: [
          { label: '完全没有', value: 0, score: 0 },
          { label: '几天', value: 1, score: 1 },
          { label: '一半以上的天数', value: 2, score: 2 },
          { label: '几乎每天', value: 3, score: 3 }
        ]
      }
    ]
  },
  {
    id: 2,
    name: 'GAD-7焦虑量表',
    category: '心理评估',
    description: '用于筛查和评估焦虑症状的标准化量表',
    item_count: 7,
    target: '成人',
    scoring_method: '0-3分制',
    created_at: '2024-01-02T00:00:00Z',
    items: []
  }
]

// 模拟问卷数据
const mockQuestionnaires = [
  {
    id: 1,
    title: '健康生活方式调查',
    description: '了解您的日常生活习惯和健康状况',
    category: '生活方式',
    status: 'active',
    created_at: '2024-01-01T00:00:00Z',
    questions: [
      {
        id: 1,
        type: 'single_choice',
        question: '您每天的睡眠时间是？',
        options: [
          { value: 'less_than_6', label: '少于6小时' },
          { value: '6_to_8', label: '6-8小时' },
          { value: 'more_than_8', label: '超过8小时' }
        ],
        required: true
      },
      {
        id: 2,
        type: 'multiple_choice',
        question: '您经常进行哪些运动？（可多选）',
        options: [
          { value: 'walking', label: '散步' },
          { value: 'running', label: '跑步' },
          { value: 'swimming', label: '游泳' },
          { value: 'cycling', label: '骑行' },
          { value: 'none', label: '不运动' }
        ],
        required: false
      }
    ]
  }
]

// 模拟评估记录数据
const mockAssessmentRecords = [
  {
    id: 1,
    assessment_id: 1,
    assessment_name: 'PHQ-9抑郁量表',
    user_id: 3,
    user_name: '李患者',
    score: 12,
    result: '中度抑郁',
    completed_at: '2024-01-15T10:30:00Z',
    evaluator: '张医生',
    round_number: 1,
    sequence_number: 1,
    notes: '患者配合度良好'
  },
  {
    id: 2,
    assessment_id: 2,
    assessment_name: 'GAD-7焦虑量表',
    user_id: 3,
    user_name: '李患者',
    score: 8,
    result: '轻度焦虑',
    completed_at: '2024-01-16T14:20:00Z',
    evaluator: '张医生',
    round_number: 1,
    sequence_number: 1,
    notes: '建议定期复查'
  }
]

// 模拟健康记录数据
const mockHealthRecords = [
  {
    id: 1,
    user_id: 3,
    type: 'vital_signs',
    data: {
      blood_pressure: '120/80',
      heart_rate: 72,
      temperature: 36.5,
      weight: 65.5
    },
    recorded_at: '2024-01-15T09:00:00Z',
    notes: '正常范围'
  }
]

/**
 * 模拟数据管理器类
 */
class MockDataManager {
  constructor() {
    this.enabled = ENABLE_MOCK_DATA
    this.data = {
      users: mockUsers,
      assessments: mockAssessments,
      questionnaires: mockQuestionnaires,
      assessmentRecords: mockAssessmentRecords,
      healthRecords: mockHealthRecords,
      labReports: mockLabReports,
      medicalRecords: mockMedicalRecords,
      dashboardStats: mockDashboardStats,
      systemPerformance: mockSystemPerformance
    }
  }

  /**
   * 检查是否启用模拟数据
   */
  isEnabled() {
    return this.enabled
  }

  /**
   * 获取模拟数据
   * @param {string} type 数据类型
   * @param {Object} filters 过滤条件
   */
  getData(type, filters = {}) {
    if (!this.enabled) {
      return []
    }

    let data = this.data[type] || []
    
    // 应用过滤条件
    if (Object.keys(filters).length > 0) {
      data = data.filter(item => {
        return Object.entries(filters).every(([key, value]) => {
          return item[key] === value
        })
      })
    }

    return data
  }

  /**
   * 获取单个模拟数据项
   * @param {string} type 数据类型
   * @param {number|string} id 数据ID
   */
  getItem(type, id) {
    if (!this.enabled) {
      return null
    }

    const data = this.data[type] || []
    return data.find(item => item.id == id)
  }

  /**
   * 添加模拟数据
   * @param {string} type 数据类型
   * @param {Object} item 数据项
   */
  addItem(type, item) {
    if (!this.enabled) {
      return false
    }

    if (!this.data[type]) {
      this.data[type] = []
    }

    const newItem = {
      ...item,
      id: this.generateId(type),
      created_at: new Date().toISOString()
    }

    this.data[type].push(newItem)
    return newItem
  }

  /**
   * 更新模拟数据
   * @param {string} type 数据类型
   * @param {number|string} id 数据ID
   * @param {Object} updates 更新数据
   */
  updateItem(type, id, updates) {
    if (!this.enabled) {
      return false
    }

    const data = this.data[type] || []
    const index = data.findIndex(item => item.id == id)
    
    if (index !== -1) {
      this.data[type][index] = {
        ...this.data[type][index],
        ...updates,
        updated_at: new Date().toISOString()
      }
      return this.data[type][index]
    }

    return null
  }

  /**
   * 删除模拟数据
   * @param {string} type 数据类型
   * @param {number|string} id 数据ID
   */
  deleteItem(type, id) {
    if (!this.enabled) {
      return false
    }

    const data = this.data[type] || []
    const index = data.findIndex(item => item.id == id)
    
    if (index !== -1) {
      this.data[type].splice(index, 1)
      return true
    }

    return false
  }

  /**
   * 生成新的ID
   * @param {string} type 数据类型
   */
  generateId(type) {
    const data = this.data[type] || []
    const maxId = data.reduce((max, item) => Math.max(max, item.id || 0), 0)
    return maxId + 1
  }

  /**
   * 重置所有模拟数据
   */
  reset() {
    this.data = {
      users: [...mockUsers],
      assessments: [...mockAssessments],
      questionnaires: [...mockQuestionnaires],
      assessmentRecords: [...mockAssessmentRecords],
      healthRecords: [...mockHealthRecords],
      labReports: [...mockLabReports],
      medicalRecords: [...mockMedicalRecords],
      dashboardStats: { ...mockDashboardStats },
      systemPerformance: { ...mockSystemPerformance }
    }
  }

  /**
   * 启用/禁用模拟数据
   * @param {boolean} enabled 是否启用
   */
  setEnabled(enabled) {
    this.enabled = enabled
  }
}

// 创建单例实例
const mockDataManager = new MockDataManager()

export default mockDataManager

// 导出便捷方法
export const getMockData = (type, filters) => mockDataManager.getData(type, filters)
export const getMockItem = (type, id) => mockDataManager.getItem(type, id)
export const isMockEnabled = () => mockDataManager.isEnabled()
export const generateMockAssessmentAnalysisData = generateMockAssessmentData