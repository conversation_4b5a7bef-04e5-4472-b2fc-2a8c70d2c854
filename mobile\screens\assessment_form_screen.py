# -*- coding: utf-8 -*-
import json
import logging
import threading
from datetime import datetime

from kivy.clock import Clock
from kivy.metrics import dp
from kivy.properties import StringProperty, ListProperty, ObjectProperty, BooleanProperty, NumericProperty
from kivy.uix.scrollview import ScrollView

from kivymd.app import MDApp
from kivymd.uix.boxlayout import MDBox<PERSON>ayout
from kivymd.uix.button import MDButton, MDButtonText
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
from kivymd.uix.textfield import MDTextField
from kivymd.uix.selectioncontrol import MDCheckbox
from kivymd.uix.slider import MD<PERSON>lider

from screens.base_screen import BaseScreen
from utils.cloud_api import get_cloud_api
from utils.user_manager import get_user_manager
from kivy.factory import Factory
from theme import AppTheme, AppMetrics

# 获取日志记录器
logger = logging.getLogger(__name__)

class AssessmentFormScreen(BaseScreen):
    """评估量表表单屏幕"""
    title = StringProperty("评估量表")
    questions = ListProperty([])
    answers = ListProperty([])
    current_assessment = ObjectProperty(None)
    is_submitting = BooleanProperty(False)
    progress = NumericProperty(0)  # 完成进度，0-100

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.app = MDApp.get_running_app()
        self.answers = []
        # 延迟初始化，确保KV文件已加载
        Clock.schedule_once(self.init_ui, 0.5)

    def on_enter(self):
        """进入屏幕时调用，每次都重新初始化"""
        logger.info("进入评估量表表单页面")
        # 每次进入页面都重新初始化UI，确保获取最新的assessment_to_fill数据
        Clock.schedule_once(self.init_ui, 0.1)
    
    def init_ui(self, dt=None):
        """初始化UI"""
        try:
            logger.info("初始化评估量表表单UI")
            # 获取当前评估量表数据
            self.current_assessment = getattr(self.app, 'assessment_to_fill', None)
            
            logger.info(f"获取到的assessment_to_fill数据: {self.current_assessment}")
            
            if not self.current_assessment:
                logger.error("未找到评估量表数据")
                self.show_error("未找到评估量表数据")
                # 延迟执行返回操作，避免在UI初始化过程中立即跳转
                Clock.schedule_once(lambda dt: self.go_back(), 1.0)
                return
            
            # 设置标题
            assessment_title = self.current_assessment.get('title', '')
            if not assessment_title and 'template' in self.current_assessment and 'name' in self.current_assessment['template']:
                assessment_title = self.current_assessment['template']['name']
            
            if assessment_title:
                self.title = assessment_title
                logger.info(f"设置评估量表标题: {assessment_title}")
            
            # 获取问题列表
            self.questions = []
            logger.info(f"评估量表数据结构: {list(self.current_assessment.keys()) if self.current_assessment else 'None'}")
            logger.info(f"完整的评估量表数据: {self.current_assessment}")
            
            if 'template' in self.current_assessment and 'questions' in self.current_assessment['template']:
                self.questions = self.current_assessment['template']['questions']
                logger.info(f"从模板获取问题列表，共 {len(self.questions)} 个问题")
                if self.questions:
                    logger.info(f"第一个问题示例: {self.questions[0] if len(self.questions) > 0 else 'None'}")
                else:
                    logger.warning("模板中的问题列表为空")
            elif 'questions' in self.current_assessment:
                self.questions = self.current_assessment['questions']
                logger.info(f"从评估量表获取问题列表，共 {len(self.questions)} 个问题")
                if self.questions:
                    logger.info(f"第一个问题示例: {self.questions[0] if len(self.questions) > 0 else 'None'}")
                else:
                    logger.warning("评估量表中的问题列表为空")
            else:
                # 尝试从API获取问题列表
                try:
                    assessment_id = self.current_assessment.get('id')
                    if assessment_id:
                        logger.info(f"尝试从API获取评估量表问题，ID: {assessment_id}")
                        cloud_api = get_cloud_api()
                        if cloud_api.is_authenticated():
                            result = cloud_api.get_assessment_questions(assessment_id)
                            if result and 'questions' in result:
                                self.questions = result['questions']
                                logger.info(f"从API获取问题列表，共 {len(self.questions)} 个问题")
                                if self.questions:
                                    logger.info(f"API返回的第一个问题示例: {self.questions[0] if len(self.questions) > 0 else 'None'}")
                except Exception as e:
                    logger.error(f"从API获取问题列表失败: {e}")
                
                if not self.questions:
                    logger.warning("未找到问题列表，所有获取方式都失败")
                    logger.info("尝试创建示例问题用于测试")
                    # 创建一些示例问题用于测试
                    self.questions = [
                        {
                            'id': 1,
                            'text': '在过去的两周内，您感到沮丧、抑郁或绝望的频率如何？',
                            'question_text': '在过去的两周内，您感到沮丧、抑郁或绝望的频率如何？',
                            'type': 'radio',
                            'question_type': 'radio',
                            'options': [
                                {'value': '0', 'label': '完全没有'},
                                {'value': '1', 'label': '几天'},
                                {'value': '2', 'label': '一半以上的天数'},
                                {'value': '3', 'label': '几乎每天'}
                            ]
                        },
                        {
                            'id': 2,
                            'text': '在过去的两周内，您对做事情失去兴趣或乐趣的频率如何？',
                            'question_text': '在过去的两周内，您对做事情失去兴趣或乐趣的频率如何？',
                            'type': 'radio',
                            'question_type': 'radio',
                            'options': [
                                {'value': '0', 'label': '完全没有'},
                                {'value': '1', 'label': '几天'},
                                {'value': '2', 'label': '一半以上的天数'},
                                {'value': '3', 'label': '几乎每天'}
                            ]
                        }
                    ]
                    logger.info(f"创建了 {len(self.questions)} 个示例问题")
            
            if not self.questions:
                logger.error("问题列表仍然为空，这不应该发生")
                self.show_empty_state()
                return
            
            logger.info(f"最终获得的问题数量: {len(self.questions)}")
            for i, q in enumerate(self.questions[:3]):  # 只显示前3个问题的详情
                logger.info(f"问题 {i+1}: {q}")
            
            # 初始化答案列表
            self.answers = [None] * len(self.questions)
            
            # 创建问题表单
            logger.info("开始创建问题表单")
            self.create_question_form()
            logger.info("问题表单创建完成")
        except Exception as e:
            logger.error(f"初始化评估量表表单时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error(f"初始化表单时出错: {str(e)}")
    
    def show_empty_state(self):
        """显示空状态"""
        try:
            # 检查form_container是否存在
            if not hasattr(self, 'ids') or 'form_container' not in self.ids:
                logger.error("form_container不存在，无法显示空状态")
                logger.info(f"当前ids: {list(self.ids.keys()) if hasattr(self, 'ids') else 'None'}")
                # 延迟重试
                Clock.schedule_once(lambda dt: self.show_empty_state(), 0.5)
                return
            
            # 清空现有内容
            self.ids.form_container.clear_widgets()
            
            # 添加空状态提示
            from kivymd.uix.label import MDLabel
            empty_label = MDLabel(
                text="暂无问题数据\n请检查评估量表配置",
                halign="center",
                theme_text_color="Secondary",
                font_style="Body",
                role="medium",
                size_hint_y=None,
                height=dp(100)
            )
            
            self.ids.form_container.add_widget(empty_label)
            logger.info("已显示空状态提示")
        except Exception as e:
            logger.error(f"显示空状态时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def create_question_form(self):
        """创建问题表单"""
        try:
            logger.info(f"开始创建问题表单，问题数量: {len(self.questions)}")
            
            # 调试信息：打印问题和选项的详细信息
            self.debug_print_questions()
            
            # 检查form_container是否存在
            if not hasattr(self, 'ids') or 'form_container' not in self.ids:
                logger.error("form_container不存在，UI可能未正确初始化")
                logger.info(f"当前ids: {list(self.ids.keys()) if hasattr(self, 'ids') else 'None'}")
                # 延迟重试
                Clock.schedule_once(lambda dt: self.create_question_form(), 0.5)
                return
            
            # 清空现有内容
            try:
                self.ids.form_container.clear_widgets()
                logger.info("已清空现有表单内容")
            except Exception as clear_error:
                logger.error(f"清空表单内容时出错: {clear_error}")
                # 如果清空失败，尝试重新获取form_container
                Clock.schedule_once(lambda dt: self.create_question_form(), 0.5)
                return
            
            if not self.questions:
                logger.warning("问题列表为空，显示空状态")
                # 显示空状态
                empty_label = MDLabel(
                    text="暂无问题数据",
                    theme_text_color="Secondary",
                    halign="center",
                    size_hint_y=None,
                    height=dp(100)
                )
                self.ids.form_container.add_widget(empty_label)
                return
            
            # 直接使用KV文件中定义的form_container
            form_container = self.ids.form_container
            logger.info("使用KV文件中定义的表单容器")
            
            # 添加评估描述
            self.add_assessment_description(form_container)
            logger.info("已添加评估描述")
            
            # 进度指示器已在KV文件中定义，无需重复添加
            logger.info("使用KV文件中的进度指示器")
            
            # 添加问题卡片
            for i, question in enumerate(self.questions):
                logger.info(f"创建第 {i+1} 个问题卡片")
                question_card = self.create_question_card(i, question)
                if question_card:
                    form_container.add_widget(question_card)
                    logger.info(f"第 {i+1} 个问题卡片已添加")
                else:
                    logger.error(f"第 {i+1} 个问题卡片创建失败")
                
                # 在问题之间添加间距（除了最后一个问题）
                if i < len(self.questions) - 1:
                    spacer = MDBoxLayout(
                        size_hint_y=None,
                        height=dp(16)  # 增加间距使布局更舒适
                    )
                    form_container.add_widget(spacer)
            
            # 添加提交按钮
            self.add_submit_button(form_container)
            logger.info("已添加提交按钮")
            
            logger.info("问题表单创建完成")
            
        except Exception as e:
            logger.error(f"创建问题表单时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error(f"创建表单时出错: {str(e)}")

    def debug_print_questions(self):
        """调试函数：打印问题和选项的详细信息"""
        try:
            logger.info("===== 调试信息：问题和选项详细信息 =====")
            for i, question in enumerate(self.questions):
                # 获取问题类型
                question_type = question.get('question_type', question.get('type', 'unknown'))
                # 获取问题文本
                question_text = question.get('question_text', question.get('text', 'no text'))
                # 获取选项
                options = question.get('options', [])
                
                logger.info(f"问题 {i+1}:")
                logger.info(f"  - ID: {question.get('id', 'no id')}")
                logger.info(f"  - 类型: {question_type}")
                logger.info(f"  - 文本: {question_text}")
                logger.info(f"  - 选项数量: {len(options)}")
                
                # 打印选项详情
                if options:
                    logger.info(f"  - 选项详情:")
                    for j, option in enumerate(options):
                        if isinstance(option, dict):
                            option_value = option.get('value', 'no value')
                            option_label = option.get('label', option.get('text', 'no label'))
                            logger.info(f"    * 选项 {j+1}: 值={option_value}, 标签={option_label}")
                        else:
                            logger.info(f"    * 选项 {j+1}: {option}")
            
            logger.info("========================================")
        except Exception as e:
            logger.error(f"调试打印问题信息时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def add_progress_indicator(self, container):
        """添加进度指示器"""
        progress_card = MDCard(
            orientation="vertical",
            size_hint_y=None,
            height=dp(80),
            padding=[dp(20), dp(16)],
            radius=[dp(12)],
            elevation=1,
            md_bg_color=self.app.theme.PRIMARY_LIGHT,
            shadow_softness=4
        )
        
        progress_label = MDLabel(
            text=f"完成进度: {self.progress:.0f}%",
            theme_text_color="Custom",
            text_color=self.app.theme.PRIMARY_COLOR,
            font_size=AppMetrics.FONT_SIZE_SMALL,
            halign="center"
        )
        
        progress_card.add_widget(progress_label)
        container.add_widget(progress_card)
        
        # 添加间距
        spacer = MDBoxLayout(
            size_hint_y=None,
            height=dp(16)
        )
        container.add_widget(spacer)

    def create_question_card(self, index, question):
        """创建问题卡片"""
        try:
            # 获取问题数据 - 优先使用question_text字段，其次使用text字段
            question_text = question.get('question_text', question.get('text', ''))
            # 如果问题文本为空，则使用默认文本，但不包含序号
            if not question_text:
                question_text = f'未设置问题内容'
            question_type = question.get('question_type', question.get('type', 'text'))
            options = question.get('options', [])
            required = question.get('required', question.get('is_required', True))
            
            # 创建卡片 - 使用自适应高度
            # 在KivyMD 2.0.1中不允许height=None，使用最小高度并在添加内容后更新
            card = MDCard(
                orientation="vertical",
                size_hint_y=None,
                height=dp(100),  # 设置初始最小高度，后续会根据内容调整
                padding=[dp(16), dp(12)],  # 增加内边距避免紧凑
                radius=[dp(6)],  # 更小的圆角
                elevation=0.5,  # 更轻的阴影
                md_bg_color=self.app.theme.CARD_BACKGROUND,
                shadow_softness=2,
                line_color=self.app.theme.BORDER_COLOR,
                line_width=0.5
            )
            
            # 问题标题 - 采用更紧凑的布局
            title_container = MDBoxLayout(
                orientation="horizontal",
                size_hint_y=None,
                height=dp(40),  # 增加高度避免重叠
                spacing=dp(8)  # 增加间距
            )
            
            # 问题序号 - 更突出的样式
            number_label = MDLabel(
                text=f"{index + 1}.",
                theme_text_color="Custom",
                text_color=self.app.theme.PRIMARY_COLOR,
                size_hint_x=None,
                width=dp(32),  # 稍微增加宽度
                font_size=dp(14),  # 使用固定字体大小
                bold=True,
                halign="left",
                valign="center"
            )
            title_container.add_widget(number_label)
            
            # 问题文本 - 简化布局，确保正确显示
            question_label = MDLabel(
                text=question_text + (" *" if required else ""),
                theme_text_color="Custom",
                text_color=[0.13, 0.13, 0.13, 1],  # 深灰色文字，确保在白色背景上清晰可见
                font_size=dp(13),  # 稍微增加字体大小提高可读性
                text_size=(None, None),
                markup=True,
                valign="center"
            )
            # 绑定宽度变化以自动调整文本大小
            question_label.bind(width=lambda instance, width: setattr(instance, 'text_size', (width - dp(16), None)))
            title_container.add_widget(question_label)
            
            card.add_widget(title_container)
            
            # 添加分隔线
            separator = MDBoxLayout(
                size_hint_y=None,
                height=dp(1),
                md_bg_color=self.app.theme.BORDER_COLOR
            )
            card.add_widget(separator)
            
            # 添加间距
            spacer = MDBoxLayout(
                size_hint_y=None,
                height=dp(6)  # 减小间距
            )
            card.add_widget(spacer)
            
            # 根据问题类型添加不同的输入控件
            input_container = self.create_input_widget(index, question_type, options, question)
            card.add_widget(input_container)
            
            # 添加完所有内容后，计算并更新卡片高度
            def update_card_height(dt):
                # 首先更新输入容器的高度
                input_height = 0
                for child in input_container.children:
                    if hasattr(child, 'height'):
                        input_height += child.height
                    if hasattr(child, 'padding'):
                        if isinstance(child.padding, list) and len(child.padding) >= 2:
                            input_height += child.padding[1] * 2
                
                # 添加间距
                input_height += input_container.spacing * (len(input_container.children) - 1 if len(input_container.children) > 0 else 0)
                
                # 设置输入容器的高度
                input_container.height = max(dp(50), input_height + dp(10))
                
                # 然后计算卡片的总高度
                total_height = 0
                for child in card.children:
                    if hasattr(child, 'height'):
                        total_height += child.height
                    if hasattr(child, 'padding'):
                        if isinstance(child.padding, list) and len(child.padding) >= 2:
                            total_height += child.padding[1] * 2
                
                # 添加卡片间距
                total_height += card.padding[1] * 2 if isinstance(card.padding, list) and len(card.padding) >= 2 else 0
                
                # 设置最小高度
                card.height = max(dp(100), total_height + dp(20))
            
            # 使用Clock延迟调用，确保子组件已完全加载
            from kivy.clock import Clock
            Clock.schedule_once(update_card_height, 0.1)
            
            return card
            
        except Exception as e:
            logger.error(f"创建问题卡片时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            
            # 返回一个错误卡片
            error_card = MDCard(
                orientation="vertical",
                size_hint_y=None,
                height=dp(80),
                padding=[dp(16), dp(12)],
                radius=[dp(8)],
                elevation=1,
                md_bg_color=self.app.theme.ERROR_COLOR
            )
            
            error_label = MDLabel(
                text=f"问题 {index + 1} 加载失败",
                theme_text_color="Custom",
                text_color=self.app.theme.TEXT_LIGHT,
                halign="center",
                font_size=AppMetrics.FONT_SIZE_SMALL
            )
            error_card.add_widget(error_label)
            return error_card

    def create_input_widget(self, index, question_type, options, question):
        container = MDBoxLayout(
            orientation="vertical",
            spacing=dp(8),
            size_hint_y=None,
            height=dp(50)
        )
        # 题目active/inactive逻辑
        is_active = question.get('active', True)
        container.disabled = not is_active
        container.opacity = 1.0 if is_active else 0.3
        # --- 题型扩展 ---
        if question_type in ['likert']:
            # Likert量表横向渲染
            from kivymd.uix.button import MDButton
            likert_row = MDBoxLayout(orientation="horizontal", spacing=dp(8), size_hint_y=None, height=dp(48))
            for i, option in enumerate(options):
                btn = MDButton(text=option.get('label', str(option)), md_bg_color="#0D47A1", theme_text_color="Custom", text_color="#fff", size_hint=(None, None), width=dp(60), height=dp(36), style="elevated")
                btn.question_index = index
                btn.option_index = i
                btn.option_value = option.get('value', i)
                btn.bind(on_release=lambda x: self.on_radio_answer(x, True))
                likert_row.add_widget(btn)
            container.add_widget(likert_row)
        elif question_type in ['semantic_differential']:
            # 语义差异量表
            from kivymd.uix.slider import MDSlider
            sd_row = MDBoxLayout(orientation="horizontal", spacing=dp(8), size_hint_y=None, height=dp(48))
            left = options[0].get('label', '极低') if options else '极低'
            right = options[-1].get('label', '极高') if options else '极高'
            sd_row.add_widget(MDLabel(text=left, size_hint_x=None, width=dp(40)))
            slider = MDSlider(min=0, max=len(options)-1, value=0, step=1, size_hint_x=1)
            slider.question_index = index
            slider.bind(value=lambda inst, val: self.on_slider_answer(inst, int(val)))
            sd_row.add_widget(slider)
            sd_row.add_widget(MDLabel(text=right, size_hint_x=None, width=dp(40)))
            container.add_widget(sd_row)
        elif question_type in ['ranking']:
            # 排序题（简单实现：输入框输入排序）
            for i, option in enumerate(options):
                row = MDBoxLayout(orientation="horizontal", spacing=dp(8), size_hint_y=None, height=dp(36))
                row.add_widget(MDLabel(text=option.get('label', str(option)), size_hint_x=0.7))
                tf = MDTextField(hint_text="排序", input_filter="int", size_hint_x=0.3)
                tf.question_index = index
                tf.option_index = i
                tf.bind(text=lambda x, t, idx=index, opt=i: self.on_ranking_answer(idx, opt, t))
                row.add_widget(tf)
                container.add_widget(row)
        elif question_type in ['time']:
            # 时间选择
            from kivymd.uix.picker import MDTimePicker
            def show_picker(instance):
                picker = MDTimePicker()
                picker.bind(time=lambda p, t: self.on_time_answer(instance, t))
                picker.open()
            btn = MDButton(text="选择时间", md_bg_color="#0D47A1", theme_text_color="Custom", text_color="#fff", style="elevated")
            btn.question_index = index
            btn.bind(on_release=show_picker)
            container.add_widget(btn)
        elif question_type in ['short_answer', 'long_answer']:
            tf = MDTextField(hint_text="请输入答案", multiline=(question_type=='long_answer'), size_hint_y=None, height=dp(80) if question_type=='long_answer' else dp(48))
            tf.question_index = index
            tf.bind(text=lambda x, t: self.on_text_answer(x, t))
            container.add_widget(tf)
        elif question_type in ['matrix']:
            # 矩阵题（简单表格实现）
            from kivymd.uix.datatables import MDDataTable
            cols = [(col.get('label', str(col)), dp(60)) for col in question.get('columns', [])]
            rows = [[row.get('label', str(row))] + ['' for _ in cols] for row in question.get('rows', [])]
            # 这里只做静态展示，实际可扩展为可编辑表格
            table = MDDataTable(column_data=cols, row_data=rows, size_hint_y=None, height=dp(120))
            container.add_widget(table)
        elif question_type in ['branching']:
            # 分支题，复用单选/多选逻辑，增加分支处理
            # 以单选为例
            radio_container = MDBoxLayout(orientation="vertical", spacing=dp(2), size_hint_y=None)
            for i, option in enumerate(options):
                row = MDBoxLayout(orientation="horizontal", size_hint_y=None, height=dp(36), spacing=dp(12))
                label = MDLabel(text=option.get('label', str(option)), size_hint_x=0.7)
                radio_btn = MDCheckbox(group=f"question_{index}", size_hint=(None, None), size=(dp(24), dp(24)), pos_hint={'center_y': 0.5}, active=False)
                radio_btn.question_index = index
                radio_btn.option_index = i
                radio_btn.option_value = option.get('value', i)
                radio_btn.branch_targets = option.get('branch_targets', None)
                radio_btn.bind(active=self.on_branching_answer)
                row.add_widget(label)
                row.add_widget(radio_btn)
                radio_container.add_widget(row)
            container.add_widget(radio_container)
        elif question_type in ['numeric_rating']:
            from kivymd.uix.slider import MDSlider
            slider = MDSlider(min=0, max=10, value=5, step=1, color="#0D47A1", size_hint_y=None, height=dp(32))
            slider.question_index = index
            slider.bind(value=lambda inst, val: self.on_slider_answer(inst, int(val)))
            container.add_widget(slider)
        elif question_type in ['fill_blank']:
            tf = MDTextField(hint_text="请填写", size_hint_y=None, height=dp(48))
            tf.question_index = index
            tf.bind(text=lambda x, t: self.on_text_answer(x, t))
            container.add_widget(tf)
        # 文本输入
        elif question_type in ['text', 'textarea']:
            text_field = MDTextField(
                hint_text="请输入答案",
                mode="outlined",
                size_hint_y=None,
                height=dp(80) if question_type == 'textarea' else dp(48),
                font_size=AppMetrics.FONT_SIZE_SMALL,
                line_color_normal=self.app.theme.BORDER_COLOR,
                line_color_focus=self.app.theme.PRIMARY_COLOR,
                multiline=(question_type == 'textarea')
            )
            text_field.question_index = index
            text_field.bind(text=lambda x, text: self.on_text_answer(x, text))
            container.height = text_field.height
            container.add_widget(text_field)
        # 数字输入
        elif question_type == 'number':
            number_field = MDTextField(
                hint_text="请输入数字",
                mode="outlined",
                input_filter="int",
                size_hint_y=None,
                height=dp(48),
                font_size=AppMetrics.FONT_SIZE_SMALL,
                line_color_normal=self.app.theme.BORDER_COLOR,
                line_color_focus=self.app.theme.PRIMARY_COLOR,
                multiline=False
            )
            number_field.question_index = index
            number_field.bind(text=lambda x, text: self.on_text_answer(x, text))
            container.height = number_field.height
            container.add_widget(number_field)
        # 单选
        elif question_type in ['radio', 'single_choice']:
            # 单选题 - 采用类似SDS量表的紧凑表格样式
            radio_container = MDBoxLayout(
                orientation="vertical",
                spacing=dp(2),  # 更紧凑的间距
                size_hint_y=None
            )
            
            if options:
                # 创建表格样式的选项布局
                table_container = MDBoxLayout(
                    orientation="vertical",
                    spacing=dp(1),
                    size_hint_y=None,
                    padding=[dp(8), dp(4)]
                )
                
                for i, option in enumerate(options):
                    option_row = MDBoxLayout(
                        orientation="horizontal",
                        size_hint_y=None,
                        height=dp(36),  # 稍微增加高度以适应表格样式
                        spacing=dp(12),
                        padding=[dp(4), dp(2)]
                    )
                    
                    # 选项序号/标识
                    option_value = option.get('value', str(i)) if isinstance(option, dict) else str(i)
                    option_id = MDLabel(
                        text=f"({option_value})",
                        theme_text_color="Custom",
                        text_color=self.app.theme.PRIMARY_COLOR,
                        font_size=dp(12),  # 使用固定字体大小
                        size_hint_x=None,
                        width=dp(40),
                        halign="center",
                        valign="center",
                        bold=True
                    )
                    option_row.add_widget(option_id)
                    
                    # 选项文本 - 修复选项内容显示问题，优先使用label字段
                    if isinstance(option, dict):
                        option_text = option.get('label', option.get('text', option.get('name', f'选项 {i+1}')))
                    elif isinstance(option, str):
                        option_text = option
                    else:
                        option_text = str(option)
                    
                    # 确保选项文本不为空
                    if not option_text or option_text.strip() == '':
                        option_text = f'选项 {i+1}'
                    
                    option_label = MDLabel(
                        text=option_text,
                        theme_text_color="Custom",
                        text_color=[0.13, 0.13, 0.13, 1],  # 深灰色文字
                        font_size=dp(12),  # 增加选项字体大小提高可读性
                        text_size=(None, None),
                        valign="center"
                    )
                    # 绑定宽度变化以自动调整文本大小
                    option_label.bind(width=lambda instance, width: setattr(instance, 'text_size', (width - dp(16), None)))
                    option_row.add_widget(option_label)
                    
                    # 单选按钮放在右侧
                    radio_btn = MDCheckbox(
                        group=f"question_{index}",
                        size_hint=(None, None),
                        size=(dp(24), dp(24)),
                        pos_hint={'center_y': 0.5},
                        active=False,
                        color_inactive=self.app.theme.BORDER_COLOR,
                        color_active=self.app.theme.PRIMARY_COLOR,
                        disabled_color=self.app.theme.BORDER_COLOR
                    )
                    # 设置radio按钮的属性
                    radio_btn.question_index = index
                    radio_btn.option_index = i
                    radio_btn.option_value = option.get('value', i)
                    radio_btn.bind(active=self.on_radio_answer)
                    
                    radio_container_widget = MDBoxLayout(
                        size_hint_x=None,
                        width=dp(40),
                        orientation="horizontal"
                    )
                    radio_container_widget.add_widget(radio_btn)
                    option_row.add_widget(radio_container_widget)
                    
                    # 添加分隔线效果（除了最后一行）
                    if i < len(options) - 1:
                        option_row.md_bg_color = self.app.theme.SURFACE_COLOR if i % 2 == 0 else self.app.theme.CARD_BACKGROUND
                    
                    table_container.add_widget(option_row)
                
                # 添加边框效果
                border_card = MDCard(
                    orientation="vertical",
                    size_hint_y=None,
                    height=len(options) * dp(36) + dp(16),
                    padding=[dp(4), dp(4)],
                    radius=[dp(6)],
                    elevation=0,
                    md_bg_color=self.app.theme.SURFACE_COLOR,
                    line_color=self.app.theme.BORDER_COLOR,
                    line_width=1
                )
                
                table_container.height = len(options) * dp(36)
                border_card.add_widget(table_container)
                radio_container.add_widget(border_card)
                
                container_height = len(options) * dp(36) + dp(24)
            else:
                # 没有选项时显示提示
                no_options_label = MDLabel(
                    text="暂无选项",
                    theme_text_color="Secondary",
                    font_size=AppMetrics.FONT_SIZE_SMALL,
                    halign="center",
                    size_hint_y=None,
                    height=dp(32)
                )
                radio_container.add_widget(no_options_label)
                container_height = dp(32)
            
            radio_container.height = container_height
            container.height = container_height
            container.add_widget(radio_container)
            
        # 多选
        elif question_type in ['checkbox', 'multiple_choice']:
            checkbox_container = MDBoxLayout(
                orientation="vertical",
                spacing=dp(6),
                size_hint_y=None
            )
            if options:
                for i, option in enumerate(options):
                    option_container = MDBoxLayout(
                        orientation="horizontal",
                        size_hint_y=None,
                        height=dp(36),
                        spacing=dp(8)
                    )
                    checkbox = MDCheckbox(
                        size_hint=(None, None),
                        size=(dp(20), dp(20)),
                        pos_hint={'center_y': 0.5},
                        active=False,
                        color_inactive=self.app.theme.BORDER_COLOR,
                        color_active=self.app.theme.PRIMARY_COLOR,
                        disabled_color=self.app.theme.BORDER_COLOR
                    )
                    checkbox.question_index = index
                    checkbox.option_index = i
                    checkbox.option_value = option.get('value', i) if isinstance(option, dict) else i
                    checkbox.bind(active=lambda x, active, opt_idx=i: self.on_checkbox_answer(x, active))
                    option_container.add_widget(checkbox)
                    option_text = option.get('label', option.get('text', f'选项 {i+1}')) if isinstance(option, dict) else str(option)
                    option_label = MDLabel(
                        text=option_text,
                        theme_text_color="Custom",
                        text_color=[0.13, 0.13, 0.13, 1],
                        font_size=dp(12),
                        size_hint_y=None,
                        height=dp(36),
                        text_size=(None, None),
                        valign="center"
                    )
                    option_container.add_widget(option_label)
                    checkbox_container.add_widget(option_container)
                container_height = len(options) * dp(36) + (len(options) - 1) * dp(6)
            else:
                no_options_label = MDLabel(
                    text="暂无选项",
                    theme_text_color="Secondary",
                    font_size=AppMetrics.FONT_SIZE_SMALL,
                    halign="center",
                    size_hint_y=None,
                    height=dp(32)
                )
                checkbox_container.add_widget(no_options_label)
                container_height = dp(32)
            checkbox_container.height = container_height
            container.height = container_height
            container.add_widget(checkbox_container)
        # 评分
        elif question_type == 'rating':
            from kivymd.uix.slider import MDSlider
            rating_container = MDBoxLayout(
                orientation="vertical",
                spacing=dp(4),
                size_hint_y=None,
                height=dp(60)
            )
            label = MDLabel(
                text="请选择评分",
                theme_text_color="Secondary",
                font_size=AppMetrics.FONT_SIZE_SMALL,
                halign="left"
            )
            rating_slider = MDSlider(
                min=1,
                max=5,
                value=1,
                step=1,
                size_hint_y=None,
                height=dp(32)
            )
            rating_slider.question_index = index
            def on_rating_value(instance, value):
                self.on_slider_answer(instance, int(value))
            rating_slider.bind(value=on_rating_value)
            rating_container.add_widget(label)
            rating_container.add_widget(rating_slider)
            container.height = dp(60)
            container.add_widget(rating_container)
        # 其他类型
        else:
            unknown_label = MDLabel(
                text=f"不支持的问题类型: {question_type}",
                theme_text_color="Error",
                font_size=AppMetrics.FONT_SIZE_SMALL,
                halign="center",
                size_hint_y=None,
                height=dp(32)
            )
            container.height = dp(32)
            container.add_widget(unknown_label)
        return container

    def add_assessment_description(self, container):
        """添加评估量表描述和说明"""
        try:
            description = ""
            instructions = ""
            
            if self.current_assessment:
                # 尝试从不同位置获取描述
                description = (
                    self.current_assessment.get('description') or 
                    self.current_assessment.get('template', {}).get('description') or
                    ""
                )
                
                # 尝试从不同位置获取说明
                instructions = (
                    self.current_assessment.get('instructions') or 
                    self.current_assessment.get('template', {}).get('instructions') or
                    ""
                )
                
                # 添加调试日志
                logger.info(f"[调试] 评估数据: {self.current_assessment.get('name', 'Unknown')}")
                logger.info(f"[调试] 直接description: {repr(self.current_assessment.get('description'))}")
                logger.info(f"[调试] template.description: {repr(self.current_assessment.get('template', {}).get('description'))}")
                logger.info(f"[调试] 最终description: {repr(description)}")
                logger.info(f"[调试] 直接instructions: {repr(self.current_assessment.get('instructions'))}")
                logger.info(f"[调试] template.instructions: {repr(self.current_assessment.get('template', {}).get('instructions'))}")
                logger.info(f"[调试] 最终instructions: {repr(instructions)}")
                logger.info(f"[调试] 条件判断 (description or instructions): {bool(description or instructions)}")
            
            # 如果有描述或说明，则显示
            if description or instructions:
                # 创建描述卡片 - 使用自适应高度
                desc_card = MDCard(
                    orientation="vertical",
                    size_hint_y=None,
                    height=dp(80),  # 设置初始最小高度
                    padding=[dp(12), dp(10)],  # 适中的内边距
                    radius=[dp(8)],  # 适中的圆角
                    elevation=1,  # 适中的阴影
                    md_bg_color=self.app.theme.SURFACE_COLOR,
                    shadow_softness=2
                )
                
                total_height = dp(20)  # 基础高度
                
                # 添加描述文本
                if description:
                    desc_label = MDLabel(
                        text=f"[b]描述:[/b] {description}",
                        theme_text_color="Secondary",
                        font_size=dp(11),
                        text_size=(None, None),
                        halign="left",
                        valign="top",
                        markup=True,
                        size_hint_y=None
                    )
                    
                    # 绑定宽度变化以实现自适应
                    def update_desc_size(instance, value):
                        instance.text_size = (instance.width - dp(24), None)
                        instance.texture_update()
                        if instance.texture_size[1] > 0:
                            instance.height = instance.texture_size[1] + dp(4)
                    
                    desc_label.bind(width=update_desc_size)
                    desc_card.add_widget(desc_label)
                    total_height += dp(30)
                
                # 添加说明文本
                if instructions:
                    if description:  # 如果已有描述，添加间距
                        spacer = MDBoxLayout(size_hint_y=None, height=dp(8))
                        desc_card.add_widget(spacer)
                        total_height += dp(8)
                    
                    inst_label = MDLabel(
                        text=f"[b]说明:[/b] {instructions}",
                        theme_text_color="Secondary",
                        font_size=dp(11),
                        text_size=(None, None),
                        halign="left",
                        valign="top",
                        markup=True,
                        size_hint_y=None
                    )
                    
                    # 绑定宽度变化以实现自适应
                    def update_inst_size(instance, value):
                        instance.text_size = (instance.width - dp(24), None)
                        instance.texture_update()
                        if instance.texture_size[1] > 0:
                            instance.height = instance.texture_size[1] + dp(4)
                    
                    inst_label.bind(width=update_inst_size)
                    desc_card.add_widget(inst_label)
                    total_height += dp(30)
                
                # 设置卡片高度
                desc_card.height = max(dp(80), total_height)
                
                container.add_widget(desc_card)
                
                # 添加间距
                spacer = MDBoxLayout(
                    size_hint_y=None,
                    height=dp(12)
                )
                container.add_widget(spacer)
                
                logger.info(f"已添加评估描述和说明 - 描述: {bool(description)}, 说明: {bool(instructions)}")
                
        except Exception as e:
            logger.error(f"添加评估量表描述时出错: {e}")

    def add_submit_button(self, container):
        """添加提交按钮"""
        try:
            spacer = MDBoxLayout(size_hint_y=None, height=dp(24))
            container.add_widget(spacer)
            button_container = MDBoxLayout(
                orientation="horizontal",
                size_hint_y=None,
                height=dp(56),
                padding=[dp(24), 0],
                spacing=dp(16)
            )
            submit_button = MDButton(
                MDButtonText(
                    text="提交评估",
                    font_size=dp(14),
                    theme_text_color="Custom",
                    text_color="#FFFFFF"
                ),
                style="elevated",
                size_hint=(1, 1),
                md_bg_color="#0D47A1",
                elevation=3,
                on_release=self.on_submit
            )
            button_container.add_widget(submit_button)
            container.add_widget(button_container)
            bottom_spacer = MDBoxLayout(size_hint_y=None, height=dp(32))
            container.add_widget(bottom_spacer)
        except Exception as e:
            logger.error(f"添加提交按钮时出错: {e}")

    def on_text_answer(self, instance, value):
        """文本回答变化时的回调"""
        try:
            question_index = getattr(instance, 'question_index', None)
            if question_index is not None:
                self.answers[question_index] = value
                self.update_progress()
                logger.debug(f"问题 {question_index + 1} 文本答案更新: {value}")
            else:
                logger.warning("无法获取问题索引，文本答案未保存")
        except Exception as e:
            logger.error(f"处理文本答案时出错: {e}")

    def on_radio_answer(self, instance, value):
        """单选回答变化时的回调"""
        try:
            if value:  # 只处理选中状态
                question_index = getattr(instance, 'question_index', None)
                option_index = getattr(instance, 'option_index', None)
                option_value = getattr(instance, 'option_value', option_index)
                
                if question_index is not None and option_index is not None:
                    self.answers[question_index] = {
                        'option_index': option_index,
                        'option_value': option_value,
                        'type': 'radio'
                    }
                    self.update_progress()
                    logger.debug(f"问题 {question_index + 1} 单选答案更新: 选项 {option_index + 1}")
        except Exception as e:
            logger.error(f"处理单选答案时出错: {e}")

    def on_checkbox_answer(self, instance, value):
        """多选回答变化时的回调"""
        try:
            question_index = getattr(instance, 'question_index', None)
            option_index = getattr(instance, 'option_index', None)
            option_value = getattr(instance, 'option_value', option_index)
            
            if question_index is not None and option_index is not None:
                # 初始化多选答案
                if self.answers[question_index] is None:
                    self.answers[question_index] = {
                        'selected_options': [],
                        'type': 'checkbox'
                    }
                elif not isinstance(self.answers[question_index], dict) or 'selected_options' not in self.answers[question_index]:
                    self.answers[question_index] = {
                        'selected_options': [],
                        'type': 'checkbox'
                    }
                
                selected_options = self.answers[question_index]['selected_options']
                
                if value:  # 选中
                    option_data = {
                        'option_index': option_index,
                        'option_value': option_value
                    }
                    if option_data not in selected_options:
                        selected_options.append(option_data)
                else:  # 取消选中
                    selected_options[:] = [opt for opt in selected_options 
                                         if opt.get('option_index') != option_index]
                
                self.update_progress()
                logger.debug(f"问题 {question_index + 1} 多选答案更新: {len(selected_options)} 个选项")
            else:
                logger.warning("无法获取问题索引或选项索引，多选答案未保存")
        except Exception as e:
            logger.error(f"处理多选答案时出错: {e}")

    def on_slider_answer(self, instance, value):
        """滑块回答变化时的回调"""
        try:
            question_index = getattr(instance, 'question_index', None)
            if question_index is not None:
                self.answers[question_index] = {
                    'value': value,
                    'type': 'slider'
                }
                self.update_progress()
                logger.debug(f"问题 {question_index + 1} 滑块答案更新: {value}")
        except Exception as e:
            logger.error(f"处理滑块答案时出错: {e}")

    def on_branching_answer(self, instance, value):
        if value:
            question_index = getattr(instance, 'question_index', None)
            option_index = getattr(instance, 'option_index', None)
            branch_targets = getattr(instance, 'branch_targets', None)
            # 先保存答案
            self.answers[question_index] = {'option_index': option_index, 'type': 'branching'}
            # 动态设置后续题目active/inactive
            if branch_targets:
                for idx, active in branch_targets.items():
                    if 0 <= idx < len(self.questions):
                        self.questions[idx]['active'] = active
            self.create_question_form()  # 刷新UI
            self.update_progress()

    def on_ranking_answer(self, question_index, option_index, value):
        if not isinstance(self.answers[question_index], dict):
            self.answers[question_index] = {}
        self.answers[question_index][option_index] = value
        self.update_progress()

    def on_time_answer(self, instance, value):
        question_index = getattr(instance, 'question_index', None)
        self.answers[question_index] = str(value)
        self.update_progress()

    def update_progress(self):
        """更新完成进度"""
        try:
            if not self.questions:
                self.progress = 0
                return
            answered_count = 0
            active_count = 0
            for i, question in enumerate(self.questions):
                if not question.get('active', True):
                    continue
                active_count += 1
                answer = self.answers[i] if i < len(self.answers) else None
                if self.is_answer_valid(answer):
                    answered_count += 1
            self.progress = (answered_count / active_count) * 100 if active_count else 0
            
            # 更新进度显示
            try:
                form_container = self.ids.form_container
                if form_container.children:
                    # 查找进度卡片并更新
                    for child in form_container.children:
                        if isinstance(child, MDCard) and hasattr(child, 'children'):
                            for card_child in child.children:
                                if isinstance(card_child, MDLabel) and "完成进度" in card_child.text:
                                    card_child.text = f"完成进度: {self.progress:.1f}%"
                                    break
            except Exception as e:
                logger.debug(f"更新进度显示时出错: {e}")
            
            logger.debug(f"进度更新: {answered_count}/{active_count} = {self.progress:.1f}%")
        except Exception as e:
            logger.error(f"更新进度时出错: {e}")

    def on_submit(self, instance=None):
        try:
            if self.is_submitting:
                logger.warning("正在提交中，请勿重复提交")
                return
            logger.info("开始提交评估量表")
            missing_required = []
            for i, question in enumerate(self.questions):
                if question.get('required', True) and question.get('active', True):
                    answer = self.answers[i] if i < len(self.answers) else None
                    if not self.is_answer_valid(answer):
                        missing_required.append(i + 1)
            if missing_required:
                missing_text = "、".join(map(str, missing_required))
                self.show_error(f"请完成必填问题：第 {missing_text} 题")
                return
            self.is_submitting = True
            submission_data = self.prepare_submission_data()
            threading.Thread(
                target=self.submit_assessment_thread,
                args=(submission_data,),
                daemon=True
            ).start()
        except Exception as e:
            logger.error(f"提交评估时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error(f"提交失败: {str(e)}")
            self.is_submitting = False

    def submit_assessment_thread(self, submission_data):
        try:
            logger.info("开始后台提交评估量表")
            cloud_api = get_cloud_api()
            if not cloud_api.is_authenticated():
                Clock.schedule_once(lambda dt: self.show_error("用户未登录，请先登录"))
                return
            result = cloud_api.submit_mobile_assessment(
                assessment_id=submission_data.get('assessment_id'),
                answers=submission_data.get('answers'),
                custom_id=submission_data.get('custom_id'),
                template_id=submission_data.get('template_id')
            )
            if result and result.get('status') == 'success':
                logger.info("评估量表提交成功")
                Clock.schedule_once(lambda dt: self.on_submit_success(result))
            else:
                error_msg = result.get('message', '提交失败') if result else '提交失败'
                logger.error(f"评估量表提交失败: {error_msg}")
                Clock.schedule_once(lambda dt: self.show_error(f"提交失败: {error_msg}"))
        except Exception as ex:
            logger.error(f"后台提交评估时出错: {ex}")
            import traceback
            logger.error(traceback.format_exc())
            error_message = str(ex)
            Clock.schedule_once(lambda dt: self.show_error(f"提交失败: {error_message}"))
        finally:
            Clock.schedule_once(lambda dt: setattr(self, 'is_submitting', False))

    def on_submit_success(self, result):
        try:
            logger.info("评估量表提交成功")
            self.show_success("评估量表提交成功！")
            app = MDApp.get_running_app()
            if hasattr(app, 'root') and hasattr(app.root, 'get_screen'):
                try:
                    survey_screen = app.root.get_screen('survey_screen')
                    if survey_screen:
                        survey_screen.load_response_history()
                        survey_screen.load_assessments()
                except Exception as e:
                    logger.error(f"刷新SurveyScreen历史记录和量表列表失败: {e}")
            Clock.schedule_once(lambda dt: self.go_back(), 1.5)
        except Exception as e:
            logger.error(f"处理提交成功回调时出错: {e}")

    def show_error(self, message):
        """显示错误信息"""
        logger.error(message)
        try:
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            
            # 确保message不为空
            if not message:
                message = "发生未知错误"
            
            # 确保app和theme存在
            error_color = "#F44336"  # 默认红色
            if hasattr(self, 'app') and self.app and hasattr(self.app, 'theme'):
                if hasattr(self.app.theme, 'ERROR_COLOR'):
                    error_color = self.app.theme.ERROR_COLOR
            
            MDSnackbar(
                MDSnackbarText(
                    text=str(message),
                ),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                duration=3,
                md_bg_color=error_color,
            ).open()
        except Exception as e:
            logger.error(f"显示错误信息失败: {e}")
            # 备用错误显示方法
            try:
                from kivymd.toast import toast
                toast(str(message))
            except:
                print(f"ERROR: {message}")

    def show_success(self, message):
        """显示成功信息"""
        try:
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            
            MDSnackbar(
                MDSnackbarText(
                    text=message,
                ),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                duration=2,
                md_bg_color=self.app.theme.SUCCESS_COLOR,
            ).open()
        except Exception as e:
            logger.error(f"显示成功信息失败: {e}")

    def show_info(self, message):
        """显示信息提示"""
        try:
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            
            MDSnackbar(
                MDSnackbarText(
                    text=message,
                ),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                duration=2,
                md_bg_color=self.app.theme.INFO_COLOR,
            ).open()
        except Exception as e:
            logger.error(f"显示信息提示失败: {e}")

    def go_back(self, *args):
        """返回上一页"""
        app = MDApp.get_running_app()
        app.root.transition.direction = 'right'
        app.root.current = 'survey_screen'

    def is_answer_valid(self, answer):
        """检查答案是否有效"""
        if answer is None:
            return False
        
        if isinstance(answer, str):
            return answer.strip() != ""
        
        if isinstance(answer, dict):
            if answer.get('type') == 'checkbox':
                return bool(answer.get('selected_options'))
            else:
                return True
        
        return True

    def prepare_submission_data(self):
        """准备提交数据"""
        try:
            user_manager = get_user_manager()
            user_info = user_manager.get_user_info()
            submission_data = {
                'assessment_id': self.current_assessment.get('id'),
                'template_id': self.current_assessment.get('template', {}).get('id'),
                'user_id': user_info.get('id') if user_info else None,
                'submitted_at': datetime.now().isoformat(),
                'answers': []
            }
            logger.info(f"准备提交数据，评估ID: {submission_data['assessment_id']}, 模板ID: {submission_data['template_id']}")
            for i, answer in enumerate(self.answers):
                if i < len(self.questions):
                    question = self.questions[i]
                    question_id = question.get('question_id') or question.get('id')
                    if not question_id:
                        logger.warning(f"问题 {i+1} 缺少question_id，将跳过此问题")
                        continue
                    # 后端要求question_id为字符串
                    question_id = str(question_id)
                    answer_data = {'question_id': question_id}
                    # 只允许answer字段
                    if answer is not None:
                        if isinstance(answer, str):
                            answer_data['answer'] = answer
                        elif isinstance(answer, dict):
                            if answer.get('type') == 'radio':
                                answer_data['answer'] = answer.get('option_value')
                            elif answer.get('type') == 'checkbox':
                                selected_options = answer.get('selected_options', [])
                                answer_data['answer'] = [opt.get('option_value') for opt in selected_options]
                            elif answer.get('type') == 'slider':
                                answer_data['answer'] = answer.get('value')
                            else:
                                answer_data['answer'] = answer.get('answer') or answer.get('value')
                        else:
                            answer_data['answer'] = answer
                    submission_data['answers'].append(answer_data)
            logger.info(f"准备提交数据完成，共 {len(submission_data['answers'])} 个答案")
            logger.debug(f"完整提交数据: {json.dumps(submission_data)}")
            return submission_data
        except Exception as e:
            logger.error(f"准备提交数据时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            raise

# 注册屏幕
Factory.register('AssessmentFormScreen', cls=AssessmentFormScreen)

# 定义KV字符串
KV = '''
<AssessmentFormScreen>:
    canvas.before:
        Color:
            rgba: [0.98, 0.98, 0.98, 1]  # 非常浅的灰色背景，确保与白色卡片有对比
        Rectangle:
            pos: self.pos
            size: self.size
    
    MDBoxLayout:
        orientation: 'vertical'
        spacing: dp(8)
        
        # 顶部应用栏
        MDBoxLayout:
            id: app_bar
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(56)
            md_bg_color: app.theme.PRIMARY_COLOR
            padding: [dp(4), dp(0), dp(4), dp(0)]
            
            MDIconButton:
                icon: "arrow-left"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.go_back()
            
            MDLabel:
                text: root.title
                theme_text_color: "Custom"
                text_color: app.theme.TEXT_LIGHT
                halign: "center"
                valign: "center"
                font_style: "Title"
            
            MDIconButton:
                icon: "help-circle"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.show_info("请回答所有问题后点击提交按钮。")
        
        # 进度条
        MDBoxLayout:
            orientation: 'vertical'
            size_hint_y: None
            height: dp(48)
            padding: [dp(16), dp(8)]
            
            MDLabel:
                text: f"完成进度: {root.progress:.1f}%"
                theme_text_color: "Secondary"
                size_hint_y: None
                height: dp(20)
            
            ProgressBar:
                id: progress_bar
                value: root.progress
                size_hint_y: None
                height: dp(4)
        
        # 表单容器
        ScrollView:
            do_scroll_x: False
            do_scroll_y: True
            
            MDBoxLayout:
                id: form_container
                orientation: 'vertical'
                size_hint_y: None
                height: self.minimum_height
                padding: [dp(16), dp(16)]
                spacing: dp(16)
'''

# 加载KV字符串
from kivy.lang import Builder
Builder.load_string(KV)
print("AssessmentFormScreen KV loaded")
