#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

# 配置
BASE_URL = 'http://localhost:8006/api/data-mode'

def test_api(method, endpoint, data=None):
    """测试API接口"""
    url = f"{BASE_URL}{endpoint}"
    print(f"\n=== {method} {endpoint} ===")
    
    try:
        if method == 'GET':
            response = requests.get(url, timeout=10)
        elif method == 'POST':
            response = requests.post(url, json=data, timeout=10)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"成功响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
        else:
            print(f"错误响应: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
    except Exception as e:
        print(f"其他错误: {e}")

if __name__ == '__main__':
    print("快速测试数据模式API...")
    
    # 1. 测试状态接口
    test_api('GET', '/status')
    
    # 2. 测试连接接口
    test_api('GET', '/test')
    
    # 3. 测试切换到模拟模式
    test_api('POST', '/switch', {'mode': 'mock', 'reason': '测试切换'})
    
    # 4. 再次检查状态
    test_api('GET', '/status')
    
    print("\n=== 测试完成 ===")