# 代码风格、注释、文档一致性最佳实践

## 1. 代码风格
- 前端统一用 ESLint + Prettier，后端统一用 black/isort/flake8。
- 变量/函数/组件命名：camelCase，组件名 PascalCase，CSS 类名 kebab-case。
- CI 阶段强制 lint 检查，所有提交需通过格式化和风格校验。

## 2. 注释规范
- 复杂逻辑、公共函数、组件、API 路由必须有 JSDoc/PyDoc 注释。
- 推荐用自动化脚本批量插入注释模板，人工补全具体说明。
- 注释内容包括用途、参数、返回值、异常、示例等。

## 3. 文档一致性
- 重要变更记录在 CHANGELOG.md，API/类型/风格/注释规范文档与实现同步。
- 团队协作入口统一在 docs/、README、CODE_STYLE.md。

## 4. 推荐流程
1. 先运行格式化/lint/注释脚本，确保风格和注释一致。
2. 代码评审重点关注风格、注释、文档同步。
3. 变更后及时补全文档和注释。

---
如需自动生成更多风格/注释/文档模板，请联系维护者。 