<template>
  <div class="admin-dashboard">
    <!-- 管理员欢迎卡片 -->
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="welcome-card">
          <template #header>
            <div class="card-header">
              <h2>超级管理员控制中心</h2>
              <div class="last-login" v-if="lastLogin">
                上次登录: {{ lastLogin }}
              </div>
            </div>
          </template>
          <div class="welcome-content">
            <p>欢迎使用超级管理员控制中心，您可以在这里管理系统用户、权限和设置。</p>
            <div class="quick-links">
              <el-button type="primary" @click="$router.push('/admin/users')">用户管理</el-button>
              <el-button type="success" @click="$router.push('/admin/permission-management')">权限管理</el-button>
              <el-button type="warning" @click="$router.push('/admin/monitoring-alerts')">监控告警</el-button>
              <el-button type="info" @click="$router.push('/admin/system-settings')">系统设置</el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统统计 -->
    <el-row :gutter="20" class="mt-4">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-card-content">
            <div class="stat-icon users-icon">
              <el-icon><el-icon-user /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.userCount }}</div>
              <div class="stat-label">用户总数</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-card-content">
            <div class="stat-icon admin-icon" style="background-color: rgba(245, 108, 108, 0.1); color: #F56C6C;">
              <el-icon><el-icon-user-filled /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.adminCount }}</div>
              <div class="stat-label">管理员数量</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-card-content">
            <div class="stat-icon alerts-icon" style="background-color: rgba(230, 162, 60, 0.1); color: #E6A23C;">
              <el-icon><el-icon-bell /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.alertCount }}</div>
              <div class="stat-label">系统告警</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-card-content">
            <div class="stat-icon today-icon">
              <el-icon><el-icon-calendar /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.todayCount }}</div>
              <div class="stat-label">今日活跃用户</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统状态 -->
    <el-row :gutter="20" class="mt-4">
      <el-col :span="24">
        <el-card class="section-card">
          <template #header>
            <div class="card-header">
              <h3>系统状态监控</h3>
              <el-button type="primary" size="small" @click="refreshData">刷新数据</el-button>
            </div>
          </template>

          <div class="section-content">
            <!-- 系统性能指标 -->
            <el-row :gutter="20" class="mb-4">
              <el-col :xs="24" :sm="12" :md="6">
                <div class="data-item">
                  <div class="data-icon">
                    <el-icon><el-icon-cpu /></el-icon>
                  </div>
                  <div class="data-info">
                    <div class="data-label">CPU 平均负载</div>
                    <div class="data-value">{{ systemData.cpuLoad }}%</div>
                  </div>
                </div>
              </el-col>

              <el-col :xs="24" :sm="12" :md="6">
                <div class="data-item">
                  <div class="data-icon">
                    <el-icon><el-icon-monitor /></el-icon>
                  </div>
                  <div class="data-info">
                    <div class="data-label">内存使用率</div>
                    <div class="data-value">{{ systemData.memoryUsage }}%</div>
                  </div>
                </div>
              </el-col>

              <el-col :xs="24" :sm="12" :md="6">
                <div class="data-item">
                  <div class="data-icon">
                    <el-icon><el-icon-data-line /></el-icon>
                  </div>
                  <div class="data-info">
                    <div class="data-label">磁盘使用率</div>
                    <div class="data-value">{{ systemData.diskUsage }}%</div>
                  </div>
                </div>
              </el-col>

              <el-col :xs="24" :sm="12" :md="6">
                <div class="data-item">
                  <div class="data-icon">
                    <el-icon><el-icon-bell /></el-icon>
                  </div>
                  <div class="data-info">
                    <div class="data-label">活跃告警</div>
                    <div class="data-value">{{ systemData.activeAlerts }}</div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最近活动 -->
    <el-card class="recent-activity mt-4">
      <template #header>
        <div class="card-header">
          <span>最近系统活动</span>
        </div>
      </template>

      <el-table :data="recentActivities" style="width: 100%">
        <el-table-column prop="time" label="时间" width="180" />
        <el-table-column prop="user" label="用户" width="180" />
        <el-table-column prop="action" label="操作" />
        <el-table-column prop="details" label="详情" />
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import axios from 'axios';

const lastLogin = ref(null);
const systemData = ref({
  cpuLoad: 45,
  memoryUsage: 68,
  diskUsage: 72,
  activeAlerts: 3
});

// 统计数据
const stats = ref({
  userCount: 0,
  adminCount: 0,
  alertCount: 0,
  todayCount: 0
});

// 最近活动
const recentActivities = ref([]);

onMounted(() => {
  // 模拟获取上次登录时间
  lastLogin.value = new Date().toLocaleString();

  // 获取系统数据
  fetchSystemData();
});

// 获取系统数据
const fetchSystemData = async () => {
  try {
    // 实际项目中应该从API获取数据
    // const response = await axios.get('/api/system/stats');
    // systemData.value = response.data;

    // 模拟系统数据
    systemData.value = {
      cpuLoad: Math.floor(Math.random() * 30) + 40, // 40-70%
      memoryUsage: Math.floor(Math.random() * 30) + 50, // 50-80%
      diskUsage: Math.floor(Math.random() * 20) + 60, // 60-80%
      activeAlerts: Math.floor(Math.random() * 5) + 1 // 1-5
    };

    // 获取真实的告警计数
    try {
      const alertResponse = await axios.get('/api/alerts/')
      const alertCount = alertResponse.data && alertResponse.data.alerts 
        ? alertResponse.data.alerts.filter(alert => alert.status !== 'resolved').length 
        : 0
      
      stats.value = {
        userCount: 128,
        adminCount: 5,
        alertCount: alertCount,
        todayCount: 32
      };
    } catch (alertError) {
      console.error('获取告警数据失败:', alertError)
      // 如果获取告警失败，使用默认值
      stats.value = {
        userCount: 128,
        adminCount: 5,
        alertCount: 0,
        todayCount: 32
      };
    }

    // 模拟最近活动
    recentActivities.value = [
      { time: '2023-04-24 10:30', user: '管理员', action: '创建用户', details: '新建用户: 张三' },
      { time: '2023-04-24 09:15', user: '系统', action: '系统告警', details: '磁盘空间不足' },
      { time: '2023-04-23 16:45', user: '管理员', action: '修改权限', details: '用户: 李四, 角色: 健康顾问' },
      { time: '2023-04-23 14:20', user: '管理员', action: '系统设置', details: '修改系统参数' },
      { time: '2023-04-23 11:10', user: '管理员', action: '登录系统', details: 'IP: *************' }
    ];
  } catch (error) {
    console.error('获取系统数据失败:', error);
    ElMessage.error('获取系统数据失败');
  }
};

// 刷新数据
const refreshData = () => {
  ElMessage.success('正在刷新数据...');
  fetchSystemData();
};
</script>

<style scoped>
.admin-dashboard {
  padding: 20px;
}

.welcome-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.last-login {
  font-size: 14px;
  color: #909399;
}

.welcome-content {
  text-align: center;
  padding: 20px 0;
}

.quick-links {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 10px;
  margin-top: 20px;
}

.data-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  background-color: #f8f9fa;
  height: 100%;
  transition: transform 0.3s, box-shadow 0.3s;
}

.data-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.data-icon {
  font-size: 2rem;
  margin-right: 15px;
  color: #409EFF;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: rgba(64, 158, 255, 0.1);
}

.data-info {
  flex: 1;
}

.data-label {
  font-size: 0.9rem;
  color: #606266;
  margin-bottom: 5px;
}

.data-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #303133;
}

.mt-4 {
  margin-top: 20px;
}

.mb-4 {
  margin-bottom: 20px;
}

.section-card {
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.section-content {
  padding: 15px 0;
}

.stat-card {
  margin-bottom: 20px;
}

.stat-card-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24px;
  margin-right: 15px;
}

.users-icon {
  background-color: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

.recent-activity {
  margin-bottom: 20px;
}
</style>
