#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建量表结果测试数据
"""

import sqlite3
import json
from datetime import datetime, timedelta
import uuid

def create_assessment_test_data():
    # 连接数据库
    db_path = "app.db"
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 检查assessment_templates表中的模板
        print("=== 检查assessment_templates表 ===")
        # 先检查表结构
        cursor.execute("PRAGMA table_info(assessment_templates)")
        columns = cursor.fetchall()
        print(f"assessment_templates表结构: {[col[1] for col in columns]}")
        
        cursor.execute("SELECT id, name FROM assessment_templates")
        templates = cursor.fetchall()
        
        if not templates:
            print("assessment_templates表中没有模板数据")
            return
            
        print(f"找到 {len(templates)} 个量表模板:")
        for template in templates:
            print(f"  ID: {template[0]}, 名称: {template[1]}")
        
        # 为SM_006用户创建一些量表结果
        print("\n=== 创建量表结果测试数据 ===")
        
        # 创建assessment记录
        assessment_data = [
            {
                'custom_id': 'SM_006',
                'template_id': templates[0][0],  # 使用第一个模板
                'template_name': templates[0][1],
                'score': 85,
                'level': '中等',
                'days_ago': 1
            },
            {
                'custom_id': 'SM_006', 
                'template_id': templates[0][0] if len(templates) > 0 else 1,
                'template_name': templates[0][1] if len(templates) > 0 else '心理健康量表',
                'score': 72,
                'level': '轻度',
                'days_ago': 3
            },
            {
                'custom_id': 'SM_006',
                'template_id': templates[1][0] if len(templates) > 1 else templates[0][0],
                'template_name': templates[1][1] if len(templates) > 1 else templates[0][1],
                'score': 90,
                'level': '良好',
                'days_ago': 7
            }
        ]
        
        for data in assessment_data:
            # 创建assessment记录 - 使用自增ID
            created_at = datetime.now() - timedelta(days=data['days_ago'])
            
            cursor.execute("""
                INSERT INTO assessments (
                    custom_id, template_id, assessment_type, name, status, 
                    score, result, created_at, updated_at
                )
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                data['custom_id'],
                data['template_id'],
                'PSYCHOLOGICAL',  # 使用枚举值
                data['template_name'],
                'completed',
                data['score'],
                data['level'],
                created_at.isoformat(),
                created_at.isoformat()
            ))
            
            # 获取刚插入的assessment_id
            assessment_id = cursor.lastrowid
            
            # 创建assessment_results记录 - 使用自增ID
            cursor.execute("""
                INSERT INTO assessment_results (
                    assessment_id, custom_id, template_id, 
                    total_score, result_level, status, 
                    calculated_at, created_at, updated_at
                )
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                assessment_id,
                data['custom_id'],
                data['template_id'],
                data['score'],
                data['level'],
                'calculated',
                created_at.isoformat(),
                created_at.isoformat(),
                created_at.isoformat()
            ))
            
            print(f"创建量表结果: {data['template_name']}, 分数: {data['score']}, 等级: {data['level']}, 日期: {created_at.strftime('%Y-%m-%d')}")
        
        # 提交事务
        conn.commit()
        print("\n测试数据创建成功！")
        
        # 验证创建的数据
        print("\n=== 验证创建的数据 ===")
        cursor.execute("""
            SELECT ar.id, ar.assessment_id, ar.custom_id, ar.total_score, ar.result_level, ar.created_at,
                   at.name as template_name
            FROM assessment_results ar
            LEFT JOIN assessment_templates at ON ar.template_id = at.id
            WHERE ar.custom_id = 'SM_006'
            ORDER BY ar.created_at DESC
        """)
        
        results = cursor.fetchall()
        print(f"SM_006的量表结果记录数: {len(results)}")
        for result in results:
            print(f"  模板: {result[6]}, 分数: {result[3]}, 等级: {result[4]}, 日期: {result[5]}")
            
    except Exception as e:
        print(f"创建测试数据时出错: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    create_assessment_test_data()