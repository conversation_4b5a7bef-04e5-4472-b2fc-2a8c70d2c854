@echo off
chcp 65001 >nul 2>&1
set PYTHONIOENCODING=utf-8
set LANG=zh_CN.UTF-8
set PYTHONLEGACYWINDOWSSTDIO=utf-8

:menu
cls
echo ========================================
echo Assessment and Questionnaire Monitoring Tool
echo ========================================
echo.
echo Please select an option:
echo 1. Run full check
echo 2. Run check and auto-fix issues
echo 3. Export monitoring report
echo 4. Fix monitoring issues (template_id and duplicate identifiers)
echo 5. Check specific user (requires user ID)
echo 6. Check specific assessment (requires assessment ID)
echo 7. Check specific questionnaire (requires questionnaire ID)
echo 8. Monitor specific template (requires template key)
echo 9. Start real-time monitoring (60-second interval)
echo 10. View help information
echo 0. Exit
echo.
set /p choice=Please enter your choice (0-10): 

if "%choice%"=="1" goto full_check
if "%choice%"=="2" goto fix_issues
if "%choice%"=="3" goto export_report
if "%choice%"=="4" goto fix_monitoring_issues
if "%choice%"=="5" goto check_user
if "%choice%"=="6" goto check_assessment
if "%choice%"=="7" goto check_questionnaire
if "%choice%"=="8" goto check_template
if "%choice%"=="9" goto real_time
if "%choice%"=="10" goto help
if "%choice%"=="0" goto exit
goto invalid

:full_check
echo.
echo Running full assessment and questionnaire monitoring check...
echo.
python "%~dp0assessment_questionnaire_monitor.py"
if errorlevel 1 (
    echo.
    echo Error occurred during monitoring check!
    pause
    goto menu
)
echo.
echo Monitoring check completed successfully!
pause
goto menu

:fix_issues
echo.
echo Running monitoring check with auto-fix...
echo.
python "%~dp0assessment_questionnaire_monitor.py" --fix
if errorlevel 1 (
    echo.
    echo Error occurred during monitoring check with auto-fix!
    pause
    goto menu
)
echo.
echo Monitoring check with auto-fix completed successfully!
pause
goto menu

:export_report
echo.
echo Exporting monitoring report...
echo.
python "%~dp0assessment_questionnaire_monitor.py" --export
if errorlevel 1 (
    echo.
    echo Error occurred during report export!
    pause
    goto menu
)
echo.
echo Report exported successfully!
pause
goto menu

:fix_monitoring_issues
echo.
echo Running monitoring issue fix script...
echo.
python "%~dp0fix_monitoring_issues.py"
if errorlevel 1 (
    echo.
    echo Error occurred during issue fix!
    pause
    goto menu
)
echo.
echo Issue fix completed successfully!
pause
goto menu

:check_user
echo.
set /p user_id=Please enter user ID: 
echo.
echo Checking user %user_id%...
echo.
python "%~dp0assessment_questionnaire_monitor.py" --user "%user_id%"
if errorlevel 1 (
    echo.
    echo Error occurred during user check!
    pause
    goto menu
)
echo.
echo User check completed successfully!
pause
goto menu

:check_assessment
echo.
set /p assessment_id=Please enter assessment ID: 
echo.
echo Checking assessment %assessment_id%...
echo.
python "%~dp0assessment_questionnaire_monitor.py" --assessment "%assessment_id%"
if errorlevel 1 (
    echo.
    echo Error occurred during assessment check!
    pause
    goto menu
)
echo.
echo Assessment check completed successfully!
pause
goto menu

:check_questionnaire
echo.
set /p questionnaire_id=Please enter questionnaire ID: 
echo.
echo Checking questionnaire %questionnaire_id%...
echo.
python "%~dp0assessment_questionnaire_monitor.py" --questionnaire "%questionnaire_id%"
if errorlevel 1 (
    echo.
    echo Error occurred during questionnaire check!
    pause
    goto menu
)
echo.
echo Questionnaire check completed successfully!
pause
goto menu

:check_template
echo.
set /p template_key=Please enter template key: 
echo.
echo Monitoring template %template_key%...
echo.
python "%~dp0assessment_questionnaire_monitor.py" --template "%template_key%"
if errorlevel 1 (
    echo.
    echo Error occurred during template monitoring!
    pause
    goto menu
)
echo.
echo Template monitoring completed successfully!
pause
goto menu

:real_time
echo.
echo Starting real-time monitoring (60-second interval)...
echo Press Ctrl+C to stop monitoring
echo.
python "%~dp0assessment_questionnaire_monitor.py" --realtime
if errorlevel 1 (
    echo.
    echo Error occurred during real-time monitoring!
    pause
    goto menu
)
echo.
echo Real-time monitoring stopped!
pause
goto menu

:help
echo.
echo ========================================
echo Help Information
echo ========================================
echo.
echo This tool provides comprehensive monitoring for assessment scales and questionnaires.
echo.
echo Available options:
echo 1. Full Check - Performs complete system monitoring
echo 2. Auto-fix - Runs monitoring and automatically fixes detected issues
echo 3. Export Report - Generates detailed monitoring report
echo 4. Fix Issues - Runs dedicated issue fix script
echo 5-7. Specific Checks - Monitor individual users, assessments, or questionnaires
echo 8. Template Monitor - Monitor specific template by key
echo 9. Real-time - Continuous monitoring with 60-second intervals
echo.
echo For more information, please refer to the documentation.
echo.
pause
goto menu

:invalid
echo.
echo Invalid selection, please try again
pause
goto menu

:exit
echo.
echo Thank you for using the monitoring tool!
echo.
exit /b 0