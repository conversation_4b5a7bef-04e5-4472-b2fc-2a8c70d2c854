"""
测试证书上传功能
"""
import os
import sys
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
if project_root not in sys.path:
    sys.path.append(project_root)

def create_test_file(file_path):
    """创建测试文件"""
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # 创建一个简单的文本文件
        with open(file_path, 'w') as f:
            f.write("This is a test file for certificate upload.")
        
        logger.info(f"已创建测试文件: {file_path}")
        return True
    except Exception as e:
        logger.error(f"创建测试文件失败: {e}")
        return False

def test_upload_certificate():
    """测试上传证书功能"""
    try:
        # 导入API客户端
        from utils.api_factory import get_api_client
        
        # 获取API客户端实例
        api_client = get_api_client(client_type="local")
        logger.info(f"已获取API客户端实例: {type(api_client).__name__}")
        
        # 创建测试文件
        data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data')
        test_file = os.path.join(data_dir, 'test_certificate.txt')
        
        if not os.path.exists(test_file):
            if not create_test_file(test_file):
                logger.error("无法创建测试文件，测试终止")
                return False
        
        # 测试上传证书
        logger.info(f"开始测试上传证书: {test_file}")
        result = api_client.upload_certificate(
            file_path=test_file,
            certificate_type="test_certificate",
            description="Test certificate upload"
        )
        
        # 检查结果
        if result:
            logger.info(f"上传结果: {result}")
            if result.get('success'):
                logger.info(f"证书上传成功，文档ID: {result.get('document_id')}")
                return True
            else:
                logger.error(f"证书上传失败: {result.get('message')}")
                return False
        else:
            logger.error("上传证书返回空结果")
            return False
    
    except Exception as e:
        logger.error(f"测试上传证书时出错: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    # 执行测试
    success = test_upload_certificate()
    
    if success:
        print("测试成功: 证书上传功能正常工作")
        sys.exit(0)
    else:
        print("测试失败: 证书上传功能存在问题")
        sys.exit(1)
