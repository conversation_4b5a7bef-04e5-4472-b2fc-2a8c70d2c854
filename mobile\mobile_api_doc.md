# 健康管理平台移动端API文档

本文档提供了健康管理平台移动端（Kivy框架）与后端服务器通信的API请求格式说明。

## 目录

1. [基础信息](#基础信息)
2. [认证机制](#认证机制)
3. [API列表](#api列表)
   - [用户认证](#用户认证)
   - [健康记录管理](#健康记录管理)
   - [评估量表](#评估量表)
   - [文档管理](#文档管理)
   - [药物管理](#药物管理)
   - [用户管理](#用户管理)
4. [Kivy实现示例](#kivy实现示例)

## 基础信息

- **基础URL**: `http://your-server-address/api`
- **请求格式**: JSON
- **响应格式**: JSON
- **字符编码**: UTF-8

## 认证机制

大部分API需要认证才能访问。认证通过在HTTP请求头中添加`Authorization`字段实现：

```
Authorization: Bearer {access_token}
```

其中`{access_token}`是通过登录API获取的令牌。

## API列表

### 用户认证

#### 登录

- **URL**: `/login`
- **方法**: POST
- **描述**: 用户登录并获取访问令牌
- **请求参数**:

```json
{
  "username": "用户名",
  "password": "密码"
}
```

- **响应**:

```json
{
  "access_token": "simulated_token",
  "token_type": "bearer",
  "username": "用户名"
}
```

- **错误响应** (401):

```json
{
  "error": "Invalid credentials"
}
```

### 健康记录管理

#### 获取健康记录列表

- **URL**: `/health-records`
- **方法**: GET
- **描述**: 获取健康记录列表
- **请求参数**:
  - `page`: 页码（默认1）
  - `limit`: 每页记录数（默认10）
- **响应**:

```json
{
  "items": [
    {
      "id": 1,
      "user_id": 101,
      "user_name": "张三",
      "record_type": "blood_pressure",
      "record_value": "120/80",
      "record_unit": "mmHg",
      "created_at": "2025-04-11 10:30:00",
      "notes": "晨起测量"
    }
  ],
  "total": 1,
  "page": 1,
  "limit": 10
}
```

#### 获取健康记录详情

- **URL**: `/health-records/{record_id}`
- **方法**: GET
- **描述**: 获取指定ID的健康记录详情
- **响应**:

```json
{
  "id": 1,
  "user_id": 101,
  "user_name": "张三",
  "record_type": "blood_pressure",
  "record_value": "120/80",
  "record_unit": "mmHg",
  "created_at": "2025-04-11 10:30:00",
  "notes": "晨起测量",
  "status": "normal"
}
```

#### 创建健康记录

- **URL**: `/health-records`
- **方法**: POST
- **描述**: 创建新的健康记录
- **请求参数**:

```json
{
  "user_id": 101,
  "record_type": "blood_pressure",
  "record_value": "120/80",
  "record_unit": "mmHg",
  "notes": "晨起测量"
}
```

- **响应**:

```json
{
  "id": 4,
  "user_id": 101,
  "user_name": "新用户",
  "record_type": "blood_pressure",
  "record_value": "120/80",
  "record_unit": "mmHg",
  "created_at": "2025-04-12 09:00:00",
  "notes": "晨起测量",
  "status": "normal"
}
```

#### 更新健康记录

- **URL**: `/health-records/{record_id}`
- **方法**: PUT
- **描述**: 更新指定ID的健康记录
- **请求参数**:

```json
{
  "record_type": "blood_pressure",
  "record_value": "118/78",
  "record_unit": "mmHg",
  "notes": "晨起测量，更新值"
}
```

- **响应**:

```json
{
  "id": 1,
  "user_id": 101,
  "user_name": "张三",
  "record_type": "blood_pressure",
  "record_value": "118/78",
  "record_unit": "mmHg",
  "created_at": "2025-04-11 10:30:00",
  "updated_at": "2025-04-12 10:30:00",
  "notes": "晨起测量，更新值",
  "status": "normal"
}
```

#### 删除健康记录

- **URL**: `/health-records/{record_id}`
- **方法**: DELETE
- **描述**: 删除指定ID的健康记录
- **响应**:

```json
{
  "success": true,
  "message": "记录 1 已删除"
}
```

### 评估量表

#### 获取评估量表列表

- **URL**: `/assessment-scales`
- **方法**: GET
- **描述**: 获取评估量表列表
- **请求参数**:
  - `page`: 页码（默认1）
  - `limit`: 每页记录数（默认10）
- **响应**:

```json
{
  "items": [
    {
      "id": 1,
      "title": "焦虑自评量表(SAS)",
      "description": "用于评估个体焦虑程度的自评量表",
      "scale_type": "psychological",
      "target_population": "adults",
      "questions": [
        {
          "text": "我觉得比平常容易紧张或着急",
          "options": [
            {"text": "很少", "value": 1},
            {"text": "有时", "value": 2},
            {"text": "大部分时间", "value": 3},
            {"text": "绝大部分或全部时间", "value": 4}
          ]
        }
      ],
      "created_at": "2025-03-10",
      "is_active": true
    }
  ],
  "total": 1,
  "page": 1,
  "limit": 10
}
```

#### 获取评估量表详情

- **URL**: `/assessment-scales/{scale_id}`
- **方法**: GET
- **描述**: 获取指定ID的评估量表详情
- **响应**:

```json
{
  "id": 1,
  "title": "焦虑自评量表(SAS)",
  "description": "用于评估个体焦虑程度的自评量表",
  "scale_type": "psychological",
  "target_population": "adults",
  "questions": [
    {
      "text": "我觉得比平常容易紧张或着急",
      "options": [
        {"text": "很少", "value": 1},
        {"text": "有时", "value": 2},
        {"text": "大部分时间", "value": 3},
        {"text": "绝大部分或全部时间", "value": 4}
      ]
    },
    {
      "text": "我无缘无故地感到害怕",
      "options": [
        {"text": "很少", "value": 1},
        {"text": "有时", "value": 2},
        {"text": "大部分时间", "value": 3},
        {"text": "绝大部分或全部时间", "value": 4}
      ]
    }
  ],
  "scoring_method": {
    "range": {"min": 20, "max": 80},
    "interpretation": [
      {"range": [20, 49], "result": "正常范围"},
      {"range": [50, 59], "result": "轻度焦虑"},
      {"range": [60, 69], "result": "中度焦虑"},
      {"range": [70, 80], "result": "重度焦虑"}
    ]
  },
  "interpretation_guide": {
    "description": "SAS得分范围为20-80分，标准分值=(原始分/80)×100",
    "recommendations": {
      "轻度焦虑": ["建议进行规律的身体活动", "学习放松技巧", "保持健康的作息习惯"],
      "中度焦虑": ["建议寻求心理咨询", "学习认知行为技巧", "必要时在专业指导下服用抗焦虑药物"],
      "重度焦虑": ["建议立即寻求专业精神科医生帮助", "可能需要药物治疗", "定期随访和评估"]
    }
  },
  "created_at": "2025-03-10",
  "is_active": true
}
```

### 文档管理

#### 获取文档列表

- **URL**: `/documents`
- **方法**: GET
- **描述**: 获取所有文档
- **响应**:

```json
[
  {
    "id": 1,
    "filename": "体检报告.pdf",
    "documentType": "medical_report",
    "fileSize": 1024000,
    "fileType": "pdf",
    "uploadDate": "2025-04-10T14:30:00",
    "description": "2025年度体检报告"
  }
]
```

#### 上传文档

- **URL**: `/mobile/upload`
- **方法**: POST
- **描述**: 上传医疗文档
- **请求参数**: 使用`multipart/form-data`格式
  - `file`: 文件数据
  - `documentType`: 文档类型
  - `description`: 文档描述（可选）
- **响应**:

```json
{
  "id": 1,
  "filename": "077dc5909abf4d62a2fc0edc8ffc8e70_体检报告.pdf",
  "originalFilename": "体检报告.pdf",
  "documentType": "medical_report",
  "fileSize": 1024000,
  "fileType": "pdf",
  "description": "2025年度体检报告",
  "uploadDate": "2025-04-12T09:00:00",
  "message": "文档上传成功"
}
```

#### 获取文档详情

- **URL**: `/documents/{document_id}`
- **方法**: GET
- **描述**: 获取指定ID的文档详情
- **响应**:

```json
{
  "id": 1,
  "filename": "体检报告.pdf",
  "documentType": "medical_report",
  "fileSize": 1024000,
  "fileType": "pdf",
  "uploadDate": "2025-04-10T14:30:00",
  "description": "2025年度体检报告",
  "filePath": "/www/wwwroot/healthapp/backend/uploads/077dc5909abf4d62a2fc0edc8ffc8e70_体检报告.pdf"
}
```

#### 下载文档

- **URL**: `/documents/{document_id}/download`
- **方法**: GET
- **描述**: 下载指定ID的文档
- **响应**: 文件数据流

#### 删除文档

- **URL**: `/documents/{document_id}`
- **方法**: DELETE
- **描述**: 删除指定ID的文档
- **响应**:

```json
{
  "message": "文档已成功删除"
}
```

### 药物管理

#### 获取药物列表

- **URL**: `/medications`
- **方法**: GET
- **描述**: 获取所有药物记录
- **响应**:

```json
[
  {
    "id": 1,
    "name": "阿司匹林",
    "dosage": "每次100mg，每天3次",
    "frequency": "每日三次",
    "startDate": "2025-04-01",
    "endDate": "2025-04-30",
    "notes": "饭后服用",
    "created_at": "2025-04-01T10:00:00"
  }
]
```

#### 获取药物详情

- **URL**: `/medications/{medication_id}`
- **方法**: GET
- **描述**: 获取指定ID的药物详情
- **响应**:

```json
{
  "id": 1,
  "name": "阿司匹林",
  "dosage": "每次100mg，每天3次",
  "frequency": "每日三次",
  "startDate": "2025-04-01",
  "endDate": "2025-04-30",
  "notes": "饭后服用",
  "created_at": "2025-04-01T10:00:00"
}
```

#### 创建药物记录

- **URL**: `/medications`
- **方法**: POST
- **描述**: 创建新的药物记录
- **请求参数**:

```json
{
  "name": "阿司匹林",
  "dosage": "每次100mg，每天3次",
  "frequency": "每日三次",
  "startDate": "2025-04-01",
  "endDate": "2025-04-30",
  "notes": "饭后服用"
}
```

- **响应**:

```json
{
  "id": 1,
  "name": "阿司匹林",
  "dosage": "每次100mg，每天3次",
  "frequency": "每日三次",
  "startDate": "2025-04-01",
  "endDate": "2025-04-30",
  "notes": "饭后服用",
  "created_at": "2025-04-12T09:00:00",
  "message": "药物记录已创建"
}
```

#### 更新药物记录

- **URL**: `/medications/{medication_id}`
- **方法**: PUT
- **描述**: 更新指定ID的药物记录
- **请求参数**:

```json
{
  "name": "阿司匹林",
  "dosage": "每次100mg，每天2次",
  "frequency": "每日两次",
  "startDate": "2025-04-01",
  "endDate": "2025-05-15",
  "notes": "饭后服用，调整为每日两次"
}
```

- **响应**:

```json
{
  "id": 1,
  "name": "阿司匹林",
  "dosage": "每次100mg，每天2次",
  "frequency": "每日两次",
  "startDate": "2025-04-01",
  "endDate": "2025-05-15",
  "notes": "饭后服用，调整为每日两次",
  "updated_at": "2025-04-12T10:30:00",
  "message": "药物记录已更新"
}
```

#### 删除药物记录

- **URL**: `/medications/{medication_id}`
- **方法**: DELETE
- **描述**: 删除指定ID的药物记录
- **响应**:

```json
{
  "message": "药物记录已成功删除"
}
```

### 用户管理

#### 获取用户列表

- **URL**: `/users`
- **方法**: GET
- **描述**: 获取所有用户
- **响应**:

```json
[
  {
    "id": 1,
    "username": "admin",
    "fullName": "管理员",
    "email": "<EMAIL>",
    "role": "super_admin",
    "status": "active",
    "created_at": "2025-04-01T10:00:00"
  }
]
```

#### 获取用户详情

- **URL**: `/users/{user_id}`
- **方法**: GET
- **描述**: 获取指定ID的用户详情
- **响应**:

```json
{
  "id": 1,
  "username": "admin",
  "fullName": "管理员",
  "email": "<EMAIL>",
  "role": "super_admin",
  "status": "active",
  "created_at": "2025-04-01T10:00:00"
}
```

#### 创建用户

- **URL**: `/users`
- **方法**: POST
- **描述**: 创建新用户
- **请求参数**:

```json
{
  "username": "newuser",
  "fullName": "新用户",
  "email": "<EMAIL>",
  "password": "password123",
  "role": "personal_user"
}
```

- **响应**:

```json
{
  "success": true,
  "message": "用户创建成功",
  "user": {
    "id": 2,
    "username": "newuser",
    "fullName": "新用户",
    "email": "<EMAIL>",
    "role": "personal_user",
    "status": "active",
    "created_at": "2025-04-12T09:00:00"
  }
}
```

## Kivy实现示例

以下是使用Kivy框架实现API请求的示例代码：

### 1. 基础HTTP请求类

```python
import json
import urllib
from kivy.network.urlrequest import UrlRequest
from kivy.app import App

class ApiClient:
    def __init__(self):
        self.base_url = "http://your-server-address/api"
        self.access_token = None
        
    def set_token(self, token):
        self.access_token = token
        
    def get_headers(self):
        headers = {"Content-Type": "application/json"}
        if self.access_token:
            headers["Authorization"] = f"Bearer {self.access_token}"
        return headers
    
    def handle_error(self, request, error):
        print(f"API错误: {error}")
        # 可以在这里添加错误处理逻辑
        
    def get(self, endpoint, on_success, params=None):
        url = self.base_url + endpoint
        if params:
            url += "?" + urllib.parse.urlencode(params)
            
        UrlRequest(
            url,
            req_headers=self.get_headers(),
            on_success=on_success,
            on_error=self.handle_error,
            on_failure=self.handle_error
        )
    
    def post(self, endpoint, data, on_success):
        UrlRequest(
            self.base_url + endpoint,
            req_headers=self.get_headers(),
            req_body=json.dumps(data),
            on_success=on_success,
            on_error=self.handle_error,
            on_failure=self.handle_error
        )
        
    def put(self, endpoint, data, on_success):
        UrlRequest(
            self.base_url + endpoint,
            req_headers=self.get_headers(),
            req_body=json.dumps(data),
            method="PUT",
            on_success=on_success,
            on_error=self.handle_error,
            on_failure=self.handle_error
        )
        
    def delete(self, endpoint, on_success):
        UrlRequest(
            self.base_url + endpoint,
            req_headers=self.get_headers(),
            method="DELETE",
            on_success=on_success,
            on_error=self.handle_error,
            on_failure=self.handle_error
        )
```

### 2. 登录示例

```python
from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.button import Button
from kivy.uix.textinput import TextInput
from kivy.uix.label import Label

class LoginScreen(BoxLayout):
    def __init__(self, **kwargs):
        super(LoginScreen, self).__init__(**kwargs)
        self.orientation = 'vertical'
        self.padding = 20
        self.spacing = 10
        
        self.add_widget(Label(text="用户名"))
        self.username = TextInput(multiline=False)
        self.add_widget(self.username)
        
        self.add_widget(Label(text="密码"))
        self.password = TextInput(multiline=False, password=True)
        self.add_widget(self.password)
        
        self.login_btn = Button(text="登录")
        self.login_btn.bind(on_press=self.login)
        self.add_widget(self.login_btn)
        
        self.result_label = Label(text="")
        self.add_widget(self.result_label)
        
    def login(self, instance):
        app = App.get_running_app()
        data = {
            "username": self.username.text,
            "password": self.password.text
        }
        
        app.api_client.post("/login", data, self.on_login_success)
        
    def on_login_success(self, request, result):
        app = App.get_running_app()
        if 'access_token' in result:
            app.api_client.set_token(result['access_token'])
            self.result_label.text = f"登录成功，欢迎 {result['username']}"
            # 登录成功后的导航逻辑
        else:
            self.result_label.text = "登录失败"
```

### 3. 获取健康记录示例

```python
from kivy.uix.recycleview import RecycleView
from kivy.uix.recycleview.views import RecycleDataViewBehavior
from kivy.uix.label import Label
from kivy.uix.boxlayout import BoxLayout
from kivy.properties import BooleanProperty, StringProperty

class HealthRecordItem(RecycleDataViewBehavior, BoxLayout):
    index = None
    selected = BooleanProperty(False)
    selectable = BooleanProperty(True)
    
    record_id = StringProperty('')
    record_type = StringProperty('')
    record_value = StringProperty('')
    record_date = StringProperty('')
    
    def refresh_view_attrs(self, rv, index, data):
        self.index = index
        self.record_id = str(data.get('id', ''))
        self.record_type = data.get('record_type', '')
        self.record_value = data.get('record_value', '')
        self.record_date = data.get('created_at', '')
        
    def on_touch_down(self, touch):
        if super(HealthRecordItem, self).on_touch_down(touch):
            return True
        if self.collide_point(*touch.pos) and self.selectable:
            return self.parent.select_with_touch(self.index, touch)

class HealthRecordsScreen(BoxLayout):
    def __init__(self, **kwargs):
        super(HealthRecordsScreen, self).__init__(**kwargs)
        self.orientation = 'vertical'
        self.padding = 10
        
        self.add_widget(Label(text="健康记录列表", size_hint_y=None, height=50))
        
        self.rv = RecycleView()
        self.rv.data = []
        self.add_widget(self.rv)
        
        refresh_btn = Button(text="刷新", size_hint_y=None, height=50)
        refresh_btn.bind(on_press=self.load_records)
        self.add_widget(refresh_btn)
        
        self.load_records()
        
    def load_records(self, *args):
        app = App.get_running_app()
        app.api_client.get("/health-records", self.on_records_loaded, {"page": 1, "limit": 20})
        
    def on_records_loaded(self, request, result):
        self.rv.data = result.get('items', [])
```

### 4. 上传文档示例

```python
from kivy.uix.filechooser import FileChooserListView
from kivy.network.urlrequest import UrlRequest
import os

class DocumentUploadScreen(BoxLayout):
    def __init__(self, **kwargs):
        super(DocumentUploadScreen, self).__init__(**kwargs)
        self.orientation = 'vertical'
        self.padding = 10
        
        self.add_widget(Label(text="选择要上传的文件"))
        
        self.file_chooser = FileChooserListView()
        self.add_widget(self.file_chooser)
        
        self.add_widget(Label(text="文档类型"))
        self.doc_type = TextInput(text="medical_report", multiline=False)
        self.add_widget(self.doc_type)
        
        self.add_widget(Label(text="描述"))
        self.description = TextInput(multiline=True)
        self.add_widget(self.description)
        
        upload_btn = Button(text="上传文档", size_hint_y=None, height=50)
        upload_btn.bind(on_press=self.upload_document)
        self.add_widget(upload_btn)
        
        self.status_label = Label(text="")
        self.add_widget(self.status_label)
        
    def upload_document(self, instance):
        if not self.file_chooser.selection:
            self.status_label.text = "请选择文件"
            return
            
        file_path = self.file_chooser.selection[0]
        file_name = os.path.basename(file_path)
        
        with open(file_path, 'rb') as f:
            file_data = f.read()
        
        app = App.get_running_app()
        url = app.api_client.base_url + "/mobile/upload"
        
        # 创建multipart/form-data请求
        import uuid
        boundary = uuid.uuid4().hex
        
        body = b''
        # 添加文档类型
        body += f'--{boundary}\r\n'.encode()
        body += f'Content-Disposition: form-data; name="documentType"\r\n\r\n'.encode()
        body += f'{self.doc_type.text}\r\n'.encode()
        
        # 添加描述
        body += f'--{boundary}\r\n'.encode()
        body += f'Content-Disposition: form-data; name="description"\r\n\r\n'.encode()
        body += f'{self.description.text}\r\n'.encode()
        
        # 添加文件
        body += f'--{boundary}\r\n'.encode()
        body += f'Content-Disposition: form-data; name="file"; filename="{file_name}"\r\n'.encode()
        body += f'Content-Type: application/octet-stream\r\n\r\n'.encode()
        body += file_data
        body += f'\r\n--{boundary}--\r\n'.encode()
        
        headers = {}
        if app.api_client.access_token:
            headers["Authorization"] = f"Bearer {app.api_client.access_token}"
        headers["Content-Type"] = f"multipart/form-data; boundary={boundary}"
        
        self.status_label.text = "正在上传..."
        
        UrlRequest(
            url,
            req_headers=headers,
            req_body=body,
            on_success=self.on_upload_success,
            on_error=self.on_upload_error,
            on_failure=self.on_upload_error
        )
        
    def on_upload_success(self, request, result):
        self.status_label.text = f"上传成功: {result.get('message', '')}"
        
    def on_upload_error(self, request, error):
        self.status_label.text = f"上传失败: {error}"
```

### 5. 主应用示例

```python
from kivy.app import App
from kivy.uix.screenmanager import ScreenManager, Screen

class HealthApp(App):
    def build(self):
        # 初始化API客户端
        self.api_client = ApiClient()
        
        # 创建屏幕管理器
        sm = ScreenManager()
        
        # 添加登录屏幕
        login_screen = Screen(name='login')
        login_screen.add_widget(LoginScreen())
        sm.add_widget(login_screen)
        
        # 添加健康记录屏幕
        records_screen = Screen(name='records')
        records_screen.add_widget(HealthRecordsScreen())
        sm.add_widget(records_screen)
        
        # 添加文档上传屏幕
        upload_screen = Screen(name='upload')
        upload_screen.add_widget(DocumentUploadScreen())
        sm.add_widget(upload_screen)
        
        return sm

if __name__ == '__main__':
    HealthApp().run()
```

## 注意事项

1. 所有API请求都应添加错误处理机制，处理网络错误、服务器错误和认证失败等情况。
2. 移动端应用应实现适当的缓存机制，减少不必要的网络请求。
3. 敏感数据传输应使用HTTPS协议，确保数据安全。
4. 移动端应用应处理网络连接不稳定的情况，提供离线模式和数据同步功能。
5. 实现适当的重试机制，处理临时性网络故障。
6. 大文件上传应考虑分片上传，并显示上传进度。