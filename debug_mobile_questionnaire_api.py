#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'YUN', 'backend'))

import sqlite3
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import logging
import requests
import json

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_api_directly():
    """直接测试API调用"""
    logger.info("=== 直接测试移动端问卷API ===")
    
    # API配置
    base_url = "http://127.0.0.1:8000"
    
    # 1. 先登录获取token
    login_data = {
        "username": "SM_006",
        "password": "123456"
    }
    
    try:
        login_response = requests.post(f"{base_url}/api/mobile/login", json=login_data)
        logger.info(f"登录响应状态码: {login_response.status_code}")
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            logger.info(f"登录成功: {login_result}")
            
            if 'access_token' in login_result:
                token = login_result['access_token']
                
                # 2. 使用token调用问卷API
                headers = {
                    "Authorization": f"Bearer {token}",
                    "Content-Type": "application/json"
                }
                
                questionnaire_response = requests.get(f"{base_url}/api/mobile/questionnaires", headers=headers)
                logger.info(f"问卷API响应状态码: {questionnaire_response.status_code}")
                logger.info(f"问卷API响应内容: {questionnaire_response.text}")
                
                if questionnaire_response.status_code == 200:
                    questionnaire_result = questionnaire_response.json()
                    logger.info(f"问卷API结果: {json.dumps(questionnaire_result, indent=2, ensure_ascii=False)}")
                else:
                    logger.error(f"问卷API调用失败: {questionnaire_response.text}")
            else:
                logger.error(f"登录响应中没有access_token: {login_result}")
        else:
            logger.error(f"登录失败: {login_response.text}")
            
    except Exception as e:
        logger.error(f"API测试失败: {e}")

def check_database_directly():
    """直接检查数据库中的分发记录"""
    logger.info("\n=== 检查数据库分发记录 ===")
    
    db_path = "c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db"
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 查询用户ID=2的分发记录
    cursor.execute("""
        SELECT qd.id, qd.questionnaire_id, qd.user_id, qd.status, qd.due_date,
               q.name, q.status as questionnaire_status, q.questionnaire_type
        FROM questionnaire_distributions qd
        JOIN questionnaires q ON qd.questionnaire_id = q.id
        WHERE qd.user_id = 2
        ORDER BY qd.id
    """)
    
    distributions = cursor.fetchall()
    
    logger.info(f"用户ID=2的分发记录: {len(distributions)} 条")
    for dist in distributions:
        logger.info(f"  分发ID={dist[0]}, 问卷ID={dist[1]}, 状态={dist[3]}, 问卷名称={dist[5]}, 问卷状态={dist[6]}, 类型={dist[7]}")
    
    # 检查问卷模板
    cursor.execute("""
        SELECT qd.id, qd.questionnaire_id, q.template_id, qt.name as template_name
        FROM questionnaire_distributions qd
        JOIN questionnaires q ON qd.questionnaire_id = q.id
        LEFT JOIN questionnaire_templates qt ON q.template_id = qt.id
        WHERE qd.user_id = 2
    """)
    
    template_info = cursor.fetchall()
    
    logger.info(f"\n问卷模板信息:")
    for info in template_info:
        logger.info(f"  分发ID={info[0]}, 问卷ID={info[1]}, 模板ID={info[2]}, 模板名称={info[3]}")
    
    conn.close()

def simulate_api_logic():
    """模拟API逻辑"""
    logger.info("\n=== 模拟API逻辑 ===")
    
    try:
        # 导入必要的模块
        from app.database import get_db
        from app.models.distribution import QuestionnaireDistribution
        from app.models.questionnaire import Questionnaire
        from app.models.questionnaire_template import QuestionnaireTemplate, QuestionnaireTemplateQuestion
        
        # 创建数据库会话
        db_path = "c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db"
        engine = create_engine(f"sqlite:///{db_path}")
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        # 模拟用户ID=2
        user_id = 2
        
        # 查询分发记录
        distributions = db.query(QuestionnaireDistribution).filter(
            QuestionnaireDistribution.user_id == user_id
        ).all()
        
        logger.info(f"ORM查询到 {len(distributions)} 条分发记录")
        
        result = []
        for dist in distributions:
            logger.info(f"处理分发记录: ID={dist.id}, 问卷ID={dist.questionnaire_id}")
            
            # 获取问卷
            questionnaire = db.query(Questionnaire).filter(Questionnaire.id == dist.questionnaire_id).first()
            if questionnaire:
                logger.info(f"  找到问卷: {questionnaire.name}, 模板ID={getattr(questionnaire, 'template_id', 'None')}")
                
                # 获取问卷模板
                template = None
                if hasattr(questionnaire, 'template_id') and questionnaire.template_id:
                    template = db.query(QuestionnaireTemplate).filter(
                        QuestionnaireTemplate.id == questionnaire.template_id
                    ).first()
                    
                if template:
                    logger.info(f"  找到模板: {template.name}")
                    
                    # 获取模板问题
                    questions = db.query(QuestionnaireTemplateQuestion).filter(
                        QuestionnaireTemplateQuestion.template_id == template.id
                    ).order_by(QuestionnaireTemplateQuestion.order).all()
                    
                    logger.info(f"  模板问题数量: {len(questions)}")
                    
                    questionnaire_data = {
                        "id": questionnaire.id,
                        "distribution_id": dist.id,
                        "name": questionnaire.name,
                        "questionnaire_type": questionnaire.questionnaire_type,
                        "status": questionnaire.status,
                        "template_found": True
                    }
                    result.append(questionnaire_data)
                else:
                    logger.warning(f"  未找到模板，跳过问卷 {questionnaire.id}")
            else:
                logger.warning(f"  未找到问卷ID {dist.questionnaire_id}")
        
        logger.info(f"\n最终结果: {len(result)} 条问卷")
        for item in result:
            logger.info(f"  问卷: {item}")
        
        db.close()
        
    except Exception as e:
        logger.error(f"模拟API逻辑失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    check_database_directly()
    simulate_api_logic()
    test_api_directly()

if __name__ == "__main__":
    main()