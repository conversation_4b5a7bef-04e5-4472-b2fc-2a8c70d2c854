#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化测试脚本 - 验证核心组件功能
"""

import sys
import os
import traceback

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试核心模块导入"""
    print("\n=== 测试模块导入 ===")
    
    try:
        print("导入 theme...")
        from theme import OptimizedColorPalette, OptimizedTheme
        print("✓ theme_optimized 导入成功")
        
        # 测试主题创建
        theme = OptimizedTheme()
        print("✓ OptimizedTheme 创建成功")
        
    except Exception as e:
        print(f"✗ theme_optimized 导入失败: {e}")
        traceback.print_exc()
    
    try:
        print("\n导入 api_config_optimized...")
        from api.api_config_optimized import OptimizedAPIConfig
        print("✓ api_config_optimized 导入成功")
        
        # 测试API配置创建
        api_config = OptimizedAPIConfig()
        print("✓ OptimizedAPIConfig 创建成功")
        
    except Exception as e:
        print(f"✗ api_config_optimized 导入失败: {e}")
        traceback.print_exc()
    
    try:
        print("\n导入 api_client_optimized...")
        from api.api_client import OptimizedAPIClient
        print("✓ api_client_optimized 导入成功")
        
        # 测试API客户端创建
        api_client = OptimizedAPIClient()
        print("✓ OptimizedAPIClient 创建成功")
        
    except Exception as e:
        print(f"✗ api_client_optimized 导入失败: {e}")
        traceback.print_exc()
    
    try:
        print("\n导入 performance_monitor...")
        from utils.performance_monitor import PerformanceMonitor
        print("✓ performance_monitor 导入成功")
        
        # 测试性能监控器创建
        monitor = PerformanceMonitor()
        print("✓ PerformanceMonitor 创建成功")
        
    except Exception as e:
        print(f"✗ performance_monitor 导入失败: {e}")
        traceback.print_exc()

def test_main_app():
    """测试主应用启动"""
    print("\n=== 测试主应用 ===")
    
    try:
        print("导入 main_optimized...")
        from main_optimized import OptimizedHealthApp
        print("✓ main_optimized 导入成功")
        
        # 测试应用类创建（不运行）
        app_class = OptimizedHealthApp
        print("✓ OptimizedHealthApp 类可用")
        
    except Exception as e:
        print(f"✗ main_optimized 导入失败: {e}")
        traceback.print_exc()

def test_homepage_screen():
    """测试主页屏幕"""
    print("\n=== 测试主页屏幕 ===")
    
    try:
        print("导入 homepage_screen_optimized...")
        from screens.homepage_screen_optimized import OptimizedHomepageScreen
        print("✓ homepage_screen_optimized 导入成功")
        
    except Exception as e:
        print(f"✗ homepage_screen_optimized 导入失败: {e}")
        traceback.print_exc()

def main():
    """主测试函数"""
    print("健康管理移动应用 - 简化测试")
    print("=" * 50)
    
    # 运行各项测试
    test_imports()
    test_main_app()
    test_homepage_screen()
    
    print("\n=== 测试完成 ===")
    print("如果所有组件都显示 ✓，说明核心功能正常")

if __name__ == "__main__":
    main()
