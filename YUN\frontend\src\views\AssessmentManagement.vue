<template>
  <div class="assessment-management">
    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <h2>评估量表管理</h2>
          <div>
            <el-button type="primary" @click="openTemplateDialog">
              <el-icon><Plus /></el-icon>
              创建量表
            </el-button>
            <el-button type="primary" @click="openQuestionnaireGenerator">
              <el-icon><DocumentAdd /></el-icon>
              量表生成器
            </el-button>
          </div>
        </div>
      </template>

      <!-- 量表列表 -->
      <el-tabs v-model="activeTab" class="assessment-tabs">
        <el-tab-pane label="全部量表" name="all">
          <assessment-list
            :assessments="filteredAssessments"
            :loading="loading"
            @edit="handleEdit"
            @delete="handleDelete"
            @distribute="handleDistribute"
            @view-results="handleViewResults"
            @review="handleReview"
            @push="handlePush"
            @preview="handlePreview"
          />
        </el-tab-pane>
        <el-tab-pane label="已发布" name="published">
          <assessment-list
            :assessments="filteredAssessments"
            :loading="loading"
            @edit="handleEdit"
            @delete="handleDelete"
            @distribute="handleDistribute"
            @view-results="handleViewResults"
            @review="handleReview"
            @push="handlePush"
            @preview="handlePreview"
          />
        </el-tab-pane>
        <el-tab-pane label="标准量表" name="standard">
          <assessment-list
            :assessments="filteredAssessments"
            :loading="loading"
            @edit="handleEdit"
            @delete="handleDelete"
            @distribute="handleDistribute"
            @view-results="handleViewResults"
            @review="handleReview"
            @push="handlePush"
            @preview="handlePreview"
          />
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 创建/编辑量表对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEditing ? '编辑量表' : '使用模板新建量表'"
      width="80%"
      :before-close="handleDialogClose"
    >
      <assessment-editor
        v-if="dialogVisible"
        :assessment="currentAssessment"
        :is-editing="isEditing"
        @save="saveAssessment"
        @cancel="dialogVisible = false"
      />
    </el-dialog>

    <!-- 分发量表对话框 -->
    <el-dialog
      v-model="distributeDialogVisible"
      title="分发量表"
      width="60%"
    >
      <assessment-distributor
        v-if="distributeDialogVisible"
        :assessment="currentAssessment"
        @distribute="confirmDistribute"
        @cancel="distributeDialogVisible = false"
      />
    </el-dialog>

    <!-- 查看量表结果对话框 -->
    <el-dialog
      v-model="resultsDialogVisible"
      title="量表结果"
      width="80%"
    >
      <assessment-results
        v-if="resultsDialogVisible"
        :assessment="currentAssessment"
        @close="resultsDialogVisible = false"
      />
    </el-dialog>

    <!-- 选择量表模板对话框 -->
    <el-dialog v-model="templateDialogVisible" title="选择量表模板" width="40%">
      <el-input v-model="templateSearch" placeholder="搜索模板名称" clearable style="margin-bottom: 12px;" />
      <el-radio-group v-model="selectedTemplateId" style="display:block;">
        <el-radio :label="0">空白新建</el-radio>
        <div v-for="tpl in filteredTemplates" :key="tpl.id" style="display:flex;align-items:center;justify-content:space-between;margin-bottom:8px;">
          <el-radio :label="tpl.id">{{ tpl.name }}（{{ tpl.type }}）</el-radio>
          <div>
            <el-button size="small" type="primary" @click.stop="onEditTemplate(tpl.id)">编辑</el-button>
            <el-button size="small" @click.stop="onUseTemplate(tpl.id)">使用</el-button>
          </div>
        </div>
      </el-radio-group>
      <template #footer>
          <el-button @click="templateDialogVisible=false">取消</el-button>
          <el-button @click="openQuestionnaireGenerator">量表生成器</el-button>
          <el-button type="primary" @click="handleTemplateSelect">确定</el-button>
        </template>
    </el-dialog>

    <!-- 量表生成器对话框 -->
    <questionnaire-generator
      v-model="questionnaireGeneratorVisible"
      @success="handleQuestionnaireCreated"
    />
    
    <!-- 量表审核预览对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      title="量表审核预览"
      width="80%"
      destroy-on-close
    >
      <div v-if="previewDialogVisible" class="assessment-preview-container">
        <assessment-preview
          :assessment="currentAssessment"
          :preview-mode="true"
        />
        <div class="review-actions">
          <el-button type="danger" @click="confirmReview(false)">返修</el-button>
          <el-button type="success" @click="confirmReview(true)">审核通过</el-button>
        </div>
      </div>
    </el-dialog>
    
    <!-- 量表简单预览对话框 -->
    <el-dialog
      v-model="simplePreviewDialogVisible"
      title="量表预览"
      width="80%"
      destroy-on-close
    >
      <div v-if="simplePreviewDialogVisible" class="assessment-preview-container">
        <assessment-preview
          :assessment="currentAssessment"
          :preview-mode="true"
        />
        <div class="preview-actions">
          <el-button type="primary" @click="simplePreviewDialogVisible = false">关闭</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Search, Edit, Delete, Share, View, DocumentAdd } from '@element-plus/icons-vue';
import axios from 'axios';
import { useRoute } from 'vue-router';
import { getAssessmentTemplates, getAssessmentTemplate } from '@/api/templates';
import { distributeAssessment, getAssessments } from '@/api/assessment';
import { getStandardAssessments, getStandardAssessment } from '@/api/clinical_scales';

// 导入组件
import AssessmentList from '../components/assessment/AssessmentList.vue';
import AssessmentEditor from '../components/assessment/AssessmentEditor.vue';
import AssessmentDistributor from '../components/assessment/AssessmentDistributor.vue';
import AssessmentResults from '../components/assessment/AssessmentResults.vue';
import AssessmentPreview from '../components/assessment/AssessmentPreview.vue';
import QuestionnaireGenerator from '../components/assessment/QuestionnaireGenerator.vue';

// 路由
const route = useRoute();

// 状态变量
const loading = ref(false);
const assessments = ref([]);
const activeTab = ref('all');
const dialogVisible = ref(false);
const distributeDialogVisible = ref(false);
const resultsDialogVisible = ref(false);
const isEditing = ref(false);
const currentAssessment = ref({});
const templateDialogVisible = ref(false);
const templateSearch = ref('');
const selectedTemplateId = ref(0);
const templates = ref([]);
const questionnaireGeneratorVisible = ref(false);

// 计算属性：根据当前标签页筛选量表
const filteredAssessments = computed(() => {
  if (activeTab.value === 'all') {
    // 显示全部量表
    return assessments.value;
  } else if (activeTab.value === 'published') {
    // 只显示已发布
    return assessments.value.filter(a => a.status === 'published');
  } else if (activeTab.value === 'standard') {
    // 只显示标准量表
    return assessments.value.filter(a => a.is_standard === true);
  }
  return [];
});

const filteredTemplates = computed(() => {
  if (!templateSearch.value) return templates.value;
  return templates.value.filter(t => t.name.includes(templateSearch.value));
});

// 初始化
onMounted(() => {
  currentAssessment.value = {};
  fetchAssessments();
  // 只加载标准量表（limit: -1，全部加载）
  fetchStandardTemplates();
  handleRouteParams();
});

// 加载标准量表模板（只在管理界面加载，limit: -1）
const fetchStandardTemplates = async () => {
  try {
    const response = await getAssessmentTemplates({ limit: -1, nocache: new Date().getTime() });
    templates.value = response.data?.data || response.data || [];
  } catch (error) {
    templates.value = [];
    console.error('加载标准量表模板失败', error);
  }
};

// 处理路由参数
const handleRouteParams = () => {
  const action = route.query.action;
  const id = route.query.id;

  if (action === 'create-template') {
    openTemplateDialog();
  } else if ((action === 'edit-template' || action === 'use-template') && id) {
    // 处理标准量表ID（字符串格式）和普通模板ID（数字格式）
    const templateId = id.startsWith('standard_') ? id : parseInt(id);
    if (action === 'edit-template') {
      handleEditTemplate(templateId);
    } else if (action === 'use-template') {
      handleUseTemplate(templateId);
    }
  }
};

// 标准化模板字段映射函数
function mapAssessmentTemplateToForm(templateData) {
  return {
    id: templateData.id,
    title: templateData.name || '',
    description: templateData.description || '',
    category: templateData.category || '',
    assessment_type: templateData.assessment_type || 'self',
    status: templateData.status || (templateData.is_active ? 'published' : 'draft'),
    question_count: Array.isArray(templateData.questions) ? templateData.questions.length : templateData.question_count || 0,
    response_count: templateData.response_count || 0,
    updated_at: templateData.updated_at || new Date().toISOString(),
    questions: Array.isArray(templateData.questions) ? templateData.questions.map(q => ({
      id: q.question_id || q.id || '',
      question_id: q.question_id || q.id || '',
      question_text: q.question_text || q.text || '',
      question_type: q.question_type || 'single_choice',
      required: q.is_required ?? q.required ?? true,
      is_required: q.is_required ?? q.required ?? true,
      options: q.options || [],
      order: q.order || 0
    })) : templateData.questions || [],
    instructions: templateData.instructions || '',
    scoring_method: templateData.scoring_method || 'sum',
    result_ranges: templateData.result_ranges || [],
    revision_reason: templateData.revision_reason || ''
  };
}

// 获取量表列表（包含标准量表和用户创建的量表）
const fetchAssessments = async () => {
  loading.value = true;
  try {
    console.log('开始获取量表列表...');
    
    // 并行获取用户创建的量表和标准量表
    const [userAssessmentsRes, standardAssessmentsRes] = await Promise.all([
      getAssessments({ page: 1, page_size: 100 }),
      getStandardAssessments().catch(err => {
        console.warn('获取标准量表失败:', err);
        return { data: { data: [] } };
      })
    ]);
    
    // 处理用户创建的量表
    const userList = userAssessmentsRes.data?.data || userAssessmentsRes.data || [];
    console.log(`获取到 ${userList.length} 个用户创建的量表`);
    
    // 处理标准量表 - 优先从records字段获取数据
    const standardList = standardAssessmentsRes.data?.records || standardAssessmentsRes.data?.data || standardAssessmentsRes.data || [];
    console.log(`获取到 ${standardList.length} 个标准量表`);
    
    // 合并并去重
    const uniqueAssessments = new Map();
    
    // 添加用户创建的量表
    if (Array.isArray(userList)) {
      userList.forEach(assessment => {
        uniqueAssessments.set(assessment.id, {
          ...assessment,
          source: 'user',
          is_standard: false
        });
      });
    }
    
    // 添加标准量表
    if (Array.isArray(standardList)) {
      standardList.forEach(tpl => {
        // 为标准量表生成唯一ID
        const standardId = `standard_${tpl.id}`;
        const mappedAssessment = {
          id: standardId,
          title: tpl.name || tpl.title || '未命名量表',
          description: tpl.description || '',
          category: '标准量表',
          assessment_type: tpl.assessment_type || 'self',
          status: 'published', // 标准量表默认为已发布状态
          question_count: tpl.question_count || 0,
          response_count: 0,
          updated_at: new Date().toISOString(),
          questions: tpl.questions || [],
          instructions: tpl.instructions || '',
          scoring_method: tpl.scoring_method || 'sum',
          result_ranges: tpl.result_ranges || [],
          revision_reason: '',
          source: 'standard', // 标记为标准量表
          is_standard: true,
          template_key: tpl.template_key
        };
        console.log(`标准量表ID ${standardId}, 名称: ${mappedAssessment.title}`);
        uniqueAssessments.set(standardId, mappedAssessment);
      });
    }
    
    // 先保存之前选中的量表ID
    const currentAssessmentId = currentAssessment.value?.id;
    assessments.value = Array.from(uniqueAssessments.values());
    console.log(`成功加载 ${assessments.value.length} 个量表（用户: ${userList.length}, 标准: ${standardList.length}）`);
    
    // 如果在操作返修，确保对应量表的状态为draft
    if (currentAssessmentId) {
      const updatedIndex = assessments.value.findIndex(a => a.id === currentAssessmentId);
      if (updatedIndex !== -1 && activeTab.value === 'draft') {
          // 确保该量表在draft状态
          if (assessments.value[updatedIndex].status !== 'draft') {
            console.log(`强制将量表 ${currentAssessmentId} 状态更新为draft`);
            assessments.value[updatedIndex].status = 'draft';
          }
        }
      }
  } catch (error) {
    console.error('获取量表列表失败:', error);
    assessments.value = [];
    ElMessage.error('获取量表列表失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 编辑模板
const handleEditTemplate = async (templateId) => {
  // 检查是否为标准量表
  if (typeof templateId === 'string' && templateId.startsWith('standard_')) {
    try {
      loading.value = true;
      const response = await getStandardAssessment(templateId);
      const templateData = response.data.data || response.data;
      if (templateData) {
        isEditing.value = false; // 强制设置为复制模式
        currentAssessment.value = {
          ...mapAssessmentTemplateToForm(templateData),
          id: null,
          title: `${templateData.name} - 副本`,
          source: 'user',
          is_standard: false
        };
        dialogVisible.value = true;
        ElMessage.info('标准量表不可直接编辑，已为您创建副本');
      } else {
        ElMessage.error('未找到该标准量表');
      }
    } catch (error) {
      console.error('获取标准量表详情失败:', error);
      ElMessage.error('获取标准量表详情失败');
    } finally {
      loading.value = false;
    }
    return;
  }
  
  try {
    loading.value = true;
    const response = await getAssessmentTemplate(templateId);
    const templateData = response.data.data || response.data;
    if (templateData) {
      isEditing.value = true;
      currentAssessment.value = mapAssessmentTemplateToForm(templateData);
      dialogVisible.value = true;
    } else {
      ElMessage.error('未找到该模板');
    }
  } catch (error) {
    ElMessage.error('获取模板详情失败');
  } finally {
    loading.value = false;
  }
};

// 使用模板创建新量表
const handleUseTemplate = async (templateId) => {
  try {
    loading.value = true;
    
    // 处理标准量表的情况
    if (typeof templateId === 'string' && templateId.startsWith('standard_')) {
      const response = await getStandardAssessment(templateId);
      const templateData = response.data.data || response.data;
      if (templateData) {
        isEditing.value = false;
        // 复制标准量表数据，但不保留ID和标准标记
        currentAssessment.value = {
          ...mapAssessmentTemplateToForm(templateData),
          id: null,
          title: `${templateData.name} - 副本`,
          source: 'user',
          is_standard: false
        };
        dialogVisible.value = true;
      } else {
        ElMessage.error('未找到该标准量表');
      }
      loading.value = false;
      return;
    }
    
    // 处理普通模板
    const response = await getAssessmentTemplate(templateId);
    const templateData = response.data.data || response.data;
    if (templateData) {
      isEditing.value = false;
      currentAssessment.value = mapAssessmentTemplateToForm(templateData);
      // 创建新量表时不带ID
      currentAssessment.value.id = null;
      dialogVisible.value = true;
    } else {
      ElMessage.error('未找到该模板');
    }
  } catch (error) {
    console.error('获取模板详情失败:', error);
    ElMessage.error('获取模板详情失败');
  } finally {
    loading.value = false;
  }
};

// 打开模板选择对话框时，加载模板列表
function openTemplateDialog() {
  templateDialogVisible.value = true;
  if (templates.value.length === 0) {
    getAssessmentTemplates().then(res => {
      templates.value = res.data?.data || res.data || [];
    });
  }
}

// 处理模板选择
const handleTemplateSelect = () => {
  templateDialogVisible.value = false;
  if (selectedTemplateId.value === 0) {
    // 空白新建
    openCreateDialog();
  } else {
    // 使用模板
    handleUseTemplate(selectedTemplateId.value);
  }
};

// 创建新量表
const openCreateDialog = () => {
  isEditing.value = false;
  currentAssessment.value = {
    title: '',
    description: '',
    assessment_type: 'self',
    questions: [],
    instructions: '',
    scoring_method: 'sum',
    result_ranges: []
  };
  dialogVisible.value = true;
};

// 编辑量表
const handleEdit = async (assessment) => {
  isEditing.value = true;
  loading.value = true;

  try {
    const response = await axios.get(`/api/assessments/${assessment.id}`);
    currentAssessment.value = mapAssessmentTemplateToForm(response.data.data || response.data);
    dialogVisible.value = true;
  } catch (error) {
    console.error('获取量表详情失败:', error);
    ElMessage.error('获取量表详情失败');
  } finally {
    loading.value = false;
  }
};

// 处理删除量表
const handleDelete = (assessment) => {
  ElMessageBox.confirm(
    `确定要删除量表 "${assessment.title}" 吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await axios.delete(`/api/assessments/${assessment.id}`);
      ElMessage.success('删除成功');
      fetchAssessments(); // 刷新列表
    } catch (error) {
      console.error('删除量表失败:', error);
      ElMessage.error('删除量表失败，请稍后重试');
    }
  }).catch(() => {
    // 用户取消删除
  });
};

// 处理分发量表
const handleDistribute = (assessment) => {
  currentAssessment.value = assessment;
  distributeDialogVisible.value = true;
};

// 确认分发量表
const confirmDistribute = async (distributionData) => {
  try {
    let templateId = currentAssessment.value.id;
    
    // 直接使用原始ID，不进行转换
    // 标准量表使用standard_xxx格式，自定义量表使用整数ID
    
    await distributeAssessment(templateId, distributionData);
    ElMessage.success('量表分发成功');
    distributeDialogVisible.value = false;
    fetchAssessments(); // 刷新列表
  } catch (error) {
    console.error('量表分发失败:', error);
    ElMessage.error('量表分发失败，请稍后重试');
  }
};

// 处理查看量表结果
const handleViewResults = (assessment) => {
  currentAssessment.value = assessment;
  resultsDialogVisible.value = true;
};

// 保存量表
const saveAssessment = async (assessmentData) => {
  try {
    if (isEditing.value) {
      // 更新量表
      await axios.put(`/api/assessments/${assessmentData.id}`, assessmentData);
      ElMessage.success('量表更新成功');
    } else {
      // 创建量表
      await axios.post('/api/assessments', assessmentData);
      ElMessage.success('量表创建成功');
    }

    dialogVisible.value = false;
    fetchAssessments(); // 刷新列表
  } catch (error) {
    console.error('保存量表失败:', error);
    ElMessage.error('保存量表失败，请稍后重试');
  }
};

// 处理对话框关闭
const handleDialogClose = (done) => {
  ElMessageBox.confirm(
    '确定要关闭吗？未保存的更改将会丢失',
    '关闭确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    done();
  }).catch(() => {
    // 用户取消关闭
  });
};

// 量表预览对话框
const previewDialogVisible = ref(false);

// 处理审核
const handleReview = async (assessment) => {
  if (!assessment || !assessment.id) {
    ElMessage.error('量表信息不完整，无法进行审核');
    return;
  }
  try {
    // 通过API获取标准量表详情
    const res = await axios.get(`/api/templates/assessment-templates/${assessment.id}`);
    if (res.data && res.data.data) {
      currentAssessment.value = adaptAssessmentDetail(res.data.data);
      previewDialogVisible.value = true;
    } else {
      ElMessage.error('未找到该量表详情');
    }
  } catch (error) {
    ElMessage.error('获取量表详情失败');
  }
};

// 单纯预览量表（不进行审核）
const simplePreviewDialogVisible = ref(false);

const handlePreview = async (assessment) => {
  if (!assessment || !assessment.id) {
    ElMessage.error('量表信息不完整，无法预览');
    return;
  }
  try {
    // 标准量表和用户量表的API路径不同
    const apiPath = assessment.is_standard 
      ? `/api/clinical-scales/standard-assessments/${assessment.template_key || assessment.id}` 
      : `/api/assessments/${assessment.id}`;
    
    const res = await axios.get(apiPath);
    if (res.data && (res.data.data || res.data)) {
      currentAssessment.value = adaptAssessmentDetail(res.data.data || res.data);
      simplePreviewDialogVisible.value = true;
    } else {
      ElMessage.error('未找到该量表详情');
    }
  } catch (error) {
    console.error('获取量表详情失败:', error);
    ElMessage.error('获取量表详情失败');
  }
};

// 字段适配函数，兼容标准量表结构
function adaptAssessmentDetail(raw) {
  return {
    title: raw.name || raw.title || '未命名',
    description: raw.description || '',
    category: raw.category || '',
    assessment_type: raw.assessment_type || 'self',
    status: raw.status || 'draft',
    questions: (raw.questions || []).map(q => ({
      id: q.question_id || q.id,
      question_text: q.question_text || q.text,
      question_type: q.question_type,
      required: q.is_required ?? q.required ?? true,
      options: q.options || [],
      order: q.order || 0
    })),
    instructions: raw.instructions || '',
    scoring_method: raw.scoring_method || '',
    result_ranges: raw.result_ranges || [],
    updated_at: raw.updated_at || '',
    revision_reason: raw.revision_reason || ''
  };
}

// 确认审核操作
const confirmReview = (approved) => {
  previewDialogVisible.value = false;
  
  if (!currentAssessment.value || !currentAssessment.value.id) {
    ElMessage.error('量表信息不完整，无法进行审核操作');
    return;
  }
  
  const assessmentId = currentAssessment.value.id;
  const assessmentTitle = currentAssessment.value.title || '未命名量表';
  
  if (approved) {
    ElMessageBox.confirm(
      `确认通过量表"${assessmentTitle}"审核？`,
      '审核确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'success'
      }
    ).then(async () => {
      try {
        console.log('发送审核通过请求, 量表ID:', assessmentId);
        const response = await axios.post(`/api/assessments/${assessmentId}/review`, {
          status: 'published'
        });
        console.log('审核通过API响应:', response);
        
        ElMessage.success('审核通过，量表已变为正式状态');
        
        // 更新本地状态
        const index = assessments.value.findIndex(a => a.id === assessmentId);
        if (index !== -1) {
          console.log('更新本地量表状态为published');
          assessments.value[index].status = 'published';
        }
        
        // 强制刷新列表
        await fetchAssessments();
        
        // 如果当前在标准量表tab，切换到全部tab
        if (activeTab.value === 'standard') {
          activeTab.value = 'all';
        }
      } catch (error) {
        console.error('审核失败:', error);
        ElMessage.error('审核操作失败');
      }
    });
  } else {
    ElMessageBox.prompt('请输入返修原因', '返修原因', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    }).then(({ value }) => {
      console.log('发送返修请求, 量表ID:', assessmentId, '原因:', value);
      
      axios.post(`/api/assessments/${assessmentId}/review`, {
        status: 'draft',
        revision_reason: value || '需要修改'
      }).then(response => {
        console.log('返修API响应:', response);
        
        // 检查API是否正确处理了状态更新
        if (response && response.data) {
          console.log('返修后的状态数据:', response.data);
        }
        
        ElMessage.success('已退回返修');
        
        // 直接强制更新当前记录状态
        const index = assessments.value.findIndex(a => a.id === assessmentId);
        if (index !== -1) {
          console.log('更新本地量表状态从', assessments.value[index].status, '到 draft');
          assessments.value[index].status = 'draft';
          // 保存返修原因
          assessments.value[index].revision_reason = value || '需要修改';
        } else {
          console.warn('未找到ID为', assessmentId, '的量表');
        }
        
        // 切换到全部tab
        activeTab.value = 'all';
        
        // 强制刷新整个量表列表
        fetchAssessments().then(() => {
          // 再次确认状态更新
          setTimeout(() => {
            const updatedIndex = assessments.value.findIndex(a => a.id === assessmentId);
            if (updatedIndex !== -1) {
              console.log('刷新后量表状态:', assessments.value[updatedIndex].status);
              if (assessments.value[updatedIndex].status !== 'draft') {
                // 强制设置为draft状态
                assessments.value[updatedIndex].status = 'draft';
                // 确保返修原因被保存
                assessments.value[updatedIndex].revision_reason = value || '需要修改';
                console.log('强制更新量表状态为draft');
              }
            }
          }, 500);
        });
      }).catch(error => {
        console.error('返修操作失败:', error);
        ElMessage.error('返修操作失败');
      });
    }).catch(() => {
      ElMessage.info('已取消返修操作');
    });
  }
};

function onEditTemplate(templateId) {
  templateDialogVisible.value = false;
  handleEditTemplate(templateId);
}
function onUseTemplate(templateId) {
  templateDialogVisible.value = false;
  handleUseTemplate(templateId);
}

// 打开量表生成器
const openQuestionnaireGenerator = () => {
  questionnaireGeneratorVisible.value = true;
};

// 处理量表生成器创建的量表
const handleQuestionnaireCreated = (assessmentData) => {
  questionnaireGeneratorVisible.value = false;
  templateDialogVisible.value = false;
  isEditing.value = false;
  currentAssessment.value = mapAssessmentTemplateToForm(assessmentData);
  dialogVisible.value = true;
  ElMessage.success('量表创建成功');
};
</script>

<style scoped>
.assessment-management {
  padding: 20px;
  background-color: #f0f2f5;
  min-height: calc(100vh - 60px);
}

.main-card {
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  color: #303133;
}

.assessment-tabs {
  margin-top: 20px;
}

/* 审核预览相关样式 */
.assessment-preview-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.review-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}
</style>
