{"test_summary": {"total_tests": 3, "successful_tests": 0, "failed_tests": 3, "warning_tests": 0, "success_rate": "0.0%", "timestamp": "2025-07-01T23:12:52.393526"}, "test_results": [{"test_name": "后端健康检查", "status": "failed", "details": "连接失败: HTTPConnectionPool(host='localhost', port=8006): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000251241B5010>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))", "timestamp": "2025-07-01T23:12:48.298548"}, {"test_name": "前端可访问性", "status": "failed", "details": "连接失败: HTTPConnectionPool(host='localhost', port=8080): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000025124152AD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))", "timestamp": "2025-07-01T23:12:52.392120"}, {"test_name": "系统测试", "status": "failed", "details": "后端服务不可用，跳过其他测试", "timestamp": "2025-07-01T23:12:52.392933"}], "recommendations": ["检查失败的测试项目，确保相关服务正常运行", "重启后端服务并检查配置", "重启前端服务并检查构建"]}