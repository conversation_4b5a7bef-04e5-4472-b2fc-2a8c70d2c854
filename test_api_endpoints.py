#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API端点和数据
"""

import requests
import json
import sys
import os

# 添加后端路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'YUN', 'backend'))

def test_api_endpoint(url, description):
    """测试API端点"""
    print(f"\n=== 测试 {description} ===")
    print(f"URL: {url}")
    
    try:
        response = requests.get(url, timeout=5)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"响应数据类型: {type(data)}")
                if isinstance(data, list):
                    print(f"数据数量: {len(data)}")
                    if len(data) > 0:
                        print(f"第一条数据: {json.dumps(data[0], ensure_ascii=False, indent=2)}")
                elif isinstance(data, dict):
                    print(f"响应数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
                else:
                    print(f"响应数据: {data}")
            except json.JSONDecodeError:
                print(f"响应内容: {response.text[:500]}")
        else:
            print(f"错误响应: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("连接失败 - 服务器可能未运行")
    except requests.exceptions.Timeout:
        print("请求超时")
    except Exception as e:
        print(f"请求异常: {e}")

def test_database_direct():
    """直接测试数据库"""
    print("\n=== 直接测试数据库 ===")
    
    try:
        from app.db.database import SessionLocal
        from app.models.assessment import Assessment
        from app.models.user import User
        
        db = SessionLocal()
        
        # 检查用户
        users = db.query(User).all()
        print(f"用户数量: {len(users)}")
        for user in users[:3]:  # 只显示前3个
            print(f"  用户: {user.custom_id} - {user.username}")
        
        # 检查评估记录
        assessments = db.query(Assessment).all()
        print(f"评估记录数量: {len(assessments)}")
        for assessment in assessments[:3]:  # 只显示前3个
            print(f"  评估: {assessment.id} - {assessment.name} - 用户:{assessment.custom_id} - 状态:{assessment.status}")
        
        # 检查SM_008的评估记录
        sm008_assessments = db.query(Assessment).filter(Assessment.custom_id == 'SM_008').all()
        print(f"SM_008的评估记录数量: {len(sm008_assessments)}")
        for assessment in sm008_assessments:
            print(f"  SM_008评估: {assessment.id} - {assessment.name} - 状态:{assessment.status}")
        
        db.close()
        
    except Exception as e:
        print(f"数据库测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("开始测试API端点和数据...")
    
    # 测试不同端口的API
    ports = [8000, 8006, 8080, 3000]
    
    for port in ports:
        test_api_endpoint(f"http://localhost:{port}/api/assessments", f"评估量表API (端口{port})")
        test_api_endpoint(f"http://localhost:{port}/api/assessments/records", f"评估记录API (端口{port})")
    
    # 直接测试数据库
    test_database_direct()
    
    print("\n测试完成!")

if __name__ == "__main__":
    main()