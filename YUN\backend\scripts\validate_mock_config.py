#!/usr/bin/env python3
"""
模拟数据配置验证脚本

此脚本验证模拟数据管理系统的配置是否正确，并提供配置建议。

使用方法:
    python scripts/validate_mock_config.py
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
script_dir = Path(__file__).parent
project_root = script_dir.parent
sys.path.insert(0, str(project_root))

try:
    from app.core.mock_data_manager import (
        BackendMockDataManager, 
        is_mock_enabled,
        get_mock_dashboard_stats,
        get_mock_weight_data,
        get_mock_bp_data,
        get_mock_exam_dist_data,
        get_mock_health_index_data,
        get_mock_timeline_data,
        get_mock_service_stats,
        get_mock_public_metrics,
        get_mock_system_metrics,
        get_mock_health_status
    )
except ImportError as e:
    print(f"错误: 无法导入模拟数据管理器: {e}")
    print("请确保在项目根目录下运行此脚本")
    sys.exit(1)


class MockConfigValidator:
    """模拟数据配置验证器"""
    
    def __init__(self):
        self.required_env_vars = {
            'MOCK_DATA_ENABLED': 'bool',
            'MOCK_DASHBOARD_ENABLED': 'bool',
            'MOCK_SERVICE_STATS_ENABLED': 'bool',
            'MOCK_HEALTH_MONITOR_ENABLED': 'bool'
        }
        
        self.validation_results = {
            'environment': [],
            'functionality': [],
            'data_integrity': [],
            'performance': []
        }
    
    def validate_environment_variables(self) -> None:
        """验证环境变量配置"""
        print("🔧 验证环境变量配置...")
        
        for var_name, var_type in self.required_env_vars.items():
            value = os.getenv(var_name)
            
            if value is None:
                self.validation_results['environment'].append({
                    'level': 'warning',
                    'message': f"环境变量 {var_name} 未设置，将使用默认值"
                })
            else:
                if var_type == 'bool':
                    if value.lower() not in ['true', 'false']:
                        self.validation_results['environment'].append({
                            'level': 'error',
                            'message': f"环境变量 {var_name} 的值 '{value}' 不是有效的布尔值"
                        })
                    else:
                        self.validation_results['environment'].append({
                            'level': 'success',
                            'message': f"环境变量 {var_name} = {value}"
                        })
    
    def validate_mock_data_functionality(self) -> None:
        """验证模拟数据功能"""
        print("⚙️ 验证模拟数据功能...")
        
        try:
            manager = BackendMockDataManager()
            
            # 测试基本功能
            if manager.is_enabled:
                self.validation_results['functionality'].append({
                    'level': 'success',
                    'message': '模拟数据管理器已启用'
                })
            else:
                self.validation_results['functionality'].append({
                    'level': 'info',
                    'message': '模拟数据管理器已禁用'
                })
            
            # 测试各种数据类型
            data_types = [
                ('dashboard_stats', get_mock_dashboard_stats),
                ('weight_data', get_mock_weight_data),
                ('bp_data', get_mock_bp_data),
                ('exam_dist_data', get_mock_exam_dist_data),
                ('health_index_data', get_mock_health_index_data),
                ('timeline_data', get_mock_timeline_data),
                ('service_stats', get_mock_service_stats),
                ('public_metrics', get_mock_public_metrics),
                ('system_metrics', get_mock_system_metrics),
                ('health_status', get_mock_health_status)
            ]
            
            for data_type, func in data_types:
                try:
                    data = func()
                    if data is not None:
                        self.validation_results['functionality'].append({
                            'level': 'success',
                            'message': f'{data_type} 数据生成成功'
                        })
                    else:
                        self.validation_results['functionality'].append({
                            'level': 'info',
                            'message': f'{data_type} 数据已禁用或不可用'
                        })
                except Exception as e:
                    self.validation_results['functionality'].append({
                        'level': 'error',
                        'message': f'{data_type} 数据生成失败: {str(e)}'
                    })
        
        except Exception as e:
            self.validation_results['functionality'].append({
                'level': 'error',
                'message': f'模拟数据管理器初始化失败: {str(e)}'
            })
    
    def validate_data_integrity(self) -> None:
        """验证数据完整性"""
        print("🔍 验证数据完整性...")
        
        try:
            manager = BackendMockDataManager()
            
            if not manager.is_enabled:
                self.validation_results['data_integrity'].append({
                    'level': 'info',
                    'message': '模拟数据已禁用，跳过数据完整性检查'
                })
                return
            
            # 验证仪表盘数据结构
            dashboard_data = get_mock_dashboard_stats()
            if dashboard_data:
                required_fields = ['total_records', 'abnormal_records', 'last_checkup_days', 
                                 'completed_questionnaires', 'health_index', 'recent_activities']
                missing_fields = [field for field in required_fields if field not in dashboard_data]
                
                if missing_fields:
                    self.validation_results['data_integrity'].append({
                        'level': 'error',
                        'message': f'仪表盘数据缺少字段: {missing_fields}'
                    })
                else:
                    self.validation_results['data_integrity'].append({
                        'level': 'success',
                        'message': '仪表盘数据结构完整'
                    })
            
            # 验证体重数据结构
            weight_data = get_mock_weight_data()
            if weight_data:
                if 'dates' in weight_data and 'weights' in weight_data:
                    if len(weight_data['dates']) == len(weight_data['weights']):
                        self.validation_results['data_integrity'].append({
                            'level': 'success',
                            'message': '体重数据结构正确'
                        })
                    else:
                        self.validation_results['data_integrity'].append({
                            'level': 'error',
                            'message': '体重数据中日期和数值数量不匹配'
                        })
                else:
                    self.validation_results['data_integrity'].append({
                        'level': 'error',
                        'message': '体重数据缺少必要字段'
                    })
            
            # 验证血压数据结构
            bp_data = get_mock_bp_data()
            if bp_data:
                required_fields = ['dates', 'systolic', 'diastolic']
                if all(field in bp_data for field in required_fields):
                    lengths = [len(bp_data[field]) for field in required_fields]
                    if len(set(lengths)) == 1:  # 所有数组长度相同
                        self.validation_results['data_integrity'].append({
                            'level': 'success',
                            'message': '血压数据结构正确'
                        })
                    else:
                        self.validation_results['data_integrity'].append({
                            'level': 'error',
                            'message': '血压数据中各数组长度不一致'
                        })
                else:
                    self.validation_results['data_integrity'].append({
                        'level': 'error',
                        'message': '血压数据缺少必要字段'
                    })
        
        except Exception as e:
            self.validation_results['data_integrity'].append({
                'level': 'error',
                'message': f'数据完整性验证失败: {str(e)}'
            })
    
    def validate_performance(self) -> None:
        """验证性能"""
        print("⚡ 验证性能...")
        
        import time
        
        try:
            manager = BackendMockDataManager()
            
            if not manager.is_enabled:
                self.validation_results['performance'].append({
                    'level': 'info',
                    'message': '模拟数据已禁用，跳过性能检查'
                })
                return
            
            # 测试数据生成性能
            test_functions = [
                ('dashboard_stats', get_mock_dashboard_stats),
                ('service_stats', get_mock_service_stats),
                ('system_metrics', get_mock_system_metrics)
            ]
            
            for func_name, func in test_functions:
                start_time = time.time()
                for _ in range(10):  # 运行10次
                    func()
                end_time = time.time()
                
                avg_time = (end_time - start_time) / 10 * 1000  # 转换为毫秒
                
                if avg_time < 1:  # 小于1毫秒
                    self.validation_results['performance'].append({
                        'level': 'success',
                        'message': f'{func_name} 平均生成时间: {avg_time:.2f}ms (优秀)'
                    })
                elif avg_time < 10:  # 小于10毫秒
                    self.validation_results['performance'].append({
                        'level': 'success',
                        'message': f'{func_name} 平均生成时间: {avg_time:.2f}ms (良好)'
                    })
                else:
                    self.validation_results['performance'].append({
                        'level': 'warning',
                        'message': f'{func_name} 平均生成时间: {avg_time:.2f}ms (可能需要优化)'
                    })
        
        except Exception as e:
            self.validation_results['performance'].append({
                'level': 'error',
                'message': f'性能验证失败: {str(e)}'
            })
    
    def print_results(self) -> None:
        """打印验证结果"""
        print("\n" + "=" * 60)
        print("📋 验证结果汇总")
        print("=" * 60)
        
        level_icons = {
            'success': '✅',
            'info': 'ℹ️',
            'warning': '⚠️',
            'error': '❌'
        }
        
        level_counts = {'success': 0, 'info': 0, 'warning': 0, 'error': 0}
        
        for category, results in self.validation_results.items():
            if results:
                print(f"\n📂 {category.replace('_', ' ').title()}:")
                for result in results:
                    level = result['level']
                    icon = level_icons.get(level, '•')
                    print(f"  {icon} {result['message']}")
                    level_counts[level] += 1
        
        print(f"\n📊 总结:")
        print(f"  ✅ 成功: {level_counts['success']}")
        print(f"  ℹ️ 信息: {level_counts['info']}")
        print(f"  ⚠️ 警告: {level_counts['warning']}")
        print(f"  ❌ 错误: {level_counts['error']}")
        
        if level_counts['error'] > 0:
            print(f"\n🚨 发现 {level_counts['error']} 个错误，请检查配置")
            return False
        elif level_counts['warning'] > 0:
            print(f"\n⚠️ 发现 {level_counts['warning']} 个警告，建议优化")
            return True
        else:
            print(f"\n🎉 所有验证通过！")
            return True
    
    def generate_config_recommendations(self) -> None:
        """生成配置建议"""
        print("\n💡 配置建议:")
        
        # 检查是否有错误
        has_errors = any(
            result['level'] == 'error' 
            for results in self.validation_results.values() 
            for result in results
        )
        
        if has_errors:
            print("  1. 修复上述错误后重新运行验证")
            print("  2. 检查 .env 文件中的配置")
            print("  3. 确保所有必要的环境变量都已设置")
        else:
            print("  1. 配置看起来很好！")
            print("  2. 可以考虑在生产环境中禁用模拟数据")
            print("  3. 定期运行此验证脚本确保配置正确")
        
        print("\n📚 更多信息:")
        print("  • 查看 docs/MOCK_DATA_GUIDE.md 获取详细文档")
        print("  • 运行 python scripts/migrate_mock_data.py 检查迁移机会")
    
    def run_validation(self) -> bool:
        """运行完整验证"""
        print("🔍 模拟数据配置验证工具")
        print("=" * 60)
        
        self.validate_environment_variables()
        self.validate_mock_data_functionality()
        self.validate_data_integrity()
        self.validate_performance()
        
        success = self.print_results()
        self.generate_config_recommendations()
        
        return success


def main():
    """主函数"""
    validator = MockConfigValidator()
    success = validator.run_validation()
    
    if success:
        print("\n✨ 验证完成！")
        sys.exit(0)
    else:
        print("\n💥 验证失败，请检查配置")
        sys.exit(1)


if __name__ == "__main__":
    main()