2025-07-30 10:25:43.131 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.0%, CPU使用率 85.7%
2025-07-30 10:25:44.689 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 86.4%, 内存: 65.4%, 磁盘: 94.3%
2025-07-30 10:25:58.303 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.2%, CPU使用率 97.0%
2025-07-30 10:26:13.416 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.9%, CPU使用率 54.2%
2025-07-30 10:26:28.524 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.2%, CPU使用率 72.0%
2025-07-30 10:26:43.636 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.1%, CPU使用率 33.3%
2025-07-30 10:26:46.479 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 100.0%, 内存: 67.2%, 磁盘: 94.3%
2025-07-30 10:26:58.768 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.9%, CPU使用率 100.0%
2025-07-30 10:27:13.886 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.5%, CPU使用率 76.0%
2025-07-30 10:27:28.994 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.8%, CPU使用率 91.7%
2025-07-30 10:27:44.109 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.1%, CPU使用率 91.7%
2025-07-30 10:27:47.826 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 66.2%, 内存: 65.1%, 磁盘: 94.3%
2025-07-30 10:27:59.216 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.1%, CPU使用率 29.2%
2025-07-30 10:28:14.385 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.4%, CPU使用率 72.4%
2025-07-30 10:28:29.490 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.9%, CPU使用率 4.2%
2025-07-30 10:28:44.597 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.4%, CPU使用率 14.3%
2025-07-30 10:28:48.850 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 24.2%, 内存: 60.3%, 磁盘: 94.3%
2025-07-30 10:28:59.702 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.4%, CPU使用率 8.3%
2025-07-30 10:29:14.808 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.4%, CPU使用率 16.7%
2025-07-30 10:29:29.913 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.3%, CPU使用率 0.0%
2025-07-30 10:29:45.024 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.3%, CPU使用率 53.6%
2025-07-30 10:29:49.879 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 27.7%, 内存: 60.4%, 磁盘: 94.3%
2025-07-30 10:30:00.160 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.4%, CPU使用率 55.6%
2025-07-30 10:30:15.267 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.4%, CPU使用率 0.0%
2025-07-30 10:30:30.375 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.5%, CPU使用率 78.6%
2025-07-30 10:30:45.481 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.3%, CPU使用率 4.2%
2025-07-30 10:30:50.901 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 30.7%, 内存: 60.4%, 磁盘: 94.3%
2025-07-30 10:31:00.586 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.6%, CPU使用率 3.8%
2025-07-30 10:31:15.693 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.5%, CPU使用率 14.3%
2025-07-30 10:31:30.798 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.4%, CPU使用率 7.7%
2025-07-30 10:31:45.904 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.6%, CPU使用率 16.7%
2025-07-30 10:31:51.922 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 9.0%, 内存: 60.4%, 磁盘: 94.3%
2025-07-30 10:32:01.010 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.4%, CPU使用率 8.3%
2025-07-30 10:32:16.116 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.5%, CPU使用率 0.0%
2025-07-30 10:32:31.226 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.7%, CPU使用率 55.6%
2025-07-30 10:32:46.332 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.8%, CPU使用率 0.0%
2025-07-30 10:32:52.945 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 19.1%, 内存: 60.8%, 磁盘: 94.3%
2025-07-30 10:33:01.437 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.8%, CPU使用率 7.7%
2025-07-30 10:33:16.543 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.8%, CPU使用率 4.2%
2025-07-30 10:33:31.649 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.7%, CPU使用率 0.0%
2025-07-30 10:33:46.754 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.7%, CPU使用率 17.9%
2025-07-30 10:33:53.969 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 16.7%, 内存: 60.5%, 磁盘: 94.3%
2025-07-30 10:34:01.859 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.4%, CPU使用率 23.1%
2025-07-30 10:34:16.964 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.4%, CPU使用率 8.3%
2025-07-30 10:34:32.077 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.4%, CPU使用率 50.0%
2025-07-30 10:34:47.182 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.5%, CPU使用率 16.7%
2025-07-30 10:34:54.999 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 11.0%, 内存: 60.5%, 磁盘: 94.3%
2025-07-30 10:35:02.288 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.4%, CPU使用率 8.0%
2025-07-30 10:35:17.563 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.5%, CPU使用率 87.0%
2025-07-30 10:35:32.669 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.5%, CPU使用率 12.5%
2025-07-30 10:35:47.775 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.1%, CPU使用率 12.5%
2025-07-30 10:35:56.030 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 24.2%, 内存: 61.0%, 磁盘: 94.3%
2025-07-30 10:36:02.881 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.1%, CPU使用率 11.1%
2025-07-30 10:36:17.987 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.2%, CPU使用率 7.1%
2025-07-30 10:36:33.094 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.3%, CPU使用率 26.9%
2025-07-30 10:36:48.207 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.1%, CPU使用率 20.0%
2025-07-30 10:36:57.053 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 19.9%, 内存: 61.1%, 磁盘: 94.3%
2025-07-30 10:37:03.315 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.0%, CPU使用率 0.0%
2025-07-30 10:37:18.420 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.0%, CPU使用率 0.0%
2025-07-30 10:37:33.527 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.0%, CPU使用率 4.2%
2025-07-30 10:37:48.632 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.0%, CPU使用率 24.0%
2025-07-30 10:37:58.078 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 21.4%, 内存: 61.1%, 磁盘: 94.3%
2025-07-30 10:38:03.747 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.9%, CPU使用率 79.2%
2025-07-30 10:38:18.853 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.8%, CPU使用率 45.8%
2025-07-30 10:38:33.959 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.8%, CPU使用率 3.6%
2025-07-30 10:38:49.066 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.8%, CPU使用率 14.3%
2025-07-30 10:38:59.100 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 35.5%, 内存: 60.9%, 磁盘: 94.3%
2025-07-30 10:39:04.174 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.8%, CPU使用率 29.6%
2025-07-30 10:39:19.280 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.8%, CPU使用率 0.0%
2025-07-30 10:39:34.386 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.7%, CPU使用率 8.3%
2025-07-30 10:39:49.491 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.7%, CPU使用率 0.0%
2025-07-30 10:40:00.123 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 15.8%, 内存: 60.8%, 磁盘: 94.3%
2025-07-30 10:40:04.597 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.8%, CPU使用率 26.9%
2025-07-30 10:40:19.704 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.8%, CPU使用率 3.6%
2025-07-30 10:40:34.811 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.7%, CPU使用率 20.8%
2025-07-30 10:40:49.916 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.8%, CPU使用率 0.0%
2025-07-30 10:41:01.145 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 17.4%, 内存: 60.7%, 磁盘: 94.3%
2025-07-30 10:41:05.022 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.6%, CPU使用率 10.7%
2025-07-30 10:41:20.128 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.8%, CPU使用率 7.1%
2025-07-30 10:41:35.234 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.7%, CPU使用率 3.8%
2025-07-30 10:41:50.339 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.7%, CPU使用率 4.2%
2025-07-30 10:42:02.167 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 9.0%, 内存: 60.7%, 磁盘: 94.3%
2025-07-30 10:42:05.446 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.7%, CPU使用率 12.5%
2025-07-30 10:42:20.552 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.7%, CPU使用率 7.1%
2025-07-30 10:42:35.659 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.7%, CPU使用率 35.7%
2025-07-30 10:42:50.764 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.7%, CPU使用率 8.7%
2025-07-30 10:43:03.188 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 20.7%, 内存: 60.6%, 磁盘: 94.3%
2025-07-30 10:43:05.871 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.7%, CPU使用率 20.8%
2025-07-30 10:43:20.977 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.8%, CPU使用率 16.7%
2025-07-30 10:43:36.082 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.6%, CPU使用率 46.4%
2025-07-30 10:43:51.187 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.8%, CPU使用率 22.2%
2025-07-30 10:44:04.212 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 19.1%, 内存: 60.8%, 磁盘: 94.3%
2025-07-30 10:44:06.294 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.8%, CPU使用率 4.2%
2025-07-30 10:44:21.399 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.6%, CPU使用率 22.7%
2025-07-30 10:44:36.508 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.8%, CPU使用率 25.0%
2025-07-30 10:44:51.613 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.7%, CPU使用率 0.0%
2025-07-30 10:45:05.233 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 19.5%, 内存: 60.7%, 磁盘: 94.3%
2025-07-30 10:45:06.722 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.7%, CPU使用率 3.7%
2025-07-30 10:45:21.838 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.8%, CPU使用率 70.8%
2025-07-30 10:45:36.945 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.7%, CPU使用率 7.7%
2025-07-30 10:45:52.050 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.7%, CPU使用率 0.0%
2025-07-30 10:46:06.254 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 8.7%, 内存: 60.8%, 磁盘: 94.3%
2025-07-30 10:46:07.156 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.8%, CPU使用率 14.8%
2025-07-30 10:46:22.262 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.7%, CPU使用率 20.8%
2025-07-30 10:46:37.367 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.7%, CPU使用率 16.0%
2025-07-30 10:46:52.475 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.8%, CPU使用率 15.4%
2025-07-30 10:47:07.294 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 17.0%, 内存: 60.8%, 磁盘: 94.3%
2025-07-30 10:47:07.581 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.7%, CPU使用率 14.3%
2025-07-30 10:47:22.688 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.8%, CPU使用率 24.0%
2025-07-30 10:47:37.795 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.7%, CPU使用率 8.3%
2025-07-30 10:47:52.902 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.8%, CPU使用率 29.2%
2025-07-30 10:48:08.008 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.7%, CPU使用率 0.0%
2025-07-30 10:48:08.316 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 20.3%, 内存: 60.7%, 磁盘: 94.3%
2025-07-30 10:48:23.114 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.8%, CPU使用率 22.2%
2025-07-30 10:48:38.219 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.6%, CPU使用率 10.7%
2025-07-30 10:48:53.324 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.7%, CPU使用率 0.0%
2025-07-30 10:49:08.430 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.7%, CPU使用率 20.8%
2025-07-30 10:49:09.338 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 10.5%, 内存: 60.7%, 磁盘: 94.3%
2025-07-30 10:49:23.536 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.6%, CPU使用率 7.1%
2025-07-30 10:49:38.641 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.6%, CPU使用率 11.5%
2025-07-30 10:49:53.748 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.7%, CPU使用率 0.0%
2025-07-30 10:50:08.854 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.7%, CPU使用率 16.7%
2025-07-30 10:50:10.360 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 20.3%, 内存: 60.7%, 磁盘: 94.3%
2025-07-30 10:50:23.959 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.5%, CPU使用率 32.1%
2025-07-30 10:50:39.064 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.7%, CPU使用率 0.0%
2025-07-30 10:50:54.170 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.6%, CPU使用率 16.7%
2025-07-30 10:51:09.401 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.7%, CPU使用率 94.7%
2025-07-30 10:51:11.382 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 26.7%, 内存: 60.7%, 磁盘: 94.3%
2025-07-30 10:51:24.507 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.6%, CPU使用率 0.0%
2025-07-30 10:51:39.613 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.5%, CPU使用率 0.0%
2025-07-30 10:51:54.718 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.7%, CPU使用率 21.4%
2025-07-30 10:52:09.824 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.6%, CPU使用率 16.7%
2025-07-30 10:52:12.404 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 9.4%, 内存: 60.6%, 磁盘: 94.3%
2025-07-30 10:52:24.930 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.7%, CPU使用率 29.2%
2025-07-30 10:52:40.035 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.1%, CPU使用率 0.0%
2025-07-30 10:52:55.141 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.9%, CPU使用率 21.4%
2025-07-30 10:53:10.248 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.0%, CPU使用率 29.2%
2025-07-30 10:53:13.433 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 16.0%, 内存: 61.1%, 磁盘: 94.3%
2025-07-30 10:53:25.354 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.0%, CPU使用率 16.7%
2025-07-30 10:53:40.460 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.9%, CPU使用率 20.0%
2025-07-30 10:53:55.566 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.8%, CPU使用率 25.0%
2025-07-30 10:54:10.672 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.7%, CPU使用率 0.0%
2025-07-30 10:54:14.459 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 37.7%, 内存: 60.8%, 磁盘: 94.3%
2025-07-30 10:54:25.780 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.8%, CPU使用率 29.2%
2025-07-30 10:54:40.885 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.0%, CPU使用率 12.5%
2025-07-30 10:54:55.993 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.2%, CPU使用率 8.3%
2025-07-30 10:55:11.099 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.1%, CPU使用率 14.3%
2025-07-30 10:55:15.480 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 26.6%, 内存: 61.2%, 磁盘: 94.3%
2025-07-30 10:55:26.205 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.0%, CPU使用率 10.7%
2025-07-30 10:55:41.311 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.0%, CPU使用率 25.0%
2025-07-30 10:55:56.417 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.4%, CPU使用率 12.5%
2025-07-30 10:56:11.522 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.2%, CPU使用率 8.0%
2025-07-30 10:56:16.503 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 28.4%, 内存: 61.3%, 磁盘: 94.3%
2025-07-30 10:56:26.627 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.2%, CPU使用率 0.0%
2025-07-30 10:56:41.736 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.2%, CPU使用率 3.6%
2025-07-30 10:56:56.841 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.5%, CPU使用率 0.0%
2025-07-30 10:57:11.946 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.3%, CPU使用率 41.7%
2025-07-30 10:57:17.530 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 27.2%, 内存: 61.4%, 磁盘: 94.3%
2025-07-30 10:57:27.053 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.3%, CPU使用率 70.4%
2025-07-30 10:57:42.160 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.2%, CPU使用率 18.5%
2025-07-30 10:57:57.266 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.3%, CPU使用率 42.3%
2025-07-30 10:58:12.374 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.2%, CPU使用率 3.6%
2025-07-30 10:58:18.552 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 12.8%, 内存: 61.3%, 磁盘: 94.3%
2025-07-30 10:58:27.481 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.1%, CPU使用率 62.5%
2025-07-30 10:58:42.586 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.3%, CPU使用率 50.0%
2025-07-30 10:58:57.691 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.2%, CPU使用率 8.0%
2025-07-30 10:59:12.800 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.5%, CPU使用率 56.7%
2025-07-30 10:59:19.603 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 86.3%, 内存: 63.1%, 磁盘: 94.3%
2025-07-30 10:59:27.909 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.4%, CPU使用率 82.1%
2025-07-30 10:59:43.015 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.9%, CPU使用率 75.0%
2025-07-30 10:59:58.140 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 62.0%, CPU使用率 83.3%
2025-07-30 11:00:13.256 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.4%, CPU使用率 75.0%
2025-07-30 11:00:20.730 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 69.9%, 内存: 64.9%, 磁盘: 94.3%
2025-07-30 11:00:28.367 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.9%, CPU使用率 61.5%
2025-07-30 11:00:43.487 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.7%, CPU使用率 100.0%
2025-07-30 11:00:58.631 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.6%, CPU使用率 100.0%
2025-07-30 11:01:13.747 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 62.7%, CPU使用率 66.7%
2025-07-30 11:01:21.780 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 98.1%, 内存: 63.0%, 磁盘: 94.3%
2025-07-30 11:01:28.897 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 62.2%, CPU使用率 96.4%
2025-07-30 11:01:44.075 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.0%, CPU使用率 79.2%
2025-07-30 11:01:59.182 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 62.9%, CPU使用率 65.2%
2025-07-30 11:02:14.289 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.2%, CPU使用率 62.5%
2025-07-30 11:02:23.337 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 97.9%, 内存: 64.2%, 磁盘: 94.3%
2025-07-30 11:02:29.471 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.2%, CPU使用率 100.0%
2025-07-30 11:02:44.643 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.0%, CPU使用率 86.2%
2025-07-30 11:02:59.999 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.9%, CPU使用率 100.0%
2025-07-30 11:03:15.206 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.2%, CPU使用率 85.7%
2025-07-30 11:03:24.439 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 91.5%, 内存: 64.1%, 磁盘: 94.3%
2025-07-30 11:03:30.466 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.8%, CPU使用率 100.0%
2025-07-30 11:03:45.596 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.3%, CPU使用率 96.6%
2025-07-30 11:04:01.012 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.1%, CPU使用率 100.0%
2025-07-30 11:04:16.188 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.5%, CPU使用率 94.4%
2025-07-30 11:04:25.562 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 77.7%, 内存: 67.2%, 磁盘: 94.3%
2025-07-30 11:04:31.304 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.4%, CPU使用率 92.6%
2025-07-30 11:04:46.674 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.1%, CPU使用率 100.0%
2025-07-30 11:05:02.090 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.8%, CPU使用率 100.0%
2025-07-30 11:05:17.246 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.5%, CPU使用率 91.7%
2025-07-30 11:05:26.980 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 88.8%, 内存: 65.6%, 磁盘: 94.3%
2025-07-30 11:05:32.585 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.6%, CPU使用率 100.0%
2025-07-30 11:05:47.846 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.1%, CPU使用率 84.8%
2025-07-30 11:06:03.200 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.1%, CPU使用率 100.0%
2025-07-30 11:06:18.364 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.9%, CPU使用率 70.4%
2025-07-30 11:06:28.115 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 88.7%, 内存: 66.5%, 磁盘: 94.3%
2025-07-30 11:06:33.544 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.7%, CPU使用率 86.7%
2025-07-30 11:06:48.804 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.6%, CPU使用率 98.2%
2025-07-30 11:07:03.923 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.4%, CPU使用率 92.9%
2025-07-30 11:07:19.482 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.5%, CPU使用率 100.0%
2025-07-30 11:07:29.470 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 100.0%, 内存: 68.4%, 磁盘: 94.3%
2025-07-30 11:07:35.022 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.8%, CPU使用率 100.0%
2025-07-30 11:07:50.302 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.7%, CPU使用率 96.4%
2025-07-30 11:08:05.559 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.0%, CPU使用率 100.0%
2025-07-30 11:08:20.733 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.8%, CPU使用率 100.0%
2025-07-30 11:08:30.955 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 99.7%, 内存: 66.4%, 磁盘: 94.3%
2025-07-30 11:08:36.087 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.6%, CPU使用率 100.0%
2025-07-30 11:08:51.924 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.8%, CPU使用率 100.0%
2025-07-30 11:09:07.219 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.4%, CPU使用率 92.9%
2025-07-30 11:09:22.548 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.1%, CPU使用率 100.0%
2025-07-30 11:09:32.007 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 80.5%, 内存: 66.8%, 磁盘: 94.3%
2025-07-30 11:09:37.672 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.3%, CPU使用率 88.9%
2025-07-30 11:09:52.787 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.5%, CPU使用率 82.1%
2025-07-30 11:10:08.034 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.1%, CPU使用率 100.0%
2025-07-30 11:10:23.498 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.8%, CPU使用率 100.0%
2025-07-30 11:10:33.681 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 92.3%, 内存: 68.1%, 磁盘: 94.3%
2025-07-30 11:10:38.913 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.4%, CPU使用率 93.1%
2025-07-30 11:10:54.201 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.2%, CPU使用率 100.0%
2025-07-30 11:11:09.584 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.8%, CPU使用率 100.0%
2025-07-30 11:11:24.738 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.1%, CPU使用率 29.6%
2025-07-30 11:11:34.812 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 54.3%, 内存: 58.9%, 磁盘: 94.3%
2025-07-30 11:11:39.844 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 58.8%, CPU使用率 44.4%
2025-07-30 11:11:54.949 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 58.7%, CPU使用率 50.0%
2025-07-30 11:12:10.054 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 59.0%, CPU使用率 11.5%
2025-07-30 11:12:25.171 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 58.9%, CPU使用率 70.8%
2025-07-30 11:12:35.834 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 52.9%, 内存: 59.0%, 磁盘: 94.3%
2025-07-30 11:12:40.282 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 58.9%, CPU使用率 28.6%
2025-07-30 11:12:55.389 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 58.9%, CPU使用率 41.7%
2025-07-30 11:13:10.496 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 59.2%, CPU使用率 54.2%
2025-07-30 11:13:25.603 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 59.2%, CPU使用率 37.5%
2025-07-30 11:13:36.860 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 39.0%, 内存: 59.4%, 磁盘: 94.3%
2025-07-30 11:13:40.710 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 59.3%, CPU使用率 17.9%
2025-07-30 11:13:55.815 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 59.2%, CPU使用率 25.9%
2025-07-30 11:14:10.921 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 59.1%, CPU使用率 16.7%
2025-07-30 11:14:26.030 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 59.1%, CPU使用率 83.3%
2025-07-30 11:14:37.924 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 73.8%, 内存: 61.1%, 磁盘: 94.3%
2025-07-30 11:14:41.139 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.4%, CPU使用率 54.2%
2025-07-30 11:48:14.495 [INFO] [root] [health_monitor.<module>:28] - 成功导入psutil模块，路径: C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\psutil\__init__.py
2025-07-30 11:48:14.594 [DEBUG] [fallback_manager] [fallback_manager.register_dependency:39] - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-07-30 11:48:14.695 [INFO] [fallback_manager] [fallback_manager.register_dependency:51] - 依赖 psutil (psutil) 可用
2025-07-30 11:48:15.822 [INFO] [health_monitor] [health_monitor.__init__:115] - 健康监控器初始化完成
2025-07-30 11:48:15.930 [INFO] [app.core.system_monitor] [system_monitor._load_history:254] - 已加载 288 个历史数据点
2025-07-30 11:48:16.119 [INFO] [app.core.alert_detector] [alert_detector._load_rules:464] - 已加载 6 个告警规则
2025-07-30 11:48:16.334 [INFO] [app.core.alert_detector] [alert_detector._load_alerts:484] - 已加载 0 个当前告警
2025-07-30 11:48:16.481 [INFO] [app.core.alert_detector] [alert_detector._load_alerts:491] - 已加载 370 个历史告警
2025-07-30 11:48:16.689 [INFO] [app.core.alert_detector] [alert_detector._load_notification_channels:502] - 已加载 1 个通知渠道
2025-07-30 11:48:16.732 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-30 11:48:16.755 [INFO] [alert_manager] [alert_manager._init_alert_rules:315] - 已初始化默认告警规则
2025-07-30 11:48:16.765 [INFO] [alert_manager] [alert_manager._init_notification_channels:333] - 已初始化默认通知渠道
2025-07-30 11:48:16.778 [INFO] [alert_manager] [alert_manager.__init__:258] - 告警管理器初始化完成
2025-07-30 11:48:19.056 [INFO] [db_service] [db_service._create_engine:92] - 数据库引擎和会话工厂创建成功
2025-07-30 11:48:19.062 [INFO] [db_service] [db_service.__init__:56] - 数据库服务初始化完成
2025-07-30 11:48:19.076 [INFO] [notification_service] [notification_service.__init__:55] - 通知服务初始化完成
2025-07-30 11:48:19.082 [INFO] [main] [main.<module>:60] - 错误处理模块导入成功
2025-07-30 11:48:19.398 [INFO] [main] [main.<module>:83] - 监控模块导入成功
2025-07-30 11:48:19.537 [INFO] [main] [main.<module>:93] - 不再使用迁移脚本，使用标准化的数据库模型
2025-07-30 11:48:27.679 [WARNING] [app.services.ocr_service] [ocr_service.<module>:37] - OpenCV未安装，图像预处理功能将不可用
2025-07-30 11:48:27.794 [INFO] [main] [main.startup_event:404] - 应用启动中...
2025-07-30 11:48:27.798 [INFO] [error_handling] [error_handling.setup_error_handling:234] - 错误处理已设置
2025-07-30 11:48:27.803 [INFO] [main] [main.startup_event:410] - 错误处理系统初始化完成
2025-07-30 11:48:27.812 [INFO] [monitoring] [monitoring.init_monitoring:441] - 添加指标端点成功: /metrics
2025-07-30 11:48:27.814 [INFO] [monitoring] [monitoring.init_monitoring:448] - 添加健康检查端点成功: /health
2025-07-30 11:48:27.816 [INFO] [monitoring] [monitoring.init_monitoring:454] - 添加详细健康检查端点成功: /api/health/detailed
2025-07-30 11:48:27.819 [INFO] [monitoring] [monitoring.init_system_info:103] - 系统信息初始化完成: Markey, Windows-10-10.0.19045-SP0, Python 3.13.2, CPU核心数: 4
2025-07-30 11:48:27.837 [INFO] [monitoring] [monitoring.init_monitoring:464] - 启动资源监控线程成功
2025-07-30 11:48:27.838 [INFO] [monitoring] [monitoring.init_monitoring:466] - 监控系统初始化成功（不使用中间件）
2025-07-30 11:48:27.844 [INFO] [monitoring] [monitoring.init_monitoring:470] - 监控系统初始化完成
2025-07-30 11:48:27.847 [INFO] [main] [main.startup_event:419] - 监控系统初始化完成
2025-07-30 11:48:27.850 [INFO] [app.db.init_db] [init_db.<module>:45] - 所有模型导入成功
2025-07-30 11:48:27.851 [INFO] [app.db.init_db] [init_db.<module>:53] - 使用sha256_crypt进行密码哈希和验证
2025-07-30 11:48:27.855 [INFO] [db_service] [db_service.get_session:114] - 数据库连接成功 (尝试 1/3)
2025-07-30 11:48:27.861 [INFO] [app.db.init_db] [init_db.init_db:67] - 所有模型导入成功
2025-07-30 11:48:27.864 [INFO] [app.db.init_db] [init_db.init_db:71] - 使用sha256_crypt进行密码哈希和验证
2025-07-30 11:48:27.865 [INFO] [app.db.init_db] [init_db.init_db:74] - 正在运行数据库迁移...
2025-07-30 11:48:27.867 [INFO] [app.db.init_db] [init_db.init_db:93] - 正在检查并更新数据库表结构...
2025-07-30 11:48:27.869 [INFO] [sqlalchemy.engine.Engine] [base._connection_begin_impl:2699] - BEGIN (implicit)
2025-07-30 11:48:27.871 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("alerts")
2025-07-30 11:48:27.880 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:27.886 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("alert_rules")
2025-07-30 11:48:27.888 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:27.896 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("alert_channels")
2025-07-30 11:48:27.898 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:27.901 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("users")
2025-07-30 11:48:27.903 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:27.916 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("health_records")
2025-07-30 11:48:27.920 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:27.929 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("health_overviews")
2025-07-30 11:48:27.932 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:27.934 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("medical_records")
2025-07-30 11:48:27.936 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:27.938 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.4%, CPU使用率 100.0%
2025-07-30 11:48:27.944 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("inpatient_records")
2025-07-30 11:48:27.948 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:27.951 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("surgery_records")
2025-07-30 11:48:27.953 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:27.958 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("lab_reports")
2025-07-30 11:48:27.963 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:27.966 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("lab_report_items")
2025-07-30 11:48:27.968 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:27.970 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("imaging_reports")
2025-07-30 11:48:27.978 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:27.983 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("examination_reports")
2025-07-30 11:48:27.984 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:27.987 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("documents")
2025-07-30 11:48:27.992 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:27.998 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("other_records")
2025-07-30 11:48:28.000 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:28.003 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("operation_logs")
2025-07-30 11:48:28.005 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:28.014 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("follow_up_records")
2025-07-30 11:48:28.017 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:28.019 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("health_diaries")
2025-07-30 11:48:28.021 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:28.030 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("registration_records")
2025-07-30 11:48:28.032 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:28.036 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("prescription_records")
2025-07-30 11:48:28.038 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:28.047 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("laboratory_records")
2025-07-30 11:48:28.052 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:28.063 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("imaging_records")
2025-07-30 11:48:28.067 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:28.071 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("medications")
2025-07-30 11:48:28.080 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:28.084 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("medication_usages")
2025-07-30 11:48:28.087 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:28.097 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("role_applications")
2025-07-30 11:48:28.101 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:28.104 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("assessments")
2025-07-30 11:48:28.116 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:28.121 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("assessment_items")
2025-07-30 11:48:28.131 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:28.135 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("assessment_responses")
2025-07-30 11:48:28.155 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:28.167 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("assessment_templates")
2025-07-30 11:48:28.171 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:28.182 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("assessment_template_questions")
2025-07-30 11:48:28.186 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:28.197 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("questionnaires")
2025-07-30 11:48:28.201 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:28.211 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("questionnaire_items")
2025-07-30 11:48:28.231 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:28.236 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("questionnaire_responses")
2025-07-30 11:48:28.244 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:28.249 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("questionnaire_answers")
2025-07-30 11:48:28.252 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:28.283 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("questionnaire_templates")
2025-07-30 11:48:28.297 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:28.301 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("questionnaire_template_questions")
2025-07-30 11:48:28.313 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:28.318 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("assessment_results")
2025-07-30 11:48:28.325 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:28.331 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("questionnaire_results")
2025-07-30 11:48:28.335 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:28.344 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("report_templates")
2025-07-30 11:48:28.348 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:28.352 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("assessment_distributions")
2025-07-30 11:48:28.361 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:28.365 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("questionnaire_distributions")
2025-07-30 11:48:28.369 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:28.376 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("service_stats")
2025-07-30 11:48:28.381 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 11:48:28.395 [INFO] [sqlalchemy.engine.Engine] [base._connection_commit_impl:2705] - COMMIT
2025-07-30 11:48:28.399 [INFO] [app.db.init_db] [init_db.init_db:97] - 数据库表结构检查和更新完成
2025-07-30 11:48:28.414 [INFO] [app.db.init_db] [init_db._setup_model_relationships:120] - 模型关系初始化完成
2025-07-30 11:48:28.417 [INFO] [app.db.init_db] [init_db.init_db:101] - 模型关系设置完成
2025-07-30 11:48:28.420 [INFO] [main] [main.startup_event:428] - 数据库初始化完成（强制重建）
2025-07-30 11:48:28.429 [INFO] [db_service] [db_service.get_session:114] - 数据库连接成功 (尝试 1/3)
2025-07-30 11:48:28.434 [INFO] [main] [main.startup_event:435] - 数据库连接正常
2025-07-30 11:48:28.436 [INFO] [main] [main.startup_event:443] - 开始初始化模板数据
2025-07-30 11:48:28.438 [INFO] [db_service] [db_service.get_session:114] - 数据库连接成功 (尝试 1/3)
2025-07-30 11:48:29.214 [INFO] [app.db.init_templates] [init_templates.init_assessment_templates:33] - 开始初始化评估量表模板...
2025-07-30 11:48:29.329 [INFO] [app.db.init_templates] [init_templates.init_assessment_templates:72] - 成功初始化 5 个评估量表模板
2025-07-30 11:48:29.494 [INFO] [app.db.init_templates] [init_templates.init_questionnaire_templates:85] - 开始初始化调查问卷模板...
2025-07-30 11:48:29.646 [INFO] [app.db.init_templates] [init_templates.init_questionnaire_templates:116] - 成功初始化 5 个调查问卷模板
2025-07-30 11:48:29.827 [INFO] [main] [main.startup_event:447] - 模板数据初始化完成
2025-07-30 11:48:30.126 [INFO] [main] [main.startup_event:452] - 数据库表结构已经标准化，不需要迁移
2025-07-30 11:48:30.484 [INFO] [main] [main.startup_event:455] - 应用启动完成
2025-07-30 11:48:43.417 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.8%, CPU使用率 100.0%
2025-07-30 11:48:44.756 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: GET /api/health ---
2025-07-30 11:48:44.902 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 11:48:44.990 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 11:48:44.992 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 11:48:47.368 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 11:48:47.377 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 11:48:47.378 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 11:48:47.478 [DEBUG] [app.core.db_connection] [db_connection.get_db_session:489] - 当前线程ID: 10988
2025-07-30 11:48:47.508 [INFO] [app.core.db_connection] [db_connection.get_db_session:492] - 尝试使用db_connection获取会话
2025-07-30 11:48:47.548 [INFO] [app.core.db_connection] [db_connection.connect:175] - 数据库连接已创建
2025-07-30 11:48:47.561 [DEBUG] [app.core.db_connection] [db_connection.checkout:179] - 数据库连接已检出
2025-07-30 11:48:47.563 [INFO] [app.core.db_connection] [db_connection.get_session:215] - 数据库连接成功 (尝试 1/3)
2025-07-30 11:48:47.564 [INFO] [app.core.db_connection] [db_connection.get_db_session:496] - 使用db_connection获取会话成功
2025-07-30 11:48:51.342 [INFO] [app.core.security] [security.verify_password:151] - 使用sha256_crypt验证密码成功
2025-07-30 11:48:51.862 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 11:48:59.933 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.9%, CPU使用率 100.0%
2025-07-30 11:49:15.553 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.8%, CPU使用率 100.0%
2025-07-30 11:49:16.682 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 100.0%, 内存: 66.1%, 磁盘: 94.3%
2025-07-30 11:49:31.226 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.7%, CPU使用率 100.0%
2025-07-30 11:49:46.449 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.1%, CPU使用率 70.8%
2025-07-30 11:50:01.625 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.0%, CPU使用率 100.0%
2025-07-30 11:50:16.933 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.2%, CPU使用率 96.9%
2025-07-30 11:50:17.862 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 87.1%, 内存: 66.0%, 磁盘: 94.3%
2025-07-30 11:50:32.499 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.8%, CPU使用率 100.0%
2025-07-30 11:50:48.365 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.4%, CPU使用率 100.0%
2025-07-30 11:51:04.425 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.1%, CPU使用率 100.0%
2025-07-30 11:51:19.511 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 100.0%, 内存: 65.4%, 磁盘: 94.3%
2025-07-30 11:51:19.649 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.4%, CPU使用率 100.0%
2025-07-30 11:51:35.120 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.8%, CPU使用率 100.0%
2025-07-30 11:51:50.300 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.3%, CPU使用率 96.7%
2025-07-30 11:52:05.553 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.4%, CPU使用率 94.1%
2025-07-30 11:52:20.630 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 99.3%, 内存: 64.7%, 磁盘: 94.3%
2025-07-30 11:52:20.686 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.7%, CPU使用率 95.8%
2025-07-30 11:52:36.484 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.8%, CPU使用率 100.0%
2025-07-30 11:52:52.000 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.7%, CPU使用率 93.0%
2025-07-30 11:53:07.244 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.6%, CPU使用率 100.0%
2025-07-30 11:53:21.867 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 99.6%, 内存: 67.2%, 磁盘: 94.3%
2025-07-30 11:53:22.363 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.8%, CPU使用率 82.8%
2025-07-30 11:53:37.469 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.7%, CPU使用率 44.4%
2025-07-30 11:53:52.574 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.5%, CPU使用率 45.8%
2025-07-30 11:54:07.702 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.2%, CPU使用率 30.8%
2025-07-30 11:54:16.921 [WARNING] [alert_manager] [alert_manager._create_alert:649] - 触发告警: disk_usage, 当前值: 94.3, 阈值: 90
2025-07-30 11:54:16.947 [WARNING] [alert_manager] [alert_manager._create_alert:649] - 触发告警: disk_free_space, 当前值: 0.0, 阈值: 1024
2025-07-30 11:54:22.807 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.6%, CPU使用率 37.5%
2025-07-30 11:54:22.924 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 51.0%, 内存: 64.4%, 磁盘: 94.3%
2025-07-30 11:54:37.915 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.1%, CPU使用率 62.5%
2025-07-30 11:54:53.040 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.6%, CPU使用率 100.0%
2025-07-30 11:55:08.194 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.3%, CPU使用率 100.0%
2025-07-30 11:55:23.312 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.3%, CPU使用率 81.5%
2025-07-30 11:55:24.042 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 70.3%, 内存: 66.1%, 磁盘: 94.3%
2025-07-30 11:55:38.420 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.9%, CPU使用率 75.0%
2025-07-30 11:55:53.525 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.2%, CPU使用率 37.5%
2025-07-30 11:56:08.733 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 62.9%, CPU使用率 100.0%
2025-07-30 11:56:23.896 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.1%, CPU使用率 75.0%
2025-07-30 11:56:25.105 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 69.1%, 内存: 62.4%, 磁盘: 94.3%
2025-07-30 11:56:39.001 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.2%, CPU使用率 37.0%
2025-07-30 11:56:54.107 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.3%, CPU使用率 4.2%
2025-07-30 11:56:54.192 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: GET /api/health ---
2025-07-30 11:56:54.193 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 11:56:54.194 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 11:56:54.195 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 11:56:56.229 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 11:56:56.230 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 11:56:56.230 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 11:56:56.232 [DEBUG] [app.core.db_connection] [db_connection.get_db_session:489] - 当前线程ID: 10988
2025-07-30 11:56:56.233 [INFO] [app.core.db_connection] [db_connection.get_db_session:492] - 尝试使用db_connection获取会话
2025-07-30 11:56:56.234 [INFO] [app.core.db_connection] [db_connection.connect:175] - 数据库连接已创建
2025-07-30 11:56:56.235 [DEBUG] [app.core.db_connection] [db_connection.checkout:179] - 数据库连接已检出
2025-07-30 11:56:56.236 [INFO] [app.core.db_connection] [db_connection.get_session:215] - 数据库连接成功 (尝试 1/3)
2025-07-30 11:56:56.236 [INFO] [app.core.db_connection] [db_connection.get_db_session:496] - 使用db_connection获取会话成功
2025-07-30 11:56:57.002 [INFO] [app.core.security] [security.verify_password:151] - 使用sha256_crypt验证密码成功
2025-07-30 11:56:57.004 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 11:57:09.212 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 62.5%, CPU使用率 33.3%
2025-07-30 11:57:24.317 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 62.8%, CPU使用率 28.6%
2025-07-30 11:57:26.134 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 37.7%, 内存: 62.8%, 磁盘: 94.3%
2025-07-30 11:57:39.435 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 62.5%, CPU使用率 92.9%
2025-07-30 11:57:54.543 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.1%, CPU使用率 41.7%
2025-07-30 11:58:09.648 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.1%, CPU使用率 37.5%
2025-07-30 11:58:24.752 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.0%, CPU使用率 37.0%
2025-07-30 11:58:27.158 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 39.9%, 内存: 60.0%, 磁盘: 94.3%
2025-07-30 11:58:34.627 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: GET /api/health ---
2025-07-30 11:58:34.628 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 11:58:34.629 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 11:58:34.630 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 11:58:36.683 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 11:58:36.684 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 11:58:36.684 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 11:58:36.686 [DEBUG] [app.core.db_connection] [db_connection.get_db_session:489] - 当前线程ID: 10988
2025-07-30 11:58:36.687 [INFO] [app.core.db_connection] [db_connection.get_db_session:492] - 尝试使用db_connection获取会话
2025-07-30 11:58:36.688 [INFO] [app.core.db_connection] [db_connection.connect:175] - 数据库连接已创建
2025-07-30 11:58:36.688 [DEBUG] [app.core.db_connection] [db_connection.checkout:179] - 数据库连接已检出
2025-07-30 11:58:36.689 [INFO] [app.core.db_connection] [db_connection.get_session:215] - 数据库连接成功 (尝试 1/3)
2025-07-30 11:58:36.690 [INFO] [app.core.db_connection] [db_connection.get_db_session:496] - 使用db_connection获取会话成功
2025-07-30 11:58:37.455 [INFO] [app.core.security] [security.verify_password:151] - 使用sha256_crypt验证密码成功
2025-07-30 11:58:37.458 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 11:58:39.856 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.4%, CPU使用率 12.5%
2025-07-30 11:58:54.962 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 62.2%, CPU使用率 37.5%
2025-07-30 11:59:10.067 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 62.2%, CPU使用率 25.0%
2025-07-30 11:59:25.176 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.9%, CPU使用率 39.3%
2025-07-30 11:59:28.192 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 45.5%, 内存: 62.0%, 磁盘: 94.3%
2025-07-30 11:59:40.298 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 62.1%, CPU使用率 89.3%
2025-07-30 11:59:55.411 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 62.4%, CPU使用率 50.0%
2025-07-30 12:00:10.524 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.4%, CPU使用率 100.0%
2025-07-30 12:00:25.629 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 62.8%, CPU使用率 60.7%
2025-07-30 12:00:29.215 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 66.2%, 内存: 62.3%, 磁盘: 94.3%
2025-07-30 12:00:40.798 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 62.9%, CPU使用率 96.7%
2025-07-30 12:00:56.114 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.0%, CPU使用率 100.0%
2025-07-30 12:01:11.228 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.9%, CPU使用率 88.5%
2025-07-30 12:01:26.566 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.8%, CPU使用率 90.2%
2025-07-30 12:01:30.408 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 97.3%, 内存: 63.2%, 磁盘: 94.3%
2025-07-30 12:01:41.677 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.4%, CPU使用率 66.7%
2025-07-30 12:01:56.784 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 62.0%, CPU使用率 71.4%
2025-07-30 12:02:12.300 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 62.4%, CPU使用率 100.0%
2025-07-30 12:02:27.534 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.6%, CPU使用率 66.7%
2025-07-30 12:02:31.738 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 70.7%, 内存: 66.3%, 磁盘: 94.3%
2025-07-30 12:02:42.639 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.5%, CPU使用率 70.8%
2025-07-30 12:02:57.816 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.8%, CPU使用率 100.0%
2025-07-30 12:03:12.928 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.6%, CPU使用率 71.4%
2025-07-30 12:03:28.034 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.2%, CPU使用率 78.6%
2025-07-30 12:03:32.873 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 67.4%, 内存: 63.7%, 磁盘: 94.3%
2025-07-30 12:03:43.142 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.7%, CPU使用率 92.9%
2025-07-30 12:03:58.249 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.2%, CPU使用率 62.5%
2025-07-30 12:04:13.354 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.3%, CPU使用率 83.3%
2025-07-30 12:04:28.503 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 62.5%, CPU使用率 93.5%
2025-07-30 12:04:34.033 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 100.0%, 内存: 65.5%, 磁盘: 94.3%
2025-07-30 12:04:34.885 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: GET /api/health ---
2025-07-30 12:04:34.894 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 12:04:34.894 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 12:04:34.899 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 12:04:37.310 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 12:04:37.310 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 12:04:37.311 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 12:04:37.314 [DEBUG] [app.core.db_connection] [db_connection.get_db_session:489] - 当前线程ID: 10988
2025-07-30 12:04:37.316 [INFO] [app.core.db_connection] [db_connection.get_db_session:492] - 尝试使用db_connection获取会话
2025-07-30 12:04:37.317 [INFO] [app.core.db_connection] [db_connection.connect:175] - 数据库连接已创建
2025-07-30 12:04:37.318 [DEBUG] [app.core.db_connection] [db_connection.checkout:179] - 数据库连接已检出
2025-07-30 12:04:37.319 [INFO] [app.core.db_connection] [db_connection.get_session:215] - 数据库连接成功 (尝试 1/3)
2025-07-30 12:04:37.320 [INFO] [app.core.db_connection] [db_connection.get_db_session:496] - 使用db_connection获取会话成功
2025-07-30 12:04:39.555 [INFO] [app.core.security] [security.verify_password:151] - 使用sha256_crypt验证密码成功
2025-07-30 12:04:39.558 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 12:04:43.899 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.2%, CPU使用率 100.0%
2025-07-30 12:04:59.011 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.6%, CPU使用率 95.8%
2025-07-30 12:05:14.188 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.3%, CPU使用率 96.6%
2025-07-30 12:05:29.447 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.2%, CPU使用率 88.5%
2025-07-30 12:05:35.119 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 84.9%, 内存: 66.7%, 磁盘: 94.3%
2025-07-30 12:05:44.877 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.0%, CPU使用率 100.0%
2025-07-30 12:06:00.174 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.0%, CPU使用率 60.7%
2025-07-30 12:06:15.293 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.7%, CPU使用率 85.7%
2025-07-30 12:06:30.400 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.3%, CPU使用率 25.0%
2025-07-30 12:06:36.456 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 99.3%, 内存: 68.6%, 磁盘: 94.3%
2025-07-30 12:06:45.517 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.0%, CPU使用率 80.8%
2025-07-30 12:07:00.924 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.3%, CPU使用率 98.5%
2025-07-30 12:07:16.096 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.4%, CPU使用率 11.5%
2025-07-30 12:07:31.201 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.1%, CPU使用率 29.2%
2025-07-30 12:07:37.477 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 37.5%, 内存: 60.3%, 磁盘: 94.3%
2025-07-30 12:07:46.306 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.0%, CPU使用率 16.7%
2025-07-30 12:08:01.410 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.3%, CPU使用率 15.4%
2025-07-30 12:08:16.622 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.3%, CPU使用率 84.0%
2025-07-30 14:20:46.344 [INFO] [root] [health_monitor.<module>:28] - 成功导入psutil模块，路径: C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\psutil\__init__.py
2025-07-30 14:20:46.407 [DEBUG] [fallback_manager] [fallback_manager.register_dependency:39] - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-07-30 14:20:46.454 [INFO] [fallback_manager] [fallback_manager.register_dependency:51] - 依赖 psutil (psutil) 可用
2025-07-30 14:20:46.768 [INFO] [health_monitor] [health_monitor.__init__:115] - 健康监控器初始化完成
2025-07-30 14:20:46.807 [INFO] [app.core.system_monitor] [system_monitor._load_history:254] - 已加载 288 个历史数据点
2025-07-30 14:20:46.825 [INFO] [app.core.alert_detector] [alert_detector._load_rules:464] - 已加载 6 个告警规则
2025-07-30 14:20:46.842 [INFO] [app.core.alert_detector] [alert_detector._load_alerts:484] - 已加载 0 个当前告警
2025-07-30 14:20:46.890 [INFO] [app.core.alert_detector] [alert_detector._load_alerts:491] - 已加载 370 个历史告警
2025-07-30 14:20:46.904 [INFO] [app.core.alert_detector] [alert_detector._load_notification_channels:502] - 已加载 1 个通知渠道
2025-07-30 14:20:46.925 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-30 14:20:46.987 [INFO] [alert_manager] [alert_manager._init_alert_rules:315] - 已初始化默认告警规则
2025-07-30 14:20:47.090 [INFO] [alert_manager] [alert_manager._init_notification_channels:333] - 已初始化默认通知渠道
2025-07-30 14:20:47.194 [INFO] [alert_manager] [alert_manager.__init__:258] - 告警管理器初始化完成
2025-07-30 14:20:49.467 [INFO] [db_service] [db_service._create_engine:92] - 数据库引擎和会话工厂创建成功
2025-07-30 14:20:49.472 [INFO] [db_service] [db_service.__init__:56] - 数据库服务初始化完成
2025-07-30 14:20:49.490 [INFO] [notification_service] [notification_service.__init__:55] - 通知服务初始化完成
2025-07-30 14:20:49.493 [INFO] [main] [main.<module>:60] - 错误处理模块导入成功
2025-07-30 14:20:49.566 [INFO] [main] [main.<module>:83] - 监控模块导入成功
2025-07-30 14:20:49.608 [INFO] [main] [main.<module>:93] - 不再使用迁移脚本，使用标准化的数据库模型
2025-07-30 14:20:55.250 [WARNING] [app.services.ocr_service] [ocr_service.<module>:37] - OpenCV未安装，图像预处理功能将不可用
2025-07-30 14:20:55.419 [INFO] [main] [main.startup_event:404] - 应用启动中...
2025-07-30 14:20:55.422 [INFO] [error_handling] [error_handling.setup_error_handling:234] - 错误处理已设置
2025-07-30 14:20:55.424 [INFO] [main] [main.startup_event:410] - 错误处理系统初始化完成
2025-07-30 14:20:55.470 [INFO] [monitoring] [monitoring.init_monitoring:441] - 添加指标端点成功: /metrics
2025-07-30 14:20:55.502 [INFO] [monitoring] [monitoring.init_monitoring:448] - 添加健康检查端点成功: /health
2025-07-30 14:20:55.537 [INFO] [monitoring] [monitoring.init_monitoring:454] - 添加详细健康检查端点成功: /api/health/detailed
2025-07-30 14:20:55.587 [INFO] [monitoring] [monitoring.init_system_info:103] - 系统信息初始化完成: Markey, Windows-10-10.0.19045-SP0, Python 3.13.2, CPU核心数: 4
2025-07-30 14:20:55.789 [INFO] [monitoring] [monitoring.init_monitoring:464] - 启动资源监控线程成功
2025-07-30 14:20:55.791 [INFO] [monitoring] [monitoring.init_monitoring:466] - 监控系统初始化成功（不使用中间件）
2025-07-30 14:20:55.832 [INFO] [monitoring] [monitoring.init_monitoring:470] - 监控系统初始化完成
2025-07-30 14:20:55.872 [INFO] [main] [main.startup_event:419] - 监控系统初始化完成
2025-07-30 14:20:55.950 [INFO] [app.db.init_db] [init_db.<module>:45] - 所有模型导入成功
2025-07-30 14:20:55.984 [INFO] [app.db.init_db] [init_db.<module>:53] - 使用sha256_crypt进行密码哈希和验证
2025-07-30 14:20:55.991 [INFO] [db_service] [db_service.get_session:114] - 数据库连接成功 (尝试 1/3)
2025-07-30 14:20:56.064 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.7%, CPU使用率 100.0%
2025-07-30 14:20:56.064 [INFO] [app.db.init_db] [init_db.init_db:67] - 所有模型导入成功
2025-07-30 14:20:56.094 [INFO] [app.db.init_db] [init_db.init_db:71] - 使用sha256_crypt进行密码哈希和验证
2025-07-30 14:20:56.125 [INFO] [app.db.init_db] [init_db.init_db:74] - 正在运行数据库迁移...
2025-07-30 14:20:56.133 [INFO] [app.db.init_db] [init_db.init_db:93] - 正在检查并更新数据库表结构...
2025-07-30 14:20:56.247 [INFO] [sqlalchemy.engine.Engine] [base._connection_begin_impl:2699] - BEGIN (implicit)
2025-07-30 14:20:56.331 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("alerts")
2025-07-30 14:20:56.619 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:20:56.810 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("alert_rules")
2025-07-30 14:20:56.860 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:20:57.019 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("alert_channels")
2025-07-30 14:20:57.259 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:20:57.366 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("users")
2025-07-30 14:20:57.503 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:20:57.550 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("health_records")
2025-07-30 14:20:57.568 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:20:57.618 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("health_overviews")
2025-07-30 14:20:57.654 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:20:57.724 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("medical_records")
2025-07-30 14:20:57.742 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:20:57.776 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("inpatient_records")
2025-07-30 14:20:57.852 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:20:57.906 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("surgery_records")
2025-07-30 14:20:57.927 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:20:57.993 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("lab_reports")
2025-07-30 14:20:58.117 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:20:58.454 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("lab_report_items")
2025-07-30 14:20:58.634 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:20:58.797 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("imaging_reports")
2025-07-30 14:20:58.851 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:20:58.894 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("examination_reports")
2025-07-30 14:20:58.920 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:20:59.034 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("documents")
2025-07-30 14:20:59.123 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:20:59.273 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("other_records")
2025-07-30 14:20:59.401 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:20:59.617 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("operation_logs")
2025-07-30 14:20:59.872 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:21:00.003 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("follow_up_records")
2025-07-30 14:21:00.146 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:21:00.242 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("health_diaries")
2025-07-30 14:21:00.350 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:21:00.407 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("registration_records")
2025-07-30 14:21:00.482 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:21:00.488 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("prescription_records")
2025-07-30 14:21:00.559 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:21:00.696 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("laboratory_records")
2025-07-30 14:21:00.763 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:21:01.047 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("imaging_records")
2025-07-30 14:21:01.151 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:21:01.416 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("medications")
2025-07-30 14:21:01.642 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:21:01.993 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("medication_usages")
2025-07-30 14:21:02.244 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:21:02.483 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("role_applications")
2025-07-30 14:21:02.663 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:21:02.768 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("assessments")
2025-07-30 14:21:02.837 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:21:02.885 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("assessment_items")
2025-07-30 14:21:02.973 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:21:03.052 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("assessment_responses")
2025-07-30 14:21:03.154 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:21:03.319 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("assessment_templates")
2025-07-30 14:21:03.533 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:21:03.662 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("assessment_template_questions")
2025-07-30 14:21:03.805 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:21:03.855 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("questionnaires")
2025-07-30 14:21:03.900 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:21:03.934 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("questionnaire_items")
2025-07-30 14:21:04.019 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:21:04.231 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("questionnaire_responses")
2025-07-30 14:21:04.593 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:21:04.768 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("questionnaire_answers")
2025-07-30 14:21:04.815 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:21:04.830 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("questionnaire_templates")
2025-07-30 14:21:04.887 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:21:04.950 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("questionnaire_template_questions")
2025-07-30 14:21:05.076 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:21:05.202 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("assessment_results")
2025-07-30 14:21:05.284 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:21:05.390 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("questionnaire_results")
2025-07-30 14:21:05.470 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:21:05.691 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("report_templates")
2025-07-30 14:21:05.955 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:21:06.178 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("assessment_distributions")
2025-07-30 14:21:06.284 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:21:06.385 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("questionnaire_distributions")
2025-07-30 14:21:06.789 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:21:07.080 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("service_stats")
2025-07-30 14:21:07.240 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 14:21:07.463 [INFO] [sqlalchemy.engine.Engine] [base._connection_commit_impl:2705] - COMMIT
2025-07-30 14:21:07.663 [INFO] [app.db.init_db] [init_db.init_db:97] - 数据库表结构检查和更新完成
2025-07-30 14:21:07.881 [INFO] [app.db.init_db] [init_db._setup_model_relationships:120] - 模型关系初始化完成
2025-07-30 14:21:08.021 [INFO] [app.db.init_db] [init_db.init_db:101] - 模型关系设置完成
2025-07-30 14:21:08.202 [INFO] [main] [main.startup_event:428] - 数据库初始化完成（强制重建）
2025-07-30 14:21:08.312 [INFO] [db_service] [db_service.get_session:114] - 数据库连接成功 (尝试 1/3)
2025-07-30 14:21:08.438 [INFO] [main] [main.startup_event:435] - 数据库连接正常
2025-07-30 14:21:08.646 [INFO] [main] [main.startup_event:443] - 开始初始化模板数据
2025-07-30 14:21:08.846 [INFO] [db_service] [db_service.get_session:114] - 数据库连接成功 (尝试 1/3)
2025-07-30 14:21:11.375 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.3%, CPU使用率 100.0%
2025-07-30 14:21:11.565 [INFO] [app.db.init_templates] [init_templates.init_assessment_templates:33] - 开始初始化评估量表模板...
2025-07-30 14:21:11.733 [INFO] [app.db.init_templates] [init_templates.init_assessment_templates:72] - 成功初始化 5 个评估量表模板
2025-07-30 14:21:12.022 [INFO] [app.db.init_templates] [init_templates.init_questionnaire_templates:85] - 开始初始化调查问卷模板...
2025-07-30 14:21:12.486 [INFO] [app.db.init_templates] [init_templates.init_questionnaire_templates:116] - 成功初始化 5 个调查问卷模板
2025-07-30 14:21:12.490 [INFO] [main] [main.startup_event:447] - 模板数据初始化完成
2025-07-30 14:21:12.495 [INFO] [main] [main.startup_event:452] - 数据库表结构已经标准化，不需要迁移
2025-07-30 14:21:12.789 [INFO] [main] [main.startup_event:455] - 应用启动完成
2025-07-30 14:21:27.097 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.5%, CPU使用率 100.0%
2025-07-30 14:21:42.583 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.4%, CPU使用率 100.0%
2025-07-30 14:21:47.918 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 100.0%, 内存: 67.8%, 磁盘: 94.3%
2025-07-30 14:21:57.713 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.9%, CPU使用率 67.9%
2025-07-30 14:22:02.516 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: GET /api/health ---
2025-07-30 14:22:02.517 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 14:22:02.518 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 14:22:02.520 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 14:22:04.559 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 14:22:04.560 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 14:22:04.561 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 14:22:04.563 [DEBUG] [app.core.db_connection] [db_connection.get_db_session:489] - 当前线程ID: 2244
2025-07-30 14:22:04.564 [INFO] [app.core.db_connection] [db_connection.get_db_session:492] - 尝试使用db_connection获取会话
2025-07-30 14:22:04.566 [INFO] [app.core.db_connection] [db_connection.connect:175] - 数据库连接已创建
2025-07-30 14:22:04.567 [DEBUG] [app.core.db_connection] [db_connection.checkout:179] - 数据库连接已检出
2025-07-30 14:22:04.568 [INFO] [app.core.db_connection] [db_connection.get_session:215] - 数据库连接成功 (尝试 1/3)
2025-07-30 14:22:04.569 [INFO] [app.core.db_connection] [db_connection.get_db_session:496] - 使用db_connection获取会话成功
2025-07-30 14:22:05.417 [INFO] [app.core.security] [security.verify_password:151] - 使用sha256_crypt验证密码成功
2025-07-30 14:22:05.420 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 14:22:12.819 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.8%, CPU使用率 57.1%
2025-07-30 14:22:27.924 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.7%, CPU使用率 7.1%
2025-07-30 14:22:43.029 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.5%, CPU使用率 4.2%
2025-07-30 14:22:49.019 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 10.9%, 内存: 68.5%, 磁盘: 94.3%
2025-07-30 14:22:58.134 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.5%, CPU使用率 32.1%
2025-07-30 14:23:13.238 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.5%, CPU使用率 20.0%
2025-07-30 14:23:28.343 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.5%, CPU使用率 4.2%
2025-07-30 14:23:43.452 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 74.2%, CPU使用率 100.0%
2025-07-30 14:23:50.043 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 28.7%, 内存: 69.8%, 磁盘: 94.3%
2025-07-30 14:23:58.562 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.8%, CPU使用率 37.5%
2025-07-30 14:24:13.708 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.1%, CPU使用率 73.5%
2025-07-30 14:24:28.816 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.1%, CPU使用率 46.4%
2025-07-30 14:24:44.240 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.9%, CPU使用率 100.0%
2025-07-30 14:24:51.067 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 32.0%, 内存: 67.3%, 磁盘: 94.3%
2025-07-30 14:24:59.700 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.4%, CPU使用率 76.3%
2025-07-30 14:25:14.816 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.1%, CPU使用率 28.6%
2025-07-30 14:25:30.065 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.4%, CPU使用率 95.9%
2025-07-30 14:25:45.182 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.3%, CPU使用率 89.7%
2025-07-30 14:25:52.155 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 81.4%, 内存: 68.5%, 磁盘: 94.3%
2025-07-30 14:26:00.291 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.8%, CPU使用率 73.1%
2025-07-30 14:26:15.424 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.2%, CPU使用率 96.7%
2025-07-30 14:26:30.541 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.0%, CPU使用率 96.4%
2025-07-30 14:26:45.710 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.2%, CPU使用率 96.6%
2025-07-30 14:26:47.310 [WARNING] [alert_manager] [alert_manager._create_alert:649] - 触发告警: disk_free_space, 当前值: 0.0, 阈值: 1024
2025-07-30 14:26:53.184 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 88.3%, 内存: 67.8%, 磁盘: 94.3%
2025-07-30 14:27:00.816 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.4%, CPU使用率 39.3%
2025-07-30 14:27:15.923 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.5%, CPU使用率 62.5%
2025-07-30 14:27:31.035 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.4%, CPU使用率 85.7%
2025-07-30 14:27:46.183 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.4%, CPU使用率 100.0%
2025-07-30 14:27:47.520 [WARNING] [alert_manager] [alert_manager._create_alert:649] - 触发告警: disk_usage, 当前值: 94.3, 阈值: 90
2025-07-30 14:27:54.218 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 88.3%, 内存: 67.7%, 磁盘: 94.3%
2025-07-30 14:28:01.338 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.2%, CPU使用率 70.8%
2025-07-30 14:28:16.720 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.5%, CPU使用率 100.0%
2025-07-30 14:28:32.429 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.5%, CPU使用率 100.0%
2025-07-30 14:28:47.608 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.0%, CPU使用率 100.0%
2025-07-30 14:28:55.430 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 92.5%, 内存: 69.2%, 磁盘: 94.3%
2025-07-30 14:29:02.777 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.2%, CPU使用率 100.0%
2025-07-30 14:29:17.927 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.4%, CPU使用率 100.0%
2025-07-30 14:29:33.305 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.2%, CPU使用率 100.0%
2025-07-30 14:29:48.646 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.4%, CPU使用率 100.0%
2025-07-30 14:29:56.483 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 83.6%, 内存: 70.7%, 磁盘: 94.4%
2025-07-30 14:30:03.766 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.6%, CPU使用率 96.0%
2025-07-30 14:30:18.885 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.0%, CPU使用率 96.4%
2025-07-30 14:30:33.996 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.4%, CPU使用率 77.8%
2025-07-30 14:30:49.131 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.2%, CPU使用率 82.1%
2025-07-30 14:30:57.760 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 97.4%, 内存: 68.7%, 磁盘: 94.4%
2025-07-30 14:31:04.268 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.7%, CPU使用率 100.0%
2025-07-30 14:31:19.388 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.4%, CPU使用率 100.0%
2025-07-30 14:31:34.505 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.9%, CPU使用率 95.8%
2025-07-30 14:31:49.722 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.3%, CPU使用率 70.4%
2025-07-30 14:31:59.056 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 97.9%, 内存: 71.4%, 磁盘: 94.3%
2025-07-30 14:32:04.881 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.2%, CPU使用率 97.4%
2025-07-30 14:32:20.032 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.9%, CPU使用率 76.0%
2025-07-30 14:32:35.141 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.4%, CPU使用率 50.0%
2025-07-30 14:32:50.413 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.5%, CPU使用率 100.0%
2025-07-30 14:33:01.064 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 80.5%, 内存: 68.7%, 磁盘: 94.3%
2025-07-30 14:33:05.540 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.1%, CPU使用率 100.0%
2025-07-30 14:33:20.839 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.0%, CPU使用率 100.0%
2025-07-30 14:33:36.103 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.3%, CPU使用率 96.7%
2025-07-30 14:33:51.208 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.8%, CPU使用率 100.0%
2025-07-30 14:34:02.108 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 100.0%, 内存: 65.2%, 磁盘: 94.3%
2025-07-30 14:34:06.319 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.7%, CPU使用率 100.0%
2025-07-30 14:34:21.439 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.0%, CPU使用率 100.0%
2025-07-30 14:34:36.547 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.5%, CPU使用率 100.0%
2025-07-30 14:34:52.075 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.2%, CPU使用率 100.0%
2025-07-30 14:35:03.378 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 100.0%, 内存: 67.2%, 磁盘: 94.4%
2025-07-30 14:35:07.478 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.1%, CPU使用率 100.0%
2025-07-30 14:35:23.160 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.0%, CPU使用率 100.0%
2025-07-30 14:35:38.721 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.1%, CPU使用率 100.0%
2025-07-30 14:35:53.988 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.8%, CPU使用率 100.0%
2025-07-30 14:36:04.536 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 100.0%, 内存: 71.0%, 磁盘: 94.4%
2025-07-30 14:36:09.665 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.8%, CPU使用率 88.5%
2025-07-30 14:36:24.772 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.9%, CPU使用率 75.0%
2025-07-30 14:36:40.184 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.4%, CPU使用率 100.0%
2025-07-30 14:36:55.445 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.8%, CPU使用率 100.0%
2025-07-30 14:37:05.629 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 98.8%, 内存: 72.0%, 磁盘: 94.4%
2025-07-30 14:37:10.554 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.9%, CPU使用率 75.0%
2025-07-30 14:37:25.658 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.0%, CPU使用率 46.4%
2025-07-30 14:37:41.027 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.3%, CPU使用率 100.0%
2025-07-30 14:37:56.501 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.6%, CPU使用率 97.6%
2025-07-30 14:38:06.812 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 94.7%, 内存: 69.0%, 磁盘: 94.4%
2025-07-30 14:38:11.618 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.8%, CPU使用率 89.3%
2025-07-30 14:38:26.728 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.8%, CPU使用率 82.1%
2025-07-30 14:38:42.336 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.2%, CPU使用率 100.0%
2025-07-30 14:38:57.595 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.5%, CPU使用率 100.0%
2025-07-30 14:39:07.939 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 94.5%, 内存: 70.6%, 磁盘: 94.4%
2025-07-30 14:39:12.874 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.9%, CPU使用率 100.0%
2025-07-30 14:39:28.290 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.9%, CPU使用率 100.0%
2025-07-30 14:39:43.610 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.5%, CPU使用率 92.3%
2025-07-30 14:39:47.850 [WARNING] [alert_manager] [alert_manager._create_alert:649] - 触发告警: cpu_usage, 当前值: 94.5, 阈值: 90
2025-07-30 14:39:59.111 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.3%, CPU使用率 100.0%
2025-07-30 14:40:09.221 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 98.7%, 内存: 71.2%, 磁盘: 94.4%
2025-07-30 14:40:14.647 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.4%, CPU使用率 100.0%
2025-07-30 14:40:30.729 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.1%, CPU使用率 99.1%
2025-07-30 14:40:46.142 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.7%, CPU使用率 100.0%
2025-07-30 14:41:01.380 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.9%, CPU使用率 98.0%
2025-07-30 14:41:10.627 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 94.0%, 内存: 70.6%, 磁盘: 94.4%
2025-07-30 14:41:16.524 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.9%, CPU使用率 83.3%
2025-07-30 14:41:31.630 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.9%, CPU使用率 64.3%
2025-07-30 14:41:47.049 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.7%, CPU使用率 100.0%
2025-07-30 14:42:02.539 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.7%, CPU使用率 95.2%
2025-07-30 14:42:11.799 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 99.2%, 内存: 71.8%, 磁盘: 94.4%
2025-07-30 14:42:17.813 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.0%, CPU使用率 100.0%
2025-07-30 14:42:33.131 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.3%, CPU使用率 100.0%
2025-07-30 14:42:48.252 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 74.9%, CPU使用率 78.6%
2025-07-30 14:43:03.677 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 75.7%, CPU使用率 100.0%
2025-07-30 14:43:12.840 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 65.4%, 内存: 75.3%, 磁盘: 94.4%
2025-07-30 14:43:18.797 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.3%, CPU使用率 100.0%
2025-07-30 14:43:33.912 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.9%, CPU使用率 96.6%
2025-07-30 14:43:49.202 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.6%, CPU使用率 100.0%
2025-07-30 14:44:04.336 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.1%, CPU使用率 96.4%
2025-07-30 14:44:14.024 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 85.9%, 内存: 73.0%, 磁盘: 94.4%
2025-07-30 14:44:19.708 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.4%, CPU使用率 100.0%
2025-07-30 14:44:34.882 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.6%, CPU使用率 96.9%
2025-07-30 14:44:50.189 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 74.9%, CPU使用率 100.0%
2025-07-30 14:45:05.464 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 75.7%, CPU使用率 100.0%
2025-07-30 14:45:15.229 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 76.8%, 内存: 75.1%, 磁盘: 94.4%
2025-07-30 14:45:20.579 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 74.9%, CPU使用率 58.3%
2025-07-30 14:45:36.128 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.9%, CPU使用率 100.0%
2025-07-30 14:45:51.236 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.4%, CPU使用率 33.3%
2025-07-30 14:46:06.378 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.3%, CPU使用率 100.0%
2025-07-30 14:46:16.736 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 88.0%, 内存: 72.7%, 磁盘: 94.4%
2025-07-30 14:46:21.571 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 75.0%, CPU使用率 100.0%
2025-07-30 14:46:36.733 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 75.6%, CPU使用率 100.0%
2025-07-30 14:46:51.855 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 74.7%, CPU使用率 92.9%
2025-07-30 14:47:07.092 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 74.5%, CPU使用率 96.9%
2025-07-30 14:47:17.795 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 86.1%, 内存: 72.5%, 磁盘: 94.4%
2025-07-30 14:47:22.197 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.3%, CPU使用率 79.2%
2025-07-30 14:47:37.307 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.2%, CPU使用率 70.8%
2025-07-30 14:47:52.472 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 74.0%, CPU使用率 96.9%
2025-07-30 14:48:07.585 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.5%, CPU使用率 82.8%
2025-07-30 14:48:18.967 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 67.8%, 内存: 73.7%, 磁盘: 94.4%
2025-07-30 14:48:22.702 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 75.1%, CPU使用率 92.9%
2025-07-30 14:48:37.985 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 74.7%, CPU使用率 100.0%
2025-07-30 14:48:53.112 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.0%, CPU使用率 100.0%
2025-07-30 14:49:08.219 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.5%, CPU使用率 95.8%
2025-07-30 14:49:20.148 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 88.7%, 内存: 73.3%, 磁盘: 94.4%
2025-07-30 14:49:23.664 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.7%, CPU使用率 96.2%
2025-07-30 14:49:38.771 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 74.8%, CPU使用率 71.4%
2025-07-30 14:49:53.878 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.6%, CPU使用率 37.0%
2025-07-30 14:50:08.984 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.7%, CPU使用率 79.2%
2025-07-30 14:50:21.455 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 97.9%, 内存: 71.5%, 磁盘: 94.4%
2025-07-30 14:50:24.190 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.7%, CPU使用率 83.3%
2025-07-30 14:50:39.297 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.5%, CPU使用率 50.0%
2025-07-30 14:50:54.404 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.0%, CPU使用率 58.3%
2025-07-30 14:51:09.513 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.0%, CPU使用率 91.7%
2025-07-30 14:51:22.510 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 84.0%, 内存: 72.5%, 磁盘: 94.4%
2025-07-30 14:51:24.655 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.0%, CPU使用率 75.0%
2025-07-30 14:51:39.762 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.2%, CPU使用率 45.8%
2025-07-30 14:51:55.126 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.3%, CPU使用率 83.0%
2025-07-30 14:52:10.353 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.6%, CPU使用率 96.6%
2025-07-30 14:52:23.804 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 79.6%, 内存: 74.2%, 磁盘: 94.4%
2025-07-30 14:52:25.537 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.9%, CPU使用率 100.0%
2025-07-30 14:52:40.647 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.4%, CPU使用率 82.1%
2025-07-30 14:52:55.772 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.8%, CPU使用率 92.9%
2025-07-30 14:53:11.136 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.9%, CPU使用率 100.0%
2025-07-30 14:53:24.846 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 70.8%, 内存: 71.6%, 磁盘: 94.5%
2025-07-30 14:53:26.247 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.6%, CPU使用率 67.9%
2025-07-30 14:53:41.357 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.5%, CPU使用率 50.0%
2025-07-30 14:53:56.507 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.1%, CPU使用率 100.0%
2025-07-30 14:54:11.842 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.6%, CPU使用率 100.0%
2025-07-30 14:54:25.896 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 79.2%, 内存: 71.6%, 磁盘: 94.4%
2025-07-30 14:54:27.271 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.1%, CPU使用率 100.0%
2025-07-30 14:54:42.476 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.2%, CPU使用率 100.0%
2025-07-30 14:54:57.598 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.8%, CPU使用率 85.7%
2025-07-30 14:55:12.705 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.9%, CPU使用率 66.7%
2025-07-30 14:55:27.149 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 72.2%, 内存: 72.8%, 磁盘: 94.4%
2025-07-30 14:55:27.975 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.2%, CPU使用率 100.0%
2025-07-30 14:55:43.318 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.9%, CPU使用率 97.8%
2025-07-30 14:55:58.811 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.7%, CPU使用率 100.0%
2025-07-30 14:56:13.929 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.4%, CPU使用率 46.4%
2025-07-30 14:56:28.381 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 92.6%, 内存: 71.2%, 磁盘: 94.4%
2025-07-30 14:56:29.037 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.5%, CPU使用率 57.1%
2025-07-30 14:56:44.586 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.2%, CPU使用率 98.7%
2025-07-30 14:56:59.732 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.3%, CPU使用率 66.7%
2025-07-30 14:57:14.842 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.0%, CPU使用率 41.7%
2025-07-30 14:57:29.409 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 75.2%, 内存: 71.9%, 磁盘: 94.4%
2025-07-30 14:57:29.949 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.1%, CPU使用率 100.0%
2025-07-30 14:57:45.409 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.8%, CPU使用率 100.0%
2025-07-30 14:58:00.515 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.1%, CPU使用率 66.7%
2025-07-30 14:58:15.622 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.6%, CPU使用率 79.2%
2025-07-30 14:58:30.445 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 88.8%, 内存: 70.3%, 磁盘: 94.5%
2025-07-30 14:58:30.728 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.2%, CPU使用率 25.0%
2025-07-30 14:58:46.066 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.2%, CPU使用率 100.0%
2025-07-30 14:59:01.304 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.0%, CPU使用率 100.0%
2025-07-30 14:59:16.433 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.6%, CPU使用率 100.0%
2025-07-30 14:59:31.482 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 93.0%, 内存: 69.2%, 磁盘: 94.4%
2025-07-30 14:59:31.539 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.2%, CPU使用率 67.9%
2025-07-30 14:59:46.826 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.7%, CPU使用率 100.0%
2025-07-30 15:00:02.028 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.7%, CPU使用率 92.1%
2025-07-30 15:00:17.333 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.1%, CPU使用率 100.0%
2025-07-30 15:00:32.443 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.2%, CPU使用率 59.3%
2025-07-30 15:00:32.510 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 79.0%, 内存: 71.2%, 磁盘: 94.4%
2025-07-30 15:00:47.627 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.8%, CPU使用率 84.2%
2025-07-30 15:01:02.835 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.9%, CPU使用率 100.0%
2025-07-30 15:01:17.942 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.8%, CPU使用率 78.6%
2025-07-30 15:01:33.170 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.5%, CPU使用率 100.0%
2025-07-30 15:01:33.544 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 81.9%, 内存: 69.9%, 磁盘: 94.4%
2025-07-30 15:01:48.391 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.0%, CPU使用率 100.0%
2025-07-30 15:02:03.900 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.7%, CPU使用率 98.6%
2025-07-30 15:02:19.490 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.9%, CPU使用率 100.0%
2025-07-30 15:02:34.630 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 90.5%, 内存: 71.7%, 磁盘: 94.4%
2025-07-30 15:02:34.704 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.5%, CPU使用率 100.0%
2025-07-30 15:02:50.069 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.6%, CPU使用率 98.2%
2025-07-30 15:03:05.349 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.7%, CPU使用率 97.4%
2025-07-30 15:03:20.531 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.0%, CPU使用率 100.0%
2025-07-30 15:03:35.654 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.6%, CPU使用率 100.0%
2025-07-30 15:03:35.758 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 98.5%, 内存: 68.1%, 磁盘: 94.4%
2025-07-30 15:03:50.859 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.8%, CPU使用率 100.0%
2025-07-30 15:04:06.095 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.6%, CPU使用率 100.0%
2025-07-30 15:04:21.235 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.4%, CPU使用率 87.5%
2025-07-30 15:04:36.364 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.6%, CPU使用率 82.1%
2025-07-30 15:04:36.908 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 94.4%, 内存: 68.6%, 磁盘: 94.4%
2025-07-30 15:04:51.656 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.2%, CPU使用率 100.0%
2025-07-30 15:05:06.843 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.6%, CPU使用率 96.2%
2025-07-30 15:05:22.008 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.6%, CPU使用率 100.0%
2025-07-30 15:05:37.116 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.1%, CPU使用率 66.7%
2025-07-30 15:05:37.935 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 88.1%, 内存: 69.6%, 磁盘: 94.4%
2025-07-30 15:05:52.337 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.6%, CPU使用率 100.0%
2025-07-30 15:06:07.448 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.2%, CPU使用率 83.3%
2025-07-30 15:06:22.557 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.3%, CPU使用率 88.0%
2025-07-30 15:06:37.712 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.9%, CPU使用率 97.2%
2025-07-30 15:06:38.958 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 75.1%, 内存: 66.2%, 磁盘: 94.4%
2025-07-30 15:06:53.103 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.1%, CPU使用率 100.0%
2025-07-30 15:07:08.655 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.0%, CPU使用率 100.0%
2025-07-30 15:07:23.839 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.3%, CPU使用率 100.0%
2025-07-30 15:07:38.976 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.6%, CPU使用率 96.4%
2025-07-30 15:07:40.147 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 90.1%, 内存: 64.6%, 磁盘: 94.4%
2025-07-30 15:07:54.086 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.1%, CPU使用率 71.4%
2025-07-30 15:08:09.190 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.9%, CPU使用率 39.3%
2025-07-30 15:08:24.294 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.6%, CPU使用率 54.2%
2025-07-30 15:08:39.406 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.5%, CPU使用率 56.0%
2025-07-30 15:08:41.169 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 39.8%, 内存: 61.4%, 磁盘: 94.4%
2025-07-30 15:08:54.511 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.4%, CPU使用率 21.4%
2025-07-30 15:09:08.747 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: GET /api/health ---
2025-07-30 15:09:08.749 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 15:09:08.749 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 15:09:08.751 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 15:09:09.615 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 62.1%, CPU使用率 0.0%
2025-07-30 15:09:10.769 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 15:09:10.776 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 15:09:10.777 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 15:09:10.779 [DEBUG] [app.core.db_connection] [db_connection.get_db_session:489] - 当前线程ID: 2244
2025-07-30 15:09:10.780 [INFO] [app.core.db_connection] [db_connection.get_db_session:492] - 尝试使用db_connection获取会话
2025-07-30 15:09:10.781 [INFO] [app.core.db_connection] [db_connection.connect:175] - 数据库连接已创建
2025-07-30 15:09:10.782 [DEBUG] [app.core.db_connection] [db_connection.checkout:179] - 数据库连接已检出
2025-07-30 15:09:10.783 [INFO] [app.core.db_connection] [db_connection.get_session:215] - 数据库连接成功 (尝试 1/3)
2025-07-30 15:09:10.783 [INFO] [app.core.db_connection] [db_connection.get_db_session:496] - 使用db_connection获取会话成功
2025-07-30 15:09:11.671 [INFO] [app.core.security] [security.verify_password:151] - 使用sha256_crypt验证密码成功
2025-07-30 15:09:11.673 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 15:09:24.721 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.1%, CPU使用率 28.0%
2025-07-30 15:09:39.827 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 62.9%, CPU使用率 16.7%
2025-07-30 15:09:42.193 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 37.1%, 内存: 62.8%, 磁盘: 94.4%
2025-07-30 15:09:54.933 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.3%, CPU使用率 45.8%
2025-07-30 15:10:10.038 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.1%, CPU使用率 26.9%
2025-07-30 15:10:25.143 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.0%, CPU使用率 48.1%
2025-07-30 15:10:40.247 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.3%, CPU使用率 41.7%
2025-07-30 15:10:43.214 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 44.9%, 内存: 61.2%, 磁盘: 94.4%
2025-07-30 15:10:55.352 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.2%, CPU使用率 39.3%
2025-07-30 15:11:10.458 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.2%, CPU使用率 22.2%
2025-07-30 15:11:25.562 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.1%, CPU使用率 52.2%
2025-07-30 15:11:40.667 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.3%, CPU使用率 34.6%
2025-07-30 15:11:44.237 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 38.5%, 内存: 61.3%, 磁盘: 94.4%
2025-07-30 15:11:55.790 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.3%, CPU使用率 46.7%
2025-07-30 15:12:10.899 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.1%, CPU使用率 28.6%
2025-07-30 15:12:26.004 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.2%, CPU使用率 21.4%
2025-07-30 15:12:41.109 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.3%, CPU使用率 4.2%
2025-07-30 15:12:45.403 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 94.6%, 内存: 61.6%, 磁盘: 94.4%
2025-07-30 15:12:53.233 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: GET /api/health ---
2025-07-30 15:12:53.236 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 15:12:53.237 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 15:12:53.239 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 15:12:55.269 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 15:12:55.270 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 15:12:55.271 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 15:12:55.273 [DEBUG] [app.core.db_connection] [db_connection.get_db_session:489] - 当前线程ID: 2244
2025-07-30 15:12:55.273 [INFO] [app.core.db_connection] [db_connection.get_db_session:492] - 尝试使用db_connection获取会话
2025-07-30 15:12:55.275 [INFO] [app.core.db_connection] [db_connection.connect:175] - 数据库连接已创建
2025-07-30 15:12:55.276 [DEBUG] [app.core.db_connection] [db_connection.checkout:179] - 数据库连接已检出
2025-07-30 15:12:55.277 [INFO] [app.core.db_connection] [db_connection.get_session:215] - 数据库连接成功 (尝试 1/3)
2025-07-30 15:12:55.277 [INFO] [app.core.db_connection] [db_connection.get_db_session:496] - 使用db_connection获取会话成功
2025-07-30 15:12:56.026 [INFO] [app.core.security] [security.verify_password:151] - 使用sha256_crypt验证密码成功
2025-07-30 15:12:56.028 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 15:12:56.215 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 62.4%, CPU使用率 75.0%
2025-07-30 15:13:11.326 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.5%, CPU使用率 87.5%
2025-07-30 15:13:26.431 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.2%, CPU使用率 0.0%
2025-07-30 15:13:41.537 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.0%, CPU使用率 24.0%
2025-07-30 15:13:46.431 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 18.4%, 内存: 63.0%, 磁盘: 94.4%
2025-07-30 15:13:56.643 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.1%, CPU使用率 19.2%
2025-07-30 15:14:11.751 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.0%, CPU使用率 29.2%
2025-07-30 15:14:26.856 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.1%, CPU使用率 16.7%
2025-07-30 15:14:41.963 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.0%, CPU使用率 8.7%
2025-07-30 15:14:47.452 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 25.3%, 内存: 63.1%, 磁盘: 94.4%
2025-07-30 15:14:57.067 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.1%, CPU使用率 7.7%
2025-07-30 15:15:12.173 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.0%, CPU使用率 25.0%
2025-07-30 15:15:27.279 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.1%, CPU使用率 4.2%
2025-07-30 15:15:42.383 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.1%, CPU使用率 3.6%
2025-07-30 15:15:48.481 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 50.8%, 内存: 65.4%, 磁盘: 94.4%
2025-07-30 15:15:57.488 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.9%, CPU使用率 29.6%
2025-07-30 15:16:12.592 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.8%, CPU使用率 8.3%
2025-07-30 15:16:27.765 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.4%, CPU使用率 76.5%
2025-07-30 15:16:42.876 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.9%, CPU使用率 37.5%
2025-07-30 15:16:49.512 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 79.0%, 内存: 67.3%, 磁盘: 94.4%
2025-07-30 15:16:57.983 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.4%, CPU使用率 68.0%
2025-07-30 15:17:13.090 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.3%, CPU使用率 87.5%
2025-07-30 15:17:28.197 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.3%, CPU使用率 72.0%
2025-07-30 15:17:43.401 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.2%, CPU使用率 92.7%
2025-07-30 15:17:50.626 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 76.8%, 内存: 68.6%, 磁盘: 94.5%
2025-07-30 15:17:58.506 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.5%, CPU使用率 26.9%
2025-07-30 15:18:13.627 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.5%, CPU使用率 79.3%
2025-07-30 15:18:28.847 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.2%, CPU使用率 100.0%
2025-07-30 15:18:44.004 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.8%, CPU使用率 82.8%
2025-07-30 15:18:51.751 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 86.1%, 内存: 68.0%, 磁盘: 94.4%
2025-07-30 15:18:59.112 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.5%, CPU使用率 67.9%
2025-07-30 15:19:14.227 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.3%, CPU使用率 60.7%
2025-07-30 15:19:29.343 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.5%, CPU使用率 96.0%
2025-07-30 15:19:44.493 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.8%, CPU使用率 87.1%
2025-07-30 15:19:52.837 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 99.2%, 内存: 66.0%, 磁盘: 94.4%
2025-07-30 15:19:59.938 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.4%, CPU使用率 100.0%
2025-07-30 15:20:15.047 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.6%, CPU使用率 58.3%
2025-07-30 15:20:30.183 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.1%, CPU使用率 96.4%
2025-07-30 15:20:45.386 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.6%, CPU使用率 100.0%
2025-07-30 15:20:53.911 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 99.2%, 内存: 69.5%, 磁盘: 94.4%
2025-07-30 15:21:00.528 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.0%, CPU使用率 100.0%
2025-07-30 15:21:15.691 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.0%, CPU使用率 77.8%
2025-07-30 15:21:30.892 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.7%, CPU使用率 97.1%
2025-07-30 15:21:46.005 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.9%, CPU使用率 71.4%
2025-07-30 15:21:55.463 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 97.5%, 内存: 66.7%, 磁盘: 94.4%
2025-07-30 15:22:01.338 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.5%, CPU使用率 100.0%
2025-07-30 15:22:16.645 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.4%, CPU使用率 57.1%
2025-07-30 15:22:32.020 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.9%, CPU使用率 100.0%
2025-07-30 15:22:47.189 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.2%, CPU使用率 87.9%
2025-07-30 15:22:56.498 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 96.2%, 内存: 69.6%, 磁盘: 94.5%
2025-07-30 15:23:02.681 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.7%, CPU使用率 100.0%
2025-07-30 15:23:18.280 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.4%, CPU使用率 100.0%
2025-07-30 15:23:34.047 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.1%, CPU使用率 100.0%
2025-07-30 15:23:49.157 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.1%, CPU使用率 45.8%
2025-07-30 15:23:57.643 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 56.6%, 内存: 68.6%, 磁盘: 94.5%
2025-07-30 15:24:04.264 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.1%, CPU使用率 79.2%
2025-07-30 15:24:19.372 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.7%, CPU使用率 87.5%
2025-07-30 15:24:34.486 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.2%, CPU使用率 83.3%
2025-07-30 15:24:49.592 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.2%, CPU使用率 45.8%
2025-07-30 15:24:58.953 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 81.6%, 内存: 67.9%, 磁盘: 94.5%
2025-07-30 15:25:04.905 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.2%, CPU使用率 90.2%
2025-07-30 15:25:20.677 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.0%, CPU使用率 100.0%
2025-07-30 15:25:35.798 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.7%, CPU使用率 100.0%
2025-07-30 15:25:50.910 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.1%, CPU使用率 85.7%
2025-07-30 15:26:00.215 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 86.1%, 内存: 69.1%, 磁盘: 94.5%
2025-07-30 15:26:06.383 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.0%, CPU使用率 100.0%
2025-07-30 15:26:21.825 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.3%, CPU使用率 100.0%
2025-07-30 15:26:37.104 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.5%, CPU使用率 100.0%
2025-07-30 15:26:52.385 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.6%, CPU使用率 94.2%
2025-07-30 15:27:01.430 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 100.0%, 内存: 68.4%, 磁盘: 94.5%
2025-07-30 15:27:08.017 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.4%, CPU使用率 100.0%
2025-07-30 15:27:23.728 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.4%, CPU使用率 100.0%
2025-07-30 15:27:39.100 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.7%, CPU使用率 100.0%
2025-07-30 15:27:54.223 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.7%, CPU使用率 100.0%
2025-07-30 15:28:03.023 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 95.3%, 内存: 70.5%, 磁盘: 94.5%
2025-07-30 15:28:09.787 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.6%, CPU使用率 100.0%
2025-07-30 15:28:25.241 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.4%, CPU使用率 100.0%
2025-07-30 15:28:40.874 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.1%, CPU使用率 100.0%
2025-07-30 15:28:56.224 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.9%, CPU使用率 100.0%
2025-07-30 15:29:04.745 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 99.2%, 内存: 72.2%, 磁盘: 94.5%
2025-07-30 15:29:11.782 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.3%, CPU使用率 100.0%
2025-07-30 15:29:27.344 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.1%, CPU使用率 100.0%
2025-07-30 15:29:42.623 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.2%, CPU使用率 100.0%
2025-07-30 15:29:57.859 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.8%, CPU使用率 100.0%
2025-07-30 15:30:06.131 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 100.0%, 内存: 69.6%, 磁盘: 94.5%
2025-07-30 15:30:13.794 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.1%, CPU使用率 100.0%
2025-07-30 15:30:28.952 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.4%, CPU使用率 41.7%
2025-07-30 15:30:44.057 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.1%, CPU使用率 58.3%
2025-07-30 15:30:59.317 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.9%, CPU使用率 100.0%
2025-07-30 15:31:07.339 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 90.2%, 内存: 71.4%, 磁盘: 94.5%
2025-07-30 15:31:14.428 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.0%, CPU使用率 58.6%
2025-07-30 15:31:29.752 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.4%, CPU使用率 96.2%
2025-07-30 15:31:45.484 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.1%, CPU使用率 100.0%
2025-07-30 15:32:01.207 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.2%, CPU使用率 100.0%
2025-07-30 15:32:08.564 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 100.0%, 内存: 71.1%, 磁盘: 94.5%
2025-07-30 15:32:16.373 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.4%, CPU使用率 100.0%
2025-07-30 15:32:31.592 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.1%, CPU使用率 100.0%
2025-07-30 15:32:47.069 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 74.4%, CPU使用率 98.4%
2025-07-30 15:32:48.733 [WARNING] [alert_manager] [alert_manager._create_alert:649] - 触发告警: cpu_usage, 当前值: 100.0, 阈值: 90
2025-07-30 15:33:02.271 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 74.1%, CPU使用率 97.2%
2025-07-30 15:33:10.087 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 100.0%, 内存: 73.6%, 磁盘: 94.5%
2025-07-30 15:33:17.500 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.7%, CPU使用率 92.3%
2025-07-30 15:33:32.762 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.8%, CPU使用率 100.0%
2025-07-30 15:33:47.895 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.2%, CPU使用率 100.0%
2025-07-30 15:34:03.001 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.7%, CPU使用率 83.3%
2025-07-30 15:34:11.215 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 87.7%, 内存: 73.9%, 磁盘: 94.5%
2025-07-30 15:34:18.289 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 76.2%, CPU使用率 71.8%
2025-07-30 15:34:33.396 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 76.7%, CPU使用率 46.4%
2025-07-30 15:34:48.503 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 75.1%, CPU使用率 57.1%
2025-07-30 15:35:03.609 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.6%, CPU使用率 83.3%
2025-07-30 15:35:12.266 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 53.1%, 内存: 73.5%, 磁盘: 94.5%
2025-07-30 15:35:18.738 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.4%, CPU使用率 76.7%
2025-07-30 15:35:33.905 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 74.9%, CPU使用率 58.3%
2025-07-30 15:35:49.019 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 75.7%, CPU使用率 92.9%
2025-07-30 15:36:04.344 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.7%, CPU使用率 84.2%
2025-07-30 15:36:13.348 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 79.4%, 内存: 75.0%, 磁盘: 94.5%
2025-07-30 15:36:19.524 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.8%, CPU使用率 60.7%
2025-07-30 15:36:34.947 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.9%, CPU使用率 100.0%
2025-07-30 15:36:50.053 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.5%, CPU使用率 46.4%
2025-07-30 15:37:05.158 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 76.4%, CPU使用率 89.3%
2025-07-30 15:37:14.377 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 82.1%, 内存: 72.0%, 磁盘: 94.5%
2025-07-30 15:37:20.554 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.2%, CPU使用率 100.0%
2025-07-30 15:37:35.994 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 75.2%, CPU使用率 100.0%
2025-07-30 15:37:51.170 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 74.6%, CPU使用率 83.3%
2025-07-30 15:38:06.278 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.2%, CPU使用率 92.0%
2025-07-30 15:38:15.415 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 84.4%, 内存: 74.5%, 磁盘: 94.5%
2025-07-30 15:38:21.470 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 74.1%, CPU使用率 100.0%
2025-07-30 15:38:36.578 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.1%, CPU使用率 61.5%
2025-07-30 15:38:51.685 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 74.1%, CPU使用率 83.3%
2025-07-30 15:39:06.792 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.7%, CPU使用率 52.0%
2025-07-30 15:39:16.632 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 95.8%, 内存: 71.5%, 磁盘: 94.5%
2025-07-30 15:39:22.037 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.9%, CPU使用率 94.3%
2025-07-30 15:39:37.153 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.7%, CPU使用率 100.0%
2025-07-30 15:39:52.287 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.8%, CPU使用率 71.4%
2025-07-30 15:40:07.482 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.2%, CPU使用率 100.0%
2025-07-30 15:40:17.849 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 71.9%, 内存: 71.3%, 磁盘: 94.5%
2025-07-30 15:40:22.589 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.2%, CPU使用率 75.0%
2025-07-30 15:40:37.696 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.6%, CPU使用率 82.1%
2025-07-30 15:40:52.804 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.1%, CPU使用率 78.6%
2025-07-30 15:41:08.080 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.3%, CPU使用率 100.0%
2025-07-30 15:41:19.159 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 76.8%, 内存: 73.4%, 磁盘: 94.5%
2025-07-30 15:41:23.252 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.0%, CPU使用率 83.9%
2025-07-30 15:41:38.407 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.6%, CPU使用率 91.2%
2025-07-30 15:41:53.629 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.0%, CPU使用率 100.0%
2025-07-30 15:42:09.281 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.6%, CPU使用率 100.0%
2025-07-30 15:42:20.236 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 71.0%, 内存: 70.4%, 磁盘: 94.5%
2025-07-30 15:42:24.670 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.4%, CPU使用率 100.0%
2025-07-30 15:42:39.865 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.5%, CPU使用率 87.1%
2025-07-30 15:42:54.977 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.5%, CPU使用率 89.3%
2025-07-30 15:43:10.368 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.1%, CPU使用率 95.2%
2025-07-30 15:43:21.474 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 86.9%, 内存: 72.7%, 磁盘: 94.5%
2025-07-30 15:43:25.688 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.0%, CPU使用率 79.5%
2025-07-30 15:43:40.924 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.2%, CPU使用率 53.6%
2025-07-30 15:43:56.031 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.8%, CPU使用率 100.0%
2025-07-30 15:44:11.397 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.5%, CPU使用率 94.1%
2025-07-30 15:44:22.789 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 73.3%, 内存: 69.3%, 磁盘: 94.5%
2025-07-30 15:44:26.673 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.5%, CPU使用率 71.4%
2025-07-30 15:44:41.788 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.9%, CPU使用率 57.1%
2025-07-30 15:44:57.092 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.3%, CPU使用率 100.0%
2025-07-30 15:45:12.199 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.4%, CPU使用率 54.2%
2025-07-30 15:45:24.346 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 85.1%, 内存: 72.3%, 磁盘: 94.5%
2025-07-30 15:45:27.306 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.2%, CPU使用率 53.6%
2025-07-30 15:45:42.426 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.9%, CPU使用率 89.3%
2025-07-30 15:45:57.538 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.2%, CPU使用率 42.9%
2025-07-30 15:46:12.643 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.5%, CPU使用率 50.0%
2025-07-30 15:46:25.394 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 98.1%, 内存: 70.2%, 磁盘: 94.4%
2025-07-30 15:46:27.899 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.1%, CPU使用率 100.0%
2025-07-30 15:46:43.043 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.7%, CPU使用率 95.8%
2025-07-30 15:46:58.176 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.7%, CPU使用率 85.7%
2025-07-30 15:47:13.283 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.4%, CPU使用率 95.8%
2025-07-30 15:47:26.437 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 82.4%, 内存: 69.1%, 磁盘: 94.5%
2025-07-30 15:47:28.390 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.4%, CPU使用率 66.7%
2025-07-30 15:47:43.515 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.8%, CPU使用率 100.0%
2025-07-30 15:47:58.625 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.7%, CPU使用率 50.0%
2025-07-30 15:48:14.040 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.3%, CPU使用率 100.0%
2025-07-30 15:48:27.469 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 93.4%, 内存: 70.5%, 磁盘: 94.5%
2025-07-30 15:48:29.557 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.8%, CPU使用率 100.0%
2025-07-30 15:48:44.884 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.4%, CPU使用率 100.0%
2025-07-30 15:49:00.182 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.3%, CPU使用率 100.0%
2025-07-30 15:49:15.809 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 74.6%, CPU使用率 98.9%
2025-07-30 15:49:28.513 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 82.9%, 内存: 69.9%, 磁盘: 94.5%
2025-07-30 15:49:31.238 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.3%, CPU使用率 98.5%
2025-07-30 15:49:46.839 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.4%, CPU使用率 100.0%
2025-07-30 15:50:02.336 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.1%, CPU使用率 100.0%
2025-07-30 15:50:17.720 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.3%, CPU使用率 95.9%
2025-07-30 15:50:29.670 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 95.5%, 内存: 73.2%, 磁盘: 94.5%
2025-07-30 15:50:33.041 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.5%, CPU使用率 94.1%
2025-07-30 15:50:48.173 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.3%, CPU使用率 96.4%
2025-07-30 15:51:03.280 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 74.3%, CPU使用率 83.3%
2025-07-30 15:51:18.337 [DEBUG] [app.core.db_connection] [db_connection.checkin:183] - 数据库连接已检入
2025-07-30 15:51:18.660 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.4%, CPU使用率 100.0%
2025-07-30 15:51:30.964 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 85.0%, 内存: 70.4%, 磁盘: 94.5%
2025-07-30 15:51:34.313 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.4%, CPU使用率 100.0%
2025-07-30 15:51:49.479 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.8%, CPU使用率 100.0%
2025-07-30 15:52:04.604 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.2%, CPU使用率 92.9%
2025-07-30 15:52:19.905 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.8%, CPU使用率 96.3%
2025-07-30 15:52:32.767 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 90.2%, 内存: 71.0%, 磁盘: 94.5%
2025-07-30 15:52:35.011 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.8%, CPU使用率 79.2%
2025-07-30 15:52:50.137 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.8%, CPU使用率 87.5%
2025-07-30 15:53:05.543 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.9%, CPU使用率 98.0%
2025-07-30 15:53:20.684 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.2%, CPU使用率 83.3%
2025-07-30 15:53:34.423 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 93.3%, 内存: 70.7%, 磁盘: 94.5%
2025-07-30 15:53:35.967 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.9%, CPU使用率 90.5%
2025-07-30 15:53:51.320 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.7%, CPU使用率 100.0%
2025-07-30 15:54:06.925 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.4%, CPU使用率 100.0%
2025-07-30 15:54:22.344 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.2%, CPU使用率 100.0%
2025-07-30 15:54:36.152 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 99.3%, 内存: 72.2%, 磁盘: 94.5%
2025-07-30 15:54:37.510 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.9%, CPU使用率 100.0%
2025-07-30 15:54:52.837 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.9%, CPU使用率 100.0%
2025-07-30 15:55:07.946 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.5%, CPU使用率 64.3%
2025-07-30 15:55:23.425 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.0%, CPU使用率 100.0%
2025-07-30 15:55:37.481 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 92.5%, 内存: 71.2%, 磁盘: 94.5%
2025-07-30 15:55:38.683 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.8%, CPU使用率 100.0%
2025-07-30 15:55:53.814 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.6%, CPU使用率 92.9%
2025-07-30 15:56:08.922 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.7%, CPU使用率 62.5%
2025-07-30 15:56:24.029 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.2%, CPU使用率 87.5%
2025-07-30 15:56:38.567 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 95.7%, 内存: 72.5%, 磁盘: 94.5%
2025-07-30 15:56:39.137 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.3%, CPU使用率 70.8%
2025-07-30 15:56:54.244 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.0%, CPU使用率 54.2%
2025-07-30 15:57:09.378 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.8%, CPU使用率 94.1%
2025-07-30 15:57:24.761 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.8%, CPU使用率 100.0%
2025-07-30 15:57:39.914 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 89.6%, 内存: 69.1%, 磁盘: 94.5%
2025-07-30 15:57:40.091 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.4%, CPU使用率 100.0%
2025-07-30 15:57:55.327 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.2%, CPU使用率 98.0%
2025-07-30 15:58:10.437 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.3%, CPU使用率 79.2%
2025-07-30 15:58:25.644 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.3%, CPU使用率 94.7%
2025-07-30 15:58:40.749 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.1%, CPU使用率 4.3%
2025-07-30 15:58:41.002 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 13.2%, 内存: 67.1%, 磁盘: 94.5%
2025-07-30 15:58:55.855 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.9%, CPU使用率 19.2%
2025-07-30 15:59:10.965 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.2%, CPU使用率 13.0%
2025-07-30 15:59:26.075 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.9%, CPU使用率 45.8%
2025-07-30 15:59:41.182 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.1%, CPU使用率 100.0%
2025-07-30 15:59:42.060 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 97.7%, 内存: 66.5%, 磁盘: 94.5%
2025-07-30 15:59:51.605 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: GET /api/health ---
2025-07-30 15:59:51.606 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 15:59:51.606 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 15:59:51.608 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 15:59:53.657 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 15:59:53.658 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 15:59:53.659 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 15:59:53.661 [DEBUG] [app.core.db_connection] [db_connection.get_db_session:489] - 当前线程ID: 2244
2025-07-30 15:59:53.661 [INFO] [app.core.db_connection] [db_connection.get_db_session:492] - 尝试使用db_connection获取会话
2025-07-30 15:59:53.662 [DEBUG] [app.core.db_connection] [db_connection.checkout:179] - 数据库连接已检出
2025-07-30 15:59:53.663 [INFO] [app.core.db_connection] [db_connection.get_session:215] - 数据库连接成功 (尝试 1/3)
2025-07-30 15:59:53.664 [INFO] [app.core.db_connection] [db_connection.get_db_session:496] - 使用db_connection获取会话成功
2025-07-30 15:59:54.506 [INFO] [app.core.security] [security.verify_password:151] - 使用sha256_crypt验证密码成功
2025-07-30 15:59:54.509 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 15:59:56.287 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.8%, CPU使用率 30.8%
2025-07-30 16:00:11.392 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.9%, CPU使用率 0.0%
2025-07-30 16:00:26.510 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.9%, CPU使用率 75.0%
2025-07-30 16:00:41.616 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.5%, CPU使用率 21.4%
2025-07-30 16:00:43.090 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 32.6%, 内存: 67.3%, 磁盘: 94.5%
2025-07-30 16:00:56.720 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.3%, CPU使用率 20.0%
2025-07-30 16:01:11.825 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.3%, CPU使用率 8.3%
2025-07-30 16:01:26.930 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.2%, CPU使用率 17.9%
2025-07-30 16:01:42.042 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.3%, CPU使用率 32.1%
2025-07-30 16:01:44.110 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 16.8%, 内存: 67.2%, 磁盘: 94.5%
2025-07-30 16:01:57.148 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.1%, CPU使用率 21.4%
2025-07-30 16:02:12.253 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.2%, CPU使用率 14.8%
2025-07-30 16:02:27.359 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.2%, CPU使用率 25.0%
2025-07-30 16:02:42.464 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.2%, CPU使用率 41.7%
2025-07-30 16:02:45.134 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 14.6%, 内存: 67.1%, 磁盘: 94.5%
2025-07-30 16:02:57.569 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.2%, CPU使用率 7.1%
2025-07-30 16:03:12.674 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.3%, CPU使用率 3.8%
2025-07-30 16:03:27.779 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.3%, CPU使用率 0.0%
2025-07-30 16:03:42.884 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.2%, CPU使用率 7.7%
2025-07-30 16:03:46.161 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 16.4%, 内存: 67.2%, 磁盘: 94.5%
2025-07-30 16:03:57.988 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.1%, CPU使用率 0.0%
2025-07-30 16:04:13.094 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.0%, CPU使用率 16.7%
2025-07-30 16:04:28.198 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.1%, CPU使用率 12.5%
2025-07-30 16:04:43.303 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.2%, CPU使用率 14.3%
2025-07-30 16:04:47.195 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 24.5%, 内存: 67.2%, 磁盘: 94.5%
2025-07-30 16:04:58.408 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.3%, CPU使用率 0.0%
2025-07-30 16:05:13.514 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.4%, CPU使用率 0.0%
2025-07-30 16:05:28.619 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.2%, CPU使用率 7.7%
2025-07-30 16:05:43.727 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.3%, CPU使用率 67.9%
2025-07-30 16:05:48.217 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 38.1%, 内存: 67.3%, 磁盘: 94.5%
2025-07-30 16:05:58.832 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.2%, CPU使用率 12.0%
2025-07-30 16:06:13.936 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.3%, CPU使用率 4.2%
2025-07-30 16:06:29.054 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.3%, CPU使用率 62.5%
2025-07-30 16:06:44.159 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.3%, CPU使用率 0.0%
2025-07-30 16:06:49.396 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 28.6%, 内存: 67.4%, 磁盘: 94.5%
2025-07-30 16:06:59.263 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.2%, CPU使用率 29.2%
2025-07-30 16:07:14.369 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.3%, CPU使用率 12.5%
2025-07-30 16:07:29.473 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.3%, CPU使用率 23.1%
2025-07-30 16:07:44.577 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.3%, CPU使用率 16.7%
2025-07-30 16:07:50.418 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 19.1%, 内存: 67.3%, 磁盘: 94.5%
2025-07-30 16:07:59.682 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.3%, CPU使用率 25.0%
2025-07-30 16:08:14.787 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.3%, CPU使用率 53.6%
2025-07-30 16:08:29.892 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.4%, CPU使用率 4.2%
2025-07-30 16:08:44.996 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.3%, CPU使用率 25.0%
2025-07-30 16:08:51.439 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 24.2%, 内存: 67.3%, 磁盘: 94.5%
2025-07-30 16:09:00.101 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.4%, CPU使用率 28.6%
2025-07-30 16:09:15.206 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.3%, CPU使用率 7.4%
2025-07-30 16:09:30.311 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.4%, CPU使用率 32.0%
2025-07-30 16:09:45.424 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.3%, CPU使用率 23.1%
2025-07-30 16:09:52.461 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 10.2%, 内存: 67.2%, 磁盘: 94.5%
2025-07-30 16:10:00.529 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.3%, CPU使用率 25.0%
2025-07-30 16:10:15.634 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.2%, CPU使用率 14.3%
2025-07-30 16:10:30.737 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.4%, CPU使用率 26.9%
2025-07-30 16:10:45.843 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.3%, CPU使用率 12.5%
2025-07-30 16:10:53.482 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 14.8%, 内存: 67.4%, 磁盘: 94.5%
2025-07-30 16:11:00.947 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.3%, CPU使用率 0.0%
2025-07-30 16:11:16.056 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.3%, CPU使用率 64.3%
2025-07-30 16:11:31.160 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.4%, CPU使用率 15.4%
2025-07-30 16:11:46.265 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.4%, CPU使用率 4.2%
2025-07-30 16:11:54.504 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 26.2%, 内存: 67.4%, 磁盘: 94.5%
2025-07-30 16:12:01.370 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.5%, CPU使用率 24.0%
2025-07-30 16:12:16.474 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.5%, CPU使用率 25.9%
2025-07-30 16:12:31.586 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.4%, CPU使用率 28.6%
2025-07-30 16:12:46.697 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.4%, CPU使用率 23.1%
2025-07-30 16:12:55.526 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 23.2%, 内存: 67.4%, 磁盘: 94.5%
2025-07-30 16:13:01.801 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.3%, CPU使用率 3.8%
2025-07-30 16:13:16.907 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.3%, CPU使用率 20.8%
2025-07-30 16:13:32.011 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.2%, CPU使用率 8.3%
2025-07-30 16:13:47.115 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.3%, CPU使用率 17.9%
2025-07-30 16:13:56.546 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 33.6%, 内存: 67.4%, 磁盘: 94.5%
2025-07-30 16:14:02.220 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.5%, CPU使用率 16.7%
2025-07-30 16:14:17.335 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.4%, CPU使用率 42.9%
2025-07-30 16:14:32.439 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.5%, CPU使用率 4.2%
2025-07-30 16:14:47.545 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.4%, CPU使用率 12.5%
2025-07-30 16:14:57.570 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 20.3%, 内存: 67.3%, 磁盘: 94.5%
2025-07-30 16:15:02.650 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.4%, CPU使用率 19.2%
2025-07-30 16:15:17.754 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.3%, CPU使用率 11.5%
2025-07-30 16:15:32.859 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.3%, CPU使用率 8.3%
2025-07-30 16:15:48.195 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.7%, CPU使用率 17.9%
2025-07-30 16:15:58.592 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 10.5%, 内存: 67.8%, 磁盘: 94.5%
2025-07-30 16:16:03.299 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.7%, CPU使用率 14.8%
2025-07-30 16:16:18.404 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.5%, CPU使用率 4.2%
2025-07-30 16:16:33.510 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.7%, CPU使用率 28.0%
2025-07-30 16:16:48.614 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.5%, CPU使用率 24.1%
2025-07-30 16:16:59.612 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 9.8%, 内存: 67.4%, 磁盘: 94.5%
2025-07-30 16:17:03.725 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.5%, CPU使用率 50.0%
2025-07-30 16:17:18.834 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.5%, CPU使用率 32.1%
2025-07-30 16:17:33.939 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.9%, CPU使用率 41.7%
2025-07-30 16:17:49.043 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.8%, CPU使用率 0.0%
2025-07-30 16:18:00.635 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 20.2%, 内存: 67.8%, 磁盘: 94.5%
2025-07-30 16:18:04.149 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.9%, CPU使用率 7.1%
2025-07-30 16:18:19.255 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.6%, CPU使用率 7.1%
2025-07-30 16:18:34.359 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.7%, CPU使用率 16.0%
2025-07-30 16:18:49.465 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.4%, CPU使用率 54.2%
2025-07-30 16:19:01.656 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 12.9%, 内存: 67.6%, 磁盘: 94.5%
2025-07-30 16:19:04.702 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.6%, CPU使用率 97.6%
2025-07-30 16:19:19.806 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.4%, CPU使用率 12.5%
2025-07-30 16:19:34.912 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.4%, CPU使用率 17.9%
2025-07-30 16:19:50.017 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.5%, CPU使用率 4.2%
2025-07-30 16:20:02.677 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 14.1%, 内存: 67.4%, 磁盘: 94.5%
2025-07-30 16:20:05.121 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.5%, CPU使用率 8.3%
2025-07-30 16:20:20.227 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.4%, CPU使用率 7.1%
2025-07-30 16:20:35.331 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.5%, CPU使用率 11.5%
2025-07-30 16:20:50.437 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.4%, CPU使用率 4.2%
2025-07-30 16:21:03.697 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 14.6%, 内存: 67.5%, 磁盘: 94.5%
2025-07-30 16:21:05.541 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.5%, CPU使用率 11.1%
2025-07-30 16:21:20.646 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.4%, CPU使用率 17.9%
2025-07-30 16:21:35.751 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.5%, CPU使用率 0.0%
2025-07-30 16:21:50.855 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.5%, CPU使用率 0.0%
2025-07-30 16:22:04.722 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 15.2%, 内存: 67.5%, 磁盘: 94.5%
2025-07-30 16:22:05.960 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.5%, CPU使用率 7.1%
2025-07-30 16:22:21.071 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.4%, CPU使用率 64.3%
2025-07-30 16:22:36.178 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.4%, CPU使用率 3.8%
2025-07-30 16:22:51.282 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.3%, CPU使用率 12.5%
2025-07-30 16:23:05.743 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 22.7%, 内存: 67.4%, 磁盘: 94.5%
2025-07-30 16:23:06.386 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.4%, CPU使用率 0.0%
2025-07-30 16:23:21.499 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.5%, CPU使用率 75.0%
2025-07-30 16:23:36.604 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.6%, CPU使用率 4.0%
2025-07-30 16:23:51.709 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.8%, CPU使用率 7.7%
2025-07-30 16:24:06.764 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 12.1%, 内存: 67.7%, 磁盘: 94.5%
2025-07-30 16:24:06.813 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.7%, CPU使用率 4.2%
2025-07-30 16:24:21.917 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.7%, CPU使用率 16.7%
2025-07-30 16:24:37.021 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.2%, CPU使用率 3.6%
2025-07-30 16:24:52.127 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.2%, CPU使用率 35.7%
2025-07-30 16:25:07.237 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.9%, CPU使用率 11.5%
2025-07-30 16:25:07.786 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 36.2%, 内存: 64.7%, 磁盘: 94.5%
2025-07-30 16:25:22.341 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.8%, CPU使用率 33.3%
2025-07-30 16:25:37.447 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.6%, CPU使用率 3.6%
2025-07-30 16:25:52.552 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.7%, CPU使用率 34.6%
2025-07-30 16:26:07.657 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.6%, CPU使用率 65.4%
2025-07-30 16:26:08.816 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 60.2%, 内存: 64.7%, 磁盘: 94.4%
2025-07-30 16:26:15.516 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: GET /api/health ---
2025-07-30 16:26:15.517 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 16:26:15.518 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 16:26:15.519 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 16:26:17.550 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 16:26:17.552 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 16:26:17.552 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 16:26:17.553 [DEBUG] [app.core.db_connection] [db_connection.get_db_session:489] - 当前线程ID: 2244
2025-07-30 16:26:17.554 [INFO] [app.core.db_connection] [db_connection.get_db_session:492] - 尝试使用db_connection获取会话
2025-07-30 16:26:17.555 [INFO] [app.core.db_connection] [db_connection.connect:175] - 数据库连接已创建
2025-07-30 16:26:17.556 [DEBUG] [app.core.db_connection] [db_connection.checkout:179] - 数据库连接已检出
2025-07-30 16:26:17.556 [INFO] [app.core.db_connection] [db_connection.get_session:215] - 数据库连接成功 (尝试 1/3)
2025-07-30 16:26:17.557 [INFO] [app.core.db_connection] [db_connection.get_db_session:496] - 使用db_connection获取会话成功
2025-07-30 16:26:18.409 [INFO] [app.core.security] [security.verify_password:151] - 使用sha256_crypt验证密码成功
2025-07-30 16:26:18.411 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 16:26:22.765 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.4%, CPU使用率 58.3%
2025-07-30 16:26:37.871 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.2%, CPU使用率 45.8%
2025-07-30 16:26:52.975 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.1%, CPU使用率 3.8%
2025-07-30 16:27:08.080 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.2%, CPU使用率 16.0%
2025-07-30 16:27:09.838 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 21.1%, 内存: 66.2%, 磁盘: 94.4%
2025-07-30 16:27:23.186 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.2%, CPU使用率 16.7%
2025-07-30 16:27:38.291 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.1%, CPU使用率 19.2%
2025-07-30 16:27:53.396 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.2%, CPU使用率 35.7%
2025-07-30 16:28:08.500 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.4%, CPU使用率 20.0%
2025-07-30 16:28:10.858 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 16.7%, 内存: 66.3%, 磁盘: 94.4%
2025-07-30 16:28:23.604 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.3%, CPU使用率 4.2%
2025-07-30 16:28:38.710 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.4%, CPU使用率 21.4%
2025-07-30 16:28:53.816 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.2%, CPU使用率 19.2%
2025-07-30 16:29:08.921 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.4%, CPU使用率 0.0%
2025-07-30 16:29:11.880 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 53.2%, 内存: 66.4%, 磁盘: 94.4%
2025-07-30 16:29:24.027 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.4%, CPU使用率 66.7%
2025-07-30 16:29:39.133 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.4%, CPU使用率 10.7%
2025-07-30 16:29:54.238 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.3%, CPU使用率 10.7%
2025-07-30 16:30:09.344 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.3%, CPU使用率 72.0%
2025-07-30 16:30:12.902 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 26.6%, 内存: 66.2%, 磁盘: 94.4%
2025-07-30 16:30:24.449 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.3%, CPU使用率 41.7%
2025-07-30 16:30:39.555 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.3%, CPU使用率 19.2%
2025-07-30 16:30:54.663 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.3%, CPU使用率 39.3%
2025-07-30 16:31:09.770 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.8%, CPU使用率 78.6%
2025-07-30 16:31:13.926 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 84.8%, 内存: 69.7%, 磁盘: 94.4%
2025-07-30 16:31:24.875 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.2%, CPU使用率 37.5%
2025-07-30 16:31:39.980 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.4%, CPU使用率 79.2%
2025-07-30 16:31:55.088 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.9%, CPU使用率 76.0%
2025-07-30 16:32:10.288 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.5%, CPU使用率 91.2%
2025-07-30 16:32:14.950 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 70.8%, 内存: 68.7%, 磁盘: 94.4%
2025-07-30 16:32:25.467 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.8%, CPU使用率 100.0%
2025-07-30 16:32:41.062 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.4%, CPU使用率 100.0%
2025-07-30 16:32:56.186 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.9%, CPU使用率 93.1%
2025-07-30 16:33:11.408 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.7%, CPU使用率 100.0%
2025-07-30 16:33:16.000 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 91.2%, 内存: 68.6%, 磁盘: 94.4%
2025-07-30 16:33:26.532 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.3%, CPU使用率 83.3%
2025-07-30 16:33:41.638 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.4%, CPU使用率 91.7%
2025-07-30 16:33:56.744 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.2%, CPU使用率 48.0%
2025-07-30 16:34:11.908 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.0%, CPU使用率 97.1%
2025-07-30 16:34:17.444 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 91.2%, 内存: 70.6%, 磁盘: 94.5%
2025-07-30 16:34:27.165 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.1%, CPU使用率 100.0%
2025-07-30 16:34:42.340 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.5%, CPU使用率 75.0%
2025-07-30 16:34:57.497 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.1%, CPU使用率 100.0%
2025-07-30 16:35:12.918 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.2%, CPU使用率 95.2%
2025-07-30 16:35:18.699 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 76.3%, 内存: 69.2%, 磁盘: 94.4%
2025-07-30 16:35:28.267 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.7%, CPU使用率 100.0%
2025-07-30 16:35:43.376 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.3%, CPU使用率 60.0%
2025-07-30 16:35:58.594 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.7%, CPU使用率 96.8%
2025-07-30 16:36:13.977 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.3%, CPU使用率 100.0%
2025-07-30 16:36:19.889 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 83.5%, 内存: 71.0%, 磁盘: 94.4%
2025-07-30 16:36:29.157 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.9%, CPU使用率 66.7%
2025-07-30 16:36:44.420 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.9%, CPU使用率 90.2%
2025-07-30 16:36:59.722 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.5%, CPU使用率 96.6%
2025-07-30 16:37:14.831 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.1%, CPU使用率 96.4%
2025-07-30 16:37:20.927 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 72.0%, 内存: 67.3%, 磁盘: 94.5%
2025-07-30 16:37:29.939 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.2%, CPU使用率 50.0%
2025-07-30 16:37:45.045 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.0%, CPU使用率 29.2%
2025-07-30 16:38:00.178 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.6%, CPU使用率 100.0%
2025-07-30 16:38:06.075 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: GET /api/health ---
2025-07-30 16:38:06.076 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 16:38:06.077 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 16:38:06.078 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 16:38:08.111 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 16:38:08.139 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 16:38:08.161 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 16:38:08.176 [DEBUG] [app.core.db_connection] [db_connection.get_db_session:489] - 当前线程ID: 2244
2025-07-30 16:38:08.176 [INFO] [app.core.db_connection] [db_connection.get_db_session:492] - 尝试使用db_connection获取会话
2025-07-30 16:38:08.178 [INFO] [app.core.db_connection] [db_connection.connect:175] - 数据库连接已创建
2025-07-30 16:38:08.178 [DEBUG] [app.core.db_connection] [db_connection.checkout:179] - 数据库连接已检出
2025-07-30 16:38:08.179 [INFO] [app.core.db_connection] [db_connection.get_session:215] - 数据库连接成功 (尝试 1/3)
2025-07-30 16:38:08.180 [INFO] [app.core.db_connection] [db_connection.get_db_session:496] - 使用db_connection获取会话成功
2025-07-30 16:38:10.993 [INFO] [app.core.security] [security.verify_password:151] - 使用sha256_crypt验证密码成功
2025-07-30 16:38:10.997 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 16:38:15.283 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.7%, CPU使用率 70.8%
2025-07-30 16:38:22.580 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 96.1%, 内存: 70.8%, 磁盘: 94.5%
2025-07-30 16:38:30.710 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.3%, CPU使用率 98.7%
2025-07-30 16:38:46.483 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.2%, CPU使用率 99.2%
2025-07-30 16:39:01.871 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.9%, CPU使用率 97.4%
2025-07-30 16:39:17.462 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.7%, CPU使用率 100.0%
2025-07-30 16:39:23.897 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 92.5%, 内存: 70.5%, 磁盘: 94.4%
2025-07-30 16:39:32.650 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.4%, CPU使用率 100.0%
2025-07-30 16:39:48.010 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.7%, CPU使用率 100.0%
2025-07-30 16:40:03.136 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.8%, CPU使用率 80.0%
2025-07-30 16:40:18.290 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.2%, CPU使用率 100.0%
2025-07-30 16:40:25.054 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 89.1%, 内存: 70.0%, 磁盘: 94.4%
2025-07-30 16:40:33.566 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.8%, CPU使用率 91.2%
2025-07-30 16:40:48.849 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.9%, CPU使用率 67.9%
2025-07-30 16:41:04.007 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.7%, CPU使用率 95.0%
2025-07-30 16:41:19.186 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.1%, CPU使用率 87.1%
2025-07-30 16:41:26.077 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 47.7%, 内存: 62.9%, 磁盘: 94.4%
2025-07-30 16:41:34.299 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 62.9%, CPU使用率 38.5%
2025-07-30 16:41:49.405 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.0%, CPU使用率 37.5%
2025-07-30 16:42:04.509 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.1%, CPU使用率 19.2%
2025-07-30 16:42:19.621 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.2%, CPU使用率 75.0%
2025-07-30 16:42:27.125 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 47.3%, 内存: 63.2%, 磁盘: 94.4%
2025-07-30 16:42:34.726 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.3%, CPU使用率 35.7%
2025-07-30 16:42:49.833 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.1%, CPU使用率 53.6%
2025-07-30 16:43:04.939 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.9%, CPU使用率 48.1%
2025-07-30 16:43:20.116 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.8%, CPU使用率 92.3%
2025-07-30 16:43:28.199 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 70.4%, 内存: 65.6%, 磁盘: 94.4%
2025-07-30 16:43:35.224 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.1%, CPU使用率 72.4%
2025-07-30 16:43:50.418 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.5%, CPU使用率 100.0%
2025-07-30 16:44:05.633 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.4%, CPU使用率 100.0%
2025-07-30 16:44:20.745 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.1%, CPU使用率 37.5%
2025-07-30 16:44:29.447 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 69.8%, 内存: 66.1%, 磁盘: 94.4%
2025-07-30 16:44:35.851 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.1%, CPU使用率 78.6%
2025-07-30 16:44:51.033 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.4%, CPU使用率 100.0%
2025-07-30 16:45:06.150 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.6%, CPU使用率 95.8%
2025-07-30 16:45:21.367 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.5%, CPU使用率 96.8%
2025-07-30 16:45:30.481 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 67.4%, 内存: 68.4%, 磁盘: 94.4%
2025-07-30 16:45:36.532 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.4%, CPU使用率 100.0%
2025-07-30 16:45:51.834 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.1%, CPU使用率 100.0%
2025-07-30 16:46:07.083 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.3%, CPU使用率 100.0%
2025-07-30 16:46:22.200 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.0%, CPU使用率 50.0%
2025-07-30 16:46:31.511 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 62.9%, 内存: 67.7%, 磁盘: 94.4%
2025-07-30 16:46:37.653 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.7%, CPU使用率 100.0%
2025-07-30 16:46:52.766 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.3%, CPU使用率 58.3%
2025-07-30 16:47:07.871 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.2%, CPU使用率 37.5%
2025-07-30 16:47:23.015 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.6%, CPU使用率 96.7%
2025-07-30 16:47:32.539 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 79.8%, 内存: 66.6%, 磁盘: 94.4%
2025-07-30 16:47:38.218 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.0%, CPU使用率 95.7%
2025-07-30 16:47:53.324 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.7%, CPU使用率 62.5%
2025-07-30 16:48:08.431 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.6%, CPU使用率 70.8%
2025-07-30 16:48:23.549 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.7%, CPU使用率 93.9%
2025-07-30 16:48:33.573 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 81.5%, 内存: 67.5%, 磁盘: 94.4%
2025-07-30 16:48:38.674 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.8%, CPU使用率 100.0%
2025-07-30 16:48:53.795 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.6%, CPU使用率 66.7%
2025-07-30 16:49:08.903 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.5%, CPU使用率 79.2%
2025-07-30 16:49:24.160 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.2%, CPU使用率 86.1%
2025-07-30 16:49:34.667 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 85.4%, 内存: 66.9%, 磁盘: 94.4%
2025-07-30 16:49:39.269 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.1%, CPU使用率 55.6%
2025-07-30 16:49:54.574 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.2%, CPU使用率 89.5%
2025-07-30 16:50:09.760 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.6%, CPU使用率 100.0%
2025-07-30 16:50:24.950 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.9%, CPU使用率 97.4%
2025-07-30 16:50:35.874 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 80.8%, 内存: 68.1%, 磁盘: 94.4%
2025-07-30 16:50:40.100 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.0%, CPU使用率 93.3%
2025-07-30 16:50:55.246 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.0%, CPU使用率 87.5%
2025-07-30 16:51:01.718 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: GET /api/health ---
2025-07-30 16:51:01.719 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 16:51:01.720 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 16:51:01.721 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 16:51:10.357 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.6%, CPU使用率 87.5%
2025-07-30 16:51:25.466 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.6%, CPU使用率 83.3%
2025-07-30 16:51:36.988 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 66.8%, 内存: 70.1%, 磁盘: 94.4%
2025-07-30 16:51:40.887 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.2%, CPU使用率 100.0%
2025-07-30 16:51:56.498 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.8%, CPU使用率 100.0%
2025-07-30 16:52:11.789 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.8%, CPU使用率 95.7%
2025-07-30 16:52:27.314 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.4%, CPU使用率 100.0%
2025-07-30 16:52:38.155 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 75.6%, 内存: 67.6%, 磁盘: 94.4%
2025-07-30 16:52:42.550 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.6%, CPU使用率 42.9%
2025-07-30 16:52:58.109 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.9%, CPU使用率 100.0%
2025-07-30 16:53:13.818 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.4%, CPU使用率 100.0%
2025-07-30 16:53:28.997 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.8%, CPU使用率 96.4%
2025-07-30 16:53:39.250 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 94.3%, 内存: 67.6%, 磁盘: 94.4%
2025-07-30 16:53:44.352 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.8%, CPU使用率 98.0%
2025-07-30 16:53:59.460 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.7%, CPU使用率 89.3%
2025-07-30 16:54:14.587 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.4%, CPU使用率 84.4%
2025-07-30 16:54:29.790 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.7%, CPU使用率 100.0%
2025-07-30 16:54:40.419 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 76.2%, 内存: 68.8%, 磁盘: 94.4%
2025-07-30 16:54:45.045 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.5%, CPU使用率 100.0%
2025-07-30 16:55:00.262 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.2%, CPU使用率 100.0%
2025-07-30 16:55:15.382 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.3%, CPU使用率 72.4%
2025-07-30 16:55:30.685 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.9%, CPU使用率 100.0%
2025-07-30 16:55:41.640 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 68.8%, 内存: 69.0%, 磁盘: 94.4%
2025-07-30 16:55:45.791 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.4%, CPU使用率 75.0%
2025-07-30 16:56:01.186 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.9%, CPU使用率 100.0%
2025-07-30 16:56:15.807 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: GET /api/health ---
2025-07-30 16:56:15.811 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 16:56:15.811 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 16:56:15.813 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 16:56:16.411 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.3%, CPU使用率 100.0%
2025-07-30 16:56:18.107 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 16:56:18.115 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 16:56:18.119 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 16:56:18.120 [DEBUG] [app.core.db_connection] [db_connection.get_db_session:489] - 当前线程ID: 2244
2025-07-30 16:56:18.123 [INFO] [app.core.db_connection] [db_connection.get_db_session:492] - 尝试使用db_connection获取会话
2025-07-30 16:56:18.125 [INFO] [app.core.db_connection] [db_connection.connect:175] - 数据库连接已创建
2025-07-30 16:56:18.126 [DEBUG] [app.core.db_connection] [db_connection.checkout:179] - 数据库连接已检出
2025-07-30 16:56:18.127 [INFO] [app.core.db_connection] [db_connection.get_session:215] - 数据库连接成功 (尝试 1/3)
2025-07-30 16:56:18.128 [INFO] [app.core.db_connection] [db_connection.get_db_session:496] - 使用db_connection获取会话成功
2025-07-30 16:56:24.281 [INFO] [app.core.security] [security.verify_password:151] - 使用sha256_crypt验证密码成功
2025-07-30 16:56:24.284 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 16:56:31.715 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.4%, CPU使用率 100.0%
2025-07-30 16:56:43.306 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 100.0%, 内存: 69.2%, 磁盘: 94.4%
2025-07-30 16:56:47.091 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.7%, CPU使用率 100.0%
2025-07-30 16:56:54.912 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: POST /api/medications ---
2025-07-30 16:56:54.913 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 16:56:54.914 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 16:56:54.918 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 404 ---

2025-07-30 16:57:02.740 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.3%, CPU使用率 100.0%
2025-07-30 16:57:17.858 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.2%, CPU使用率 100.0%
2025-07-30 16:57:32.986 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.6%, CPU使用率 91.7%
2025-07-30 16:57:44.406 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 98.1%, 内存: 68.5%, 磁盘: 94.5%
2025-07-30 16:57:48.094 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.1%, CPU使用率 58.3%
2025-07-30 16:58:03.301 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.4%, CPU使用率 97.2%
2025-07-30 16:58:18.412 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.7%, CPU使用率 92.9%
2025-07-30 16:58:33.564 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.5%, CPU使用率 84.0%
2025-07-30 16:58:45.705 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 99.4%, 内存: 66.4%, 磁盘: 94.4%
2025-07-30 16:58:48.734 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.9%, CPU使用率 100.0%
2025-07-30 16:59:03.982 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.5%, CPU使用率 100.0%
2025-07-30 16:59:19.091 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.6%, CPU使用率 79.2%
2025-07-30 16:59:34.196 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.2%, CPU使用率 34.5%
2025-07-30 16:59:46.735 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 52.3%, 内存: 60.3%, 磁盘: 94.4%
2025-07-30 16:59:49.304 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.4%, CPU使用率 7.1%
2025-07-30 17:00:04.411 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.1%, CPU使用率 50.0%
2025-07-30 17:00:11.907 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: GET /api/health ---
2025-07-30 17:00:11.910 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 17:00:11.913 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 17:00:11.915 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 17:00:13.950 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 17:00:13.952 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 17:00:13.955 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 17:00:13.957 [DEBUG] [app.core.db_connection] [db_connection.get_db_session:489] - 当前线程ID: 2244
2025-07-30 17:00:13.958 [INFO] [app.core.db_connection] [db_connection.get_db_session:492] - 尝试使用db_connection获取会话
2025-07-30 17:00:13.960 [INFO] [app.core.db_connection] [db_connection.connect:175] - 数据库连接已创建
2025-07-30 17:00:13.961 [DEBUG] [app.core.db_connection] [db_connection.checkout:179] - 数据库连接已检出
2025-07-30 17:00:13.963 [INFO] [app.core.db_connection] [db_connection.get_session:215] - 数据库连接成功 (尝试 1/3)
2025-07-30 17:00:13.964 [INFO] [app.core.db_connection] [db_connection.get_db_session:496] - 使用db_connection获取会话成功
2025-07-30 17:00:14.890 [INFO] [app.core.security] [security.verify_password:151] - 使用sha256_crypt验证密码成功
2025-07-30 17:00:14.897 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 17:00:19.517 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.0%, CPU使用率 45.8%
2025-07-30 17:00:34.625 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 62.8%, CPU使用率 37.5%
2025-07-30 17:00:43.678 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: POST /api/medications ---
2025-07-30 17:00:43.680 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 17:00:43.683 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 17:00:43.686 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 404 ---

2025-07-30 17:00:47.766 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 27.6%, 内存: 62.8%, 磁盘: 94.4%
2025-07-30 17:00:49.733 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 62.7%, CPU使用率 28.0%
2025-07-30 17:01:04.840 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 62.9%, CPU使用率 16.7%
2025-07-30 17:01:19.945 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 62.9%, CPU使用率 17.9%
2025-07-30 17:01:35.054 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 62.9%, CPU使用率 22.2%
2025-07-30 17:01:48.787 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 12.8%, 内存: 63.1%, 磁盘: 94.4%
2025-07-30 17:01:50.160 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.0%, CPU使用率 46.4%
2025-07-30 17:02:05.267 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.1%, CPU使用率 12.5%
2025-07-30 17:02:20.379 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.1%, CPU使用率 92.9%
2025-07-30 17:02:35.492 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.0%, CPU使用率 0.0%
2025-07-30 17:02:49.838 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 27.9%, 内存: 63.1%, 磁盘: 94.4%
2025-07-30 17:02:50.598 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.1%, CPU使用率 22.2%
2025-07-30 17:03:05.746 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.0%, CPU使用率 65.7%
2025-07-30 17:03:20.860 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.5%, CPU使用率 37.5%
2025-07-30 17:03:35.967 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.2%, CPU使用率 75.0%
2025-07-30 17:03:50.869 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 24.5%, 内存: 63.9%, 磁盘: 94.4%
2025-07-30 17:03:51.075 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.9%, CPU使用率 33.3%
2025-07-30 17:04:06.184 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 62.3%, CPU使用率 100.0%
2025-07-30 17:04:21.436 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.9%, CPU使用率 100.0%
2025-07-30 17:04:36.546 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.9%, CPU使用率 62.5%
2025-07-30 17:04:51.730 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.1%, CPU使用率 92.9%
2025-07-30 17:04:51.899 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 85.7%, 内存: 65.7%, 磁盘: 94.4%
2025-07-30 17:05:06.909 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.6%, CPU使用率 100.0%
2025-07-30 17:05:22.082 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.1%, CPU使用率 100.0%
2025-07-30 17:05:37.195 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.2%, CPU使用率 92.9%
2025-07-30 17:05:52.311 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.7%, CPU使用率 91.7%
2025-07-30 17:05:53.436 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 95.0%, 内存: 64.6%, 磁盘: 94.5%
2025-07-30 17:06:07.424 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.4%, CPU使用率 93.1%
2025-07-30 17:06:22.671 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.6%, CPU使用率 95.6%
2025-07-30 17:06:37.783 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.2%, CPU使用率 87.5%
2025-07-30 17:06:52.896 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.5%, CPU使用率 92.9%
2025-07-30 17:06:54.598 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 72.3%, 内存: 65.7%, 磁盘: 94.4%
2025-07-30 17:07:08.082 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.0%, CPU使用率 97.7%
2025-07-30 17:07:23.346 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.6%, CPU使用率 95.7%
2025-07-30 17:07:38.572 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.2%, CPU使用率 97.6%
2025-07-30 17:07:53.908 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.9%, CPU使用率 93.0%
2025-07-30 17:07:55.839 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 95.8%, 内存: 64.7%, 磁盘: 94.4%
2025-07-30 17:08:09.018 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.1%, CPU使用率 71.4%
2025-07-30 17:08:24.132 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.2%, CPU使用率 78.6%
2025-07-30 17:08:39.530 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.4%, CPU使用率 100.0%
2025-07-30 17:08:54.972 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.2%, CPU使用率 100.0%
2025-07-30 17:08:57.382 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 100.0%, 内存: 66.5%, 磁盘: 94.5%
2025-07-30 17:09:10.199 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.6%, CPU使用率 96.8%
2025-07-30 17:09:25.762 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.0%, CPU使用率 100.0%
2025-07-30 17:09:40.988 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.9%, CPU使用率 57.1%
2025-07-30 17:09:56.522 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.6%, CPU使用率 100.0%
2025-07-30 17:09:58.638 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 78.2%, 内存: 69.6%, 磁盘: 94.4%
2025-07-30 17:10:12.087 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.0%, CPU使用率 100.0%
2025-07-30 17:10:27.333 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.1%, CPU使用率 94.3%
2025-07-30 17:10:42.547 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.8%, CPU使用率 95.0%
2025-07-30 17:10:58.147 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.4%, CPU使用率 100.0%
2025-07-30 17:10:59.944 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 100.0%, 内存: 68.2%, 磁盘: 94.4%
2025-07-30 17:11:13.413 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.6%, CPU使用率 96.6%
2025-07-30 17:11:28.756 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.0%, CPU使用率 97.7%
2025-07-30 17:11:43.863 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.3%, CPU使用率 57.1%
2025-07-30 17:11:59.504 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.0%, CPU使用率 100.0%
2025-07-30 17:12:01.871 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 100.0%, 内存: 67.1%, 磁盘: 94.4%
2025-07-30 17:12:14.819 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.3%, CPU使用率 100.0%
2025-07-30 17:12:29.971 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.2%, CPU使用率 100.0%
2025-07-30 17:12:45.508 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.1%, CPU使用率 100.0%
2025-07-30 17:13:00.932 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.3%, CPU使用率 100.0%
2025-07-30 17:13:03.381 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 97.0%, 内存: 68.7%, 磁盘: 94.4%
2025-07-30 17:13:16.593 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.6%, CPU使用率 100.0%
2025-07-30 17:13:32.581 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.5%, CPU使用率 100.0%
2025-07-30 17:13:47.914 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.4%, CPU使用率 82.1%
2025-07-30 17:14:03.046 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.8%, CPU使用率 92.6%
2025-07-30 17:14:04.539 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 89.6%, 内存: 72.2%, 磁盘: 94.4%
2025-07-30 17:14:18.160 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.1%, CPU使用率 92.9%
2025-07-30 17:14:33.499 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.9%, CPU使用率 98.1%
2025-07-30 17:14:48.609 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.6%, CPU使用率 70.8%
2025-07-30 17:15:04.166 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.9%, CPU使用率 100.0%
2025-07-30 17:15:05.838 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 91.3%, 内存: 72.1%, 磁盘: 94.4%
2025-07-30 17:15:19.399 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.6%, CPU使用率 78.6%
2025-07-30 17:15:34.760 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 70.0%, CPU使用率 100.0%
2025-07-30 17:15:50.448 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.4%, CPU使用率 100.0%
2025-07-30 17:16:06.123 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.2%, CPU使用率 100.0%
2025-07-30 17:16:06.904 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 87.0%, 内存: 71.8%, 磁盘: 94.4%
2025-07-30 17:16:21.709 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.8%, CPU使用率 100.0%
2025-07-30 17:16:36.941 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.9%, CPU使用率 92.9%
2025-07-30 17:16:52.055 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.0%, CPU使用率 50.0%
2025-07-30 17:17:07.271 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.6%, CPU使用率 100.0%
2025-07-30 17:17:08.224 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 85.2%, 内存: 72.4%, 磁盘: 94.4%
2025-07-30 17:17:22.410 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.2%, CPU使用率 100.0%
2025-07-30 17:17:37.696 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 72.7%, CPU使用率 100.0%
2025-07-30 17:17:53.356 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.8%, CPU使用率 100.0%
2025-07-30 17:18:08.570 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 76.5%, CPU使用率 100.0%
2025-07-30 17:18:09.982 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 100.0%, 内存: 72.8%, 磁盘: 94.4%
2025-07-30 17:18:23.679 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 74.2%, CPU使用率 96.6%
2025-07-30 17:18:39.125 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.2%, CPU使用率 100.0%
2025-07-30 17:18:54.380 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 75.3%, CPU使用率 95.1%
2025-07-30 17:19:09.978 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 74.2%, CPU使用率 100.0%
2025-07-30 17:19:11.503 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 100.0%, 内存: 74.6%, 磁盘: 94.4%
2025-07-30 17:19:25.177 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 74.2%, CPU使用率 87.5%
2025-07-30 17:19:40.473 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.6%, CPU使用率 100.0%
2025-07-30 17:19:55.589 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 73.6%, CPU使用率 100.0%
2025-07-30 17:20:10.968 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 77.3%, CPU使用率 100.0%
2025-07-30 17:20:12.778 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 91.9%, 内存: 74.0%, 磁盘: 94.4%
2025-07-30 17:20:26.535 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 77.4%, CPU使用率 98.6%
2025-07-30 17:20:42.206 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 77.3%, CPU使用率 97.1%
2025-07-30 17:20:57.714 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 76.3%, CPU使用率 100.0%
2025-07-30 17:21:12.829 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 74.7%, CPU使用率 79.2%
2025-07-30 17:21:14.001 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 83.7%, 内存: 76.4%, 磁盘: 94.4%
2025-07-30 17:21:28.135 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 75.4%, CPU使用率 98.2%
2025-07-30 17:21:43.246 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 74.2%, CPU使用率 62.5%
2025-07-30 17:21:58.357 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 74.4%, CPU使用率 100.0%
2025-07-30 17:22:13.467 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 77.9%, CPU使用率 83.3%
2025-07-30 17:22:15.252 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 96.2%, 内存: 76.0%, 磁盘: 94.5%
2025-07-30 17:22:28.584 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 78.3%, CPU使用率 53.6%
2025-07-30 17:22:43.692 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 77.2%, CPU使用率 64.3%
2025-07-30 17:22:58.871 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 76.0%, CPU使用率 100.0%
2025-07-30 17:23:14.120 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 78.8%, CPU使用率 97.1%
2025-07-30 17:23:16.616 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 97.2%, 内存: 76.5%, 磁盘: 94.4%
2025-07-30 17:23:29.408 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 79.3%, CPU使用率 97.9%
2025-07-30 17:23:44.822 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 77.9%, CPU使用率 83.3%
2025-07-30 17:24:00.478 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 78.5%, CPU使用率 100.0%
2025-07-30 17:24:15.598 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 78.0%, CPU使用率 85.7%
2025-07-30 17:24:18.749 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 100.0%, 内存: 80.1%, 磁盘: 94.5%
2025-07-30 17:24:31.016 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 79.5%, CPU使用率 98.2%
2025-07-30 17:24:46.500 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 78.2%, CPU使用率 100.0%
2025-07-30 17:25:01.864 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 76.9%, CPU使用率 100.0%
2025-07-30 17:25:17.013 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 80.6%, CPU使用率 96.4%
2025-07-30 17:25:20.431 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 92.7%, 内存: 78.3%, 磁盘: 94.4%
2025-07-30 17:25:32.130 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 80.5%, CPU使用率 89.7%
2025-07-30 17:25:47.341 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 79.4%, CPU使用率 93.3%
2025-07-30 17:26:03.035 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 79.6%, CPU使用率 100.0%
2025-07-30 17:26:18.156 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 77.8%, CPU使用率 79.2%
2025-07-30 17:26:21.470 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 83.1%, 内存: 80.0%, 磁盘: 94.4%
2025-07-30 17:26:33.359 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 77.8%, CPU使用率 100.0%
2025-07-30 17:26:48.558 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 78.8%, CPU使用率 97.6%
2025-07-30 17:27:03.672 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 78.5%, CPU使用率 70.8%
2025-07-30 17:27:18.850 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 80.8%, CPU使用率 48.1%
2025-07-30 17:27:22.526 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 71.5%, 内存: 78.8%, 磁盘: 94.4%
2025-07-30 17:27:34.126 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 81.2%, CPU使用率 100.0%
2025-07-30 17:27:49.263 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 79.4%, CPU使用率 95.8%
2025-07-30 17:28:04.652 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 77.3%, CPU使用率 100.0%
2025-07-30 17:28:19.834 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 77.6%, CPU使用率 94.6%
2025-07-30 17:28:23.555 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 66.8%, 内存: 81.1%, 磁盘: 94.4%
2025-07-30 17:28:34.945 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 80.4%, CPU使用率 40.7%
2025-07-30 17:28:50.099 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 78.6%, CPU使用率 60.0%
2025-07-30 17:29:05.253 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 80.3%, CPU使用率 84.4%
2025-07-30 17:29:20.365 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 79.2%, CPU使用率 78.6%
2025-07-30 17:29:24.672 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 83.3%, 内存: 80.2%, 磁盘: 94.4%
2025-07-30 17:29:35.716 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 80.3%, CPU使用率 100.0%
2025-07-30 17:29:51.133 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 77.9%, CPU使用率 100.0%
2025-07-30 17:30:06.248 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 80.9%, CPU使用率 91.7%
2025-07-30 17:30:21.356 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 79.2%, CPU使用率 66.7%
2025-07-30 17:30:25.755 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 96.9%, 内存: 79.0%, 磁盘: 94.4%
2025-07-30 17:30:36.465 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 81.5%, CPU使用率 91.7%
2025-07-30 17:30:51.821 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 77.8%, CPU使用率 97.9%
2025-07-30 17:31:07.158 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 78.1%, CPU使用率 100.0%
2025-07-30 17:31:22.459 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 78.0%, CPU使用率 100.0%
2025-07-30 17:31:26.791 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 71.7%, 内存: 81.7%, 磁盘: 94.5%
2025-07-30 17:31:37.921 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 78.2%, CPU使用率 100.0%
2025-07-30 17:31:53.431 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 80.6%, CPU使用率 100.0%
2025-07-30 17:32:08.617 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 79.5%, CPU使用率 60.7%
2025-07-30 17:32:23.848 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 80.0%, CPU使用率 95.6%
2025-07-30 17:32:27.826 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 86.0%, 内存: 82.2%, 磁盘: 94.4%
2025-07-30 17:32:39.179 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 80.3%, CPU使用率 87.5%
2025-07-30 17:32:54.293 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 81.5%, CPU使用率 100.0%
2025-07-30 17:33:09.652 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 79.9%, CPU使用率 100.0%
2025-07-30 17:33:24.920 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 80.5%, CPU使用率 97.0%
2025-07-30 17:33:29.012 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 57.5%, 内存: 78.1%, 磁盘: 94.4%
2025-07-30 17:33:40.031 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 80.0%, CPU使用率 83.3%
2025-07-30 17:33:55.235 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 77.4%, CPU使用率 100.0%
2025-07-30 17:34:10.558 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 78.8%, CPU使用率 100.0%
2025-07-30 17:34:25.700 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 79.7%, CPU使用率 83.3%
2025-07-30 17:34:30.110 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 95.7%, 内存: 79.2%, 磁盘: 94.4%
2025-07-30 17:34:40.814 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 78.4%, CPU使用率 100.0%
2025-07-30 17:34:55.936 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 79.5%, CPU使用率 75.0%
2025-07-30 17:35:11.068 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 79.1%, CPU使用率 78.6%
2025-07-30 17:35:26.218 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 79.3%, CPU使用率 93.8%
2025-07-30 17:35:31.142 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 85.2%, 内存: 77.0%, 磁盘: 94.4%
2025-07-30 17:35:41.330 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 77.5%, CPU使用率 41.7%
2025-07-30 17:35:56.462 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 77.6%, CPU使用率 75.9%
2025-07-30 17:36:11.570 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 76.5%, CPU使用率 67.9%
2025-07-30 17:36:26.790 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 77.5%, CPU使用率 97.4%
2025-07-30 17:36:32.337 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 92.1%, 内存: 78.4%, 磁盘: 94.4%
2025-07-30 17:36:41.974 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 78.5%, CPU使用率 90.0%
2025-07-30 17:36:57.174 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 77.1%, CPU使用率 100.0%
2025-07-30 17:37:12.366 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 77.6%, CPU使用率 96.8%
2025-07-30 17:37:27.534 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 76.4%, CPU使用率 90.0%
2025-07-30 17:37:33.703 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 96.0%, 内存: 76.3%, 磁盘: 94.4%
2025-07-30 17:37:42.685 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 76.9%, CPU使用率 78.1%
2025-07-30 17:37:57.794 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 79.6%, CPU使用率 25.0%
2025-07-30 17:38:12.900 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 77.1%, CPU使用率 53.8%
2025-07-30 17:38:28.072 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 78.7%, CPU使用率 90.0%
2025-07-30 17:38:34.747 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 74.0%, 内存: 76.4%, 磁盘: 94.4%
2025-07-30 17:38:43.344 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 79.3%, CPU使用率 82.4%
2025-07-30 17:38:58.543 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 76.6%, CPU使用率 100.0%
2025-07-30 17:39:13.855 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 77.0%, CPU使用率 87.5%
2025-07-30 17:39:29.066 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 80.7%, CPU使用率 79.3%
2025-07-30 17:39:35.821 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 84.3%, 内存: 77.3%, 磁盘: 94.4%
2025-07-30 17:39:44.357 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 79.2%, CPU使用率 96.4%
2025-07-30 17:39:59.558 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 77.7%, CPU使用率 100.0%
2025-07-30 18:20:32.895 [INFO] [root] [health_monitor.<module>:28] - 成功导入psutil模块，路径: C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\psutil\__init__.py
2025-07-30 18:20:32.896 [DEBUG] [fallback_manager] [fallback_manager.register_dependency:39] - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-07-30 18:20:32.902 [INFO] [fallback_manager] [fallback_manager.register_dependency:51] - 依赖 psutil (psutil) 可用
2025-07-30 18:20:33.035 [INFO] [health_monitor] [health_monitor.__init__:115] - 健康监控器初始化完成
2025-07-30 18:20:33.057 [INFO] [app.core.system_monitor] [system_monitor._load_history:254] - 已加载 288 个历史数据点
2025-07-30 18:20:33.066 [INFO] [app.core.alert_detector] [alert_detector._load_rules:464] - 已加载 6 个告警规则
2025-07-30 18:20:33.076 [INFO] [app.core.alert_detector] [alert_detector._load_alerts:484] - 已加载 0 个当前告警
2025-07-30 18:20:33.146 [INFO] [app.core.alert_detector] [alert_detector._load_alerts:491] - 已加载 370 个历史告警
2025-07-30 18:20:33.218 [INFO] [app.core.alert_detector] [alert_detector._load_notification_channels:502] - 已加载 1 个通知渠道
2025-07-30 18:20:33.325 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-30 18:20:33.411 [INFO] [alert_manager] [alert_manager._init_alert_rules:315] - 已初始化默认告警规则
2025-07-30 18:20:33.413 [INFO] [alert_manager] [alert_manager._init_notification_channels:333] - 已初始化默认通知渠道
2025-07-30 18:20:33.414 [INFO] [alert_manager] [alert_manager.__init__:258] - 告警管理器初始化完成
2025-07-30 18:20:35.709 [INFO] [db_service] [db_service._create_engine:92] - 数据库引擎和会话工厂创建成功
2025-07-30 18:20:35.723 [INFO] [db_service] [db_service.__init__:56] - 数据库服务初始化完成
2025-07-30 18:20:35.754 [INFO] [notification_service] [notification_service.__init__:55] - 通知服务初始化完成
2025-07-30 18:20:35.760 [INFO] [main] [main.<module>:60] - 错误处理模块导入成功
2025-07-30 18:20:35.967 [INFO] [main] [main.<module>:83] - 监控模块导入成功
2025-07-30 18:20:35.975 [INFO] [main] [main.<module>:93] - 不再使用迁移脚本，使用标准化的数据库模型
2025-07-30 18:20:42.590 [WARNING] [app.services.ocr_service] [ocr_service.<module>:37] - OpenCV未安装，图像预处理功能将不可用
2025-07-30 18:20:42.958 [INFO] [main] [main.startup_event:404] - 应用启动中...
2025-07-30 18:20:43.007 [INFO] [error_handling] [error_handling.setup_error_handling:234] - 错误处理已设置
2025-07-30 18:20:43.043 [INFO] [main] [main.startup_event:410] - 错误处理系统初始化完成
2025-07-30 18:20:43.050 [INFO] [monitoring] [monitoring.init_monitoring:441] - 添加指标端点成功: /metrics
2025-07-30 18:20:43.105 [INFO] [monitoring] [monitoring.init_monitoring:448] - 添加健康检查端点成功: /health
2025-07-30 18:20:43.116 [INFO] [monitoring] [monitoring.init_monitoring:454] - 添加详细健康检查端点成功: /api/health/detailed
2025-07-30 18:20:43.256 [INFO] [monitoring] [monitoring.init_system_info:103] - 系统信息初始化完成: Markey, Windows-10-10.0.19045-SP0, Python 3.13.2, CPU核心数: 4
2025-07-30 18:20:43.527 [INFO] [monitoring] [monitoring.init_monitoring:464] - 启动资源监控线程成功
2025-07-30 18:20:43.534 [INFO] [monitoring] [monitoring.init_monitoring:466] - 监控系统初始化成功（不使用中间件）
2025-07-30 18:20:43.536 [INFO] [monitoring] [monitoring.init_monitoring:470] - 监控系统初始化完成
2025-07-30 18:20:43.541 [INFO] [main] [main.startup_event:419] - 监控系统初始化完成
2025-07-30 18:20:43.578 [INFO] [app.db.init_db] [init_db.<module>:45] - 所有模型导入成功
2025-07-30 18:20:43.623 [INFO] [app.db.init_db] [init_db.<module>:53] - 使用sha256_crypt进行密码哈希和验证
2025-07-30 18:20:43.645 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 63.2%, CPU使用率 100.0%
2025-07-30 18:20:43.649 [INFO] [db_service] [db_service.get_session:114] - 数据库连接成功 (尝试 1/3)
2025-07-30 18:20:43.692 [INFO] [app.db.init_db] [init_db.init_db:67] - 所有模型导入成功
2025-07-30 18:20:43.731 [INFO] [app.db.init_db] [init_db.init_db:71] - 使用sha256_crypt进行密码哈希和验证
2025-07-30 18:20:43.740 [INFO] [app.db.init_db] [init_db.init_db:74] - 正在运行数据库迁移...
2025-07-30 18:20:43.749 [INFO] [app.db.init_db] [init_db.init_db:93] - 正在检查并更新数据库表结构...
2025-07-30 18:20:43.762 [INFO] [sqlalchemy.engine.Engine] [base._connection_begin_impl:2699] - BEGIN (implicit)
2025-07-30 18:20:43.779 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("alerts")
2025-07-30 18:20:43.787 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:43.878 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("alert_rules")
2025-07-30 18:20:43.906 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:43.911 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("alert_channels")
2025-07-30 18:20:44.008 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:44.041 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("users")
2025-07-30 18:20:44.070 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:44.105 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("health_records")
2025-07-30 18:20:44.126 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:44.150 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("health_overviews")
2025-07-30 18:20:44.211 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:44.263 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("medical_records")
2025-07-30 18:20:44.291 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:44.325 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("inpatient_records")
2025-07-30 18:20:44.363 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:44.388 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("surgery_records")
2025-07-30 18:20:44.397 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:44.409 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("lab_reports")
2025-07-30 18:20:44.414 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:44.441 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("lab_report_items")
2025-07-30 18:20:44.456 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:44.473 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("imaging_reports")
2025-07-30 18:20:44.483 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:44.499 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("examination_reports")
2025-07-30 18:20:44.519 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:44.530 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("documents")
2025-07-30 18:20:44.573 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:44.577 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("other_records")
2025-07-30 18:20:44.583 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:44.592 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("operation_logs")
2025-07-30 18:20:44.596 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:44.599 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("follow_up_records")
2025-07-30 18:20:44.606 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:44.610 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("health_diaries")
2025-07-30 18:20:44.632 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:44.675 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("registration_records")
2025-07-30 18:20:44.694 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:44.743 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("prescription_records")
2025-07-30 18:20:44.786 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:44.791 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("laboratory_records")
2025-07-30 18:20:44.808 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:44.814 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("imaging_records")
2025-07-30 18:20:44.819 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:44.849 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("medications")
2025-07-30 18:20:44.857 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:44.862 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("medication_usages")
2025-07-30 18:20:44.896 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:44.903 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("role_applications")
2025-07-30 18:20:44.908 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:44.925 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("assessments")
2025-07-30 18:20:44.955 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:44.966 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("assessment_items")
2025-07-30 18:20:44.976 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:44.981 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("assessment_responses")
2025-07-30 18:20:44.998 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:45.007 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("assessment_templates")
2025-07-30 18:20:45.011 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:45.015 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("assessment_template_questions")
2025-07-30 18:20:45.023 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:45.029 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("questionnaires")
2025-07-30 18:20:45.033 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:45.042 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("questionnaire_items")
2025-07-30 18:20:45.046 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:45.053 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("questionnaire_responses")
2025-07-30 18:20:45.061 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:45.079 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("questionnaire_answers")
2025-07-30 18:20:45.083 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:45.091 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("questionnaire_templates")
2025-07-30 18:20:45.094 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:45.098 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("questionnaire_template_questions")
2025-07-30 18:20:45.108 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:45.112 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("assessment_results")
2025-07-30 18:20:45.121 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:45.126 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("questionnaire_results")
2025-07-30 18:20:45.131 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:45.142 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("report_templates")
2025-07-30 18:20:45.146 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:45.149 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("assessment_distributions")
2025-07-30 18:20:45.157 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:45.164 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("questionnaire_distributions")
2025-07-30 18:20:45.168 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:45.177 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("service_stats")
2025-07-30 18:20:45.180 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:20:45.190 [INFO] [sqlalchemy.engine.Engine] [base._connection_commit_impl:2705] - COMMIT
2025-07-30 18:20:45.194 [INFO] [app.db.init_db] [init_db.init_db:97] - 数据库表结构检查和更新完成
2025-07-30 18:20:45.200 [INFO] [app.db.init_db] [init_db._setup_model_relationships:120] - 模型关系初始化完成
2025-07-30 18:20:45.207 [INFO] [app.db.init_db] [init_db.init_db:101] - 模型关系设置完成
2025-07-30 18:20:45.211 [INFO] [main] [main.startup_event:428] - 数据库初始化完成（强制重建）
2025-07-30 18:20:45.216 [INFO] [db_service] [db_service.get_session:114] - 数据库连接成功 (尝试 1/3)
2025-07-30 18:20:45.222 [INFO] [main] [main.startup_event:435] - 数据库连接正常
2025-07-30 18:20:45.226 [INFO] [main] [main.startup_event:443] - 开始初始化模板数据
2025-07-30 18:20:45.229 [INFO] [db_service] [db_service.get_session:114] - 数据库连接成功 (尝试 1/3)
2025-07-30 18:20:45.671 [INFO] [app.db.init_templates] [init_templates.init_assessment_templates:33] - 开始初始化评估量表模板...
2025-07-30 18:20:45.736 [INFO] [app.db.init_templates] [init_templates.init_assessment_templates:72] - 成功初始化 5 个评估量表模板
2025-07-30 18:20:45.787 [INFO] [app.db.init_templates] [init_templates.init_questionnaire_templates:85] - 开始初始化调查问卷模板...
2025-07-30 18:20:45.856 [INFO] [app.db.init_templates] [init_templates.init_questionnaire_templates:116] - 成功初始化 5 个调查问卷模板
2025-07-30 18:20:45.862 [INFO] [main] [main.startup_event:447] - 模板数据初始化完成
2025-07-30 18:20:45.865 [INFO] [main] [main.startup_event:452] - 数据库表结构已经标准化，不需要迁移
2025-07-30 18:20:45.875 [INFO] [main] [main.startup_event:455] - 应用启动完成
2025-07-30 18:20:58.792 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.1%, CPU使用率 38.5%
2025-07-30 18:20:59.410 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: GET /api/health ---
2025-07-30 18:20:59.411 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 18:20:59.412 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 18:20:59.415 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 18:21:01.469 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 18:21:01.471 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 18:21:01.471 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 18:21:01.474 [DEBUG] [app.core.db_connection] [db_connection.get_db_session:489] - 当前线程ID: 15892
2025-07-30 18:21:01.475 [INFO] [app.core.db_connection] [db_connection.get_db_session:492] - 尝试使用db_connection获取会话
2025-07-30 18:21:01.478 [INFO] [app.core.db_connection] [db_connection.connect:175] - 数据库连接已创建
2025-07-30 18:21:01.478 [DEBUG] [app.core.db_connection] [db_connection.checkout:179] - 数据库连接已检出
2025-07-30 18:21:01.479 [INFO] [app.core.db_connection] [db_connection.get_session:215] - 数据库连接成功 (尝试 1/3)
2025-07-30 18:21:01.480 [INFO] [app.core.db_connection] [db_connection.get_db_session:496] - 使用db_connection获取会话成功
2025-07-30 18:21:02.913 [INFO] [app.core.security] [security.verify_password:151] - 使用sha256_crypt验证密码成功
2025-07-30 18:21:02.916 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 18:21:13.897 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.1%, CPU使用率 24.1%
2025-07-30 18:21:29.003 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.6%, CPU使用率 29.6%
2025-07-30 18:21:33.930 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 27.9%, 内存: 65.3%, 磁盘: 94.5%
2025-07-30 18:21:44.109 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.4%, CPU使用率 4.2%
2025-07-30 18:21:59.217 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.4%, CPU使用率 54.2%
2025-07-30 18:22:14.321 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 65.3%, CPU使用率 42.9%
2025-07-30 18:22:29.436 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 64.7%, CPU使用率 100.0%
2025-07-30 18:22:34.951 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 23.0%, 内存: 64.5%, 磁盘: 94.5%
2025-07-30 18:22:44.554 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 62.9%, CPU使用率 17.9%
2025-07-30 18:22:59.659 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.8%, CPU使用率 29.6%
2025-07-30 18:23:14.763 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.4%, CPU使用率 4.2%
2025-07-30 18:23:29.868 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.2%, CPU使用率 78.6%
2025-07-30 18:23:35.976 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 51.6%, 内存: 60.2%, 磁盘: 94.5%
2025-07-30 18:23:44.972 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.1%, CPU使用率 37.0%
2025-07-30 18:24:00.078 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.5%, CPU使用率 8.3%
2025-07-30 18:24:15.182 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.4%, CPU使用率 50.0%
2025-07-30 18:24:30.287 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.2%, CPU使用率 50.0%
2025-07-30 18:24:36.999 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 46.9%, 内存: 60.3%, 磁盘: 94.5%
2025-07-30 18:24:45.402 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 60.1%, CPU使用率 46.2%
2025-07-30 18:56:44.639 [INFO] [root] [health_monitor.<module>:28] - 成功导入psutil模块，路径: C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\psutil\__init__.py
2025-07-30 18:56:44.643 [DEBUG] [fallback_manager] [fallback_manager.register_dependency:39] - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-07-30 18:56:44.645 [INFO] [fallback_manager] [fallback_manager.register_dependency:51] - 依赖 psutil (psutil) 可用
2025-07-30 18:56:45.159 [INFO] [health_monitor] [health_monitor.__init__:115] - 健康监控器初始化完成
2025-07-30 18:56:45.179 [INFO] [app.core.system_monitor] [system_monitor._load_history:254] - 已加载 288 个历史数据点
2025-07-30 18:56:45.194 [INFO] [app.core.alert_detector] [alert_detector._load_rules:464] - 已加载 6 个告警规则
2025-07-30 18:56:45.203 [INFO] [app.core.alert_detector] [alert_detector._load_alerts:484] - 已加载 0 个当前告警
2025-07-30 18:56:45.214 [INFO] [app.core.alert_detector] [alert_detector._load_alerts:491] - 已加载 370 个历史告警
2025-07-30 18:56:45.362 [INFO] [app.core.alert_detector] [alert_detector._load_notification_channels:502] - 已加载 1 个通知渠道
2025-07-30 18:56:45.490 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-30 18:56:45.504 [INFO] [alert_manager] [alert_manager._init_alert_rules:315] - 已初始化默认告警规则
2025-07-30 18:56:45.506 [INFO] [alert_manager] [alert_manager._init_notification_channels:333] - 已初始化默认通知渠道
2025-07-30 18:56:45.512 [INFO] [alert_manager] [alert_manager.__init__:258] - 告警管理器初始化完成
2025-07-30 18:56:47.957 [INFO] [db_service] [db_service._create_engine:92] - 数据库引擎和会话工厂创建成功
2025-07-30 18:56:47.962 [INFO] [db_service] [db_service.__init__:56] - 数据库服务初始化完成
2025-07-30 18:56:47.979 [INFO] [notification_service] [notification_service.__init__:55] - 通知服务初始化完成
2025-07-30 18:56:47.991 [INFO] [main] [main.<module>:60] - 错误处理模块导入成功
2025-07-30 18:56:48.459 [INFO] [main] [main.<module>:83] - 监控模块导入成功
2025-07-30 18:56:48.472 [INFO] [main] [main.<module>:93] - 不再使用迁移脚本，使用标准化的数据库模型
2025-07-30 18:56:58.738 [WARNING] [app.services.ocr_service] [ocr_service.<module>:37] - OpenCV未安装，图像预处理功能将不可用
2025-07-30 18:56:58.921 [INFO] [main] [main.startup_event:404] - 应用启动中...
2025-07-30 18:56:58.924 [INFO] [error_handling] [error_handling.setup_error_handling:234] - 错误处理已设置
2025-07-30 18:56:58.935 [INFO] [main] [main.startup_event:410] - 错误处理系统初始化完成
2025-07-30 18:56:58.940 [INFO] [monitoring] [monitoring.init_monitoring:441] - 添加指标端点成功: /metrics
2025-07-30 18:56:58.945 [INFO] [monitoring] [monitoring.init_monitoring:448] - 添加健康检查端点成功: /health
2025-07-30 18:56:58.956 [INFO] [monitoring] [monitoring.init_monitoring:454] - 添加详细健康检查端点成功: /api/health/detailed
2025-07-30 18:56:58.960 [INFO] [monitoring] [monitoring.init_system_info:103] - 系统信息初始化完成: Markey, Windows-10-10.0.19045-SP0, Python 3.13.2, CPU核心数: 4
2025-07-30 18:56:58.988 [INFO] [monitoring] [monitoring.init_monitoring:464] - 启动资源监控线程成功
2025-07-30 18:56:58.989 [INFO] [monitoring] [monitoring.init_monitoring:466] - 监控系统初始化成功（不使用中间件）
2025-07-30 18:56:58.996 [INFO] [monitoring] [monitoring.init_monitoring:470] - 监控系统初始化完成
2025-07-30 18:56:59.000 [INFO] [main] [main.startup_event:419] - 监控系统初始化完成
2025-07-30 18:56:59.007 [INFO] [app.db.init_db] [init_db.<module>:45] - 所有模型导入成功
2025-07-30 18:56:59.012 [INFO] [app.db.init_db] [init_db.<module>:53] - 使用sha256_crypt进行密码哈希和验证
2025-07-30 18:56:59.024 [INFO] [db_service] [db_service.get_session:114] - 数据库连接成功 (尝试 1/3)
2025-07-30 18:56:59.033 [INFO] [app.db.init_db] [init_db.init_db:67] - 所有模型导入成功
2025-07-30 18:56:59.044 [INFO] [app.db.init_db] [init_db.init_db:71] - 使用sha256_crypt进行密码哈希和验证
2025-07-30 18:56:59.061 [INFO] [app.db.init_db] [init_db.init_db:74] - 正在运行数据库迁移...
2025-07-30 18:56:59.073 [INFO] [app.db.init_db] [init_db.init_db:93] - 正在检查并更新数据库表结构...
2025-07-30 18:56:59.078 [INFO] [sqlalchemy.engine.Engine] [base._connection_begin_impl:2699] - BEGIN (implicit)
2025-07-30 18:56:59.094 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 66.7%, CPU使用率 100.0%
2025-07-30 18:56:59.089 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("alerts")
2025-07-30 18:56:59.107 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:56:59.123 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("alert_rules")
2025-07-30 18:56:59.128 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:56:59.137 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("alert_channels")
2025-07-30 18:56:59.142 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:56:59.155 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("users")
2025-07-30 18:56:59.158 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:56:59.167 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("health_records")
2025-07-30 18:56:59.171 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:56:59.176 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("health_overviews")
2025-07-30 18:56:59.187 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:56:59.196 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("medical_records")
2025-07-30 18:56:59.203 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:56:59.213 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("inpatient_records")
2025-07-30 18:56:59.220 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:56:59.227 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("surgery_records")
2025-07-30 18:56:59.231 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:56:59.236 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("lab_reports")
2025-07-30 18:56:59.242 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:56:59.245 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("lab_report_items")
2025-07-30 18:56:59.252 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:56:59.258 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("imaging_reports")
2025-07-30 18:56:59.262 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:56:59.271 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("examination_reports")
2025-07-30 18:56:59.275 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:56:59.280 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("documents")
2025-07-30 18:56:59.291 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:56:59.304 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("other_records")
2025-07-30 18:56:59.308 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:56:59.317 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("operation_logs")
2025-07-30 18:56:59.321 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:56:59.325 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("follow_up_records")
2025-07-30 18:56:59.336 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:56:59.341 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("health_diaries")
2025-07-30 18:56:59.352 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:56:59.357 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("registration_records")
2025-07-30 18:56:59.369 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:56:59.374 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("prescription_records")
2025-07-30 18:56:59.383 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:56:59.387 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("laboratory_records")
2025-07-30 18:56:59.402 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:56:59.408 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("imaging_records")
2025-07-30 18:56:59.412 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:56:59.434 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("medications")
2025-07-30 18:56:59.441 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:56:59.452 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("medication_usages")
2025-07-30 18:56:59.461 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:56:59.496 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("role_applications")
2025-07-30 18:56:59.528 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:56:59.672 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("assessments")
2025-07-30 18:56:59.838 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:56:59.969 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("assessment_items")
2025-07-30 18:56:59.987 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:57:00.028 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("assessment_responses")
2025-07-30 18:57:00.157 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:57:00.226 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("assessment_templates")
2025-07-30 18:57:00.236 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:57:00.255 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("assessment_template_questions")
2025-07-30 18:57:00.276 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:57:00.327 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("questionnaires")
2025-07-30 18:57:00.370 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:57:00.436 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("questionnaire_items")
2025-07-30 18:57:00.479 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:57:00.489 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("questionnaire_responses")
2025-07-30 18:57:00.526 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:57:00.569 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("questionnaire_answers")
2025-07-30 18:57:00.585 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:57:00.661 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("questionnaire_templates")
2025-07-30 18:57:00.922 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:57:01.176 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("questionnaire_template_questions")
2025-07-30 18:57:01.524 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:57:01.681 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("assessment_results")
2025-07-30 18:57:01.851 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:57:02.018 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("questionnaire_results")
2025-07-30 18:57:02.091 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:57:02.118 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("report_templates")
2025-07-30 18:57:02.153 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:57:02.207 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("assessment_distributions")
2025-07-30 18:57:02.257 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:57:02.392 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("questionnaire_distributions")
2025-07-30 18:57:02.454 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:57:02.535 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - PRAGMA main.table_info("service_stats")
2025-07-30 18:57:02.578 [INFO] [sqlalchemy.engine.Engine] [base._execute_context:1843] - [raw sql] ()
2025-07-30 18:57:02.607 [INFO] [sqlalchemy.engine.Engine] [base._connection_commit_impl:2705] - COMMIT
2025-07-30 18:57:02.635 [INFO] [app.db.init_db] [init_db.init_db:97] - 数据库表结构检查和更新完成
2025-07-30 18:57:02.661 [INFO] [app.db.init_db] [init_db._setup_model_relationships:120] - 模型关系初始化完成
2025-07-30 18:57:02.691 [INFO] [app.db.init_db] [init_db.init_db:101] - 模型关系设置完成
2025-07-30 18:57:02.725 [INFO] [main] [main.startup_event:428] - 数据库初始化完成（强制重建）
2025-07-30 18:57:02.838 [INFO] [db_service] [db_service.get_session:114] - 数据库连接成功 (尝试 1/3)
2025-07-30 18:57:02.962 [INFO] [main] [main.startup_event:435] - 数据库连接正常
2025-07-30 18:57:03.105 [INFO] [main] [main.startup_event:443] - 开始初始化模板数据
2025-07-30 18:57:03.570 [INFO] [db_service] [db_service.get_session:114] - 数据库连接成功 (尝试 1/3)
2025-07-30 18:57:04.538 [INFO] [app.db.init_templates] [init_templates.init_assessment_templates:33] - 开始初始化评估量表模板...
2025-07-30 18:57:04.723 [INFO] [app.db.init_templates] [init_templates.init_assessment_templates:72] - 成功初始化 5 个评估量表模板
2025-07-30 18:57:04.890 [INFO] [app.db.init_templates] [init_templates.init_questionnaire_templates:85] - 开始初始化调查问卷模板...
2025-07-30 18:57:05.070 [INFO] [app.db.init_templates] [init_templates.init_questionnaire_templates:116] - 成功初始化 5 个调查问卷模板
2025-07-30 18:57:05.085 [INFO] [main] [main.startup_event:447] - 模板数据初始化完成
2025-07-30 18:57:05.094 [INFO] [main] [main.startup_event:452] - 数据库表结构已经标准化，不需要迁移
2025-07-30 18:57:05.129 [INFO] [main] [main.startup_event:455] - 应用启动完成
2025-07-30 18:57:09.783 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: GET /api/health ---
2025-07-30 18:57:09.792 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 18:57:09.795 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 18:57:09.801 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 18:57:12.101 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 18:57:12.118 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 18:57:12.196 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 18:57:12.621 [DEBUG] [app.core.db_connection] [db_connection.get_db_session:489] - 当前线程ID: 5096
2025-07-30 18:57:12.631 [INFO] [app.core.db_connection] [db_connection.get_db_session:492] - 尝试使用db_connection获取会话
2025-07-30 18:57:12.635 [INFO] [app.core.db_connection] [db_connection.connect:175] - 数据库连接已创建
2025-07-30 18:57:12.636 [DEBUG] [app.core.db_connection] [db_connection.checkout:179] - 数据库连接已检出
2025-07-30 18:57:12.637 [INFO] [app.core.db_connection] [db_connection.get_session:215] - 数据库连接成功 (尝试 1/3)
2025-07-30 18:57:12.638 [INFO] [app.core.db_connection] [db_connection.get_db_session:496] - 使用db_connection获取会话成功
2025-07-30 18:57:14.289 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.3%, CPU使用率 100.0%
2025-07-30 18:57:15.904 [INFO] [app.core.security] [security.verify_password:151] - 使用sha256_crypt验证密码成功
2025-07-30 18:57:15.908 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 18:57:29.456 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.1%, CPU使用率 92.9%
2025-07-30 18:57:44.566 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 67.8%, CPU使用率 53.6%
2025-07-30 18:57:45.809 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 99.2%, 内存: 68.4%, 磁盘: 94.5%
2025-07-30 18:57:59.677 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 69.0%, CPU使用率 62.1%
2025-07-30 18:58:14.966 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.6%, CPU使用率 100.0%
2025-07-30 18:58:30.192 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.6%, CPU使用率 46.4%
2025-07-30 18:58:45.298 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 68.5%, CPU使用率 64.0%
2025-07-30 18:58:46.891 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 98.1%, 内存: 68.0%, 磁盘: 94.5%
2025-07-30 18:59:00.568 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 71.9%, CPU使用率 96.7%
2025-07-30 18:59:16.001 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 61.6%, CPU使用率 100.0%
2025-07-30 18:59:31.634 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 39.8%, CPU使用率 100.0%
2025-07-30 18:59:47.253 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 41.9%, CPU使用率 100.0%
2025-07-30 18:59:48.186 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 100.0%, 内存: 42.2%, 磁盘: 94.5%
2025-07-30 19:00:02.706 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.2%, CPU使用率 100.0%
2025-07-30 19:00:13.903 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: GET /api/health ---
2025-07-30 19:00:13.904 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 19:00:13.905 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 19:00:13.906 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 19:00:15.953 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 19:00:15.954 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 19:00:15.954 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 19:00:15.957 [DEBUG] [app.core.db_connection] [db_connection.get_db_session:489] - 当前线程ID: 5096
2025-07-30 19:00:15.958 [INFO] [app.core.db_connection] [db_connection.get_db_session:492] - 尝试使用db_connection获取会话
2025-07-30 19:00:15.959 [INFO] [app.core.db_connection] [db_connection.connect:175] - 数据库连接已创建
2025-07-30 19:00:15.960 [DEBUG] [app.core.db_connection] [db_connection.checkout:179] - 数据库连接已检出
2025-07-30 19:00:15.961 [INFO] [app.core.db_connection] [db_connection.get_session:215] - 数据库连接成功 (尝试 1/3)
2025-07-30 19:00:15.961 [INFO] [app.core.db_connection] [db_connection.get_db_session:496] - 使用db_connection获取会话成功
2025-07-30 19:00:16.784 [INFO] [app.core.security] [security.verify_password:151] - 使用sha256_crypt验证密码成功
2025-07-30 19:00:16.787 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 19:00:17.847 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 43.7%, CPU使用率 78.1%
2025-07-30 19:00:32.950 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.2%, CPU使用率 8.3%
2025-07-30 19:00:48.056 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.0%, CPU使用率 16.0%
2025-07-30 19:00:49.392 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 20.9%, 内存: 44.0%, 磁盘: 94.6%
2025-07-30 19:01:03.160 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.1%, CPU使用率 3.8%
2025-07-30 19:01:18.266 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.2%, CPU使用率 20.8%
2025-07-30 19:01:33.370 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.1%, CPU使用率 4.2%
2025-07-30 19:01:48.475 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 43.9%, CPU使用率 32.1%
2025-07-30 19:01:50.415 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 66.5%, 内存: 44.1%, 磁盘: 94.7%
2025-07-30 19:02:03.586 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.5%, CPU使用率 64.3%
2025-07-30 19:02:18.692 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.3%, CPU使用率 28.6%
2025-07-30 19:02:33.797 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.2%, CPU使用率 29.2%
2025-07-30 19:02:45.564 [WARNING] [alert_manager] [alert_manager._create_alert:649] - 触发告警: disk_free_space, 当前值: 0.0, 阈值: 1024
2025-07-30 19:02:48.902 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 42.0%, CPU使用率 37.5%
2025-07-30 19:02:51.436 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 63.0%, 内存: 42.0%, 磁盘: 94.8%
2025-07-30 19:03:04.008 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 42.0%, CPU使用率 57.1%
2025-07-30 19:03:19.112 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 42.5%, CPU使用率 73.1%
2025-07-30 19:03:34.217 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 42.9%, CPU使用率 62.5%
2025-07-30 19:03:45.565 [WARNING] [alert_manager] [alert_manager._create_alert:649] - 触发告警: disk_usage, 当前值: 94.8, 阈值: 90
2025-07-30 19:03:49.424 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 42.9%, CPU使用率 96.9%
2025-07-30 19:03:52.821 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 98.0%, 内存: 43.6%, 磁盘: 94.8%
2025-07-30 19:04:04.531 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.0%, CPU使用率 79.2%
2025-07-30 19:04:19.636 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.2%, CPU使用率 52.2%
2025-07-30 19:04:34.741 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.2%, CPU使用率 55.2%
2025-07-30 19:04:49.846 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 43.8%, CPU使用率 38.5%
2025-07-30 19:04:53.895 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 52.7%, 内存: 43.8%, 磁盘: 94.8%
2025-07-30 19:05:04.949 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 43.7%, CPU使用率 52.2%
2025-07-30 19:05:20.054 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.3%, CPU使用率 57.1%
2025-07-30 19:05:35.159 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.3%, CPU使用率 35.7%
2025-07-30 19:05:50.266 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.4%, CPU使用率 36.0%
2025-07-30 19:05:54.916 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 42.6%, 内存: 44.5%, 磁盘: 94.8%
2025-07-30 19:06:05.371 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.4%, CPU使用率 8.3%
2025-07-30 19:06:20.478 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.0%, CPU使用率 60.0%
2025-07-30 19:06:35.583 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 46.3%, CPU使用率 52.0%
2025-07-30 19:06:50.689 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.0%, CPU使用率 50.0%
2025-07-30 19:06:55.962 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 74.7%, 内存: 45.7%, 磁盘: 94.8%
2025-07-30 19:07:00.223 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: GET /api/health ---
2025-07-30 19:07:00.225 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 19:07:00.225 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 19:07:00.227 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 19:07:02.276 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 19:07:02.277 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 19:07:02.278 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 19:07:02.279 [DEBUG] [app.core.db_connection] [db_connection.get_db_session:489] - 当前线程ID: 5096
2025-07-30 19:07:02.280 [INFO] [app.core.db_connection] [db_connection.get_db_session:492] - 尝试使用db_connection获取会话
2025-07-30 19:07:02.281 [INFO] [app.core.db_connection] [db_connection.connect:175] - 数据库连接已创建
2025-07-30 19:07:02.282 [DEBUG] [app.core.db_connection] [db_connection.checkout:179] - 数据库连接已检出
2025-07-30 19:07:02.283 [INFO] [app.core.db_connection] [db_connection.get_session:215] - 数据库连接成功 (尝试 1/3)
2025-07-30 19:07:02.284 [INFO] [app.core.db_connection] [db_connection.get_db_session:496] - 使用db_connection获取会话成功
2025-07-30 19:07:03.041 [INFO] [app.core.security] [security.verify_password:151] - 使用sha256_crypt验证密码成功
2025-07-30 19:07:03.044 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 19:07:05.795 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 46.4%, CPU使用率 45.8%
2025-07-30 19:07:20.899 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 47.0%, CPU使用率 0.0%
2025-07-30 19:07:36.003 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 46.8%, CPU使用率 3.6%
2025-07-30 19:07:51.112 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 47.6%, CPU使用率 100.0%
2025-07-30 19:07:57.154 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 100.0%, 内存: 47.5%, 磁盘: 94.8%
2025-07-30 19:08:06.220 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 47.6%, CPU使用率 16.7%
2025-07-30 19:08:21.327 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 47.2%, CPU使用率 25.0%
2025-07-30 19:08:36.432 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 47.3%, CPU使用率 37.5%
2025-07-30 19:08:51.537 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 46.8%, CPU使用率 14.3%
2025-07-30 19:08:58.192 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 9.7%, 内存: 46.7%, 磁盘: 94.8%
2025-07-30 19:09:06.642 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 46.7%, CPU使用率 0.0%
2025-07-30 19:09:21.769 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 47.3%, CPU使用率 100.0%
2025-07-30 19:09:36.876 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 48.6%, CPU使用率 54.2%
2025-07-30 19:09:51.994 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 47.9%, CPU使用率 50.0%
2025-07-30 19:09:59.213 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 29.6%, 内存: 47.2%, 磁盘: 94.8%
2025-07-30 19:10:07.098 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 47.3%, CPU使用率 35.7%
2025-07-30 19:10:22.203 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 47.4%, CPU使用率 8.3%
2025-07-30 19:10:37.310 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 47.4%, CPU使用率 20.8%
2025-07-30 19:10:52.594 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 33.0%, CPU使用率 100.0%
2025-07-30 19:11:00.233 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 40.2%, 内存: 32.6%, 磁盘: 94.6%
2025-07-30 19:11:07.753 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 32.7%, CPU使用率 14.3%
2025-07-30 19:11:23.221 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 39.6%, CPU使用率 100.0%
2025-07-30 19:11:38.814 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 43.8%, CPU使用率 100.0%
2025-07-30 19:11:54.611 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 50.1%, CPU使用率 100.0%
2025-07-30 19:12:01.909 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 100.0%, 内存: 49.1%, 磁盘: 94.5%
2025-07-30 19:12:10.000 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 46.3%, CPU使用率 78.6%
2025-07-30 19:12:25.105 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.3%, CPU使用率 58.3%
2025-07-30 19:12:40.214 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.4%, CPU使用率 69.0%
2025-07-30 19:12:55.318 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.4%, CPU使用率 64.3%
2025-07-30 19:13:02.952 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 68.2%, 内存: 45.2%, 磁盘: 94.5%
2025-07-30 19:13:10.425 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.4%, CPU使用率 82.1%
2025-07-30 19:13:25.529 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.4%, CPU使用率 88.0%
2025-07-30 19:13:40.665 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.2%, CPU使用率 81.2%
2025-07-30 19:13:55.771 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.3%, CPU使用率 65.5%
2025-07-30 19:14:03.982 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 58.4%, 内存: 44.4%, 磁盘: 94.5%
2025-07-30 19:14:10.876 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.2%, CPU使用率 88.0%
2025-07-30 19:14:25.981 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.2%, CPU使用率 45.8%
2025-07-30 19:14:41.086 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.3%, CPU使用率 59.3%
2025-07-30 19:14:56.193 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.1%, CPU使用率 67.9%
2025-07-30 19:15:05.003 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 59.1%, 内存: 44.1%, 磁盘: 94.5%
2025-07-30 19:15:11.297 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.1%, CPU使用率 68.0%
2025-07-30 19:15:26.404 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.2%, CPU使用率 77.8%
2025-07-30 19:15:41.510 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.4%, CPU使用率 66.7%
2025-07-30 19:15:56.614 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.3%, CPU使用率 40.0%
2025-07-30 19:16:06.028 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 61.9%, 内存: 44.1%, 磁盘: 94.5%
2025-07-30 19:16:11.720 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.2%, CPU使用率 37.5%
2025-07-30 19:16:26.825 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.1%, CPU使用率 56.5%
2025-07-30 19:16:41.931 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.4%, CPU使用率 53.6%
2025-07-30 19:16:57.035 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.2%, CPU使用率 50.0%
2025-07-30 19:17:07.050 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 50.6%, 内存: 44.3%, 磁盘: 94.5%
2025-07-30 19:17:12.142 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.4%, CPU使用率 66.7%
2025-07-30 19:17:27.282 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.7%, CPU使用率 90.6%
2025-07-30 19:17:42.387 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.5%, CPU使用率 50.0%
2025-07-30 19:17:57.492 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.5%, CPU使用率 35.7%
2025-07-30 19:18:08.071 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 49.0%, 内存: 44.6%, 磁盘: 94.5%
2025-07-30 19:18:12.596 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.5%, CPU使用率 65.4%
2025-07-30 19:18:27.701 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.3%, CPU使用率 50.0%
2025-07-30 19:18:42.806 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.0%, CPU使用率 70.0%
2025-07-30 19:18:57.923 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.7%, CPU使用率 85.2%
2025-07-30 19:19:09.194 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 82.8%, 内存: 45.2%, 磁盘: 94.5%
2025-07-30 19:19:13.028 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 44.9%, CPU使用率 33.3%
2025-07-30 19:19:28.133 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.0%, CPU使用率 76.0%
2025-07-30 19:19:43.242 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.0%, CPU使用率 67.9%
2025-07-30 19:19:58.347 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.0%, CPU使用率 89.3%
2025-07-30 19:20:10.245 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 57.3%, 内存: 45.2%, 磁盘: 94.5%
2025-07-30 19:20:13.453 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.0%, CPU使用率 58.3%
2025-07-30 19:20:28.558 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.1%, CPU使用率 37.5%
2025-07-30 19:20:43.664 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.2%, CPU使用率 51.7%
2025-07-30 19:20:58.768 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.3%, CPU使用率 48.0%
2025-07-30 19:21:11.269 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 46.9%, 内存: 45.2%, 磁盘: 94.5%
2025-07-30 19:21:13.873 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.2%, CPU使用率 43.5%
2025-07-30 19:21:28.978 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.3%, CPU使用率 30.8%
2025-07-30 19:21:44.083 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.3%, CPU使用率 42.3%
2025-07-30 19:21:59.189 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.3%, CPU使用率 45.8%
2025-07-30 19:22:12.323 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 66.9%, 内存: 45.4%, 磁盘: 94.5%
2025-07-30 19:22:14.294 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.1%, CPU使用率 50.0%
2025-07-30 19:22:29.400 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.2%, CPU使用率 50.0%
2025-07-30 19:22:44.505 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.3%, CPU使用率 33.3%
2025-07-30 19:22:59.609 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.2%, CPU使用率 54.5%
2025-07-30 19:23:13.348 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 48.3%, 内存: 45.1%, 磁盘: 94.5%
2025-07-30 19:23:14.714 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.1%, CPU使用率 50.0%
2025-07-30 19:23:29.819 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.0%, CPU使用率 46.2%
2025-07-30 19:23:44.927 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.6%, CPU使用率 60.7%
2025-07-30 19:24:00.031 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.4%, CPU使用率 58.3%
2025-07-30 19:24:14.376 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 69.9%, 内存: 45.3%, 磁盘: 94.5%
2025-07-30 19:24:15.142 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.4%, CPU使用率 53.6%
2025-07-30 19:24:30.247 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.3%, CPU使用率 54.2%
2025-07-30 19:24:45.352 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.3%, CPU使用率 25.0%
2025-07-30 19:25:00.456 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.2%, CPU使用率 48.0%
2025-07-30 19:25:15.398 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 44.9%, 内存: 45.4%, 磁盘: 94.5%
2025-07-30 19:25:15.562 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.4%, CPU使用率 52.0%
2025-07-30 19:25:30.666 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.3%, CPU使用率 65.5%
2025-07-30 19:25:45.771 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.5%, CPU使用率 44.8%
2025-07-30 19:26:00.877 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.9%, CPU使用率 46.4%
2025-07-30 19:26:15.982 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.4%, CPU使用率 57.1%
2025-07-30 19:26:16.423 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 40.4%, 内存: 45.4%, 磁盘: 94.5%
2025-07-30 19:26:31.087 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.5%, CPU使用率 48.3%
2025-07-30 19:26:46.794 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 47.8%, CPU使用率 100.0%
2025-07-30 19:27:02.187 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.8%, CPU使用率 93.2%
2025-07-30 19:27:17.297 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.6%, CPU使用率 58.3%
2025-07-30 19:27:17.449 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 47.3%, 内存: 45.6%, 磁盘: 94.5%
2025-07-30 19:27:32.468 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.6%, CPU使用率 100.0%
2025-07-30 19:27:47.574 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.9%, CPU使用率 50.0%
2025-07-30 19:28:02.679 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 45.7%, CPU使用率 42.9%
2025-07-30 19:28:17.785 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 46.1%, CPU使用率 86.2%
2025-07-30 19:28:18.539 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 87.8%, 内存: 46.3%, 磁盘: 94.5%
2025-07-30 19:28:32.892 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 46.3%, CPU使用率 20.8%
2025-07-30 19:28:47.610 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: GET /api/health ---
2025-07-30 19:28:47.611 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 19:28:47.612 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 19:28:47.613 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 19:28:47.995 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 47.7%, CPU使用率 28.0%
2025-07-30 19:28:49.652 [INFO] [main] [main.auth_debug_middleware:116] - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 19:28:49.653 [INFO] [main] [main.auth_debug_middleware:127] - 请求没有认证头部
2025-07-30 19:28:49.654 [INFO] [main] [main.auth_debug_middleware:237] - 没有认证头部，设置用户为None
2025-07-30 19:28:49.655 [DEBUG] [app.core.db_connection] [db_connection.get_db_session:489] - 当前线程ID: 5096
2025-07-30 19:28:49.656 [INFO] [app.core.db_connection] [db_connection.get_db_session:492] - 尝试使用db_connection获取会话
2025-07-30 19:28:49.657 [INFO] [app.core.db_connection] [db_connection.connect:175] - 数据库连接已创建
2025-07-30 19:28:49.658 [DEBUG] [app.core.db_connection] [db_connection.checkout:179] - 数据库连接已检出
2025-07-30 19:28:49.659 [INFO] [app.core.db_connection] [db_connection.get_session:215] - 数据库连接成功 (尝试 1/3)
2025-07-30 19:28:49.660 [INFO] [app.core.db_connection] [db_connection.get_db_session:496] - 使用db_connection获取会话成功
2025-07-30 19:28:50.439 [INFO] [app.core.security] [security.verify_password:151] - 使用sha256_crypt验证密码成功
2025-07-30 19:28:50.442 [INFO] [main] [main.auth_debug_middleware:245] - --- 请求结束: 200 ---

2025-07-30 19:29:03.099 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 48.5%, CPU使用率 35.7%
2025-07-30 19:29:18.204 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 48.2%, CPU使用率 20.8%
2025-07-30 19:29:19.568 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 36.3%, 内存: 48.2%, 磁盘: 94.5%
2025-07-30 19:29:33.308 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 49.2%, CPU使用率 33.3%
2025-07-30 19:29:48.414 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 49.0%, CPU使用率 28.6%
2025-07-30 19:30:03.603 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 47.9%, CPU使用率 100.0%
2025-07-30 19:30:18.712 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 47.6%, CPU使用率 23.1%
2025-07-30 19:30:20.589 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 14.8%, 内存: 47.5%, 磁盘: 94.5%
2025-07-30 19:30:33.818 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 47.5%, CPU使用率 60.7%
2025-07-30 19:30:48.922 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 47.5%, CPU使用率 0.0%
2025-07-30 19:31:04.026 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 47.5%, CPU使用率 0.0%
2025-07-30 19:31:19.130 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 47.3%, CPU使用率 14.3%
2025-07-30 19:31:21.610 [DEBUG] [health_monitor] [health_monitor._collect_metrics:208] - 系统指标 - CPU: 19.1%, 内存: 47.4%, 磁盘: 94.5%
2025-07-30 19:31:34.235 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 47.9%, CPU使用率 29.2%
2025-07-30 19:31:49.342 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 47.6%, CPU使用率 4.2%
2025-07-30 19:32:04.447 [DEBUG] [monitoring] [monitoring.update_resource_metrics:119] - 资源指标更新: 内存使用率 47.4%, CPU使用率 30.8%
