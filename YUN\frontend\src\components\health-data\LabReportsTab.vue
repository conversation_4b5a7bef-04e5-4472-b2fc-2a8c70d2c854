<template>
  <div class="lab-reports-tab">
    <div class="tab-header">
      <h3>实验室检验报告</h3>
      <div class="filter-container">
        <el-select v-model="filterType" placeholder="检验类型" clearable @change="filterReports">
          <el-option label="全部" value="" />
          <el-option label="血液检验" value="blood" />
          <el-option label="生化检验" value="biochemical" />
          <el-option label="免疫检验" value="immunology" />
          <el-option label="微生物检验" value="microbiology" />
          <el-option label="其他检验" value="other" />
        </el-select>
        <el-select v-model="filterAbnormal" placeholder="结果状态" clearable @change="filterReports">
          <el-option label="全部" value="" />
          <el-option label="异常" value="true" />
          <el-option label="正常" value="false" />
        </el-select>
        <el-button type="primary" size="small" @click="refreshData">刷新数据</el-button>
      </div>
    </div>

    <el-table
      :data="filteredReports"
      style="width: 100%"
      v-loading="loading"
      :empty-text="emptyText"
    >
      <el-table-column prop="test_date" label="检验日期" width="120">
        <template #default="scope">
          {{ formatDate(scope.row.test_date) }}
        </template>
      </el-table-column>
      <el-table-column prop="test_type" label="检验类型" width="120">
        <template #default="scope">
          {{ getTestTypeLabel(scope.row.test_type) }}
        </template>
      </el-table-column>
      <el-table-column prop="test_name" label="检验项目" width="150" />
      <el-table-column prop="specimen" label="检验标本" width="120" />
      <el-table-column prop="hospital_name" label="医院" width="180" />
      <el-table-column prop="department" label="科室" width="120" />
      <el-table-column prop="is_abnormal" label="结果" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.is_abnormal ? 'danger' : 'success'">
            {{ scope.row.is_abnormal ? '异常' : '正常' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120" fixed="right">
        <template #default="scope">
          <el-button type="primary" link @click="viewReport(scope.row)">查看</el-button>
          <el-button type="danger" link @click="deleteReport(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 报告详情对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="70%"
    >
      <div v-if="currentReport" class="report-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="检验日期">{{ formatDate(currentReport.test_date) }}</el-descriptions-item>
          <el-descriptions-item label="报告日期">{{ formatDate(currentReport.result_date) }}</el-descriptions-item>
          <el-descriptions-item label="检验类型">{{ getTestTypeLabel(currentReport.test_type) }}</el-descriptions-item>
          <el-descriptions-item label="检验项目">{{ currentReport.test_name }}</el-descriptions-item>
          <el-descriptions-item label="检验标本">{{ currentReport.specimen }}</el-descriptions-item>
          <el-descriptions-item label="结果状态">
            <el-tag :type="currentReport.is_abnormal ? 'danger' : 'success'">
              {{ currentReport.is_abnormal ? '异常' : '正常' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="医院">{{ currentReport.hospital_name }}</el-descriptions-item>
          <el-descriptions-item label="科室">{{ currentReport.department }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="report-results">
          <h4>检验结果</h4>
          <el-table :data="parseResults(currentReport.result_items)" border style="width: 100%">
            <el-table-column prop="item_name" label="检验项目" width="180" />
            <el-table-column prop="item_value" label="结果值" width="120" />
            <el-table-column prop="reference_range" label="参考范围" width="180" />
            <el-table-column prop="unit" label="单位" width="100" />
            <el-table-column prop="is_abnormal" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.is_abnormal ? 'danger' : 'success'">
                  {{ scope.row.is_abnormal ? '异常' : '正常' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
        
        <div class="report-summary" v-if="currentReport.result_summary">
          <h4>结果小结</h4>
          <div class="summary-content">{{ currentReport.result_summary }}</div>
        </div>
        
        <div class="report-notes" v-if="currentReport.notes">
          <h4>备注</h4>
          <div class="notes-content">{{ currentReport.notes }}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import axios from 'axios';
import { getMockData, isMockEnabled } from '../../mocks/mockDataManager';

// 接收用户ID作为属性
const props = defineProps({
  customId: {
    type: [Number, String],
    required: true
  }
});

// 状态变量
const loading = ref(false);
const labReports = ref([]);
const filterType = ref('');
const filterAbnormal = ref('');
const dialogVisible = ref(false);
const currentReport = ref(null);

// 计算属性
const dialogTitle = computed(() => {
  if (!currentReport.value) return '检验报告详情';
  
  const typeLabel = getTestTypeLabel(currentReport.value.test_type);
  const dateStr = formatDate(currentReport.value.test_date);
  
  return `${typeLabel} - ${currentReport.value.test_name} (${dateStr})`;
});

const emptyText = computed(() => {
  return loading.value ? '加载中...' : '暂无检验报告数据';
});

const filteredReports = computed(() => {
  let result = labReports.value;
  
  if (filterType.value) {
    result = result.filter(report => report.test_type === filterType.value);
  }
  
  if (filterAbnormal.value !== '') {
    const isAbnormal = filterAbnormal.value === 'true';
    result = result.filter(report => report.is_abnormal === isAbnormal);
  }
  
  return result;
});

// 获取检验报告数据
const fetchLabReports = async () => {
  if (!props.customId) return;
  
  loading.value = true;
  try {
    const response = await axios.get(`/api/v1/aggregated/health-profile/${props.customId}`, {
        params: {
          include_lab_reports: true
        }
      });
    const profileData = response.data.profile_data || {};
    labReports.value = profileData.lab_reports || [];
    
    // 如果没有真实数据且启用了模拟数据，使用模拟数据
    if ((!response.data || response.data.length === 0) && isMockEnabled()) {
      labReports.value = getMockData('labReports', { custom_id: props.customId });
    }
  } catch (error) {
    console.error('获取检验报告失败:', error);
    ElMessage.error('获取检验报告失败，请稍后重试');
    
    // 如果启用了模拟数据，使用模拟数据
    if (isMockEnabled()) {
      labReports.value = getMockData('labReports', { custom_id: props.customId });
    }
  } finally {
    loading.value = false;
  }
};

// 监听用户ID变化
watch(() => props.customId, (newVal) => {
  if (newVal) {
    fetchLabReports();
  } else {
    labReports.value = [];
  }
}, { immediate: true });

// 初始化
onMounted(() => {
  if (props.customId) {
    fetchLabReports();
  }
});

// 刷新数据
const refreshData = () => {
  fetchLabReports();
};

// 筛选报告
const filterReports = () => {
  // 筛选逻辑已通过计算属性实现
};

// 查看报告详情
const viewReport = (report) => {
  currentReport.value = report;
  dialogVisible.value = true;
};

// 删除报告
const deleteReport = (report) => {
  ElMessageBox.confirm(
    `确定要删除 ${formatDate(report.test_date)} 的${report.test_name}检验报告吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await axios.delete(`/api/user-health-records/user/${props.customId}/${report.id}`, {
        params: {
          record_type: 'lab'
        }
      });
      ElMessage.success('删除成功');
      fetchLabReports(); // 刷新数据
    } catch (error) {
      console.error('删除检验报告失败:', error);
      ElMessage.error('删除检验报告失败，请稍后重试');
      
      // 如果启用了模拟数据，模拟删除成功
      if (isMockEnabled()) {
        labReports.value = labReports.value.filter(item => item.id !== report.id);
        ElMessage.success('删除成功');
      }
    }
  }).catch(() => {
    // 用户取消删除
  });
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  return date.toLocaleDateString();
};

// 获取检验类型标签
const getTestTypeLabel = (type) => {
  const typeMap = {
    'blood': '血液检验',
    'biochemical': '生化检验',
    'immunology': '免疫检验',
    'microbiology': '微生物检验',
    'other': '其他检验'
  };
  
  return typeMap[type] || type;
};

// 解析检验结果项
const parseResults = (resultItems) => {
  if (!resultItems) return [];
  
  // 如果是字符串，尝试解析JSON
  if (typeof resultItems === 'string') {
    try {
      return JSON.parse(resultItems);
    } catch (error) {
      console.error('解析检验结果项失败:', error);
      return [];
    }
  }
  
  // 如果已经是数组，直接返回
  return resultItems;
};
</script>

<style scoped>
.lab-reports-tab {
  padding: 10px;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.tab-header h3 {
  margin: 0;
}

.filter-container {
  display: flex;
  gap: 10px;
}

.report-detail {
  padding: 10px;
}

.report-results,
.report-summary,
.report-notes {
  margin-top: 20px;
}

.report-results h4,
.report-summary h4,
.report-notes h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #303133;
  font-size: 16px;
}

.summary-content,
.notes-content {
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  white-space: pre-line;
}
</style>
