<template>
  <div class="test-config-manager">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>测试配置管理</h2>
      <p>管理测试环境配置、参数设置和执行策略</p>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" icon="Plus" @click="showCreateDialog = true">
          新建配置
        </el-button>
        <el-button icon="Refresh" @click="loadConfigs">
          刷新
        </el-button>
        <el-button icon="Upload" @click="importConfig">
          导入配置
        </el-button>
        <el-button icon="Download" @click="exportConfigs">
          导出配置
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索配置名称或描述"
          prefix-icon="Search"
          style="width: 300px"
          clearable
        />
        <el-select v-model="filterEnvironment" placeholder="环境" style="width: 120px; margin-left: 10px">
          <el-option label="全部" value="" />
          <el-option label="开发" value="development" />
          <el-option label="测试" value="testing" />
          <el-option label="预发布" value="staging" />
          <el-option label="生产" value="production" />
        </el-select>
      </div>
    </div>

    <!-- 配置列表 -->
    <div class="config-grid">
      <div
        v-for="config in filteredConfigs"
        :key="config.id"
        class="config-card"
        :class="{ active: config.active }"
      >
        <div class="config-header">
          <div class="config-title">
            <h3>{{ config.name }}</h3>
            <el-tag :type="getEnvironmentType(config.environment)" size="small">
              {{ getEnvironmentLabel(config.environment) }}
            </el-tag>
          </div>
          <div class="config-actions">
            <el-dropdown @command="handleConfigAction">
              <el-button type="text" icon="MoreFilled" />
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="{ action: 'edit', config }" icon="Edit">
                    编辑
                  </el-dropdown-item>
                  <el-dropdown-item :command="{ action: 'clone', config }" icon="CopyDocument">
                    克隆
                  </el-dropdown-item>
                  <el-dropdown-item :command="{ action: 'activate', config }" icon="Check">
                    {{ config.active ? '停用' : '激活' }}
                  </el-dropdown-item>
                  <el-dropdown-item :command="{ action: 'export', config }" icon="Download">
                    导出
                  </el-dropdown-item>
                  <el-dropdown-item :command="{ action: 'delete', config }" icon="Delete" divided>
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>

        <div class="config-description">
          <p>{{ config.description || '暂无描述' }}</p>
        </div>

        <div class="config-details">
          <div class="detail-item">
            <span class="label">测试类型:</span>
            <span class="value">{{ config.testTypes?.join(', ') || '未设置' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">超时时间:</span>
            <span class="value">{{ config.timeout || 30000 }}ms</span>
          </div>
          <div class="detail-item">
            <span class="label">重试次数:</span>
            <span class="value">{{ config.retries || 0 }}次</span>
          </div>
          <div class="detail-item">
            <span class="label">并行执行:</span>
            <span class="value">{{ config.parallel ? '是' : '否' }}</span>
          </div>
        </div>

        <div class="config-footer">
          <div class="config-meta">
            <span>创建时间: {{ formatDate(config.createdAt) }}</span>
            <span>更新时间: {{ formatDate(config.updatedAt) }}</span>
          </div>
          <div class="config-status">
            <el-switch
              v-model="config.active"
              @change="toggleConfigStatus(config)"
              active-text="启用"
              inactive-text="停用"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="filteredConfigs.length === 0" class="empty-state">
      <el-empty description="暂无配置数据">
        <el-button type="primary" @click="showCreateDialog = true">
          创建第一个配置
        </el-button>
      </el-empty>
    </div>

    <!-- 创建/编辑配置对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingConfig ? '编辑配置' : '创建配置'"
      width="800px"
      @close="resetForm"
    >
      <el-form
        ref="configFormRef"
        :model="configForm"
        :rules="configRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="配置名称" prop="name">
              <el-input v-model="configForm.name" placeholder="请输入配置名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="环境" prop="environment">
              <el-select v-model="configForm.environment" placeholder="选择环境">
                <el-option label="开发" value="development" />
                <el-option label="测试" value="testing" />
                <el-option label="预发布" value="staging" />
                <el-option label="生产" value="production" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="描述">
          <el-input
            v-model="configForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入配置描述"
          />
        </el-form-item>

        <el-form-item label="测试类型">
          <el-checkbox-group v-model="configForm.testTypes">
            <el-checkbox label="unit">单元测试</el-checkbox>
            <el-checkbox label="integration">集成测试</el-checkbox>
            <el-checkbox label="e2e">端到端测试</el-checkbox>
            <el-checkbox label="api">API测试</el-checkbox>
            <el-checkbox label="performance">性能测试</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="超时时间(ms)">
              <el-input-number
                v-model="configForm.timeout"
                :min="1000"
                :max="300000"
                :step="1000"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="重试次数">
              <el-input-number
                v-model="configForm.retries"
                :min="0"
                :max="10"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="并行数量">
              <el-input-number
                v-model="configForm.parallelCount"
                :min="1"
                :max="20"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="并行执行">
              <el-switch v-model="configForm.parallel" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="覆盖率检查">
              <el-switch v-model="configForm.coverage" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="环境变量">
          <div class="env-vars">
            <div
              v-for="(envVar, index) in configForm.envVars"
              :key="index"
              class="env-var-item"
            >
              <el-input
                v-model="envVar.key"
                placeholder="变量名"
                style="width: 200px; margin-right: 10px"
              />
              <el-input
                v-model="envVar.value"
                placeholder="变量值"
                style="width: 300px; margin-right: 10px"
              />
              <el-button
                type="danger"
                icon="Delete"
                @click="removeEnvVar(index)"
              />
            </div>
            <el-button type="primary" icon="Plus" @click="addEnvVar">
              添加环境变量
            </el-button>
          </div>
        </el-form-item>

        <el-form-item label="高级配置">
          <el-input
            v-model="configForm.advancedConfig"
            type="textarea"
            :rows="6"
            placeholder="JSON格式的高级配置"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveConfig" :loading="saving">
          {{ editingConfig ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 导入配置对话框 -->
    <el-dialog v-model="showImportDialog" title="导入配置" width="600px">
      <el-upload
        ref="uploadRef"
        :auto-upload="false"
        :on-change="handleFileChange"
        :show-file-list="false"
        accept=".json"
        drag
      >
        <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
        <div class="el-upload__text">
          将配置文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            只能上传 JSON 格式的配置文件
          </div>
        </template>
      </el-upload>

      <div v-if="importPreview" class="import-preview">
        <h4>配置预览:</h4>
        <pre>{{ JSON.stringify(importPreview, null, 2) }}</pre>
      </div>

      <template #footer>
        <el-button @click="showImportDialog = false">取消</el-button>
        <el-button
          type="primary"
          @click="confirmImport"
          :disabled="!importPreview"
          :loading="importing"
        >
          确认导入
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { testApi } from '@/api/test'

// 响应式数据
const configs = ref([])
const searchKeyword = ref('')
const filterEnvironment = ref('')
const showCreateDialog = ref(false)
const showImportDialog = ref(false)
const editingConfig = ref(null)
const saving = ref(false)
const importing = ref(false)
const importPreview = ref(null)

// 表单引用
const configFormRef = ref()
const uploadRef = ref()

// 配置表单
const configForm = reactive({
  name: '',
  environment: 'development',
  description: '',
  testTypes: ['unit'],
  timeout: 30000,
  retries: 2,
  parallel: true,
  parallelCount: 4,
  coverage: true,
  envVars: [],
  advancedConfig: '{}'
})

// 表单验证规则
const configRules = {
  name: [
    { required: true, message: '请输入配置名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  environment: [
    { required: true, message: '请选择环境', trigger: 'change' }
  ]
}

// 计算属性
const filteredConfigs = computed(() => {
  let filtered = configs.value

  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(config =>
      config.name.toLowerCase().includes(keyword) ||
      (config.description && config.description.toLowerCase().includes(keyword))
    )
  }

  if (filterEnvironment.value) {
    filtered = filtered.filter(config => config.environment === filterEnvironment.value)
  }

  return filtered
})

// 方法
const loadConfigs = async () => {
  try {
    // 模拟加载配置数据
    configs.value = [
      {
        id: '1',
        name: '开发环境配置',
        environment: 'development',
        description: '用于开发环境的测试配置',
        testTypes: ['unit', 'integration'],
        timeout: 30000,
        retries: 2,
        parallel: true,
        parallelCount: 4,
        coverage: true,
        active: true,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-15')
      },
      {
        id: '2',
        name: '测试环境配置',
        environment: 'testing',
        description: '用于测试环境的完整测试配置',
        testTypes: ['unit', 'integration', 'e2e'],
        timeout: 60000,
        retries: 3,
        parallel: true,
        parallelCount: 8,
        coverage: true,
        active: true,
        createdAt: new Date('2024-01-02'),
        updatedAt: new Date('2024-01-16')
      },
      {
        id: '3',
        name: '生产环境配置',
        environment: 'production',
        description: '用于生产环境的性能测试配置',
        testTypes: ['api', 'performance'],
        timeout: 120000,
        retries: 1,
        parallel: false,
        parallelCount: 1,
        coverage: false,
        active: false,
        createdAt: new Date('2024-01-03'),
        updatedAt: new Date('2024-01-17')
      }
    ]
  } catch (error) {
    ElMessage.error('加载配置失败: ' + error.message)
  }
}

const getEnvironmentType = (environment) => {
  const types = {
    development: 'success',
    testing: 'warning',
    staging: 'info',
    production: 'danger'
  }
  return types[environment] || 'info'
}

const getEnvironmentLabel = (environment) => {
  const labels = {
    development: '开发',
    testing: '测试',
    staging: '预发布',
    production: '生产'
  }
  return labels[environment] || environment
}

const handleConfigAction = async ({ action, config }) => {
  switch (action) {
    case 'edit':
      editConfig(config)
      break
    case 'clone':
      cloneConfig(config)
      break
    case 'activate':
      await toggleConfigStatus(config)
      break
    case 'export':
      exportConfig(config)
      break
    case 'delete':
      await deleteConfig(config)
      break
  }
}

const editConfig = (config) => {
  editingConfig.value = config
  Object.assign(configForm, {
    ...config,
    envVars: config.envVars || [],
    advancedConfig: JSON.stringify(config.advancedConfig || {}, null, 2)
  })
  showCreateDialog.value = true
}

const cloneConfig = (config) => {
  editingConfig.value = null
  Object.assign(configForm, {
    ...config,
    name: config.name + ' (副本)',
    envVars: config.envVars || [],
    advancedConfig: JSON.stringify(config.advancedConfig || {}, null, 2)
  })
  showCreateDialog.value = true
}

const toggleConfigStatus = async (config) => {
  try {
    config.active = !config.active
    ElMessage.success(`配置已${config.active ? '启用' : '停用'}`)
  } catch (error) {
    config.active = !config.active
    ElMessage.error('操作失败: ' + error.message)
  }
}

const deleteConfig = async (config) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除配置 "${config.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const index = configs.value.findIndex(c => c.id === config.id)
    if (index > -1) {
      configs.value.splice(index, 1)
      ElMessage.success('配置删除成功')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error.message)
    }
  }
}

const saveConfig = async () => {
  try {
    await configFormRef.value.validate()
    saving.value = true

    // 验证高级配置JSON格式
    let advancedConfig = {}
    if (configForm.advancedConfig.trim()) {
      try {
        advancedConfig = JSON.parse(configForm.advancedConfig)
      } catch (error) {
        ElMessage.error('高级配置JSON格式错误')
        return
      }
    }

    const configData = {
      ...configForm,
      advancedConfig,
      updatedAt: new Date()
    }

    if (editingConfig.value) {
      // 更新配置
      const index = configs.value.findIndex(c => c.id === editingConfig.value.id)
      if (index > -1) {
        configs.value[index] = { ...editingConfig.value, ...configData }
      }
      ElMessage.success('配置更新成功')
    } else {
      // 创建新配置
      const newConfig = {
        id: Date.now().toString(),
        ...configData,
        active: true,
        createdAt: new Date()
      }
      configs.value.unshift(newConfig)
      ElMessage.success('配置创建成功')
    }

    showCreateDialog.value = false
    resetForm()
  } catch (error) {
    ElMessage.error('保存失败: ' + error.message)
  } finally {
    saving.value = false
  }
}

const resetForm = () => {
  editingConfig.value = null
  Object.assign(configForm, {
    name: '',
    environment: 'development',
    description: '',
    testTypes: ['unit'],
    timeout: 30000,
    retries: 2,
    parallel: true,
    parallelCount: 4,
    coverage: true,
    envVars: [],
    advancedConfig: '{}'
  })
  configFormRef.value?.clearValidate()
}

const addEnvVar = () => {
  configForm.envVars.push({ key: '', value: '' })
}

const removeEnvVar = (index) => {
  configForm.envVars.splice(index, 1)
}

const importConfig = () => {
  showImportDialog.value = true
  importPreview.value = null
}

const handleFileChange = (file) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      importPreview.value = JSON.parse(e.target.result)
    } catch (error) {
      ElMessage.error('文件格式错误，请上传有效的JSON文件')
      importPreview.value = null
    }
  }
  reader.readAsText(file.raw)
}

const confirmImport = async () => {
  try {
    importing.value = true
    
    if (Array.isArray(importPreview.value)) {
      // 批量导入
      importPreview.value.forEach(config => {
        const newConfig = {
          ...config,
          id: Date.now().toString() + Math.random(),
          createdAt: new Date(),
          updatedAt: new Date()
        }
        configs.value.unshift(newConfig)
      })
      ElMessage.success(`成功导入 ${importPreview.value.length} 个配置`)
    } else {
      // 单个导入
      const newConfig = {
        ...importPreview.value,
        id: Date.now().toString(),
        createdAt: new Date(),
        updatedAt: new Date()
      }
      configs.value.unshift(newConfig)
      ElMessage.success('配置导入成功')
    }
    
    showImportDialog.value = false
    importPreview.value = null
  } catch (error) {
    ElMessage.error('导入失败: ' + error.message)
  } finally {
    importing.value = false
  }
}

const exportConfig = (config) => {
  const dataStr = JSON.stringify(config, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)
  const link = document.createElement('a')
  link.href = url
  link.download = `${config.name}-config.json`
  link.click()
  URL.revokeObjectURL(url)
  ElMessage.success('配置导出成功')
}

const exportConfigs = () => {
  const dataStr = JSON.stringify(configs.value, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)
  const link = document.createElement('a')
  link.href = url
  link.download = `test-configs-${new Date().toISOString().split('T')[0]}.json`
  link.click()
  URL.revokeObjectURL(url)
  ElMessage.success('所有配置导出成功')
}

const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadConfigs()
})
</script>

<style scoped>
.test-config-manager {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar-left {
  display: flex;
  gap: 10px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.config-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.config-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.config-card.active {
  border-color: #409eff;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.config-title {
  flex: 1;
}

.config-title h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.config-description {
  margin-bottom: 16px;
}

.config-description p {
  margin: 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.config-details {
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.detail-item .label {
  color: #909399;
  font-weight: 500;
}

.detail-item .value {
  color: #303133;
  font-weight: 600;
}

.config-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.config-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
  color: #909399;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.env-vars {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  background: #fafafa;
}

.env-var-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.env-var-item:last-child {
  margin-bottom: 0;
}

.import-preview {
  margin-top: 20px;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
}

.import-preview h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
}

.import-preview pre {
  margin: 0;
  font-size: 12px;
  color: #606266;
  white-space: pre-wrap;
  word-break: break-all;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .test-config-manager {
    padding: 10px;
  }
  
  .toolbar {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .toolbar-right {
    justify-content: space-between;
  }
  
  .config-grid {
    grid-template-columns: 1fr;
  }
  
  .config-footer {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .env-var-item {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .test-config-manager {
    background: #1a1a1a;
  }
  
  .page-header h2 {
    color: #e4e7ed;
  }
  
  .page-header p {
    color: #a8abb2;
  }
  
  .toolbar,
  .config-card,
  .empty-state {
    background: #2d2d2d;
    border-color: #4c4d4f;
  }
  
  .config-title h3 {
    color: #e4e7ed;
  }
  
  .config-description p {
    color: #c0c4cc;
  }
  
  .detail-item .label {
    color: #a8abb2;
  }
  
  .detail-item .value {
    color: #e4e7ed;
  }
  
  .config-footer {
    border-top-color: #4c4d4f;
  }
  
  .config-meta {
    color: #a8abb2;
  }
  
  .env-vars {
    background: #1a1a1a;
    border-color: #4c4d4f;
  }
  
  .import-preview {
    background: #1a1a1a;
    border-color: #4c4d4f;
  }
  
  .import-preview h4 {
    color: #e4e7ed;
  }
  
  .import-preview pre {
    color: #c0c4cc;
  }
}
</style>