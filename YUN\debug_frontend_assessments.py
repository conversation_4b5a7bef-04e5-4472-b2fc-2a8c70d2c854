# -*- coding: utf-8 -*-
"""
调试前端量表列表显示问题
检查新API返回的数据结构是否与前端期望匹配
"""

import requests
import json
from datetime import datetime
import sys
import os

# 添加后端路径以导入MockDataManager
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
try:
    from backend.app.core.mock_data_manager import get_debug_config
except ImportError:
    # 如果导入失败，使用默认配置
    def get_debug_config():
        return {
            "base_url": "http://127.0.0.1:8006",
            "username": "admin",
            "password": "admin123",
            "expected_fields": ['id', 'name', 'category', 'item_count', 'target', 'created_at'],
            "field_mapping": {
                'template_id': 'id',
                'template_name': 'name', 
                'question_count': 'item_count',
                'response_count': 'usage_count',
                'category': 'category', 
                'target': 'target',
                'created_at': 'created_at'
            }
        }

# 从MockDataManager获取配置
config = get_debug_config()
BASE_URL = config["base_url"]
USERNAME = config["username"]
PASSWORD = config["password"]

def login_and_get_token():
    """登录并获取token"""
    try:
        login_data = {
            "username": USERNAME,
            "password": PASSWORD
        }
        
        response = requests.post(f"{BASE_URL}/auth/login", data=login_data)
        print(f"登录响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"登录响应数据: {result}")
                if result:
                    token = result.get('access_token') or result.get('token')
                    if token:
                        print(f"✅ 登录成功，获取到token: {token[:20]}...")
                        return token
                    else:
                        print(f"❌ 响应中未找到token字段")
                        return None
                else:
                    print(f"❌ 登录响应为空")
                    return None
            except Exception as json_error:
                print(f"❌ 解析登录响应JSON失败: {json_error}")
                print(f"原始响应内容: {response.text}")
                return None
        else:
            print(f"❌ 登录失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 登录异常: {str(e)}")
        return None

def check_api_response_structure(token):
    """检查新API的响应结构"""
    print("\n=== 检查新API响应结构 ===")    
    try:
        headers = {'Authorization': f'Bearer {token}'}
        response = requests.get(f"{BASE_URL}/api/clinical-scales/standard-assessments", headers=headers)
        
        print(f"API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"\n📊 API响应数据结构分析:")
            print(f"响应数据类型: {type(data)}")
            
            if isinstance(data, dict):
                print(f"响应对象的键: {list(data.keys())}")
                
                # 检查各种可能的数据字段
                for key in ['data', 'items', 'assessments', 'records']:
                    if key in data:
                        value = data[key]
                        print(f"\n🔍 发现字段 '{key}':")
                        print(f"  类型: {type(value)}")
                        if isinstance(value, list):
                            print(f"  数组长度: {len(value)}")
                            if value:
                                first_item = value[0]
                                print(f"  第一项类型: {type(first_item)}")
                                if isinstance(first_item, dict):
                                    print(f"  第一项字段: {list(first_item.keys())}")
                                    print(f"  第一项示例数据:")
                                    for field, val in first_item.items():
                                        print(f"    {field}: {val}")
                                        
                # 从配置获取前端期望的字段
                expected_fields = config["expected_fields"]
                print(f"\n🎯 前端期望的字段: {expected_fields}")
                
                # 尝试找到实际的数据数组
                actual_data = None
                if isinstance(data, list):
                    actual_data = data
                elif 'data' in data and isinstance(data['data'], list):
                    actual_data = data['data']
                elif 'items' in data and isinstance(data['items'], list):
                    actual_data = data['items']
                elif 'assessments' in data and isinstance(data['assessments'], list):
                    actual_data = data['assessments']
                    
                if actual_data and len(actual_data) > 0:
                    first_item = actual_data[0]
                    if isinstance(first_item, dict):
                        actual_fields = list(first_item.keys())
                        print(f"\n✅ 实际数据字段: {actual_fields}")
                        
                        # 字段匹配分析
                        missing_fields = [f for f in expected_fields if f not in actual_fields]
                        extra_fields = [f for f in actual_fields if f not in expected_fields]
                        
                        if missing_fields:
                            print(f"❌ 缺少字段: {missing_fields}")
                        if extra_fields:
                            print(f"ℹ️  额外字段: {extra_fields}")
                        if not missing_fields:
                            print(f"✅ 所有期望字段都存在")
                            
                        # 从配置获取字段映射建议
                        print(f"\n💡 字段映射建议:")
                        field_mapping = config["field_mapping"]
                        
                        for api_field, frontend_field in field_mapping.items():
                            if api_field in actual_fields:
                                print(f"  {api_field} -> {frontend_field}")
                else:
                    print(f"❌ 未找到有效的数据数组")
                    
            elif isinstance(data, list):
                print(f"响应是数组，长度: {len(data)}")
                if data:
                    first_item = data[0]
                    print(f"第一项类型: {type(first_item)}")
                    if isinstance(first_item, dict):
                        print(f"第一项字段: {list(first_item.keys())}")
                        
            # 输出完整响应用于调试
            print(f"\n📋 完整API响应:")
            print(json.dumps(data, ensure_ascii=False, indent=2))
            
        else:
            print(f"❌ API调用失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 检查API响应异常: {str(e)}")

def suggest_frontend_fix(token):
    """基于API响应建议前端修复方案"""
    print("\n=== 前端修复建议 ===")
    
    try:
        headers = {'Authorization': f'Bearer {token}'}
        response = requests.get(f"{BASE_URL}/api/clinical-scales/standard-assessments", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            
            print("\n🔧 建议的前端修复代码:")
            print("```javascript")
            print("// 在fetchAssessments函数中，替换数据处理部分:")
            print("")
            
            if isinstance(data, dict) and 'data' in data:
                print("// API返回格式: {status, data, total}")
                print("if (response.data && response.data.data) {")
                print("  // 映射API字段到前端期望字段")
                print("  assessments.value = response.data.data.map(item => ({")
                print("    id: item.template_id || item.id,")
                print("    name: item.template_name || item.name,")
                print("    category: item.category || '未分类',")
                print("    item_count: item.question_count || item.item_count || 0,")
                print("    target: item.target || '通用',")
                print("    created_at: item.created_at || item.create_time || ''")
                print("  }))")
                print("}")
            else:
                print("// 根据实际API响应调整")
                print("assessments.value = response.data")
                
            print("```")
            
            # 检查是否需要字段映射
            if 'data' in data and isinstance(data['data'], list) and data['data']:
                first_item = data['data'][0]
                if isinstance(first_item, dict):
                    actual_fields = list(first_item.keys())
                    
                    print("\n📝 字段映射对照表:")
                    print("| API字段 | 前端字段 | 说明 |")
                    print("|---------|----------|------|")
                    
                    # 从配置获取字段映射
                    mapping = config["field_mapping"]
                    
                    for api_field, frontend_field in mapping.items():
                        status = "✅" if api_field in actual_fields else "❌"
                        print(f"| {api_field} | {frontend_field} | {status} |")
                        
        else:
            print(f"❌ 无法获取API响应进行分析")
            
    except Exception as e:
        print(f"❌ 生成修复建议异常: {str(e)}")

def main():
    print("=== 前端量表列表显示问题调试 ===")
    print(f"调试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"后端地址: {BASE_URL}")
    
    # 登录获取token
    token = login_and_get_token()
    if not token:
        print("❌ 无法获取token，调试终止")
        return
    
    # 检查API响应结构
    check_api_response_structure(token)
    
    # 提供修复建议
    suggest_frontend_fix(token)
    
    print("\n=== 调试完成 ===")
    print("请根据上述分析结果修改前端代码")

if __name__ == "__main__":
    main()