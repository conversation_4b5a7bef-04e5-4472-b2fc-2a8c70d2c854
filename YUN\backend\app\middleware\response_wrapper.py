from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
import json

class ResponseWrapperMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        response = await call_next(request)
        # 只包装JSON响应
        if isinstance(response, JSONResponse):
            # 读取原始body
            body = b""
            async for chunk in response.body_iterator:
                body += chunk
            try:
                data = json.loads(body.decode())
            except Exception:
                data = body.decode()
            wrapped = {"code": 0, "message": "success", "data": data}
            return JSONResponse(content=wrapped, status_code=response.status_code)
        return response 