import pytest
import os
from unittest.mock import patch
from app.core.mock_data_manager import Backend<PERSON>ockDataManager, is_mock_enabled


class TestMockDataManager:
    """测试模拟数据管理器"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        self.manager = BackendMockDataManager()
    
    def test_is_enabled_default(self):
        """测试默认启用状态"""
        with patch.dict(os.environ, {}, clear=True):
            manager = BackendMockDataManager()
            enabled = manager.is_enabled()
            assert isinstance(enabled, bool)
    
    def test_is_enabled_from_env(self):
        """测试从环境变量读取启用状态"""
        with patch.dict(os.environ, {'ENABLE_MOCK_DATA': 'false'}):
            manager = BackendMockDataManager()
            assert manager.is_enabled() is False
        
        with patch.dict(os.environ, {'ENABLE_MOCK_DATA': 'true'}):
            manager = BackendMockDataManager()
            assert manager.is_enabled() is True
    
    def test_dashboard_stats_structure(self):
        """测试仪表盘统计数据结构"""
        from app.core.mock_data_manager import get_mock_dashboard_stats
        data = get_mock_dashboard_stats()
        assert data is not None
        assert 'total_records' in data
        assert 'abnormal_records' in data
        assert 'last_checkup_days' in data
        assert 'completed_questionnaires' in data
        assert 'health_index' in data
        assert 'recent_activities' in data
        assert isinstance(data['recent_activities'], list)
    
    def test_dashboard_stats_time_range(self):
        """测试不同时间范围的仪表盘数据"""
        ranges = ['3months', '6months', '1year', 'all']
        for time_range in ranges:
            from app.core.mock_data_manager import get_mock_dashboard_stats
            data = get_mock_dashboard_stats(time_range)
            assert data is not None
            assert 'total_records' in data
    
    def test_weight_data_structure(self):
        """测试体重数据结构"""
        from app.core.mock_data_manager import get_mock_weight_data
        data = get_mock_weight_data()
        assert data is not None
        assert 'dates' in data
        assert 'weights' in data
        assert isinstance(data['dates'], list)
        assert isinstance(data['weights'], list)
        assert len(data['dates']) == len(data['weights'])
    
    def test_bp_data_structure(self):
        """测试血压数据结构"""
        from app.core.mock_data_manager import get_mock_bp_data
        data = get_mock_bp_data()
        assert data is not None
        assert 'dates' in data
        assert 'systolic' in data
        assert 'diastolic' in data
        assert isinstance(data['dates'], list)
        assert isinstance(data['systolic'], list)
        assert isinstance(data['diastolic'], list)
        assert len(data['dates']) == len(data['systolic']) == len(data['diastolic'])
    
    def test_exam_distribution_structure(self):
        """测试检查分布数据结构"""
        from app.core.mock_data_manager import get_mock_exam_dist_data
        data = get_mock_exam_dist_data()
        assert data is not None
        assert 'distribution' in data
        assert isinstance(data['distribution'], list)
        for item in data['distribution']:
            assert 'type' in item
            assert 'count' in item
            assert 'percentage' in item
    
    def test_health_index_structure(self):
        """测试健康指数数据结构"""
        from app.core.mock_data_manager import get_mock_health_index_data
        data = get_mock_health_index_data()
        assert data is not None
        assert 'current_index' in data
        assert 'trend' in data
        assert 'history' in data
        assert isinstance(data['history'], list)
    
    def test_timeline_data_structure(self):
        """测试时间线数据结构"""
        from app.core.mock_data_manager import get_mock_timeline_data
        data = get_mock_timeline_data()
        assert data is not None
        assert 'timeline' in data
        assert isinstance(data['timeline'], list)
        for item in data['timeline']:
            assert 'date' in item
            assert 'type' in item
            assert 'description' in item
    
    def test_service_stats_structure(self):
        """测试服务统计数据结构"""
        from app.core.mock_data_manager import get_mock_service_stats
        data = get_mock_service_stats()
        assert data is not None
        assert 'auth_service' in data
        assert 'database_service' in data
        assert 'session_manager' in data
    
    def test_public_metrics_structure(self):
        """测试公开指标数据结构"""
        from app.core.mock_data_manager import get_mock_public_metrics
        data = get_mock_public_metrics()
        assert data is not None
        assert 'requests_24h' in data
        assert 'active_users_24h' in data
        assert 'avg_response_time' in data
        assert 'error_rate' in data
        assert 'uptime_percentage' in data
    
    def test_system_metrics_structure(self):
        """测试系统指标数据结构"""
        from app.core.mock_data_manager import get_mock_system_metrics
        data = get_mock_system_metrics()
        assert data is not None
        assert 'cpu_percent' in data
        assert 'memory_percent' in data
        assert 'disk_percent' in data
    
    def test_health_status_structure(self):
        """测试健康状态数据结构"""
        from app.core.mock_data_manager import get_mock_health_status
        data = get_mock_health_status()
        assert data is not None
        assert 'status' in data
        assert 'timestamp' in data
        assert 'system' in data
        assert 'application' in data
        assert 'dependencies' in data
    
    def test_disabled_mock_data(self):
        """测试禁用模拟数据时的行为"""
        with patch.dict(os.environ, {
            'ENABLE_MOCK_DATA': 'false',
            'DEVELOPMENT_MODE': 'false',
            'TESTING': 'false'
        }):
            # 创建新的管理器实例来测试禁用模式
            manager = BackendMockDataManager()
            assert manager.is_enabled() is False
            # 直接测试实例方法
            assert manager.generate_mock_dashboard_stats() is None
            assert manager.generate_mock_weight_data() is None
            assert manager.generate_mock_service_stats() is None
    
    def test_module_level_functions(self):
        """测试模块级别的便捷函数"""
        from app.core.mock_data_manager import (
            get_mock_dashboard_stats,
            get_mock_weight_data,
            get_mock_service_stats
        )
        
        # 这些函数应该返回数据或None（取决于配置）
        dashboard_data = get_mock_dashboard_stats()
        weight_data = get_mock_weight_data()
        service_data = get_mock_service_stats()
        
        # 如果启用了模拟数据，应该返回有效数据
        if is_mock_enabled():
            assert dashboard_data is not None
            assert weight_data is not None
            assert service_data is not None


class TestMockDataIntegration:
    """集成测试"""
    
    def test_data_consistency(self):
        """测试数据一致性"""
        manager = BackendMockDataManager()
        
        # 多次调用应该返回结构一致的数据
        from app.core.mock_data_manager import get_mock_dashboard_stats
        data1 = get_mock_dashboard_stats()
        data2 = get_mock_dashboard_stats()
        
        if data1 and data2:
            assert set(data1.keys()) == set(data2.keys())
    
    def test_environment_variable_override(self):
        """测试环境变量覆盖"""
        # 测试禁用模拟数据
        with patch.dict(os.environ, {
            'ENABLE_MOCK_DATA': 'false',
            'DEVELOPMENT_MODE': 'false',
            'TESTING': 'false'
        }):
            manager = BackendMockDataManager()
            # 所有数据都应该被禁用
            from app.core.mock_data_manager import get_mock_dashboard_stats, get_mock_service_stats
            assert get_mock_dashboard_stats() is None
            assert get_mock_service_stats() is None


class TestMockDataManagerExtended:
    """扩展的模拟数据管理器测试"""
    
    def test_error_handling(self):
        """测试错误处理"""
        manager = BackendMockDataManager()
        
        # 测试无效的时间范围
        from app.core.mock_data_manager import get_mock_dashboard_stats
        data = get_mock_dashboard_stats('invalid_range')
        assert data is not None  # 应该返回默认数据
    
    def test_data_types_validation(self):
        """测试数据类型验证"""
        manager = BackendMockDataManager()
        
        # 测试仪表盘数据类型
        from app.core.mock_data_manager import get_mock_dashboard_stats
        dashboard_data = get_mock_dashboard_stats()
        if dashboard_data:
            assert isinstance(dashboard_data['total_records'], int)
            assert isinstance(dashboard_data['abnormal_records'], int)
            assert isinstance(dashboard_data['health_index'], (int, float))
            assert isinstance(dashboard_data['recent_activities'], list)
    
    def test_performance(self):
        """测试性能"""
        import time
        manager = BackendMockDataManager()
        
        # 测试数据生成性能
        from app.core.mock_data_manager import get_mock_dashboard_stats
        start_time = time.time()
        for _ in range(100):
            get_mock_dashboard_stats()
        end_time = time.time()
        
        # 100次调用应该在1秒内完成
        assert (end_time - start_time) < 1.0
    
    def test_memory_usage(self):
        """测试内存使用"""
        import sys
        manager = BackendMockDataManager()
        
        # 获取初始内存使用
        initial_size = sys.getsizeof(manager)
        
        # 多次调用不应该显著增加内存使用
        from app.core.mock_data_manager import get_mock_dashboard_stats
        for _ in range(1000):
            get_mock_dashboard_stats()
        
        final_size = sys.getsizeof(manager)
        # 内存增长应该很小
        assert final_size - initial_size < 1000  # 小于1KB
    
    def test_concurrent_access(self):
        """测试并发访问"""
        import threading
        import time
        
        manager = BackendMockDataManager()
        results = []
        errors = []
        
        def worker():
            try:
                from app.core.mock_data_manager import get_mock_dashboard_stats
                data = get_mock_dashboard_stats()
                results.append(data)
            except Exception as e:
                errors.append(e)
        
        # 创建多个线程并发访问
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=worker)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 不应该有错误
        assert len(errors) == 0
        # 应该有结果
        assert len(results) == 10
    
    def test_configuration_edge_cases(self):
        """测试配置边界情况"""
        # 测试空字符串配置
        with patch.dict(os.environ, {
            'ENABLE_MOCK_DATA': '',
            'DEVELOPMENT_MODE': 'false',
            'TESTING': 'false'
        }):
            manager = BackendMockDataManager()
            # 空字符串应该被视为False
            assert manager.is_enabled() is False
        
        # 测试大小写不敏感
        with patch.dict(os.environ, {'ENABLE_MOCK_DATA': 'TRUE'}):
            manager = BackendMockDataManager()
            assert manager.is_enabled() is True
        
        with patch.dict(os.environ, {
            'ENABLE_MOCK_DATA': 'False',
            'DEVELOPMENT_MODE': 'false',
            'TESTING': 'false'
        }):
            manager = BackendMockDataManager()
            assert manager.is_enabled() is False


class TestMockDataManagerSecurity:
    """模拟数据管理器安全测试"""
    
    def test_no_sensitive_data_exposure(self):
        """测试不暴露敏感数据"""
        manager = BackendMockDataManager()
        
        # 获取所有模拟数据
        from app.core.mock_data_manager import get_mock_dashboard_stats, get_mock_weight_data, get_mock_service_stats
        dashboard_data = get_mock_dashboard_stats()
        weight_data = get_mock_weight_data()
        service_data = get_mock_service_stats()
        
        # 检查是否包含敏感信息
        sensitive_keywords = ['password', 'secret', 'key', 'token', 'credential']
        
        for data in [dashboard_data, weight_data, service_data]:
            if data:
                data_str = str(data).lower()
                for keyword in sensitive_keywords:
                    assert keyword not in data_str, f"发现敏感信息: {keyword}"
    
    def test_input_validation(self):
        """测试输入验证"""
        manager = BackendMockDataManager()
        
        # 测试SQL注入尝试
        malicious_inputs = [
            "'; DROP TABLE users; --",
            "<script>alert('xss')</script>",
            "../../../etc/passwd",
            "null",
            "undefined"
        ]
        
        for malicious_input in malicious_inputs:
            try:
                # 这些输入不应该导致异常或安全问题
                from app.core.mock_data_manager import get_mock_dashboard_stats
                data = get_mock_dashboard_stats(malicious_input)
                # 应该返回安全的默认数据
                assert data is not None
            except Exception as e:
                # 如果抛出异常，应该是预期的验证错误
                assert "validation" in str(e).lower() or "invalid" in str(e).lower()


if __name__ == '__main__':
    pytest.main([__file__])