import sqlite3

def fix_custom_id_data():
    """修正评估数据的custom_id，从SM_006改为SM_008"""
    
    conn = sqlite3.connect('YUN/backend/app.db')
    cursor = conn.cursor()
    
    print("=== 修正前的数据 ===")
    cursor.execute('SELECT id, name, custom_id, status FROM assessments ORDER BY id')
    assessments = cursor.fetchall()
    for a in assessments:
        print(f'ID={a[0]}, 名称={a[1]}, custom_id="{a[2]}", 状态={a[3]}')
    
    print("\n=== 开始修正custom_id ===")
    # 将所有SM_006的评估改为SM_008
    cursor.execute('UPDATE assessments SET custom_id = ? WHERE custom_id = ?', ('SM_008', 'SM_006'))
    affected_rows = cursor.rowcount
    print(f'已更新 {affected_rows} 条记录')
    
    # 提交更改
    conn.commit()
    
    print("\n=== 修正后的数据 ===")
    cursor.execute('SELECT id, name, custom_id, status FROM assessments ORDER BY id')
    assessments = cursor.fetchall()
    for a in assessments:
        print(f'ID={a[0]}, 名称={a[1]}, custom_id="{a[2]}", 状态={a[3]}')
    
    print("\n=== 验证SM_008的评估数量 ===")
    cursor.execute('SELECT COUNT(*) FROM assessments WHERE custom_id = ?', ('SM_008',))
    count = cursor.fetchone()[0]
    print(f'custom_id="SM_008"的评估数量: {count}')
    
    # 按状态统计
    cursor.execute('SELECT status, COUNT(*) FROM assessments WHERE custom_id = ? GROUP BY status', ('SM_008',))
    status_counts = cursor.fetchall()
    for status, count in status_counts:
        print(f'  {status}状态: {count}个')
    
    conn.close()
    print("\n✅ 数据修正完成！")

if __name__ == '__main__':
    fix_custom_id_data()