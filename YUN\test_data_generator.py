#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据生成器
自动生成各种类型的测试数据，支持数据库、API、前端测试
@version 1.0.0
<AUTHOR> Management System
@date 2024-01-15
"""

import json
import random
import string
import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from faker import Faker
import sqlite3
import os
import uuid

# 初始化Faker
fake = Faker('zh_CN')

@dataclass
class TestUser:
    """测试用户数据模型"""
    id: Optional[int] = None
    username: str = ""
    email: str = ""
    password: str = ""
    phone: str = ""
    name: str = ""
    age: int = 0
    gender: str = ""
    created_at: str = ""
    is_active: bool = True
    role: str = "user"

@dataclass
class TestAssessment:
    """测试评估数据模型"""
    id: Optional[int] = None
    user_id: int = 0
    template_id: str = ""
    title: str = ""
    status: str = "pending"
    score: float = 0.0
    answers: Dict[str, Any] = None
    created_at: str = ""
    completed_at: Optional[str] = None
    
    def __post_init__(self):
        if self.answers is None:
            self.answers = {}

@dataclass
class TestQuestionnaire:
    """测试问卷数据模型"""
    id: str = ""
    title: str = ""
    description: str = ""
    questions: List[Dict[str, Any]] = None
    scoring_rules: Dict[str, Any] = None
    category: str = ""
    version: str = "1.0"
    is_active: bool = True
    
    def __post_init__(self):
        if self.questions is None:
            self.questions = []
        if self.scoring_rules is None:
            self.scoring_rules = {}

class TestDataGenerator:
    """测试数据生成器主类"""
    
    def __init__(self, db_path: str = None):
        self.db_path = db_path or "test_data.db"
        self.fake = fake
        self.templates = {
            "SDS": "抑郁自评量表",
            "SAS": "焦虑自评量表",
            "MMSE": "简易精神状态检查",
            "SM008": "健康评估问卷"
        }
        
    def generate_random_string(self, length: int = 10) -> str:
        """生成随机字符串"""
        return ''.join(random.choices(string.ascii_letters + string.digits, k=length))
    
    def generate_random_email(self) -> str:
        """生成随机邮箱"""
        domains = ['gmail.com', 'qq.com', '163.com', 'sina.com', 'outlook.com']
        username = self.generate_random_string(8).lower()
        domain = random.choice(domains)
        return f"{username}@{domain}"
    
    def generate_phone_number(self) -> str:
        """生成手机号码"""
        prefixes = ['130', '131', '132', '133', '134', '135', '136', '137', '138', '139',
                   '150', '151', '152', '153', '155', '156', '157', '158', '159',
                   '180', '181', '182', '183', '184', '185', '186', '187', '188', '189']
        prefix = random.choice(prefixes)
        suffix = ''.join([str(random.randint(0, 9)) for _ in range(8)])
        return prefix + suffix
    
    def generate_test_user(self, **kwargs) -> TestUser:
        """生成测试用户数据"""
        defaults = {
            'username': self.fake.user_name(),
            'email': self.generate_random_email(),
            'password': 'test123456',
            'phone': self.generate_phone_number(),
            'name': self.fake.name(),
            'age': random.randint(18, 80),
            'gender': random.choice(['male', 'female']),
            'created_at': self.fake.date_time_between(start_date='-1y', end_date='now').isoformat(),
            'is_active': random.choice([True, True, True, False]),  # 75% 概率为活跃用户
            'role': random.choice(['user', 'user', 'user', 'admin'])  # 75% 概率为普通用户
        }
        defaults.update(kwargs)
        return TestUser(**defaults)
    
    def generate_test_assessment(self, user_id: int = None, **kwargs) -> TestAssessment:
        """生成测试评估数据"""
        template_id = random.choice(list(self.templates.keys()))
        status_choices = ['pending', 'in_progress', 'completed', 'cancelled']
        status = random.choice(status_choices)
        
        # 根据状态生成相应的数据
        score = 0.0
        completed_at = None
        answers = {}
        
        if status == 'completed':
            score = round(random.uniform(20, 100), 2)
            completed_at = self.fake.date_time_between(start_date='-30d', end_date='now').isoformat()
            answers = self.generate_assessment_answers(template_id)
        elif status == 'in_progress':
            answers = self.generate_partial_answers(template_id)
        
        defaults = {
            'user_id': user_id or random.randint(1, 100),
            'template_id': template_id,
            'title': self.templates[template_id],
            'status': status,
            'score': score,
            'answers': answers,
            'created_at': self.fake.date_time_between(start_date='-60d', end_date='now').isoformat(),
            'completed_at': completed_at
        }
        defaults.update(kwargs)
        return TestAssessment(**defaults)
    
    def generate_assessment_answers(self, template_id: str) -> Dict[str, Any]:
        """生成评估答案数据"""
        answers = {}
        
        if template_id == "SDS":
            # 抑郁自评量表 - 20题，1-4分
            for i in range(1, 21):
                answers[f"q{i}"] = random.randint(1, 4)
        elif template_id == "SAS":
            # 焦虑自评量表 - 20题，1-4分
            for i in range(1, 21):
                answers[f"q{i}"] = random.randint(1, 4)
        elif template_id == "MMSE":
            # 简易精神状态检查 - 30分制
            sections = {
                'orientation_time': random.randint(0, 5),
                'orientation_place': random.randint(0, 5),
                'registration': random.randint(0, 3),
                'attention': random.randint(0, 5),
                'recall': random.randint(0, 3),
                'language': random.randint(0, 9)
            }
            answers.update(sections)
        elif template_id == "SM008":
            # 健康评估问卷 - 综合评估
            for i in range(1, 31):
                answers[f"q{i}"] = random.choice(['A', 'B', 'C', 'D'])
        
        return answers
    
    def generate_partial_answers(self, template_id: str) -> Dict[str, Any]:
        """生成部分答案数据（用于进行中的评估）"""
        full_answers = self.generate_assessment_answers(template_id)
        # 随机保留50%-80%的答案
        keep_ratio = random.uniform(0.5, 0.8)
        keys_to_keep = random.sample(list(full_answers.keys()), 
                                   int(len(full_answers) * keep_ratio))
        return {k: full_answers[k] for k in keys_to_keep}
    
    def generate_test_questionnaire(self, template_id: str = None, **kwargs) -> TestQuestionnaire:
        """生成测试问卷数据"""
        if not template_id:
            template_id = random.choice(list(self.templates.keys()))
        
        questions = self.generate_questionnaire_questions(template_id)
        scoring_rules = self.generate_scoring_rules(template_id)
        
        defaults = {
            'id': template_id,
            'title': self.templates[template_id],
            'description': f"{self.templates[template_id]}的详细描述和使用说明",
            'questions': questions,
            'scoring_rules': scoring_rules,
            'category': self.get_questionnaire_category(template_id),
            'version': f"{random.randint(1, 3)}.{random.randint(0, 9)}",
            'is_active': True
        }
        defaults.update(kwargs)
        return TestQuestionnaire(**defaults)
    
    def generate_questionnaire_questions(self, template_id: str) -> List[Dict[str, Any]]:
        """生成问卷题目"""
        questions = []
        
        if template_id == "SDS":
            sds_questions = [
                "我感到情绪沮丧，郁闷",
                "我感到早晨心情最好",
                "我要哭或想哭",
                "我夜间睡眠不好",
                "我吃饭象平时一样多"
            ]
            for i, text in enumerate(sds_questions[:5], 1):  # 示例前5题
                questions.append({
                    'id': f"q{i}",
                    'text': text,
                    'type': 'single_choice',
                    'options': [
                        {'value': 1, 'text': '没有或很少时间'},
                        {'value': 2, 'text': '小部分时间'},
                        {'value': 3, 'text': '相当多时间'},
                        {'value': 4, 'text': '绝大部分或全部时间'}
                    ],
                    'required': True
                })
        
        elif template_id == "MMSE":
            mmse_sections = [
                {'section': '时间定向力', 'questions': 5},
                {'section': '地点定向力', 'questions': 5},
                {'section': '即刻记忆', 'questions': 3},
                {'section': '注意力和计算力', 'questions': 5},
                {'section': '延迟回忆', 'questions': 3},
                {'section': '语言能力', 'questions': 9}
            ]
            
            q_id = 1
            for section in mmse_sections:
                for i in range(section['questions']):
                    questions.append({
                        'id': f"q{q_id}",
                        'text': f"{section['section']} - 题目 {i+1}",
                        'type': 'single_choice',
                        'section': section['section'],
                        'options': [
                            {'value': 0, 'text': '错误'},
                            {'value': 1, 'text': '正确'}
                        ],
                        'required': True
                    })
                    q_id += 1
        
        return questions
    
    def generate_scoring_rules(self, template_id: str) -> Dict[str, Any]:
        """生成评分规则"""
        if template_id == "SDS":
            return {
                'type': 'sum',
                'total_score': 80,
                'interpretation': {
                    'normal': {'min': 0, 'max': 52, 'description': '正常范围'},
                    'mild': {'min': 53, 'max': 62, 'description': '轻度抑郁'},
                    'moderate': {'min': 63, 'max': 72, 'description': '中度抑郁'},
                    'severe': {'min': 73, 'max': 80, 'description': '重度抑郁'}
                }
            }
        elif template_id == "MMSE":
            return {
                'type': 'sum',
                'total_score': 30,
                'interpretation': {
                    'normal': {'min': 27, 'max': 30, 'description': '正常'},
                    'mild': {'min': 21, 'max': 26, 'description': '轻度认知障碍'},
                    'moderate': {'min': 10, 'max': 20, 'description': '中度认知障碍'},
                    'severe': {'min': 0, 'max': 9, 'description': '重度认知障碍'}
                }
            }
        
        return {'type': 'sum', 'total_score': 100}
    
    def get_questionnaire_category(self, template_id: str) -> str:
        """获取问卷分类"""
        categories = {
            'SDS': '心理健康',
            'SAS': '心理健康',
            'MMSE': '认知评估',
            'SM008': '综合健康'
        }
        return categories.get(template_id, '其他')
    
    def generate_batch_users(self, count: int = 50) -> List[TestUser]:
        """批量生成用户数据"""
        users = []
        for i in range(count):
            user = self.generate_test_user()
            user.id = i + 1
            users.append(user)
        return users
    
    def generate_batch_assessments(self, user_count: int = 50, 
                                 assessments_per_user: int = 5) -> List[TestAssessment]:
        """批量生成评估数据"""
        assessments = []
        assessment_id = 1
        
        for user_id in range(1, user_count + 1):
            # 每个用户生成随机数量的评估
            count = random.randint(1, assessments_per_user)
            for _ in range(count):
                assessment = self.generate_test_assessment(user_id=user_id)
                assessment.id = assessment_id
                assessments.append(assessment)
                assessment_id += 1
        
        return assessments
    
    def generate_batch_questionnaires(self) -> List[TestQuestionnaire]:
        """批量生成问卷数据"""
        questionnaires = []
        for template_id in self.templates.keys():
            questionnaire = self.generate_test_questionnaire(template_id)
            questionnaires.append(questionnaire)
        return questionnaires
    
    def save_to_json(self, data: Any, filename: str) -> str:
        """保存数据到JSON文件"""
        filepath = os.path.join(os.path.dirname(__file__), 'test_data', filename)
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        # 转换dataclass为字典
        if isinstance(data, list):
            json_data = [asdict(item) if hasattr(item, '__dataclass_fields__') else item for item in data]
        else:
            json_data = asdict(data) if hasattr(data, '__dataclass_fields__') else data
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2, default=str)
        
        return filepath
    
    def create_test_database(self) -> str:
        """创建测试数据库"""
        db_path = os.path.join(os.path.dirname(__file__), 'test_data', self.db_path)
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建用户表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                phone TEXT,
                name TEXT,
                age INTEGER,
                gender TEXT,
                created_at TEXT,
                is_active BOOLEAN DEFAULT 1,
                role TEXT DEFAULT 'user'
            )
        ''')
        
        # 创建评估表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_assessments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                template_id TEXT,
                title TEXT,
                status TEXT,
                score REAL,
                answers TEXT,
                created_at TEXT,
                completed_at TEXT,
                FOREIGN KEY (user_id) REFERENCES test_users (id)
            )
        ''')
        
        # 创建问卷表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_questionnaires (
                id TEXT PRIMARY KEY,
                title TEXT,
                description TEXT,
                questions TEXT,
                scoring_rules TEXT,
                category TEXT,
                version TEXT,
                is_active BOOLEAN DEFAULT 1
            )
        ''')
        
        conn.commit()
        conn.close()
        
        return db_path
    
    def populate_test_database(self, user_count: int = 50, 
                             assessments_per_user: int = 5) -> Dict[str, int]:
        """填充测试数据库"""
        db_path = self.create_test_database()
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 生成并插入用户数据
        users = self.generate_batch_users(user_count)
        for user in users:
            cursor.execute('''
                INSERT INTO test_users 
                (username, email, password, phone, name, age, gender, created_at, is_active, role)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                user.username, user.email, user.password, user.phone,
                user.name, user.age, user.gender, user.created_at,
                user.is_active, user.role
            ))
        
        # 生成并插入评估数据
        assessments = self.generate_batch_assessments(user_count, assessments_per_user)
        for assessment in assessments:
            cursor.execute('''
                INSERT INTO test_assessments 
                (user_id, template_id, title, status, score, answers, created_at, completed_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                assessment.user_id, assessment.template_id, assessment.title,
                assessment.status, assessment.score, json.dumps(assessment.answers),
                assessment.created_at, assessment.completed_at
            ))
        
        # 生成并插入问卷数据
        questionnaires = self.generate_batch_questionnaires()
        for questionnaire in questionnaires:
            cursor.execute('''
                INSERT INTO test_questionnaires 
                (id, title, description, questions, scoring_rules, category, version, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                questionnaire.id, questionnaire.title, questionnaire.description,
                json.dumps(questionnaire.questions), json.dumps(questionnaire.scoring_rules),
                questionnaire.category, questionnaire.version, questionnaire.is_active
            ))
        
        conn.commit()
        conn.close()
        
        return {
            'users': len(users),
            'assessments': len(assessments),
            'questionnaires': len(questionnaires),
            'database_path': db_path
        }
    
    def generate_api_test_data(self) -> Dict[str, Any]:
        """生成API测试数据"""
        return {
            'login_data': {
                'valid_user': {
                    'username': 'testuser',
                    'password': 'test123456'
                },
                'invalid_user': {
                    'username': 'invaliduser',
                    'password': 'wrongpassword'
                }
            },
            'assessment_data': {
                'create_assessment': {
                    'template_id': 'SDS',
                    'title': '抑郁自评量表测试'
                },
                'submit_answers': {
                    'assessment_id': 1,
                    'answers': self.generate_assessment_answers('SDS')
                }
            },
            'user_data': {
                'register_user': {
                    'username': f'newuser_{uuid.uuid4().hex[:8]}',
                    'email': self.generate_random_email(),
                    'password': 'newpassword123',
                    'name': self.fake.name()
                }
            }
        }
    
    def export_all_test_data(self) -> Dict[str, str]:
        """导出所有测试数据"""
        results = {}
        
        # 生成并保存各类测试数据
        users = self.generate_batch_users(50)
        results['users_json'] = self.save_to_json(users, 'test_users.json')
        
        assessments = self.generate_batch_assessments(50, 5)
        results['assessments_json'] = self.save_to_json(assessments, 'test_assessments.json')
        
        questionnaires = self.generate_batch_questionnaires()
        results['questionnaires_json'] = self.save_to_json(questionnaires, 'test_questionnaires.json')
        
        api_data = self.generate_api_test_data()
        results['api_data_json'] = self.save_to_json(api_data, 'api_test_data.json')
        
        # 创建数据库
        db_stats = self.populate_test_database(50, 5)
        results['database'] = db_stats['database_path']
        results['database_stats'] = db_stats
        
        return results

def main():
    """主函数 - 生成测试数据"""
    print("开始生成测试数据...")
    
    generator = TestDataGenerator()
    results = generator.export_all_test_data()
    
    print("\n测试数据生成完成！")
    print("生成的文件:")
    for key, value in results.items():
        if key != 'database_stats':
            print(f"  {key}: {value}")
    
    if 'database_stats' in results:
        stats = results['database_stats']
        print(f"\n数据库统计:")
        print(f"  用户数量: {stats['users']}")
        print(f"  评估数量: {stats['assessments']}")
        print(f"  问卷数量: {stats['questionnaires']}")
    
    print("\n测试数据已准备就绪，可用于自动化测试！")

if __name__ == "__main__":
    main()