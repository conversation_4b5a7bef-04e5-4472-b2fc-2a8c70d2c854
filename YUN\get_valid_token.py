import requests
import json

def get_valid_token():
    """获取有效的认证token"""
    base_url = "http://localhost:8006"
    
    # 尝试登录获取token
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        # 登录请求 - 使用form-data格式
        response = requests.post(f"{base_url}/api/auth/login", data=login_data, timeout=10)
        print(f"登录状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            token = data.get('access_token')
            print(f"获取到token: {token[:50]}...")
            return token
        else:
            print(f"登录失败: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"登录请求异常: {str(e)}")
        return None

def test_api_with_valid_token(token):
    """使用有效token测试API"""
    base_url = "http://localhost:8006"
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 测试端点列表
    endpoints = [
        "/api/templates/assessment-templates",
        "/api/clinical-scales/standard-assessments", 
        "/api/clinical-scales/standard-questionnaires"
    ]
    
    print("\n开始测试修复后的API端点...\n")
    
    for endpoint in endpoints:
        print(f"测试端点: {endpoint}")
        try:
            response = requests.get(f"{base_url}{endpoint}", headers=headers, timeout=10)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"响应成功! 数据类型: {type(data)}")
                if isinstance(data, dict):
                    if 'data' in data:
                        print(f"数据记录数: {len(data.get('data', []))}")
                    if 'total' in data:
                        print(f"总记录数: {data.get('total')}")
                print(f"响应内容: {json.dumps(data, ensure_ascii=False, indent=2)[:300]}...")
            else:
                print(f"响应失败: {response.text[:200]}...")
                
        except requests.exceptions.RequestException as e:
            print(f"请求异常: {str(e)}")
        
        print("-" * 50)

if __name__ == "__main__":
    # 获取有效token
    token = get_valid_token()
    
    if token:
        # 使用有效token测试API
        test_api_with_valid_token(token)
    else:
        print("无法获取有效token，测试终止")