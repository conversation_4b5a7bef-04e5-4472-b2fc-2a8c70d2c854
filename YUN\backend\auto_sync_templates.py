#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os
import sys
from pathlib import Path
from datetime import datetime

def extract_template_from_file(file_path):
    """从文件中提取模板信息"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        if '_TEMPLATE = {' not in content:
            return None
        lines = content.split('\n')
        in_template = False
        template_data = {}
        for line in lines:
            line = line.strip()
            if '_TEMPLATE = {' in line:
                in_template = True
                continue
            if in_template:
                # 提取所有标准字段
                for field in ['template_key', 'name', 'name_en', 'version', 'description', 'instructions', 'category', 'type', 'status']:
                    if f'"{field}"' in line and ':' in line:
                        value_part = line.split(':', 1)[1].strip()
                        if value_part.startswith('"'):
                            value_part = value_part[1:]
                        if '"' in value_part:
                            value_part = value_part.split('"')[0]
                        template_data[field] = value_part.strip()
                # 提取questionnaire_type或assessment_type
                if 'questionnaire_type' in line and ':' in line:
                    type_part = line.split(':', 1)[1].strip()
                    if 'QuestionnaireType.' in type_part:
                        type_value = type_part.split('QuestionnaireType.')[1].split(',')[0].strip()
                        template_data['questionnaire_type'] = type_value.lower()
                elif 'assessment_type' in line and ':' in line:
                    type_part = line.split(':', 1)[1].strip()
                    if 'AssessmentType.' in type_part:
                        type_value = type_part.split('AssessmentType.')[1].split(',')[0].strip()
                        template_data['assessment_type'] = type_value.upper()  # 保持与数据库一致
                # 模板结束
                if line == '}' or (line.startswith('}') and not line.endswith(',')):
                    break
        return template_data if template_data else None
    except Exception as e:
        print(f"解析文件 {file_path} 时出错: {e}")
        return None


def sync_templates_to_database():
    """同步模板到数据库（量表和问卷）"""
    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()
    # 1. 同步 questionnaire_templates
    cursor.execute('SELECT template_key FROM questionnaire_templates')
    existing_q_keys = {row[0] for row in cursor.fetchall()}
    # 2. 同步 assessment_templates
    cursor.execute('SELECT template_key FROM assessment_templates')
    existing_a_keys = {row[0] for row in cursor.fetchall()}
    print(f"数据库中现有的questionnaire template_key: {sorted(existing_q_keys)}")
    print(f"数据库中现有的assessment template_key: {sorted(existing_a_keys)}")
    clinical_scales_path = Path('app/clinical_scales')
    template_files = []
    for folder in ['assessment', 'questionnaire']:
        folder_path = clinical_scales_path / folder
        if folder_path.exists():
            for file in folder_path.glob('*.py'):
                if file.name != '__init__.py':
                    template_files.append((folder, file))
    print(f"\n找到 {len(template_files)} 个模板文件")
    added_q = updated_q = added_a = updated_a = 0
    for folder, file_path in template_files:
        print(f"\n处理文件: {file_path.name}")
        template_data = extract_template_from_file(file_path)
        if not template_data or 'template_key' not in template_data:
            print(f"  ❌ 无法提取模板信息")
            continue
        template_key = template_data['template_key']
        if folder == 'questionnaire':
            if template_key in existing_q_keys:
                cursor.execute(
                    'SELECT description, instructions, questionnaire_type, status FROM questionnaire_templates WHERE template_key = ?',
                    (template_key,)
                )
                result = cursor.fetchone()
                if result:
                    db_desc, db_inst, db_qtype, db_status = result
                    file_desc = template_data.get('description', '')
                    file_inst = template_data.get('instructions', '')
                    file_qtype = template_data.get('questionnaire_type', '')
                    file_status = template_data.get('status', '')
                    if (not db_desc and file_desc) or (not db_inst and file_inst) or (not db_qtype and file_qtype) or (not db_status and file_status):
                        cursor.execute(
                            'UPDATE questionnaire_templates SET description = ?, instructions = ?, questionnaire_type = ?, status = ?, updated_at = ? WHERE template_key = ?',
                            (file_desc, file_inst, file_qtype, file_status, datetime.now().isoformat(), template_key)
                        )
                        updated_q += 1
                        print(f"  ✅ 更新了questionnaire description、instructions、questionnaire_type、status")
                    else:
                        print(f"  ℹ️ 问卷已存在且无需更新")
            else:
                cursor.execute('SELECT MAX(id) FROM questionnaire_templates')
                max_id = cursor.fetchone()[0] or 0
                new_id = max_id + 1
                cursor.execute(
                    '''INSERT INTO questionnaire_templates 
                       (id, template_key, name, version, description, instructions, questionnaire_type, status, is_active, created_at, updated_at, created_by) 
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                    (
                        new_id,
                        template_key,
                        template_data.get('name', template_key),
                        template_data.get('version', '1.0'),
                        template_data.get('description', ''),
                        template_data.get('instructions', ''),
                        template_data.get('questionnaire_type', 'health'),
                        template_data.get('status', 'pending'),
                        True,  # is_active
                        datetime.now().isoformat(),  # created_at
                        datetime.now().isoformat(),  # updated_at
                        None  # created_by
                    )
                )
                added_q += 1
                print(f"  ✅ 添加新问卷模板 (ID: {new_id})")
        elif folder == 'assessment':
            # 保证assessment_type有值
            assessment_type = template_data.get('assessment_type')
            if not assessment_type:
                assessment_type = 'MENTAL_HEALTH'  # 默认值
            if template_key in existing_a_keys:
                cursor.execute(
                    'SELECT description, instructions, status, assessment_type FROM assessment_templates WHERE template_key = ?',
                    (template_key,)
                )
                result = cursor.fetchone()
                if result:
                    db_desc, db_inst, db_status, db_assessment_type = result
                    file_desc = template_data.get('description', '')
                    file_inst = template_data.get('instructions', '')
                    file_status = template_data.get('status', '')
                    if (not db_desc and file_desc) or (not db_inst and file_inst) or (not db_status and file_status) or (not db_assessment_type and assessment_type):
                        cursor.execute(
                            'UPDATE assessment_templates SET description = ?, instructions = ?, status = ?, assessment_type = ?, updated_at = ? WHERE template_key = ?',
                            (file_desc, file_inst, file_status, assessment_type, datetime.now().isoformat(), template_key)
                        )
                        updated_a += 1
                        print(f"  ✅ 更新了assessment description、instructions、status、assessment_type")
                    else:
                        print(f"  ℹ️ 量表已存在且无需更新")
            else:
                cursor.execute('SELECT MAX(id) FROM assessment_templates')
                max_id = cursor.fetchone()[0] or 0
                new_id = max_id + 1
                cursor.execute(
                    '''INSERT INTO assessment_templates 
                       (id, template_key, name, version, description, instructions, status, assessment_type, is_active, created_at, created_by) 
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                    (
                        new_id,
                        template_key,
                        template_data.get('name', template_key),
                        template_data.get('version', '1.0'),
                        template_data.get('description', ''),
                        template_data.get('instructions', ''),
                        template_data.get('status', 'pending'),
                        assessment_type,
                        True,  # is_active
                        datetime.now().isoformat(),  # created_at
                        None  # created_by
                    )
                )
                added_a += 1
                print(f"  ✅ 添加新量表模板 (ID: {new_id})")
    conn.commit()
    conn.close()
    print(f"\n=== 同步完成 ===")
    print(f"新增问卷模板: {added_q}，更新: {updated_q}")
    print(f"新增量表模板: {added_a}，更新: {updated_a}")
    return added_q, updated_q, added_a, updated_a

if __name__ == '__main__':
    print("开始同步模板到数据库...")
    sync_templates_to_database()
    print("\n同步完成！")