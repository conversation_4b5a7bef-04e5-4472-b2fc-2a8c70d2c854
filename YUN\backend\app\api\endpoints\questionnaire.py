"""调查问卷API

此模块提供了调查问卷相关的API端点，包括问卷的创建、查询、更新和删除，
以及基于规则自动生成问卷的功能。
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Request, status as http_status
from sqlalchemy.orm import Session
from datetime import datetime
from pydantic import BaseModel

from app.api.deps import get_db
from app.core.auth import get_current_active_user_custom
from app.models.user import User
from app.models.questionnaire import QuestionnaireTemplate, QuestionnaireTemplateQuestion
from app.models.enums import QuestionnaireType
from app.clinical_scales.generators.questionnaire_generator import QuestionnaireGenerator

router = APIRouter()

class QuestionnaireQuestion(BaseModel):
    question_id: str
    question_text: str
    question_type: Optional[str] = None
    options: Optional[List[Dict[str, Any]]] = None
    order: Optional[int] = None
    is_required: Optional[bool] = True

class QuestionnaireCreate(BaseModel):
    questionnaire_type: str
    name: str
    version: Optional[str] = "1.0"
    description: Optional[str] = ""
    instructions: Optional[str] = ""
    questions: List[QuestionnaireQuestion]

class QuestionnaireRuleCreate(BaseModel):
    questionnaire_type: str
    name: Optional[str] = None
    description: Optional[str] = None
    instructions: Optional[str] = None
    question_count: Optional[int] = 10
    question_types: Optional[List[str]] = None
    topics: Optional[List[str]] = None
    question_templates: Optional[List[Dict[str, Any]]] = None

@router.get("", response_model=List[dict])
def get_questionnaires(
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
):
    """获取问卷列表（系统预置+当前用户自定义）"""
    # 查询包括标准问卷和用户自定义问卷
    # created_by=1 表示系统预置的标准问卷，current_user.custom_id 表示当前用户创建的问卷
    query = db.query(QuestionnaireTemplate).filter(
        QuestionnaireTemplate.created_by.in_([1, getattr(current_user, 'custom_id', 0)])
    )
    total = query.count()
    questionnaires = query.order_by(QuestionnaireTemplate.created_at.desc()) \
        .offset((page - 1) * page_size).limit(page_size).all()

    result = []
    for q in questionnaires:
        # 问题数量
        question_count = db.query(QuestionnaireTemplateQuestion).filter(
            QuestionnaireTemplateQuestion.template_id == q.id
        ).count()
        
        result.append({
            "id": q.id,
            "name": q.name,
            "description": q.description,
            "questionnaire_type": q.questionnaire_type.name if hasattr(q.questionnaire_type, 'name') else str(q.questionnaire_type),
            "version": q.version,
            "created_at": q.created_at.isoformat() if q.created_at else None,
            "updated_at": q.updated_at.isoformat() if q.updated_at else None,
            "is_system": q.created_by == 1,
            "is_active": q.is_active,
            "question_count": question_count,
        })
    return result

@router.get("/{questionnaire_id}", response_model=dict)
def get_questionnaire(
    questionnaire_id: int,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
):
    """获取调查问卷详情"""
    questionnaire = db.query(QuestionnaireTemplate).filter(QuestionnaireTemplate.id == questionnaire_id).first()
    if not questionnaire:
        raise HTTPException(status_code=http_status.HTTP_404_NOT_FOUND, detail="问卷不存在")
    
    # 获取题目
    questions = db.query(QuestionnaireTemplateQuestion).filter(
        QuestionnaireTemplateQuestion.template_id == questionnaire.id
    ).order_by(QuestionnaireTemplateQuestion.order).all()
    
    questions_data = [
        {
            "id": q.id,
            "question_id": q.question_id,
            "question_text": q.question_text,
            "question_type": q.question_type,
            "options": q.options,
            "order": q.order,
            "is_required": q.is_required,
        } for q in questions
    ]
    
    return {
        "status": "success",
        "data": {
            "id": questionnaire.id,
            "name": questionnaire.title,
            "description": questionnaire.description,
            "questionnaire_type": questionnaire.questionnaire_type.name if hasattr(questionnaire.questionnaire_type, 'name') else str(questionnaire.questionnaire_type),
            "version": getattr(questionnaire, 'version', '1.0') or '1.0',
            "instructions": questionnaire.instructions,
            "created_at": questionnaire.created_at.isoformat() if questionnaire.created_at else None,
            "updated_at": questionnaire.updated_at.isoformat() if questionnaire.updated_at else None,
            "is_system": questionnaire.created_by == 1,
            "is_active": questionnaire.is_active,
            "questions": questions_data
        }
    }

@router.post("", response_model=dict)
def create_questionnaire(
    data: QuestionnaireCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
):
    """创建调查问卷"""
    # 创建问卷模板
    questionnaire = QuestionnaireTemplate(
        questionnaire_type=data.questionnaire_type,
        name=data.name,
        version=data.version,
        description=data.description,
        instructions=data.instructions,
        is_active=True,
        created_by=getattr(current_user, 'custom_id', 1),
        created_at=datetime.now()
    )
    db.add(questionnaire)
    db.flush()  # 获取ID
    
    # 添加问题
    for i, q in enumerate(data.questions):
        question = QuestionnaireTemplateQuestion(
            template_id=questionnaire.id,
            question_id=q.question_id,
            question_text=q.question_text,
            question_type=q.question_type or "text",
            options=q.options or [],
            order=q.order or (i + 1),
            is_required=q.is_required,
            created_at=datetime.now()
        )
        db.add(question)
    
    db.commit()
    db.refresh(questionnaire)
    
    return {
        "status": "success",
        "message": "调查问卷创建成功",
        "data": {
            "id": questionnaire.id,
            "name": questionnaire.title,
            "description": questionnaire.description,
            "questions": [q.dict() for q in data.questions]
        }
    }

@router.post("/generate", response_model=dict)
def generate_questionnaire(
    data: QuestionnaireRuleCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
):
    """基于规则自动生成调查问卷"""
    # 创建生成器
    generator = QuestionnaireGenerator(db)
    
    # 构建规则
    rules = {
        "questionnaire_type": data.questionnaire_type,
        "name": data.name or f"自动生成问卷-{datetime.now().strftime('%Y%m%d%H%M%S')}",
        "description": data.description or "根据规则自动生成的调查问卷",
        "instructions": data.instructions or "请根据实际情况填写以下问题",
        "question_count": data.question_count or 10,
        "question_types": data.question_types or ["single_choice", "multiple_choice", "text"],
        "topics": data.topics or [],
        "question_templates": data.question_templates or []
    }
    
    # 生成问卷
    try:
        questionnaire = generator.generate_questionnaire_from_rules(
            rules=rules,
            created_by=getattr(current_user, 'custom_id', 1)
        )
        
        # 获取问题
        questions = db.query(QuestionnaireTemplateQuestion).filter(
            QuestionnaireTemplateQuestion.template_id == questionnaire.id
        ).order_by(QuestionnaireTemplateQuestion.order).all()
        
        questions_data = [
            {
                "id": q.id,
                "question_id": q.question_id,
                "question_text": q.question_text,
                "question_type": q.question_type,
                "options": q.options,
                "order": q.order,
                "is_required": q.is_required,
            } for q in questions
        ]
        
        return {
            "status": "success",
            "message": "调查问卷生成成功",
            "data": {
                "id": questionnaire.id,
                "name": questionnaire.title,
                "description": questionnaire.description,
                "questionnaire_type": questionnaire.questionnaire_type.name if hasattr(questionnaire.questionnaire_type, 'name') else str(questionnaire.questionnaire_type),
                "version": getattr(questionnaire, 'version', '1.0') or '1.0',
                "instructions": questionnaire.instructions,
                "created_at": questionnaire.created_at.isoformat() if questionnaire.created_at else None,
                "is_active": questionnaire.is_active,
                "questions": questions_data
            }
        }
    except Exception as e:
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"生成问卷失败: {str(e)}"
        )

@router.put("/{questionnaire_id}", response_model=dict)
def update_questionnaire(
    questionnaire_id: int,
    data: QuestionnaireCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
):
    """更新调查问卷"""
    # 获取现有问卷
    questionnaire = db.query(QuestionnaireTemplate).filter(QuestionnaireTemplate.id == questionnaire_id).first()
    if not questionnaire:
        raise HTTPException(status_code=http_status.HTTP_404_NOT_FOUND, detail="问卷不存在")
    
    # 检查权限（系统问卷只能由系统更新，用户问卷只能由创建者更新）
    if questionnaire.created_by == 1 and getattr(current_user, 'custom_id', 0) != 1:
        raise HTTPException(status_code=http_status.HTTP_403_FORBIDDEN, detail="无权更新系统问卷")
    if questionnaire.created_by != 1 and questionnaire.created_by != getattr(current_user, 'custom_id', 0):
        raise HTTPException(status_code=http_status.HTTP_403_FORBIDDEN, detail="无权更新他人创建的问卷")
    
    # 更新问卷信息
    questionnaire.title = data.name
    questionnaire.description = data.description
    questionnaire.instructions = data.instructions
    questionnaire.updated_at = datetime.now()
    
    # 更新版本号
    current_version = float(getattr(questionnaire, 'version', '1.0') or '1.0')
    questionnaire.version = str(current_version + 0.1)
    
    # 删除现有问题
    db.query(QuestionnaireTemplateQuestion).filter(
        QuestionnaireTemplateQuestion.template_id == questionnaire_id
    ).delete()
    
    # 添加新问题
    for i, q in enumerate(data.questions):
        question = QuestionnaireTemplateQuestion(
            template_id=questionnaire.id,
            question_id=q.question_id,
            question_text=q.question_text,
            question_type=q.question_type or "text",
            options=q.options or [],
            order=q.order or (i + 1),
            is_required=q.is_required,
            created_at=datetime.now()
        )
        db.add(question)
    
    db.commit()
    db.refresh(questionnaire)
    
    return {
        "status": "success",
        "message": "调查问卷更新成功",
        "data": {
            "id": questionnaire.id,
            "name": questionnaire.title,
            "description": questionnaire.description,
            "version": getattr(questionnaire, 'version', '1.0') or '1.0',
            "questions": [q.dict() for q in data.questions]
        }
    }

@router.delete("/{questionnaire_id}", response_model=dict)
def delete_questionnaire(
    questionnaire_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
):
    """删除调查问卷（系统预置问卷禁止删除）"""
    # 获取问卷
    questionnaire = db.query(QuestionnaireTemplate).filter(QuestionnaireTemplate.id == questionnaire_id).first()
    if not questionnaire:
        raise HTTPException(status_code=http_status.HTTP_404_NOT_FOUND, detail="问卷不存在")
    
    # 系统问卷禁止删除
    if questionnaire.created_by == 1:
        raise HTTPException(status_code=http_status.HTTP_403_FORBIDDEN, detail="系统预置问卷禁止删除")
    
    # 检查权限（只能删除自己创建的问卷）
    if questionnaire.created_by != getattr(current_user, 'custom_id', 0):
        raise HTTPException(status_code=http_status.HTTP_403_FORBIDDEN, detail="无权删除他人创建的问卷")
    
    # 删除问题
    db.query(QuestionnaireTemplateQuestion).filter(
        QuestionnaireTemplateQuestion.template_id == questionnaire_id
    ).delete()
    
    # 删除问卷
    db.delete(questionnaire)
    db.commit()
    
    return {
        "status": "success",
        "message": "调查问卷删除成功"
    }