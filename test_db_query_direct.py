#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试数据库查询逻辑
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'YUN', 'backend'))

from sqlalchemy import create_engine, desc
from sqlalchemy.orm import sessionmaker
from app.models.assessment import Assessment
from app.models.distribution import AssessmentDistribution

def test_database_query():
    """测试数据库查询逻辑"""
    print("测试数据库查询逻辑...")
    
    # 创建数据库连接
    engine = create_engine('sqlite:///YUN/backend/app.db')
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        custom_id = "SM_006"
        
        # 测试1: 直接查询Assessment表
        print("\n=== 测试1: 直接查询Assessment表 ===")
        direct_query = db.query(Assessment).filter(Assessment.custom_id == custom_id)
        direct_assessments = direct_query.all()
        print(f"直接查询结果数量: {len(direct_assessments)}")
        
        for assessment in direct_assessments:
            print(f"  ID: {assessment.id}, 名称: {assessment.name}, 状态: {assessment.status}, 创建时间: {assessment.created_at}")
        
        # 测试2: 使用outerjoin查询（模拟API逻辑）
        print("\n=== 测试2: 使用outerjoin查询 ===")
        join_query = db.query(Assessment).outerjoin(AssessmentDistribution)
        join_query = join_query.filter(Assessment.custom_id == custom_id)
        join_query = join_query.order_by(desc(Assessment.created_at))
        join_assessments = join_query.all()
        print(f"outerjoin查询结果数量: {len(join_assessments)}")
        
        for assessment in join_assessments:
            print(f"  ID: {assessment.id}, 名称: {assessment.name}, 状态: {assessment.status}, 创建时间: {assessment.created_at}")
        
        # 测试3: 检查是否有重复记录
        print("\n=== 测试3: 检查重复记录 ===")
        distinct_query = db.query(Assessment).outerjoin(AssessmentDistribution)
        distinct_query = distinct_query.filter(Assessment.custom_id == custom_id)
        distinct_query = distinct_query.distinct(Assessment.id)
        distinct_query = distinct_query.order_by(desc(Assessment.created_at))
        distinct_assessments = distinct_query.all()
        print(f"去重后查询结果数量: {len(distinct_assessments)}")
        
        for assessment in distinct_assessments:
            print(f"  ID: {assessment.id}, 名称: {assessment.name}, 状态: {assessment.status}, 创建时间: {assessment.created_at}")
        
        # 测试4: 检查AssessmentDistribution表
        print("\n=== 测试4: 检查AssessmentDistribution表 ===")
        # 先找到用户ID
        from app.models.user import User
        user = db.query(User).filter(User.custom_id == custom_id).first()
        if user:
            print(f"找到用户: ID={user.id}, Custom ID={user.custom_id}")
            distribution_query = db.query(AssessmentDistribution).filter(AssessmentDistribution.user_id == user.id)
            distributions = distribution_query.all()
            print(f"分发记录数量: {len(distributions)}")
            
            for dist in distributions:
                print(f"  分发ID: {dist.id}, Assessment ID: {dist.assessment_id}, User ID: {dist.user_id}, 状态: {dist.status}")
        else:
            print(f"未找到custom_id为{custom_id}的用户")
        
        # 测试5: 检查是否有join条件问题
        print("\n=== 测试5: 检查join条件 ===")
        # 查看Assessment表中有AssessmentDistribution记录的
        if user:
            with_dist_query = db.query(Assessment).join(AssessmentDistribution)
            with_dist_query = with_dist_query.filter(Assessment.custom_id == custom_id)
            with_dist_query = with_dist_query.filter(AssessmentDistribution.user_id == user.id)
            with_dist_assessments = with_dist_query.all()
            print(f"有分发记录的量表数量: {len(with_dist_assessments)}")
            
            for assessment in with_dist_assessments:
                print(f"  ID: {assessment.id}, 名称: {assessment.name}, 状态: {assessment.status}")
        else:
            print("无法检查join条件，因为未找到用户")
        
        # 测试6: 查看所有Assessment记录（不限制custom_id）
        print("\n=== 测试6: 查看所有Assessment记录 ===")
        all_query = db.query(Assessment).order_by(desc(Assessment.created_at)).limit(10)
        all_assessments = all_query.all()
        print(f"所有量表数量（前10条）: {len(all_assessments)}")
        
        for assessment in all_assessments:
            print(f"  ID: {assessment.id}, Custom ID: {assessment.custom_id}, 名称: {assessment.name}, 状态: {assessment.status}")
            
    except Exception as e:
        print(f"查询出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    test_database_query()