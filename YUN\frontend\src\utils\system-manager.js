/**
 * 前端系统管理器
 * 与后端统一管理平台集成，提供前端系统状态监控和管理功能
 * 
 * 版本: 1.0
 * 创建时间: 2024-12-30
 */

import axios from 'axios';

export class SystemManager {
  constructor() {
    this.isInitialized = false;
    this.systemStatus = {
      frontend: 'active',
      backend: 'unknown',
      api: 'unknown',
      database: 'unknown'
    };
    this.lastStatusCheck = null;
    this.statusCheckInterval = null;
    this.errorCount = 0;
    this.maxErrors = 5;
  }

  /**
   * 初始化系统管理器
   */
  initialize() {
    if (this.isInitialized) {
      console.warn('系统管理器已经初始化');
      return;
    }

    console.log('🚀 初始化前端系统管理器...');
    
    // 记录前端启动时间
    this.startTime = new Date();
    
    // 初始化系统状态
    this.systemStatus.frontend = 'active';
    this.lastStatusCheck = new Date();
    
    // 启动定期状态检查
    this.startStatusMonitoring();
    
    // 监听页面可见性变化
    this.setupVisibilityChangeHandler();
    
    // 监听网络状态变化
    this.setupNetworkStatusHandler();
    
    this.isInitialized = true;
    console.log('✅ 前端系统管理器初始化完成');
  }

  /**
   * 启动状态监控
   */
  startStatusMonitoring() {
    // 每分钟检查一次系统状态
    this.statusCheckInterval = setInterval(() => {
      this.checkSystemStatus();
    }, 60000);
    
    // 立即执行一次检查
    this.checkSystemStatus();
  }

  /**
   * 检查系统状态
   */
  async checkSystemStatus() {
    try {
      // 检查后端API状态
      const response = await axios.get('/api/management/status', {
        timeout: 5000
      });
      
      if (response.status === 200) {
        this.systemStatus.backend = 'active';
        this.systemStatus.api = 'active';
        
        // 更新详细状态信息
        if (response.data.components) {
          this.systemStatus.database = response.data.components.database || 'unknown';
        }
        
        this.errorCount = 0; // 重置错误计数
      }
      
      this.lastStatusCheck = new Date();
      
    } catch (error) {
      console.warn('系统状态检查失败:', error.message);
      
      this.systemStatus.backend = 'error';
      this.systemStatus.api = 'error';
      this.errorCount++;
      
      // 如果错误次数过多，可能需要采取措施
      if (this.errorCount >= this.maxErrors) {
        console.error('系统状态检查连续失败，可能存在严重问题');
        this.handleSystemError();
      }
    }
  }

  /**
   * 处理系统错误
   */
  handleSystemError() {
    // 可以在这里添加错误处理逻辑
    // 比如显示错误提示、尝试重连等
    console.error('🚨 系统出现严重错误，请检查后端服务状态');
    
    // 发送错误事件
    window.dispatchEvent(new CustomEvent('systemError', {
      detail: {
        message: '系统连接异常',
        errorCount: this.errorCount,
        lastCheck: this.lastStatusCheck
      }
    }));
  }

  /**
   * 设置页面可见性变化处理
   */
  setupVisibilityChangeHandler() {
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible') {
        // 页面变为可见时，立即检查系统状态
        console.log('页面变为可见，检查系统状态');
        this.checkSystemStatus();
      }
    });
  }

  /**
   * 设置网络状态变化处理
   */
  setupNetworkStatusHandler() {
    window.addEventListener('online', () => {
      console.log('网络连接恢复，检查系统状态');
      this.checkSystemStatus();
    });
    
    window.addEventListener('offline', () => {
      console.warn('网络连接断开');
      this.systemStatus.backend = 'offline';
      this.systemStatus.api = 'offline';
    });
  }

  /**
   * 获取系统状态
   */
  async getSystemStatus() {
    try {
      // 获取最新的后端状态
      const response = await axios.get('/api/management/status', {
        timeout: 3000
      });
      
      return {
        success: true,
        status: {
          ...this.systemStatus,
          backend_details: response.data,
          frontend_uptime: this.getUptime(),
          last_check: this.lastStatusCheck,
          error_count: this.errorCount
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        status: {
          ...this.systemStatus,
          frontend_uptime: this.getUptime(),
          last_check: this.lastStatusCheck,
          error_count: this.errorCount
        }
      };
    }
  }

  /**
   * 获取前端运行时间
   */
  getUptime() {
    if (!this.startTime) return 0;
    return Math.floor((new Date() - this.startTime) / 1000);
  }

  /**
   * 获取系统信息
   */
  getSystemInfo() {
    return {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      screen: {
        width: screen.width,
        height: screen.height,
        colorDepth: screen.colorDepth
      },
      window: {
        innerWidth: window.innerWidth,
        innerHeight: window.innerHeight
      },
      location: {
        href: window.location.href,
        hostname: window.location.hostname,
        port: window.location.port,
        protocol: window.location.protocol
      }
    };
  }

  /**
   * 发送系统事件到后端
   */
  async sendSystemEvent(eventType, eventData) {
    try {
      await axios.post('/api/management/events', {
        type: eventType,
        data: eventData,
        timestamp: new Date().toISOString(),
        source: 'frontend'
      });
    } catch (error) {
      console.warn('发送系统事件失败:', error.message);
    }
  }

  /**
   * 执行系统诊断
   */
  async runDiagnostic() {
    console.log('🔍 开始系统诊断...');
    
    const diagnostic = {
      timestamp: new Date().toISOString(),
      frontend: {
        status: 'active',
        uptime: this.getUptime(),
        info: this.getSystemInfo()
      },
      backend: null,
      connectivity: null,
      performance: null
    };
    
    try {
      // 测试后端连接
      const startTime = performance.now();
      const response = await axios.get('/api/management/status');
      const endTime = performance.now();
      
      diagnostic.backend = {
        status: 'active',
        response_time: Math.round(endTime - startTime),
        data: response.data
      };
      
      diagnostic.connectivity = 'good';
      
    } catch (error) {
      diagnostic.backend = {
        status: 'error',
        error: error.message
      };
      diagnostic.connectivity = 'poor';
    }
    
    // 性能测试
    diagnostic.performance = {
      memory: this.getMemoryInfo(),
      timing: this.getPerformanceTiming()
    };
    
    console.log('📊 系统诊断完成:', diagnostic);
    return diagnostic;
  }

  /**
   * 获取内存信息
   */
  getMemoryInfo() {
    if ('memory' in performance) {
      return {
        usedJSHeapSize: performance.memory.usedJSHeapSize,
        totalJSHeapSize: performance.memory.totalJSHeapSize,
        jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
      };
    }
    return null;
  }

  /**
   * 获取性能时间信息
   */
  getPerformanceTiming() {
    if ('timing' in performance) {
      const timing = performance.timing;
      return {
        domContentLoaded: timing.domContentLoadedEventEnd - timing.navigationStart,
        loadComplete: timing.loadEventEnd - timing.navigationStart,
        domReady: timing.domComplete - timing.navigationStart
      };
    }
    return null;
  }

  /**
   * 清理资源
   */
  cleanup() {
    console.log('🧹 清理系统管理器资源...');
    
    if (this.statusCheckInterval) {
      clearInterval(this.statusCheckInterval);
      this.statusCheckInterval = null;
    }
    
    // 发送前端关闭事件
    this.sendSystemEvent('frontend_shutdown', {
      uptime: this.getUptime(),
      timestamp: new Date().toISOString()
    });
    
    this.isInitialized = false;
    console.log('✅ 系统管理器资源清理完成');
  }

  /**
   * 重启系统监控
   */
  restart() {
    console.log('🔄 重启系统管理器...');
    this.cleanup();
    setTimeout(() => {
      this.initialize();
    }, 1000);
  }
}

// 导出单例实例
export const systemManager = new SystemManager();

// 全局暴露
if (typeof window !== 'undefined') {
  window.SystemManager = SystemManager;
  window.systemManager = systemManager;
}