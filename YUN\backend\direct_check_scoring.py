# -*- coding: utf-8 -*-

import sqlite3
import os

def direct_check_scoring():
    """直接检查数据库中的计分规则"""
    
    # 数据库文件路径
    db_path = os.path.join(os.path.dirname(__file__), 'app.db')
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("=== 直接检查数据库中的计分规则 ===")
        
        # 检查抑郁自评量表模板
        cursor.execute("""
            SELECT id, name FROM assessment_templates WHERE name LIKE '%抑郁自评%'
        """)
        template = cursor.fetchone()
        
        if template:
            template_id, template_name = template
            print(f"找到模板: {template_name} (ID: {template_id})")
            
            # 检查前几个问题的计分规则
            cursor.execute("""
                SELECT question_id, question_text, scoring
                FROM assessment_template_questions
                WHERE template_id = ?
                ORDER BY question_id
                LIMIT 5
            """, (template_id,))
            questions = cursor.fetchall()
            
            print(f"\n前5个问题的计分规则:")
            for q in questions:
                question_id, question_text, scoring = q
                print(f"\n题目 {question_id}: {question_text[:40]}...")
                if scoring:
                    print(f"  计分规则: {scoring[:100]}...")
                else:
                    print(f"  计分规则: None")
            
            # 统计有计分规则的问题数量
            cursor.execute("""
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN scoring IS NOT NULL AND scoring != '' THEN 1 ELSE 0 END) as with_scoring
                FROM assessment_template_questions
                WHERE template_id = ?
            """, (template_id,))
            total, with_scoring = cursor.fetchone()
            print(f"\n统计结果:")
            print(f"  总问题数: {total}")
            print(f"  有计分规则的问题数: {with_scoring}")
            print(f"  计分覆盖率: {with_scoring/total*100 if total > 0 else 0:.1f}%")
        else:
            print("未找到抑郁自评量表模板")
        
        # 再次运行修复脚本的核心逻辑
        print(f"\n=== 再次尝试修复计分规则 ===")
        
        if template:
            template_id, template_name = template
            
            # 正向计分规则
            forward_scoring = {
                "option_scores": {
                    "1": 1,
                    "2": 2,
                    "3": 3,
                    "4": 4
                }
            }
            
            # 反向计分规则
            reverse_scoring = {
                "option_scores": {
                    "1": 4,
                    "2": 3,
                    "3": 2,
                    "4": 1
                }
            }
            
            # 反向计分的题目
            reverse_questions = {
                'sds_2', 'sds_5', 'sds_6', 'sds_11', 'sds_12', 'sds_14', 'sds_16', 'sds_17', 'sds_18', 'sds_20'
            }
            
            # 获取所有问题
            cursor.execute("""
                SELECT question_id FROM assessment_template_questions
                WHERE template_id = ?
            """, (template_id,))
            all_questions = cursor.fetchall()
            
            import json
            updated_count = 0
            
            for (question_id,) in all_questions:
                if question_id in reverse_questions:
                    scoring_rules = reverse_scoring
                else:
                    scoring_rules = forward_scoring
                
                cursor.execute("""
                    UPDATE assessment_template_questions
                    SET scoring = ?
                    WHERE template_id = ? AND question_id = ?
                """, (json.dumps(scoring_rules), template_id, question_id))
                
                updated_count += 1
            
            conn.commit()
            print(f"更新了 {updated_count} 个问题的计分规则")
            
            # 再次验证
            cursor.execute("""
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN scoring IS NOT NULL AND scoring != '' THEN 1 ELSE 0 END) as with_scoring
                FROM assessment_template_questions
                WHERE template_id = ?
            """, (template_id,))
            total, with_scoring = cursor.fetchone()
            print(f"\n更新后的统计:")
            print(f"  总问题数: {total}")
            print(f"  有计分规则的问题数: {with_scoring}")
            print(f"  计分覆盖率: {with_scoring/total*100 if total > 0 else 0:.1f}%")
        
        conn.close()
        
    except Exception as e:
        print(f"检查时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    direct_check_scoring()