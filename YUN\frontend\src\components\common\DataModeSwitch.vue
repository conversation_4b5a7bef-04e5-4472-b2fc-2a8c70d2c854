<template>
  <div class="data-mode-switch">
    <!-- 当前状态显示 -->
    <div class="current-status">
      <div class="status-header">
        <i :class="currentModeIcon" :style="{ color: currentModeColor }"></i>
        <span class="status-title">数据模式状态</span>
        <el-button 
          type="text" 
          size="mini" 
          @click="refreshStatus"
          :loading="loading"
          class="refresh-btn"
        >
          <i class="el-icon-refresh"></i>
        </el-button>
      </div>
      
      <div class="status-content">
        <div class="mode-info">
          <span class="mode-label">{{ currentModeLabel }}</span>
          <el-tag 
            :type="currentModeTagType" 
            size="mini"
            class="mode-tag"
          >
            {{ currentModeDescription }}
          </el-tag>
        </div>
        
        <div class="status-details">
          <div class="detail-item">
            <span class="detail-label">模拟数据:</span>
            <el-tag :type="status.mock_enabled ? 'success' : 'info'" size="mini">
              {{ status.mock_enabled ? '已启用' : '已禁用' }}
            </el-tag>
          </div>
          
          <div class="detail-item">
            <span class="detail-label">环境变量:</span>
            <el-tag :type="status.env_mock_enabled ? 'success' : 'info'" size="mini">
              {{ status.env_mock_enabled ? '已启用' : '已禁用' }}
            </el-tag>
          </div>
          
          <div class="detail-item">
            <span class="detail-label">最后更新:</span>
            <span class="detail-value">{{ formatTime(status.last_updated) }}</span>
          </div>
          
          <div class="detail-item">
            <span class="detail-label">更新者:</span>
            <span class="detail-value">{{ status.updated_by }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 模式切换面板 -->
    <div class="switch-panel">
      <div class="panel-header">
        <span class="panel-title">切换数据模式</span>
      </div>
      
      <div class="mode-options">
        <div 
          v-for="mode in availableModes" 
          :key="mode.value"
          class="mode-option"
          :class="{ 
            'active': selectedMode === mode.value,
            'current': status.current_mode === mode.value
          }"
          @click="selectMode(mode.value)"
        >
          <div class="option-header">
            <i :class="DATA_MODE_ICONS[mode.value]" :style="{ color: DATA_MODE_COLORS[mode.value] }"></i>
            <span class="option-label">{{ mode.label }}</span>
            <el-tag v-if="status.current_mode === mode.value" type="success" size="mini">当前</el-tag>
          </div>
          <div class="option-description">{{ mode.description }}</div>
        </div>
      </div>
      
      <div class="switch-controls">
        <el-input
          v-model="switchReason"
          placeholder="请输入切换原因（可选）"
          size="small"
          class="reason-input"
        ></el-input>
        
        <div class="control-buttons">
          <el-button 
            type="primary" 
            size="small"
            @click="performSwitch"
            :loading="switching"
            :disabled="!selectedMode || selectedMode === status.current_mode"
          >
            <i class="el-icon-switch-button"></i>
            切换模式
          </el-button>
          
          <el-button 
            type="info" 
            size="small"
            @click="testCurrentMode"
            :loading="testing"
          >
            <i class="el-icon-cpu"></i>
            测试模式
          </el-button>
        </div>
      </div>
    </div>

    <!-- 切换历史 -->
    <div class="history-panel" v-if="showHistory">
      <div class="panel-header">
        <span class="panel-title">切换历史</span>
        <el-button 
          type="text" 
          size="mini"
          @click="toggleHistory"
        >
          <i class="el-icon-arrow-up"></i>
        </el-button>
      </div>
      
      <div class="history-list">
        <div 
          v-for="(record, index) in history" 
          :key="index"
          class="history-item"
        >
          <div class="history-time">{{ formatTime(record.timestamp) }}</div>
          <div class="history-change">
            <span class="from-mode">{{ DATA_MODE_LABELS[record.from_mode] }}</span>
            <i class="el-icon-right"></i>
            <span class="to-mode">{{ DATA_MODE_LABELS[record.to_mode] }}</span>
          </div>
          <div class="history-reason">{{ record.reason }}</div>
          <div class="history-user">{{ record.user }}</div>
        </div>
      </div>
    </div>
    
    <!-- 历史展开按钮 -->
    <div class="history-toggle" v-if="!showHistory">
      <el-button 
        type="text" 
        size="mini"
        @click="toggleHistory"
      >
        <i class="el-icon-arrow-down"></i>
        查看切换历史
      </el-button>
    </div>
  </div>
</template>

<script>
import { 
  getDataModeStatus, 
  switchDataMode, 
  testDataMode, 
  getDataModeSwitchHistory,
  DATA_MODES,
  DATA_MODE_LABELS,
  DATA_MODE_DESCRIPTIONS,
  DATA_MODE_COLORS,
  DATA_MODE_ICONS
} from '@/api/dataMode'

export default {
  name: 'DataModeSwitch',
  data() {
    return {
      // 数据模式常量
      DATA_MODES,
      DATA_MODE_LABELS,
      DATA_MODE_DESCRIPTIONS,
      DATA_MODE_COLORS,
      DATA_MODE_ICONS,
      
      // 组件状态
      loading: false,
      switching: false,
      testing: false,
      
      // 数据状态
      status: {
        current_mode: 'auto',
        mock_enabled: false,
        env_mock_enabled: false,
        last_updated: null,
        updated_by: 'system',
        available_modes: []
      },
      
      // 切换控制
      selectedMode: null,
      switchReason: '',
      
      // 历史记录
      showHistory: false,
      history: []
    }
  },
  
  computed: {
    currentModeLabel() {
      return DATA_MODE_LABELS[this.status.current_mode] || '未知模式'
    },
    
    currentModeDescription() {
      return DATA_MODE_DESCRIPTIONS[this.status.current_mode] || ''
    },
    
    currentModeColor() {
      return DATA_MODE_COLORS[this.status.current_mode] || '#909399'
    },
    
    currentModeIcon() {
      return DATA_MODE_ICONS[this.status.current_mode] || 'el-icon-question'
    },
    
    currentModeTagType() {
      const typeMap = {
        [DATA_MODES.AUTO]: 'primary',
        [DATA_MODES.MOCK]: 'warning',
        [DATA_MODES.PRODUCTION]: 'success'
      }
      return typeMap[this.status.current_mode] || 'info'
    },
    
    availableModes() {
      return this.status.available_modes || []
    }
  },
  
  mounted() {
    this.loadStatus()
  },
  
  methods: {
    // 加载状态
    async loadStatus() {
      this.loading = true
      try {
        const response = await getDataModeStatus()
        console.log('DataModeSwitch - API响应:', response)
        
        // 由于响应拦截器直接返回response.data，所以这里response就是API的原始响应
        if (response && response.status === 'success') {
          this.status = response.data
          this.selectedMode = this.status.current_mode
          console.log('DataModeSwitch - 状态已更新:', this.status)
        } else {
          console.error('DataModeSwitch - API响应格式错误:', response)
          this.$message.error('获取数据模式状态失败')
        }
      } catch (error) {
        console.error('获取数据模式状态失败:', error)
        this.$message.error('获取数据模式状态失败: ' + (error.message || '未知错误'))
      } finally {
        this.loading = false
      }
    },
    
    // 刷新状态
    async refreshStatus() {
      await this.loadStatus()
      this.$message.success('状态已刷新')
    },
    
    // 选择模式
    selectMode(mode) {
      this.selectedMode = mode
    },
    
    // 执行切换
    async performSwitch(params = null) {
      // 如果传入了参数，使用传入的参数；否则使用组件内部状态
      const mode = params?.mode || this.selectedMode
      const reason = params?.reason || this.switchReason || '用户手动切换'
      
      if (!mode) {
        this.$message.warning('请选择要切换的模式')
        return
      }
      
      if (mode === this.status.current_mode) {
        this.$message.warning('所选模式与当前模式相同')
        return
      }
      
      this.switching = true
      try {
        const response = await switchDataMode({
          mode: mode,
          reason: reason
        })
        
        if (response.data && response.data.status === 'success') {
          this.$message.success(response.data.message || '模式切换成功')
          this.switchReason = ''
          await this.loadStatus()
          this.$emit('mode-changed', {
            oldMode: response.data.data.old_mode,
            newMode: response.data.data.new_mode
          })
        } else {
          this.$message.error('模式切换失败')
        }
      } catch (error) {
        console.error('模式切换失败:', error)
        this.$message.error('模式切换失败: ' + (error.message || '未知错误'))
      } finally {
        this.switching = false
      }
    },
    
    // 测试当前模式
    async testCurrentMode() {
      this.testing = true
      try {
        const response = await testDataMode()
        if (response.data && response.data.status === 'success') {
          const testData = response.data.data
          this.$message.success(`测试完成 - 数据源: ${testData.data_source}`)
          
          // 显示测试结果详情
          this.$alert(
            `当前模式: ${testData.current_mode}\n` +
            `数据源: ${testData.data_source}\n` +
            `模拟数据启用: ${testData.mock_enabled ? '是' : '否'}\n` +
            `测试时间: ${this.formatTime(testData.test_time)}`,
            '模式测试结果',
            {
              confirmButtonText: '确定',
              type: 'info'
            }
          )
        } else {
          this.$message.error('模式测试失败')
        }
      } catch (error) {
        console.error('模式测试失败:', error)
        this.$message.error('模式测试失败: ' + (error.message || '未知错误'))
      } finally {
        this.testing = false
      }
    },
    
    // 切换历史显示
    async toggleHistory() {
      this.showHistory = !this.showHistory
      if (this.showHistory && this.history.length === 0) {
        await this.loadHistory()
      }
    },
    
    // 加载历史记录
    async loadHistory() {
      try {
        const response = await getDataModeSwitchHistory()
        if (response.data && response.data.status === 'success') {
          this.history = response.data.data.history || []
        }
      } catch (error) {
        console.error('获取切换历史失败:', error)
        this.$message.error('获取切换历史失败')
      }
    },
    
    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return '-'
      try {
        const date = new Date(timeStr)
        return date.toLocaleString('zh-CN')
      } catch (error) {
        return timeStr
      }
    }
  }
}
</script>

<style scoped>
.data-mode-switch {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.current-status {
  padding: 20px;
  border-bottom: 1px solid #ebeef5;
}

.status-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.status-header i {
  font-size: 18px;
  margin-right: 8px;
}

.status-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  flex: 1;
}

.refresh-btn {
  padding: 0;
  margin-left: 8px;
}

.status-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.mode-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.mode-label {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.mode-tag {
  margin-left: 8px;
}

.status-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-label {
  font-size: 14px;
  color: #606266;
  min-width: 80px;
}

.detail-value {
  font-size: 14px;
  color: #303133;
}

.switch-panel {
  padding: 20px;
  border-bottom: 1px solid #ebeef5;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.panel-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.mode-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
  margin-bottom: 16px;
}

.mode-option {
  border: 2px solid #ebeef5;
  border-radius: 6px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.mode-option:hover {
  border-color: #c0c4cc;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.mode-option.active {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.mode-option.current {
  border-color: #67c23a;
  background-color: #f0f9ff;
}

.option-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.option-header i {
  font-size: 16px;
}

.option-label {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  flex: 1;
}

.option-description {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
}

.switch-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.reason-input {
  width: 100%;
}

.control-buttons {
  display: flex;
  gap: 12px;
}

.history-panel {
  padding: 20px;
}

.history-list {
  max-height: 300px;
  overflow-y: auto;
}

.history-item {
  display: grid;
  grid-template-columns: 150px 1fr 100px;
  gap: 12px;
  padding: 12px;
  border-bottom: 1px solid #f5f7fa;
  align-items: center;
}

.history-item:last-child {
  border-bottom: none;
}

.history-time {
  font-size: 12px;
  color: #909399;
}

.history-change {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.from-mode {
  color: #f56c6c;
}

.to-mode {
  color: #67c23a;
}

.history-reason {
  font-size: 12px;
  color: #606266;
}

.history-user {
  font-size: 12px;
  color: #909399;
}

.history-toggle {
  padding: 12px 20px;
  text-align: center;
  border-top: 1px solid #ebeef5;
}

@media (max-width: 768px) {
  .status-details {
    grid-template-columns: 1fr;
  }
  
  .mode-options {
    grid-template-columns: 1fr;
  }
  
  .control-buttons {
    flex-direction: column;
  }
  
  .history-item {
    grid-template-columns: 1fr;
    gap: 4px;
  }
}
</style>