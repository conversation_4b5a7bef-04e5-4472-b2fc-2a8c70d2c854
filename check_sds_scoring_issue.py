#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门检查SDS抑郁自评量表计分问题
"""

import sqlite3
import json
import os

def check_sds_scoring_issue():
    """检查SDS量表计分问题"""
    db_path = 'c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db'
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 检查SDS模板是否存在
        print("=== 1. 检查SDS模板 ===")
        cursor.execute("""
            SELECT id, template_key, name, scoring_method, max_score, status
            FROM assessment_templates 
            WHERE template_key = 'sds' OR name LIKE '%抑郁%'
        """)
        
        templates = cursor.fetchall()
        if not templates:
            print("❌ 未找到SDS模板")
            return
        
        for template in templates:
            template_id, template_key, name, scoring_method, max_score, status = template
            print(f"模板ID: {template_id}, Key: {template_key}, 名称: {name}")
            print(f"计分方法: {scoring_method}, 最大分数: {max_score}, 状态: {status}")
            
            # 2. 检查该模板的问题计分规则
            print(f"\n=== 2. 检查模板 {template_id} 的问题计分规则 ===")
            cursor.execute("""
                SELECT question_id, question_text, scoring, dimension_key
                FROM assessment_template_questions 
                WHERE template_id = ?
                ORDER BY question_id
                LIMIT 5
            """, (template_id,))
            
            questions = cursor.fetchall()
            print(f"问题总数: {len(questions)} (显示前5个)")
            
            for question in questions:
                question_id, question_text, scoring, dimension_key = question
                print(f"\n问题ID: {question_id}")
                print(f"  文本: {question_text[:30]}...")
                print(f"  维度: {dimension_key}")
                print(f"  计分规则: {scoring}")
                
                # 检查计分规则
                if not scoring or scoring.strip() == '':
                    print(f"  ❌ 计分规则为空!")
                elif scoring == 'null' or scoring == 'None':
                    print(f"  ❌ 计分规则为null!")
                else:
                    try:
                        if scoring.startswith('{') or scoring.startswith('['):
                            parsed = json.loads(scoring)
                            print(f"  ✅ 计分规则JSON格式正确: {parsed}")
                        else:
                            print(f"  ⚠️ 计分规则非JSON格式: {scoring}")
                    except json.JSONDecodeError as e:
                        print(f"  ❌ 计分规则JSON格式错误: {e}")
            
            # 3. 检查最近的评估记录
            print(f"\n=== 3. 检查模板 {template_id} 的最近评估记录 ===")
            cursor.execute("""
                SELECT ar.id, ar.assessment_id, ar.total_score, ar.dimension_scores, ar.raw_answers, ar.created_at
                FROM assessment_results ar
                JOIN assessments a ON ar.assessment_id = a.id
                WHERE a.template_id = ?
                ORDER BY ar.created_at DESC
                LIMIT 2
            """, (template_id,))
            
            results = cursor.fetchall()
            if results:
                for result in results:
                    result_id, assessment_id, total_score, dimension_scores, raw_answers, created_at = result
                    print(f"\n评估结果ID: {result_id}, 评估ID: {assessment_id}")
                    print(f"  创建时间: {created_at}")
                    print(f"  总分: {total_score}")
                    print(f"  维度分: {dimension_scores}")
                    
                    if raw_answers:
                        try:
                            answers_data = json.loads(raw_answers)
                            print(f"  原始答案: 包含 {len(answers_data)} 个答案")
                            # 显示前2个答案
                            if isinstance(answers_data, list):
                                for i, answer in enumerate(answers_data[:2]):
                                    print(f"    答案 {i+1}: {answer}")
                            elif isinstance(answers_data, dict):
                                print(f"    原始答案(字典格式): {str(answers_data)[:200]}...")
                        except json.JSONDecodeError:
                            print(f"  原始答案JSON解析失败: {raw_answers[:100]}...")
                    else:
                        print(f"  ❌ 原始答案为空!")
            else:
                print("未找到评估记录")
        
        # 4. 检查SM_008用户的具体情况
        print("\n=== 4. 检查SM_008用户的评估情况 ===")
        cursor.execute("""
            SELECT id, custom_id, username
            FROM users
            WHERE custom_id = 'SM_008'
        """)
        
        user_info = cursor.fetchone()
        if user_info:
            user_id, custom_id, username = user_info
            print(f"用户ID: {user_id}, 自定义ID: {custom_id}, 用户名: {username}")
            
            # 查找该用户的SDS评估
            cursor.execute("""
                SELECT a.id, a.template_id, ar.total_score, ar.dimension_scores, ar.raw_answers, ar.created_at
                FROM assessments a
                LEFT JOIN assessment_results ar ON a.id = ar.assessment_id
                JOIN assessment_templates at ON a.template_id = at.id
                WHERE a.user_id = ? AND (at.template_key = 'sds' OR at.name LIKE '%抑郁%')
                ORDER BY a.created_at DESC
                LIMIT 1
            """, (user_id,))
            
            user_assessment = cursor.fetchone()
            if user_assessment:
                assessment_id, template_id, total_score, dimension_scores, raw_answers, created_at = user_assessment
                print(f"\n找到评估记录:")
                print(f"  评估ID: {assessment_id}, 模板ID: {template_id}")
                print(f"  总分: {total_score}, 维度分: {dimension_scores}")
                print(f"  创建时间: {created_at}")
                
                if raw_answers:
                    try:
                        answers_data = json.loads(raw_answers)
                        print(f"  原始答案解析成功，包含 {len(answers_data)} 个答案")
                        
                        # 分析答案和分数
                        total_calculated = 0
                        if isinstance(answers_data, list):
                            for i, answer in enumerate(answers_data[:3]):
                                print(f"    答案 {i+1}: {answer}")
                                if isinstance(answer, dict) and 'score' in answer:
                                    total_calculated += answer.get('score', 0)
                            print(f"  前3个答案计算总分: {total_calculated}")
                        elif isinstance(answers_data, dict):
                            print(f"    原始答案(字典格式): {str(answers_data)[:300]}...")
                        
                    except json.JSONDecodeError as e:
                        print(f"  原始答案JSON解析失败: {e}")
                        print(f"  原始数据: {raw_answers[:200]}...")
                else:
                    print(f"  ❌ 原始答案为空!")
            else:
                print("未找到该用户的SDS评估记录")
        else:
            print("未找到SM_008用户")
        
        conn.close()
        
    except Exception as e:
        print(f"检查过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_sds_scoring_issue()