import asyncio
from playwright.async_api import async_playwright
import json

async def test_questionnaire_frontend():
    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(headless=False, slow_mo=500)
        context = await browser.new_context()
        page = await context.new_page()
        
        # 监听控制台消息
        console_messages = []
        page.on("console", lambda msg: console_messages.append(f"Console [{msg.type}]: {msg.text}"))
        
        # 监听网络请求
        network_requests = []
        page.on("request", lambda request: network_requests.append({
            "url": request.url,
            "method": request.method,
            "headers": dict(request.headers)
        }))
        
        # 监听网络响应
        network_responses = []
        async def handle_response(response):
            try:
                if 'questionnaires' in response.url:
                    body = await response.text()
                    network_responses.append({
                        "url": response.url,
                        "status": response.status,
                        "status_text": response.status_text,
                        "body": body[:500] if body else ""  # 只保留前500字符
                    })
                else:
                    network_responses.append({
                        "url": response.url,
                        "status": response.status,
                        "status_text": response.status_text
                    })
            except Exception as e:
                network_responses.append({
                    "url": response.url,
                    "status": response.status,
                    "status_text": response.status_text,
                    "error": str(e)
                })
        
        page.on("response", handle_response)
        
        try:
            print("开始测试前端问卷页面...")
            
            # 1. 访问前端登录页面
            await page.goto("http://localhost:8080/login")
            await page.wait_for_load_state("networkidle")
            print("已访问登录页面")
            
            # 2. 登录
            await page.fill('input[placeholder="请输入用户名"]', "admin")
            await page.fill('input[placeholder="请输入密码"]', "admin123")
            await page.click('button[type="submit"]')
            await page.wait_for_load_state("networkidle")
            print("已完成登录")
            
            # 3. 导航到健康资料页面
            await page.goto("http://localhost:8080/health-data")
            await page.wait_for_load_state("networkidle")
            print("已访问健康资料页面")
            
            # 4. 搜索用户SM_008
            await page.fill('input[placeholder="请输入用户ID"]', "SM_008")
            await page.click('button:has-text("搜索")')
            await page.wait_for_timeout(2000)  # 等待搜索结果
            print("已搜索用户SM_008")
            
            # 检查搜索结果
            search_results = await page.query_selector_all('.el-table__row')
            print(f"搜索到 {len(search_results)} 个用户")
            
            if len(search_results) == 0:
                print("未找到用户SM_008，尝试搜索其他用户")
                await page.fill('input[placeholder="请输入用户ID"]', "")
                await page.fill('input[placeholder="请输入姓名"]', "张")
                await page.click('button:has-text("搜索")')
                await page.wait_for_timeout(2000)
                search_results = await page.query_selector_all('.el-table__row')
                print(f"重新搜索到 {len(search_results)} 个用户")
            
            if len(search_results) > 0:
                # 5. 点击查看健康资料
                await page.click('button:has-text("查看健康资料")')
                await page.wait_for_timeout(2000)  # 等待抽屉打开
                print("已打开健康资料抽屉")
                
                # 6. 切换到问卷与评估Tab
                questionnaire_tab = await page.query_selector('div[id="tab-questionnaires"]')
                if questionnaire_tab:
                    await questionnaire_tab.click()
                    await page.wait_for_timeout(2000)
                    print("已切换到问卷与评估Tab")
                    
                    # 7. 测试未完成Tab
                    pending_tab = await page.query_selector('div[id="tab-pending"]')
                    if pending_tab:
                        await pending_tab.click()
                        await page.wait_for_timeout(3000)  # 等待数据加载
                        print("已切换到未完成Tab")
                        
                        # 检查未完成Tab的数据
                        await page.wait_for_timeout(2000)
                        pending_content = await page.query_selector('.pending-content')
                        if pending_content:
                            table_rows = await pending_content.query_selector_all('.el-table__row')
                            empty_element = await pending_content.query_selector('.el-empty')
                            
                            if len(table_rows) > 0:
                                print(f"未完成Tab显示 {len(table_rows)} 行数据")
                            elif empty_element:
                                empty_text = await empty_element.text_content()
                                print(f"未完成Tab显示空状态: {empty_text}")
                            else:
                                print("未完成Tab: 内容区域存在但无数据")
                        else:
                            print("未完成Tab: 未找到内容区域")
                    
                    # 8. 测试已完成Tab
                    completed_tab = await page.query_selector('div[id="tab-completed"]')
                    if completed_tab:
                        await completed_tab.click()
                        await page.wait_for_timeout(3000)  # 等待数据加载
                        print("已切换到已完成Tab")
                        
                        # 检查已完成Tab的数据
                        await page.wait_for_timeout(2000)
                        completed_content = await page.query_selector('.completed-content')
                        if completed_content:
                            table_rows = await completed_content.query_selector_all('.el-table__row')
                            empty_element = await completed_content.query_selector('.el-empty')
                            
                            if len(table_rows) > 0:
                                print(f"已完成Tab显示 {len(table_rows)} 行数据")
                            elif empty_element:
                                empty_text = await empty_element.text_content()
                                print(f"已完成Tab显示空状态: {empty_text}")
                            else:
                                print("已完成Tab: 内容区域存在但无数据")
                        else:
                            print("已完成Tab: 未找到内容区域")
                else:
                    print("未找到问卷与评估Tab")
            else:
                print("未找到任何用户，无法继续测试")
            
            # 9. 检查API调用
            print("\n=== 网络请求分析 ===")
            questionnaire_requests = [req for req in network_requests if 'questionnaires' in req['url']]
            for req in questionnaire_requests:
                auth_header = req['headers'].get('authorization', 'No Auth')
                print(f"请求: {req['method']} {req['url']}")
                print(f"  认证头: {auth_header[:50]}..." if len(auth_header) > 50 else f"  认证头: {auth_header}")
            
            print("\n=== 网络响应分析 ===")
            questionnaire_responses = [resp for resp in network_responses if 'questionnaires' in resp['url']]
            for resp in questionnaire_responses:
                print(f"响应: {resp['status']} {resp['url']}")
                if 'body' in resp and resp['body']:
                    print(f"  响应体: {resp['body']}")
                if 'error' in resp:
                    print(f"  错误: {resp['error']}")
            
            # 10. 检查控制台错误
            print("\n=== 控制台消息 ===")
            error_messages = [msg for msg in console_messages if 'error' in msg.lower()]
            if error_messages:
                for msg in error_messages:
                    print(msg)
            else:
                print("无控制台错误")
                # 显示最近的几条消息
                for msg in console_messages[-5:]:
                    print(msg)
            
        except Exception as e:
            print(f"测试过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            await browser.close()
            print("测试完成")

if __name__ == "__main__":
    asyncio.run(test_questionnaire_frontend())