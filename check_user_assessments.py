#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def check_user_assessments():
    """检查数据库中各用户的评估数据"""
    conn = sqlite3.connect('YUN/backend/app.db')
    cursor = conn.cursor()
    
    try:
        # 1. 检查各用户的评估数量
        print("=== 数据库中各用户的评估数量 ===")
        cursor.execute('SELECT custom_id, COUNT(*) FROM assessments GROUP BY custom_id')
        users = cursor.fetchall()
        
        if not users:
            print("数据库中没有任何评估数据")
        else:
            for user in users:
                print(f"用户 {user[0]}: {user[1]}个评估")
        
        # 2. 检查SM_006和SM_008的具体评估
        print("\n=== SM_006和SM_008的评估详情 ===")
        cursor.execute("""
            SELECT custom_id, name, status, template_id, created_at 
            FROM assessments 
            WHERE custom_id IN ('SM_006', 'SM_008') 
            ORDER BY custom_id, id
        """)
        assessments = cursor.fetchall()
        
        if not assessments:
            print("SM_006和SM_008都没有评估数据")
        else:
            current_user = None
            for assessment in assessments:
                if current_user != assessment[0]:
                    current_user = assessment[0]
                    print(f"\n{current_user}的评估:")
                print(f"  - {assessment[1]} (状态: {assessment[2]}, 模板ID: {assessment[3]}, 创建时间: {assessment[4]})")
        
        # 3. 检查SM_006是否需要创建评估数据
        print("\n=== 检查SM_006是否需要创建评估数据 ===")
        cursor.execute('SELECT COUNT(*) FROM assessments WHERE custom_id = "SM_006"')
        sm006_count = cursor.fetchone()[0]
        
        if sm006_count == 0:
            print("SM_006没有评估数据，需要创建")
            
            # 检查可用的评估模板
            cursor.execute('SELECT id, name FROM assessment_templates LIMIT 5')
            templates = cursor.fetchall()
            print(f"\n可用的评估模板 ({len(templates)}个):")
            for template in templates:
                print(f"  - ID: {template[0]}, 名称: {template[1]}")
        else:
            print(f"SM_006已有 {sm006_count} 个评估，无需创建新的")
            
            # 显示现有评估
            cursor.execute("""
                SELECT a.name, a.status, t.name as template_name, t.description
                FROM assessments a
                LEFT JOIN assessment_templates t ON a.template_id = t.id
                WHERE a.custom_id = 'SM_006'
            """)
            existing = cursor.fetchall()
            print("\nSM_006现有评估:")
            for assessment in existing:
                print(f"  - {assessment[0]} (状态: {assessment[1]})")
                print(f"    模板: {assessment[2]}")
                if assessment[3]:
                    print(f"    描述: {assessment[3][:100]}...")
        
    except Exception as e:
        print(f"检查失败: {e}")
    finally:
        conn.close()

if __name__ == '__main__':
    check_user_assessments()