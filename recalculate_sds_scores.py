#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新计算SDS抑郁自评量表的分数
"""

import sqlite3
import json
import os

def recalculate_sds_scores():
    """重新计算SDS量表的分数"""
    db_path = 'c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db'
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 获取SDS模板信息
        print("=== 1. 获取SDS模板信息 ===")
        cursor.execute("""
            SELECT id, template_key, name, max_score
            FROM assessment_templates 
            WHERE template_key = 'sds' OR name LIKE '%抑郁%'
        """)
        
        template = cursor.fetchone()
        if not template:
            print("❌ 未找到SDS模板")
            return
        
        template_id, template_key, name, max_score = template
        print(f"找到模板: ID={template_id}, Key={template_key}, Name={name}")
        
        # 2. 获取问题的计分规则
        print("\n=== 2. 获取问题计分规则 ===")
        cursor.execute("""
            SELECT question_id, scoring, dimension_key
            FROM assessment_template_questions 
            WHERE template_id = ?
            ORDER BY question_id
        """, (template_id,))
        
        questions = cursor.fetchall()
        scoring_rules = {}
        dimension_questions = {}
        
        for question_id, scoring, dimension_key in questions:
            if scoring:
                try:
                    scoring_rules[question_id] = json.loads(scoring)
                    if dimension_key:
                        if dimension_key not in dimension_questions:
                            dimension_questions[dimension_key] = []
                        dimension_questions[dimension_key].append(question_id)
                except json.JSONDecodeError:
                    print(f"⚠️ 问题 {question_id} 计分规则JSON格式错误")
        
        print(f"加载了 {len(scoring_rules)} 个问题的计分规则")
        print(f"维度分组: {dimension_questions}")
        
        # 3. 查找需要重新计算的评估结果
        print("\n=== 3. 查找需要重新计算的评估结果 ===")
        cursor.execute("""
            SELECT ar.id, ar.assessment_id, ar.total_score, ar.dimension_scores, ar.raw_answers
            FROM assessment_results ar
            JOIN assessments a ON ar.assessment_id = a.id
            WHERE a.template_id = ? AND (ar.total_score = 0 OR ar.total_score IS NULL)
        """, (template_id,))
        
        results_to_update = cursor.fetchall()
        print(f"找到 {len(results_to_update)} 个需要重新计算的评估结果")
        
        # 4. 重新计算每个评估结果
        updated_count = 0
        
        for result_id, assessment_id, old_total_score, old_dimension_scores, raw_answers in results_to_update:
            print(f"\n处理评估结果 {result_id} (评估ID: {assessment_id})")
            
            if not raw_answers:
                print(f"  ❌ 原始答案为空，跳过")
                continue
            
            try:
                answers_data = json.loads(raw_answers)
                if not isinstance(answers_data, list):
                    print(f"  ❌ 原始答案格式错误，跳过")
                    continue
                
                # 重新计算分数
                new_total_score = 0
                new_dimension_scores = {}
                updated_answers = []
                
                # 初始化维度分数
                for dimension in dimension_questions:
                    new_dimension_scores[dimension] = {
                        "score": 0,
                        "name": get_dimension_name(dimension),
                        "question_count": len(dimension_questions[dimension])
                    }
                
                # 计算每个答案的分数
                for answer in answers_data:
                    if not isinstance(answer, dict):
                        continue
                    
                    question_id = answer.get('question_id')
                    answer_value = str(answer.get('answer', ''))
                    
                    if question_id in scoring_rules and answer_value in scoring_rules[question_id]:
                        # 计算新分数
                        new_score = scoring_rules[question_id][answer_value]
                        
                        # 更新答案中的分数
                        updated_answer = answer.copy()
                        updated_answer['score'] = new_score
                        updated_answers.append(updated_answer)
                        
                        # 累加总分
                        new_total_score += new_score
                        
                        # 累加维度分
                        for dimension, questions_in_dim in dimension_questions.items():
                            if question_id in questions_in_dim:
                                new_dimension_scores[dimension]["score"] += new_score
                                break
                    else:
                        # 保持原答案
                        updated_answers.append(answer)
                        print(f"    ⚠️ 问题 {question_id} 答案 {answer_value} 没有对应的计分规则")
                
                # 计算标准分 (SDS标准分 = 原始分 × 1.25)
                standard_score = new_total_score * 1.25
                
                print(f"  原始分: {new_total_score}, 标准分: {standard_score}")
                print(f"  维度分: {new_dimension_scores}")
                
                # 更新数据库
                cursor.execute("""
                    UPDATE assessment_results 
                    SET total_score = ?, dimension_scores = ?, raw_answers = ?
                    WHERE id = ?
                """, (
                    standard_score,
                    json.dumps(new_dimension_scores, ensure_ascii=False),
                    json.dumps(updated_answers, ensure_ascii=False),
                    result_id
                ))
                
                # 同时更新assessments表中的分数
                cursor.execute("""
                    UPDATE assessments 
                    SET score = ?, result = ?
                    WHERE id = ?
                """, (
                    standard_score,
                    get_sds_result(standard_score),
                    assessment_id
                ))
                
                updated_count += 1
                print(f"  ✅ 更新成功")
                
            except json.JSONDecodeError as e:
                print(f"  ❌ 原始答案JSON解析失败: {e}")
                continue
            except Exception as e:
                print(f"  ❌ 处理失败: {e}")
                continue
        
        # 5. 提交更改
        if updated_count > 0:
            conn.commit()
            print(f"\n✅ 成功重新计算了 {updated_count} 个评估结果")
        else:
            print("\n⚠️ 没有需要更新的评估结果")
        
        # 6. 验证SM_008用户的结果
        print("\n=== 4. 验证SM_008用户的结果 ===")
        cursor.execute("""
            SELECT ar.id, ar.total_score, ar.dimension_scores, a.result
            FROM assessment_results ar
            JOIN assessments a ON ar.assessment_id = a.id
            WHERE a.template_id = ? AND a.custom_id = 'SM_008'
            ORDER BY ar.created_at DESC
            LIMIT 1
        """, (template_id,))
        
        final_result = cursor.fetchone()
        if final_result:
            result_id, total_score, dimension_scores, result = final_result
            print(f"SM_008最新评估结果:")
            print(f"  结果ID: {result_id}")
            print(f"  总分: {total_score}")
            print(f"  结果: {result}")
            print(f"  维度分: {dimension_scores}")
        
        conn.close()
        
        print("\n=== 重新计算完成 ===")
        print("建议操作:")
        print("1. 检查前端显示是否正常")
        print("2. 测试新的SDS量表提交")
        
    except Exception as e:
        print(f"重新计算过程中出错: {e}")
        import traceback
        traceback.print_exc()

def get_dimension_name(dimension_key):
    """获取维度名称"""
    dimension_names = {
        "depressive_emotion": "抑郁情感",
        "somatic_symptoms": "躯体症状",
        "psychomotor_symptoms": "精神运动症状"
    }
    return dimension_names.get(dimension_key, dimension_key)

def get_sds_result(score):
    """根据分数获取SDS结果"""
    if score < 50:
        return "正常"
    elif score < 60:
        return "轻度抑郁"
    elif score < 70:
        return "中度抑郁"
    else:
        return "重度抑郁"

if __name__ == "__main__":
    recalculate_sds_scores()