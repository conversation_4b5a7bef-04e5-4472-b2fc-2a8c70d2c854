#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复移动端API的custom_id参数类型问题
"""

import os
import re

def fix_mobile_notifications_api():
    """修复mobile_notifications.py中的custom_id参数类型"""
    file_path = os.path.join('YUN', 'backend', 'app', 'api', 'endpoints', 'mobile_notifications.py')
    
    if not os.path.exists(file_path):
        print(f"错误: 文件不存在 {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找get_mobile_assessments函数中的custom_id参数定义
    pattern = r'(def get_mobile_assessments\([^)]*custom_id: int = Query\([^)]*\)[^)]*\):)'
    if not re.search(pattern, content):
        print("未找到需要修改的custom_id参数定义")
        
        # 查找实际的参数定义
        assessment_func = re.search(r'def get_mobile_assessments\([^{]*:', content)
        if assessment_func:
            print(f"实际的参数定义:\n{assessment_func.group(0)}")
        return False
    
    # 将custom_id参数类型从int改为str
    modified_content = re.sub(
        r'(custom_id: )int( = Query\([^)]*\))', 
        r'\1str\2', 
        content
    )
    
    # 检查是否有变化
    if modified_content == content:
        print("内容未发生变化，可能正则表达式匹配失败")
        return False
    
    # 备份原文件
    backup_path = file_path + '.bak'
    with open(backup_path, 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"已备份原文件到 {backup_path}")
    
    # 写入修改后的内容
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(modified_content)
    print(f"已修改 {file_path}")
    
    # 验证修改
    with open(file_path, 'r', encoding='utf-8') as f:
        new_content = f.read()
    
    assessment_func = re.search(r'def get_mobile_assessments\([^{]*custom_id[^{]*:', new_content)
    if assessment_func:
        print(f"修改后的参数定义:\n{assessment_func.group(0)}")
    
    return True

def fix_mobile_questionnaires_api():
    """修复mobile_notifications.py中的get_mobile_questionnaires函数的custom_id参数类型"""
    file_path = os.path.join('YUN', 'backend', 'app', 'api', 'endpoints', 'mobile_notifications.py')
    
    if not os.path.exists(file_path):
        print(f"错误: 文件不存在 {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找get_mobile_questionnaires函数中的custom_id参数定义
    pattern = r'(def get_mobile_questionnaires\([^)]*custom_id: int = Query\([^)]*\)[^)]*\):)'
    if not re.search(pattern, content):
        print("未找到需要修改的问卷API中的custom_id参数定义")
        
        # 查找实际的参数定义
        questionnaire_func = re.search(r'def get_mobile_questionnaires\([^{]*:', content)
        if questionnaire_func:
            print(f"问卷API实际的参数定义:\n{questionnaire_func.group(0)}")
        return False
    
    # 将custom_id参数类型从int改为str
    modified_content = re.sub(
        r'(def get_mobile_questionnaires\([^)]*custom_id: )int( = Query\([^)]*\)[^)]*\):)', 
        r'\1str\2', 
        content
    )
    
    # 检查是否有变化
    if modified_content == content:
        print("问卷API内容未发生变化，可能正则表达式匹配失败")
        return False
    
    # 写入修改后的内容
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(modified_content)
    print(f"已修改问卷API参数 {file_path}")
    
    # 验证修改
    with open(file_path, 'r', encoding='utf-8') as f:
        new_content = f.read()
    
    questionnaire_func = re.search(r'def get_mobile_questionnaires\([^{]*custom_id[^{]*:', new_content)
    if questionnaire_func:
        print(f"修改后的问卷API参数定义:\n{questionnaire_func.group(0)}")
    
    return True

def main():
    """主函数"""
    print("开始修复移动端API的custom_id参数类型问题...")
    
    # 修复量表API
    assessment_fixed = fix_mobile_notifications_api()
    
    # 修复问卷API
    questionnaire_fixed = fix_mobile_questionnaires_api()
    
    if assessment_fixed or questionnaire_fixed:
        print("\n修复完成，请重启后端服务以应用更改")
    else:
        print("\n未进行任何修改，请检查API定义")

if __name__ == "__main__":
    main()