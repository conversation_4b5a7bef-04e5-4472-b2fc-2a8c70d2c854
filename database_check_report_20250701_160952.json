{"tables": {"alert_channels": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，2 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 created_by 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "name", "type": "VARCHAR(255)", "nullable": false, "default": null, "primary_key": 0}, {"name": "type", "type": "VARCHAR(50)", "nullable": false, "default": null, "primary_key": 0}, {"name": "config", "type": "TEXT", "nullable": false, "default": null, "primary_key": 0}, {"name": "enabled", "type": "BOOLEAN", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_by", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["created_by"], "referred_schema": null, "referred_table": "users", "referred_columns": ["custom_id"], "options": {}}], "indexes": [{"name": "ix_alert_channels_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 8}}, "alert_rules": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，2 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 created_by 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "name", "type": "VARCHAR(255)", "nullable": false, "default": null, "primary_key": 0}, {"name": "description", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "condition", "type": "TEXT", "nullable": false, "default": null, "primary_key": 0}, {"name": "severity", "type": "VARCHAR(50)", "nullable": false, "default": null, "primary_key": 0}, {"name": "enabled", "type": "BOOLEAN", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_by", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["created_by"], "referred_schema": null, "referred_table": "users", "referred_columns": ["custom_id"], "options": {}}], "indexes": [{"name": "ix_alert_rules_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 9}}, "alerts": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，2 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 custom_id 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "title", "type": "VARCHAR(255)", "nullable": false, "default": null, "primary_key": 0}, {"name": "description", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "severity", "type": "VARCHAR(50)", "nullable": false, "default": null, "primary_key": 0}, {"name": "status", "type": "VARCHAR(50)", "nullable": false, "default": null, "primary_key": 0}, {"name": "source", "type": "VARCHAR(255)", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "acknowledged_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "resolved_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "custom_id", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["custom_id"], "referred_schema": null, "referred_table": "users", "referred_columns": ["custom_id"], "options": {}}], "indexes": [{"name": "ix_alerts_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 11}}, "assessment_distributions": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，4 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 assessment_id 没有索引，可能影响查询性能", "外键列 custom_id 没有索引，可能影响查询性能", "外键列 distributor_custom_id 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "assessment_id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 0}, {"name": "custom_id", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "distributor_custom_id", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "status", "type": "VARCHAR(50)", "nullable": true, "default": null, "primary_key": 0}, {"name": "due_date", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "completed_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "message", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "description", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "instructions", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "template_id", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["assessment_id"], "referred_schema": null, "referred_table": "assessments", "referred_columns": ["id"], "options": {"ondelete": "CASCADE"}}, {"name": null, "constrained_columns": ["custom_id"], "referred_schema": null, "referred_table": "users", "referred_columns": ["custom_id"], "options": {"ondelete": "CASCADE"}}, {"name": null, "constrained_columns": ["distributor_custom_id"], "referred_schema": null, "referred_table": "users", "referred_columns": ["custom_id"], "options": {"ondelete": "SET NULL"}}], "indexes": [{"name": "ix_assessment_distributions_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 13}}, "assessment_items": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，2 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 assessment_id 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "assessment_id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 0}, {"name": "question_id", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "question_text", "type": "TEXT", "nullable": false, "default": null, "primary_key": 0}, {"name": "question_type", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "options", "type": "JSON", "nullable": true, "default": null, "primary_key": 0}, {"name": "order_num", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}, {"name": "is_required", "type": "BOOLEAN", "nullable": true, "default": null, "primary_key": 0}, {"name": "jump_logic", "type": "JSON", "nullable": true, "default": null, "primary_key": 0}, {"name": "answer", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "score", "type": "FLOAT", "nullable": true, "default": null, "primary_key": 0}, {"name": "notes", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": "CURRENT_TIMESTAMP", "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["assessment_id"], "referred_schema": null, "referred_table": "assessments", "referred_columns": ["id"], "options": {}}], "indexes": [{"name": "ix_assessment_items_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 13}}, "assessment_responses": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，3 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 assessment_id 没有索引，可能影响查询性能", "外键列 custom_id 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "assessment_id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 0}, {"name": "custom_id", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "answers", "type": "JSON", "nullable": false, "default": null, "primary_key": 0}, {"name": "score", "type": "FLOAT", "nullable": true, "default": null, "primary_key": 0}, {"name": "result", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "notes", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": "CURRENT_TIMESTAMP", "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "dimensions", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "dimension_key", "type": "VARCHAR(100)", "nullable": true, "default": null, "primary_key": 0}, {"name": "dimension_scores", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "status", "type": "VARCHAR(20)", "nullable": true, "default": "'completed'", "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["assessment_id"], "referred_schema": null, "referred_table": "assessments", "referred_columns": ["id"], "options": {}}, {"name": null, "constrained_columns": ["custom_id"], "referred_schema": null, "referred_table": "users", "referred_columns": ["custom_id"], "options": {}}], "indexes": [{"name": "idx_assessment_responses_dimension_key", "column_names": ["dimension_key"], "unique": 0, "dialect_options": {}}, {"name": "ix_assessment_responses_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 13}}, "assessment_results": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，4 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 assessment_id 没有索引，可能影响查询性能", "外键列 custom_id 没有索引，可能影响查询性能", "外键列 template_id 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "assessment_id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 0}, {"name": "custom_id", "type": "VARCHAR(50)", "nullable": false, "default": null, "primary_key": 0}, {"name": "template_id", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}, {"name": "total_score", "type": "FLOAT", "nullable": true, "default": null, "primary_key": 0}, {"name": "max_score", "type": "FLOAT", "nullable": true, "default": null, "primary_key": 0}, {"name": "percentage", "type": "FLOAT", "nullable": true, "default": null, "primary_key": 0}, {"name": "result_level", "type": "VARCHAR(50)", "nullable": true, "default": null, "primary_key": 0}, {"name": "result_category", "type": "VARCHAR(100)", "nullable": true, "default": null, "primary_key": 0}, {"name": "interpretation", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "recommendations", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "dimension_scores", "type": "JSON", "nullable": true, "default": null, "primary_key": 0}, {"name": "calculation_details", "type": "JSON", "nullable": true, "default": null, "primary_key": 0}, {"name": "raw_answers", "type": "JSON", "nullable": true, "default": null, "primary_key": 0}, {"name": "report_generated", "type": "BOOLEAN", "nullable": true, "default": null, "primary_key": 0}, {"name": "report_content", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "report_format", "type": "VARCHAR(20)", "nullable": true, "default": null, "primary_key": 0}, {"name": "report_template", "type": "VARCHAR(100)", "nullable": true, "default": null, "primary_key": 0}, {"name": "status", "type": "VARCHAR(20)", "nullable": true, "default": null, "primary_key": 0}, {"name": "calculated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["assessment_id"], "referred_schema": null, "referred_table": "assessments", "referred_columns": ["id"], "options": {"ondelete": "CASCADE"}}, {"name": null, "constrained_columns": ["custom_id"], "referred_schema": null, "referred_table": "users", "referred_columns": ["custom_id"], "options": {}}, {"name": null, "constrained_columns": ["template_id"], "referred_schema": null, "referred_table": "assessment_templates", "referred_columns": ["id"], "options": {}}], "indexes": [{"name": "ix_assessment_results_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 22}}, "assessment_template_questions": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，2 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 template_id 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "template_id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 0}, {"name": "question_id", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "question_text", "type": "TEXT", "nullable": false, "default": null, "primary_key": 0}, {"name": "question_type", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "options", "type": "JSON", "nullable": true, "default": null, "primary_key": 0}, {"name": "scoring", "type": "JSON", "nullable": true, "default": null, "primary_key": 0}, {"name": "order", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 0}, {"name": "is_required", "type": "BOOLEAN", "nullable": true, "default": null, "primary_key": 0}, {"name": "jump_logic", "type": "JSON", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": "CURRENT_TIMESTAMP", "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "dimensions", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "dimension_key", "type": "VARCHAR(100)", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["template_id"], "referred_schema": null, "referred_table": "assessment_templates", "referred_columns": ["id"], "options": {}}], "indexes": [{"name": "idx_assessment_template_questions_dimension_key", "column_names": ["dimension_key"], "unique": 0, "dialect_options": {}}, {"name": "ix_assessment_template_questions_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 14}}, "assessment_templates": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，2 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 created_by 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "template_key", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "assessment_type", "type": "VARCHAR(16)", "nullable": false, "default": null, "primary_key": 0}, {"name": "sub_type", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "name", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "version", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "description", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "instructions", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "scoring_method", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "max_score", "type": "FLOAT", "nullable": true, "default": null, "primary_key": 0}, {"name": "result_ranges", "type": "JSON", "nullable": true, "default": null, "primary_key": 0}, {"name": "is_active", "type": "BOOLEAN", "nullable": true, "default": null, "primary_key": 0}, {"name": "status", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_by", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": "CURRENT_TIMESTAMP", "primary_key": 0}, {"name": "dimensions", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "dimension_key", "type": "VARCHAR(100)", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["created_by"], "referred_schema": null, "referred_table": "users", "referred_columns": ["id"], "options": {}}], "indexes": [{"name": "idx_assessment_templates_dimension_key", "column_names": ["dimension_key"], "unique": 0, "dialect_options": {}}, {"name": "ix_assessment_templates_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}, {"name": "ix_assessment_templates_template_key", "column_names": ["template_key"], "unique": 1, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 17}}, "assessments": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，3 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 custom_id 没有索引，可能影响查询性能", "外键列 template_id 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "custom_id", "type": "VARCHAR(20)", "nullable": false, "default": null, "primary_key": 0}, {"name": "template_id", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}, {"name": "assessment_type", "type": "VARCHAR(16)", "nullable": false, "default": null, "primary_key": 0}, {"name": "name", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "version", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "round_number", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 0}, {"name": "sequence_number", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 0}, {"name": "unique_identifier", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "completed_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "assessor", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "score", "type": "FLOAT", "nullable": true, "default": null, "primary_key": 0}, {"name": "max_score", "type": "FLOAT", "nullable": true, "default": null, "primary_key": 0}, {"name": "result", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "conclusion", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "notes", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "status", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": "CURRENT_TIMESTAMP", "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "last_reminded_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["custom_id"], "referred_schema": null, "referred_table": "users", "referred_columns": ["custom_id"], "options": {}}, {"name": null, "constrained_columns": ["template_id"], "referred_schema": null, "referred_table": "assessment_templates", "referred_columns": ["id"], "options": {}}], "indexes": [{"name": "ix_assessments_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}, {"name": "ix_assessments_unique_identifier", "column_names": ["unique_identifier"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 20}}, "consultant_clients": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，3 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 consultant_id 没有索引，可能影响查询性能", "外键列 client_id 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "consultant_id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 0}, {"name": "client_id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 0}, {"name": "status", "type": "VARCHAR(20)", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["consultant_id"], "referred_schema": null, "referred_table": "users", "referred_columns": ["id"], "options": {}}, {"name": null, "constrained_columns": ["client_id"], "referred_schema": null, "referred_table": "users", "referred_columns": ["id"], "options": {}}], "indexes": [{"name": "ix_consultant_clients_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 6}}, "documents": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，6 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 custom_id 没有索引，可能影响查询性能", "外键列 health_record_id 没有索引，可能影响查询性能", "外键列 medical_record_id 没有索引，可能影响查询性能", "外键列 lab_report_id 没有索引，可能影响查询性能", "外键列 examination_report_id 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "custom_id", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "health_record_id", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}, {"name": "medical_record_id", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}, {"name": "lab_report_id", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}, {"name": "examination_report_id", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}, {"name": "filename", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "file_path", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "file_size", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 0}, {"name": "file_type", "type": "VARCHAR(5)", "nullable": false, "default": null, "primary_key": 0}, {"name": "document_category", "type": "VARCHAR(18)", "nullable": true, "default": null, "primary_key": 0}, {"name": "mime_type", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "description", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "source", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "ocr_processed", "type": "BOOLEAN", "nullable": true, "default": null, "primary_key": 0}, {"name": "ocr_content", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "ocr_status", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "ocr_confidence", "type": "FLOAT", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": "CURRENT_TIMESTAMP", "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["custom_id"], "referred_schema": null, "referred_table": "users", "referred_columns": ["custom_id"], "options": {"ondelete": "CASCADE"}}, {"name": null, "constrained_columns": ["health_record_id"], "referred_schema": null, "referred_table": "health_records", "referred_columns": ["id"], "options": {"ondelete": "CASCADE"}}, {"name": null, "constrained_columns": ["medical_record_id"], "referred_schema": null, "referred_table": "medical_records", "referred_columns": ["id"], "options": {"ondelete": "CASCADE"}}, {"name": null, "constrained_columns": ["lab_report_id"], "referred_schema": null, "referred_table": "lab_reports", "referred_columns": ["id"], "options": {"ondelete": "CASCADE"}}, {"name": null, "constrained_columns": ["examination_report_id"], "referred_schema": null, "referred_table": "examination_reports", "referred_columns": ["id"], "options": {"ondelete": "CASCADE"}}], "indexes": [{"name": "ix_documents_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 20}}, "examination_reports": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，2 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 custom_id 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "custom_id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 0}, {"name": "exam_type", "type": "VARCHAR(18)", "nullable": false, "default": null, "primary_key": 0}, {"name": "hospital_name", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "department", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "exam_part", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "exam_date", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "report_date", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "device", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "doctor_name", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "description", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "conclusion", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "recommendation", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "notes", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "is_abnormal", "type": "BOOLEAN", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": "CURRENT_TIMESTAMP", "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["custom_id"], "referred_schema": null, "referred_table": "users", "referred_columns": ["custom_id"], "options": {}}], "indexes": [{"name": "ix_examination_reports_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 17}}, "follow_up_records": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，3 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 custom_id 没有索引，可能影响查询性能", "外键列 consultant_id 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "custom_id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 0}, {"name": "consultant_id", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}, {"name": "follow_up_type", "type": "VARCHAR(15)", "nullable": false, "default": null, "primary_key": 0}, {"name": "title", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "content", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "follow_up_date", "type": "DATETIME", "nullable": false, "default": null, "primary_key": 0}, {"name": "next_follow_up_date", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "recommendations", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "notes", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": "CURRENT_TIMESTAMP", "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["custom_id"], "referred_schema": null, "referred_table": "users", "referred_columns": ["custom_id"], "options": {}}, {"name": null, "constrained_columns": ["consultant_id"], "referred_schema": null, "referred_table": "users", "referred_columns": ["custom_id"], "options": {}}], "indexes": [{"name": "ix_follow_up_records_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 12}}, "health_diaries": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，2 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 custom_id 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "custom_id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 0}, {"name": "diary_type", "type": "VARCHAR(10)", "nullable": false, "default": null, "primary_key": 0}, {"name": "title", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "content", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "mood_level", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}, {"name": "energy_level", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}, {"name": "pain_level", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}, {"name": "diary_date", "type": "DATETIME", "nullable": false, "default": null, "primary_key": 0}, {"name": "is_important", "type": "BOOLEAN", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": "CURRENT_TIMESTAMP", "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["custom_id"], "referred_schema": null, "referred_table": "users", "referred_columns": ["custom_id"], "options": {}}], "indexes": [{"name": "ix_health_diaries_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 12}}, "health_overviews": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，2 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 custom_id 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "custom_id", "type": "VARCHAR(20)", "nullable": false, "default": null, "primary_key": 0}, {"name": "height", "type": "FLOAT", "nullable": true, "default": null, "primary_key": 0}, {"name": "weight", "type": "FLOAT", "nullable": true, "default": null, "primary_key": 0}, {"name": "blood_type", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "allergies", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "chronic_diseases", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "family_history", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "current_medications", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "lifestyle_summary", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": "CURRENT_TIMESTAMP", "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["custom_id"], "referred_schema": null, "referred_table": "users", "referred_columns": ["custom_id"], "options": {}}], "indexes": [{"name": "ix_health_overviews_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [{"name": null, "column_names": ["custom_id"]}], "check_constraints": [], "column_count": 12}}, "health_records": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，2 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 custom_id 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "custom_id", "type": "VARCHAR(20)", "nullable": false, "default": null, "primary_key": 0}, {"name": "record_type", "type": "VARCHAR(14)", "nullable": false, "default": null, "primary_key": 0}, {"name": "title", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "description", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "content", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "is_important", "type": "BOOLEAN", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": "CURRENT_TIMESTAMP", "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["custom_id"], "referred_schema": null, "referred_table": "users", "referred_columns": ["custom_id"], "options": {}}], "indexes": [{"name": "ix_health_records_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 9}}, "imaging_records": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，2 个警告", "issues": [], "warnings": ["外键列 custom_id 没有索引，可能影响查询性能", "外键列 registration_record_id 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "custom_id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 0}, {"name": "registration_record_id", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}, {"name": "hospital_name", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "department", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "exam_type", "type": "VARCHAR(10)", "nullable": false, "default": null, "primary_key": 0}, {"name": "exam_name", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "body_part", "type": "VARCHAR(10)", "nullable": false, "default": null, "primary_key": 0}, {"name": "exam_date", "type": "DATETIME", "nullable": false, "default": null, "primary_key": 0}, {"name": "result_date", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "result_summary", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "is_abnormal", "type": "BOOLEAN", "nullable": false, "default": null, "primary_key": 0}, {"name": "report_status", "type": "VARCHAR(11)", "nullable": false, "default": null, "primary_key": 0}, {"name": "urgency_level", "type": "VARCHAR(7)", "nullable": false, "default": null, "primary_key": 0}, {"name": "notes", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": false, "default": null, "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["custom_id"], "referred_schema": null, "referred_table": "users", "referred_columns": ["custom_id"], "options": {}}, {"name": null, "constrained_columns": ["registration_record_id"], "referred_schema": null, "referred_table": "registration_records", "referred_columns": ["id"], "options": {}}], "indexes": [{"name": "ix_imaging_records_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 17}}, "imaging_reports": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，2 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 custom_id 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "custom_id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 0}, {"name": "report_type", "type": "VARCHAR(11)", "nullable": false, "default": null, "primary_key": 0}, {"name": "body_part", "type": "VARCHAR(10)", "nullable": false, "default": null, "primary_key": 0}, {"name": "hospital_name", "type": "VARCHAR(255)", "nullable": false, "default": null, "primary_key": 0}, {"name": "department", "type": "VARCHAR(255)", "nullable": true, "default": null, "primary_key": 0}, {"name": "doctor_name", "type": "VARCHAR(255)", "nullable": true, "default": null, "primary_key": 0}, {"name": "exam_date", "type": "DATETIME", "nullable": false, "default": null, "primary_key": 0}, {"name": "report_date", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "findings", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "impression", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "recommendations", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "is_abnormal", "type": "BOOLEAN", "nullable": true, "default": null, "primary_key": 0}, {"name": "notes", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": "CURRENT_TIMESTAMP", "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["custom_id"], "referred_schema": null, "referred_table": "users", "referred_columns": ["custom_id"], "options": {}}], "indexes": [{"name": "ix_imaging_reports_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 16}}, "inpatient_records": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，3 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 custom_id 没有索引，可能影响查询性能", "外键列 medical_record_id 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "custom_id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 0}, {"name": "medical_record_id", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}, {"name": "hospital_name", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "department", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "admission_date", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "discharge_date", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "admission_diagnosis", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "discharge_diagnosis", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "treatment_summary", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "doctor_advice", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "notes", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": "CURRENT_TIMESTAMP", "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["custom_id"], "referred_schema": null, "referred_table": "users", "referred_columns": ["custom_id"], "options": {}}, {"name": null, "constrained_columns": ["medical_record_id"], "referred_schema": null, "referred_table": "medical_records", "referred_columns": ["id"], "options": {}}], "indexes": [{"name": "ix_inpatient_records_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 14}}, "lab_report_items": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，2 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 report_id 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "report_id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 0}, {"name": "item_name", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "item_value", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "item_unit", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "reference_range", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "is_abnormal", "type": "BOOLEAN", "nullable": true, "default": null, "primary_key": 0}, {"name": "abnormal_level", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "notes", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": "CURRENT_TIMESTAMP", "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["report_id"], "referred_schema": null, "referred_table": "lab_reports", "referred_columns": ["id"], "options": {}}], "indexes": [{"name": "ix_lab_report_items_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 11}}, "lab_reports": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，2 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 custom_id 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "custom_id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 0}, {"name": "report_type", "type": "VARCHAR(16)", "nullable": false, "default": null, "primary_key": 0}, {"name": "hospital_name", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "department", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "test_date", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "report_date", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "doctor_name", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "diagnosis", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "notes", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "is_abnormal", "type": "BOOLEAN", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": "CURRENT_TIMESTAMP", "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["custom_id"], "referred_schema": null, "referred_table": "users", "referred_columns": ["custom_id"], "options": {}}], "indexes": [{"name": "ix_lab_reports_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 13}}, "laboratory_records": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，2 个警告", "issues": [], "warnings": ["外键列 custom_id 没有索引，可能影响查询性能", "外键列 registration_record_id 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "custom_id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 0}, {"name": "registration_record_id", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}, {"name": "hospital_name", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "department", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "test_type", "type": "VARCHAR(15)", "nullable": false, "default": null, "primary_key": 0}, {"name": "test_name", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "test_date", "type": "DATETIME", "nullable": false, "default": null, "primary_key": 0}, {"name": "result_date", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "result_summary", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "is_abnormal", "type": "BOOLEAN", "nullable": false, "default": null, "primary_key": 0}, {"name": "reference_range", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "report_status", "type": "VARCHAR(11)", "nullable": false, "default": null, "primary_key": 0}, {"name": "urgency_level", "type": "VARCHAR(7)", "nullable": false, "default": null, "primary_key": 0}, {"name": "notes", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": false, "default": null, "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["custom_id"], "referred_schema": null, "referred_table": "users", "referred_columns": ["custom_id"], "options": {}}, {"name": null, "constrained_columns": ["registration_record_id"], "referred_schema": null, "referred_table": "registration_records", "referred_columns": ["id"], "options": {}}], "indexes": [{"name": "ix_laboratory_records_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 17}}, "medical_records": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，2 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 custom_id 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "custom_id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 0}, {"name": "record_type", "type": "VARCHAR(10)", "nullable": false, "default": null, "primary_key": 0}, {"name": "hospital_name", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "department", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "doctor_name", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "visit_date", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "diagnosis", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "treatment", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "prescription", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "notes", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "is_important", "type": "BOOLEAN", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": "CURRENT_TIMESTAMP", "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["custom_id"], "referred_schema": null, "referred_table": "users", "referred_columns": ["custom_id"], "options": {}}], "indexes": [{"name": "ix_medical_records_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 14}}, "medication_usages": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，2 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 medication_id 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "medication_id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 0}, {"name": "taken_at", "type": "DATETIME", "nullable": false, "default": null, "primary_key": 0}, {"name": "dosage_taken", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "notes", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": "CURRENT_TIMESTAMP", "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["medication_id"], "referred_schema": null, "referred_table": "medications", "referred_columns": ["id"], "options": {}}], "indexes": [{"name": "ix_medication_usages_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 6}}, "medications": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，2 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 custom_id 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "custom_id", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "name", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "dosage", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "frequency", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "start_date", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "end_date", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "instructions", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "prescription_required", "type": "BOOLEAN", "nullable": true, "default": null, "primary_key": 0}, {"name": "notes", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "medication_type", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": "CURRENT_TIMESTAMP", "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["custom_id"], "referred_schema": null, "referred_table": "users", "referred_columns": ["custom_id"], "options": {}}], "indexes": [{"name": "ix_medications_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 13}}, "operation_logs": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，2 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 custom_id 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "custom_id", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}, {"name": "operation_type", "type": "VARCHAR(10)", "nullable": false, "default": null, "primary_key": 0}, {"name": "target_type", "type": "VARCHAR(14)", "nullable": false, "default": null, "primary_key": 0}, {"name": "target_id", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "description", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "details", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "ip_address", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "user_agent", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "status", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": "CURRENT_TIMESTAMP", "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["custom_id"], "referred_schema": null, "referred_table": "users", "referred_columns": ["custom_id"], "options": {}}], "indexes": [{"name": "ix_operation_logs_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 11}}, "other_records": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，2 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 custom_id 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "custom_id", "type": "VARCHAR(20)", "nullable": false, "default": null, "primary_key": 0}, {"name": "record_type", "type": "VARCHAR(14)", "nullable": true, "default": null, "primary_key": 0}, {"name": "title", "type": "VARCHAR(255)", "nullable": false, "default": null, "primary_key": 0}, {"name": "description", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "content", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "record_date", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "is_important", "type": "BOOLEAN", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": "CURRENT_TIMESTAMP", "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["custom_id"], "referred_schema": null, "referred_table": "users", "referred_columns": ["custom_id"], "options": {}}], "indexes": [{"name": "ix_other_records_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 10}}, "prescription_records": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，2 个警告", "issues": [], "warnings": ["外键列 custom_id 没有索引，可能影响查询性能", "外键列 registration_record_id 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "custom_id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 0}, {"name": "registration_record_id", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}, {"name": "hospital_name", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "department", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "doctor_name", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "prescription_date", "type": "DATETIME", "nullable": false, "default": null, "primary_key": 0}, {"name": "diagnosis", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "medication_list", "type": "TEXT", "nullable": false, "default": null, "primary_key": 0}, {"name": "dosage_instructions", "type": "TEXT", "nullable": false, "default": null, "primary_key": 0}, {"name": "prescription_number", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "is_long_term", "type": "BOOLEAN", "nullable": false, "default": null, "primary_key": 0}, {"name": "refill_count", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}, {"name": "notes", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": false, "default": null, "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["custom_id"], "referred_schema": null, "referred_table": "users", "referred_columns": ["custom_id"], "options": {}}, {"name": null, "constrained_columns": ["registration_record_id"], "referred_schema": null, "referred_table": "registration_records", "referred_columns": ["id"], "options": {}}], "indexes": [{"name": "ix_prescription_records_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 16}}, "questionnaire_answers": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，2 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 response_id 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "response_id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 0}, {"name": "question_id", "type": "VARCHAR(50)", "nullable": false, "default": null, "primary_key": 0}, {"name": "answer", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "score", "type": "FLOAT", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["response_id"], "referred_schema": null, "referred_table": "questionnaire_responses", "referred_columns": ["id"], "options": {"ondelete": "CASCADE"}}], "indexes": [{"name": "ix_questionnaire_answers_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 7}}, "questionnaire_distributions": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，4 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 questionnaire_id 没有索引，可能影响查询性能", "外键列 custom_id 没有索引，可能影响查询性能", "外键列 distributor_custom_id 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "questionnaire_id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 0}, {"name": "custom_id", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "distributor_custom_id", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "status", "type": "VARCHAR(50)", "nullable": true, "default": null, "primary_key": 0}, {"name": "due_date", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "completed_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "message", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "description", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "instructions", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "template_id", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["questionnaire_id"], "referred_schema": null, "referred_table": "questionnaires", "referred_columns": ["id"], "options": {"ondelete": "CASCADE"}}, {"name": null, "constrained_columns": ["custom_id"], "referred_schema": null, "referred_table": "users", "referred_columns": ["custom_id"], "options": {"ondelete": "CASCADE"}}, {"name": null, "constrained_columns": ["distributor_custom_id"], "referred_schema": null, "referred_table": "users", "referred_columns": ["custom_id"], "options": {"ondelete": "SET NULL"}}], "indexes": [{"name": "ix_questionnaire_distributions_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 13}}, "questionnaire_items": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，2 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 questionnaire_id 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "questionnaire_id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 0}, {"name": "question_id", "type": "VARCHAR(50)", "nullable": false, "default": null, "primary_key": 0}, {"name": "question_text", "type": "TEXT", "nullable": false, "default": null, "primary_key": 0}, {"name": "answer", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "score", "type": "FLOAT", "nullable": true, "default": null, "primary_key": 0}, {"name": "notes", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "question_type", "type": "VARCHAR(50)", "nullable": true, "default": null, "primary_key": 0}, {"name": "options", "type": "JSON", "nullable": true, "default": null, "primary_key": 0}, {"name": "order", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}, {"name": "is_required", "type": "BOOLEAN", "nullable": true, "default": null, "primary_key": 0}, {"name": "jump_logic", "type": "JSON", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "dimensions", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "dimension_key", "type": "VARCHAR(100)", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["questionnaire_id"], "referred_schema": null, "referred_table": "questionnaires", "referred_columns": ["id"], "options": {"ondelete": "CASCADE"}}], "indexes": [{"name": "idx_questionnaire_items_dimension_key", "column_names": ["dimension_key"], "unique": 0, "dialect_options": {}}, {"name": "ix_questionnaire_items_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 16}}, "questionnaire_responses": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，3 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 questionnaire_id 没有索引，可能影响查询性能", "外键列 custom_id 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "questionnaire_id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 0}, {"name": "custom_id", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "total_score", "type": "FLOAT", "nullable": true, "default": null, "primary_key": 0}, {"name": "status", "type": "VARCHAR(20)", "nullable": true, "default": null, "primary_key": 0}, {"name": "answers", "type": "JSON", "nullable": true, "default": null, "primary_key": 0}, {"name": "report", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "dimensions", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "dimension_key", "type": "VARCHAR(100)", "nullable": true, "default": null, "primary_key": 0}, {"name": "dimension_scores", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["questionnaire_id"], "referred_schema": null, "referred_table": "questionnaires", "referred_columns": ["id"], "options": {"ondelete": "CASCADE"}}, {"name": null, "constrained_columns": ["custom_id"], "referred_schema": null, "referred_table": "users", "referred_columns": ["custom_id"], "options": {}}], "indexes": [{"name": "idx_questionnaire_responses_dimension_key", "column_names": ["dimension_key"], "unique": 0, "dialect_options": {}}, {"name": "ix_questionnaire_responses_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 12}}, "questionnaire_results": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，5 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 questionnaire_id 没有索引，可能影响查询性能", "外键列 response_id 没有索引，可能影响查询性能", "外键列 custom_id 没有索引，可能影响查询性能", "外键列 template_id 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "questionnaire_id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 0}, {"name": "response_id", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}, {"name": "custom_id", "type": "VARCHAR(50)", "nullable": false, "default": null, "primary_key": 0}, {"name": "template_id", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}, {"name": "total_score", "type": "FLOAT", "nullable": true, "default": null, "primary_key": 0}, {"name": "max_score", "type": "FLOAT", "nullable": true, "default": null, "primary_key": 0}, {"name": "percentage", "type": "FLOAT", "nullable": true, "default": null, "primary_key": 0}, {"name": "result_level", "type": "VARCHAR(50)", "nullable": true, "default": null, "primary_key": 0}, {"name": "result_category", "type": "VARCHAR(100)", "nullable": true, "default": null, "primary_key": 0}, {"name": "interpretation", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "recommendations", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "dimension_scores", "type": "JSON", "nullable": true, "default": null, "primary_key": 0}, {"name": "calculation_details", "type": "JSON", "nullable": true, "default": null, "primary_key": 0}, {"name": "raw_answers", "type": "JSON", "nullable": true, "default": null, "primary_key": 0}, {"name": "report_generated", "type": "BOOLEAN", "nullable": true, "default": null, "primary_key": 0}, {"name": "report_content", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "report_format", "type": "VARCHAR(20)", "nullable": true, "default": null, "primary_key": 0}, {"name": "report_template", "type": "VARCHAR(100)", "nullable": true, "default": null, "primary_key": 0}, {"name": "status", "type": "VARCHAR(20)", "nullable": true, "default": null, "primary_key": 0}, {"name": "calculated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["questionnaire_id"], "referred_schema": null, "referred_table": "questionnaires", "referred_columns": ["id"], "options": {"ondelete": "CASCADE"}}, {"name": null, "constrained_columns": ["response_id"], "referred_schema": null, "referred_table": "questionnaire_responses", "referred_columns": ["id"], "options": {"ondelete": "CASCADE"}}, {"name": null, "constrained_columns": ["custom_id"], "referred_schema": null, "referred_table": "users", "referred_columns": ["custom_id"], "options": {}}, {"name": null, "constrained_columns": ["template_id"], "referred_schema": null, "referred_table": "questionnaire_templates", "referred_columns": ["id"], "options": {}}], "indexes": [{"name": "ix_questionnaire_results_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 23}}, "questionnaire_template_questions": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，2 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 template_id 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "template_id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 0}, {"name": "question_id", "type": "VARCHAR(50)", "nullable": false, "default": null, "primary_key": 0}, {"name": "question_text", "type": "TEXT", "nullable": false, "default": null, "primary_key": 0}, {"name": "question_type", "type": "VARCHAR(50)", "nullable": true, "default": null, "primary_key": 0}, {"name": "options", "type": "JSON", "nullable": true, "default": null, "primary_key": 0}, {"name": "order", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}, {"name": "is_required", "type": "BOOLEAN", "nullable": true, "default": null, "primary_key": 0}, {"name": "jump_logic", "type": "JSON", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "dimensions", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "dimension_key", "type": "VARCHAR(100)", "nullable": true, "default": null, "primary_key": 0}, {"name": "scoring", "type": "JSON", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["template_id"], "referred_schema": null, "referred_table": "questionnaire_templates", "referred_columns": ["id"], "options": {"ondelete": "CASCADE"}}], "indexes": [{"name": "idx_questionnaire_template_questions_dimension_key", "column_names": ["dimension_key"], "unique": 0, "dialect_options": {}}, {"name": "ix_questionnaire_template_questions_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 14}}, "questionnaire_templates": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，2 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 created_by 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "template_key", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "name", "type": "VARCHAR(100)", "nullable": false, "default": null, "primary_key": 0}, {"name": "questionnaire_type", "type": "VARCHAR(50)", "nullable": false, "default": null, "primary_key": 0}, {"name": "version", "type": "VARCHAR(20)", "nullable": true, "default": null, "primary_key": 0}, {"name": "description", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "instructions", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "is_active", "type": "BOOLEAN", "nullable": true, "default": null, "primary_key": 0}, {"name": "status", "type": "VARCHAR(20)", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_by", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}, {"name": "dimensions", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "dimension_key", "type": "VARCHAR(100)", "nullable": true, "default": null, "primary_key": 0}, {"name": "scoring_method", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "max_score", "type": "FLOAT", "nullable": true, "default": null, "primary_key": 0}, {"name": "result_ranges", "type": "JSON", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["created_by"], "referred_schema": null, "referred_table": "users", "referred_columns": ["id"], "options": {}}], "indexes": [{"name": "idx_questionnaire_templates_dimension_key", "column_names": ["dimension_key"], "unique": 0, "dialect_options": {}}, {"name": "ix_questionnaire_templates_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}, {"name": "ix_questionnaire_templates_questionnaire_type", "column_names": ["questionnaire_type"], "unique": 0, "dialect_options": {}}, {"name": "ix_questionnaire_templates_template_key", "column_names": ["template_key"], "unique": 1, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 17}}, "questionnaires": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，3 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 template_id 没有索引，可能影响查询性能", "外键列 created_by 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "title", "type": "VARCHAR(200)", "nullable": false, "default": null, "primary_key": 0}, {"name": "description", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "max_score", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}, {"name": "template_id", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}, {"name": "custom_id", "type": "VARCHAR(50)", "nullable": false, "default": null, "primary_key": 0}, {"name": "questionnaire_type", "type": "VARCHAR(50)", "nullable": true, "default": null, "primary_key": 0}, {"name": "notes", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "status", "type": "VARCHAR(20)", "nullable": true, "default": null, "primary_key": 0}, {"name": "version", "type": "VARCHAR(20)", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_by", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["template_id"], "referred_schema": null, "referred_table": "questionnaire_templates", "referred_columns": ["id"], "options": {}}, {"name": null, "constrained_columns": ["created_by"], "referred_schema": null, "referred_table": "users", "referred_columns": ["id"], "options": {}}], "indexes": [{"name": "ix_questionnaires_custom_id", "column_names": ["custom_id"], "unique": 0, "dialect_options": {}}, {"name": "ix_questionnaires_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}, {"name": "ix_questionnaires_questionnaire_type", "column_names": ["questionnaire_type"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 13}}, "registration_records": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，1 个警告", "issues": [], "warnings": ["外键列 custom_id 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "custom_id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 0}, {"name": "hospital_name", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "department", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "doctor_name", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "registration_date", "type": "DATETIME", "nullable": false, "default": null, "primary_key": 0}, {"name": "appointment_date", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "registration_fee", "type": "FLOAT", "nullable": true, "default": null, "primary_key": 0}, {"name": "status", "type": "VARCHAR(9)", "nullable": false, "default": null, "primary_key": 0}, {"name": "notes", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": false, "default": null, "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["custom_id"], "referred_schema": null, "referred_table": "users", "referred_columns": ["custom_id"], "options": {}}], "indexes": [{"name": "ix_registration_records_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 12}}, "report_templates": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，2 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 created_by 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "name", "type": "VARCHAR(100)", "nullable": false, "default": null, "primary_key": 0}, {"name": "template_type", "type": "VARCHAR(50)", "nullable": false, "default": null, "primary_key": 0}, {"name": "target_type", "type": "VARCHAR(50)", "nullable": true, "default": null, "primary_key": 0}, {"name": "template_content", "type": "TEXT", "nullable": false, "default": null, "primary_key": 0}, {"name": "css_styles", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "variables", "type": "JSON", "nullable": true, "default": null, "primary_key": 0}, {"name": "is_default", "type": "BOOLEAN", "nullable": true, "default": null, "primary_key": 0}, {"name": "is_active", "type": "BOOLEAN", "nullable": true, "default": null, "primary_key": 0}, {"name": "version", "type": "VARCHAR(20)", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_by", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["created_by"], "referred_schema": null, "referred_table": "users", "referred_columns": ["id"], "options": {}}], "indexes": [{"name": "ix_report_templates_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 13}}, "role_applications": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，3 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 custom_id 没有索引，可能影响查询性能", "外键列 processed_by 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "custom_id", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "current_role", "type": "VARCHAR(20)", "nullable": false, "default": null, "primary_key": 0}, {"name": "requested_role", "type": "VARCHAR(20)", "nullable": false, "default": null, "primary_key": 0}, {"name": "reason", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "status", "type": "VARCHAR(20)", "nullable": true, "default": null, "primary_key": 0}, {"name": "documents", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "processed_by", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}, {"name": "processed_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": "CURRENT_TIMESTAMP", "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "inactive", "type": "BOOLEAN", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["custom_id"], "referred_schema": null, "referred_table": "users", "referred_columns": ["custom_id"], "options": {"ondelete": "CASCADE"}}, {"name": null, "constrained_columns": ["processed_by"], "referred_schema": null, "referred_table": "users", "referred_columns": ["id"], "options": {"ondelete": "SET NULL"}}], "indexes": [{"name": "ix_role_applications_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 12}}, "service_stats": {"status": "ok", "message": "表结构检查完成，发现 0 个问题，0 个警告", "issues": [], "warnings": [], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "timestamp", "type": "DATETIME", "nullable": false, "default": null, "primary_key": 0}, {"name": "request_count", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 0}, {"name": "active_users", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 0}, {"name": "avg_response_time", "type": "FLOAT", "nullable": false, "default": null, "primary_key": 0}, {"name": "error_count", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 0}, {"name": "service_name", "type": "VARCHAR(100)", "nullable": true, "default": null, "primary_key": 0}, {"name": "endpoint", "type": "VARCHAR(200)", "nullable": true, "default": null, "primary_key": 0}, {"name": "method", "type": "VARCHAR(10)", "nullable": true, "default": null, "primary_key": 0}, {"name": "status_code", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}, {"name": "cpu_usage", "type": "FLOAT", "nullable": true, "default": null, "primary_key": 0}, {"name": "memory_usage", "type": "FLOAT", "nullable": true, "default": null, "primary_key": 0}, {"name": "disk_usage", "type": "FLOAT", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [], "indexes": [{"name": "ix_service_stats_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}, {"name": "ix_service_stats_timestamp", "column_names": ["timestamp"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 13}}, "surgery_records": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，3 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值", "外键列 custom_id 没有索引，可能影响查询性能", "外键列 medical_record_id 没有索引，可能影响查询性能"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "custom_id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 0}, {"name": "medical_record_id", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 0}, {"name": "hospital_name", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "department", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "surgery_name", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "surgery_date", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "surgeon", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "anesthesia_type", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "surgery_description", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "complications", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "notes", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": "CURRENT_TIMESTAMP", "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [{"name": null, "constrained_columns": ["custom_id"], "referred_schema": null, "referred_table": "users", "referred_columns": ["custom_id"], "options": {}}, {"name": null, "constrained_columns": ["medical_record_id"], "referred_schema": null, "referred_table": "medical_records", "referred_columns": ["id"], "options": {}}], "indexes": [{"name": "ix_surgery_records_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 14}}, "test_table": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，1 个警告", "issues": [], "warnings": ["关键列 id 允许NULL值"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": true, "default": null, "primary_key": 1}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [], "indexes": [], "unique_constraints": [], "check_constraints": [], "column_count": 1}}, "users": {"status": "warning", "message": "表结构检查完成，发现 0 个问题，1 个警告", "issues": [], "warnings": ["关键列 created_at 允许NULL值"], "details": {"columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": null, "primary_key": 1}, {"name": "username", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "email", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "phone", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "hashed_password", "type": "VARCHAR", "nullable": false, "default": null, "primary_key": 0}, {"name": "full_name", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "role", "type": "VARCHAR(20)", "nullable": true, "default": null, "primary_key": 0}, {"name": "is_active", "type": "BOOLEAN", "nullable": true, "default": null, "primary_key": 0}, {"name": "is_superuser", "type": "BOOLEAN", "nullable": true, "default": null, "primary_key": 0}, {"name": "id_number", "type": "VARCHAR(18)", "nullable": true, "default": null, "primary_key": 0}, {"name": "gender", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "birth_date", "type": "DATE", "nullable": true, "default": null, "primary_key": 0}, {"name": "address", "type": "VARCHAR", "nullable": true, "default": null, "primary_key": 0}, {"name": "custom_id", "type": "VARCHAR(20)", "nullable": true, "default": null, "primary_key": 0}, {"name": "profile_photo", "type": "VARCHAR(255)", "nullable": true, "default": null, "primary_key": 0}, {"name": "emergency_contact", "type": "VARCHAR(50)", "nullable": true, "default": null, "primary_key": 0}, {"name": "emergency_phone", "type": "VARCHAR(20)", "nullable": true, "default": null, "primary_key": 0}, {"name": "registration_type", "type": "VARCHAR(20)", "nullable": true, "default": null, "primary_key": 0}, {"name": "relationship_type", "type": "VARCHAR(50)", "nullable": true, "default": null, "primary_key": 0}, {"name": "additional_roles", "type": "TEXT", "nullable": true, "default": null, "primary_key": 0}, {"name": "verification_status", "type": "VARCHAR(20)", "nullable": true, "default": null, "primary_key": 0}, {"name": "is_first_login", "type": "BOOLEAN", "nullable": true, "default": null, "primary_key": 0}, {"name": "role_application_status", "type": "VARCHAR(20)", "nullable": true, "default": null, "primary_key": 0}, {"name": "role_application_role", "type": "VARCHAR(20)", "nullable": true, "default": null, "primary_key": 0}, {"name": "password_reset_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}, {"name": "created_at", "type": "DATETIME", "nullable": true, "default": "CURRENT_TIMESTAMP", "primary_key": 0}, {"name": "updated_at", "type": "DATETIME", "nullable": true, "default": null, "primary_key": 0}], "primary_keys": {"constrained_columns": ["id"], "name": null}, "foreign_keys": [], "indexes": [{"name": "ix_users_custom_id", "column_names": ["custom_id"], "unique": 1, "dialect_options": {}}, {"name": "ix_users_email", "column_names": ["email"], "unique": 0, "dialect_options": {}}, {"name": "ix_users_full_name", "column_names": ["full_name"], "unique": 0, "dialect_options": {}}, {"name": "ix_users_id", "column_names": ["id"], "unique": 0, "dialect_options": {}}, {"name": "ix_users_id_number", "column_names": ["id_number"], "unique": 1, "dialect_options": {}}, {"name": "ix_users_phone", "column_names": ["phone"], "unique": 0, "dialect_options": {}}, {"name": "ix_users_username", "column_names": ["username"], "unique": 1, "dialect_options": {}}], "unique_constraints": [], "check_constraints": [], "column_count": 27}}}, "relationships": {}, "indexes": {}, "constraints": {}, "data_integrity": {}, "summary": {}, "supported_tables": {"status": "error", "message": "支持表检查完成，发现 1 个问题，26 个警告", "issues": ["支持列表中的表 user_health_records 在数据库中不存在"], "warnings": ["数据库中的表 alert_channels 未在支持列表中", "数据库中的表 alert_rules 未在支持列表中", "数据库中的表 alerts 未在支持列表中", "数据库中的表 assessment_items 未在支持列表中", "数据库中的表 assessment_template_questions 未在支持列表中", "数据库中的表 assessments 未在支持列表中", "数据库中的表 consultant_clients 未在支持列表中", "数据库中的表 documents 未在支持列表中", "数据库中的表 health_overviews 未在支持列表中", "数据库中的表 imaging_records 未在支持列表中", "数据库中的表 inpatient_records 未在支持列表中", "数据库中的表 lab_report_items 未在支持列表中", "数据库中的表 laboratory_records 未在支持列表中", "数据库中的表 medication_usages 未在支持列表中", "数据库中的表 operation_logs 未在支持列表中", "数据库中的表 prescription_records 未在支持列表中", "数据库中的表 questionnaire_answers 未在支持列表中", "数据库中的表 questionnaire_items 未在支持列表中", "数据库中的表 questionnaire_template_questions 未在支持列表中", "数据库中的表 questionnaires 未在支持列表中", "数据库中的表 registration_records 未在支持列表中", "数据库中的表 report_templates 未在支持列表中", "数据库中的表 role_applications 未在支持列表中", "数据库中的表 service_stats 未在支持列表中", "数据库中的表 surgery_records 未在支持列表中", "数据库中的表 test_table 未在支持列表中"], "statistics": {"total_supported": 19, "existing_supported": 18, "missing_supported": 1, "total_database": 44, "unsupported_in_db": 26}, "details": {"existing_tables": ["users", "health_records", "questionnaire_templates", "questionnaire_results", "assessment_results", "assessment_templates", "health_diaries", "medical_records", "lab_reports", "examination_reports", "follow_up_records", "medications", "imaging_reports", "other_records", "questionnaire_responses", "assessment_responses", "questionnaire_distributions", "assessment_distributions"], "missing_tables": ["user_health_records"], "unsupported_tables": ["alert_channels", "alert_rules", "alerts", "assessment_items", "assessment_template_questions", "assessments", "consultant_clients", "documents", "health_overviews", "imaging_records", "inpatient_records", "lab_report_items", "laboratory_records", "medication_usages", "operation_logs", "prescription_records", "questionnaire_answers", "questionnaire_items", "questionnaire_template_questions", "questionnaires", "registration_records", "report_templates", "role_applications", "service_stats", "surgery_records", "test_table"]}}}