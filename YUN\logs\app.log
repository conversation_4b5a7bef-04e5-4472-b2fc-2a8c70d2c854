2025-06-30 14:52:28.899 [WARNING] [__main__] [logging_utils._log:378] - 后端配置加载失败: No module named 'app'
2025-06-30 14:52:28.901 [WARNING] [__main__] [logging_utils._log:378] - 前端配置加载失败: 'ConfigManager' object has no attribute '_update_frontend_from_env'
2025-06-30 14:52:28.902 [INFO] [__main__] [logging_utils._log:378] - 配置加载完成
2025-06-30 18:08:09.529 [INFO] [backend.app.core.db_connection] [db_connection._create_engine:191] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:08:09.529 [INFO] [backend.app.core.db_connection] [db_connection._create_engine:191] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:08:09.534 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 18:08:09.534 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 18:08:09.536 [INFO] [app.db.base_session] [base_session.<module>:44] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:08:09.536 [INFO] [app.db.base_session] [base_session.<module>:44] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:08:10.433 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'SafeConfigParser': <class 'configparser.ConfigParser'>
2025-06-30 18:08:10.433 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'SafeConfigParser': <class 'configparser.ConfigParser'>
2025-06-30 18:08:10.435 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'NativeStringIO': <class '_io.StringIO'>
2025-06-30 18:08:10.435 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'NativeStringIO': <class '_io.StringIO'>
2025-06-30 18:08:10.437 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'BytesIO': <class '_io.BytesIO'>
2025-06-30 18:08:10.437 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'BytesIO': <class '_io.BytesIO'>
2025-06-30 18:08:10.447 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'sha256_crypt' handler: <class 'passlib.handlers.sha2_crypt.sha256_crypt'>
2025-06-30 18:08:10.447 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'sha256_crypt' handler: <class 'passlib.handlers.sha2_crypt.sha256_crypt'>
2025-06-30 18:08:10.456 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'bcrypt' handler: <class 'passlib.handlers.bcrypt.bcrypt'>
2025-06-30 18:08:10.456 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'bcrypt' handler: <class 'passlib.handlers.bcrypt.bcrypt'>
2025-06-30 18:08:10.535 [INFO] [app.core.db_connection] [db_connection._create_engine:191] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:08:10.535 [INFO] [app.core.db_connection] [db_connection._create_engine:191] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:08:10.728 [INFO] [auth_service] [auth_service_unified.__init__:49] - 统一认证服务初始化完成
2025-06-30 18:08:10.728 [INFO] [auth_service] [auth_service_unified.__init__:49] - 统一认证服务初始化完成
2025-06-30 18:08:11.636 [INFO] [root] [health_monitor.<module>:23] - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-06-30 18:08:11.636 [INFO] [root] [health_monitor.<module>:23] - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-06-30 18:08:11.638 [DEBUG] [fallback_manager] [fallback_manager.register_dependency:39] - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend\\app', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-06-30 18:08:11.638 [DEBUG] [fallback_manager] [fallback_manager.register_dependency:39] - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend\\app', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-06-30 18:08:11.661 [INFO] [fallback_manager] [fallback_manager.register_dependency:51] - 依赖 psutil (psutil) 可用
2025-06-30 18:08:11.661 [INFO] [fallback_manager] [fallback_manager.register_dependency:51] - 依赖 psutil (psutil) 可用
2025-06-30 18:08:12.199 [INFO] [health_monitor] [health_monitor.__init__:110] - 健康监控器初始化完成
2025-06-30 18:08:12.199 [INFO] [health_monitor] [health_monitor.__init__:110] - 健康监控器初始化完成
2025-06-30 18:08:12.209 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 18:08:12.209 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 18:08:12.219 [INFO] [alert_manager] [alert_manager._init_alert_rules:315] - 已初始化默认告警规则
2025-06-30 18:08:12.219 [INFO] [alert_manager] [alert_manager._init_alert_rules:315] - 已初始化默认告警规则
2025-06-30 18:08:12.220 [INFO] [alert_manager] [alert_manager._init_notification_channels:333] - 已初始化默认通知渠道
2025-06-30 18:08:12.220 [INFO] [alert_manager] [alert_manager._init_notification_channels:333] - 已初始化默认通知渠道
2025-06-30 18:08:12.222 [INFO] [alert_manager] [alert_manager.__init__:258] - 告警管理器初始化完成
2025-06-30 18:08:12.222 [INFO] [alert_manager] [alert_manager.__init__:258] - 告警管理器初始化完成
2025-06-30 18:09:01.682 [INFO] [backend.app.core.db_connection] [db_connection._create_engine:191] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:09:01.682 [INFO] [backend.app.core.db_connection] [db_connection._create_engine:191] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:09:01.711 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 18:09:01.711 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 18:09:01.716 [INFO] [app.db.base_session] [base_session.<module>:44] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:09:01.716 [INFO] [app.db.base_session] [base_session.<module>:44] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:09:03.188 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'SafeConfigParser': <class 'configparser.ConfigParser'>
2025-06-30 18:09:03.188 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'SafeConfigParser': <class 'configparser.ConfigParser'>
2025-06-30 18:09:03.205 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'NativeStringIO': <class '_io.StringIO'>
2025-06-30 18:09:03.205 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'NativeStringIO': <class '_io.StringIO'>
2025-06-30 18:09:03.211 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'BytesIO': <class '_io.BytesIO'>
2025-06-30 18:09:03.211 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'BytesIO': <class '_io.BytesIO'>
2025-06-30 18:09:03.231 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'sha256_crypt' handler: <class 'passlib.handlers.sha2_crypt.sha256_crypt'>
2025-06-30 18:09:03.231 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'sha256_crypt' handler: <class 'passlib.handlers.sha2_crypt.sha256_crypt'>
2025-06-30 18:09:03.256 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'bcrypt' handler: <class 'passlib.handlers.bcrypt.bcrypt'>
2025-06-30 18:09:03.256 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'bcrypt' handler: <class 'passlib.handlers.bcrypt.bcrypt'>
2025-06-30 18:09:03.526 [INFO] [app.core.db_connection] [db_connection._create_engine:191] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:09:03.526 [INFO] [app.core.db_connection] [db_connection._create_engine:191] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:09:03.736 [INFO] [auth_service] [auth_service_unified.__init__:49] - 统一认证服务初始化完成
2025-06-30 18:09:03.736 [INFO] [auth_service] [auth_service_unified.__init__:49] - 统一认证服务初始化完成
2025-06-30 18:09:05.845 [INFO] [root] [health_monitor.<module>:23] - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-06-30 18:09:05.845 [INFO] [root] [health_monitor.<module>:23] - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-06-30 18:09:05.847 [DEBUG] [fallback_manager] [fallback_manager.register_dependency:39] - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\uvicorn.exe', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-06-30 18:09:05.847 [DEBUG] [fallback_manager] [fallback_manager.register_dependency:39] - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\uvicorn.exe', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-06-30 18:09:05.869 [INFO] [fallback_manager] [fallback_manager.register_dependency:51] - 依赖 psutil (psutil) 可用
2025-06-30 18:09:05.869 [INFO] [fallback_manager] [fallback_manager.register_dependency:51] - 依赖 psutil (psutil) 可用
2025-06-30 18:09:05.978 [INFO] [health_monitor] [health_monitor.__init__:110] - 健康监控器初始化完成
2025-06-30 18:09:05.978 [INFO] [health_monitor] [health_monitor.__init__:110] - 健康监控器初始化完成
2025-06-30 18:09:05.989 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 18:09:05.989 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 18:09:05.996 [INFO] [alert_manager] [alert_manager._init_alert_rules:315] - 已初始化默认告警规则
2025-06-30 18:09:05.996 [INFO] [alert_manager] [alert_manager._init_alert_rules:315] - 已初始化默认告警规则
2025-06-30 18:09:05.998 [INFO] [alert_manager] [alert_manager._init_notification_channels:333] - 已初始化默认通知渠道
2025-06-30 18:09:05.998 [INFO] [alert_manager] [alert_manager._init_notification_channels:333] - 已初始化默认通知渠道
2025-06-30 18:09:06.000 [INFO] [alert_manager] [alert_manager.__init__:258] - 告警管理器初始化完成
2025-06-30 18:09:06.000 [INFO] [alert_manager] [alert_manager.__init__:258] - 告警管理器初始化完成
2025-06-30 18:09:08.842 [INFO] [backend.app.main] [logging_utils._log:378] - 健康管理平台API启动
2025-06-30 18:09:08.842 [INFO] [backend.app.main] [logging_utils._log:378] - 健康管理平台API启动
2025-06-30 18:09:08.910 [INFO] [config_manager] [logging_utils._log:378] - 配置加载完成
2025-06-30 18:09:08.910 [INFO] [config_manager] [logging_utils._log:378] - 配置加载完成
2025-06-30 18:09:08.931 [INFO] [service_manager] [logging_utils._log:378] - 服务管理器初始化完成，项目根目录: C:\Users\<USER>\Desktop\health-Trea\YUN
2025-06-30 18:09:08.931 [INFO] [service_manager] [logging_utils._log:378] - 服务管理器初始化完成，项目根目录: C:\Users\<USER>\Desktop\health-Trea\YUN
2025-06-30 18:09:08.940 [INFO] [service_manager] [logging_utils._log:378] - 已初始化 2 个服务
2025-06-30 18:09:08.940 [INFO] [service_manager] [logging_utils._log:378] - 已初始化 2 个服务
2025-06-30 18:09:08.943 [INFO] [backend.app.main] [logging_utils._log:378] - 后端API服务已启动，PID: 19240，端口: 8000
2025-06-30 18:09:08.943 [INFO] [backend.app.main] [logging_utils._log:378] - 后端API服务已启动，PID: 19240，端口: 8000
2025-06-30 18:09:08.946 [INFO] [backend.app.main] [logging_utils._log:378] - 已向统一管理平台注册API服务
2025-06-30 18:09:08.946 [INFO] [backend.app.main] [logging_utils._log:378] - 已向统一管理平台注册API服务
2025-06-30 18:10:07.370 [DEBUG] [health_monitor] [health_monitor._collect_metrics:198] - 系统指标 - CPU: 100.0%, 内存: 47.0%, 磁盘: 77.6%
2025-06-30 18:10:07.370 [DEBUG] [health_monitor] [health_monitor._collect_metrics:198] - 系统指标 - CPU: 100.0%, 内存: 47.0%, 磁盘: 77.6%
2025-06-30 18:11:08.940 [DEBUG] [health_monitor] [health_monitor._collect_metrics:198] - 系统指标 - CPU: 84.5%, 内存: 46.5%, 磁盘: 77.6%
2025-06-30 18:11:08.940 [DEBUG] [health_monitor] [health_monitor._collect_metrics:198] - 系统指标 - CPU: 84.5%, 内存: 46.5%, 磁盘: 77.6%
2025-06-30 18:11:09.312 [INFO] [backend.app.main] [logging_utils._log:378] - 健康管理平台API关闭
2025-06-30 18:11:09.312 [INFO] [backend.app.main] [logging_utils._log:378] - 健康管理平台API关闭
2025-06-30 18:11:09.526 [INFO] [config_manager] [logging_utils._log:378] - 配置加载完成
2025-06-30 18:11:09.526 [INFO] [config_manager] [logging_utils._log:378] - 配置加载完成
2025-06-30 18:11:09.594 [INFO] [service_manager] [logging_utils._log:378] - 服务管理器初始化完成，项目根目录: C:\Users\<USER>\Desktop\health-Trea\YUN
2025-06-30 18:11:09.594 [INFO] [service_manager] [logging_utils._log:378] - 服务管理器初始化完成，项目根目录: C:\Users\<USER>\Desktop\health-Trea\YUN
2025-06-30 18:11:09.612 [INFO] [service_manager] [logging_utils._log:378] - 已初始化 2 个服务
2025-06-30 18:11:09.612 [INFO] [service_manager] [logging_utils._log:378] - 已初始化 2 个服务
2025-06-30 18:11:09.621 [INFO] [backend.app.main] [logging_utils._log:378] - 后端API服务已关闭，PID: 19240
2025-06-30 18:11:09.621 [INFO] [backend.app.main] [logging_utils._log:378] - 后端API服务已关闭，PID: 19240
2025-06-30 18:11:09.624 [INFO] [backend.app.main] [logging_utils._log:378] - 已向统一管理平台报告API服务关闭
2025-06-30 18:11:09.624 [INFO] [backend.app.main] [logging_utils._log:378] - 已向统一管理平台报告API服务关闭
2025-06-30 18:11:21.947 [INFO] [backend.app.core.db_connection] [db_connection._create_engine:191] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:11:21.947 [INFO] [backend.app.core.db_connection] [db_connection._create_engine:191] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:11:22.185 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 18:11:22.185 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 18:11:22.231 [INFO] [app.db.base_session] [base_session.<module>:44] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:11:22.231 [INFO] [app.db.base_session] [base_session.<module>:44] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:11:23.587 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'SafeConfigParser': <class 'configparser.ConfigParser'>
2025-06-30 18:11:23.587 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'SafeConfigParser': <class 'configparser.ConfigParser'>
2025-06-30 18:11:23.590 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'NativeStringIO': <class '_io.StringIO'>
2025-06-30 18:11:23.590 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'NativeStringIO': <class '_io.StringIO'>
2025-06-30 18:11:23.592 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'BytesIO': <class '_io.BytesIO'>
2025-06-30 18:11:23.592 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'BytesIO': <class '_io.BytesIO'>
2025-06-30 18:11:23.598 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'sha256_crypt' handler: <class 'passlib.handlers.sha2_crypt.sha256_crypt'>
2025-06-30 18:11:23.598 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'sha256_crypt' handler: <class 'passlib.handlers.sha2_crypt.sha256_crypt'>
2025-06-30 18:11:23.611 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'bcrypt' handler: <class 'passlib.handlers.bcrypt.bcrypt'>
2025-06-30 18:11:23.611 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'bcrypt' handler: <class 'passlib.handlers.bcrypt.bcrypt'>
2025-06-30 18:11:23.811 [INFO] [app.core.db_connection] [db_connection._create_engine:191] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:11:23.811 [INFO] [app.core.db_connection] [db_connection._create_engine:191] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:11:23.958 [INFO] [auth_service] [auth_service_unified.__init__:49] - 统一认证服务初始化完成
2025-06-30 18:11:23.958 [INFO] [auth_service] [auth_service_unified.__init__:49] - 统一认证服务初始化完成
2025-06-30 18:11:25.643 [INFO] [root] [health_monitor.<module>:23] - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-06-30 18:11:25.643 [INFO] [root] [health_monitor.<module>:23] - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-06-30 18:11:25.780 [DEBUG] [fallback_manager] [fallback_manager.register_dependency:39] - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\uvicorn.exe', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-06-30 18:11:25.780 [DEBUG] [fallback_manager] [fallback_manager.register_dependency:39] - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\uvicorn.exe', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-06-30 18:11:25.804 [INFO] [fallback_manager] [fallback_manager.register_dependency:51] - 依赖 psutil (psutil) 可用
2025-06-30 18:11:25.804 [INFO] [fallback_manager] [fallback_manager.register_dependency:51] - 依赖 psutil (psutil) 可用
2025-06-30 18:11:26.098 [INFO] [health_monitor] [health_monitor.__init__:110] - 健康监控器初始化完成
2025-06-30 18:11:26.098 [INFO] [health_monitor] [health_monitor.__init__:110] - 健康监控器初始化完成
2025-06-30 18:11:26.116 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 18:11:26.116 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 18:11:26.126 [INFO] [alert_manager] [alert_manager._init_alert_rules:315] - 已初始化默认告警规则
2025-06-30 18:11:26.126 [INFO] [alert_manager] [alert_manager._init_alert_rules:315] - 已初始化默认告警规则
2025-06-30 18:11:26.136 [INFO] [alert_manager] [alert_manager._init_notification_channels:333] - 已初始化默认通知渠道
2025-06-30 18:11:26.136 [INFO] [alert_manager] [alert_manager._init_notification_channels:333] - 已初始化默认通知渠道
2025-06-30 18:11:26.168 [INFO] [alert_manager] [alert_manager.__init__:258] - 告警管理器初始化完成
2025-06-30 18:11:26.168 [INFO] [alert_manager] [alert_manager.__init__:258] - 告警管理器初始化完成
2025-06-30 18:11:29.559 [INFO] [backend.app.main] [logging_utils._log:378] - 健康管理平台API启动
2025-06-30 18:11:29.559 [INFO] [backend.app.main] [logging_utils._log:378] - 健康管理平台API启动
2025-06-30 18:11:29.692 [INFO] [config_manager] [logging_utils._log:378] - 配置加载完成
2025-06-30 18:11:29.692 [INFO] [config_manager] [logging_utils._log:378] - 配置加载完成
2025-06-30 18:11:29.730 [INFO] [service_manager] [logging_utils._log:378] - 服务管理器初始化完成，项目根目录: C:\Users\<USER>\Desktop\health-Trea\YUN
2025-06-30 18:11:29.730 [INFO] [service_manager] [logging_utils._log:378] - 服务管理器初始化完成，项目根目录: C:\Users\<USER>\Desktop\health-Trea\YUN
2025-06-30 18:11:29.765 [INFO] [service_manager] [logging_utils._log:378] - 已初始化 2 个服务
2025-06-30 18:11:29.765 [INFO] [service_manager] [logging_utils._log:378] - 已初始化 2 个服务
2025-06-30 18:11:29.819 [INFO] [backend.app.main] [logging_utils._log:378] - 后端API服务已启动，PID: 16944，端口: 8000
2025-06-30 18:11:29.819 [INFO] [backend.app.main] [logging_utils._log:378] - 后端API服务已启动，PID: 16944，端口: 8000
2025-06-30 18:11:29.831 [INFO] [backend.app.main] [logging_utils._log:378] - 已向统一管理平台注册API服务
2025-06-30 18:11:29.831 [INFO] [backend.app.main] [logging_utils._log:378] - 已向统一管理平台注册API服务
2025-06-30 18:11:46.755 [INFO] [backend.app.main] [logging_utils._log:378] - 健康管理平台API关闭
2025-06-30 18:11:46.755 [INFO] [backend.app.main] [logging_utils._log:378] - 健康管理平台API关闭
2025-06-30 18:11:46.816 [INFO] [config_manager] [logging_utils._log:378] - 配置加载完成
2025-06-30 18:11:46.816 [INFO] [config_manager] [logging_utils._log:378] - 配置加载完成
2025-06-30 18:11:46.819 [INFO] [service_manager] [logging_utils._log:378] - 服务管理器初始化完成，项目根目录: C:\Users\<USER>\Desktop\health-Trea\YUN
2025-06-30 18:11:46.819 [INFO] [service_manager] [logging_utils._log:378] - 服务管理器初始化完成，项目根目录: C:\Users\<USER>\Desktop\health-Trea\YUN
2025-06-30 18:11:46.822 [INFO] [service_manager] [logging_utils._log:378] - 已初始化 2 个服务
2025-06-30 18:11:46.822 [INFO] [service_manager] [logging_utils._log:378] - 已初始化 2 个服务
2025-06-30 18:11:46.825 [INFO] [backend.app.main] [logging_utils._log:378] - 后端API服务已关闭，PID: 16944
2025-06-30 18:11:46.825 [INFO] [backend.app.main] [logging_utils._log:378] - 后端API服务已关闭，PID: 16944
2025-06-30 18:11:46.832 [INFO] [backend.app.main] [logging_utils._log:378] - 已向统一管理平台报告API服务关闭
2025-06-30 18:11:46.832 [INFO] [backend.app.main] [logging_utils._log:378] - 已向统一管理平台报告API服务关闭
2025-06-30 18:12:11.750 [INFO] [backend.app.core.db_connection] [db_connection._create_engine:191] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:12:11.750 [INFO] [backend.app.core.db_connection] [db_connection._create_engine:191] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:12:11.754 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 18:12:11.754 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 18:12:11.757 [INFO] [app.db.base_session] [base_session.<module>:44] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:12:11.757 [INFO] [app.db.base_session] [base_session.<module>:44] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:12:12.428 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'SafeConfigParser': <class 'configparser.ConfigParser'>
2025-06-30 18:12:12.428 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'SafeConfigParser': <class 'configparser.ConfigParser'>
2025-06-30 18:12:12.430 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'NativeStringIO': <class '_io.StringIO'>
2025-06-30 18:12:12.430 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'NativeStringIO': <class '_io.StringIO'>
2025-06-30 18:12:12.432 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'BytesIO': <class '_io.BytesIO'>
2025-06-30 18:12:12.432 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'BytesIO': <class '_io.BytesIO'>
2025-06-30 18:12:12.437 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'sha256_crypt' handler: <class 'passlib.handlers.sha2_crypt.sha256_crypt'>
2025-06-30 18:12:12.437 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'sha256_crypt' handler: <class 'passlib.handlers.sha2_crypt.sha256_crypt'>
2025-06-30 18:12:12.450 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'bcrypt' handler: <class 'passlib.handlers.bcrypt.bcrypt'>
2025-06-30 18:12:12.450 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'bcrypt' handler: <class 'passlib.handlers.bcrypt.bcrypt'>
2025-06-30 18:12:12.532 [INFO] [app.core.db_connection] [db_connection._create_engine:191] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:12:12.532 [INFO] [app.core.db_connection] [db_connection._create_engine:191] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:12:12.717 [INFO] [auth_service] [auth_service_unified.__init__:49] - 统一认证服务初始化完成
2025-06-30 18:12:12.717 [INFO] [auth_service] [auth_service_unified.__init__:49] - 统一认证服务初始化完成
2025-06-30 18:12:13.614 [INFO] [root] [health_monitor.<module>:23] - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-06-30 18:12:13.614 [INFO] [root] [health_monitor.<module>:23] - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-06-30 18:12:13.616 [DEBUG] [fallback_manager] [fallback_manager.register_dependency:39] - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend\\app', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-06-30 18:12:13.616 [DEBUG] [fallback_manager] [fallback_manager.register_dependency:39] - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend\\app', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-06-30 18:12:13.628 [INFO] [fallback_manager] [fallback_manager.register_dependency:51] - 依赖 psutil (psutil) 可用
2025-06-30 18:12:13.628 [INFO] [fallback_manager] [fallback_manager.register_dependency:51] - 依赖 psutil (psutil) 可用
2025-06-30 18:12:13.784 [INFO] [health_monitor] [health_monitor.__init__:110] - 健康监控器初始化完成
2025-06-30 18:12:13.784 [INFO] [health_monitor] [health_monitor.__init__:110] - 健康监控器初始化完成
2025-06-30 18:12:13.793 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 18:12:13.793 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 18:12:13.800 [INFO] [alert_manager] [alert_manager._init_alert_rules:315] - 已初始化默认告警规则
2025-06-30 18:12:13.800 [INFO] [alert_manager] [alert_manager._init_alert_rules:315] - 已初始化默认告警规则
2025-06-30 18:12:13.802 [INFO] [alert_manager] [alert_manager._init_notification_channels:333] - 已初始化默认通知渠道
2025-06-30 18:12:13.802 [INFO] [alert_manager] [alert_manager._init_notification_channels:333] - 已初始化默认通知渠道
2025-06-30 18:12:13.804 [INFO] [alert_manager] [alert_manager.__init__:258] - 告警管理器初始化完成
2025-06-30 18:12:13.804 [INFO] [alert_manager] [alert_manager.__init__:258] - 告警管理器初始化完成
2025-06-30 18:13:51.702 [INFO] [backend.app.core.db_connection] [db_connection._create_engine:191] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:13:51.702 [INFO] [backend.app.core.db_connection] [db_connection._create_engine:191] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:13:51.705 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 18:13:51.705 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 18:13:51.708 [INFO] [app.db.base_session] [base_session.<module>:44] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:13:51.708 [INFO] [app.db.base_session] [base_session.<module>:44] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:13:52.415 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'SafeConfigParser': <class 'configparser.ConfigParser'>
2025-06-30 18:13:52.415 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'SafeConfigParser': <class 'configparser.ConfigParser'>
2025-06-30 18:13:52.417 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'NativeStringIO': <class '_io.StringIO'>
2025-06-30 18:13:52.417 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'NativeStringIO': <class '_io.StringIO'>
2025-06-30 18:13:52.418 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'BytesIO': <class '_io.BytesIO'>
2025-06-30 18:13:52.418 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'BytesIO': <class '_io.BytesIO'>
2025-06-30 18:13:52.423 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'sha256_crypt' handler: <class 'passlib.handlers.sha2_crypt.sha256_crypt'>
2025-06-30 18:13:52.423 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'sha256_crypt' handler: <class 'passlib.handlers.sha2_crypt.sha256_crypt'>
2025-06-30 18:13:52.439 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'bcrypt' handler: <class 'passlib.handlers.bcrypt.bcrypt'>
2025-06-30 18:13:52.439 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'bcrypt' handler: <class 'passlib.handlers.bcrypt.bcrypt'>
2025-06-30 18:13:52.518 [INFO] [app.core.db_connection] [db_connection._create_engine:191] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:13:52.518 [INFO] [app.core.db_connection] [db_connection._create_engine:191] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:13:52.704 [INFO] [auth_service] [auth_service_unified.__init__:49] - 统一认证服务初始化完成
2025-06-30 18:13:52.704 [INFO] [auth_service] [auth_service_unified.__init__:49] - 统一认证服务初始化完成
2025-06-30 18:13:54.033 [INFO] [root] [health_monitor.<module>:23] - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-06-30 18:13:54.033 [INFO] [root] [health_monitor.<module>:23] - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-06-30 18:13:54.076 [DEBUG] [fallback_manager] [fallback_manager.register_dependency:39] - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend\\app', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-06-30 18:13:54.076 [DEBUG] [fallback_manager] [fallback_manager.register_dependency:39] - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend\\app', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-06-30 18:13:54.101 [INFO] [fallback_manager] [fallback_manager.register_dependency:51] - 依赖 psutil (psutil) 可用
2025-06-30 18:13:54.101 [INFO] [fallback_manager] [fallback_manager.register_dependency:51] - 依赖 psutil (psutil) 可用
2025-06-30 18:13:54.392 [INFO] [health_monitor] [health_monitor.__init__:110] - 健康监控器初始化完成
2025-06-30 18:13:54.392 [INFO] [health_monitor] [health_monitor.__init__:110] - 健康监控器初始化完成
2025-06-30 18:13:54.402 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 18:13:54.402 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 18:13:54.409 [INFO] [alert_manager] [alert_manager._init_alert_rules:315] - 已初始化默认告警规则
2025-06-30 18:13:54.409 [INFO] [alert_manager] [alert_manager._init_alert_rules:315] - 已初始化默认告警规则
2025-06-30 18:13:54.410 [INFO] [alert_manager] [alert_manager._init_notification_channels:333] - 已初始化默认通知渠道
2025-06-30 18:13:54.410 [INFO] [alert_manager] [alert_manager._init_notification_channels:333] - 已初始化默认通知渠道
2025-06-30 18:13:54.414 [INFO] [alert_manager] [alert_manager.__init__:258] - 告警管理器初始化完成
2025-06-30 18:13:54.414 [INFO] [alert_manager] [alert_manager.__init__:258] - 告警管理器初始化完成
2025-06-30 18:14:06.297 [INFO] [backend.app.core.db_connection] [db_connection._create_engine:191] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:14:06.297 [INFO] [backend.app.core.db_connection] [db_connection._create_engine:191] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:14:06.354 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 18:14:06.354 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 18:14:06.371 [INFO] [app.db.base_session] [base_session.<module>:44] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:14:06.371 [INFO] [app.db.base_session] [base_session.<module>:44] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:14:07.783 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'SafeConfigParser': <class 'configparser.ConfigParser'>
2025-06-30 18:14:07.783 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'SafeConfigParser': <class 'configparser.ConfigParser'>
2025-06-30 18:14:07.882 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'NativeStringIO': <class '_io.StringIO'>
2025-06-30 18:14:07.882 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'NativeStringIO': <class '_io.StringIO'>
2025-06-30 18:14:07.926 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'BytesIO': <class '_io.BytesIO'>
2025-06-30 18:14:07.926 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'BytesIO': <class '_io.BytesIO'>
2025-06-30 18:14:07.939 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'sha256_crypt' handler: <class 'passlib.handlers.sha2_crypt.sha256_crypt'>
2025-06-30 18:14:07.939 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'sha256_crypt' handler: <class 'passlib.handlers.sha2_crypt.sha256_crypt'>
2025-06-30 18:14:07.962 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'bcrypt' handler: <class 'passlib.handlers.bcrypt.bcrypt'>
2025-06-30 18:14:07.962 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'bcrypt' handler: <class 'passlib.handlers.bcrypt.bcrypt'>
2025-06-30 18:14:08.300 [INFO] [app.core.db_connection] [db_connection._create_engine:191] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:14:08.300 [INFO] [app.core.db_connection] [db_connection._create_engine:191] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-06-30 18:14:08.851 [INFO] [auth_service] [auth_service_unified.__init__:49] - 统一认证服务初始化完成
2025-06-30 18:14:08.851 [INFO] [auth_service] [auth_service_unified.__init__:49] - 统一认证服务初始化完成
2025-06-30 18:14:10.956 [INFO] [root] [health_monitor.<module>:23] - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-06-30 18:14:10.956 [INFO] [root] [health_monitor.<module>:23] - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-06-30 18:14:11.006 [DEBUG] [fallback_manager] [fallback_manager.register_dependency:39] - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend\\app', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-06-30 18:14:11.006 [DEBUG] [fallback_manager] [fallback_manager.register_dependency:39] - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend\\app', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-06-30 18:14:11.108 [INFO] [fallback_manager] [fallback_manager.register_dependency:51] - 依赖 psutil (psutil) 可用
2025-06-30 18:14:11.108 [INFO] [fallback_manager] [fallback_manager.register_dependency:51] - 依赖 psutil (psutil) 可用
2025-06-30 18:14:12.543 [INFO] [health_monitor] [health_monitor.__init__:110] - 健康监控器初始化完成
2025-06-30 18:14:12.543 [INFO] [health_monitor] [health_monitor.__init__:110] - 健康监控器初始化完成
2025-06-30 18:14:12.624 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 18:14:12.624 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-06-30 18:14:12.661 [INFO] [alert_manager] [alert_manager._init_alert_rules:315] - 已初始化默认告警规则
2025-06-30 18:14:12.661 [INFO] [alert_manager] [alert_manager._init_alert_rules:315] - 已初始化默认告警规则
2025-06-30 18:14:12.737 [INFO] [alert_manager] [alert_manager._init_notification_channels:333] - 已初始化默认通知渠道
2025-06-30 18:14:12.737 [INFO] [alert_manager] [alert_manager._init_notification_channels:333] - 已初始化默认通知渠道
2025-06-30 18:14:12.810 [INFO] [alert_manager] [alert_manager.__init__:258] - 告警管理器初始化完成
2025-06-30 18:14:12.810 [INFO] [alert_manager] [alert_manager.__init__:258] - 告警管理器初始化完成
2025-06-30 18:14:15.185 [DEBUG] [asyncio] [selector_events.__init__:64] - Using selector: SelectSelector
2025-06-30 18:14:15.185 [DEBUG] [asyncio] [selector_events.__init__:64] - Using selector: SelectSelector
2025-06-30 18:14:17.436 [INFO] [backend.app.main] [logging_utils._log:378] - 健康管理平台API启动
2025-06-30 18:14:17.436 [INFO] [backend.app.main] [logging_utils._log:378] - 健康管理平台API启动
2025-06-30 18:14:17.560 [INFO] [config_manager] [logging_utils._log:378] - 配置加载完成
2025-06-30 18:14:17.560 [INFO] [config_manager] [logging_utils._log:378] - 配置加载完成
2025-06-30 18:14:17.613 [INFO] [service_manager] [logging_utils._log:378] - 服务管理器初始化完成，项目根目录: C:\Users\<USER>\Desktop\health-Trea\YUN
2025-06-30 18:14:17.613 [INFO] [service_manager] [logging_utils._log:378] - 服务管理器初始化完成，项目根目录: C:\Users\<USER>\Desktop\health-Trea\YUN
2025-06-30 18:14:17.619 [INFO] [service_manager] [logging_utils._log:378] - 已初始化 2 个服务
2025-06-30 18:14:17.619 [INFO] [service_manager] [logging_utils._log:378] - 已初始化 2 个服务
2025-06-30 18:14:17.622 [INFO] [backend.app.main] [logging_utils._log:378] - 后端API服务已启动，PID: 17464，端口: 8000
2025-06-30 18:14:17.622 [INFO] [backend.app.main] [logging_utils._log:378] - 后端API服务已启动，PID: 17464，端口: 8000
2025-06-30 18:14:17.624 [INFO] [backend.app.main] [logging_utils._log:378] - 已向统一管理平台注册API服务
2025-06-30 18:14:17.624 [INFO] [backend.app.main] [logging_utils._log:378] - 已向统一管理平台注册API服务
2025-06-30 18:14:55.189 [DEBUG] [health_monitor] [health_monitor._collect_metrics:198] - 系统指标 - CPU: 52.5%, 内存: 46.6%, 磁盘: 77.6%
2025-06-30 18:14:55.189 [DEBUG] [health_monitor] [health_monitor._collect_metrics:198] - 系统指标 - CPU: 52.5%, 内存: 46.6%, 磁盘: 77.6%
2025-06-30 22:58:19.971 [DEBUG] [asyncio] [proactor_events.__init__:631] - Using proactor: IocpProactor
2025-06-30 22:58:19.980 [INFO] [__main__] [logging_utils._log:378] - 主管理器初始化完成，项目根目录: c:\Users\<USER>\Desktop\health-Trea\YUN
2025-06-30 22:58:19.989 [INFO] [project_manager] [logging_utils._log:378] - 项目管理器初始化完成，项目根目录: c:\Users\<USER>\Desktop\health-Trea\YUN
2025-06-30 22:58:19.996 [INFO] [project_manager] [logging_utils._log:378] - 临时目录: C:\Users\<USER>\AppData\Local\Temp\project_manager_7z4o5z61
2025-06-30 22:58:19.998 [INFO] [project_manager] [logging_utils._log:378] - 已初始化 3 个组件
2025-06-30 22:58:20.013 [WARNING] [config_manager] [logging_utils._log:378] - 后端配置加载失败: No module named 'app'
2025-06-30 22:58:20.016 [INFO] [config_manager] [logging_utils._log:378] - 配置加载完成
2025-06-30 22:58:20.018 [INFO] [test_manager] [logging_utils._log:378] - 测试管理器初始化完成，项目根目录: c:\Users\<USER>\Desktop\health-Trea\YUN
2025-06-30 22:58:20.019 [INFO] [test_manager] [logging_utils._log:378] - 临时目录: C:\Users\<USER>\AppData\Local\Temp\test_manager_1ak3r4lz
2025-06-30 22:58:20.020 [INFO] [test_manager] [logging_utils._log:378] - 已初始化 3 个测试套件
2025-06-30 22:58:20.026 [WARNING] [config_manager] [logging_utils._log:378] - 后端配置加载失败: No module named 'app'
2025-06-30 22:58:20.028 [INFO] [config_manager] [logging_utils._log:378] - 配置加载完成
2025-06-30 22:58:20.029 [INFO] [service_manager] [logging_utils._log:378] - 服务管理器初始化完成，项目根目录: c:\Users\<USER>\Desktop\health-Trea\YUN
2025-06-30 22:58:20.030 [INFO] [service_manager] [logging_utils._log:378] - 已初始化 2 个服务
2025-06-30 22:58:20.045 [WARNING] [config_manager] [logging_utils._log:378] - 后端配置加载失败: No module named 'app'
2025-06-30 22:58:20.048 [INFO] [config_manager] [logging_utils._log:378] - 配置加载完成
2025-06-30 22:58:20.054 [WARNING] [config_manager] [logging_utils._log:378] - 后端配置加载失败: No module named 'app'
2025-06-30 22:58:20.057 [INFO] [config_manager] [logging_utils._log:378] - 配置加载完成
2025-06-30 22:58:20.062 [INFO] [service_manager] [logging_utils._log:378] - 服务管理器初始化完成，项目根目录: c:\Users\<USER>\Desktop\health-Trea\YUN
2025-06-30 22:58:20.066 [INFO] [service_manager] [logging_utils._log:378] - 已初始化 2 个服务
2025-06-30 22:58:20.067 [INFO] [deployment_manager] [logging_utils._log:378] - 部署管理器初始化完成，项目根目录: c:\Users\<USER>\Desktop\health-Trea\YUN
2025-06-30 22:58:20.068 [INFO] [deployment_manager] [logging_utils._log:378] - 已初始化 2 个构建配置
2025-06-30 22:58:20.068 [INFO] [deployment_manager] [logging_utils._log:378] - 已初始化 2 个部署配置
2025-06-30 22:58:20.069 [INFO] [__main__] [logging_utils._log:378] - 所有管理器初始化完成
2025-07-01 13:53:22.380 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:53:22.478 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:53:22.518 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:53:22.564 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:53:22.635 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:53:22.653 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:53:22.659 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:53:22.664 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:53:22.672 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:53:22.685 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:53:22.700 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:53:22.706 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:53:22.709 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:53:22.710 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:53:22.712 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:53:22.714 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:53:22.716 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:53:22.718 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:53:22.722 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:53:22.732 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:53:22.738 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:53:22.741 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:53:22.745 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:53:22.750 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:53:22.768 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:53:22.770 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:53:22.779 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:53:22.783 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:53:22.784 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:53:22.786 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:53:22.787 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:53:22.790 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:53:22.795 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:53:22.796 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:53:22.798 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:53:22.801 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:53:22.807 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:53:22.809 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:53:22.815 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:53:22.831 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:53:22.837 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:53:22.838 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:53:22.840 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:53:22.842 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:53:22.844 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:53:22.855 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:53:22.864 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:53:22.868 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:53:22.870 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:53:22.871 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:53:22.874 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:53:22.875 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:53:22.877 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:53:22.879 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:53:22.881 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:53:22.887 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:53:22.889 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:53:22.891 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:53:22.893 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:53:22.895 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:53:22.899 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:53:22.901 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:53:22.902 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:53:22.904 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:53:22.905 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:53:22.910 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:53:22.912 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:53:22.914 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:53:22.916 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:53:22.917 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:53:22.920 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:53:22.922 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:53:22.929 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:53:22.951 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:53:22.954 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:53:22.957 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:53:22.961 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:53:22.962 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:53:22.964 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:53:22.967 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:53:23.064 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'sha256_crypt' handler: <class 'passlib.handlers.sha2_crypt.sha256_crypt'>
2025-07-01 13:53:23.073 [INFO] [auth_service] [auth_service_unified.__init__:49] - 统一认证服务初始化完成
2025-07-01 13:53:23.147 [INFO] [backend.app.core.db_connection] [db_connection._create_engine:191] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-01 13:53:23.150 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-01 13:53:24.642 [INFO] [root] [health_monitor.<module>:23] - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-07-01 13:53:24.645 [DEBUG] [fallback_manager] [fallback_manager.register_dependency:39] - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-07-01 13:53:24.653 [INFO] [fallback_manager] [fallback_manager.register_dependency:51] - 依赖 psutil (psutil) 可用
2025-07-01 13:53:24.836 [INFO] [health_monitor] [health_monitor.__init__:110] - 健康监控器初始化完成
2025-07-01 13:53:24.843 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-01 13:53:24.852 [INFO] [alert_manager] [alert_manager._init_alert_rules:315] - 已初始化默认告警规则
2025-07-01 13:53:24.853 [INFO] [alert_manager] [alert_manager._init_notification_channels:333] - 已初始化默认通知渠道
2025-07-01 13:53:24.855 [INFO] [alert_manager] [alert_manager.__init__:258] - 告警管理器初始化完成
2025-07-01 13:54:35.482 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'SafeConfigParser': <class 'configparser.ConfigParser'>
2025-07-01 13:54:35.485 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'NativeStringIO': <class '_io.StringIO'>
2025-07-01 13:54:35.486 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'BytesIO': <class '_io.BytesIO'>
2025-07-01 13:54:35.496 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'bcrypt' handler: <class 'passlib.handlers.bcrypt.bcrypt'>
2025-07-01 13:54:35.919 [INFO] [backend.app.core.db_connection] [db_connection._create_engine:191] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-01 13:54:35.919 [INFO] [backend.app.core.db_connection] [db_connection._create_engine:191] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-01 13:54:35.923 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-01 13:54:35.923 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-01 13:54:35.926 [INFO] [app.db.base_session] [base_session.<module>:44] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-01 13:54:35.926 [INFO] [app.db.base_session] [base_session.<module>:44] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-01 13:54:37.262 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'sha256_crypt' handler: <class 'passlib.handlers.sha2_crypt.sha256_crypt'>
2025-07-01 13:54:37.262 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'sha256_crypt' handler: <class 'passlib.handlers.sha2_crypt.sha256_crypt'>
2025-07-01 13:54:37.375 [INFO] [app.core.db_connection] [db_connection._create_engine:191] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-01 13:54:37.375 [INFO] [app.core.db_connection] [db_connection._create_engine:191] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-01 13:54:37.387 [INFO] [auth_service] [auth_service_unified.__init__:49] - 统一认证服务初始化完成
2025-07-01 13:54:37.387 [INFO] [auth_service] [auth_service_unified.__init__:49] - 统一认证服务初始化完成
2025-07-01 13:54:39.590 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:54:39.590 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:54:39.591 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:54:39.591 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:54:39.592 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:54:39.592 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:54:39.593 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:54:39.593 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:54:39.594 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:54:39.594 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:54:39.596 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:54:39.596 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:54:39.599 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:54:39.599 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:54:39.601 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:54:39.601 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:54:39.602 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:54:39.602 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:54:39.603 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:54:39.603 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:54:39.605 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:54:39.605 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:54:39.607 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:54:39.607 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:54:39.608 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:54:39.608 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:54:39.610 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:54:39.610 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:54:39.611 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:54:39.611 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:54:39.613 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:54:39.613 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:54:39.616 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:54:39.616 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:54:39.618 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:54:39.618 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:54:39.620 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:54:39.620 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:54:39.621 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:54:39.621 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:54:39.624 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:54:39.624 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:54:39.627 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:54:39.627 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:54:39.629 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:54:39.629 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:54:39.633 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:54:39.633 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:54:39.635 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:54:39.635 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:54:39.637 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:54:39.637 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:54:39.638 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:54:39.638 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:54:39.640 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:54:39.640 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:54:39.641 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:54:39.641 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:54:39.642 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:54:39.642 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:54:39.644 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:54:39.644 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:54:39.646 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:54:39.646 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:54:39.649 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:54:39.649 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:54:39.651 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:54:39.651 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:54:39.653 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:54:39.653 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:54:39.655 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:54:39.655 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:54:39.656 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:54:39.656 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:54:39.658 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:54:39.658 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:54:39.660 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:54:39.660 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:54:39.661 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:54:39.661 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:54:39.665 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:54:39.665 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:54:39.668 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:54:39.668 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:54:39.671 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:54:39.671 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:54:39.673 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:54:39.673 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:54:39.674 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:54:39.674 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:54:39.676 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:54:39.676 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:54:39.678 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:54:39.678 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:54:39.682 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:54:39.682 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:54:39.685 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:54:39.685 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:54:39.687 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:54:39.687 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:54:39.689 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:54:39.689 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:54:39.691 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:54:39.691 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:54:39.692 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:54:39.692 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:54:39.694 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:54:39.694 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:54:39.695 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:54:39.695 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:54:39.699 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:54:39.699 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:54:39.701 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:54:39.701 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:54:39.703 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:54:39.703 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:54:39.704 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:54:39.704 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:54:39.706 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:54:39.706 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:54:39.709 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:54:39.709 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:54:39.711 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:54:39.711 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:54:39.712 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:54:39.712 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:54:39.718 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:54:39.718 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:54:39.721 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:54:39.721 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:54:39.724 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:54:39.724 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:54:39.726 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:54:39.726 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:54:39.729 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:54:39.729 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:54:39.736 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:54:39.736 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:54:39.742 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:54:39.742 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:54:39.744 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:54:39.744 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:54:39.752 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:54:39.752 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:54:39.757 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:54:39.757 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:54:39.761 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:54:39.761 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:54:39.775 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:54:39.775 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:54:39.803 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:54:39.803 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:54:39.869 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:54:39.869 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:54:39.910 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:54:39.910 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:54:39.927 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:54:39.927 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:54:39.946 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:54:39.946 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:54:39.978 [INFO] [root] [health_monitor.<module>:23] - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-07-01 13:54:39.978 [INFO] [root] [health_monitor.<module>:23] - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-07-01 13:54:40.008 [DEBUG] [fallback_manager] [fallback_manager.register_dependency:39] - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-07-01 13:54:40.008 [DEBUG] [fallback_manager] [fallback_manager.register_dependency:39] - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-07-01 13:54:40.068 [INFO] [fallback_manager] [fallback_manager.register_dependency:51] - 依赖 psutil (psutil) 可用
2025-07-01 13:54:40.068 [INFO] [fallback_manager] [fallback_manager.register_dependency:51] - 依赖 psutil (psutil) 可用
2025-07-01 13:54:40.357 [INFO] [health_monitor] [health_monitor.__init__:110] - 健康监控器初始化完成
2025-07-01 13:54:40.357 [INFO] [health_monitor] [health_monitor.__init__:110] - 健康监控器初始化完成
2025-07-01 13:54:40.367 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-01 13:54:40.367 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-01 13:54:40.376 [INFO] [alert_manager] [alert_manager._init_alert_rules:315] - 已初始化默认告警规则
2025-07-01 13:54:40.376 [INFO] [alert_manager] [alert_manager._init_alert_rules:315] - 已初始化默认告警规则
2025-07-01 13:54:40.378 [INFO] [alert_manager] [alert_manager._init_notification_channels:333] - 已初始化默认通知渠道
2025-07-01 13:54:40.378 [INFO] [alert_manager] [alert_manager._init_notification_channels:333] - 已初始化默认通知渠道
2025-07-01 13:54:40.380 [INFO] [alert_manager] [alert_manager.__init__:258] - 告警管理器初始化完成
2025-07-01 13:54:40.380 [INFO] [alert_manager] [alert_manager.__init__:258] - 告警管理器初始化完成
2025-07-01 13:55:45.793 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:55:45.794 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:55:45.795 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:55:45.795 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:55:45.796 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:55:45.797 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:55:45.798 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:55:45.799 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:55:45.801 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:55:45.801 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:55:45.803 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:55:45.804 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:55:45.806 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:55:45.807 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:55:45.808 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:55:45.809 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:55:45.810 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:55:45.811 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:55:45.812 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:55:45.813 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:55:45.816 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:55:45.817 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:55:45.818 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:55:45.819 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:55:45.820 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:55:45.820 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:55:45.821 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:55:45.823 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:55:45.825 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:55:45.826 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:55:45.826 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:55:45.827 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:55:45.828 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:55:45.829 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:55:45.831 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:55:45.832 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:55:45.833 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:55:45.833 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:55:45.834 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:55:45.835 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:55:45.837 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:55:45.838 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:55:45.842 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:55:45.844 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:55:45.845 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:55:45.846 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:55:45.848 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:55:45.849 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:55:45.850 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:55:45.854 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:55:45.856 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:55:45.859 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:55:45.860 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:55:45.861 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:55:45.862 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:55:45.863 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:55:45.864 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:55:45.865 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:55:45.866 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:55:45.867 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:55:45.869 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:55:45.871 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:55:45.874 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:55:45.875 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:55:45.876 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:55:45.877 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:55:45.877 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:55:45.878 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:55:45.879 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:55:45.879 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:55:45.880 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:55:45.881 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:55:45.882 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:55:45.883 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:55:45.885 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:55:45.886 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:55:45.887 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:55:45.887 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:55:45.891 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:55:45.893 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:56:43.893 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:56:43.894 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:56:43.896 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:56:43.898 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:56:43.899 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:56:43.900 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:56:43.902 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:56:43.903 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:56:43.905 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:56:43.906 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:56:43.907 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:56:43.908 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:56:43.910 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:56:43.911 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:56:43.913 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:56:43.915 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:56:43.916 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:56:43.918 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:56:43.919 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:56:43.921 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:56:43.926 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:56:43.927 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:56:43.928 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:56:43.931 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:56:43.936 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:56:43.939 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:56:43.944 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:56:43.946 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:56:43.948 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:56:43.949 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:56:43.951 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:56:43.952 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:56:43.953 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:56:43.955 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:56:43.956 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:56:43.957 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:56:43.958 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:56:43.959 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:56:43.961 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:56:43.962 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:56:43.965 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:56:43.966 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:56:43.967 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:56:43.968 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:56:43.970 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:56:43.971 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:56:43.972 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:56:43.973 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:56:43.974 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:56:43.975 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:56:43.976 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:56:43.985 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:56:43.987 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:56:43.991 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:56:43.993 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:56:43.996 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:56:44.000 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:56:44.002 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:56:44.005 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:56:44.012 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:56:44.018 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:56:44.019 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:56:44.020 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:56:44.021 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:56:44.023 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:56:44.024 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:56:44.039 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:56:44.040 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:56:44.041 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:56:44.042 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:56:44.044 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:56:44.045 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:56:44.046 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:56:44.054 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:56:44.055 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:56:44.056 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:56:44.057 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:56:44.059 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:56:44.060 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:56:44.062 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:56:44.161 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'sha256_crypt' handler: <class 'passlib.handlers.sha2_crypt.sha256_crypt'>
2025-07-01 13:57:00.120 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:57:00.124 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:57:00.125 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:57:00.126 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:57:00.128 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:57:00.129 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:57:00.130 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:57:00.132 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:57:00.134 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:57:00.135 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:57:00.141 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:57:00.142 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:57:00.143 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:57:00.144 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:57:00.146 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:57:00.148 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:57:00.150 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:57:00.153 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:57:00.155 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:57:00.158 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:57:00.162 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:57:00.164 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:57:00.166 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:57:00.170 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:57:00.172 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:57:00.174 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:57:00.177 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:57:00.182 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:57:00.184 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:57:00.186 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:57:00.188 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:57:00.190 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:57:00.192 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:57:00.194 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:57:00.196 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:57:00.197 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:57:00.198 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:57:00.199 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:57:00.201 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:57:00.201 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:57:00.203 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:57:00.204 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:57:00.205 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:57:00.206 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:57:00.207 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:57:00.209 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:57:00.213 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:57:00.214 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:57:00.215 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:57:00.216 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:57:00.217 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:57:00.218 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:57:00.219 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:57:00.220 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:57:00.221 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:57:00.222 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:57:00.223 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:57:00.224 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:57:00.225 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:57:00.226 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:57:00.229 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:57:00.230 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:57:00.231 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:57:00.232 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:57:00.233 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:57:00.234 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:57:00.235 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:57:00.236 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:57:00.237 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:57:00.238 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:57:00.239 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:57:00.243 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:57:00.246 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:57:00.247 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:57:00.248 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:57:00.249 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:57:00.251 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:57:00.252 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:57:00.253 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:57:00.254 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:57:00.313 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'sha256_crypt' handler: <class 'passlib.handlers.sha2_crypt.sha256_crypt'>
2025-07-01 13:57:53.237 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:57:53.245 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:57:53.251 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:57:53.258 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:57:53.266 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:57:53.280 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:57:53.303 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:57:53.344 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:57:53.352 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:57:53.355 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:57:53.357 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:57:53.361 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:57:53.365 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:57:53.377 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:57:53.381 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:57:53.383 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:57:53.387 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:57:53.389 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:57:53.390 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:57:53.392 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:57:53.396 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:57:53.399 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:57:53.400 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:57:53.401 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:57:53.402 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:57:53.403 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:57:53.404 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:57:53.406 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:57:53.409 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:57:53.410 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:57:53.414 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:57:53.416 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:57:53.417 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:57:53.418 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:57:53.419 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:57:53.421 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:57:53.423 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:57:53.424 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:57:53.426 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:57:53.427 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:57:53.429 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:57:53.431 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:57:53.432 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:57:53.434 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:57:53.435 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:57:53.437 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:57:53.439 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:57:53.441 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:57:53.458 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:57:53.460 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:57:53.466 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:57:53.478 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:57:53.483 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:57:53.484 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:57:53.485 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:57:53.488 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:57:53.493 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:57:53.495 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:57:53.497 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:57:53.499 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:57:53.502 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:57:53.504 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:57:53.525 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:57:53.542 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:57:53.543 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:57:53.544 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:57:53.546 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:57:53.550 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:57:53.551 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:57:53.552 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:57:53.553 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:57:53.554 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:57:53.558 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:57:53.560 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:57:53.561 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:57:53.562 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:57:53.564 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:57:53.565 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:57:53.566 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:57:53.569 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:57:53.703 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'sha256_crypt' handler: <class 'passlib.handlers.sha2_crypt.sha256_crypt'>
2025-07-01 13:58:45.116 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:58:45.117 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:58:45.118 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:58:45.120 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:58:45.121 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:58:45.122 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:58:45.124 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:58:45.125 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:58:45.126 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:58:45.127 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:58:45.128 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:58:45.129 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:58:45.130 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:58:45.132 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:58:45.133 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:58:45.135 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:58:45.136 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:58:45.137 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:58:45.138 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:58:45.140 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:58:45.148 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:58:45.151 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:58:45.153 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:58:45.156 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:58:45.158 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:58:45.161 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:58:45.165 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:58:45.168 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:58:45.170 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:58:45.171 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:58:45.173 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:58:45.176 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:58:45.177 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:58:45.179 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:58:45.180 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:58:45.182 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:58:45.183 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:58:45.185 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:58:45.186 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:58:45.188 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:58:45.190 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:58:45.192 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:58:45.194 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:58:45.198 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:58:45.199 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:58:45.201 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:58:45.202 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:58:45.203 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:58:45.204 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:58:45.205 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:58:45.207 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:58:45.208 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:58:45.209 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:58:45.210 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:58:45.212 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:58:45.214 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:58:45.215 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:58:45.217 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:58:45.218 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:58:45.220 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:58:45.223 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:58:45.224 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:58:45.225 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:58:45.226 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:58:45.227 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:58:45.228 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:58:45.230 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:58:45.231 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:58:45.232 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:58:45.233 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:58:45.235 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:58:45.236 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:58:45.237 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:58:45.238 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:58:45.240 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:58:45.241 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:58:45.242 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:58:45.243 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:58:45.245 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:58:45.247 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:58:45.382 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'sha256_crypt' handler: <class 'passlib.handlers.sha2_crypt.sha256_crypt'>
2025-07-01 13:59:41.506 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:59:41.510 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:59:41.514 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:59:41.527 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:59:41.534 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:59:41.536 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:59:41.537 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:59:41.539 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:59:41.544 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:59:41.547 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:59:41.548 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:59:41.550 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:59:41.552 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:59:41.554 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:59:41.555 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:59:41.562 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:59:41.564 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:59:41.567 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:59:41.569 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:59:41.570 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:59:41.578 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:59:41.581 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:59:41.582 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:59:41.584 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:59:41.585 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:59:41.589 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:59:41.595 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:59:41.598 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:59:41.600 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:59:41.602 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:59:41.604 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:59:41.606 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:59:41.612 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:59:41.614 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:59:41.615 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:59:41.618 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:59:41.619 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:59:41.621 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:59:41.625 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:59:41.632 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:59:41.634 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:59:41.636 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:59:41.638 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:59:41.640 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:59:41.646 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:59:41.650 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:59:41.651 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:59:41.655 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:59:41.659 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:59:41.663 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:59:41.670 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:59:41.671 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:59:41.672 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:59:41.679 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:59:41.681 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:59:41.682 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:59:41.684 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:59:41.686 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:59:41.687 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:59:41.688 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:59:41.696 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 13:59:41.698 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 13:59:41.699 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 13:59:41.731 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 13:59:41.754 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 13:59:41.821 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 13:59:41.921 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 13:59:42.052 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 13:59:42.264 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 13:59:42.494 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 13:59:42.739 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 13:59:42.777 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 13:59:42.786 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 13:59:42.878 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 13:59:42.953 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 13:59:42.997 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 13:59:43.062 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 13:59:43.103 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 13:59:43.165 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 13:59:43.195 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 13:59:43.531 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'sha256_crypt' handler: <class 'passlib.handlers.sha2_crypt.sha256_crypt'>
2025-07-01 14:00:14.211 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 14:00:14.212 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 14:00:14.214 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 14:00:14.215 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 14:00:14.216 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 14:00:14.217 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 14:00:14.218 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 14:00:14.219 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 14:00:14.222 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 14:00:14.223 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 14:00:14.224 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 14:00:14.226 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 14:00:14.227 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 14:00:14.228 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 14:00:14.230 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 14:00:14.231 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 14:00:14.233 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 14:00:14.236 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 14:00:14.242 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 14:00:14.243 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 14:00:14.246 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 14:00:14.247 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 14:00:14.248 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 14:00:14.249 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 14:00:14.250 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 14:00:14.252 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 14:00:14.256 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 14:00:14.257 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 14:00:14.258 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 14:00:14.259 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 14:00:14.260 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 14:00:14.261 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 14:00:14.262 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 14:00:14.262 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 14:00:14.263 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 14:00:14.265 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 14:00:14.266 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 14:00:14.267 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 14:00:14.268 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 14:00:14.269 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 14:00:14.274 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 14:00:14.275 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 14:00:14.276 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 14:00:14.277 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 14:00:14.278 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 14:00:14.279 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 14:00:14.280 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 14:00:14.281 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 14:00:14.282 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 14:00:14.283 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 14:00:14.284 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 14:00:14.285 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 14:00:14.288 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 14:00:14.290 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 14:00:14.291 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 14:00:14.292 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 14:00:14.293 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 14:00:14.294 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 14:00:14.295 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 14:00:14.296 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 14:00:14.299 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 14:00:14.301 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 14:00:14.302 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 14:00:14.305 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 14:00:14.307 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 14:00:14.308 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 14:00:14.309 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 14:00:14.310 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 14:00:14.311 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 14:00:14.312 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 14:00:14.313 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 14:00:14.315 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 14:00:14.316 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 14:00:14.317 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 14:00:14.322 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 14:00:14.323 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 14:00:14.324 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 14:00:14.326 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 14:00:14.327 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 14:00:14.328 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 14:00:14.433 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'sha256_crypt' handler: <class 'passlib.handlers.sha2_crypt.sha256_crypt'>
2025-07-01 14:01:19.102 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 14:01:19.104 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 14:01:19.105 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 14:01:19.106 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 14:01:19.107 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 14:01:19.109 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 14:01:19.112 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 14:01:19.114 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 14:01:19.116 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 14:01:19.117 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 14:01:19.118 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 14:01:19.120 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 14:01:19.121 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 14:01:19.123 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 14:01:19.124 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 14:01:19.129 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 14:01:19.132 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 14:01:19.134 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 14:01:19.135 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 14:01:19.136 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 14:01:19.139 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 14:01:19.148 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 14:01:19.150 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 14:01:19.151 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 14:01:19.152 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 14:01:19.153 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 14:01:19.154 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 14:01:19.155 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 14:01:19.156 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 14:01:19.157 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 14:01:19.160 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 14:01:19.165 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 14:01:19.167 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 14:01:19.169 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 14:01:19.170 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 14:01:19.171 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 14:01:19.173 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 14:01:19.174 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 14:01:19.175 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 14:01:19.176 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 14:01:19.182 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 14:01:19.185 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 14:01:19.186 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 14:01:19.187 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 14:01:19.188 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 14:01:19.190 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 14:01:19.191 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 14:01:19.192 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 14:01:19.193 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 14:01:19.195 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 14:01:19.200 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 14:01:19.201 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 14:01:19.202 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 14:01:19.203 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 14:01:19.204 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 14:01:19.205 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 14:01:19.206 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 14:01:19.208 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 14:01:19.210 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 14:01:19.215 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 14:01:19.219 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 14:01:19.221 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 14:01:19.222 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 14:01:19.223 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 14:01:19.224 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 14:01:19.225 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 14:01:19.230 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 14:01:19.231 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 14:01:19.232 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 14:01:19.233 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 14:01:19.234 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 14:01:19.235 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 14:01:19.235 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 14:01:19.236 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 14:01:19.237 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 14:01:19.238 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 14:01:19.239 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 14:01:19.240 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 14:01:19.241 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 14:01:19.241 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 14:01:19.326 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'sha256_crypt' handler: <class 'passlib.handlers.sha2_crypt.sha256_crypt'>
2025-07-01 23:01:13.398 [INFO] [BackendMockDataManager] [mock_data_manager.__init__:35] - 后端模拟数据模式已启用
2025-07-01 23:01:14.147 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'SafeConfigParser': <class 'configparser.ConfigParser'>
2025-07-01 23:01:14.148 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'NativeStringIO': <class '_io.StringIO'>
2025-07-01 23:01:14.148 [DEBUG] [passlib.utils.compat] [__init__.__getattr__:449] - loaded lazy attr 'BytesIO': <class '_io.BytesIO'>
2025-07-01 23:01:14.163 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'bcrypt' handler: <class 'passlib.handlers.bcrypt.bcrypt'>
2025-07-01 23:01:14.560 [INFO] [backend.app.core.db_connection] [db_connection._create_engine:191] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-01 23:01:14.560 [INFO] [backend.app.core.db_connection] [db_connection._create_engine:191] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-01 23:01:14.563 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-01 23:01:14.563 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-01 23:01:14.565 [INFO] [app.db.base_session] [base_session.<module>:44] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-01 23:01:14.565 [INFO] [app.db.base_session] [base_session.<module>:44] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-01 23:01:15.940 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'sha256_crypt' handler: <class 'passlib.handlers.sha2_crypt.sha256_crypt'>
2025-07-01 23:01:15.940 [DEBUG] [passlib.registry] [registry.register_crypt_handler:296] - registered 'sha256_crypt' handler: <class 'passlib.handlers.sha2_crypt.sha256_crypt'>
2025-07-01 23:01:15.966 [INFO] [app.core.db_connection] [db_connection._create_engine:191] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-01 23:01:15.966 [INFO] [app.core.db_connection] [db_connection._create_engine:191] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-01 23:01:15.971 [INFO] [auth_service] [auth_service_unified.__init__:49] - 统一认证服务初始化完成
2025-07-01 23:01:15.971 [INFO] [auth_service] [auth_service_unified.__init__:49] - 统一认证服务初始化完成
2025-07-01 23:01:18.503 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 23:01:18.503 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 23:01:18.503 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 23:01:18.503 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 23:01:18.504 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 23:01:18.504 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 23:01:18.504 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 23:01:18.504 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 23:01:18.504 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 23:01:18.504 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 23:01:18.505 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 23:01:18.505 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 23:01:18.505 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 23:01:18.505 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 23:01:18.505 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 23:01:18.505 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 23:01:18.505 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 23:01:18.505 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 23:01:18.506 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 23:01:18.506 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 23:01:18.506 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 23:01:18.506 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 23:01:18.506 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 23:01:18.506 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 23:01:18.506 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 23:01:18.506 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 23:01:18.507 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 23:01:18.507 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 23:01:18.508 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 23:01:18.508 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 23:01:18.508 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 23:01:18.508 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 23:01:18.509 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 23:01:18.509 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 23:01:18.509 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 23:01:18.509 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 23:01:18.510 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 23:01:18.510 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 23:01:18.510 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 23:01:18.510 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 23:01:18.512 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 23:01:18.512 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 23:01:18.512 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 23:01:18.512 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 23:01:18.513 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 23:01:18.513 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 23:01:18.513 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 23:01:18.513 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 23:01:18.513 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 23:01:18.513 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 23:01:18.513 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 23:01:18.513 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 23:01:18.514 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 23:01:18.514 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 23:01:18.514 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 23:01:18.514 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 23:01:18.514 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 23:01:18.514 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 23:01:18.514 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 23:01:18.514 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 23:01:18.515 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 23:01:18.515 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 23:01:18.515 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 23:01:18.515 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 23:01:18.515 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 23:01:18.515 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 23:01:18.515 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 23:01:18.515 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 23:01:18.516 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 23:01:18.516 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 23:01:18.516 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 23:01:18.516 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 23:01:18.516 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 23:01:18.516 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 23:01:18.516 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 23:01:18.516 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 23:01:18.516 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 23:01:18.516 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 23:01:18.517 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 23:01:18.517 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 23:01:18.518 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 23:01:18.518 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 23:01:18.518 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 23:01:18.518 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 23:01:18.518 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 23:01:18.518 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 23:01:18.519 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 23:01:18.519 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 23:01:18.519 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 23:01:18.519 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 23:01:18.519 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 23:01:18.519 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 23:01:18.519 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 23:01:18.519 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 23:01:18.520 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 23:01:18.520 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 23:01:18.520 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 23:01:18.520 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 23:01:18.520 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 23:01:18.520 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 23:01:18.520 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 23:01:18.520 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 23:01:18.520 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 23:01:18.520 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 23:01:18.521 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 23:01:18.521 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 23:01:18.521 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 23:01:18.521 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 23:01:18.521 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 23:01:18.521 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 23:01:18.521 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 23:01:18.521 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 23:01:18.522 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 23:01:18.522 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 23:01:18.522 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 23:01:18.522 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 23:01:18.522 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 23:01:18.522 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 23:01:18.522 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 23:01:18.522 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 23:01:18.526 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 23:01:18.526 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: users -> User
2025-07-01 23:01:18.526 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 23:01:18.526 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_records -> HealthRecord
2025-07-01 23:01:18.527 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 23:01:18.527 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_templates -> QuestionnaireTemplate
2025-07-01 23:01:18.527 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 23:01:18.527 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_results -> QuestionnaireResult
2025-07-01 23:01:18.527 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 23:01:18.527 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_results -> AssessmentResult
2025-07-01 23:01:18.528 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 23:01:18.528 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_templates -> AssessmentTemplate
2025-07-01 23:01:18.528 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 23:01:18.528 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: health_diaries -> HealthDiary
2025-07-01 23:01:18.528 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 23:01:18.528 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medical_records -> MedicalRecord
2025-07-01 23:01:18.528 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 23:01:18.528 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: lab_reports -> LabReport
2025-07-01 23:01:18.529 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 23:01:18.529 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: examination_reports -> ExaminationReport
2025-07-01 23:01:18.529 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 23:01:18.529 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: follow_up_records -> FollowUpRecord
2025-07-01 23:01:18.529 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 23:01:18.529 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: medications -> Medication
2025-07-01 23:01:18.529 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 23:01:18.529 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: imaging_reports -> ImagingReport
2025-07-01 23:01:18.530 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 23:01:18.530 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: other_records -> OtherRecord
2025-07-01 23:01:18.530 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 23:01:18.530 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: user_health_records -> HealthRecord
2025-07-01 23:01:18.530 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 23:01:18.530 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_responses -> QuestionnaireResponse
2025-07-01 23:01:18.530 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 23:01:18.530 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_responses -> AssessmentResponse
2025-07-01 23:01:18.531 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 23:01:18.531 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: questionnaire_distributions -> QuestionnaireDistribution
2025-07-01 23:01:18.531 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 23:01:18.531 [DEBUG] [DataExportService] [logging_utils._log:378] - 已注册表模型: assessment_distributions -> AssessmentDistribution
2025-07-01 23:01:18.531 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 23:01:18.531 [INFO] [DataExportService] [logging_utils._log:378] - 已初始化 19 个表模型
2025-07-01 23:01:18.763 [INFO] [root] [health_monitor.<module>:28] - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-07-01 23:01:18.763 [INFO] [root] [health_monitor.<module>:28] - 成功导入psutil模块，路径: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psutil\__init__.py
2025-07-01 23:01:18.763 [DEBUG] [fallback_manager] [fallback_manager.register_dependency:39] - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\pytest.exe', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-07-01 23:01:18.763 [DEBUG] [fallback_manager] [fallback_manager.register_dependency:39] - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\pytest.exe', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-07-01 23:01:18.764 [INFO] [fallback_manager] [fallback_manager.register_dependency:51] - 依赖 psutil (psutil) 可用
2025-07-01 23:01:18.764 [INFO] [fallback_manager] [fallback_manager.register_dependency:51] - 依赖 psutil (psutil) 可用
2025-07-01 23:01:19.271 [INFO] [health_monitor] [health_monitor.__init__:115] - 健康监控器初始化完成
2025-07-01 23:01:19.271 [INFO] [health_monitor] [health_monitor.__init__:115] - 健康监控器初始化完成
2025-07-01 23:01:19.280 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-01 23:01:19.280 [INFO] [query_cache] [query_cache.__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-01 23:01:19.289 [INFO] [alert_manager] [alert_manager._init_alert_rules:315] - 已初始化默认告警规则
2025-07-01 23:01:19.289 [INFO] [alert_manager] [alert_manager._init_alert_rules:315] - 已初始化默认告警规则
2025-07-01 23:01:19.290 [INFO] [alert_manager] [alert_manager._init_notification_channels:333] - 已初始化默认通知渠道
2025-07-01 23:01:19.290 [INFO] [alert_manager] [alert_manager._init_notification_channels:333] - 已初始化默认通知渠道
2025-07-01 23:01:19.366 [INFO] [alert_manager] [alert_manager.__init__:258] - 告警管理器初始化完成
2025-07-01 23:01:19.366 [INFO] [alert_manager] [alert_manager.__init__:258] - 告警管理器初始化完成
2025-07-01 23:01:21.475 [INFO] [login_attempt_manager] [login_attempt_manager.__init__:28] - 登录尝试管理器初始化 - 最大尝试次数: 5, 锁定时间: 300秒, 使用Redis: False
2025-07-01 23:01:21.475 [INFO] [login_attempt_manager] [login_attempt_manager.__init__:28] - 登录尝试管理器初始化 - 最大尝试次数: 5, 锁定时间: 300秒, 使用Redis: False
2025-07-01 23:01:21.484 [INFO] [ip_limiter] [ip_limiter.__init__:34] - IP限制器初始化 - 使用Redis: False
2025-07-01 23:01:21.484 [INFO] [ip_limiter] [ip_limiter.__init__:34] - IP限制器初始化 - 使用Redis: False
2025-07-01 23:01:21.502 [INFO] [token_manager] [token_manager.__init__:47] - 令牌管理器初始化 - 使用Redis: False, 令牌过期时间: 86400秒
2025-07-01 23:01:21.502 [INFO] [token_manager] [token_manager.__init__:47] - 令牌管理器初始化 - 使用Redis: False, 令牌过期时间: 86400秒
2025-07-01 23:01:21.533 [INFO] [auth_service] [auth_service.__init__:47] - 认证服务初始化完成
2025-07-01 23:01:21.533 [INFO] [auth_service] [auth_service.__init__:47] - 认证服务初始化完成
2025-07-01 23:01:21.551 [INFO] [db_service] [db_service._create_engine:92] - 数据库引擎和会话工厂创建成功
2025-07-01 23:01:21.551 [INFO] [db_service] [db_service._create_engine:92] - 数据库引擎和会话工厂创建成功
2025-07-01 23:01:21.551 [INFO] [db_service] [db_service.__init__:56] - 数据库服务初始化完成
2025-07-01 23:01:21.551 [INFO] [db_service] [db_service.__init__:56] - 数据库服务初始化完成
2025-07-01 23:01:21.600 [INFO] [distributed_lock] [distributed_lock.__init__:41] - 分布式锁初始化完成 - 使用Redis: False
2025-07-01 23:01:21.600 [INFO] [distributed_lock] [distributed_lock.__init__:41] - 分布式锁初始化完成 - 使用Redis: False
2025-07-01 23:01:22.459 [INFO] [token_manager] [token_manager.__init__:45] - 令牌管理服务初始化完成
2025-07-01 23:01:22.459 [INFO] [token_manager] [token_manager.__init__:45] - 令牌管理服务初始化完成
