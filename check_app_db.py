import sqlite3

# 检查app.db数据库
db_path = "YUN/backend/app.db"
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

print("检查app.db数据库表结构...")
print("="*50)

# 获取所有表名
cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
tables = cursor.fetchall()

print(f"app.db中共有 {len(tables)} 个表:")
for table in tables:
    table_name = table[0]
    print(f"\n表名: {table_name}")
    
    # 获取记录数
    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
    count = cursor.fetchone()[0]
    print(f"  记录数: {count}")
    
    # 如果是用户相关表，显示一些示例数据
    if 'user' in table_name.lower() and count > 0:
        cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
        rows = cursor.fetchall()
        print(f"  示例数据:")
        for row in rows:
            print(f"    {row}")
    
    # 如果是评估相关表，显示一些示例数据
    if 'assessment' in table_name.lower() and count > 0:
        cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
        rows = cursor.fetchall()
        print(f"  示例数据:")
        for row in rows:
            print(f"    {row}")

# 特别检查SM_008用户
if any('user' in table[0].lower() for table in tables):
    print("\n" + "="*50)
    print("检查SM_008用户数据:")
    
    # 尝试不同的用户表名
    user_tables = [table[0] for table in tables if 'user' in table[0].lower()]
    
    for user_table in user_tables:
        print(f"\n在表 {user_table} 中查找SM_008:")
        try:
            # 获取表结构
            cursor.execute(f"PRAGMA table_info({user_table})")
            columns = cursor.fetchall()
            column_names = [col[1] for col in columns]
            print(f"  表字段: {column_names}")
            
            # 查找SM_008用户
            if 'custom_id' in column_names:
                cursor.execute(f"SELECT * FROM {user_table} WHERE custom_id = 'SM_008'")
            elif 'username' in column_names:
                cursor.execute(f"SELECT * FROM {user_table} WHERE username = 'SM_008'")
            else:
                cursor.execute(f"SELECT * FROM {user_table} LIMIT 3")
            
            rows = cursor.fetchall()
            if rows:
                print(f"  找到记录: {rows}")
            else:
                print(f"  未找到SM_008用户")
                
        except Exception as e:
            print(f"  查询出错: {e}")

conn.close()
print("\napp.db检查完成")