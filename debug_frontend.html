<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #409eff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #337ab7;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            color: #dc3545;
        }
        .success {
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>前端调试页面</h1>
        
        <div class="section">
            <h3>认证状态检查</h3>
            <button onclick="checkAuthStatus()">检查认证状态</button>
            <button onclick="clearAuth()">清除认证信息</button>
            <div id="authResult" class="result"></div>
        </div>
        
        <div class="section">
            <h3>登录测试</h3>
            <input type="text" id="username" placeholder="用户名" value="admin">
            <input type="password" id="password" placeholder="密码" value="admin123">
            <button onclick="testLogin()">测试登录</button>
            <div id="loginResult" class="result"></div>
        </div>
        
        <div class="section">
            <h3>API测试</h3>
            <button onclick="testAssessmentsAPI()">测试评估量表API</button>
            <button onclick="testAssessmentRecordsAPI()">测试评估记录API</button>
            <button onclick="testQuestionnairesAPI()">测试问卷API</button>
            <div id="apiResult" class="result"></div>
        </div>
        
        <div class="section">
            <h3>完整认证测试</h3>
            <button onclick="runFullTest()">运行完整测试</button>
            <div id="fullTestResult" class="result"></div>
        </div>
    </div>

    <script src="test_frontend_auth.js"></script>
    <script>
        function log(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = isError ? 'error' : 'success';
            element.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            element.scrollTop = element.scrollHeight;
        }

        function checkAuthStatus() {
            const authResult = document.getElementById('authResult');
            authResult.innerHTML = '';
            
            const token = localStorage.getItem('token');
            const user = localStorage.getItem('user');
            const customId = localStorage.getItem('custom_id');
            
            log('authResult', `Token: ${token ? token.substring(0, 20) + '...' : '无'}`);
            log('authResult', `User: ${user || '无'}`);
            log('authResult', `Custom ID: ${customId || '无'}`);
            
            if (token) {
                try {
                    const parts = token.split('.');
                    log('authResult', `Token部分数量: ${parts.length}`);
                    if (parts.length === 3) {
                        const payload = JSON.parse(atob(parts[1]));
                        log('authResult', `Token载荷: ${JSON.stringify(payload, null, 2)}`);
                        
                        const now = Math.floor(Date.now() / 1000);
                        const isExpired = payload.exp && payload.exp < now;
                        log('authResult', `Token是否过期: ${isExpired}`, isExpired);
                    }
                } catch (e) {
                    log('authResult', `解析Token失败: ${e.message}`, true);
                }
            }
        }

        function clearAuth() {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            localStorage.removeItem('custom_id');
            log('authResult', '认证信息已清除');
        }

        async function testLogin() {
            const loginResult = document.getElementById('loginResult');
            loginResult.innerHTML = '';
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                log('loginResult', '请输入用户名和密码', true);
                return;
            }
            
            try {
                log('loginResult', `尝试登录: ${username}`);
                
                const response = await fetch('/api/auth/frontend_login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                log('loginResult', `响应状态: ${response.status}`);
                
                if (response.ok) {
                    const data = await response.json();
                    log('loginResult', `登录成功: ${JSON.stringify(data, null, 2)}`);
                    
                    if (data.access_token) {
                        localStorage.setItem('token', data.access_token);
                        log('loginResult', 'Token已保存到localStorage');
                    }
                    
                    if (data.user) {
                        localStorage.setItem('user', JSON.stringify(data.user));
                        if (data.user.custom_id) {
                            localStorage.setItem('custom_id', data.user.custom_id);
                        }
                        log('loginResult', '用户信息已保存到localStorage');
                    }
                } else {
                    const errorText = await response.text();
                    log('loginResult', `登录失败: ${errorText}`, true);
                }
            } catch (error) {
                log('loginResult', `登录异常: ${error.message}`, true);
            }
        }

        async function testAPI(url, description) {
            const apiResult = document.getElementById('apiResult');
            
            try {
                log('apiResult', `测试 ${description}: ${url}`);
                
                const token = localStorage.getItem('token');
                const headers = {
                    'Content-Type': 'application/json'
                };
                
                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                    log('apiResult', `使用Token: ${token.substring(0, 20)}...`);
                } else {
                    log('apiResult', '警告: 没有Token', true);
                }
                
                const response = await fetch(url, { headers });
                log('apiResult', `${description} 响应状态: ${response.status}`);
                
                if (response.ok) {
                    const data = await response.json();
                    log('apiResult', `${description} 成功: ${JSON.stringify(data, null, 2)}`);
                } else {
                    const errorText = await response.text();
                    log('apiResult', `${description} 失败: ${errorText}`, true);
                }
            } catch (error) {
                log('apiResult', `${description} 异常: ${error.message}`, true);
            }
        }

        function testAssessmentsAPI() {
            document.getElementById('apiResult').innerHTML = '';
            testAPI('/api/assessments', '评估量表API');
        }

        function testAssessmentRecordsAPI() {
            document.getElementById('apiResult').innerHTML = '';
            testAPI('/api/assessments/records', '评估记录API');
        }

        function testQuestionnairesAPI() {
            document.getElementById('apiResult').innerHTML = '';
            testAPI('/api/questionnaires', '问卷API');
        }

        // 运行完整测试
        async function runFullTest() {
            document.getElementById('fullTestResult').innerHTML = '<p>正在运行测试...</p>';
            
            // 捕获console.log输出
            const originalLog = console.log;
            const logs = [];
            console.log = function(...args) {
                logs.push(args.join(' '));
                originalLog.apply(console, args);
            };
            
            try {
                await window.testAuth.main();
                
                document.getElementById('fullTestResult').innerHTML = `
                    <h4>测试结果:</h4>
                    <pre>${logs.join('\n')}</pre>
                `;
            } catch (error) {
                document.getElementById('fullTestResult').innerHTML = `<p style="color: red;">测试失败: ${error.message}</p>`;
            } finally {
                console.log = originalLog;
            }
        }

        // 页面加载时自动检查认证状态
        window.onload = function() {
            checkAuthStatus();
        };
    </script>
</body>
</html>