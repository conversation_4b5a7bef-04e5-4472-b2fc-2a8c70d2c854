<template>
  <div class="assessment-analysis">
    <el-card class="analysis-card">
      <template #header>
        <div class="card-header">
          <h3>{{ assessment.title }} - 结果分析</h3>
          <div class="header-actions">
            <export-button
              button-text="导出数据"
              size="small"
              :data="responseData"
              :columns="exportColumns"
              :filename="assessment.title + '-评估结果'"
              :title="assessment.title + ' - 评估结果'"
              orientation="landscape"
              :charts="Object.values(chartInstances)"
              :show-chart-export="true"
              @export="handleExport"
            />
          </div>
        </div>
      </template>

      <!-- 基本统计信息 -->
      <div class="statistics-section">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="总填写人数" :value="totalResponses" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="平均得分" :value="averageScore" :precision="2" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="最高得分" :value="maxScore" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="最低得分" :value="minScore" />
          </el-col>
        </el-row>
      </div>

      <!-- 得分分布图 -->
      <div class="chart-section">
        <h4>得分分布</h4>
        <bar-chart
          :title="'得分分布'"
          :x-axis-data="scoreDistribution.xAxis"
          :series-data="[scoreDistribution.series]"
          :series-names="['人数']"
          :show-zoom="true"
          height="300px"
          @chart-ready="chart => onChartReady(chart, 'scoreDistribution')"
        />
      </div>

      <!-- 结果分类统计 -->
      <div class="chart-section" v-if="assessment.interpretation && assessment.interpretation.length > 0">
        <h4>结果分类统计</h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <pie-chart
              :title="'结果分类'"
              :data="resultCategoryData"
              pie-type="ring"
              height="300px"
            />
          </el-col>
          <el-col :span="12">
            <el-table :data="resultCategoryTable" style="width: 100%">
              <el-table-column prop="category" label="结果分类" />
              <el-table-column prop="count" label="人数" />
              <el-table-column prop="percentage" label="百分比" />
            </el-table>
          </el-col>
        </el-row>
      </div>

      <!-- 问题得分分析 -->
      <div class="chart-section">
        <h4>问题得分分析</h4>
        <bar-chart
          :title="'各问题平均得分'"
          :x-axis-data="questionScores.xAxis"
          :series-data="[questionScores.series]"
          :series-names="['平均分']"
          :show-zoom="true"
          height="300px"
        />
      </div>

      <!-- 时间趋势分析 -->
      <div class="chart-section">
        <h4>时间趋势分析</h4>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-radio-group v-model="timeGrouping" size="small" style="margin-bottom: 15px;">
              <el-radio-button label="day">按日</el-radio-button>
              <el-radio-button label="week">按周</el-radio-button>
              <el-radio-button label="month">按月</el-radio-button>
            </el-radio-group>
            <line-chart
              :title="'得分趋势'"
              :x-axis-data="timeTrend.xAxis"
              :series-data="[timeTrend.series]"
              :series-names="['平均分']"
              :show-zoom="true"
              line-type="area"
              :smooth="true"
              height="300px"
            />
          </el-col>
        </el-row>
      </div>

      <!-- 详细数据表格 -->
      <div class="data-section">
        <h4>详细数据</h4>
        <el-table
          :data="responseData"
          style="width: 100%"
          height="400"
          border
          v-loading="loading"
        >
          <el-table-column type="index" width="50" />
          <el-table-column prop="user_name" label="用户" width="120" />
          <el-table-column prop="created_at" label="填写时间" width="180" />
          <el-table-column prop="total_score" label="总分" width="80" />
          <el-table-column prop="result" label="结果" width="120" />
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button type="primary" link @click="viewDetail(scope.row)">查看详情</el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalItems"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="填写详情"
      width="60%"
    >
      <div v-if="currentDetail">
        <h4>基本信息</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户">{{ currentDetail.user_name }}</el-descriptions-item>
          <el-descriptions-item label="填写时间">{{ currentDetail.created_at }}</el-descriptions-item>
          <el-descriptions-item label="总分">{{ currentDetail.total_score }}</el-descriptions-item>
          <el-descriptions-item label="结果">{{ currentDetail.result }}</el-descriptions-item>
        </el-descriptions>

        <h4 style="margin-top: 20px;">问题回答</h4>
        <el-table :data="currentDetail.answers" style="width: 100%">
          <el-table-column prop="question" label="问题" min-width="200" />
          <el-table-column prop="answer" label="回答" width="150" />
          <el-table-column prop="score" label="得分" width="80" />
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import axios from 'axios';
import BarChart from '../charts/BarChart.vue';
import LineChart from '../charts/LineChart.vue';
import PieChart from '../charts/PieChart.vue';
import ExportButton from '../common/ExportButton.vue';
import { formatDateData, calculateStats, formatNumber } from '../../utils/chartUtils';
import { exportToExcel, exportToPDF, exportChartsToPDF } from '../../utils/exportUtils';
import { generateMockAssessmentAnalysisData, isMockEnabled } from '../../mocks/mockDataManager';

// 使用Vue 3的<script setup>语法，defineProps是编译器宏，不需要导入
const props = defineProps({
  assessment: {
    type: Object,
    required: true
  }
});

// 状态变量
const loading = ref(false);
const responseData = ref([]);
const currentPage = ref(1);
const pageSize = ref(20);
const totalItems = ref(0);
const timeGrouping = ref('month');
const detailDialogVisible = ref(false);
const currentDetail = ref(null);

// 计算属性
const totalResponses = computed(() => responseData.value.length);
const averageScore = computed(() => {
  if (!responseData.value.length) return 0;
  const scores = responseData.value.map(item => item.total_score).filter(score => !isNaN(Number(score)));
  return scores.length ? scores.reduce((sum, score) => sum + score, 0) / scores.length : 0;
});
const maxScore = computed(() => {
  if (!responseData.value.length) return 0;
  const scores = responseData.value.map(item => item.total_score).filter(score => !isNaN(Number(score)));
  return scores.length ? Math.max(...scores) : 0;
});
const minScore = computed(() => {
  if (!responseData.value.length) return 0;
  const scores = responseData.value.map(item => item.total_score).filter(score => !isNaN(Number(score)));
  return scores.length ? Math.min(...scores) : 0;
});

// 得分分布数据
const scoreDistribution = computed(() => {
  if (!responseData.value.length) return { xAxis: [], series: [] };

  // 计算得分范围
  const min = Math.floor(minScore.value);
  const max = Math.ceil(maxScore.value);
  const step = Math.max(1, Math.ceil((max - min) / 10)); // 最多10个区间

  // 创建区间
  const intervals = [];
  for (let i = min; i < max; i += step) {
    intervals.push(`${i}-${i + step}`);
  }

  // 统计每个区间的人数
  const counts = new Array(intervals.length).fill(0);
  responseData.value.forEach(item => {
    const score = item.total_score;
    if (!isNaN(Number(score))) {
      const index = Math.min(Math.floor((score - min) / step), intervals.length - 1);
      counts[index]++;
    }
  });

  return { xAxis: intervals, series: counts };
});

// 结果分类数据
const resultCategoryData = computed(() => {
  if (!responseData.value.length || !props.assessment.interpretation) return [];

  // 统计每个结果分类的人数
  const categoryMap = new Map();
  responseData.value.forEach(item => {
    const result = item.result || '未分类';
    categoryMap.set(result, (categoryMap.get(result) || 0) + 1);
  });

  // 转换为饼图数据格式
  return Array.from(categoryMap.entries()).map(([name, value]) => ({ name, value }));
});

// 结果分类表格数据
const resultCategoryTable = computed(() => {
  if (!resultCategoryData.value.length) return [];

  const total = resultCategoryData.value.reduce((sum, item) => sum + item.value, 0);
  return resultCategoryData.value.map(item => ({
    category: item.name,
    count: item.value,
    percentage: `${((item.value / total) * 100).toFixed(2)}%`
  }));
});

// 问题得分数据
const questionScores = computed(() => {
  if (!responseData.value.length || !props.assessment.questions) return { xAxis: [], series: [] };

  // 获取所有问题
  const questions = props.assessment.questions.map(q => q.question_text);

  // 计算每个问题的平均得分
  const scores = new Array(questions.length).fill(0);
  const counts = new Array(questions.length).fill(0);

  responseData.value.forEach(response => {
    if (response.answers && Array.isArray(response.answers)) {
      response.answers.forEach((answer, index) => {
        if (index < scores.length && !isNaN(Number(answer.score))) {
          scores[index] += Number(answer.score);
          counts[index]++;
        }
      });
    }
  });

  // 计算平均分
  const averages = scores.map((score, index) => counts[index] ? score / counts[index] : 0);

  return { xAxis: questions, series: averages };
});

// 时间趋势数据
const timeTrend = computed(() => {
  if (!responseData.value.length) return { xAxis: [], series: [] };

  // 按时间分组
  const groupedData = new Map();
  responseData.value.forEach(item => {
    if (!item.created_at) return;

    // 格式化日期
    let dateKey;
    const date = new Date(item.created_at);
    if (timeGrouping.value === 'day') {
      dateKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    } else if (timeGrouping.value === 'week') {
      // 获取周的第一天
      const firstDay = new Date(date);
      const day = date.getDay() || 7; // 将周日视为7
      firstDay.setDate(date.getDate() - day + 1);
      dateKey = `${firstDay.getFullYear()}-${String(firstDay.getMonth() + 1).padStart(2, '0')}-${String(firstDay.getDate()).padStart(2, '0')}`;
    } else if (timeGrouping.value === 'month') {
      dateKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
    }

    if (!groupedData.has(dateKey)) {
      groupedData.set(dateKey, { sum: 0, count: 0 });
    }

    const group = groupedData.get(dateKey);
    if (!isNaN(Number(item.total_score))) {
      group.sum += Number(item.total_score);
      group.count++;
    }
  });

  // 计算每个时间段的平均分
  const sortedDates = Array.from(groupedData.keys()).sort();
  const averages = sortedDates.map(date => {
    const group = groupedData.get(date);
    return group.count ? group.sum / group.count : 0;
  });

  return { xAxis: sortedDates, series: averages };
});

// 初始化
onMounted(() => {
  fetchResponseData();
});

// 监听量表变化
watch(() => props.assessment.id, () => {
  fetchResponseData();
});

// 监听分页变化
watch([currentPage, pageSize], () => {
  fetchResponseData();
});

// 监听时间分组变化
watch(timeGrouping, () => {
  // 时间趋势数据会自动更新
});

// 获取填写数据
const fetchResponseData = async () => {
  if (!props.assessment.id) return;

  loading.value = true;
  try {
    const response = await axios.get(`/api/assessments/${props.assessment.id}/responses`, {
      params: {
        page: currentPage.value,
        page_size: pageSize.value
      }
    });

    responseData.value = response.data.items || [];
    totalItems.value = response.data.total || 0;

    // 如果没有真实数据且启用了模拟数据，使用模拟数据
    if ((!response.data.items || response.data.items.length === 0) && isMockEnabled()) {
      const mockData = generateMockAssessmentAnalysisData(props.assessment);
      responseData.value = mockData;
      totalItems.value = mockData.length;
    }
  } catch (error) {
    console.error('获取填写数据失败:', error);
    ElMessage.error('获取填写数据失败，请稍后重试');

    // 如果启用了模拟数据，使用模拟数据
    if (isMockEnabled()) {
      const mockData = generateMockAssessmentAnalysisData(props.assessment);
      responseData.value = mockData;
      totalItems.value = mockData.length;
    }
  } finally {
    loading.value = false;
  }
};

// 模拟数据生成已移至 mockDataManager.js

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
};

// 处理每页条数变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1;
};

// 查看详情
const viewDetail = (row) => {
  currentDetail.value = row;
  detailDialogVisible.value = true;
};

// 导出列配置
const exportColumns = [
  { title: '用户', dataIndex: 'user_name', width: 120 },
  { title: '填写时间', dataIndex: 'created_at', width: 150 },
  { title: '总分', dataIndex: 'total_score', width: 80 },
  { title: '结果', dataIndex: 'result', width: 120 }
];

// 图表实例集合
const chartInstances = ref({});

// 记录图表实例
const onChartReady = (chart, id) => {
  chartInstances.value[id] = chart;
};

// 处理导出事件
const handleExport = (event) => {
  if (event.success) {
    ElMessage.success(`${event.type === 'excel' ? 'Excel' : event.type === 'pdf' ? 'PDF' : '图表'} 导出成功`);
  } else {
    ElMessage.error(`导出失败: ${event.error || '未知错误'}`);
  }
};
</script>

<style scoped>
.assessment-analysis {
  width: 100%;
}

.analysis-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
}

.statistics-section {
  margin-bottom: 30px;
}

.chart-section {
  margin-bottom: 30px;
}

.chart-section h4 {
  margin-bottom: 15px;
  color: #606266;
  font-weight: 500;
}

.data-section {
  margin-bottom: 20px;
}

.data-section h4 {
  margin-bottom: 15px;
  color: #606266;
  font-weight: 500;
}

.pagination-container {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}
</style>
