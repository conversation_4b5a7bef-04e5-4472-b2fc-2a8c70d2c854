#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
注册移动端计分API路由

功能：
1. 将新创建的移动端计分API添加到主路由中
2. 更新app/api/api.py文件

作者：AI助手
创建时间：2024年
"""

import os
import sys
import re

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def register_mobile_scoring_api():
    """
    注册移动端计分API到主路由
    """
    api_file_path = "c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app/api/api.py"
    
    # 读取当前API文件内容
    with open(api_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否已经导入
    if "from app.api.endpoints import mobile_scoring_api" in content:
        print("✅ 移动端计分API已经导入，无需修改")
        return True
    
    # 添加导入语句
    import_pattern = r"from app\.api\.endpoints import (\w+)\s*$"
    last_import = re.findall(import_pattern, content, re.MULTILINE)[-1]
    
    new_import = f"from app.api.endpoints import {last_import}\nfrom app.api.endpoints import mobile_scoring_api"
    content = content.replace(f"from app.api.endpoints import {last_import}", new_import)
    
    # 查找API路由注册部分
    router_pattern = r"(api_router\.include_router\([\s\S]+?\))\s*$"
    last_router = re.findall(router_pattern, content, re.MULTILINE)[-1]
    
    # 添加新的路由注册
    new_router = f"{last_router}\n\n# 注册移动端计分API路由\napi_router.include_router(\n    mobile_scoring_api.router,\n    prefix=\"/mobile\",\n    tags=[\"移动端计分\"]\n)"
    
    content = content.replace(last_router, new_router)
    
    # 写入更新后的内容
    with open(api_file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ 已成功注册移动端计分API到 {api_file_path}")
    return True

def main():
    """
    主函数
    """
    print("🚀 开始注册移动端计分API路由...")
    
    try:
        # 注册API路由
        register_mobile_scoring_api()
        
        print("\n" + "="*60)
        print("✅ 移动端计分API路由注册完成！")
        print("="*60)
        
        print("\n📋 后续步骤：")
        print("  1. 重启后端服务")
        print("  2. 修改移动端代码，使用新的API端点")
        print("  3. 移除移动端的计分逻辑")
        print("  4. 测试新的提交流程")
        
    except Exception as e:
        print(f"❌ 注册过程中发生错误: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    main()