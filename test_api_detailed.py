import requests
import json

# 测试API的查询逻辑
base_url = "http://localhost:8000"

# 添加认证头部（模拟移动端请求）
headers = {
    "X-User-ID": "SM_006",
    "Content-Type": "application/json"
}

def test_api_detailed():
    print("=== 测试量表API详细信息 ===")
    
    # 发送请求并获取详细响应
    print("\n发送请求:")
    print(f"URL: {base_url}/api/mobile/assessments?custom_id=SM_006&limit=20")
    print(f"请求头: {headers}")
    
    response = requests.get(f"{base_url}/api/mobile/assessments?custom_id=SM_006&limit=20", headers=headers)
    print(f"\n响应状态码: {response.status_code}")
    print(f"响应头: {dict(response.headers)}")
    
    if response.status_code == 200:
        try:
            data = response.json()
            print("\n响应体:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            if 'data' in data and 'assessments' in data['data']:
                assessments = data['data']['assessments']
                print(f"\n返回的量表数量: {len(assessments)}")
                
                # 检查是否有状态统计
                if 'status_counts' in data['data']:
                    status_counts = data['data']['status_counts']
                    print(f"状态统计: {status_counts}")
            else:
                print("响应中没有量表数据")
        except json.JSONDecodeError:
            print("响应不是有效的JSON格式")
            print(f"原始响应: {response.text}")
    else:
        print(f"请求失败: {response.text}")

    # 测试不同状态的查询
    print("\n\n=== 测试不同状态的查询 ===")
    
    # 测试pending状态
    print("\n测试pending状态:")
    response = requests.get(f"{base_url}/api/mobile/assessments?custom_id=SM_006&status=pending&limit=20", headers=headers)
    if response.status_code == 200:
        data = response.json()
        if 'data' in data and 'assessments' in data['data']:
            assessments = data['data']['assessments']
            print(f"pending状态量表数量: {len(assessments)}")
    
    # 测试completed状态
    print("\n测试completed状态:")
    response = requests.get(f"{base_url}/api/mobile/assessments?custom_id=SM_006&status=completed&limit=20", headers=headers)
    if response.status_code == 200:
        data = response.json()
        if 'data' in data and 'assessments' in data['data']:
            assessments = data['data']['assessments']
            print(f"completed状态量表数量: {len(assessments)}")

if __name__ == "__main__":
    test_api_detailed()