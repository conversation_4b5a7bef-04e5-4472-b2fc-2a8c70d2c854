import time
import logging
from functools import wraps
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
import psutil
import threading
from collections import defaultdict, deque

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """
    性能监控器
    """
    
    def __init__(self, max_records: int = 1000):
        self.max_records = max_records
        self.api_metrics = defaultdict(lambda: {
            'call_count': 0,
            'total_time': 0,
            'avg_time': 0,
            'min_time': float('inf'),
            'max_time': 0,
            'error_count': 0,
            'recent_calls': deque(maxlen=100)  # 保存最近100次调用
        })
        self.system_metrics = {
            'cpu_usage': deque(maxlen=60),  # 保存最近60个数据点
            'memory_usage': deque(maxlen=60),
            'disk_usage': deque(maxlen=60)
        }
        self.lock = threading.Lock()
        
        # 启动系统监控线程
        self.monitoring_thread = threading.Thread(target=self._monitor_system, daemon=True)
        self.monitoring_thread.start()
    
    def _monitor_system(self):
        """
        监控系统资源使用情况
        """
        while True:
            try:
                with self.lock:
                    # CPU使用率
                    cpu_percent = psutil.cpu_percent(interval=1)
                    self.system_metrics['cpu_usage'].append({
                        'timestamp': datetime.now().isoformat(),
                        'value': cpu_percent
                    })
                    
                    # 内存使用率
                    memory = psutil.virtual_memory()
                    self.system_metrics['memory_usage'].append({
                        'timestamp': datetime.now().isoformat(),
                        'value': memory.percent
                    })
                    
                    # 磁盘使用率
                    disk = psutil.disk_usage('/')
                    self.system_metrics['disk_usage'].append({
                        'timestamp': datetime.now().isoformat(),
                        'value': (disk.used / disk.total) * 100
                    })
                
                time.sleep(60)  # 每分钟采集一次
            except Exception as e:
                logger.error(f"系统监控出错: {e}")
                time.sleep(60)
    
    def record_api_call(self, endpoint: str, execution_time: float, success: bool = True):
        """
        记录API调用指标
        """
        with self.lock:
            metrics = self.api_metrics[endpoint]
            
            # 更新调用统计
            metrics['call_count'] += 1
            metrics['total_time'] += execution_time
            metrics['avg_time'] = metrics['total_time'] / metrics['call_count']
            metrics['min_time'] = min(metrics['min_time'], execution_time)
            metrics['max_time'] = max(metrics['max_time'], execution_time)
            
            if not success:
                metrics['error_count'] += 1
            
            # 记录最近的调用
            metrics['recent_calls'].append({
                'timestamp': datetime.now().isoformat(),
                'execution_time': execution_time,
                'success': success
            })
    
    def get_api_metrics(self, endpoint: Optional[str] = None) -> Dict[str, Any]:
        """
        获取API指标
        """
        with self.lock:
            if endpoint:
                if endpoint in self.api_metrics:
                    metrics = dict(self.api_metrics[endpoint])
                    # 转换deque为list以便JSON序列化
                    metrics['recent_calls'] = list(metrics['recent_calls'])
                    return {endpoint: metrics}
                else:
                    return {}
            else:
                result = {}
                for ep, metrics in self.api_metrics.items():
                    result[ep] = dict(metrics)
                    result[ep]['recent_calls'] = list(metrics['recent_calls'])
                return result
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """
        获取系统指标
        """
        with self.lock:
            result = {}
            for metric_name, values in self.system_metrics.items():
                result[metric_name] = list(values)
            return result
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """
        获取性能摘要
        """
        with self.lock:
            # API性能摘要
            api_summary = {
                'total_endpoints': len(self.api_metrics),
                'total_calls': sum(m['call_count'] for m in self.api_metrics.values()),
                'total_errors': sum(m['error_count'] for m in self.api_metrics.values()),
                'slowest_endpoints': [],
                'most_called_endpoints': []
            }
            
            # 最慢的端点
            sorted_by_avg_time = sorted(
                self.api_metrics.items(),
                key=lambda x: x[1]['avg_time'],
                reverse=True
            )[:5]
            
            api_summary['slowest_endpoints'] = [
                {
                    'endpoint': ep,
                    'avg_time': metrics['avg_time'],
                    'call_count': metrics['call_count']
                }
                for ep, metrics in sorted_by_avg_time
            ]
            
            # 调用最多的端点
            sorted_by_calls = sorted(
                self.api_metrics.items(),
                key=lambda x: x[1]['call_count'],
                reverse=True
            )[:5]
            
            api_summary['most_called_endpoints'] = [
                {
                    'endpoint': ep,
                    'call_count': metrics['call_count'],
                    'avg_time': metrics['avg_time']
                }
                for ep, metrics in sorted_by_calls
            ]
            
            # 系统资源摘要
            system_summary = {}
            for metric_name, values in self.system_metrics.items():
                if values:
                    recent_values = [v['value'] for v in list(values)[-10:]]  # 最近10个数据点
                    system_summary[metric_name] = {
                        'current': recent_values[-1] if recent_values else 0,
                        'avg': sum(recent_values) / len(recent_values) if recent_values else 0,
                        'max': max(recent_values) if recent_values else 0,
                        'min': min(recent_values) if recent_values else 0
                    }
            
            return {
                'api_summary': api_summary,
                'system_summary': system_summary,
                'timestamp': datetime.now().isoformat()
            }
    
    def clear_metrics(self):
        """
        清空指标数据
        """
        with self.lock:
            self.api_metrics.clear()
            for metric_values in self.system_metrics.values():
                metric_values.clear()

# 全局性能监控实例
performance_monitor = PerformanceMonitor()

def monitor_performance(endpoint_name: Optional[str] = None):
    """
    性能监控装饰器
    """
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            success = True
            endpoint = endpoint_name or f"{func.__module__}.{func.__name__}"
            
            try:
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                raise
            finally:
                execution_time = time.time() - start_time
                performance_monitor.record_api_call(endpoint, execution_time, success)
                
                # 记录慢查询
                if execution_time > 2.0:  # 超过2秒的请求
                    logger.warning(
                        f"慢查询检测: {endpoint} 执行时间: {execution_time:.2f}s"
                    )
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            success = True
            endpoint = endpoint_name or f"{func.__module__}.{func.__name__}"
            
            try:
                result = func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                raise
            finally:
                execution_time = time.time() - start_time
                performance_monitor.record_api_call(endpoint, execution_time, success)
                
                # 记录慢查询
                if execution_time > 2.0:  # 超过2秒的请求
                    logger.warning(
                        f"慢查询检测: {endpoint} 执行时间: {execution_time:.2f}s"
                    )
        
        # 根据函数类型返回对应的包装器
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

def log_api_access(request_info: Dict[str, Any]):
    """
    记录API访问日志
    """
    logger.info(
        f"API访问 - "
        f"方法: {request_info.get('method', 'Unknown')} "
        f"路径: {request_info.get('path', 'Unknown')} "
        f"用户: {request_info.get('user_id', 'Anonymous')} "
        f"IP: {request_info.get('client_ip', 'Unknown')} "
        f"用户代理: {request_info.get('user_agent', 'Unknown')}"
    )

def get_performance_metrics():
    """
    获取性能指标（供API端点使用）
    """
    return performance_monitor.get_performance_summary()

def get_api_metrics(endpoint: Optional[str] = None):
    """
    获取API指标（供API端点使用）
    """
    return performance_monitor.get_api_metrics(endpoint)

def get_system_metrics():
    """
    获取系统指标（供API端点使用）
    """
    return performance_monitor.get_system_metrics()