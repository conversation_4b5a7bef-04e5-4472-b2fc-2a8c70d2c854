# 移动端修改指南

## 概述

本指南详细说明移动端需要进行的修改，以配合后端的计分API更新。主要目标是让移动端只提交原始答案，由后端进行计分和分析。

## 主要修改内容

### 1. API端点更新

#### 新的API端点

**评估量表提交（新）**
```
POST /api/mobile/assessments/{assessment_id}/submit-raw
```

**问卷提交（新）**
```
POST /api/mobile/questionnaires/{questionnaire_id}/submit-raw
```

#### 保留的API端点（用于获取数据）
```
GET /api/mobile/assessment-templates    # 获取评估量表模板
GET /api/mobile/questionnaire-templates # 获取问卷模板
GET /api/mobile/assessments             # 获取用户评估量表列表
GET /api/mobile/questionnaires          # 获取用户问卷列表
```

### 2. 数据结构变更

#### 提交数据格式变更

**修改前（包含分数）**
```json
{
  "answers": [
    {
      "question_id": "1",
      "answer": "选项A",
      "score": 3
    },
    {
      "question_id": "2",
      "answer": ["选项A", "选项B"],
      "score": 5
    }
  ]
}
```

**修改后（仅原始答案）**
```json
{
  "answers": [
    {
      "question_id": "1",
      "answer": "选项A"
    },
    {
      "question_id": "2",
      "answer": ["选项A", "选项B"]
    }
  ]
}
```

#### 响应数据格式

**评估量表响应**
```json
{
  "status": "success",
  "message": "评估量表提交成功",
  "data": {
    "assessment_id": 123,
    "response_id": 456,
    "result_id": 789,
    "total_score": 85,
    "max_score": 100,
    "percentage": 85.0,
    "result_category": "良好",
    "conclusion": "评估结果良好，大部分指标正常",
    "dimension_scores": {
      "emotion": {
        "name": "情绪症状",
        "score": 25,
        "max_score": 30,
        "weight": 1.0,
        "weighted_score": 25
      },
      "sleep": {
        "name": "睡眠症状",
        "score": 20,
        "max_score": 25,
        "weight": 1.2,
        "weighted_score": 24
      }
    },
    "completed_at": "2024-01-01T12:00:00"
  }
}
```

**问卷响应**
```json
{
  "status": "success",
  "message": "问卷提交成功",
  "data": {
    "questionnaire_id": 123,
    "response_id": 456,
    "result_id": 789,
    "total_score": 45,
    "max_score": 50,
    "completion_rate": 90.0,
    "result_category": "健康状况调查",
    "conclusion": "健康状况调查已完成，完成率: 90.0%",
    "completed_at": "2024-01-01T12:00:00"
  }
}
```

## 具体修改步骤

### 1. 移除客户端计分逻辑

#### 需要删除的文件/模块
- 计分规则配置文件
- 客户端计分函数
- 分数计算工具类
- 维度分数计算逻辑

#### 示例（React Native）
```javascript
// 删除这些文件
// src/utils/scoring.js
// src/config/scoringRules.js
// src/services/calculationService.js

// 删除这些函数
// calculateQuestionScore()
// calculateTotalScore()
// calculateDimensionScores()
// getScoreLevel()
```

#### 示例（Flutter）
```dart
// 删除这些文件
// lib/utils/scoring_utils.dart
// lib/models/scoring_rules.dart
// lib/services/calculation_service.dart

// 删除这些类和方法
// class ScoringUtils
// class ScoringRules
// calculateScore()
// getDimensionScores()
```

### 2. 更新数据提交逻辑

#### React Native 示例

**修改前**
```javascript
// 旧的提交逻辑
const submitAssessment = async (assessmentId, answers) => {
  const submitData = {
    answers: answers.map(answer => {
      const score = calculateQuestionScore(answer); // 移除这行
      return {
        question_id: answer.questionId,
        answer: answer.selectedOption,
        score: score // 移除这行
      };
    })
  };
  
  const response = await fetch(`/api/mobile/assessments/${assessmentId}/submit`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(submitData)
  });
  
  return response.json();
};
```

**修改后**
```javascript
// 新的提交逻辑
const submitAssessment = async (assessmentId, answers) => {
  const submitData = {
    answers: answers.map(answer => ({
      question_id: answer.questionId,
      answer: answer.selectedOption // 仅提交原始答案
    }))
  };
  
  const response = await fetch(`/api/mobile/assessments/${assessmentId}/submit-raw`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(submitData)
  });
  
  const result = await response.json();
  
  if (result.status === 'success') {
    // 处理后端返回的计分结果
    const {
      total_score,
      max_score,
      percentage,
      result_category,
      conclusion,
      dimension_scores
    } = result.data;
    
    // 显示结果
    showResults({
      score: total_score,
      maxScore: max_score,
      percentage: percentage,
      category: result_category,
      conclusion: conclusion,
      dimensions: dimension_scores
    });
  }
  
  return result;
};
```

#### Flutter 示例

**修改前**
```dart
// 旧的提交逻辑
Future<Map<String, dynamic>> submitAssessment(int assessmentId, List<Answer> answers) async {
  final submitData = {
    'answers': answers.map((answer) {
      final score = calculateQuestionScore(answer); // 移除这行
      return {
        'question_id': answer.questionId,
        'answer': answer.selectedOption,
        'score': score, // 移除这行
      };
    }).toList(),
  };
  
  final response = await http.post(
    Uri.parse('/api/mobile/assessments/$assessmentId/submit'),
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    },
    body: json.encode(submitData),
  );
  
  return json.decode(response.body);
}
```

**修改后**
```dart
// 新的提交逻辑
Future<Map<String, dynamic>> submitAssessment(int assessmentId, List<Answer> answers) async {
  final submitData = {
    'answers': answers.map((answer) => {
      'question_id': answer.questionId,
      'answer': answer.selectedOption, // 仅提交原始答案
    }).toList(),
  };
  
  final response = await http.post(
    Uri.parse('/api/mobile/assessments/$assessmentId/submit-raw'),
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    },
    body: json.encode(submitData),
  );
  
  final result = json.decode(response.body);
  
  if (result['status'] == 'success') {
    // 处理后端返回的计分结果
    final data = result['data'];
    final totalScore = data['total_score'];
    final maxScore = data['max_score'];
    final percentage = data['percentage'];
    final resultCategory = data['result_category'];
    final conclusion = data['conclusion'];
    final dimensionScores = data['dimension_scores'];
    
    // 显示结果
    showResults(AssessmentResult(
      score: totalScore,
      maxScore: maxScore,
      percentage: percentage,
      category: resultCategory,
      conclusion: conclusion,
      dimensions: dimensionScores,
    ));
  }
  
  return result;
}
```

### 3. 更新问卷提交逻辑

#### React Native 示例

```javascript
const submitQuestionnaire = async (questionnaireId, answers) => {
  const submitData = {
    answers: answers.map(answer => ({
      question_id: answer.questionId,
      answer: answer.selectedOption // 支持单选、多选、文本等
    }))
  };
  
  const response = await fetch(`/api/mobile/questionnaires/${questionnaireId}/submit-raw`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(submitData)
  });
  
  const result = await response.json();
  
  if (result.status === 'success') {
    const {
      completion_rate,
      result_category,
      conclusion
    } = result.data;
    
    showQuestionnaireResults({
      completionRate: completion_rate,
      category: result_category,
      conclusion: conclusion
    });
  }
  
  return result;
};
```

### 4. 更新进度条逻辑

由于移动端不再进行计分，进度条应该基于答题进度而不是分数计算进度。

#### React Native 示例

```javascript
// 更新进度条逻辑
const updateProgress = (answeredCount, totalCount) => {
  const progress = (answeredCount / totalCount) * 100;
  setProgressPercentage(progress);
  
  // 可以添加更详细的进度信息
  setProgressText(`已完成 ${answeredCount}/${totalCount} 题`);
};

// 在用户回答问题时调用
const handleAnswerChange = (questionId, answer) => {
  // 更新答案
  updateAnswer(questionId, answer);
  
  // 计算已回答的问题数量
  const answeredCount = answers.filter(a => a.answer !== null && a.answer !== '').length;
  const totalCount = questions.length;
  
  // 更新进度
  updateProgress(answeredCount, totalCount);
};
```

#### Flutter 示例

```dart
void updateProgress(int answeredCount, int totalCount) {
  final progress = answeredCount / totalCount;
  setState(() {
    progressPercentage = progress;
    progressText = '已完成 $answeredCount/$totalCount 题';
  });
}

void handleAnswerChange(String questionId, dynamic answer) {
  // 更新答案
  updateAnswer(questionId, answer);
  
  // 计算已回答的问题数量
  final answeredCount = answers.where((a) => a.answer != null && a.answer != '').length;
  final totalCount = questions.length;
  
  // 更新进度
  updateProgress(answeredCount, totalCount);
}
```

### 5. 更新结果显示逻辑

#### React Native 示例

```javascript
// 评估量表结果显示
const AssessmentResultScreen = ({ result }) => {
  const {
    total_score,
    max_score,
    percentage,
    result_category,
    conclusion,
    dimension_scores
  } = result;
  
  return (
    <View style={styles.container}>
      <Text style={styles.title}>评估结果</Text>
      
      {/* 总分显示 */}
      <View style={styles.scoreContainer}>
        <Text style={styles.scoreText}>
          {total_score}/{max_score}
        </Text>
        <Text style={styles.percentageText}>
          {percentage.toFixed(1)}%
        </Text>
      </View>
      
      {/* 结果分类 */}
      <Text style={styles.categoryText}>{result_category}</Text>
      
      {/* 结论 */}
      <Text style={styles.conclusionText}>{conclusion}</Text>
      
      {/* 维度分数 */}
      {dimension_scores && (
        <View style={styles.dimensionsContainer}>
          <Text style={styles.dimensionsTitle}>维度分析</Text>
          {Object.entries(dimension_scores).map(([key, dimension]) => (
            <View key={key} style={styles.dimensionItem}>
              <Text style={styles.dimensionName}>{dimension.name}</Text>
              <Text style={styles.dimensionScore}>
                {dimension.score}/{dimension.max_score}
              </Text>
            </View>
          ))}
        </View>
      )}
    </View>
  );
};
```

### 6. 更新历史记录功能

历史记录功能基本不需要修改，因为后端API保持兼容。

```javascript
// 获取历史记录（无需修改）
const getAssessmentHistory = async () => {
  const response = await fetch('/api/mobile/assessments', {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  const result = await response.json();
  return result.data;
};

// 获取详细结果（无需修改）
const getAssessmentResult = async (resultId) => {
  const response = await fetch(`/api/assessment-results/${resultId}`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  const result = await response.json();
  return result.data;
};
```

### 7. 错误处理更新

```javascript
// 更新错误处理逻辑
const handleSubmitError = (error) => {
  if (error.status === 400) {
    // 请求格式错误
    showAlert('提交失败', '请检查答案格式是否正确');
  } else if (error.status === 403) {
    // 权限错误
    showAlert('提交失败', '您没有权限提交此评估');
  } else if (error.status === 404) {
    // 资源不存在
    showAlert('提交失败', '评估或问卷不存在');
  } else if (error.status === 500) {
    // 服务器错误
    showAlert('提交失败', '服务器计分过程中发生错误，请稍后重试');
  } else {
    // 其他错误
    showAlert('提交失败', '网络错误，请检查网络连接');
  }
};
```

## 测试建议

### 1. 单元测试

```javascript
// 测试数据提交格式
describe('Assessment Submission', () => {
  test('should submit raw answers without scores', () => {
    const answers = [
      { question_id: '1', answer: '选项A' },
      { question_id: '2', answer: ['选项A', '选项B'] }
    ];
    
    const submitData = formatSubmissionData(answers);
    
    expect(submitData.answers).toHaveLength(2);
    expect(submitData.answers[0]).not.toHaveProperty('score');
    expect(submitData.answers[0]).toHaveProperty('answer');
  });
});
```

### 2. 集成测试

```javascript
// 测试完整提交流程
describe('Assessment Submission Flow', () => {
  test('should submit assessment and receive calculated results', async () => {
    const assessmentId = 123;
    const answers = [
      { question_id: '1', answer: '选项A' },
      { question_id: '2', answer: '选项B' }
    ];
    
    const result = await submitAssessment(assessmentId, answers);
    
    expect(result.status).toBe('success');
    expect(result.data).toHaveProperty('total_score');
    expect(result.data).toHaveProperty('result_category');
    expect(result.data).toHaveProperty('dimension_scores');
  });
});
```

## 部署注意事项

### 1. 版本兼容性

- 保留旧的API端点一段时间，确保平滑迁移
- 在移动端添加版本检查，引导用户更新

### 2. 用户通知

```javascript
// 版本检查示例
const checkAppVersion = async () => {
  const response = await fetch('/api/mobile/version-check');
  const result = await response.json();
  
  if (result.requiresUpdate) {
    showUpdateDialog({
      title: '应用更新',
      message: '为了获得更好的体验，请更新到最新版本',
      onUpdate: () => {
        // 跳转到应用商店
        openAppStore();
      }
    });
  }
};
```

### 3. 数据迁移

如果本地存储了计分相关的数据，需要清理：

```javascript
// 清理本地存储的计分数据
const cleanupLocalScoringData = () => {
  AsyncStorage.removeItem('scoring_rules');
  AsyncStorage.removeItem('cached_scores');
  AsyncStorage.removeItem('dimension_configs');
};
```

## 常见问题

### Q: 如果网络中断，答案会丢失吗？
A: 建议在本地保存答案，网络恢复后再提交。

### Q: 如何处理部分回答的情况？
A: 后端会自动处理部分回答，并在结果中标明完成率。

### Q: 维度分数如何显示？
A: 后端会返回详细的维度分数信息，包括各维度的得分和权重。

### Q: 如何确保数据安全？
A: 继续使用现有的认证机制，所有API调用都需要有效的token。

## 总结

通过以上修改，移动端将：

1. **简化逻辑**：移除复杂的计分逻辑，专注于用户体验
2. **提高准确性**：由后端统一计分，确保结果一致性
3. **便于维护**：计分规则变更只需修改后端，无需更新移动端
4. **增强功能**：支持更复杂的维度计分和分析

建议按照以上步骤逐步修改，并充分测试后再发布到生产环境。