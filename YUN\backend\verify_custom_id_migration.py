#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证user_id到custom_id迁移的脚本

此脚本将验证:
1. 分发表是否正确使用custom_id字段
2. 数据是否正确迁移
3. 外键关系是否正确
"""

import sqlite3
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def verify_table_structure(db_path, table_name):
    """验证表结构"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name=?
        """, (table_name,))
        
        if not cursor.fetchone():
            logger.warning(f"表 {table_name} 不存在")
            return False
        
        # 获取表结构
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        
        logger.info(f"\n表 {table_name} 的结构:")
        for col in columns:
            logger.info(f"  {col[1]} {col[2]} {'NOT NULL' if col[3] else 'NULL'} {'PRIMARY KEY' if col[5] else ''}")
        
        # 检查是否有custom_id字段
        column_names = [col[1] for col in columns]
        has_custom_id = 'custom_id' in column_names
        has_user_id = 'user_id' in column_names
        
        logger.info(f"  有custom_id字段: {has_custom_id}")
        logger.info(f"  有user_id字段: {has_user_id}")
        
        if has_user_id:
            logger.warning(f"警告: 表 {table_name} 仍然有user_id字段")
        
        if not has_custom_id:
            logger.error(f"错误: 表 {table_name} 缺少custom_id字段")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"验证表结构失败: {e}")
        return False
    finally:
        conn.close()

def verify_data_integrity(db_path):
    """验证数据完整性"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查assessment_distributions表的数据
        cursor.execute("SELECT COUNT(*) FROM assessment_distributions")
        ad_count = cursor.fetchone()[0]
        logger.info(f"\nassessment_distributions表记录数: {ad_count}")
        
        if ad_count > 0:
            # 检查custom_id是否都有效
            cursor.execute("""
                SELECT COUNT(*) FROM assessment_distributions ad
                LEFT JOIN users u ON ad.custom_id = u.custom_id
                WHERE u.custom_id IS NULL
            """)
            invalid_custom_ids = cursor.fetchone()[0]
            logger.info(f"无效的custom_id数量: {invalid_custom_ids}")
            
            # 显示一些示例数据
            cursor.execute("""
                SELECT ad.id, ad.custom_id, ad.distributor_custom_id, ad.status, u.username
                FROM assessment_distributions ad
                LEFT JOIN users u ON ad.custom_id = u.custom_id
                LIMIT 5
            """)
            sample_data = cursor.fetchall()
            logger.info("示例数据:")
            for row in sample_data:
                logger.info(f"  ID: {row[0]}, custom_id: {row[1]}, distributor: {row[2]}, status: {row[3]}, username: {row[4]}")
        
        # 检查questionnaire_distributions表的数据
        cursor.execute("SELECT COUNT(*) FROM questionnaire_distributions")
        qd_count = cursor.fetchone()[0]
        logger.info(f"\nquestionnaire_distributions表记录数: {qd_count}")
        
        if qd_count > 0:
            # 检查custom_id是否都有效
            cursor.execute("""
                SELECT COUNT(*) FROM questionnaire_distributions qd
                LEFT JOIN users u ON qd.custom_id = u.custom_id
                WHERE u.custom_id IS NULL
            """)
            invalid_custom_ids = cursor.fetchone()[0]
            logger.info(f"无效的custom_id数量: {invalid_custom_ids}")
            
            # 显示一些示例数据
            cursor.execute("""
                SELECT qd.id, qd.custom_id, qd.distributor_custom_id, qd.status, u.username
                FROM questionnaire_distributions qd
                LEFT JOIN users u ON qd.custom_id = u.custom_id
                LIMIT 5
            """)
            sample_data = cursor.fetchall()
            logger.info("示例数据:")
            for row in sample_data:
                logger.info(f"  ID: {row[0]}, custom_id: {row[1]}, distributor: {row[2]}, status: {row[3]}, username: {row[4]}")
        
        return True
        
    except Exception as e:
        logger.error(f"验证数据完整性失败: {e}")
        return False
    finally:
        conn.close()

def verify_sm006_data(db_path):
    """验证SM_006用户的数据"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        logger.info("\n=== 验证SM_006用户数据 ===")
        
        # 检查用户是否存在
        cursor.execute("SELECT * FROM users WHERE custom_id = 'SM_006'")
        user = cursor.fetchone()
        if user:
            logger.info(f"找到用户SM_006: {user}")
        else:
            logger.warning("未找到用户SM_006")
            return
        
        # 检查评估分发记录
        cursor.execute("""
            SELECT COUNT(*) FROM assessment_distributions 
            WHERE custom_id = 'SM_006'
        """)
        ad_count = cursor.fetchone()[0]
        logger.info(f"SM_006的评估分发记录数: {ad_count}")
        
        # 检查问卷分发记录
        cursor.execute("""
            SELECT COUNT(*) FROM questionnaire_distributions 
            WHERE custom_id = 'SM_006'
        """)
        qd_count = cursor.fetchone()[0]
        logger.info(f"SM_006的问卷分发记录数: {qd_count}")
        
        # 显示详细记录
        if ad_count > 0:
            cursor.execute("""
                SELECT id, assessment_id, status, created_at 
                FROM assessment_distributions 
                WHERE custom_id = 'SM_006'
                LIMIT 5
            """)
            records = cursor.fetchall()
            logger.info("SM_006的评估记录:")
            for record in records:
                logger.info(f"  ID: {record[0]}, assessment_id: {record[1]}, status: {record[2]}, created: {record[3]}")
        
        if qd_count > 0:
            cursor.execute("""
                SELECT id, questionnaire_id, status, created_at 
                FROM questionnaire_distributions 
                WHERE custom_id = 'SM_006'
                LIMIT 5
            """)
            records = cursor.fetchall()
            logger.info("SM_006的问卷记录:")
            for record in records:
                logger.info(f"  ID: {record[0]}, questionnaire_id: {record[1]}, status: {record[2]}, created: {record[3]}")
        
    except Exception as e:
        logger.error(f"验证SM_006数据失败: {e}")
    finally:
        conn.close()

def check_foreign_keys(db_path):
    """检查外键约束"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        logger.info("\n=== 检查外键约束 ===")
        
        # 启用外键检查
        cursor.execute("PRAGMA foreign_keys = ON")
        
        # 检查外键约束
        cursor.execute("PRAGMA foreign_key_check(assessment_distributions)")
        fk_errors = cursor.fetchall()
        if fk_errors:
            logger.error(f"assessment_distributions外键错误: {fk_errors}")
        else:
            logger.info("assessment_distributions外键检查通过")
        
        cursor.execute("PRAGMA foreign_key_check(questionnaire_distributions)")
        fk_errors = cursor.fetchall()
        if fk_errors:
            logger.error(f"questionnaire_distributions外键错误: {fk_errors}")
        else:
            logger.info("questionnaire_distributions外键检查通过")
        
    except Exception as e:
        logger.error(f"检查外键约束失败: {e}")
    finally:
        conn.close()

def main():
    """主函数"""
    db_path = r"c:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db"
    
    if not Path(db_path).exists():
        logger.error(f"数据库文件不存在: {db_path}")
        return
    
    logger.info(f"开始验证数据库: {db_path}")
    
    # 验证表结构
    logger.info("\n=== 验证表结构 ===")
    ad_ok = verify_table_structure(db_path, "assessment_distributions")
    qd_ok = verify_table_structure(db_path, "questionnaire_distributions")
    
    if not (ad_ok and qd_ok):
        logger.error("表结构验证失败")
        return
    
    # 验证数据完整性
    logger.info("\n=== 验证数据完整性 ===")
    if not verify_data_integrity(db_path):
        logger.error("数据完整性验证失败")
        return
    
    # 验证SM_006数据
    verify_sm006_data(db_path)
    
    # 检查外键约束
    check_foreign_keys(db_path)
    
    logger.info("\n=== 验证完成 ===")
    logger.info("迁移验证成功！所有检查都通过了。")

if __name__ == "__main__":
    main()