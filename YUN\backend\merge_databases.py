#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库合并脚本
用于分析和合并多个app.db文件
"""

import sqlite3
import os
import shutil
from datetime import datetime

def analyze_database(db_path):
    """分析数据库文件"""
    if not os.path.exists(db_path):
        return None
    
    size = os.path.getsize(db_path)
    info = {
        'path': db_path,
        'size': size,
        'tables': [],
        'user_count': 0,
        'has_data': False
    }
    
    if size < 1024:  # 小于1KB的文件可能是空的
        return info
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [t[0] for t in cursor.fetchall()]
        info['tables'] = tables
        
        # 检查用户表
        if 'users' in tables:
            cursor.execute('SELECT COUNT(*) FROM users')
            info['user_count'] = cursor.fetchone()[0]
            info['has_data'] = info['user_count'] > 0
        
        # 检查其他重要表是否有数据
        important_tables = ['medical_records', 'lab_reports', 'health_records']
        for table in important_tables:
            if table in tables:
                cursor.execute(f'SELECT COUNT(*) FROM {table}')
                count = cursor.fetchone()[0]
                if count > 0:
                    info['has_data'] = True
                    break
        
        conn.close()
    except Exception as e:
        print(f"分析数据库 {db_path} 时出错: {e}")
    
    return info

def main():
    print("开始分析数据库文件...")
    
    # 数据库文件路径
    db_files = [
        'app.db',  # 主数据库
        'app/app.db',  # 子目录中的数据库
        'app/db/app.db',  # 另一个子目录中的数据库
        'app/tools/app.db'  # 工具目录中的数据库
    ]
    
    # 分析所有数据库文件
    db_info = []
    for db_file in db_files:
        info = analyze_database(db_file)
        if info:
            db_info.append(info)
    
    # 显示分析结果
    print("\n数据库文件分析结果:")
    print("=" * 60)
    
    main_db = None
    for info in db_info:
        print(f"文件: {info['path']}")
        print(f"  大小: {info['size']:,} bytes")
        print(f"  表数量: {len(info['tables'])}")
        print(f"  用户数量: {info['user_count']}")
        print(f"  包含数据: {'是' if info['has_data'] else '否'}")
        
        if info['has_data'] and info['user_count'] > 0:
            if main_db is None or info['size'] > main_db['size']:
                main_db = info
        print()
    
    # 确定主数据库
    if main_db:
        print(f"建议使用的主数据库: {main_db['path']}")
        print(f"  原因: 包含 {main_db['user_count']} 个用户，大小 {main_db['size']:,} bytes")
        
        # 如果主数据库不是 app.db，建议重命名
        if main_db['path'] != 'app.db':
            print("\n建议操作:")
            print(f"1. 备份当前 app.db")
            print(f"2. 将 {main_db['path']} 复制为 app.db")
            print(f"3. 删除其他空的或重复的数据库文件")
    else:
        print("未找到包含有效数据的数据库文件")
    
    # 检查是否有硬编码的用户名密码
    print("\n检查硬编码用户凭据...")
    if main_db:
        try:
            conn = sqlite3.connect(main_db['path'])
            cursor = conn.cursor()
            cursor.execute("SELECT username, email, role FROM users")
            users = cursor.fetchall()
            
            print("当前用户列表:")
            for user in users:
                print(f"  用户名: {user[0]}, 邮箱: {user[1]}, 角色: {user[2]}")
            
            conn.close()
        except Exception as e:
            print(f"读取用户数据时出错: {e}")

if __name__ == "__main__":
    main()