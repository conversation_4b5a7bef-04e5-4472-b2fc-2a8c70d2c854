# 生产环境模拟数据配置指南

## 概述

本文档提供了在生产环境中配置模拟数据管理器的最佳实践和建议。模拟数据管理器是一个强大的工具，可以在开发和测试环境中提供模拟数据，但在生产环境中需要谨慎配置，以确保系统安全性和性能。

## 环境变量配置

### 主开关

`ENABLE_MOCK_DATA` 是控制整个模拟数据系统的主开关。在生产环境中，**强烈建议将此变量设置为 `false`**。

```bash
# 生产环境配置示例
ENABLE_MOCK_DATA=false
```

### 细分控制

如果在特殊情况下需要在生产环境中启用部分模拟数据功能，可以使用以下细分控制变量：

```bash
# 主开关 - 通常在生产环境中设置为 false
ENABLE_MOCK_DATA=false

# 细分控制 - 只有在主开关为 true 时才会生效
MOCK_DATA_ENABLED=false
MOCK_DASHBOARD_ENABLED=false
MOCK_SERVICE_STATS_ENABLED=false
MOCK_HEALTH_MONITOR_ENABLED=false
```

## 部署策略

### 多环境配置

建议为不同环境维护不同的配置文件：

1. **开发环境**：启用所有模拟数据功能
   ```bash
   ENABLE_MOCK_DATA=true
   MOCK_DATA_ENABLED=true
   MOCK_DASHBOARD_ENABLED=true
   MOCK_SERVICE_STATS_ENABLED=true
   MOCK_HEALTH_MONITOR_ENABLED=true
   ```

2. **测试环境**：根据测试需求选择性启用
   ```bash
   ENABLE_MOCK_DATA=true
   MOCK_DATA_ENABLED=true
   MOCK_DASHBOARD_ENABLED=false  # 使用真实数据测试仪表盘
   MOCK_SERVICE_STATS_ENABLED=true
   MOCK_HEALTH_MONITOR_ENABLED=true
   ```

3. **生产环境**：禁用所有模拟数据功能
   ```bash
   ENABLE_MOCK_DATA=false
   MOCK_DATA_ENABLED=false
   MOCK_DASHBOARD_ENABLED=false
   MOCK_SERVICE_STATS_ENABLED=false
   MOCK_HEALTH_MONITOR_ENABLED=false
   ```

### 容器化部署

在使用 Docker 或 Kubernetes 等容器化部署时，可以通过环境变量注入来配置模拟数据：

```yaml
# docker-compose.yml 示例
version: '3'
services:
  backend:
    image: your-app-image
    environment:
      - ENABLE_MOCK_DATA=false  # 生产环境禁用模拟数据
```

```yaml
# Kubernetes deployment 示例
apiVersion: apps/v1
kind: Deployment
metadata:
  name: your-app
spec:
  template:
    spec:
      containers:
      - name: your-app
        image: your-app-image
        env:
        - name: ENABLE_MOCK_DATA
          value: "false"  # 生产环境禁用模拟数据
```

## 安全考虑

### 数据敏感性

模拟数据可能包含敏感信息的模拟版本。在生产环境中启用模拟数据可能导致：

1. 数据混淆：真实数据与模拟数据混合
2. 安全风险：模拟数据可能暴露系统结构
3. 合规问题：在某些受监管行业中可能违反规定

### 访问控制

如果在生产环境中需要启用模拟数据（例如演示实例），请确保：

1. 实施严格的访问控制
2. 明确标记数据来源（真实或模拟）
3. 限制模拟数据的范围和敏感度

## 性能影响

在生产环境中启用模拟数据生成可能会对系统性能产生影响：

1. 增加 CPU 和内存使用
2. 可能引入额外的延迟
3. 增加日志和监控的复杂性

## 监控和日志

无论环境如何配置，都应该监控模拟数据管理器的状态：

```python
# 日志示例
logger.info(f"Mock Data Manager enabled: {mock_data_manager.is_enabled}")
logger.info(f"Mock Dashboard enabled: {mock_data_manager.is_dashboard_enabled}")
```

## 故障排除

### 常见问题

1. **模拟数据意外出现在生产环境**
   - 检查环境变量配置
   - 验证部署流程
   - 检查代码中的硬编码默认值

2. **环境变量未正确加载**
   - 确认环境变量设置方法
   - 检查应用程序启动日志
   - 验证配置文件权限

### 诊断命令

```bash
# 检查当前环境变量设置
echo $ENABLE_MOCK_DATA

# 在 Docker 容器中检查
docker exec -it your-container-name env | grep MOCK

# 在 Kubernetes Pod 中检查
kubectl exec -it your-pod-name -- env | grep MOCK
```

## 最佳实践总结

1. **默认安全**：在生产环境中默认禁用所有模拟数据功能
2. **环境隔离**：为不同环境维护不同的配置
3. **明确文档**：记录所有环境中的模拟数据配置
4. **定期审核**：定期检查生产环境配置，确保模拟数据功能被正确禁用
5. **自动化验证**：在部署流程中添加检查，验证生产环境中模拟数据配置的正确性

## 相关文档

- [模拟数据使用指南](../MOCK_DATA_GUIDE.md)
- [前端数据模式管理](../frontend/MOCK_DATA_GUIDE.md)
- [环境配置指南](./ENVIRONMENT_CONFIG.md)