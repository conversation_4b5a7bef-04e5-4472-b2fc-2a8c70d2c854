/**
 * 健康资料相关API
 */
import request from '@/utils/request'

/**
 * 获取健康记录列表
 * @param {Object} params 查询参数
 * @returns {Promise} 请求结果
 */
export function getHealthRecords(params) {
  return request({
    url: '/api/user-health-records/user/' + (params.custom_id || ''),
    method: 'get',
    params: {
      ...params
      // 移除强制设置record_type为'health'，允许获取所有类型的记录
      // record_type: 'health'
    }
  })
}

/**
 * 获取健康记录详情
 * @param {Number} id 记录ID
 * @param {String} customId 用户ID
 * @returns {Promise} 请求结果
 */
export function getHealthRecord(id, customId) {
  return request({
    url: `/api/user-health-records/user/${customId}`,
    method: 'get',
    params: {
      record_type: 'health',
      record_id: id
    }
  })
}

/**
 * 创建健康记录
 * @param {Object} data 记录数据
 * @returns {Promise} 请求结果
 */
export function createHealthRecord(data) {
  return request({
    url: `/api/user-health-records/user/${data.custom_id || ''}`,
    method: 'post',
    data: {
      ...data,
      record_type: 'health'
    }
  })
}

/**
 * 更新健康记录
 * @param {Number} id 记录ID
 * @param {Object} data 记录数据
 * @returns {Promise} 请求结果
 */
export function updateHealthRecord(id, data) {
  return request({
    url: `/api/user-health-records/user/${data.custom_id || ''}/${id}`,
    method: 'put',
    data: {
      ...data,
      record_type: 'health'
    }
  })
}

/**
 * 删除健康记录
 * @param {Number} id 记录ID
 * @param {String} customId 用户ID
 * @returns {Promise} 请求结果
 */
export function deleteHealthRecord(id, customId) {
  return request({
    url: `/api/user-health-records/user/${customId}/${id}`,
    method: 'delete',
    params: {
      record_type: 'health'
    }
  })
}

/**
 * 获取医疗记录列表
 * @param {Object} params 查询参数
 * @returns {Promise} 请求结果
 */
export function getMedicalRecords(params) {
  return request({
    url: '/api/user-health-records/user/' + (params.custom_id || ''),
    method: 'get',
    params: {
      ...params,
      record_type: 'medical'
    }
  })
}

/**
 * 获取医疗记录详情
 * @param {Number} id 记录ID
 * @param {String} customId 用户ID
 * @returns {Promise} 请求结果
 */
export function getMedicalRecord(id, customId) {
  return request({
    url: `/api/user-health-records/user/${customId}`,
    method: 'get',
    params: {
      record_type: 'medical',
      record_id: id
    }
  })
}

/**
 * 创建医疗记录
 * @param {Object} data 记录数据
 * @returns {Promise} 请求结果
 */
export function createMedicalRecord(data) {
  return request({
    url: `/api/user-health-records/user/${data.custom_id || ''}`,
    method: 'post',
    data: {
      ...data,
      record_type: 'medical'
    }
  })
}

/**
 * 更新医疗记录
 * @param {Number} id 记录ID
 * @param {Object} data 记录数据
 * @returns {Promise} 请求结果
 */
export function updateMedicalRecord(id, data) {
  return request({
    url: `/api/user-health-records/user/${data.custom_id || ''}/${id}`,
    method: 'put',
    data: {
      ...data,
      record_type: 'medical'
    }
  })
}

/**
 * 删除医疗记录
 * @param {Number} id 记录ID
 * @param {String} customId 用户ID
 * @returns {Promise} 请求结果
 */
export function deleteMedicalRecord(id, customId) {
  return request({
    url: `/api/user-health-records/user/${customId}/${id}`,
    method: 'delete',
    params: {
      record_type: 'medical'
    }
  })
}
