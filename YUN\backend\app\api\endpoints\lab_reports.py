"""实验室报告API路由
"""
from typing import Any, List, Dict, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body, status
from sqlalchemy.orm import Session
from datetime import datetime
from app.utils.field_compatibility import ensure_field_compatibility, ensure_list_field_compatibility

from app.db.session import get_db
from app.models.user import User
from app.core.auth import get_current_active_user_custom
from app.api import deps

router = APIRouter()

# 管理端API - 获取特定用户的实验室报告
@router.get("/user/{custom_id}", response_model=Dict[str, Any])
def read_user_records(
    *,
    db: Session = Depends(deps.get_db),
    custom_id: str = Path(..., description="用户ID"),
    report_type: Optional[str] = Query(None, description="报告类型"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    current_user: User = Depends(get_current_active_user_custom)
) -> Any:
    """
    获取指定用户的实验室报告列表
    """
    print(f"lab_reports.py - 获取用户记录 - 用户ID: {custom_id}")

    # 支持数字ID和自定义ID查找用户
    user = None
    if custom_id.isdigit():
        user = db.query(User).filter(User.id == int(custom_id)).first()
    else:
        user = db.query(User).filter(User.custom_id == custom_id).first()
    
    if not user:
        print(f"用户{custom_id}不存在，返回404异常")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到用户ID: {custom_id}"
        )

    # 权限校验
    if current_user.id != user.id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        print(f"权限检查失败 - 当前用户: {current_user.custom_id}, 角色: {current_user.role}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限查看此用户的数据"
        )

    try:
        # 导入必要的模块
        from app.models.lab_report import LabReport

        # 构建查询
        query = db.query(LabReport).filter(LabReport.custom_id == user.custom_id)
        
        # 只在report_type为合法且在枚举中时才加过滤，否则不加
        if report_type is not None:
            try:
                from app.models.lab_report import LabReportType
                valid_types = [e.value for e in LabReportType]
                if report_type in valid_types:
                    query = query.filter(LabReport.report_type == report_type)
                elif report_type != '':
                    print(f"report_type非法或未定义: {report_type}, 有效类型为: {valid_types}，返回空结果")
                    return {"status": "success", "total": 0, "items": [], "message": f"无效的report_type: {report_type}，有效类型为: {valid_types}"}
                # report_type为None或空字符串时，不加过滤，查全部
            except Exception as e:
                print(f"report_type枚举校验异常: {e}, 返回空结果")
                return {"status": "success", "total": 0, "items": [], "message": f"report_type参数异常: {str(e)}"}
        
        # 修正：确保 start_date 和 end_date 为 datetime 类型
        def parse_date_safe(val):
            if isinstance(val, datetime) or val is None:
                return val
            try:
                return datetime.fromisoformat(val)
            except Exception:
                try:
                    return datetime.strptime(val, "%Y-%m-%d")
                except Exception:
                    return None
        if start_date is not None:
            start_date = parse_date_safe(start_date)
        if end_date is not None:
            end_date = parse_date_safe(end_date)
        if start_date:
            query = query.filter(LabReport.report_date >= start_date)
        if end_date:
            query = query.filter(LabReport.report_date <= end_date)
        
        # 获取总数
        total = query.count()
        
        # 获取分页数据
        records = []
        if total > 0:
            lab_reports = query.order_by(LabReport.created_at.desc()).offset(skip).limit(limit).all()
            
            # 转换为字典列表
            for report in lab_reports:
                records.append({
                    "id": report.id,
                    "custom_id": report.custom_id,
                    "lab_name": report.lab_name,
                    "report_type": report.report_type,
                    "report_date": report.report_date.isoformat() if report.report_date else None,
                    "results": report.results,
                    "notes": report.notes
                })
        
        print(f"用户{custom_id}查询结果 - 总数: {total}, 返回记录数: {len(records)}")
        return {
            "status": "success",
            "total": total,
            "items": records
        }
    except Exception as e:
        print(f"获取用户实验室报告时出错: {str(e)}")
        # 返回空结果而不是抛出异常
        return {
            "status": "success",
            "total": 0,
            "items": []
        }

@router.get("/lab-reports/user/{custom_id}", response_model=Dict[str, Any])
def read_user_lab_reports(
    *,
    db: Session = Depends(deps.get_db),
    custom_id: str = Path(..., description="用户ID"),
    report_type: Optional[str] = Query(None, description="报告类型"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    current_user: User = Depends(get_current_active_user_custom)
) -> Any:
    """
    获取指定用户的实验室报告列表
    """
    try:
        from app.models.lab_report import LabReport, LabReportType
        user = None
        if custom_id.isdigit():
            user = db.query(User).filter(User.id == int(custom_id)).first()
        else:
            user = db.query(User).filter(User.custom_id == custom_id).first()
        if not user:
            return {"total": 0, "records": []}
        if current_user.id != user.id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
            return {"total": 0, "records": []}
        query = db.query(LabReport).filter(LabReport.custom_id == user.custom_id)
        # 仅当report_type为合法枚举值时加过滤，否则不加
        if report_type:
            try:
                valid_types = [e.value for e in LabReportType]
                if report_type in valid_types:
                    query = query.filter(LabReport.report_type == report_type)
                else:
                    return {"total": 0, "records": [], "message": f"无效的report_type: {report_type}，有效类型为: {valid_types}"}
            except Exception as e:
                return {"total": 0, "records": [], "message": f"report_type参数异常: {str(e)}"}
        # 修正：确保 start_date 和 end_date 为 datetime 类型
        def parse_date_safe(val):
            if isinstance(val, datetime) or val is None:
                return val
            try:
                return datetime.fromisoformat(val)
            except Exception:
                try:
                    return datetime.strptime(val, "%Y-%m-%d")
                except Exception:
                    return None
        if start_date is not None:
            start_date = parse_date_safe(start_date)
        if end_date is not None:
            end_date = parse_date_safe(end_date)
        if start_date:
            query = query.filter(LabReport.report_date >= start_date)
        if end_date:
            query = query.filter(LabReport.report_date <= end_date)
        total = query.count()
        records = []
        if total > 0:
            lab_reports = query.order_by(LabReport.created_at.desc()).offset(skip).limit(limit).all()
            for report in lab_reports:
                records.append({
                    "id": report.id,
                    "custom_id": report.custom_id,
                    "report_type": report.report_type.value if hasattr(report, 'report_type') and report.report_type else None,
                    "hospital_name": report.hospital_name,
                    "department": report.department,
                    "test_date": report.test_date.isoformat() if report.test_date else None,
                    "report_date": report.report_date.isoformat() if report.report_date else None,
                    "doctor_name": report.doctor_name,
                    "diagnosis": report.diagnosis,
                    "notes": report.notes,
                    "is_abnormal": report.is_abnormal
                })
        return {"total": total, "records": records}
    except Exception as e:
        print(f"获取用户实验室报告时出错: {str(e)}")
        return {"total": 0, "records": []}

# 移动端API
@router.get("/lab-reports", response_model=Dict[str, Any])
def mobile_get_lab_reports(
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
) -> Any:
    """
    移动端获取实验室报告列表
    """
    total = 1  # 示例总数

    # 示例记录
    reports = [
        {
            "id": 1,
            "custom_id": current_user.id,
            "lab_name": "示例实验室",
            "report_type": "血常规",
            "report_date": datetime.now().isoformat(),
            "results": {
                "红细胞计数": "4.5 x 10^12/L",
                "白细胞计数": "7.2 x 10^9/L",
                "血红蛋白": "140 g/L",
                "血小板计数": "250 x 10^9/L"
            },
            "notes": "正常范围内"
        }
    ]

    return {
        "status": "success",
        "total": total,
        "page": page,
        "limit": limit,
        "items": reports
    }