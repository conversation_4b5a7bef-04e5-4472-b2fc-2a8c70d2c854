<template>
  <div class="deployment-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><Upload /></el-icon>
        部署管理
      </h1>
      <p class="page-description">管理应用部署、版本发布和环境配置</p>
    </div>

    <!-- 部署概览 -->
    <el-row :gutter="20" class="deployment-overview">
      <el-col :span="6">
        <el-card class="overview-card" shadow="hover">
          <div class="overview-content">
            <div class="overview-icon">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="overview-info">
              <h3>{{ successfulDeployments }}</h3>
              <p>成功部署</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card" shadow="hover">
          <div class="overview-content">
            <div class="overview-icon failed">
              <el-icon><CircleClose /></el-icon>
            </div>
            <div class="overview-info">
              <h3>{{ failedDeployments }}</h3>
              <p>失败部署</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card" shadow="hover">
          <div class="overview-content">
            <div class="overview-icon running">
              <el-icon><Loading /></el-icon>
            </div>
            <div class="overview-info">
              <h3>{{ runningDeployments }}</h3>
              <p>进行中</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card" shadow="hover">
          <div class="overview-content">
            <div class="overview-icon environments">
              <el-icon><Monitor /></el-icon>
            </div>
            <div class="overview-info">
              <h3>{{ totalEnvironments }}</h3>
              <p>部署环境</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速部署 -->
    <el-card class="quick-deploy" shadow="hover">
      <template #header>
        <span>快速部署</span>
      </template>
      
      <div class="deploy-form">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-select v-model="quickDeploy.environment" placeholder="选择环境" style="width: 100%">
              <el-option label="开发环境" value="development" />
              <el-option label="测试环境" value="testing" />
              <el-option label="预发布环境" value="staging" />
              <el-option label="生产环境" value="production" />
            </el-select>
          </el-col>
          
          <el-col :span="6">
            <el-select v-model="quickDeploy.branch" placeholder="选择分支" style="width: 100%">
              <el-option label="main" value="main" />
              <el-option label="develop" value="develop" />
              <el-option label="release" value="release" />
              <el-option label="hotfix" value="hotfix" />
            </el-select>
          </el-col>
          
          <el-col :span="6">
            <el-select v-model="quickDeploy.version" placeholder="选择版本" style="width: 100%">
              <el-option label="v1.0.0" value="v1.0.0" />
              <el-option label="v1.0.1" value="v1.0.1" />
              <el-option label="v1.1.0" value="v1.1.0" />
              <el-option label="latest" value="latest" />
            </el-select>
          </el-col>
          
          <el-col :span="6">
            <el-button 
              type="primary" 
              size="large"
              @click="startQuickDeploy"
              :loading="quickDeploying"
              style="width: 100%"
            >
              <el-icon><Upload /></el-icon>
              开始部署
            </el-button>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 部署历史 -->
    <el-card class="deployment-history" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>部署历史</span>
          <div class="header-actions">
            <el-input
              v-model="searchText"
              placeholder="搜索部署记录"
              size="small"
              style="width: 200px; margin-right: 10px;"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            
            <el-select v-model="statusFilter" size="small" style="width: 120px; margin-right: 10px;">
              <el-option label="全部状态" value="all" />
              <el-option label="成功" value="success" />
              <el-option label="失败" value="failed" />
              <el-option label="进行中" value="running" />
              <el-option label="已回滚" value="rollback" />
            </el-select>
            
            <el-select v-model="environmentFilter" size="small" style="width: 120px; margin-right: 10px;">
              <el-option label="全部环境" value="all" />
              <el-option label="开发" value="development" />
              <el-option label="测试" value="testing" />
              <el-option label="预发布" value="staging" />
              <el-option label="生产" value="production" />
            </el-select>
            
            <el-button 
              type="primary" 
              size="small" 
              @click="refreshDeployments"
              :loading="refreshLoading"
            >
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table :data="filteredDeployments" stripe style="width: 100%">
        <el-table-column prop="id" label="部署ID" width="100" />
        
        <el-table-column prop="environment" label="环境" width="100">
          <template #default="{ row }">
            <el-tag :type="getEnvironmentType(row.environment)" size="small">
              {{ getEnvironmentText(row.environment) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="version" label="版本" width="120">
          <template #default="{ row }">
            <div class="version-info">
              <el-tag size="small">{{ row.version }}</el-tag>
              <div class="branch">{{ row.branch }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              <el-icon v-if="row.status === 'running'" class="rotating"><Loading /></el-icon>
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="progress" label="进度" width="150">
          <template #default="{ row }">
            <div class="progress-info">
              <el-progress 
                :percentage="row.progress" 
                :status="getProgressStatus(row.status)"
                :show-text="false"
                :stroke-width="8"
              />
              <span class="progress-text">{{ row.progress }}%</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="duration" label="耗时" width="100">
          <template #default="{ row }">
            {{ formatDuration(row.duration) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="deployer" label="部署人" width="100" />
        
        <el-table-column prop="startTime" label="开始时间" width="150">
          <template #default="{ row }">
            {{ formatTime(row.startTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button-group size="small">
              <el-button type="info" @click="viewDeploymentDetails(row)">
                <el-icon><View /></el-icon>
                详情
              </el-button>
              
              <el-button 
                v-if="row.status === 'success' && row.environment !== 'production'"
                type="warning" 
                @click="promoteDeployment(row)"
              >
                <el-icon><Top /></el-icon>
                推广
              </el-button>
              
              <el-button 
                v-if="row.status === 'success'"
                type="danger" 
                @click="rollbackDeployment(row)"
              >
                <el-icon><RefreshLeft /></el-icon>
                回滚
              </el-button>
              
              <el-button 
                v-if="row.status === 'running'"
                type="danger" 
                @click="cancelDeployment(row)"
              >
                <el-icon><Close /></el-icon>
                取消
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 环境管理 -->
    <el-card class="environment-management" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>环境管理</span>
          <div class="header-actions">
            <el-button 
              type="success" 
              size="small" 
              @click="showEnvironmentDialog"
            >
              <el-icon><Plus /></el-icon>
              添加环境
            </el-button>
          </div>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6" v-for="env in environments" :key="env.id">
          <el-card class="environment-card" shadow="hover">
            <div class="env-header">
              <div class="env-name">
                <div class="env-icon" :class="env.type">
                  <el-icon><component :is="getEnvironmentIcon(env.type)" /></el-icon>
                </div>
                <div>
                  <h4>{{ env.name }}</h4>
                  <p>{{ env.description }}</p>
                </div>
              </div>
              
              <div class="env-status">
                <el-tag :type="env.status === 'healthy' ? 'success' : 'danger'" size="small">
                  {{ env.status === 'healthy' ? '健康' : '异常' }}
                </el-tag>
              </div>
            </div>
            
            <div class="env-info">
              <div class="info-item">
                <span class="label">URL:</span>
                <span class="value">{{ env.url }}</span>
              </div>
              
              <div class="info-item">
                <span class="label">版本:</span>
                <span class="value">{{ env.currentVersion }}</span>
              </div>
              
              <div class="info-item">
                <span class="label">最后部署:</span>
                <span class="value">{{ formatTime(env.lastDeployment) }}</span>
              </div>
            </div>
            
            <div class="env-actions">
              <el-button-group size="small">
                <el-button type="primary" @click="deployToEnvironment(env)">
                  <el-icon><Upload /></el-icon>
                  部署
                </el-button>
                
                <el-button type="info" @click="viewEnvironmentLogs(env)">
                  <el-icon><Document /></el-icon>
                  日志
                </el-button>
                
                <el-button type="success" @click="editEnvironment(env)">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
              </el-button-group>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>

    <!-- 部署详情对话框 -->
    <el-dialog 
      v-model="detailsDialogVisible" 
      :title="'部署详情 - ' + (currentDeployment?.id || '')"
      width="80%"
      top="5vh"
    >
      <div class="deployment-details" v-if="currentDeployment">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="基本信息" name="info">
            <div class="basic-info">
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="info-group">
                    <h4>部署信息</h4>
                    <div class="info-item">
                      <span class="label">部署ID:</span>
                      <span class="value">{{ currentDeployment.id }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">环境:</span>
                      <el-tag :type="getEnvironmentType(currentDeployment.environment)" size="small">
                        {{ getEnvironmentText(currentDeployment.environment) }}
                      </el-tag>
                    </div>
                    <div class="info-item">
                      <span class="label">版本:</span>
                      <span class="value">{{ currentDeployment.version }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">分支:</span>
                      <span class="value">{{ currentDeployment.branch }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">状态:</span>
                      <el-tag :type="getStatusType(currentDeployment.status)" size="small">
                        {{ getStatusText(currentDeployment.status) }}
                      </el-tag>
                    </div>
                  </div>
                </el-col>
                
                <el-col :span="12">
                  <div class="info-group">
                    <h4>时间信息</h4>
                    <div class="info-item">
                      <span class="label">开始时间:</span>
                      <span class="value">{{ formatTime(currentDeployment.startTime) }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">结束时间:</span>
                      <span class="value">{{ currentDeployment.endTime ? formatTime(currentDeployment.endTime) : '-' }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">耗时:</span>
                      <span class="value">{{ formatDuration(currentDeployment.duration) }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">部署人:</span>
                      <span class="value">{{ currentDeployment.deployer }}</span>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="部署步骤" name="steps">
            <div class="deployment-steps">
              <el-timeline>
                <el-timeline-item 
                  v-for="step in deploymentSteps" 
                  :key="step.id"
                  :type="getStepType(step.status)"
                  :icon="getStepIcon(step.status)"
                  :timestamp="formatTime(step.timestamp)"
                >
                  <div class="step-content">
                    <h4>{{ step.name }}</h4>
                    <p>{{ step.description }}</p>
                    <div v-if="step.status === 'running'" class="step-progress">
                      <el-progress :percentage="step.progress" :show-text="false" />
                    </div>
                    <div v-if="step.error" class="step-error">
                      <el-alert :title="step.error" type="error" :closable="false" />
                    </div>
                  </div>
                </el-timeline-item>
              </el-timeline>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="部署日志" name="logs">
            <div class="deployment-logs">
              <div class="log-toolbar">
                <el-button-group size="small">
                  <el-button @click="refreshDeploymentLogs">
                    <el-icon><Refresh /></el-icon>
                    刷新
                  </el-button>
                  <el-button @click="downloadDeploymentLogs">
                    <el-icon><Download /></el-icon>
                    下载
                  </el-button>
                  <el-button @click="clearDeploymentLogs">
                    <el-icon><Delete /></el-icon>
                    清空
                  </el-button>
                </el-button-group>
              </div>
              
              <div class="log-content">
                <pre>{{ deploymentLogs }}</pre>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>

    <!-- 环境编辑对话框 -->
    <el-dialog 
      v-model="environmentDialogVisible" 
      :title="environmentEditMode === 'create' ? '添加环境' : '编辑环境'"
      width="50%"
    >
      <el-form :model="currentEnvironment" :rules="environmentRules" ref="environmentForm" label-width="100px">
        <el-form-item label="环境名称" prop="name">
          <el-input v-model="currentEnvironment.name" />
        </el-form-item>
        
        <el-form-item label="环境类型" prop="type">
          <el-select v-model="currentEnvironment.type" style="width: 100%">
            <el-option label="开发环境" value="development" />
            <el-option label="测试环境" value="testing" />
            <el-option label="预发布环境" value="staging" />
            <el-option label="生产环境" value="production" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="访问URL" prop="url">
          <el-input v-model="currentEnvironment.url" />
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input v-model="currentEnvironment.description" type="textarea" :rows="3" />
        </el-form-item>
        
        <el-form-item label="配置" prop="config">
          <el-input 
            v-model="currentEnvironment.config"
            type="textarea"
            :rows="8"
            placeholder="请输入JSON格式的环境配置"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="environmentDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveEnvironment" :loading="saveLoading">
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Upload,
  CircleCheck,
  CircleClose,
  Loading,
  Monitor,
  Search,
  Refresh,
  View,
  Top,
  RefreshLeft,
  Close,
  Plus,
  Edit,
  Document,
  Delete
} from '@element-plus/icons-vue';
import axios from 'axios';

// 响应式数据
const searchText = ref('');
const statusFilter = ref('all');
const environmentFilter = ref('all');
const refreshLoading = ref(false);
const quickDeploying = ref(false);
const detailsDialogVisible = ref(false);
const environmentDialogVisible = ref(false);
const environmentEditMode = ref('create');
const saveLoading = ref(false);
const activeTab = ref('info');
const currentDeployment = ref(null);
const deploymentLogs = ref('');

const quickDeploy = reactive({
  environment: '',
  branch: '',
  version: ''
});

const currentEnvironment = reactive({
  name: '',
  type: 'development',
  url: '',
  description: '',
  config: ''
});

const deployments = ref([
  {
    id: 'D001',
    environment: 'production',
    version: 'v1.0.0',
    branch: 'main',
    status: 'success',
    progress: 100,
    duration: 180000,
    deployer: 'admin',
    startTime: new Date(Date.now() - 3600000).toISOString(),
    endTime: new Date(Date.now() - 3300000).toISOString()
  },
  {
    id: 'D002',
    environment: 'staging',
    version: 'v1.0.1',
    branch: 'develop',
    status: 'running',
    progress: 65,
    duration: 120000,
    deployer: 'developer',
    startTime: new Date(Date.now() - 120000).toISOString(),
    endTime: null
  },
  {
    id: 'D003',
    environment: 'testing',
    version: 'v1.0.0',
    branch: 'feature/new-ui',
    status: 'failed',
    progress: 45,
    duration: 90000,
    deployer: 'tester',
    startTime: new Date(Date.now() - 7200000).toISOString(),
    endTime: new Date(Date.now() - 7110000).toISOString()
  },
  {
    id: 'D004',
    environment: 'development',
    version: 'v1.1.0',
    branch: 'develop',
    status: 'rollback',
    progress: 100,
    duration: 150000,
    deployer: 'admin',
    startTime: new Date(Date.now() - 86400000).toISOString(),
    endTime: new Date(Date.now() - 86250000).toISOString()
  }
]);

const environments = ref([
  {
    id: 1,
    name: '生产环境',
    type: 'production',
    url: 'https://health.example.com',
    description: '生产环境，面向最终用户',
    status: 'healthy',
    currentVersion: 'v1.0.0',
    lastDeployment: new Date(Date.now() - 3600000).toISOString()
  },
  {
    id: 2,
    name: '预发布环境',
    type: 'staging',
    url: 'https://staging.health.example.com',
    description: '预发布环境，用于最终测试',
    status: 'healthy',
    currentVersion: 'v1.0.1',
    lastDeployment: new Date(Date.now() - 120000).toISOString()
  },
  {
    id: 3,
    name: '测试环境',
    type: 'testing',
    url: 'https://test.health.example.com',
    description: '测试环境，用于功能测试',
    status: 'unhealthy',
    currentVersion: 'v1.0.0',
    lastDeployment: new Date(Date.now() - 7200000).toISOString()
  },
  {
    id: 4,
    name: '开发环境',
    type: 'development',
    url: 'https://dev.health.example.com',
    description: '开发环境，用于日常开发',
    status: 'healthy',
    currentVersion: 'v1.1.0',
    lastDeployment: new Date(Date.now() - 86400000).toISOString()
  }
]);

const deploymentSteps = ref([
  {
    id: 1,
    name: '代码检出',
    description: '从Git仓库检出代码',
    status: 'success',
    progress: 100,
    timestamp: new Date(Date.now() - 300000).toISOString()
  },
  {
    id: 2,
    name: '依赖安装',
    description: '安装项目依赖',
    status: 'success',
    progress: 100,
    timestamp: new Date(Date.now() - 240000).toISOString()
  },
  {
    id: 3,
    name: '代码构建',
    description: '编译和打包代码',
    status: 'running',
    progress: 75,
    timestamp: new Date(Date.now() - 180000).toISOString()
  },
  {
    id: 4,
    name: '运行测试',
    description: '执行自动化测试',
    status: 'pending',
    progress: 0,
    timestamp: null
  },
  {
    id: 5,
    name: '部署应用',
    description: '部署到目标环境',
    status: 'pending',
    progress: 0,
    timestamp: null
  }
]);

// 表单验证规则
const environmentRules = {
  name: [{ required: true, message: '请输入环境名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择环境类型', trigger: 'change' }],
  url: [{ required: true, message: '请输入访问URL', trigger: 'blur' }]
};

// 计算属性
const filteredDeployments = computed(() => {
  let filtered = deployments.value;
  
  // 状态过滤
  if (statusFilter.value !== 'all') {
    filtered = filtered.filter(deployment => deployment.status === statusFilter.value);
  }
  
  // 环境过滤
  if (environmentFilter.value !== 'all') {
    filtered = filtered.filter(deployment => deployment.environment === environmentFilter.value);
  }
  
  // 搜索过滤
  if (searchText.value) {
    const search = searchText.value.toLowerCase();
    filtered = filtered.filter(deployment => 
      deployment.id.toLowerCase().includes(search) ||
      deployment.version.toLowerCase().includes(search) ||
      deployment.deployer.toLowerCase().includes(search)
    );
  }
  
  return filtered;
});

const successfulDeployments = computed(() => {
  return deployments.value.filter(d => d.status === 'success').length;
});

const failedDeployments = computed(() => {
  return deployments.value.filter(d => d.status === 'failed').length;
});

const runningDeployments = computed(() => {
  return deployments.value.filter(d => d.status === 'running').length;
});

const totalEnvironments = computed(() => {
  return environments.value.length;
});

// 方法
const getEnvironmentType = (environment) => {
  const typeMap = {
    'development': 'info',
    'testing': 'warning',
    'staging': 'primary',
    'production': 'danger'
  };
  return typeMap[environment] || 'info';
};

const getEnvironmentText = (environment) => {
  const textMap = {
    'development': '开发',
    'testing': '测试',
    'staging': '预发布',
    'production': '生产'
  };
  return textMap[environment] || '未知';
};

const getEnvironmentIcon = (type) => {
  const iconMap = {
    'development': 'Cpu',
    'testing': 'Monitor',
    'staging': 'Upload',
    'production': 'CircleCheck'
  };
  return iconMap[type] || 'Monitor';
};

const getStatusType = (status) => {
  const typeMap = {
    'success': 'success',
    'failed': 'danger',
    'running': 'primary',
    'rollback': 'warning'
  };
  return typeMap[status] || 'info';
};

const getStatusText = (status) => {
  const textMap = {
    'success': '成功',
    'failed': '失败',
    'running': '进行中',
    'rollback': '已回滚'
  };
  return textMap[status] || '未知';
};

const getProgressStatus = (status) => {
  if (status === 'success') return 'success';
  if (status === 'failed') return 'exception';
  return null;
};

const getStepType = (status) => {
  const typeMap = {
    'success': 'success',
    'failed': 'danger',
    'running': 'primary',
    'pending': 'info'
  };
  return typeMap[status] || 'info';
};

const getStepIcon = (status) => {
  const iconMap = {
    'success': 'CircleCheck',
    'failed': 'CircleClose',
    'running': 'Loading',
    'pending': 'Clock'
  };
  return iconMap[status] || 'Clock';
};

const formatDuration = (duration) => {
  if (!duration) return '-';
  const minutes = Math.floor(duration / 60000);
  const seconds = Math.floor((duration % 60000) / 1000);
  return `${minutes}m ${seconds}s`;
};

const formatTime = (timestamp) => {
  if (!timestamp) return '-';
  return new Date(timestamp).toLocaleString();
};

const refreshDeployments = async () => {
  refreshLoading.value = true;
  try {
    const response = await axios.get('/api/management/deployments');
    deployments.value = response.data.deployments || [];
    
    const envResponse = await axios.get('/api/management/environments');
    environments.value = envResponse.data.environments || [];
    
    ElMessage.success('部署记录已刷新');
  } catch (error) {
    console.error('刷新部署记录失败:', error);
    ElMessage.error('刷新部署记录失败');
  } finally {
    refreshLoading.value = false;
  }
};

const startQuickDeploy = async () => {
  if (!quickDeploy.environment || !quickDeploy.branch || !quickDeploy.version) {
    ElMessage.warning('请选择环境、分支和版本');
    return;
  }
  
  quickDeploying.value = true;
  try {
    const response = await axios.post('/api/management/deployments', {
      environment: quickDeploy.environment,
      branch: quickDeploy.branch,
      version: quickDeploy.version
    });
    
    // 添加新的部署记录
    deployments.value.unshift({
      id: response.data.id,
      environment: quickDeploy.environment,
      version: quickDeploy.version,
      branch: quickDeploy.branch,
      status: 'running',
      progress: 0,
      duration: 0,
      deployer: 'current_user',
      startTime: new Date().toISOString(),
      endTime: null
    });
    
    ElMessage.success('部署已开始');
    
    // 重置表单
    Object.assign(quickDeploy, {
      environment: '',
      branch: '',
      version: ''
    });
  } catch (error) {
    console.error('开始部署失败:', error);
    ElMessage.error('开始部署失败');
  } finally {
    quickDeploying.value = false;
  }
};

const viewDeploymentDetails = async (deployment) => {
  currentDeployment.value = deployment;
  
  try {
    const response = await axios.get(`/api/management/deployments/${deployment.id}/details`);
    deploymentSteps.value = response.data.steps || [];
    deploymentLogs.value = response.data.logs || '';
  } catch (error) {
    console.error('获取部署详情失败:', error);
    ElMessage.error('获取部署详情失败');
  }
  
  detailsDialogVisible.value = true;
};

const promoteDeployment = async (deployment) => {
  try {
    await ElMessageBox.confirm(
      `确定要将 ${deployment.version} 推广到生产环境吗？`,
      '确认推广',
      { type: 'warning' }
    );
    
    await axios.post(`/api/management/deployments/${deployment.id}/promote`);
    ElMessage.success('推广部署成功');
    refreshDeployments();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('推广部署失败:', error);
      ElMessage.error('推广部署失败');
    }
  }
};

const rollbackDeployment = async (deployment) => {
  try {
    await ElMessageBox.confirm(
      `确定要回滚部署 ${deployment.id} 吗？`,
      '确认回滚',
      { type: 'warning' }
    );
    
    await axios.post(`/api/management/deployments/${deployment.id}/rollback`);
    deployment.status = 'rollback';
    ElMessage.success('回滚部署成功');
  } catch (error) {
    if (error !== 'cancel') {
      console.error('回滚部署失败:', error);
      ElMessage.error('回滚部署失败');
    }
  }
};

const cancelDeployment = async (deployment) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消部署 ${deployment.id} 吗？`,
      '确认取消',
      { type: 'warning' }
    );
    
    await axios.post(`/api/management/deployments/${deployment.id}/cancel`);
    deployment.status = 'failed';
    ElMessage.success('取消部署成功');
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消部署失败:', error);
      ElMessage.error('取消部署失败');
    }
  }
};

const deployToEnvironment = (environment) => {
  quickDeploy.environment = environment.type;
  ElMessage.info(`已选择环境：${environment.name}`);
};

const viewEnvironmentLogs = (environment) => {
  ElMessage.info('查看环境日志功能开发中');
};

const showEnvironmentDialog = () => {
  environmentEditMode.value = 'create';
  Object.assign(currentEnvironment, {
    name: '',
    type: 'development',
    url: '',
    description: '',
    config: ''
  });
  environmentDialogVisible.value = true;
};

const editEnvironment = (environment) => {
  environmentEditMode.value = 'edit';
  Object.assign(currentEnvironment, { ...environment });
  environmentDialogVisible.value = true;
};

const saveEnvironment = async () => {
  try {
    saveLoading.value = true;
    
    if (environmentEditMode.value === 'create') {
      const response = await axios.post('/api/management/environments', currentEnvironment);
      environments.value.push({
        ...currentEnvironment,
        id: response.data.id,
        status: 'healthy',
        currentVersion: 'v1.0.0',
        lastDeployment: new Date().toISOString()
      });
      ElMessage.success('环境添加成功');
    } else {
      await axios.put(`/api/management/environments/${currentEnvironment.id}`, currentEnvironment);
      const index = environments.value.findIndex(e => e.id === currentEnvironment.id);
      if (index !== -1) {
        environments.value[index] = { ...currentEnvironment };
      }
      ElMessage.success('环境保存成功');
    }
    
    environmentDialogVisible.value = false;
  } catch (error) {
    console.error('保存环境失败:', error);
    ElMessage.error('保存环境失败');
  } finally {
    saveLoading.value = false;
  }
};

const refreshDeploymentLogs = async () => {
  try {
    const response = await axios.get(`/api/management/deployments/${currentDeployment.value.id}/logs`);
    deploymentLogs.value = response.data.logs || '';
    ElMessage.success('日志已刷新');
  } catch (error) {
    console.error('刷新日志失败:', error);
    ElMessage.error('刷新日志失败');
  }
};

const downloadDeploymentLogs = () => {
  const blob = new Blob([deploymentLogs.value], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `deployment_logs_${currentDeployment.value.id}_${new Date().toISOString().slice(0, 10)}.log`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

const clearDeploymentLogs = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空部署日志吗？此操作不可恢复。',
      '确认清空',
      { type: 'warning' }
    );
    
    await axios.delete(`/api/management/deployments/${currentDeployment.value.id}/logs`);
    deploymentLogs.value = '';
    ElMessage.success('日志已清空');
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空日志失败:', error);
      ElMessage.error('清空日志失败');
    }
  }
};

// 定时刷新
let refreshInterval;

// 生命周期
onMounted(() => {
  refreshDeployments();
  
  // 每30秒刷新一次状态
  refreshInterval = setInterval(() => {
    if (deployments.value.some(d => d.status === 'running')) {
      refreshDeployments();
    }
  }, 30000);
});

onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval);
  }
});
</script>

<style scoped>
.deployment-management {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.deployment-overview {
  margin-bottom: 20px;
}

.overview-card {
  height: 100px;
}

.overview-content {
  display: flex;
  align-items: center;
  gap: 15px;
  height: 100%;
}

.overview-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  background-color: #67c23a;
}

.overview-icon.failed {
  background-color: #f56c6c;
}

.overview-icon.running {
  background-color: #409eff;
}

.overview-icon.environments {
  background-color: #909399;
}

.overview-info h3 {
  margin: 0 0 5px 0;
  font-size: 24px;
  color: #303133;
  font-weight: 600;
}

.overview-info p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.quick-deploy {
  margin-bottom: 20px;
}

.deploy-form {
  padding: 10px 0;
}

.deployment-history,
.environment-management {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
}

.version-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.version-info .branch {
  font-size: 12px;
  color: #909399;
}

.progress-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-text {
  font-size: 12px;
  color: #606266;
  min-width: 35px;
}

.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.environment-card {
  margin-bottom: 20px;
  height: 200px;
}

.env-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.env-name {
  display: flex;
  align-items: center;
  gap: 10px;
}

.env-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.env-icon.development {
  background-color: #409eff;
}

.env-icon.testing {
  background-color: #e6a23c;
}

.env-icon.staging {
  background-color: #909399;
}

.env-icon.production {
  background-color: #f56c6c;
}

.env-name h4 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 16px;
}

.env-name p {
  margin: 0;
  color: #606266;
  font-size: 12px;
}

.env-info {
  margin-bottom: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 12px;
}

.info-item .label {
  color: #909399;
  font-weight: 500;
}

.info-item .value {
  color: #303133;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.env-actions {
  display: flex;
  justify-content: center;
}

.deployment-details {
  height: 60vh;
}

.basic-info {
  padding: 20px 0;
}

.info-group {
  margin-bottom: 20px;
}

.info-group h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.info-group .info-item {
  margin-bottom: 12px;
  font-size: 14px;
}

.info-group .info-item .label {
  color: #606266;
  font-weight: 500;
  min-width: 80px;
  display: inline-block;
}

.info-group .info-item .value {
  color: #303133;
}

.deployment-steps {
  padding: 20px 0;
  max-height: 400px;
  overflow-y: auto;
}

.step-content h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 14px;
}

.step-content p {
  margin: 0 0 8px 0;
  color: #606266;
  font-size: 12px;
}

.step-progress {
  margin-bottom: 8px;
}

.step-error {
  margin-top: 8px;
}

.deployment-logs {
  height: 400px;
  display: flex;
  flex-direction: column;
}

.log-toolbar {
  margin-bottom: 10px;
  display: flex;
  justify-content: flex-end;
}

.log-content {
  flex: 1;
  background-color: #1e1e1e;
  color: #d4d4d4;
  padding: 16px;
  border-radius: 4px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.log-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.dialog-footer {
  text-align: right;
}

@media (max-width: 768px) {
  .deployment-management {
    padding: 10px;
  }
  
  .deployment-overview {
    margin-bottom: 15px;
  }
  
  .overview-content {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }
  
  .deploy-form .el-row {
    flex-direction: column;
  }
  
  .deploy-form .el-col {
    margin-bottom: 10px;
  }
  
  .header-actions {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .version-info {
    align-items: flex-start;
  }
  
  .env-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .env-actions {
    justify-content: flex-start;
  }
}
</style>