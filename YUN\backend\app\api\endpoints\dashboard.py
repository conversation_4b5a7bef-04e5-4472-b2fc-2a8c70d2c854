# -*- coding: utf-8 -*-
"""
仪表盘API路由
"""
from typing import Any, Dict, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status, Request
from sqlalchemy.orm import Session
from datetime import datetime, timedelta

from app.db.session import get_db
from app.models.user import User
from app.models.health_record import HealthRecord
from app.models.medical_record import MedicalRecord
from app.models.lab_report import LabReport
from app.models.document import Document
from app.models.questionnaire import QuestionnaireResponse
from app.core.auth import get_current_active_user_custom
from app.core.mock_data_manager import (
    is_mock_enabled,
    get_mock_dashboard_stats,
    get_mock_weight_data,
    get_mock_bp_data,
    get_mock_exam_dist_data,
    get_mock_health_index_data,
    get_mock_timeline_data,
    get_mock_dashboard_stats,
    get_mock_weight_data,
    get_mock_bp_data,
    get_mock_exam_dist_data,
    get_mock_health_index_data,
    get_mock_timeline_data
)

router = APIRouter()

@router.get("/stats/{user_id}", response_model=Dict[str, Any])
async def get_dashboard_stats(
    user_id: str,
    timeRange: str = Query("6months", description="时间范围: 3months, 6months, 1year, all"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """
    获取用户健康数据统计信息
    支持通过数字ID或自定义ID查询
    """
    # 查询用户 - 支持数字ID和自定义ID
    user = None
    if user_id.isdigit():
        # 如果是数字ID
        user = db.query(User).filter(User.id == int(user_id)).first()
    else:
        # 如果是自定义ID
        user = db.query(User).filter(User.custom_id == user_id).first()

    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到用户ID: {user_id}"
        )

    # 检查权限
    if current_user.id != user.id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限查看此用户的数据"
        )

    # 根据时间范围确定开始日期
    now = datetime.now()
    if timeRange == "3months":
        start_date = now - timedelta(days=90)
    elif timeRange == "6months":
        start_date = now - timedelta(days=180)
    elif timeRange == "1year":
        start_date = now - timedelta(days=365)
    else:  # all
        start_date = now - timedelta(days=3650)  # 10年

    # 返回模拟数据
    mock_data = get_mock_dashboard_stats(timeRange)
    if mock_data:
        return mock_data
    
    # 如果模拟数据也不可用，返回默认数据
    # 使用统一的模拟数据管理器
    mock_data = get_mock_dashboard_stats(user.custom_id, start_date, now)
    if mock_data:
        return mock_data
    
    # 如果模拟数据不可用，返回空数据
    return {
        "totalRecords": 0,
        "abnormalCount": 0,
        "lastCheckupDays": 0,
        "completedQuestionnaires": 0,
        "healthIndex": 0,
        "recentActivities": []
    }

@router.get("/", response_model=dict)
def dashboard_overview(current_user: User = Depends(get_current_active_user_custom)):
    """
    获取Dashboard总览数据（示例数据）
    """
    return {
        "status": "success",
        "data": {
            "user_count": 1234,
            "active_users": 321,
            "alerts_today": 5,
            "documents": 88,
            "system_status": "ok"
        }
    }

@router.get("/debug", response_model=dict)
def debug_info(current_user: User = Depends(get_current_active_user_custom)):
    """
    获取后端调试信息（示例数据）
    """
    import sys, os
    return {
        "status": "success",
        "python_version": sys.version,
        "cwd": os.getcwd(),
        "env": dict(os.environ),
    }

@router.get("/weight-trend/{user_id}", response_model=Dict[str, Any])
async def get_weight_trend(
    user_id: str,
    timeRange: str = Query("6months", description="时间范围: 3months, 6months, 1year, all"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """
    获取用户体重变化趋势
    支持通过数字ID或自定义ID查询
    """
    # 查询用户 - 支持数字ID和自定义ID
    user = None
    if user_id.isdigit():
        # 如果是数字ID
        user = db.query(User).filter(User.id == int(user_id)).first()
    else:
        # 如果是自定义ID
        user = db.query(User).filter(User.custom_id == user_id).first()

    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到用户ID: {user_id}"
        )

    # 检查权限
    if current_user.id != user.id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限查看此用户的数据"
        )

    # 计算时间范围
    start_date = None
    if timeRange == "3months":
        start_date = datetime.now() - timedelta(days=90)
    elif timeRange == "6months":
        start_date = datetime.now() - timedelta(days=180)
    elif timeRange == "1year":
        start_date = datetime.now() - timedelta(days=365)

    # 查询体重记录
    weight_records = []
    try:
        weight_query = db.query(HealthRecord).filter(
            HealthRecord.custom_id == custom_id,
            HealthRecord.record_type == "weight"
        ).order_by(HealthRecord.created_at.asc())

        if start_date:
            weight_query = weight_query.filter(HealthRecord.created_at >= start_date)

        weight_records = weight_query.all()
    except:
        # 如果查询失败，返回模拟数据
        return generate_mock_weight_data()

    # 如果没有记录，返回模拟数据
    if not weight_records:
        return generate_mock_weight_data()

    # 处理记录数据
    dates = []
    weights = []

    for record in weight_records:
        if hasattr(record, 'created_at') and hasattr(record, 'record_value'):
            dates.append(record.created_at.strftime("%Y-%m-%d"))
            weights.append(float(record.record_value))

    # 如果处理后没有数据，返回模拟数据
    if not dates or not weights:
        mock_data = get_mock_weight_data()
        if mock_data:
            return mock_data
        # 使用统一的模拟数据管理器
        mock_data = get_mock_weight_data()
        if mock_data:
            return mock_data
        
        # 如果模拟数据不可用，返回空数据
        return {
            "dates": [],
            "weights": []
        }

    return {
        "dates": dates,
        "weights": weights
    }

@router.get("/bp-trend/{user_id}", response_model=Dict[str, Any])
async def get_bp_trend(
    user_id: str,
    timeRange: str = Query("6months", description="时间范围: 3months, 6months, 1year, all"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """
    获取用户血压变化趋势
    支持通过数字ID或自定义ID查询
    """
    # 查询用户 - 支持数字ID和自定义ID
    user = None
    if user_id.isdigit():
        # 如果是数字ID
        user = db.query(User).filter(User.id == int(user_id)).first()
    else:
        # 如果是自定义ID
        user = db.query(User).filter(User.custom_id == user_id).first()

    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到用户ID: {user_id}"
        )

    # 检查权限
    if current_user.id != user.id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限查看此用户的数据"
        )

    # 计算时间范围
    start_date = None
    if timeRange == "3months":
        start_date = datetime.now() - timedelta(days=90)
    elif timeRange == "6months":
        start_date = datetime.now() - timedelta(days=180)
    elif timeRange == "1year":
        start_date = datetime.now() - timedelta(days=365)

    # 查询血压记录
    bp_records = []
    try:
        bp_query = db.query(HealthRecord).filter(
            HealthRecord.custom_id == custom_id,
            HealthRecord.record_type == "blood_pressure"
        ).order_by(HealthRecord.created_at.asc())

        if start_date:
            bp_query = bp_query.filter(HealthRecord.created_at >= start_date)

        bp_records = bp_query.all()
    except:
        # 如果查询失败，返回模拟数据
        return generate_mock_bp_data()

    # 如果没有记录，返回模拟数据
    if not bp_records:
        return generate_mock_bp_data()

    # 处理记录数据
    dates = []
    systolic = []
    diastolic = []

    for record in bp_records:
        if hasattr(record, 'created_at') and hasattr(record, 'record_value'):
            dates.append(record.created_at.strftime("%Y-%m-%d"))

            # 假设血压值格式为"120/80"
            try:
                bp_values = record.record_value.split('/')
                if len(bp_values) == 2:
                    systolic.append(int(bp_values[0]))
                    diastolic.append(int(bp_values[1]))
            except:
                # 如果解析失败，使用随机值
                systolic.append(random.randint(110, 140))
                diastolic.append(random.randint(70, 90))

    # 如果处理后没有数据，返回模拟数据
    if not dates or not systolic or not diastolic:
        mock_data = get_mock_bp_data()
        if mock_data:
            return mock_data
        # 使用统一的模拟数据管理器
        mock_data = get_mock_bp_data()
        if mock_data:
            return mock_data
        
        # 如果模拟数据不可用，返回空数据
        return {
            "dates": [],
            "systolic": [],
            "diastolic": []
        }

    return {
        "dates": dates,
        "systolic": systolic,
        "diastolic": diastolic
    }

@router.get("/exam-distribution/{user_id}", response_model=List[Dict[str, Any]])
async def get_exam_distribution(
    user_id: str,
    timeRange: str = Query("6months", description="时间范围: 3months, 6months, 1year, all"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """
    获取用户检查记录分布
    支持通过数字ID或自定义ID查询
    """
    # 查询用户 - 支持数字ID和自定义ID
    user = None
    if user_id.isdigit():
        # 如果是数字ID
        user = db.query(User).filter(User.id == int(user_id)).first()
    else:
        # 如果是自定义ID
        user = db.query(User).filter(User.custom_id == user_id).first()

    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到用户ID: {user_id}"
        )

    # 检查权限
    if current_user.id != user.id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限查看此用户的数据"
        )

    # 返回模拟数据
    mock_data = get_mock_exam_dist_data()
    if mock_data:
        return mock_data
    return generate_mock_exam_dist_data()

# 生成模拟检查记录分布数据
def generate_mock_exam_dist_data():
    return [
        { "value": 10, "name": "实验室检验" },
        { "value": 8, "name": "影像学检查" },
        { "value": 5, "name": "心电图检查" },
        { "value": 3, "name": "超声检查" },
        { "value": 2, "name": "其他检查" }
    ]

@router.get("/health-index/{user_id}", response_model=Dict[str, Any])
async def get_health_index(
    user_id: str,
    timeRange: str = Query("6months", description="时间范围: 3months, 6months, 1year, all"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """
    获取用户健康指标数据
    支持通过数字ID或自定义ID查询
    """
    # 查询用户 - 支持数字ID和自定义ID
    user = None
    if user_id.isdigit():
        # 如果是数字ID
        user = db.query(User).filter(User.id == int(user_id)).first()
    else:
        # 如果是自定义ID
        user = db.query(User).filter(User.custom_id == user_id).first()

    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到用户ID: {user_id}"
        )

    # 检查权限
    if current_user.id != user.id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限查看此用户的数据"
        )

    # 使用统一的模拟数据管理器
    mock_data = get_mock_health_index_data()
    if mock_data:
        return mock_data
    
    # 如果模拟数据不可用，返回空数据
    return {
        "indicators": [],
        "currentValues": [],
        "referenceValues": []
    }

@router.get("/timeline/{user_id}", response_model=List[Dict[str, Any]])
async def get_timeline(
    user_id: str,
    timeRange: str = Query("6months", description="时间范围: 3months, 6months, 1year, all"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """
    获取用户健康记录时间线
    支持通过数字ID或自定义ID查询
    """
    # 查询用户 - 支持数字ID和自定义ID
    user = None
    if user_id.isdigit():
        # 如果是数字ID
        user = db.query(User).filter(User.id == int(user_id)).first()
    else:
        # 如果是自定义ID
        user = db.query(User).filter(User.custom_id == user_id).first()

    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到用户ID: {user_id}"
        )

    # 检查权限
    if current_user.id != user.id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限查看此用户的数据"
        )

    # 计算时间范围
    start_date = None
    if timeRange == "3months":
        start_date = datetime.now() - timedelta(days=90)
    elif timeRange == "6months":
        start_date = datetime.now() - timedelta(days=180)
    elif timeRange == "1year":
        start_date = datetime.now() - timedelta(days=365)

    # 尝试从数据库获取时间线数据
    timeline_data = []

    # 返回模拟数据
    mock_data = get_mock_timeline_data()
    if mock_data:
        return mock_data
    # 使用统一的模拟数据管理器
    mock_data = get_mock_timeline_data()
    if mock_data:
        return mock_data
    
    # 如果模拟数据不可用，返回空数据
    return []
