# -*- coding: utf-8 -*-
"""
健康管理系统统一项目管理器
统一管理前端、后端相关脚本，规范运行流程，确保各项功能正常运行

版本: 1.0
作者: Health Management System
创建时间: 2024-12-30
"""

import os
import sys
import json
import asyncio
import subprocess
import tempfile
import shutil
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入后端核心模块
try:
    from backend.app.core.error_handler import ErrorHandler
    from backend.app.core.response_handler import ResponseHandler
    from backend.app.core.validators import DataValidator
    from backend.app.core.file_utils import FileManager
    from backend.app.core.logging_utils import get_logger
except ImportError as e:
    print(f"警告：无法导入后端核心模块: {e}")
    # 创建基础类以确保程序能继续运行
    class ErrorHandler:
        def handle_error(self, error): pass
    class ResponseHandler:
        def success(self, data): return {"success": True, "data": data}
        def error(self, message): return {"success": False, "error": message}
    class DataValidator:
        def validate(self, data): return True
    class FileManager:
        def __init__(self): pass
    def get_logger(name): 
        import logging
        return logging.getLogger(name)

logger = get_logger(__name__)

class ComponentType(Enum):
    """组件类型"""
    FRONTEND = "frontend"
    BACKEND = "backend"
    SCRIPT = "script"
    CONFIG = "config"
    TEST = "test"

class ServiceStatus(Enum):
    """服务状态"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    ERROR = "error"
    UNKNOWN = "unknown"

@dataclass
class ComponentInfo:
    """组件信息"""
    name: str
    type: ComponentType
    path: Path
    status: ServiceStatus = ServiceStatus.UNKNOWN
    port: Optional[int] = None
    pid: Optional[int] = None
    dependencies: List[str] = None
    config_files: List[str] = None
    start_command: Optional[str] = None
    stop_command: Optional[str] = None
    health_check_url: Optional[str] = None

class ProjectManager:
    """项目统一管理器"""
    
    def __init__(self):
        """初始化项目管理器"""
        self.project_root = project_root
        self.components = {}
        self.error_handler = ErrorHandler()
        self.response_handler = ResponseHandler()
        self.validator = DataValidator()
        self.file_manager = FileManager()
        self.temp_dir = tempfile.mkdtemp(prefix="project_manager_")
        self.operation_logs = []
        
        logger.info(f"项目管理器初始化完成，项目根目录: {self.project_root}")
        logger.info(f"临时目录: {self.temp_dir}")
        
        # 初始化组件配置
        self._initialize_components()
    
    def _initialize_components(self):
        """初始化组件配置"""
        try:
            # 后端组件
            self.components['backend'] = ComponentInfo(
                name="健康管理系统后端",
                type=ComponentType.BACKEND,
                path=self.project_root / "backend",
                port=8000,
                dependencies=["mysql", "redis"],
                config_files=["backend/app/config/environment.py"],
                start_command="cd backend && python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload",
                health_check_url="http://localhost:8000/"
            )
            
            # 前端组件
            self.components['frontend'] = ComponentInfo(
                name="健康管理系统前端",
                type=ComponentType.FRONTEND,
                path=self.project_root / "frontend",
                port=3000,
                dependencies=["backend"],
                config_files=["frontend/package.json", "frontend/.env"],
                start_command="cd frontend && npm run dev",
                health_check_url="http://localhost:3000/"
            )
            
            # 数据导出测试脚本
            self.components['data_export_test'] = ComponentInfo(
                name="数据导出测试脚本",
                type=ComponentType.TEST,
                path=self.project_root / "backend" / "test_data_export.py",
                dependencies=["backend"],
                start_command="cd backend && python test_data_export.py"
            )
            
            logger.info(f"已初始化 {len(self.components)} 个组件")
            
        except Exception as e:
            logger.error(f"初始化组件配置失败: {str(e)}")
            self.log_operation("初始化组件", False, f"失败: {str(e)}")
    
    def log_operation(self, operation: str, success: bool, message: str, details: Dict = None):
        """记录操作日志"""
        log_entry = {
            'operation': operation,
            'success': success,
            'message': message,
            'timestamp': datetime.now().isoformat(),
            'details': details or {}
        }
        self.operation_logs.append(log_entry)
        
        status = "✅ 成功" if success else "❌ 失败"
        logger.info(f"{status} - {operation}: {message}")
        
        if details:
            for key, value in details.items():
                logger.info(f"  {key}: {value}")
    
    async def check_component_status(self, component_name: str) -> ServiceStatus:
        """检查组件状态"""
        try:
            if component_name not in self.components:
                return ServiceStatus.UNKNOWN
            
            component = self.components[component_name]
            
            # 检查进程是否运行
            if component.port:
                # 检查端口是否被占用
                import socket
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                result = sock.connect_ex(('localhost', component.port))
                sock.close()
                
                if result == 0:
                    component.status = ServiceStatus.RUNNING
                    return ServiceStatus.RUNNING
                else:
                    component.status = ServiceStatus.STOPPED
                    return ServiceStatus.STOPPED
            
            # 对于脚本类型，检查文件是否存在
            if component.type == ComponentType.TEST:
                if component.path.exists():
                    component.status = ServiceStatus.STOPPED  # 脚本默认为停止状态
                    return ServiceStatus.STOPPED
                else:
                    component.status = ServiceStatus.ERROR
                    return ServiceStatus.ERROR
            
            return ServiceStatus.UNKNOWN
            
        except Exception as e:
            logger.error(f"检查组件 {component_name} 状态失败: {str(e)}")
            return ServiceStatus.ERROR
    
    async def start_component(self, component_name: str) -> bool:
        """启动组件"""
        try:
            if component_name not in self.components:
                self.log_operation(f"启动组件 {component_name}", False, "组件不存在")
                return False
            
            component = self.components[component_name]
            
            # 检查依赖
            if component.dependencies:
                for dep in component.dependencies:
                    if dep in self.components:
                        dep_status = await self.check_component_status(dep)
                        if dep_status != ServiceStatus.RUNNING:
                            self.log_operation(
                                f"启动组件 {component_name}", 
                                False, 
                                f"依赖组件 {dep} 未运行"
                            )
                            return False
            
            # 检查当前状态
            current_status = await self.check_component_status(component_name)
            if current_status == ServiceStatus.RUNNING:
                self.log_operation(f"启动组件 {component_name}", True, "组件已在运行")
                return True
            
            # 启动组件
            if component.start_command:
                component.status = ServiceStatus.STARTING
                
                # 执行启动命令
                process = await asyncio.create_subprocess_shell(
                    component.start_command,
                    cwd=self.project_root,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                # 对于服务类型，不等待完成
                if component.type in [ComponentType.FRONTEND, ComponentType.BACKEND]:
                    # 等待一段时间让服务启动
                    await asyncio.sleep(3)
                    
                    # 检查是否启动成功
                    final_status = await self.check_component_status(component_name)
                    if final_status == ServiceStatus.RUNNING:
                        component.pid = process.pid
                        self.log_operation(
                            f"启动组件 {component_name}", 
                            True, 
                            "启动成功",
                            {"PID": process.pid, "端口": component.port}
                        )
                        return True
                    else:
                        self.log_operation(f"启动组件 {component_name}", False, "启动失败")
                        return False
                else:
                    # 对于脚本类型，等待执行完成
                    stdout, stderr = await process.communicate()
                    
                    if process.returncode == 0:
                        self.log_operation(
                            f"执行脚本 {component_name}", 
                            True, 
                            "执行成功",
                            {"输出": stdout.decode()[:200] if stdout else "无输出"}
                        )
                        return True
                    else:
                        self.log_operation(
                            f"执行脚本 {component_name}", 
                            False, 
                            "执行失败",
                            {"错误": stderr.decode()[:200] if stderr else "无错误信息"}
                        )
                        return False
            
            self.log_operation(f"启动组件 {component_name}", False, "没有配置启动命令")
            return False
            
        except Exception as e:
            logger.error(f"启动组件 {component_name} 失败: {str(e)}")
            self.log_operation(f"启动组件 {component_name}", False, f"异常: {str(e)}")
            return False
    
    async def stop_component(self, component_name: str) -> bool:
        """停止组件"""
        try:
            if component_name not in self.components:
                self.log_operation(f"停止组件 {component_name}", False, "组件不存在")
                return False
            
            component = self.components[component_name]
            
            # 检查当前状态
            current_status = await self.check_component_status(component_name)
            if current_status == ServiceStatus.STOPPED:
                self.log_operation(f"停止组件 {component_name}", True, "组件已停止")
                return True
            
            # 停止组件
            if component.stop_command:
                process = await asyncio.create_subprocess_shell(
                    component.stop_command,
                    cwd=self.project_root,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                stdout, stderr = await process.communicate()
                
                if process.returncode == 0:
                    component.status = ServiceStatus.STOPPED
                    component.pid = None
                    self.log_operation(f"停止组件 {component_name}", True, "停止成功")
                    return True
                else:
                    self.log_operation(
                        f"停止组件 {component_name}", 
                        False, 
                        "停止失败",
                        {"错误": stderr.decode()[:200] if stderr else "无错误信息"}
                    )
                    return False
            else:
                # 如果没有停止命令，尝试通过PID杀死进程
                if component.pid:
                    try:
                        import signal
                        os.kill(component.pid, signal.SIGTERM)
                        component.status = ServiceStatus.STOPPED
                        component.pid = None
                        self.log_operation(f"停止组件 {component_name}", True, "通过PID停止成功")
                        return True
                    except ProcessLookupError:
                        component.status = ServiceStatus.STOPPED
                        component.pid = None
                        self.log_operation(f"停止组件 {component_name}", True, "进程已不存在")
                        return True
                    except Exception as e:
                        self.log_operation(
                            f"停止组件 {component_name}", 
                            False, 
                            f"通过PID停止失败: {str(e)}"
                        )
                        return False
            
            self.log_operation(f"停止组件 {component_name}", False, "无法停止组件")
            return False
            
        except Exception as e:
            logger.error(f"停止组件 {component_name} 失败: {str(e)}")
            self.log_operation(f"停止组件 {component_name}", False, f"异常: {str(e)}")
            return False
    
    async def restart_component(self, component_name: str) -> bool:
        """重启组件"""
        try:
            self.log_operation(f"重启组件 {component_name}", True, "开始重启")
            
            # 先停止
            stop_success = await self.stop_component(component_name)
            if not stop_success:
                self.log_operation(f"重启组件 {component_name}", False, "停止失败")
                return False
            
            # 等待一段时间
            await asyncio.sleep(2)
            
            # 再启动
            start_success = await self.start_component(component_name)
            if start_success:
                self.log_operation(f"重启组件 {component_name}", True, "重启成功")
                return True
            else:
                self.log_operation(f"重启组件 {component_name}", False, "启动失败")
                return False
                
        except Exception as e:
            logger.error(f"重启组件 {component_name} 失败: {str(e)}")
            self.log_operation(f"重启组件 {component_name}", False, f"异常: {str(e)}")
            return False
    
    async def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            status_info = {
                'timestamp': datetime.now().isoformat(),
                'components': {},
                'summary': {
                    'total': len(self.components),
                    'running': 0,
                    'stopped': 0,
                    'error': 0,
                    'unknown': 0
                }
            }
            
            for name, component in self.components.items():
                current_status = await self.check_component_status(name)
                
                status_info['components'][name] = {
                    'name': component.name,
                    'type': component.type.value,
                    'status': current_status.value,
                    'port': component.port,
                    'pid': component.pid,
                    'path': str(component.path)
                }
                
                # 更新统计
                if current_status == ServiceStatus.RUNNING:
                    status_info['summary']['running'] += 1
                elif current_status == ServiceStatus.STOPPED:
                    status_info['summary']['stopped'] += 1
                elif current_status == ServiceStatus.ERROR:
                    status_info['summary']['error'] += 1
                else:
                    status_info['summary']['unknown'] += 1
            
            return status_info
            
        except Exception as e:
            logger.error(f"获取系统状态失败: {str(e)}")
            return {'error': str(e)}
    
    def cleanup(self):
        """清理资源"""
        try:
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                logger.info(f"已清理临时目录: {self.temp_dir}")
        except Exception as e:
            logger.warning(f"清理资源失败: {str(e)}")
    
    def generate_report(self) -> Dict[str, Any]:
        """生成操作报告"""
        try:
            total_operations = len(self.operation_logs)
            successful_operations = sum(1 for log in self.operation_logs if log['success'])
            failed_operations = total_operations - successful_operations
            
            report = {
                'timestamp': datetime.now().isoformat(),
                'summary': {
                    'total_operations': total_operations,
                    'successful_operations': successful_operations,
                    'failed_operations': failed_operations,
                    'success_rate': f"{(successful_operations/total_operations*100):.1f}%" if total_operations > 0 else "0%"
                },
                'components': list(self.components.keys()),
                'operations': self.operation_logs
            }
            
            return report
            
        except Exception as e:
            logger.error(f"生成报告失败: {str(e)}")
            return {'error': str(e)}

# 主要功能函数
async def main():
    """主函数"""
    manager = ProjectManager()
    
    try:
        print("\n" + "="*60)
        print("健康管理系统项目管理器")
        print("="*60)
        
        # 显示系统状态
        print("\n📊 系统状态检查...")
        status = await manager.get_system_status()
        
        print(f"\n组件总数: {status['summary']['total']}")
        print(f"运行中: {status['summary']['running']}")
        print(f"已停止: {status['summary']['stopped']}")
        print(f"错误: {status['summary']['error']}")
        print(f"未知: {status['summary']['unknown']}")
        
        print("\n📋 组件详情:")
        for name, info in status['components'].items():
            status_icon = {
                'running': '🟢',
                'stopped': '🔴',
                'error': '❌',
                'unknown': '⚪'
            }.get(info['status'], '⚪')
            
            print(f"  {status_icon} {info['name']} ({info['type']}) - {info['status']}")
            if info['port']:
                print(f"    端口: {info['port']}")
            if info['pid']:
                print(f"    PID: {info['pid']}")
        
        # 交互式菜单
        while True:
            print("\n" + "-"*40)
            print("请选择操作:")
            print("1. 启动后端服务")
            print("2. 启动前端服务")
            print("3. 运行数据导出测试")
            print("4. 停止所有服务")
            print("5. 重启所有服务")
            print("6. 查看系统状态")
            print("7. 生成操作报告")
            print("0. 退出")
            
            choice = input("\n请输入选择 (0-7): ").strip()
            
            if choice == '1':
                print("\n🚀 启动后端服务...")
                success = await manager.start_component('backend')
                print(f"结果: {'成功' if success else '失败'}")
                
            elif choice == '2':
                print("\n🚀 启动前端服务...")
                success = await manager.start_component('frontend')
                print(f"结果: {'成功' if success else '失败'}")
                
            elif choice == '3':
                print("\n🧪 运行数据导出测试...")
                success = await manager.start_component('data_export_test')
                print(f"结果: {'成功' if success else '失败'}")
                
            elif choice == '4':
                print("\n🛑 停止所有服务...")
                for component_name in ['frontend', 'backend']:
                    success = await manager.stop_component(component_name)
                    print(f"停止 {component_name}: {'成功' if success else '失败'}")
                    
            elif choice == '5':
                print("\n🔄 重启所有服务...")
                for component_name in ['backend', 'frontend']:
                    success = await manager.restart_component(component_name)
                    print(f"重启 {component_name}: {'成功' if success else '失败'}")
                    
            elif choice == '6':
                print("\n📊 系统状态:")
                status = await manager.get_system_status()
                for name, info in status['components'].items():
                    status_icon = {
                        'running': '🟢',
                        'stopped': '🔴',
                        'error': '❌',
                        'unknown': '⚪'
                    }.get(info['status'], '⚪')
                    print(f"  {status_icon} {info['name']} - {info['status']}")
                    
            elif choice == '7':
                print("\n📄 生成操作报告...")
                report = manager.generate_report()
                
                # 保存报告
                report_file = manager.project_root / f"project_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                with open(report_file, 'w', encoding='utf-8') as f:
                    json.dump(report, f, ensure_ascii=False, indent=2)
                
                print(f"报告已保存到: {report_file}")
                print(f"总操作数: {report['summary']['total_operations']}")
                print(f"成功率: {report['summary']['success_rate']}")
                
            elif choice == '0':
                print("\n👋 退出项目管理器")
                break
                
            else:
                print("\n❌ 无效选择，请重新输入")
    
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 运行错误: {str(e)}")
        logger.error(f"主函数运行错误: {str(e)}")
    finally:
        # 清理资源
        manager.cleanup()
        print("\n🧹 资源清理完成")

if __name__ == "__main__":
    asyncio.run(main())