import sqlite3
import os

# 检查数据库文件路径
db_path = 'c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db'
if not os.path.exists(db_path):
    print(f"数据库文件不存在: {db_path}")
    exit(1)

conn = sqlite3.connect(db_path)
cursor = conn.cursor()

print("=== 检查AssessmentDistribution表结构 ===")
cursor.execute("PRAGMA table_info(assessment_distributions)")
columns = cursor.fetchall()
for col in columns:
    print(f"列: {col[1]} - 类型: {col[2]}")

print("\n=== 先查找SM_006用户的user_id ===")
cursor.execute("SELECT id, custom_id, username FROM users WHERE custom_id = 'SM_006'")
user_row = cursor.fetchone()
if user_row:
    user_id = user_row[0]
    print(f"找到用户: ID={user_id}, custom_id={user_row[1]}, username={user_row[2]}")
    
    print("\n=== 检查该用户的分发记录 ===")
    cursor.execute("SELECT * FROM assessment_distributions WHERE user_id = ? ORDER BY created_at DESC LIMIT 5", (user_id,))
    rows = cursor.fetchall()
    print(f"找到 {len(rows)} 条分发记录:")
    for i, row in enumerate(rows):
        print(f"分发记录 {i+1}: {row}")
else:
    print("未找到custom_id为SM_006的用户")

print("\n=== 检查最新的Assessment记录 ===")
cursor.execute("SELECT id, custom_id, name, status, created_at FROM assessments WHERE custom_id = 'SM_006' ORDER BY created_at DESC LIMIT 5")
rows = cursor.fetchall()
print(f"找到 {len(rows)} 条Assessment记录:")
for i, row in enumerate(rows):
    print(f"Assessment {i+1}: ID={row[0]}, custom_id={row[1]}, name={row[2]}, status={row[3]}, created_at={row[4]}")

conn.close()