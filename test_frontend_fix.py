#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试前端修复后的API调用
验证token传递和Authorization头是否正确设置
"""

import requests
import json

def test_login_and_api():
    """测试登录和API调用"""
    base_url = 'http://localhost:8006'
    
    print("=== 测试前端修复后的API调用 ===")
    
    # 1. 测试登录
    print("\n1. 测试登录...")
    login_url = f'{base_url}/api/auth/login_json'
    login_data = {
        'username': 'admin',
        'password': 'admin'
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        print(f"登录状态码: {response.status_code}")
        
        if response.status_code == 200:
            login_result = response.json()
            print(f"登录响应: {json.dumps(login_result, indent=2, ensure_ascii=False)}")
            
            # 获取token
            token = login_result.get('access_token') or login_result.get('token')
            if token:
                print(f"获取到token: {token[:20]}...")
                
                # 2. 测试评估列表API
                print("\n2. 测试评估列表API...")
                api_url = f'{base_url}/api/assessments'
                headers = {
                    'Authorization': f'Bearer {token}',
                    'Content-Type': 'application/json'
                }
                
                api_response = requests.get(api_url, headers=headers)
                print(f"评估列表API状态码: {api_response.status_code}")
                print(f"评估列表API响应: {json.dumps(api_response.json(), indent=2, ensure_ascii=False)}")
                
                # 3. 测试评估记录API
                print("\n3. 测试评估记录API...")
                records_url = f'{base_url}/api/assessments/records'
                
                records_response = requests.get(records_url, headers=headers)
                print(f"评估记录API状态码: {records_response.status_code}")
                print(f"评估记录API响应: {json.dumps(records_response.json(), indent=2, ensure_ascii=False)}")
                
                # 4. 验证Authorization头格式
                print("\n4. 验证Authorization头格式...")
                print(f"Authorization头: {headers['Authorization']}")
                
                # 检查token格式（应该是3部分的JWT）
                token_parts = token.split('.')
                print(f"Token部分数量: {len(token_parts)} (应该是3)")
                
                if len(token_parts) == 3:
                    print("✅ Token格式正确")
                else:
                    print("❌ Token格式不正确")
                    
            else:
                print("❌ 登录响应中没有找到token")
        else:
            print(f"❌ 登录失败: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请确保后端服务在8006端口运行")
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")

if __name__ == '__main__':
    test_login_and_api()