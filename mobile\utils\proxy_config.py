"""
代理配置工具模块
用于禁用代理设置，避免代理连接问题
"""

import os
import logging

logger = logging.getLogger(__name__)

def disable_proxy_globally():
    """
    全局禁用代理设置
    清除所有可能的代理环境变量
    """
    proxy_vars = [
        'HTTP_PROXY', 'HTTPS_PROXY', 'FTP_PROXY',
        'http_proxy', 'https_proxy', 'ftp_proxy',
        'ALL_PROXY', 'all_proxy',
        'NO_PROXY', 'no_proxy',
        'SOCKS_PROXY', 'socks_proxy',
        'SOCKS4_PROXY', 'socks4_proxy',
        'SOCKS5_PROXY', 'socks5_proxy'
    ]

    removed_count = 0
    for var in proxy_vars:
        if var in os.environ:
            old_value = os.environ.pop(var)
            logger.info(f"已移除代理环境变量 {var}: {old_value}")
            removed_count += 1

    # 强制设置NO_PROXY环境变量，包含所有可能的地址
    os.environ['NO_PROXY'] = '*,localhost,127.0.0.1,************'
    os.environ['no_proxy'] = '*,localhost,127.0.0.1,************'

    # 禁用requests的trust_env，防止读取系统代理设置
    try:
        import requests
        if hasattr(requests.sessions, 'Session'):
            original_session_init = requests.sessions.Session.__init__

            def no_proxy_session_init(self, *args, **kwargs):
                original_session_init(self, *args, **kwargs)
                # 强制禁用代理
                self.proxies = {
                    'http': None,
                    'https': None,
                    'ftp': None,
                    'socks4': None,
                    'socks5': None
                }
                self.trust_env = False

            requests.sessions.Session.__init__ = no_proxy_session_init
            logger.info("已修改requests.Session以禁用代理")
    except Exception as e:
        logger.warning(f"修改requests.Session时出错: {e}")

    if removed_count > 0:
        logger.info(f"已移除 {removed_count} 个代理环境变量")
    else:
        logger.debug("未发现代理环境变量")

def get_no_proxy_config():
    """
    获取禁用代理的requests配置

    Returns:
        dict: 包含proxies=None的配置字典
    """
    return {
        'proxies': {
            'http': None,
            'https': None,
            'ftp': None
        }
    }

def apply_no_proxy_to_requests():
    """
    为requests库应用全局代理禁用设置
    """
    try:
        import requests
        import urllib3

        # 保存原始的request方法
        if not hasattr(requests, '_original_request'):
            requests._original_request = requests.request

        # 创建包装函数
        def no_proxy_request(*args, **kwargs):
            # 强制禁用代理，即使用户指定了代理也要覆盖
            kwargs['proxies'] = {
                'http': None,
                'https': None,
                'ftp': None,
                'socks4': None,
                'socks5': None
            }

            # 检查是否支持trust_env参数
            try:
                import inspect
                sig = inspect.signature(requests._original_request)
                if 'trust_env' in sig.parameters:
                    kwargs['trust_env'] = False
            except Exception:
                # 如果检查失败，移除trust_env参数（如果存在）
                kwargs.pop('trust_env', None)

            return requests._original_request(*args, **kwargs)

        # 替换request方法
        requests.request = no_proxy_request

        # 同时禁用urllib3的代理
        try:
            # 禁用urllib3的代理管理器
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

            # 清除urllib3的代理池管理器
            if hasattr(urllib3.poolmanager, 'ProxyManager'):
                original_proxy_manager = urllib3.poolmanager.ProxyManager

                def disabled_proxy_manager(*args, **kwargs):
                    logger.warning("尝试创建代理管理器，但已被禁用")
                    # 返回普通的PoolManager而不是ProxyManager
                    return urllib3.poolmanager.PoolManager(*args, **kwargs)

                urllib3.poolmanager.ProxyManager = disabled_proxy_manager

        except Exception as e:
            logger.warning(f"禁用urllib3代理时出错: {e}")

        logger.info("已为requests库应用全局代理禁用设置")

    except ImportError:
        logger.warning("requests库未安装，无法应用代理禁用设置")
    except Exception as e:
        logger.error(f"应用代理禁用设置时出错: {e}")

def restore_requests_original():
    """
    恢复requests库的原始方法
    """
    try:
        import requests

        if hasattr(requests, '_original_request'):
            requests.request = requests._original_request
            delattr(requests, '_original_request')
            logger.info("已恢复requests库的原始方法")
        else:
            logger.warning("未找到requests库的原始方法")

    except ImportError:
        logger.warning("requests库未安装")
    except Exception as e:
        logger.error(f"恢复requests原始方法时出错: {e}")

def check_proxy_status():
    """
    检查当前代理状态

    Returns:
        dict: 包含代理状态信息的字典
    """
    proxy_vars = [
        'HTTP_PROXY', 'HTTPS_PROXY', 'FTP_PROXY',
        'http_proxy', 'https_proxy', 'ftp_proxy',
        'ALL_PROXY', 'all_proxy'
    ]

    active_proxies = {}
    for var in proxy_vars:
        if var in os.environ:
            active_proxies[var] = os.environ[var]

    status = {
        'has_proxy_vars': len(active_proxies) > 0,
        'active_proxies': active_proxies,
        'proxy_count': len(active_proxies)
    }

    return status

def log_proxy_status():
    """
    记录当前代理状态到日志
    """
    status = check_proxy_status()

    if status['has_proxy_vars']:
        logger.warning(f"检测到 {status['proxy_count']} 个活动代理变量:")
        for var, value in status['active_proxies'].items():
            logger.warning(f"  {var} = {value}")
    else:
        logger.info("未检测到代理环境变量")

# 初始化时自动禁用代理
def initialize_proxy_config():
    """
    初始化代理配置
    自动禁用代理设置
    """
    logger.info("初始化代理配置...")

    # 记录当前状态
    log_proxy_status()

    # 禁用代理
    disable_proxy_globally()

    # 为requests应用代理禁用
    apply_no_proxy_to_requests()

    logger.info("代理配置初始化完成")

def diagnose_network_issues():
    """
    诊断网络连接问题

    Returns:
        dict: 诊断结果
    """
    diagnosis = {
        'proxy_status': check_proxy_status(),
        'network_tests': {},
        'recommendations': []
    }

    # 测试基本网络连接
    test_urls = [
        'http://************:80',
        'http://localhost:8006',
        'https://www.baidu.com'
    ]

    for url in test_urls:
        try:
            import requests
            response = requests.get(
                url + '/api/health' if not url.startswith('https://www') else url,
                timeout=5,
                proxies={
                    'http': None,
                    'https': None,
                    'ftp': None,
                    'socks4': None,
                    'socks5': None
                },
                trust_env=False
            )
            diagnosis['network_tests'][url] = {
                'status': 'success',
                'status_code': response.status_code,
                'response_time': response.elapsed.total_seconds()
            }
        except Exception as e:
            diagnosis['network_tests'][url] = {
                'status': 'failed',
                'error': str(e)
            }

    # 生成建议
    if diagnosis['proxy_status']['has_proxy_vars']:
        diagnosis['recommendations'].append("检测到代理环境变量，建议清除")

    failed_tests = [url for url, result in diagnosis['network_tests'].items()
                   if result['status'] == 'failed']
    if failed_tests:
        diagnosis['recommendations'].append(f"网络连接失败: {', '.join(failed_tests)}")

    return diagnosis

def fix_proxy_issues():
    """
    修复代理相关问题

    Returns:
        bool: 是否成功修复
    """
    try:
        logger.info("开始修复代理问题...")

        # 1. 强制禁用所有代理
        disable_proxy_globally()

        # 2. 重新应用requests代理禁用
        apply_no_proxy_to_requests()

        # 3. 清除可能的代理缓存
        try:
            import requests
            # 清除requests的session缓存
            if hasattr(requests, 'sessions'):
                requests.sessions.Session.close_all = lambda: None
        except Exception as e:
            logger.warning(f"清除session缓存时出错: {e}")

        # 4. 验证修复结果
        diagnosis = diagnose_network_issues()
        if not diagnosis['proxy_status']['has_proxy_vars']:
            logger.info("代理问题修复成功")
            return True
        else:
            logger.warning("代理问题可能未完全修复")
            return False

    except Exception as e:
        logger.error(f"修复代理问题时出错: {e}")
        return False

# 模块导入时自动初始化
if __name__ != "__main__":
    initialize_proxy_config()
