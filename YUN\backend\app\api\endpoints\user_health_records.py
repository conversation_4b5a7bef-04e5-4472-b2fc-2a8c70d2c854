from typing import Any, Optional
from datetime import datetime
from typing import Optional, Any, List, Dict
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session
from starlette import status

from app.models.user import User
from app.models.health_record import HealthRecord
from app.models.distribution import QuestionnaireDistribution, AssessmentDistribution
from app.models.questionnaire import Questionnaire, QuestionnaireResponse
from app.models.assessment import Assessment, AssessmentResponse
from app.models.result import QuestionnaireResult, AssessmentResult
from app.api import deps

# 导入各模块的crud或API方法
from app.api.endpoints import health_records, medical_records, lab_reports, examination_reports, physical_exams, other_records, questionnaires
from app.crud import health_record
from app.utils.health_records_cache import cache_health_records, invalidate_user_cache
from app.utils.performance_monitor import monitor_performance, log_api_access
from app.middleware.error_handler import (
    HealthRecordsErrorHandler,
    user_not_found_error,
    permission_denied_error,
    validation_error,
    database_error
)

router = APIRouter()

def get_user_health_records_for_aggregation(
    db: Session,
    custom_id: str,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    skip: int = 0,
    limit: int = 100,
    **kwargs
) -> Dict[str, Any]:
    """
    为聚合API提供健康记录数据。
    """
    try:
        user = db.query(User).filter(User.custom_id == custom_id).first()
        if not user:
            return {"status": "success", "total": 0, "items": []}

        query = db.query(HealthRecord).filter(HealthRecord.custom_id == custom_id)

        if start_date:
            query = query.filter(HealthRecord.created_at >= start_date)
        if end_date:
            query = query.filter(HealthRecord.created_at <= end_date)

        total = query.count()
        records = query.order_by(HealthRecord.created_at.desc()).offset(skip).limit(limit).all()

        items = [
            {
                "id": record.id,
                "custom_id": record.custom_id,
                "record_type": record.record_type,
                "record_date": record.created_at.isoformat() if record.created_at else None,
                "title": record.title,
                "description": record.description,
                "content": record.content,
                "metadata": None,
                "status": "active",
                "created_at": record.created_at.isoformat() if record.created_at else None,
                "updated_at": record.updated_at.isoformat() if record.updated_at else None
            }
            for record in records
        ]
        return {"status": "success", "total": total, "items": items}
    except Exception as e:
        print(f"获取用户健康记录聚合数据失败: {str(e)}")
        return {"status": "error", "total": 0, "items": [], "message": f"获取健康记录聚合数据失败: {str(e)}"}

RECORD_TYPE_MAP = {
    # 修改health类型的处理方式
    "health": lambda db, custom_id, **kwargs: get_user_health_records_for_aggregation(db, custom_id, **kwargs),
    "medical": lambda db, custom_id, **kwargs: medical_records.get_user_medical_records(db, custom_id, record_type="outpatient", **kwargs),
    "lab": lambda db, custom_id, **kwargs: lab_reports.read_user_records(db=db, custom_id=custom_id, **{k: v for k, v in kwargs.items() if k != 'status'}),
    "exam": lambda db, custom_id, **kwargs: examination_reports.read_user_records(db=db, custom_id=custom_id, **{k: v for k, v in kwargs.items() if k != 'status'}),
    "physical": lambda db, custom_id, **kwargs: physical_exams.read_user_records(db=db, custom_id=custom_id, **{k: v for k, v in kwargs.items() if k != 'status'}),
    "medication": lambda *args, **kwargs: [],  # 已废弃单独API，聚合接口不再依赖
    "follow_up": lambda *args, **kwargs: [],
    "health_diary": lambda *args, **kwargs: [],
    "questionnaire": lambda db, custom_id, **kwargs: get_optimized_questionnaire_records(db, custom_id, **kwargs),
    "assessment": lambda db, custom_id, **kwargs: get_optimized_assessment_records(db, custom_id, **kwargs),
    "other": lambda db, custom_id, **kwargs: other_records.read_user_records(db=db, custom_id=custom_id, **{k: v for k, v in kwargs.items() if k != 'status'}),
}

RECORD_TYPE_LABELS = {
    "health": "健康档案",
    "medical": "医疗记录",
    "lab": "实验室检验报告",
    "exam": "检查报告",
    "physical": "体检报告",
    "medication": "药物",
    "follow_up": "随访记录",
    "health_diary": "健康日记",
    "questionnaire": "问卷调查",
    "assessment": "量表评估",
    "other": "其他记录",
}

@router.get("/", response_model=dict)
@monitor_performance("get_user_health_records")
def get_user_health_records(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user_custom),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000)
):
    """获取用户健康记录列表"""
    try:
        # 构建查询
        query = db.query(HealthRecord).filter(HealthRecord.custom_id == current_user.custom_id)
        
        # 获取总数
        total = query.count()
        
        # 获取分页数据
        records = query.order_by(HealthRecord.created_at.desc()).offset(skip).limit(limit).all()
        return {
            "status": "success",
            "data": {
                "total": total,
                "records": [
                    {
                        "id": record.id,
                        "custom_id": record.custom_id,
                        "record_type": record.record_type,
                        "record_date": record.created_at.isoformat() if record.created_at else None,
                        "title": record.title,
                        "description": record.description,
                        "content": record.content,
                        "metadata": None,
                        "status": "active",
                        "created_at": record.created_at.isoformat() if record.created_at else None,
                        "updated_at": record.updated_at.isoformat() if record.updated_at else None
                    }
                    for record in records
                ]
            }
        }
    except Exception as e:
        print(f"获取用户健康记录失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取用户健康记录失败: {str(e)}"
        )

@router.get("/{record_id}", response_model=dict)
@monitor_performance("get_user_health_record")
def get_user_health_record(
    record_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user_custom)
):
    """获取单个健康记录详情"""
    try:
        record = db.query(HealthRecord).filter(
            HealthRecord.id == record_id,
            HealthRecord.custom_id == current_user.custom_id
        ).first()
        
        if not record:
            raise HTTPException(
                status_code=404,
                detail="健康记录不存在"
            )
        
        return {
            "status": "success",
            "data": {
                "id": record.id,
                "custom_id": record.custom_id,
                "record_type": record.record_type,
                "record_date": record.created_at.isoformat() if record.created_at else None,
                "title": record.title,
                "description": record.description,
                "content": record.content,
                "metadata": None,
                "status": "active",
                "created_at": record.created_at.isoformat() if record.created_at else None,
                "updated_at": record.updated_at.isoformat() if record.updated_at else None
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        print(f"获取健康记录详情失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取健康记录详情失败: {str(e)}"
        )

def get_optimized_questionnaire_records(
    db: Session,
    custom_id: str,
    status: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    skip: int = 0,
    limit: int = 100,
    **kwargs
) -> Dict[str, Any]:
    """
    优化的问卷记录获取函数，统一处理待完成和已完成的问卷，字段名与前端完全一致，补全分析报告字段
    """
    try:
        user = db.query(User).filter(User.custom_id == custom_id).first()
        if not user:
            return {"status": "error", "message": "用户不存在", "data": {"total": 0, "records": []}}
        all_items = []
        # 待完成
        if status in [None, "pending"]:
            pending_query = db.query(QuestionnaireDistribution, Questionnaire).join(
                Questionnaire, QuestionnaireDistribution.questionnaire_id == Questionnaire.id
            ).filter(
                QuestionnaireDistribution.custom_id == custom_id,
                QuestionnaireDistribution.status == "pending"
            )
            if start_date:
                pending_query = pending_query.filter(QuestionnaireDistribution.created_at >= start_date)
            if end_date:
                pending_query = pending_query.filter(QuestionnaireDistribution.created_at <= end_date)
            for distribution, questionnaire in pending_query.all():
                all_items.append({
                    "id": distribution.id,
                    "questionnaire_id": questionnaire.id,
                    "name": questionnaire.title,
                    "title": questionnaire.title,
                    "category": getattr(questionnaire, 'questionnaire_type', None),
                    "item_count": len(getattr(questionnaire, 'questions', []) or []),
                    "target": getattr(questionnaire, 'target', None),
                    "description": questionnaire.description or questionnaire.notes,
                    "instructions": getattr(questionnaire, 'instructions', None),
                    "status": "pending",
                    "type": "questionnaire",
                    "created_at": distribution.created_at.isoformat() if distribution.created_at else None,
                    "completed_at": None,
                    "due_date": distribution.due_date.isoformat() if distribution.due_date else None,
                    "distribution_id": distribution.id,
                    "response_id": None,
                    "score": None,
                    "result_level": None,
                    "interpretation": None,
                    "report_content": None
                })
        # 已完成
        if status in [None, "completed"]:
            completed_query = db.query(QuestionnaireResponse, Questionnaire).join(
                Questionnaire, QuestionnaireResponse.questionnaire_id == Questionnaire.id
            ).filter(
                QuestionnaireResponse.custom_id == custom_id,
                QuestionnaireResponse.status == "completed"
            )
            if start_date:
                completed_query = completed_query.filter(QuestionnaireResponse.created_at >= start_date)
            if end_date:
                completed_query = completed_query.filter(QuestionnaireResponse.created_at <= end_date)
            for response, questionnaire in completed_query.all():
                result = db.query(QuestionnaireResult).filter(
                    QuestionnaireResult.response_id == response.id
                ).first()
                all_items.append({
                    "id": response.id,
                    "questionnaire_id": questionnaire.id,
                    "name": questionnaire.title,
                    "title": questionnaire.title,
                    "category": getattr(questionnaire, 'questionnaire_type', None),
                    "item_count": len(getattr(questionnaire, 'questions', []) or []),
                    "target": getattr(questionnaire, 'target', None),
                    "description": questionnaire.description or questionnaire.notes,
                    "instructions": getattr(questionnaire, 'instructions', None),
                    "status": "completed",
                    "type": "questionnaire",
                    "created_at": response.created_at.isoformat() if response.created_at else None,
                    "completed_at": response.updated_at.isoformat() if response.updated_at else None,
                    "due_date": None,
                    "distribution_id": None,
                    "response_id": response.id,
                    "score": response.total_score if hasattr(response, 'total_score') else None,
                    "result_level": result.result_level if result else None,
                    "interpretation": result.interpretation if result else None,
                    "report_content": result.report_content if result else None,
                    "answers": response.answers if hasattr(response, 'answers') else None
                })
        all_items.sort(key=lambda x: x.get("completed_at") or x["created_at"], reverse=True)
        total = len(all_items)
        paginated_items = all_items[skip:skip + limit]
        return {
            "status": "success",
            "data": {
                "total": total,
                "records": paginated_items
            }
        }
    except Exception as e:
        print(f"获取用户问卷记录时出错: {str(e)}")
        return {"status": "error", "message": str(e), "data": {"total": 0, "records": []}}


def get_optimized_assessment_records(
    db: Session,
    custom_id: str,
    status: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    skip: int = 0,
    limit: int = 100,
    **kwargs
) -> Dict[str, Any]:
    """
    优化的评估记录获取函数，统一处理待完成和已完成的评估，字段名与前端完全一致，补全分析报告字段
    """
    try:
        user = db.query(User).filter(User.custom_id == custom_id).first()
        if not user:
            return {"status": "error", "message": "用户不存在", "data": {"total": 0, "records": []}}
        all_items = []
        # 待完成
        if status in [None, "pending"]:
            pending_query = db.query(AssessmentDistribution, Assessment).join(
                Assessment, AssessmentDistribution.assessment_id == Assessment.id
            ).filter(
                AssessmentDistribution.custom_id == custom_id,
                AssessmentDistribution.status == "pending"
            )
            if start_date:
                pending_query = pending_query.filter(AssessmentDistribution.created_at >= start_date)
            if end_date:
                pending_query = pending_query.filter(AssessmentDistribution.created_at <= end_date)
            for distribution, assessment in pending_query.all():
                all_items.append({
                    "id": distribution.id,
                    "assessment_id": assessment.id,
                    "name": assessment.name,
                    "title": assessment.name,
                    "category": getattr(assessment.template, 'sub_type', None) if assessment.template else getattr(assessment, 'assessment_type', None),
                    "item_count": len(getattr(assessment, 'questions', []) or []),
                    "target": getattr(assessment, 'target', None),
                    "description": assessment.notes,
                    "instructions": getattr(assessment, 'instructions', None),
                    "status": "pending",
                    "type": "assessment",
                    "created_at": distribution.created_at.isoformat() if distribution.created_at else None,
                    "completed_at": None,
                    "due_date": distribution.due_date.isoformat() if distribution.due_date else None,
                    "distribution_id": distribution.id,
                    "response_id": None,
                    "score": None,
                    "result_level": None,
                    "interpretation": None,
                    "report_content": None
                })
        # 已完成
        if status in [None, "completed"]:
            completed_query = db.query(AssessmentResponse, Assessment).join(
                Assessment, AssessmentResponse.assessment_id == Assessment.id
            ).filter(
                AssessmentResponse.custom_id == custom_id,
                AssessmentResponse.status == "completed"
            )
            if start_date:
                completed_query = completed_query.filter(AssessmentResponse.created_at >= start_date)
            if end_date:
                completed_query = completed_query.filter(AssessmentResponse.created_at <= end_date)
            for response, assessment in completed_query.all():
                result = db.query(AssessmentResult).filter(
                    AssessmentResult.assessment_id == response.assessment_id,
                    AssessmentResult.custom_id == custom_id
                ).first()
                all_items.append({
                    "id": response.id,
                    "assessment_id": assessment.id,
                    "name": assessment.name,
                    "title": assessment.name,
                    "category": getattr(assessment.template, 'sub_type', None) if assessment.template else getattr(assessment, 'assessment_type', None),
                    "item_count": len(getattr(assessment, 'questions', []) or []),
                    "target": getattr(assessment, 'target', None),
                    "description": assessment.notes,
                    "instructions": getattr(assessment, 'instructions', None),
                    "status": "completed",
                    "type": "assessment",
                    "created_at": response.created_at.isoformat() if response.created_at else None,
                    "completed_at": response.updated_at.isoformat() if response.updated_at else None,
                    "due_date": None,
                    "distribution_id": None,
                    "response_id": response.id,
                    "score": response.score if hasattr(response, 'score') else None,
                    "result_level": result.result_level if result else None,
                    "interpretation": result.interpretation if result else None,
                    "report_content": result.report_content if result else None,
                    "answers": response.answers if hasattr(response, 'answers') else None
                })
        all_items.sort(key=lambda x: x.get("completed_at") or x["created_at"], reverse=True)
        total = len(all_items)
        paginated_items = all_items[skip:skip + limit]
        return {
            "status": "success",
            "data": {
                "total": total,
                "records": paginated_items
            }
        }
    except Exception as e:
        print(f"获取用户评估记录时出错: {str(e)}")
        return {"status": "error", "message": str(e), "data": {"total": 0, "records": []}}


@router.get("/user/{custom_id}", response_model=dict)
@monitor_performance("get_user_health_records_by_custom_id")
def get_user_health_records_by_custom_id(
    custom_id: str,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user_custom),
    record_type: Optional[str] = Query(None, description="记录类型过滤"),
    status: Optional[str] = Query(None, description="状态过滤"),
    start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000)
):
    """根据custom_id获取用户健康记录聚合数据"""
    try:
        # 验证用户权限
        user = db.query(User).filter(User.custom_id == custom_id).first()
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
        
        # 解析日期参数
        parsed_start_date = None
        parsed_end_date = None
        if start_date:
            try:
                parsed_start_date = datetime.strptime(start_date, "%Y-%m-%d")
            except ValueError:
                raise HTTPException(status_code=400, detail="开始日期格式错误，应为YYYY-MM-DD")
        if end_date:
            try:
                parsed_end_date = datetime.strptime(end_date, "%Y-%m-%d")
            except ValueError:
                raise HTTPException(status_code=400, detail="结束日期格式错误，应为YYYY-MM-DD")
        
        # 如果指定了记录类型，只返回该类型的数据
        if record_type and record_type in RECORD_TYPE_MAP:
            handler = RECORD_TYPE_MAP[record_type]
            result = handler(
                db, 
                custom_id, 
                status=status,
                start_date=parsed_start_date,
                end_date=parsed_end_date,
                skip=skip,
                limit=limit
            )
            if isinstance(result, dict) and "data" in result:
                return result
            else:
                return {
                    "status": "success",
                    "data": {
                        "total": len(result) if isinstance(result, list) else 0,
                        "records": result if isinstance(result, list) else []
                    }
                }
        
        # 聚合所有类型的数据
        all_records = []
        total_count = 0
        
        for rtype, handler in RECORD_TYPE_MAP.items():
            try:
                result = handler(
                    db, 
                    custom_id, 
                    status=status,
                    start_date=parsed_start_date,
                    end_date=parsed_end_date,
                    skip=0,  # 先获取所有数据，后面统一分页
                    limit=10000  # 设置一个较大的限制
                )
                
                if isinstance(result, dict) and "data" in result:
                    records = result["data"].get("records", [])
                elif isinstance(result, dict) and "items" in result:
                    records = result.get("items", [])
                elif isinstance(result, list):
                    records = result
                else:
                    records = []
                
                # 为每条记录添加类型标识
                for record in records:
                    if isinstance(record, dict):
                        record["record_type"] = rtype
                        record["record_type_label"] = RECORD_TYPE_LABELS.get(rtype, rtype)
                
                all_records.extend(records)
                
            except Exception as e:
                print(f"获取{rtype}类型记录时出错: {str(e)}")
                continue
        
        # 按创建时间排序
        all_records.sort(
            key=lambda x: x.get("completed_at") or x.get("created_at") or "", 
            reverse=True
        )
        
        # 分页处理
        total_count = len(all_records)
        paginated_records = all_records[skip:skip + limit]
        
        return {
            "status": "success",
            "data": {
                "total": total_count,
                "records": paginated_records
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"获取用户健康记录聚合数据失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取用户健康记录失败: {str(e)}"
        )
