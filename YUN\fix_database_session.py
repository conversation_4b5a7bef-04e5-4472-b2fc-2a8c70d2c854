#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库会话配置修复脚本
修复异步数据库会话配置问题
"""

import os
import sys
from pathlib import Path
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def fix_database_session():
    """修复数据库会话配置"""
    project_root = Path(__file__).parent
    backend_root = project_root / "backend"
    
    # 检查并修复数据库会话文件
    session_file = backend_root / "app" / "db" / "session.py"
    
    if not session_file.exists():
        logger.info("创建数据库会话配置文件...")
        
        # 确保目录存在
        session_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 创建完整的数据库会话配置
        session_content = '''
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库会话配置
提供同步和异步数据库会话
"""

from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from typing import Generator, AsyncGenerator
import os
from pathlib import Path

# 数据库配置
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./app.db")
ASYNC_DATABASE_URL = DATABASE_URL.replace("sqlite:///", "sqlite+aiosqlite:///")

# 同步数据库引擎
engine = create_engine(
    DATABASE_URL,
    connect_args={"check_same_thread": False} if "sqlite" in DATABASE_URL else {},
    poolclass=StaticPool if "sqlite" in DATABASE_URL else None,
    echo=False
)

# 异步数据库引擎
async_engine = create_async_engine(
    ASYNC_DATABASE_URL,
    connect_args={"check_same_thread": False} if "sqlite" in ASYNC_DATABASE_URL else {},
    poolclass=StaticPool if "sqlite" in ASYNC_DATABASE_URL else None,
    echo=False
)

# 同步会话工厂
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine
)

# 异步会话工厂
AsyncSessionLocal = sessionmaker(
    class_=AsyncSession,
    autocommit=False,
    autoflush=False,
    bind=async_engine
)

def get_db() -> Generator[Session, None, None]:
    """获取同步数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    """获取异步数据库会话"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()

async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    """获取异步数据库会话（别名）"""
    async for session in get_async_session():
        yield session

def create_tables():
    """创建数据库表"""
    from app.models.base import Base
    Base.metadata.create_all(bind=engine)

async def create_tables_async():
    """异步创建数据库表"""
    from app.models.base import Base
    async with async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

def get_database_url():
    """获取数据库URL"""
    return DATABASE_URL

def get_async_database_url():
    """获取异步数据库URL"""
    return ASYNC_DATABASE_URL

# 数据库健康检查
async def check_database_health():
    """检查数据库连接健康状态"""
    try:
        async with AsyncSessionLocal() as session:
            # 执行简单查询测试连接
            result = await session.execute("SELECT 1")
            await result.fetchone()
            return True
    except Exception as e:
        logger.error(f"数据库健康检查失败: {e}")
        return False

def check_database_health_sync():
    """同步检查数据库连接健康状态"""
    try:
        with SessionLocal() as session:
            # 执行简单查询测试连接
            result = session.execute("SELECT 1")
            result.fetchone()
            return True
    except Exception as e:
        logger.error(f"数据库健康检查失败: {e}")
        return False
'''
        
        with open(session_file, 'w', encoding='utf-8') as f:
            f.write(session_content)
        
        logger.info(f"✅ 数据库会话配置文件已创建: {session_file}")
    else:
        logger.info("数据库会话配置文件已存在，检查内容...")
        
        with open(session_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否包含异步会话配置
        if 'AsyncSession' not in content:
            logger.info("添加异步会话配置...")
            
            # 添加异步导入
            if 'from sqlalchemy.ext.asyncio import' not in content:
                content = content.replace(
                    'from sqlalchemy import create_engine',
                    'from sqlalchemy import create_engine\nfrom sqlalchemy.ext.asyncio import create_async_engine, AsyncSession'
                )
            
            # 添加异步引擎和会话配置
            async_config = '''

# 异步数据库引擎
ASYNC_DATABASE_URL = DATABASE_URL.replace("sqlite:///", "sqlite+aiosqlite:///")
async_engine = create_async_engine(
    ASYNC_DATABASE_URL,
    connect_args={"check_same_thread": False} if "sqlite" in ASYNC_DATABASE_URL else {},
    echo=False
)

# 异步会话工厂
AsyncSessionLocal = sessionmaker(
    class_=AsyncSession,
    autocommit=False,
    autoflush=False,
    bind=async_engine
)

async def get_async_session():
    """获取异步数据库会话"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()
'''
            
            content += async_config
            
            with open(session_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info("✅ 异步会话配置已添加")
        else:
            logger.info("✅ 异步会话配置已存在")

def fix_database_imports():
    """修复数据库导入问题"""
    project_root = Path(__file__).parent
    backend_root = project_root / "backend"
    
    # 检查并修复主要API文件中的数据库导入
    api_files = [
        backend_root / "app" / "api" / "v1" / "aggregated.py",
        backend_root / "app" / "main.py"
    ]
    
    for api_file in api_files:
        if api_file.exists():
            with open(api_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否有正确的数据库导入
            if 'from app.db.session import' in content:
                if 'get_async_session' not in content:
                    # 添加异步会话导入
                    content = content.replace(
                        'from app.db.session import get_db',
                        'from app.db.session import get_db, get_async_session'
                    )
                    
                    with open(api_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    logger.info(f"✅ 已修复 {api_file.name} 的数据库导入")
                else:
                    logger.info(f"✅ {api_file.name} 的数据库导入已正确")
            else:
                logger.warning(f"⚠️ {api_file.name} 中未找到数据库导入")

def main():
    """主函数"""
    logger.info("开始修复数据库会话配置...")
    
    try:
        fix_database_session()
        fix_database_imports()
        
        logger.info("✅ 数据库会话配置修复完成")
        
        print("\n" + "="*50)
        print("数据库会话配置修复摘要")
        print("="*50)
        print("✅ 数据库会话配置文件已创建/更新")
        print("✅ 异步会话配置已添加")
        print("✅ 数据库导入已修复")
        print("\n下一步操作:")
        print("  - 重启后端服务")
        print("  - 运行数据库连接测试")
        print("  - 验证API端点功能")
        print("="*50)
        
    except Exception as e:
        logger.error(f"❌ 数据库会话配置修复失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()