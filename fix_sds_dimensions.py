#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复SDS抑郁自评量表的维度配置
"""

import sqlite3
import json
import os
import sys

# 添加项目根目录到Python路径
sys.path.append('c:/Users/<USER>/Desktop/health-Trea/YUN/backend')

def fix_sds_dimensions():
    """修复SDS量表的维度配置"""
    
    # 导入SDS模板
    try:
        from app.clinical_scales.assessment.sds import SDS_TEMPLATE
        print(f"成功导入SDS模板，包含 {len(SDS_TEMPLATE['questions'])} 个问题")
    except ImportError as e:
        print(f"导入SDS模板失败: {e}")
        return
    
    db_path = 'c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db'
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 获取SDS模板信息
        print("=== 1. 获取SDS模板信息 ===")
        cursor.execute("""
            SELECT id, template_key, name
            FROM assessment_templates 
            WHERE template_key = 'sds' OR name LIKE '%抑郁%'
        """)
        
        template = cursor.fetchone()
        if not template:
            print("❌ 未找到SDS模板")
            return
        
        template_id, template_key, name = template
        print(f"找到模板: ID={template_id}, Key={template_key}, Name={name}")
        
        # 2. 更新模板的维度信息
        print("\n=== 2. 更新模板维度信息 ===")
        dimensions_json = json.dumps(SDS_TEMPLATE['dimensions'], ensure_ascii=False)
        cursor.execute("""
            UPDATE assessment_templates 
            SET dimensions = ?
            WHERE id = ?
        """, (dimensions_json, template_id))
        print("✅ 模板维度信息已更新")
        
        # 3. 创建问题ID到维度的映射
        print("\n=== 3. 创建维度映射 ===")
        question_to_dimension = {}
        for dimension in SDS_TEMPLATE['dimensions']:
            dimension_key = dimension['key']
            for question_id in dimension['question_ids']:
                question_to_dimension[question_id] = dimension_key
        
        print(f"维度映射: {question_to_dimension}")
        
        # 4. 更新问题的维度信息
        print("\n=== 4. 更新问题维度信息 ===")
        cursor.execute("""
            SELECT id, question_id, dimension_key
            FROM assessment_template_questions 
            WHERE template_id = ?
            ORDER BY question_id
        """, (template_id,))
        
        questions = cursor.fetchall()
        updated_count = 0
        
        for db_id, question_id, current_dimension in questions:
            if question_id in question_to_dimension:
                new_dimension = question_to_dimension[question_id]
                if current_dimension != new_dimension:
                    cursor.execute("""
                        UPDATE assessment_template_questions 
                        SET dimension_key = ?
                        WHERE id = ?
                    """, (new_dimension, db_id))
                    print(f"  更新问题 {question_id}: {current_dimension} -> {new_dimension}")
                    updated_count += 1
                else:
                    print(f"  问题 {question_id}: 维度已正确 ({current_dimension})")
            else:
                print(f"  ⚠️ 问题 {question_id}: 在模板中未找到对应维度")
        
        print(f"\n更新了 {updated_count} 个问题的维度信息")
        
        # 5. 重新计算已有评估结果的维度分
        print("\n=== 5. 重新计算维度分 ===")
        cursor.execute("""
            SELECT ar.id, ar.assessment_id, ar.raw_answers, ar.total_score
            FROM assessment_results ar
            JOIN assessments a ON ar.assessment_id = a.id
            WHERE a.template_id = ?
        """, (template_id,))
        
        results_to_update = cursor.fetchall()
        print(f"找到 {len(results_to_update)} 个评估结果需要重新计算维度分")
        
        # 获取更新后的问题维度信息
        cursor.execute("""
            SELECT question_id, dimension_key, scoring
            FROM assessment_template_questions 
            WHERE template_id = ? AND dimension_key IS NOT NULL
            ORDER BY question_id
        """, (template_id,))
        
        question_dimensions = {}
        scoring_rules = {}
        for question_id, dimension_key, scoring in cursor.fetchall():
            question_dimensions[question_id] = dimension_key
            if scoring:
                try:
                    scoring_rules[question_id] = json.loads(scoring)
                except json.JSONDecodeError:
                    pass
        
        # 重新计算每个评估结果的维度分
        for result_id, assessment_id, raw_answers, total_score in results_to_update:
            if not raw_answers:
                continue
            
            try:
                answers_data = json.loads(raw_answers)
                if not isinstance(answers_data, list):
                    continue
                
                # 计算维度分
                dimension_scores = {}
                
                # 初始化维度分数
                for dimension in SDS_TEMPLATE['dimensions']:
                    dimension_key = dimension['key']
                    dimension_scores[dimension_key] = {
                        "score": 0,
                        "name": dimension['name'],
                        "question_count": len(dimension['question_ids']),
                        "max_score": dimension['max_score']
                    }
                
                # 累加每个答案的分数到对应维度
                for answer in answers_data:
                    if not isinstance(answer, dict):
                        continue
                    
                    question_id = answer.get('question_id')
                    score = answer.get('score', 0)
                    
                    if question_id in question_dimensions:
                        dimension_key = question_dimensions[question_id]
                        if dimension_key in dimension_scores:
                            dimension_scores[dimension_key]["score"] += score
                
                # 计算维度标准分 (原始分 × 1.25)
                for dimension_key in dimension_scores:
                    raw_score = dimension_scores[dimension_key]["score"]
                    dimension_scores[dimension_key]["standard_score"] = raw_score * 1.25
                
                # 更新数据库
                cursor.execute("""
                    UPDATE assessment_results 
                    SET dimension_scores = ?
                    WHERE id = ?
                """, (
                    json.dumps(dimension_scores, ensure_ascii=False),
                    result_id
                ))
                
                print(f"  更新评估结果 {result_id} 的维度分: {dimension_scores}")
                
            except json.JSONDecodeError as e:
                print(f"  ❌ 评估结果 {result_id} 原始答案JSON解析失败: {e}")
                continue
            except Exception as e:
                print(f"  ❌ 评估结果 {result_id} 处理失败: {e}")
                continue
        
        # 6. 提交更改
        conn.commit()
        print("\n✅ 所有更改已提交")
        
        # 7. 验证SM_008用户的结果
        print("\n=== 6. 验证SM_008用户的结果 ===")
        cursor.execute("""
            SELECT ar.id, ar.total_score, ar.dimension_scores, a.result
            FROM assessment_results ar
            JOIN assessments a ON ar.assessment_id = a.id
            WHERE a.template_id = ? AND a.custom_id = 'SM_008'
            ORDER BY ar.created_at DESC
            LIMIT 1
        """, (template_id,))
        
        final_result = cursor.fetchone()
        if final_result:
            result_id, total_score, dimension_scores, result = final_result
            print(f"SM_008最新评估结果:")
            print(f"  结果ID: {result_id}")
            print(f"  总分: {total_score}")
            print(f"  结果: {result}")
            
            if dimension_scores:
                try:
                    dim_data = json.loads(dimension_scores)
                    print(f"  维度分:")
                    for dim_key, dim_info in dim_data.items():
                        if isinstance(dim_info, dict):
                            name = dim_info.get('name', dim_key)
                            score = dim_info.get('score', 0)
                            standard_score = dim_info.get('standard_score', score * 1.25)
                            print(f"    {name}: 原始分={score}, 标准分={standard_score}")
                except json.JSONDecodeError:
                    print(f"  维度分JSON解析失败: {dimension_scores}")
            else:
                print(f"  维度分: 无")
        else:
            print("未找到SM_008的评估结果")
        
        conn.close()
        
        print("\n=== 维度修复完成 ===")
        print("建议操作:")
        print("1. 检查前端显示是否正常")
        print("2. 测试新的SDS量表提交")
        print("3. 验证维度分是否正确显示")
        
    except Exception as e:
        print(f"维度修复过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_sds_dimensions()