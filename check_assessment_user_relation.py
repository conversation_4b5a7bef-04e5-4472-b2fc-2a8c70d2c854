import sqlite3

def check_assessment_user_relation():
    """检查评估数据和用户的关联情况"""
    
    conn = sqlite3.connect('YUN/backend/app.db')
    cursor = conn.cursor()
    
    print("=== 检查用户数据 ===")
    cursor.execute('SELECT id, username, custom_id FROM users')
    users = cursor.fetchall()
    print(f'用户数据 ({len(users)}个):')
    for u in users:
        print(f'  ID={u[0]}, 用户名={u[1]}, custom_id={u[2]}')
    
    print("\n=== 检查评估数据 ===")
    # 先检查assessments表的结构，看看是否有user_id字段
    cursor.execute("PRAGMA table_info(assessments)")
    columns = cursor.fetchall()
    print("assessments表的列:")
    for col in columns:
        print(f"  {col[1]} ({col[2]})")
    
    # 检查评估数据
    cursor.execute('SELECT id, name, status, created_at FROM assessments ORDER BY created_at DESC')
    assessments = cursor.fetchall()
    print(f'\n评估数据 ({len(assessments)}个):')
    for a in assessments:
        print(f'  ID={a[0]}, 名称={a[1]}, 状态={a[2]}, 创建时间={a[3]}')
    
    # 检查是否有user_id或类似字段来关联用户
    print("\n=== 检查评估数据的完整信息 ===")
    cursor.execute('SELECT * FROM assessments LIMIT 1')
    sample = cursor.fetchone()
    if sample:
        print(f"示例评估记录: {sample}")
    
    conn.close()
    
    print("\n=== 分析问题 ===")
    print("1. 数据库中只有SM_001(admin)和SM_008(markey)两个用户")
    print("2. 我们之前为SM_006创建评估数据，但SM_006用户不存在")
    print("3. 需要检查评估数据是否正确关联到现有用户")
    print("4. 移动端API需要通过用户ID来过滤评估数据")

if __name__ == '__main__':
    check_assessment_user_relation()