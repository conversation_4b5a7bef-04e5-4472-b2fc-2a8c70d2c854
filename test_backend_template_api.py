
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试后端模板API是否正确返回问题数据
"""

import requests
import json

def test_backend_template_api():
    """测试后端模板API"""
    base_url = "http://localhost:8000"
    
    # 测试获取MMSE模板详情
    template_id = 4  # MMSE模板ID
    
    print(f"测试获取模板 {template_id} 的详情...")
    
    try:
        response = requests.get(f"{base_url}/api/templates/assessment-templates/{template_id}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应结构: {list(data.keys())}")
            
            if 'data' in data:
                template_data = data['data']
                print(f"模板字段: {list(template_data.keys())}")
                print(f"模板名称: {template_data.get('name')}")
                
                if 'questions' in template_data:
                    questions = template_data['questions']
                    print(f"问题数量: {len(questions)}")
                    
                    if questions:
                        first_q = questions[0]
                        print(f"第一个问题字段: {list(first_q.keys())}")
                        print(f"第一个问题文本: {first_q.get('question_text')}")
                        print(f"第一个问题选项: {first_q.get('options')}")
                        
                        # 保存完整响应用于分析
                        with open('template_api_response.json', 'w', encoding='utf-8') as f:
                            json.dump(data, f, ensure_ascii=False, indent=2)
                        print("完整响应已保存到 template_api_response.json")
                    else:
                        print("问题列表为空")
                else:
                    print("响应中不包含questions字段")
                    print(f"完整响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
            else:
                print(f"响应中不包含data字段: {data}")
        else:
            print(f"API调用失败: {response.text}")
            
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == "__main__":
    test_backend_template_api()
