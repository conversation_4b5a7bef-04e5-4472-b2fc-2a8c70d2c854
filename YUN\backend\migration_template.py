
# 模拟数据迁移模板
# 使用此模板将旧的模拟数据迁移到新的统一管理系统

from app.core.mock_data_manager import (
    is_mock_enabled,
    get_mock_dashboard_stats,
    get_mock_weight_data,
    get_mock_bp_data,
    get_mock_service_stats,
    # 添加其他需要的导入
)

# 迁移前的代码示例:
# def old_mock_function():
#     return {
#         "data": "hardcoded_value",
#         "count": 123
#     }

# 迁移后的代码示例:
def new_mock_function():
    # 首先尝试使用统一的模拟数据管理器
    mock_data = get_mock_dashboard_stats()
    if mock_data:
        return mock_data
    
    # 如果模拟数据不可用，提供回退逻辑
    return {
        "data": "default_value",
        "count": 0
    }

# 迁移检查清单:
# □ 将硬编码数据移动到 MockDataManager
# □ 更新导入语句
# □ 添加环境变量检查
# □ 提供回退机制
# □ 更新测试用例
# □ 更新文档
