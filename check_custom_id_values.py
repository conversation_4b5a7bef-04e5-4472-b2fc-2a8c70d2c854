import sqlite3

def check_custom_id_values():
    """检查assessments表的custom_id字段值"""
    
    conn = sqlite3.connect('YUN/backend/app.db')
    cursor = conn.cursor()
    
    print("=== 检查assessments表的custom_id字段 ===")
    cursor.execute('SELECT id, name, custom_id, status FROM assessments ORDER BY id')
    assessments = cursor.fetchall()
    for a in assessments:
        print(f'ID={a[0]}, 名称={a[1]}, custom_id="{a[2]}", 状态={a[3]}')
    
    print("\n=== 检查SM_008的匹配情况 ===")
    cursor.execute('SELECT COUNT(*) FROM assessments WHERE custom_id = ?', ('SM_008',))
    count = cursor.fetchone()[0]
    print(f'custom_id="SM_008"的评估数量: {count}')
    
    print("\n=== 检查所有不同的custom_id值 ===")
    cursor.execute('SELECT DISTINCT custom_id FROM assessments')
    custom_ids = cursor.fetchall()
    for cid in custom_ids:
        cursor.execute('SELECT COUNT(*) FROM assessments WHERE custom_id = ?', (cid[0],))
        count = cursor.fetchone()[0]
        print(f'custom_id="{cid[0]}" 有 {count} 个评估')
    
    print("\n=== 检查assessments表的所有列 ===")
    cursor.execute('PRAGMA table_info(assessments)')
    columns = cursor.fetchall()
    for col in columns:
        print(f'列: {col[1]}, 类型: {col[2]}, 非空: {col[3]}, 默认值: {col[4]}, 主键: {col[5]}')
    
    conn.close()

if __name__ == '__main__':
    check_custom_id_values()