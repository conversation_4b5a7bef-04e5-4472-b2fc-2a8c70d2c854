import sqlite3
import requests
import json

def check_admin_and_assessments():
    # 连接数据库
    conn = sqlite3.connect('YUN/backend/app.db')
    cursor = conn.cursor()
    
    print("=== 检查admin用户信息 ===")
    cursor.execute('SELECT id, username, custom_id, role, is_active FROM users WHERE username = "admin"')
    admin = cursor.fetchone()
    if admin:
        print(f'Admin用户信息: ID={admin[0]}, username={admin[1]}, custom_id={admin[2]}, role={admin[3]}, is_active={admin[4]}')
    else:
        print('未找到admin用户')
    
    print("\n=== 检查所有用户信息 ===")
    cursor.execute('SELECT id, username, custom_id, role, is_active FROM users')
    users = cursor.fetchall()
    for user in users:
        print(f'用户: ID={user[0]}, username={user[1]}, custom_id={user[2]}, role={user[3]}, is_active={user[4]}')
    
    print("\n=== 检查所有评估数据 ===")
    # 先检查assessments表的列
    cursor.execute("PRAGMA table_info(assessments)")
    columns = cursor.fetchall()
    print("assessments表的列:")
    for col in columns:
        print(f"  {col[1]} ({col[2]})")
    
    cursor.execute('''
        SELECT a.id, a.template_id, a.name, a.status, a.created_at
        FROM assessments a
        ORDER BY a.created_at DESC
    ''')
    assessments = cursor.fetchall()
    print(f'\n总共有 {len(assessments)} 个评估:')
    for assessment in assessments:
        print(f'  评估ID={assessment[0]}, 模板ID={assessment[1]}, 名称={assessment[2]}, 状态={assessment[3]}, 创建时间={assessment[4]}')
    
    conn.close()
    
    # 测试使用admin token访问API
    print("\n=== 测试admin token访问API ===")
    try:
        # 获取admin token
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        login_response = requests.post(
            "http://localhost:8000/api/auth/login",
            data=login_data
        )
        
        if login_response.status_code == 200:
            token_data = login_response.json()
            access_token = token_data.get('access_token')
            print(f'获取到admin token: {access_token[:50]}...')
            
            # 使用admin token调用移动端API
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }
            
            # 测试不同的参数组合
            test_cases = [
                {'params': {}, 'desc': '无参数'},
                {'params': {'custom_id': 'SM_006'}, 'desc': 'custom_id=SM_006'},
                {'params': {'custom_id': 'SM_008'}, 'desc': 'custom_id=SM_008'},
                {'params': {'status': 'pending'}, 'desc': 'status=pending'},
                {'params': {'custom_id': 'SM_006', 'status': 'pending'}, 'desc': 'SM_006+pending'},
                {'params': {'custom_id': 'SM_008', 'status': 'pending'}, 'desc': 'SM_008+pending'},
            ]
            
            for test_case in test_cases:
                print(f"\n--- 测试: {test_case['desc']} ---")
                response = requests.get(
                    "http://localhost:8000/api/mobile/assessments",
                    headers=headers,
                    params=test_case['params']
                )
                
                print(f'状态码: {response.status_code}')
                if response.status_code == 200:
                    data = response.json()
                    print(f'返回数据: {json.dumps(data, ensure_ascii=False, indent=2)}')
                else:
                    print(f'错误: {response.text}')
        else:
            print(f'登录失败: {login_response.status_code} - {login_response.text}')
            
    except Exception as e:
        print(f'API测试失败: {str(e)}')

if __name__ == '__main__':
    check_admin_and_assessments()