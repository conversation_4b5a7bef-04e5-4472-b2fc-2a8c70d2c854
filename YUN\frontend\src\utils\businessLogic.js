/**
 * 业务逻辑工具函数
 * 将复杂的业务逻辑从组件中分离，提高代码复用性和可维护性
 */

/**
 * 评估量表相关业务逻辑
 */
export const assessmentUtils = {
  /**
   * 计算评估总分
   * @param {Array} responses 用户回答
   * @param {Array} items 量表项目
   * @returns {number} 总分
   */
  calculateTotalScore(responses, items) {
    if (!responses || !items) return 0
    
    return responses.reduce((total, response, index) => {
      const item = items[index]
      if (!item || !item.options) return total
      
      const option = item.options.find(opt => opt.value === response)
      return total + (option?.score || 0)
    }, 0)
  },

  /**
   * 根据分数判断评估结果
   * @param {number} score 总分
   * @param {string} assessmentType 评估类型
   * @returns {Object} 评估结果
   */
  interpretScore(score, assessmentType) {
    const interpretations = {
      'PHQ-9': [
        { min: 0, max: 4, level: '无抑郁', severity: 'none', color: 'success' },
        { min: 5, max: 9, level: '轻度抑郁', severity: 'mild', color: 'warning' },
        { min: 10, max: 14, level: '中度抑郁', severity: 'moderate', color: 'danger' },
        { min: 15, max: 19, level: '中重度抑郁', severity: 'moderate-severe', color: 'danger' },
        { min: 20, max: 27, level: '重度抑郁', severity: 'severe', color: 'danger' }
      ],
      'GAD-7': [
        { min: 0, max: 4, level: '无焦虑', severity: 'none', color: 'success' },
        { min: 5, max: 9, level: '轻度焦虑', severity: 'mild', color: 'warning' },
        { min: 10, max: 14, level: '中度焦虑', severity: 'moderate', color: 'danger' },
        { min: 15, max: 21, level: '重度焦虑', severity: 'severe', color: 'danger' }
      ],
      'SDS': [
        { min: 25, max: 49, level: '正常', severity: 'none', color: 'success' },
        { min: 50, max: 59, level: '轻度抑郁', severity: 'mild', color: 'warning' },
        { min: 60, max: 69, level: '中度抑郁', severity: 'moderate', color: 'danger' },
        { min: 70, max: 100, level: '重度抑郁', severity: 'severe', color: 'danger' }
      ]
    }

    const ranges = interpretations[assessmentType] || []
    const result = ranges.find(range => score >= range.min && score <= range.max)
    
    return result || {
      level: '未知',
      severity: 'unknown',
      color: 'info'
    }
  },

  /**
   * 验证评估回答的完整性
   * @param {Array} responses 用户回答
   * @param {Array} items 量表项目
   * @returns {Object} 验证结果
   */
  validateResponses(responses, items) {
    if (!items || items.length === 0) {
      return { valid: false, message: '量表项目为空' }
    }

    if (!responses || responses.length !== items.length) {
      return { valid: false, message: '请完成所有题目' }
    }

    const unansweredItems = []
    responses.forEach((response, index) => {
      if (response === null || response === undefined || response === '') {
        unansweredItems.push(index + 1)
      }
    })

    if (unansweredItems.length > 0) {
      return {
        valid: false,
        message: `请完成第 ${unansweredItems.join(', ')} 题`,
        unansweredItems
      }
    }

    return { valid: true }
  },

  /**
   * 生成评估报告
   * @param {Object} assessmentData 评估数据
   * @returns {Object} 评估报告
   */
  generateReport(assessmentData) {
    const { assessment, responses, score, user, evaluator } = assessmentData
    const interpretation = this.interpretScore(score, assessment.name)
    
    return {
      id: Date.now(),
      assessment_id: assessment.id,
      assessment_name: assessment.name,
      user_id: user.id,
      user_name: user.name,
      evaluator: evaluator?.name || '系统',
      score,
      result: interpretation.level,
      severity: interpretation.severity,
      responses,
      completed_at: new Date().toISOString(),
      recommendations: this.generateRecommendations(interpretation.severity),
      next_assessment_date: this.calculateNextAssessmentDate(interpretation.severity)
    }
  },

  /**
   * 生成建议
   * @param {string} severity 严重程度
   * @returns {Array} 建议列表
   */
  generateRecommendations(severity) {
    const recommendations = {
      none: [
        '保持良好的生活习惯',
        '定期进行体检',
        '适当运动和放松'
      ],
      mild: [
        '建议关注心理健康',
        '可考虑心理咨询',
        '保持规律作息',
        '适当进行放松训练'
      ],
      moderate: [
        '建议寻求专业心理帮助',
        '考虑药物治疗',
        '定期复查',
        '家属应给予支持'
      ],
      'moderate-severe': [
        '强烈建议专业治疗',
        '需要药物干预',
        '密切监护',
        '定期随访'
      ],
      severe: [
        '立即寻求专业治疗',
        '必要时住院治疗',
        '24小时监护',
        '紧急干预措施'
      ]
    }

    return recommendations[severity] || recommendations.none
  },

  /**
   * 计算下次评估日期
   * @param {string} severity 严重程度
   * @returns {string} 下次评估日期
   */
  calculateNextAssessmentDate(severity) {
    const intervals = {
      none: 90, // 3个月
      mild: 30, // 1个月
      moderate: 14, // 2周
      'moderate-severe': 7, // 1周
      severe: 3 // 3天
    }

    const days = intervals[severity] || 30
    const nextDate = new Date()
    nextDate.setDate(nextDate.getDate() + days)
    
    return nextDate.toISOString().split('T')[0]
  }
}

/**
 * 问卷相关业务逻辑
 */
export const questionnaireUtils = {
  /**
   * 验证问卷回答
   * @param {Object} responses 用户回答
   * @param {Array} questions 问题列表
   * @returns {Object} 验证结果
   */
  validateResponses(responses, questions) {
    const errors = []
    
    questions.forEach((question, index) => {
      if (question.required) {
        const response = responses[question.id]
        if (!response || (Array.isArray(response) && response.length === 0)) {
          errors.push(`第 ${index + 1} 题为必答题`)
        }
      }
    })

    return {
      valid: errors.length === 0,
      errors
    }
  },

  /**
   * 处理跳转逻辑
   * @param {Object} responses 当前回答
   * @param {Array} questions 问题列表
   * @param {number} currentIndex 当前问题索引
   * @returns {number} 下一个问题索引
   */
  handleJumpLogic(responses, questions, currentIndex) {
    const currentQuestion = questions[currentIndex]
    if (!currentQuestion.jumpLogic) {
      return currentIndex + 1
    }

    const response = responses[currentQuestion.id]
    const jumpRule = currentQuestion.jumpLogic.find(rule => 
      rule.condition === response
    )

    if (jumpRule) {
      if (jumpRule.action === 'skip') {
        return jumpRule.target
      } else if (jumpRule.action === 'end') {
        return questions.length
      }
    }

    return currentIndex + 1
  },

  /**
   * 计算问卷完成进度
   * @param {Object} responses 用户回答
   * @param {Array} questions 问题列表
   * @returns {number} 完成百分比
   */
  calculateProgress(responses, questions) {
    if (!questions || questions.length === 0) return 0
    
    const answeredCount = questions.filter(question => 
      responses[question.id] !== undefined && responses[question.id] !== null
    ).length
    
    return Math.round((answeredCount / questions.length) * 100)
  }
}

/**
 * 用户管理相关业务逻辑
 */
export const userUtils = {
  /**
   * 验证用户权限
   * @param {Object} user 用户对象
   * @param {string} permission 权限名称
   * @returns {boolean} 是否有权限
   */
  hasPermission(user, permission) {
    if (!user || !user.role) return false
    
    const rolePermissions = {
      admin: ['*'], // 管理员拥有所有权限
      doctor: [
        'view_patients',
        'create_assessment',
        'view_assessment',
        'edit_assessment',
        'view_reports'
      ],
      nurse: [
        'view_patients',
        'create_assessment',
        'view_assessment'
      ],
      patient: [
        'view_own_data',
        'submit_assessment'
      ]
    }

    const permissions = rolePermissions[user.role] || []
    return permissions.includes('*') || permissions.includes(permission)
  },

  /**
   * 格式化用户显示名称
   * @param {Object} user 用户对象
   * @returns {string} 格式化的名称
   */
  formatUserName(user) {
    if (!user) return '未知用户'
    
    if (user.name) {
      return user.name
    }
    
    if (user.username) {
      return user.username
    }
    
    return `用户${user.id || user.custom_id || ''}`
  },

  /**
   * 获取用户角色显示名称
   * @param {string} role 角色代码
   * @returns {string} 角色显示名称
   */
  getRoleDisplayName(role) {
    const roleNames = {
      admin: '管理员',
      doctor: '医生',
      nurse: '护士',
      patient: '患者',
      guest: '访客'
    }
    
    return roleNames[role] || role
  }
}

/**
 * 数据格式化工具
 */
export const formatUtils = {
  /**
   * 格式化日期时间
   * @param {string|Date} date 日期
   * @param {string} format 格式类型
   * @returns {string} 格式化后的日期
   */
  formatDateTime(date, format = 'datetime') {
    if (!date) return '-'
    
    const d = new Date(date)
    if (isNaN(d.getTime())) return '-'
    
    const formats = {
      date: d.toLocaleDateString('zh-CN'),
      time: d.toLocaleTimeString('zh-CN'),
      datetime: d.toLocaleString('zh-CN'),
      relative: this.getRelativeTime(d)
    }
    
    return formats[format] || formats.datetime
  },

  /**
   * 获取相对时间
   * @param {Date} date 日期
   * @returns {string} 相对时间描述
   */
  getRelativeTime(date) {
    const now = new Date()
    const diff = now - date
    const seconds = Math.floor(diff / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)
    const days = Math.floor(hours / 24)
    
    if (days > 0) return `${days}天前`
    if (hours > 0) return `${hours}小时前`
    if (minutes > 0) return `${minutes}分钟前`
    return '刚刚'
  },

  /**
   * 格式化文件大小
   * @param {number} bytes 字节数
   * @returns {string} 格式化后的大小
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },

  /**
   * 格式化数字
   * @param {number} num 数字
   * @param {number} decimals 小数位数
   * @returns {string} 格式化后的数字
   */
  formatNumber(num, decimals = 2) {
    if (num === null || num === undefined) return '-'
    return Number(num).toFixed(decimals)
  }
}

/**
 * 验证工具
 */
export const validationUtils = {
  /**
   * 验证邮箱格式
   * @param {string} email 邮箱地址
   * @returns {boolean} 是否有效
   */
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  },

  /**
   * 验证手机号格式
   * @param {string} phone 手机号
   * @returns {boolean} 是否有效
   */
  isValidPhone(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phone)
  },

  /**
   * 验证身份证号格式
   * @param {string} idCard 身份证号
   * @returns {boolean} 是否有效
   */
  isValidIdCard(idCard) {
    const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
    return idCardRegex.test(idCard)
  },

  /**
   * 验证密码强度
   * @param {string} password 密码
   * @returns {Object} 验证结果
   */
  validatePassword(password) {
    const result = {
      valid: false,
      strength: 'weak',
      messages: []
    }

    if (!password) {
      result.messages.push('密码不能为空')
      return result
    }

    if (password.length < 8) {
      result.messages.push('密码长度至少8位')
    }

    if (!/[a-z]/.test(password)) {
      result.messages.push('密码需包含小写字母')
    }

    if (!/[A-Z]/.test(password)) {
      result.messages.push('密码需包含大写字母')
    }

    if (!/\d/.test(password)) {
      result.messages.push('密码需包含数字')
    }

    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      result.messages.push('密码需包含特殊字符')
    }

    // 计算密码强度
    let score = 0
    if (password.length >= 8) score++
    if (/[a-z]/.test(password)) score++
    if (/[A-Z]/.test(password)) score++
    if (/\d/.test(password)) score++
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score++

    if (score >= 4) {
      result.strength = 'strong'
      result.valid = true
    } else if (score >= 3) {
      result.strength = 'medium'
    }

    return result
  }
}