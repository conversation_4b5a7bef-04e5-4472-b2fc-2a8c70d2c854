# -*- coding: utf-8 -*-
"""
健康管理系统统一测试管理器
统一管理前端、后端测试脚本，规范测试流程，确保各项功能正常运行

版本: 1.0
作者: Health Management System
创建时间: 2024-12-30
"""

import os
import sys
import json
import asyncio
import subprocess
import tempfile
import shutil
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入后端核心模块
try:
    from backend.app.core.error_handler import ErrorHandler
    from backend.app.core.response_handler import ResponseHandler
    from backend.app.core.validators import DataValidator
    from backend.app.core.file_utils import FileManager
    from backend.app.core.logging_utils import get_logger
except ImportError as e:
    print(f"警告：无法导入后端核心模块: {e}")
    # 创建基础类以确保程序能继续运行
    class ErrorHandler:
        def handle_error(self, error): pass
    class ResponseHandler:
        def success(self, data): return {"success": True, "data": data}
        def error(self, message): return {"success": False, "error": message}
    class DataValidator:
        def validate(self, data): return True
    class FileManager:
        def __init__(self): pass
    def get_logger(name): 
        import logging
        return logging.getLogger(name)

logger = get_logger(__name__)

class TestType(Enum):
    """测试类型"""
    UNIT = "unit"
    INTEGRATION = "integration"
    FUNCTIONAL = "functional"
    PERFORMANCE = "performance"
    SECURITY = "security"
    API = "api"
    UI = "ui"
    DATA = "data"

class TestStatus(Enum):
    """测试状态"""
    PENDING = "pending"
    RUNNING = "running"
    PASSED = "passed"
    FAILED = "failed"
    ERROR = "error"
    SKIPPED = "skipped"

@dataclass
class TestCase:
    """测试用例"""
    name: str
    type: TestType
    path: Path
    status: TestStatus = TestStatus.PENDING
    duration: Optional[float] = None
    error_message: Optional[str] = None
    dependencies: List[str] = None
    parameters: Dict[str, Any] = None
    expected_result: Any = None
    actual_result: Any = None

@dataclass
class TestSuite:
    """测试套件"""
    name: str
    description: str
    test_cases: List[TestCase] = None
    status: TestStatus = TestStatus.PENDING
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    total_duration: Optional[float] = None
    passed_count: int = 0
    failed_count: int = 0
    error_count: int = 0
    skipped_count: int = 0
    
    def __post_init__(self):
        if self.test_cases is None:
            self.test_cases = []

class TestManager:
    """测试管理器"""
    
    def __init__(self):
        """初始化测试管理器"""
        self.project_root = project_root
        self.backend_dir = self.project_root / "backend"
        self.frontend_dir = self.project_root / "frontend"
        self.test_suites = {}
        self.test_results = []
        self.error_handler = ErrorHandler()
        self.response_handler = ResponseHandler()
        self.validator = DataValidator()
        self.file_manager = FileManager()
        self.temp_dir = tempfile.mkdtemp(prefix="test_manager_")
        
        logger.info(f"测试管理器初始化完成，项目根目录: {self.project_root}")
        logger.info(f"临时目录: {self.temp_dir}")
        
        # 初始化测试套件
        self._initialize_test_suites()
    
    def _initialize_test_suites(self):
        """初始化测试套件"""
        try:
            # 数据导出测试套件
            data_export_suite = TestSuite(
                name="数据导出测试",
                description="测试数据导出、导入和格式转换功能"
            )
            
            # 添加测试用例
            data_export_suite.test_cases.append(
                TestCase(
                    name="数据导出测试",
                    type=TestType.DATA,
                    path=self.backend_dir / "test_data_export.py"
                )
            )
            
            self.test_suites['data_export'] = data_export_suite
            
            # API测试套件
            api_test_suite = TestSuite(
                name="API测试",
                description="测试后端API接口功能"
            )
            
            # 添加API测试用例
            api_endpoints = [
                "users", "health_records", "assessments", "questionnaires", 
                "documents", "templates", "auth"
            ]
            
            for endpoint in api_endpoints:
                api_test_suite.test_cases.append(
                    TestCase(
                        name=f"{endpoint.replace('_', ' ').title()} API测试",
                        type=TestType.API,
                        path=self.backend_dir / "tests" / "api" / f"test_{endpoint}.py"
                    )
                )
            
            self.test_suites['api'] = api_test_suite
            
            # 前端测试套件
            frontend_test_suite = TestSuite(
                name="前端测试",
                description="测试前端组件和功能"
            )
            
            # 添加前端测试用例
            frontend_test_suite.test_cases.append(
                TestCase(
                    name="组件测试",
                    type=TestType.UI,
                    path=self.frontend_dir / "tests" / "unit"
                )
            )
            
            frontend_test_suite.test_cases.append(
                TestCase(
                    name="E2E测试",
                    type=TestType.FUNCTIONAL,
                    path=self.frontend_dir / "tests" / "e2e"
                )
            )
            
            self.test_suites['frontend'] = frontend_test_suite
            
            logger.info(f"已初始化 {len(self.test_suites)} 个测试套件")
            
        except Exception as e:
            logger.error(f"初始化测试套件失败: {str(e)}")
    
    def log_test_result(self, test_name: str, suite_name: str, status: TestStatus, 
                       duration: float, message: str, details: Dict = None):
        """记录测试结果"""
        result = {
            'test_name': test_name,
            'suite_name': suite_name,
            'status': status.value,
            'duration': duration,
            'message': message,
            'timestamp': datetime.now().isoformat(),
            'details': details or {}
        }
        self.test_results.append(result)
        
        status_icon = {
            TestStatus.PASSED: "✅",
            TestStatus.FAILED: "❌",
            TestStatus.ERROR: "⚠️",
            TestStatus.SKIPPED: "⏭️"
        }.get(status, "❓")
        
        logger.info(f"{status_icon} [{suite_name}] {test_name}: {message} ({duration:.2f}s)")
        
        if details:
            for key, value in details.items():
                logger.info(f"  {key}: {value}")
    
    async def run_test_case(self, test_case: TestCase, suite_name: str) -> TestStatus:
        """运行测试用例"""
        try:
            if not test_case.path.exists():
                test_case.status = TestStatus.ERROR
                test_case.error_message = f"测试文件不存在: {test_case.path}"
                self.log_test_result(
                    test_case.name, suite_name, TestStatus.ERROR, 0,
                    test_case.error_message
                )
                return TestStatus.ERROR
            
            test_case.status = TestStatus.RUNNING
            start_time = datetime.now()
            
            # 根据测试类型执行不同的命令
            if test_case.type == TestType.DATA:
                # 数据导出测试
                cmd = f"python {test_case.path}"
                cwd = self.project_root
            elif test_case.type == TestType.API:
                # API测试
                cmd = f"pytest {test_case.path} -v"
                cwd = self.backend_dir
            elif test_case.type == TestType.UI:
                # 前端单元测试
                cmd = "npm run test:unit"
                cwd = self.frontend_dir
            elif test_case.type == TestType.FUNCTIONAL:
                # 前端E2E测试
                cmd = "npm run test:e2e"
                cwd = self.frontend_dir
            else:
                # 默认使用Python运行
                cmd = f"python {test_case.path}"
                cwd = self.project_root
            
            # 执行测试命令
            process = await asyncio.create_subprocess_shell(
                cmd,
                cwd=cwd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            test_case.duration = duration
            
            # 解析测试结果
            if process.returncode == 0:
                test_case.status = TestStatus.PASSED
                self.log_test_result(
                    test_case.name, suite_name, TestStatus.PASSED, duration,
                    "测试通过",
                    {"输出": stdout.decode()[:200] if stdout else "无输出"}
                )
                return TestStatus.PASSED
            else:
                test_case.status = TestStatus.FAILED
                test_case.error_message = stderr.decode() if stderr else "未知错误"
                self.log_test_result(
                    test_case.name, suite_name, TestStatus.FAILED, duration,
                    "测试失败",
                    {
                        "错误": stderr.decode()[:200] if stderr else "无错误信息",
                        "输出": stdout.decode()[:200] if stdout else "无输出"
                    }
                )
                return TestStatus.FAILED
                
        except Exception as e:
            test_case.status = TestStatus.ERROR
            test_case.error_message = str(e)
            self.log_test_result(
                test_case.name, suite_name, TestStatus.ERROR, 0,
                f"测试执行异常: {str(e)}"
            )
            return TestStatus.ERROR
    
    async def run_test_suite(self, suite_name: str) -> bool:
        """运行测试套件"""
        try:
            if suite_name not in self.test_suites:
                logger.error(f"测试套件不存在: {suite_name}")
                return False
            
            suite = self.test_suites[suite_name]
            suite.status = TestStatus.RUNNING
            suite.start_time = datetime.now()
            
            logger.info(f"开始运行测试套件: {suite.name}")
            logger.info(f"描述: {suite.description}")
            logger.info(f"测试用例数: {len(suite.test_cases)}")
            
            # 重置计数器
            suite.passed_count = 0
            suite.failed_count = 0
            suite.error_count = 0
            suite.skipped_count = 0
            
            # 运行所有测试用例
            for test_case in suite.test_cases:
                status = await self.run_test_case(test_case, suite.name)
                
                # 更新计数器
                if status == TestStatus.PASSED:
                    suite.passed_count += 1
                elif status == TestStatus.FAILED:
                    suite.failed_count += 1
                elif status == TestStatus.ERROR:
                    suite.error_count += 1
                elif status == TestStatus.SKIPPED:
                    suite.skipped_count += 1
            
            # 更新套件状态
            suite.end_time = datetime.now()
            suite.total_duration = (suite.end_time - suite.start_time).total_seconds()
            
            if suite.error_count > 0:
                suite.status = TestStatus.ERROR
            elif suite.failed_count > 0:
                suite.status = TestStatus.FAILED
            else:
                suite.status = TestStatus.PASSED
            
            # 记录测试套件结果
            status_icon = {
                TestStatus.PASSED: "✅",
                TestStatus.FAILED: "❌",
                TestStatus.ERROR: "⚠️"
            }.get(suite.status, "❓")
            
            logger.info(f"{status_icon} 测试套件 {suite.name} 完成")
            logger.info(f"总用时: {suite.total_duration:.2f}秒")
            logger.info(f"通过: {suite.passed_count}, 失败: {suite.failed_count}, 错误: {suite.error_count}, 跳过: {suite.skipped_count}")
            
            return suite.status == TestStatus.PASSED
            
        except Exception as e:
            logger.error(f"运行测试套件 {suite_name} 失败: {str(e)}")
            return False
    
    async def run_all_tests(self) -> bool:
        """运行所有测试"""
        try:
            logger.info("开始运行所有测试套件")
            
            start_time = datetime.now()
            all_passed = True
            
            for suite_name in self.test_suites.keys():
                suite_passed = await self.run_test_suite(suite_name)
                all_passed = all_passed and suite_passed
            
            end_time = datetime.now()
            total_duration = (end_time - start_time).total_seconds()
            
            # 统计结果
            total_tests = sum(len(suite.test_cases) for suite in self.test_suites.values())
            passed_tests = sum(suite.passed_count for suite in self.test_suites.values())
            failed_tests = sum(suite.failed_count for suite in self.test_suites.values())
            error_tests = sum(suite.error_count for suite in self.test_suites.values())
            skipped_tests = sum(suite.skipped_count for suite in self.test_suites.values())
            
            logger.info("所有测试完成")
            logger.info(f"总用时: {total_duration:.2f}秒")
            logger.info(f"总测试数: {total_tests}")
            logger.info(f"通过: {passed_tests}, 失败: {failed_tests}, 错误: {error_tests}, 跳过: {skipped_tests}")
            
            return all_passed
            
        except Exception as e:
            logger.error(f"运行所有测试失败: {str(e)}")
            return False
    
    def generate_test_report(self) -> Dict[str, Any]:
        """生成测试报告"""
        try:
            # 统计结果
            total_tests = sum(len(suite.test_cases) for suite in self.test_suites.values())
            passed_tests = sum(suite.passed_count for suite in self.test_suites.values())
            failed_tests = sum(suite.failed_count for suite in self.test_suites.values())
            error_tests = sum(suite.error_count for suite in self.test_suites.values())
            skipped_tests = sum(suite.skipped_count for suite in self.test_suites.values())
            
            # 计算总时间
            total_duration = sum(suite.total_duration or 0 for suite in self.test_suites.values())
            
            # 生成报告
            report = {
                'timestamp': datetime.now().isoformat(),
                'summary': {
                    'total_tests': total_tests,
                    'passed_tests': passed_tests,
                    'failed_tests': failed_tests,
                    'error_tests': error_tests,
                    'skipped_tests': skipped_tests,
                    'success_rate': f"{(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "0%",
                    'total_duration': total_duration
                },
                'suites': {},
                'results': self.test_results
            }
            
            # 添加套件详情
            for name, suite in self.test_suites.items():
                report['suites'][name] = {
                    'name': suite.name,
                    'description': suite.description,
                    'status': suite.status.value if suite.status else TestStatus.PENDING.value,
                    'total_duration': suite.total_duration,
                    'passed_count': suite.passed_count,
                    'failed_count': suite.failed_count,
                    'error_count': suite.error_count,
                    'skipped_count': suite.skipped_count,
                    'test_cases': [
                        {
                            'name': tc.name,
                            'type': tc.type.value,
                            'status': tc.status.value,
                            'duration': tc.duration,
                            'error_message': tc.error_message
                        } for tc in suite.test_cases
                    ]
                }
            
            return report
            
        except Exception as e:
            logger.error(f"生成测试报告失败: {str(e)}")
            return {'error': str(e)}
    
    def cleanup(self):
        """清理资源"""
        try:
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                logger.info(f"已清理临时目录: {self.temp_dir}")
        except Exception as e:
            logger.warning(f"清理资源失败: {str(e)}")

# 主要功能函数
async def main():
    """主函数"""
    manager = TestManager()
    
    try:
        print("\n" + "="*60)
        print("健康管理系统测试管理器")
        print("="*60)
        
        # 交互式菜单
        while True:
            print("\n" + "-"*40)
            print("请选择操作:")
            print("1. 运行数据导出测试")
            print("2. 运行API测试")
            print("3. 运行前端测试")
            print("4. 运行所有测试")
            print("5. 生成测试报告")
            print("0. 退出")
            
            choice = input("\n请输入选择 (0-5): ").strip()
            
            if choice == '1':
                print("\n🧪 运行数据导出测试...")
                success = await manager.run_test_suite('data_export')
                print(f"结果: {'成功' if success else '失败'}")
                
            elif choice == '2':
                print("\n🧪 运行API测试...")
                success = await manager.run_test_suite('api')
                print(f"结果: {'成功' if success else '失败'}")
                
            elif choice == '3':
                print("\n🧪 运行前端测试...")
                success = await manager.run_test_suite('frontend')
                print(f"结果: {'成功' if success else '失败'}")
                
            elif choice == '4':
                print("\n🧪 运行所有测试...")
                success = await manager.run_all_tests()
                print(f"结果: {'全部通过' if success else '存在失败'}")
                
            elif choice == '5':
                print("\n📄 生成测试报告...")
                report = manager.generate_test_report()
                
                # 保存报告
                report_file = manager.project_root / f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                with open(report_file, 'w', encoding='utf-8') as f:
                    json.dump(report, f, ensure_ascii=False, indent=2, default=str)
                
                print(f"报告已保存到: {report_file}")
                print(f"总测试数: {report['summary']['total_tests']}")
                print(f"通过率: {report['summary']['success_rate']}")
                
            elif choice == '0':
                print("\n👋 退出测试管理器")
                break
                
            else:
                print("\n❌ 无效选择，请重新输入")
    
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 运行错误: {str(e)}")
        logger.error(f"主函数运行错误: {str(e)}")
    finally:
        # 清理资源
        manager.cleanup()
        print("\n🧹 资源清理完成")

if __name__ == "__main__":
    asyncio.run(main())