import sqlite3
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 连接数据库
engine = create_engine('sqlite:///c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db')
Session = sessionmaker(bind=engine)
session = Session()

print("=== 检查Assessment表中custom_id字段的数据类型和值 ===")

# 1. 查看表结构
print("\n1. Assessment表结构:")
result = session.execute(text("PRAGMA table_info(assessments)"))
for row in result:
    print(f"  {row}")

# 2. 查看所有Assessment记录的custom_id值和类型
print("\n2. Assessment表中所有记录的custom_id:")
result = session.execute(text("SELECT id, custom_id, name, status FROM assessments ORDER BY id"))
for row in result:
    print(f"  ID: {row[0]}, custom_id: {row[1]} (类型: {type(row[1])}), 名称: {row[2]}, 状态: {row[3]}")

# 3. 查看users表结构
print("\n3. Users表结构:")
result = session.execute(text("PRAGMA table_info(users)"))
for row in result:
    print(f"  {row}")

# 4. 尝试不同的custom_id查询方式
print("\n4. 测试不同的custom_id查询:")

# 查询字符串'SM_006'
result = session.execute(text("SELECT COUNT(*) FROM assessments WHERE custom_id = 'SM_006'"))
count_str = result.scalar()
print(f"  custom_id = 'SM_006' (字符串): {count_str} 条记录")

# 查询数字（假设SM_006对应某个数字ID）
for test_id in [6, 7, 8, 9, 10]:
    result = session.execute(text(f"SELECT COUNT(*) FROM assessments WHERE custom_id = {test_id}"))
    count_num = result.scalar()
    if count_num > 0:
        print(f"  custom_id = {test_id} (数字): {count_num} 条记录")

# 5. 查看users表中的custom_id
print("\n5. Users表中的custom_id:")
result = session.execute(text("SELECT custom_id FROM users LIMIT 10"))
for row in result:
    print(f"  用户custom_id: {row[0]} (类型: {type(row[0])})")

session.close()