#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API路由修复脚本
根据测试结果修复API路由配置问题
"""

import asyncio
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
import requests
from requests.auth import HTTPBasicAuth
import traceback

class APIRoutesFixer:
    """API路由修复器"""
    
    def __init__(self, base_url: str = "http://localhost:8006"):
        self.base_url = base_url
        self.session = requests.Session()
        self.fix_results = []
        self.auth_token = None
        self.test_user_id = "admin"
        self.test_password = "admin123"
        
    def log_fix(self, fix_name: str, success: bool, details: str = ""):
        """记录修复结果"""
        result = {
            "fix_name": fix_name,
            "success": success,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        self.fix_results.append(result)
        status = "✓" if success else "✗"
        print(f"{status} {fix_name}: {details}")
        
    async def test_and_fix_authentication(self):
        """测试并修复认证问题"""
        print("\n=== 认证问题修复 ===")
        
        # 测试不同的登录端点和数据格式
        login_endpoints = [
            {
                "url": "/api/auth/login",
                "data_format": "json",
                "data": {"username": self.test_user_id, "password": self.test_password}
            },
            {
                "url": "/api/auth/login",
                "data_format": "form",
                "data": {"username": self.test_user_id, "password": self.test_password}
            },
            {
                "url": "/api/auth/frontend_login",
                "data_format": "json",
                "data": {"username": self.test_user_id, "password": self.test_password}
            },
            {
                "url": "/api/auth/simple_login",
                "data_format": "json",
                "data": {"username": self.test_user_id, "password": self.test_password}
            },
            {
                "url": "/auth/login",
                "data_format": "json",
                "data": {"username": self.test_user_id, "password": self.test_password}
            }
        ]
        
        for endpoint in login_endpoints:
            try:
                if endpoint["data_format"] == "json":
                    response = self.session.post(
                        f"{self.base_url}{endpoint['url']}", 
                        json=endpoint["data"]
                    )
                else:
                    response = self.session.post(
                        f"{self.base_url}{endpoint['url']}", 
                        data=endpoint["data"]
                    )
                    
                if response.status_code == 200:
                    data = response.json()
                    if "access_token" in data:
                        self.auth_token = data["access_token"]
                        self.session.headers.update({"Authorization": f"Bearer {self.auth_token}"})
                        self.log_fix(f"登录端点 {endpoint['url']}", True, f"成功获取令牌，格式: {endpoint['data_format']}")
                        return True
                    else:
                        self.log_fix(f"登录端点 {endpoint['url']}", True, f"登录成功但无令牌，格式: {endpoint['data_format']}")
                else:
                    self.log_fix(f"登录端点 {endpoint['url']}", False, f"状态码: {response.status_code}, 格式: {endpoint['data_format']}")
                    
            except Exception as e:
                self.log_fix(f"登录端点 {endpoint['url']}", False, f"请求错误: {str(e)}")
                
        return False
        
    async def test_available_routes(self):
        """测试可用的路由"""
        print("\n=== 可用路由检测 ===")
        
        # 测试各种可能的路由前缀
        route_prefixes = [
            "/api",
            "/api/v1",
            "/api/v1/aggregated",
            "/api/questionnaires",
            "/api/templates",
            "/api/health-records",
            "/api/medical-records",
            "/api/dashboard",
            "/api/data-management",
            "/api/clinical-scales",
            "/api/user-health-records",
            "/api/mobile"
        ]
        
        available_routes = []
        
        for prefix in route_prefixes:
            try:
                response = self.session.get(f"{self.base_url}{prefix}")
                if response.status_code in [200, 401, 403]:  # 这些状态码表示路由存在
                    available_routes.append(prefix)
                    self.log_fix(f"路由检测 {prefix}", True, f"状态码: {response.status_code}")
                else:
                    self.log_fix(f"路由检测 {prefix}", False, f"状态码: {response.status_code}")
            except Exception as e:
                self.log_fix(f"路由检测 {prefix}", False, f"请求错误: {str(e)}")
                
        return available_routes
        
    async def test_specific_endpoints(self):
        """测试具体的端点"""
        print("\n=== 具体端点测试 ===")
        
        # 测试不需要认证的端点
        public_endpoints = [
            "/api/health",
            "/api/health/ping",
            "/api/health/check",
            "/api/v1/aggregated/health",
            "/docs",
            "/openapi.json"
        ]
        
        for endpoint in public_endpoints:
            try:
                response = self.session.get(f"{self.base_url}{endpoint}")
                if response.status_code == 200:
                    self.log_fix(f"公开端点 {endpoint}", True, f"状态码: {response.status_code}")
                else:
                    self.log_fix(f"公开端点 {endpoint}", False, f"状态码: {response.status_code}")
            except Exception as e:
                self.log_fix(f"公开端点 {endpoint}", False, f"请求错误: {str(e)}")
                
        # 如果有认证令牌，测试需要认证的端点
        if self.auth_token:
            protected_endpoints = [
                "/api/users/me",
                "/api/v1/aggregated/data-sources/status",
                "/api/dashboard",
                "/api/questionnaires"
            ]
            
            for endpoint in protected_endpoints:
                try:
                    response = self.session.get(f"{self.base_url}{endpoint}")
                    if response.status_code == 200:
                        self.log_fix(f"受保护端点 {endpoint}", True, f"状态码: {response.status_code}")
                    else:
                        self.log_fix(f"受保护端点 {endpoint}", False, f"状态码: {response.status_code}")
                except Exception as e:
                    self.log_fix(f"受保护端点 {endpoint}", False, f"请求错误: {str(e)}")
                    
    async def test_aggregated_api_endpoints(self):
        """测试聚合API的各个端点"""
        print("\n=== 聚合API端点测试 ===")
        
        aggregated_endpoints = [
            "/api/v1/aggregated/health",
            "/api/v1/aggregated/data-sources/status",
            "/api/v1/aggregated/users/admin/data",
            "/api/v1/aggregated/questionnaires",
            "/api/v1/aggregated/assessments"
        ]
        
        for endpoint in aggregated_endpoints:
            try:
                response = self.session.get(f"{self.base_url}{endpoint}")
                if response.status_code in [200, 401, 403]:
                    self.log_fix(f"聚合API {endpoint}", True, f"状态码: {response.status_code}")
                else:
                    self.log_fix(f"聚合API {endpoint}", False, f"状态码: {response.status_code}")
            except Exception as e:
                self.log_fix(f"聚合API {endpoint}", False, f"请求错误: {str(e)}")
                
    async def test_database_connectivity(self):
        """测试数据库连接"""
        print("\n=== 数据库连接测试 ===")
        
        db_endpoints = [
            "/api/health/database",
            "/api/v1/aggregated/health",
            "/api/dashboard/debug"
        ]
        
        for endpoint in db_endpoints:
            try:
                response = self.session.get(f"{self.base_url}{endpoint}")
                if response.status_code == 200:
                    data = response.json()
                    if "database" in str(data).lower() or "db" in str(data).lower():
                        self.log_fix(f"数据库端点 {endpoint}", True, "数据库连接正常")
                    else:
                        self.log_fix(f"数据库端点 {endpoint}", True, f"端点可访问，状态码: {response.status_code}")
                else:
                    self.log_fix(f"数据库端点 {endpoint}", False, f"状态码: {response.status_code}")
            except Exception as e:
                self.log_fix(f"数据库端点 {endpoint}", False, f"请求错误: {str(e)}")
                
    async def generate_route_mapping(self):
        """生成路由映射报告"""
        print("\n=== 生成路由映射 ===")
        
        # 尝试获取OpenAPI规范
        try:
            response = self.session.get(f"{self.base_url}/openapi.json")
            if response.status_code == 200:
                openapi_spec = response.json()
                paths = openapi_spec.get("paths", {})
                
                route_mapping = {
                    "available_paths": list(paths.keys()),
                    "total_endpoints": len(paths),
                    "methods_summary": {}
                }
                
                # 统计HTTP方法
                for path, methods in paths.items():
                    for method in methods.keys():
                        if method.upper() not in route_mapping["methods_summary"]:
                            route_mapping["methods_summary"][method.upper()] = 0
                        route_mapping["methods_summary"][method.upper()] += 1
                        
                # 保存路由映射
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                mapping_file = f"api_route_mapping_{timestamp}.json"
                
                with open(mapping_file, 'w', encoding='utf-8') as f:
                    json.dump(route_mapping, f, ensure_ascii=False, indent=2)
                    
                self.log_fix("路由映射生成", True, f"发现 {len(paths)} 个端点，保存到 {mapping_file}")
                
                # 显示前10个路径
                print("\n前10个可用路径:")
                for i, path in enumerate(list(paths.keys())[:10]):
                    print(f"  {i+1}. {path}")
                    
            else:
                self.log_fix("路由映射生成", False, f"无法获取OpenAPI规范，状态码: {response.status_code}")
                
        except Exception as e:
            self.log_fix("路由映射生成", False, f"请求错误: {str(e)}")
            
    async def run_all_fixes(self):
        """运行所有修复检测"""
        print("开始API路由修复检测...")
        start_time = time.time()
        
        # 按顺序执行修复检测
        await self.test_and_fix_authentication()
        available_routes = await self.test_available_routes()
        await self.test_specific_endpoints()
        await self.test_aggregated_api_endpoints()
        await self.test_database_connectivity()
        await self.generate_route_mapping()
        
        total_time = time.time() - start_time
        
        # 生成修复报告
        await self.generate_fix_report(total_time, available_routes)
        
    async def generate_fix_report(self, total_time: float, available_routes: List[str]):
        """生成修复报告"""
        total_fixes = len(self.fix_results)
        successful_fixes = sum(1 for result in self.fix_results if result["success"])
        failed_fixes = total_fixes - successful_fixes
        success_rate = (successful_fixes / total_fixes * 100) if total_fixes > 0 else 0
        
        # 失败的修复详情
        failed_details = [result for result in self.fix_results if not result["success"]]
        
        report = {
            "summary": {
                "total_checks": total_fixes,
                "successful": successful_fixes,
                "failed": failed_fixes,
                "success_rate": f"{success_rate:.1f}%",
                "total_time": f"{total_time:.2f}s",
                "timestamp": datetime.now().isoformat()
            },
            "available_routes": available_routes,
            "failed_checks": failed_details,
            "all_results": self.fix_results,
            "recommendations": self.generate_recommendations()
        }
        
        # 保存报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"api_routes_fix_report_{timestamp}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
            
        print(f"\n=== 修复检测报告 ===")
        print(f"总检测项: {total_fixes}")
        print(f"成功: {successful_fixes}")
        print(f"失败: {failed_fixes}")
        print(f"成功率: {success_rate:.1f}%")
        print(f"总耗时: {total_time:.2f}s")
        print(f"可用路由: {len(available_routes)} 个")
        print(f"详细报告已保存到: {report_file}")
        
        if failed_details:
            print("\n需要修复的问题:")
            for fix in failed_details:
                print(f"  - {fix['fix_name']}: {fix['details']}")
                
        print("\n修复建议:")
        for recommendation in report["recommendations"]:
            print(f"  - {recommendation}")
            
    def generate_recommendations(self) -> List[str]:
        """生成修复建议"""
        recommendations = []
        
        # 检查认证问题
        auth_failed = any("登录" in result["fix_name"] and not result["success"] for result in self.fix_results)
        if auth_failed:
            recommendations.append("检查认证端点的请求格式，确保使用正确的字段名和数据格式")
            
        # 检查路由问题
        route_failed = any("路由检测" in result["fix_name"] and not result["success"] for result in self.fix_results)
        if route_failed:
            recommendations.append("检查API路由配置，确保所有模块都正确注册到主路由器")
            
        # 检查数据库问题
        db_failed = any("数据库" in result["fix_name"] and not result["success"] for result in self.fix_results)
        if db_failed:
            recommendations.append("检查数据库连接配置和数据库服务状态")
            
        # 检查聚合API问题
        aggregated_failed = any("聚合API" in result["fix_name"] and not result["success"] for result in self.fix_results)
        if aggregated_failed:
            recommendations.append("检查聚合API模块的导入和注册，确保所有依赖都正确安装")
            
        if not recommendations:
            recommendations.append("大部分功能正常，建议进行详细的功能测试")
            
        return recommendations
        
async def main():
    """主函数"""
    fixer = APIRoutesFixer()
    await fixer.run_all_fixes()
    
if __name__ == "__main__":
    asyncio.run(main())