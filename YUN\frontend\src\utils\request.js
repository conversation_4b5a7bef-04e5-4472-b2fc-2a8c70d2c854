/**
 * 请求工具
 */
import axios from "axios";
import { ElMessage } from "element-plus";
import router from "@/router";
import { getToken } from "@/utils/auth-standard";

// 自动选择 API 地址
const getApiUrl = () => {
  // 检测是否为本地环境
  const isLocalhost =
    window.location.hostname === "localhost" ||
    window.location.hostname === "127.0.0.1";

  // 根据环境选择 API 地址
  return isLocalhost
    ? import.meta.env.VITE_LOCAL_API_URL
    : import.meta.env.VITE_PUBLIC_API_URL;
};

// 创建axios实例
const service = axios.create({
  baseURL: "", // 使用相对路径，让代理处理
  timeout: 15000, // 请求超时时间
  withCredentials: true, // 跨域请求时发送cookies
});

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 记录详细的请求信息
    const method = config.method.toUpperCase();
    const url = config.url;
    console.log(`发送请求: ${method} ${url}`);

    // 如果是登录请求，记录更详细的信息（但不记录密码）
    if (url.includes('login') || url.includes('auth')) {
      const requestData = { ...config.data };
      if (requestData && requestData.password) {
        requestData.password = '******'; // 隐藏密码
      }
      console.log(`登录请求详情 - 方法: ${method}, URL: ${url}`);
      console.log('请求头:', config.headers);
      console.log('请求数据:', requestData);
    }

    // 获取token
    const token = getToken();
    if (token) {
      // 设置请求头
      config.headers["Authorization"] = `Bearer ${token}`;
      console.log(`已添加认证头：Bearer ${token.substring(0, 15)}...`);
    } else {
      console.log('请求未包含令牌');
    }

    // 从localStorage获取custom_id，作为备用认证方式
    let customId = localStorage.getItem("custom_id");

    // 如果没有custom_id，尝试从用户信息中获取
    if (!customId) {
      const userStr = localStorage.getItem("user");
      if (userStr) {
        try {
          const user = JSON.parse(userStr);
          if (user && user.custom_id) {
            customId = user.custom_id;
            localStorage.setItem("custom_id", customId);
          } else if (user && user.id) {
            // 如果没有custom_id但有用户ID，使用用户ID作为fallback
            customId = user.id.toString();
          }
        } catch (e) {
          console.warn('解析用户信息失败:', e);
        }
      }
    }

    // 如果仍然没有customId，对于问卷相关API使用默认值
    if (!customId && (url.includes('/questionnaires') || url.includes('/templates/questionnaire-templates'))) {
      customId = '1'; // 使用默认用户ID，确保标准问卷能正常获取
      console.log('为问卷相关API使用默认X-User-ID: 1');
    }

    // 对于分发量表的请求，不添加X-User-ID头部，避免影响分发目标用户
    if (url.includes('/distribute')) {
      console.log('分发请求，不添加X-User-ID头部，避免影响分发目标');
    } else if (customId) {
      // 添加X-User-ID头，与移动端保持一致
      config.headers["X-User-ID"] = customId;
      console.log(`已添加X-User-ID头: ${customId}`);
    }

    return config;
  },
  (error) => {
    console.error(`请求拦截器错误: ${error.message}`);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    const res = response.data;
    const status = response.status;
    const url = response.config.url;

    console.log(`收到响应: ${response.config.method.toUpperCase()} ${url} [${status}]`);

    // 如果是登录相关请求，记录更详细的响应信息
    if (url.includes('login') || url.includes('auth')) {
      // 克隆响应数据以便修改敏感字段
      const safeResponseData = { ...res };
      if (safeResponseData.access_token) {
        safeResponseData.access_token = safeResponseData.access_token.substring(0, 15) + '...';
      }
      console.log('登录响应数据:', safeResponseData);
    }

    // 检查响应中是否包含custom_id，如果有则更新本地存储
    if (res.custom_id) {
      localStorage.setItem("custom_id", res.custom_id);
      console.log(`已保存custom_id到本地存储: ${res.custom_id}`);
    } else if (res.user && res.user.custom_id) {
      localStorage.setItem("custom_id", res.user.custom_id);
      console.log(`已保存user.custom_id到本地存储: ${res.user.custom_id}`);
    }

    // 如果响应成功但业务状态码不是200，显示错误信息
    if (res.code && res.code !== 200) {
      ElMessage({
        message: res.message || "请求失败",
        type: "error",
        duration: 5 * 1000,
      });

      // 401: 未登录或token过期
      if (res.code === 401) {
        // 清除token并跳转到登录页
        localStorage.removeItem("token");
        // 保留custom_id，可能仍然可以使用X-User-ID认证
        router.push("/login");
      }

      return Promise.reject(new Error(res.message || "请求失败"));
    } else {
      return res;
    }
  },
  (error) => {
    console.error("响应错误:", error);

    // 记录原始请求信息
    if (error.config) {
      const method = error.config.method.toUpperCase();
      const url = error.config.url;
      console.log(`错误请求信息: ${method} ${url}`);
    }

    // 处理HTTP错误状态码
    if (error.response) {
      const status = error.response.status;
      const url = error.config?.url || '未知URL';

      console.log(`错误响应: ${url} [${status}]`);

      // 特殊处理307重定向错误
      if (status === 307) {
        const location = error.response.headers?.location;
        console.error(`检测到307重定向: ${url} -> ${location || '未知目标'}`);

        // 对于登录页重定向，使用特殊处理
        if (url.includes('login')) {
          console.log('登录页重定向，尝试使用前端路由重定向');

          // 如果使用Vue路由的登录页
          if (location === '/login' || location === '/#/login') {
            ElMessage({
              message: "请直接使用前端应用登录",
              type: "warning",
              duration: 5 * 1000,
            });

            // 使用Vue Router导航
            router.push('/login');

            // 返回一个空的成功响应，阻止错误继续传播
            return Promise.resolve({ _redirected: true });
          }
        }

        // 其他307重定向
        ElMessage({
          message: `请求被重定向: ${location || '未知目标'}`,
          type: "warning",
          duration: 5 * 1000,
        });
      }
      // 401: 未登录或token过期
      else if (status === 401) {
        console.log('检测到401未授权错误');
        // 清除token
        localStorage.removeItem("token");

        // 检查是否有custom_id，如果没有才跳转到登录页
        const customId = localStorage.getItem("custom_id");
        if (!customId) {
          router.push("/login");
        } else {
          // 有custom_id，显示警告但不跳转
          ElMessage({
            message: "JWT令牌已过期，但仍可使用X-User-ID认证",
            type: "warning",
            duration: 5 * 1000,
          });
        }
      }
      // 405: Method Not Allowed - 请求方法不允许
      else if (status === 405) {
        console.error(`请求方法不允许: ${error.config?.method} ${url}`);
        ElMessage({
          message: `API方法错误: 当前使用 ${error.config?.method?.toUpperCase()} 方法，但API不支持此方法`,
          type: "error",
          duration: 5 * 1000,
        });
      }
      else {
        // 显示错误信息
        ElMessage({
          message: error.response.data?.message || `请求失败: ${status}`,
          type: "error",
          duration: 5 * 1000,
        });
      }
    } else if (error.request) {
      // 请求已发送但没有收到响应
      console.log("请求已发送但没有收到响应");

      // 检查是否为本地环境
      const isLocalhost =
        window.location.hostname === "localhost" ||
        window.location.hostname === "127.0.0.1";

      // 本地环境下，提示用户检查后端服务
      if (isLocalhost) {
        ElMessage({
          message: "无法连接到后端服务，请确保后端服务已启动",
          type: "error",
          duration: 5 * 1000,
        });
      } else {
        // 非本地环境，提示网络错误
        ElMessage({
          message: "网络错误，请检查您的网络连接",
          type: "error",
          duration: 5 * 1000,
        });
      }
    } else {
      // 其他错误
      ElMessage({
        message: error.message || "未知错误",
        type: "error",
        duration: 5 * 1000,
      });
    }

    return Promise.reject(error);
  }
);

export default service;
