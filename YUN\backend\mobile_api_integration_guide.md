# 移动端计分API集成指南

## 概述

本指南说明如何将移动端修改为使用新的计分API，实现原始答案提交和后端自动计分。

## 主要变更

### 1. 新增API端点

#### 评估量表提交（新）
```
POST /api/mobile/assessments/{assessment_id}/submit-raw
```

#### 问卷提交（新）
```
POST /api/mobile/questionnaires/{questionnaire_id}/submit-raw
```

### 2. 请求格式变更

#### 旧格式（包含分数）
```json
{
  "answers": [
    {
      "question_id": "1",
      "answer": "选项A",
      "score": 3
    }
  ]
}
```

#### 新格式（仅原始答案）
```json
{
  "answers": [
    {
      "question_id": "1",
      "answer": "选项A"
    },
    {
      "question_id": "2",
      "answer": ["选项A", "选项B"]
    }
  ]
}
```

### 3. 响应格式

#### 评估量表响应
```json
{
  "status": "success",
  "message": "评估量表提交成功",
  "data": {
    "assessment_id": 123,
    "response_id": 456,
    "result_id": 789,
    "total_score": 85,
    "max_score": 100,
    "percentage": 85.0,
    "result_category": "良好",
    "conclusion": "评估结果良好，大部分指标正常",
    "dimension_scores": {
      "emotion": {
        "name": "情绪症状",
        "score": 25,
        "max_score": 30,
        "weight": 1.0,
        "weighted_score": 25
      }
    },
    "completed_at": "2024-01-01T12:00:00"
  }
}
```

#### 问卷响应
```json
{
  "status": "success",
  "message": "问卷提交成功",
  "data": {
    "questionnaire_id": 123,
    "response_id": 456,
    "result_id": 789,
    "total_score": 45,
    "max_score": 50,
    "completion_rate": 90.0,
    "result_category": "健康状况调查",
    "conclusion": "健康状况调查已完成，完成率: 90.0%",
    "completed_at": "2024-01-01T12:00:00"
  }
}
```

## 移动端修改指南

### 1. 移除客户端计分逻辑

移动端不再需要：
- 计分规则配置
- 客户端计分函数
- 分数计算逻辑
- 维度分数计算

### 2. 修改数据提交逻辑

#### 修改前（包含分数）
```javascript
// 旧的提交逻辑
const submitData = {
  answers: answers.map(answer => ({
    question_id: answer.questionId,
    answer: answer.selectedOption,
    score: calculateScore(answer) // 移除这行
  }))
};
```

#### 修改后（仅原始答案）
```javascript
// 新的提交逻辑
const submitData = {
  answers: answers.map(answer => ({
    question_id: answer.questionId,
    answer: answer.selectedOption // 仅提交原始答案
  }))
};
```

### 3. 更新API调用

#### 评估量表提交
```javascript
// 使用新的API端点
const response = await fetch(`/api/mobile/assessments/${assessmentId}/submit-raw`, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify(submitData)
});

const result = await response.json();
if (result.status === 'success') {
  // 处理成功响应
  const {
    total_score,
    max_score,
    percentage,
    result_category,
    conclusion,
    dimension_scores
  } = result.data;
  
  // 显示结果
  showResults({
    score: total_score,
    maxScore: max_score,
    percentage: percentage,
    category: result_category,
    conclusion: conclusion,
    dimensions: dimension_scores
  });
}
```

#### 问卷提交
```javascript
// 使用新的API端点
const response = await fetch(`/api/mobile/questionnaires/${questionnaireId}/submit-raw`, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify(submitData)
});

const result = await response.json();
if (result.status === 'success') {
  // 处理成功响应
  const {
    completion_rate,
    result_category,
    conclusion
  } = result.data;
  
  // 显示结果
  showQuestionnaireResults({
    completionRate: completion_rate,
    category: result_category,
    conclusion: conclusion
  });
}
```

### 4. 进度条逻辑调整

由于移动端不再进行计分，进度条可以基于：
- 已回答问题数量
- 总问题数量
- 完成百分比

```javascript
// 进度条更新逻辑
const updateProgress = (answeredCount, totalCount) => {
  const progress = (answeredCount / totalCount) * 100;
  setProgressPercentage(progress);
};
```

### 5. 历史记录显示

移动端可以通过现有的API获取历史记录：
- `/api/mobile/assessments` - 获取评估量表列表
- `/api/mobile/questionnaires` - 获取问卷列表
- `/api/assessment-results/{result_id}` - 获取详细结果
- `/api/questionnaire-results/{result_id}` - 获取详细结果

## 后端集成

### 1. 注册新的API路由

在 `app/api/api.py` 中添加：

```python
from app.api.endpoints import mobile_scoring_api

api_router.include_router(
    mobile_scoring_api.router,
    prefix="/mobile",
    tags=["移动端计分"]
)
```

### 2. 数据库迁移

确保数据库中的计分规则和维度配置已正确设置（已通过之前的脚本完成）。

## 测试建议

### 1. 单元测试

为新的计分服务创建单元测试：

```python
def test_assessment_scoring():
    # 测试评估量表计分
    pass

def test_questionnaire_scoring():
    # 测试问卷计分
    pass
```

### 2. 集成测试

测试完整的提交流程：
- 原始答案提交
- 后端计分
- 结果保存
- 响应返回

### 3. 移动端测试

- 测试新的API调用
- 验证响应处理
- 检查进度条显示
- 确认历史记录功能

## 部署注意事项

1. **向后兼容性**：保留旧的API端点一段时间，以便平滑迁移
2. **数据验证**：确保所有模板的计分规则配置正确
3. **性能监控**：监控新API的响应时间和错误率
4. **用户通知**：通知用户更新移动端应用

## 常见问题

### Q: 如果计分规则缺失怎么办？
A: 系统会使用默认计分规则，并记录警告日志。

### Q: 如何处理部分回答的情况？
A: 系统会计算已回答问题的分数，并在结果中标明完成率。

### Q: 维度分数如何计算？
A: 根据问题所属维度和维度权重自动计算。

### Q: 如何查看详细的计分过程？
A: 可以通过 `calculation_details` 字段查看详细的计分过程。
