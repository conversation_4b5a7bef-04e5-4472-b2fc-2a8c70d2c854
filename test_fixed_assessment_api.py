import requests
import json

# 测试修复后的评估记录API
base_url = "http://localhost:8006"

# 先登录获取token
login_url = f"{base_url}/api/auth/login_json"
login_data = {
    "username": "SM_008",
    "password": "123456"
}

print("正在登录...")
try:
    login_response = requests.post(login_url, json=login_data)
    print(f"登录状态码: {login_response.status_code}")
    
    if login_response.status_code == 200:
        login_result = login_response.json()
        token = login_result.get('access_token')
        print(f"登录成功，获取到token")
        
        # 设置认证头
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        # 测试获取SM_008用户的评估记录
        test_url = f"{base_url}/api/user-health-records/SM_008?record_type=assessment"
        print(f"\n测试URL: {test_url}")
        print("="*50)
        
        response = requests.get(test_url, headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"\n响应数据结构:")
            print(f"- status: {data.get('status')}")
            print(f"- total: {data.get('total')}")
            print(f"- page_total: {data.get('page_total')}")
            print(f"- items数量: {len(data.get('items', []))}")
            
            items = data.get('items', [])
            if items:
                print(f"\n评估记录详情:")
                for i, item in enumerate(items, 1):
                    print(f"\n记录 {i}:")
                    print(f"  - id: {item.get('id')}")
                    print(f"  - name: {item.get('name')}")
                    print(f"  - status: {item.get('status')}")
                    print(f"  - type: {item.get('type')}")
                    print(f"  - score: {item.get('score')}")
                    print(f"  - created_at: {item.get('created_at')}")
                    print(f"  - completed_at: {item.get('completed_at')}")
            else:
                print("\n没有找到评估记录")
        else:
            print(f"\n错误响应: {response.text}")
        
        # 测试获取已完成状态的评估记录
        print("\n" + "="*50)
        print("测试获取已完成状态的评估记录")
        test_url_completed = f"{base_url}/api/user-health-records/SM_008?record_type=assessment&status=completed"
        print(f"测试URL: {test_url_completed}")
        
        response = requests.get(test_url_completed, headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"\n已完成评估记录:")
            print(f"- total: {data.get('total')}")
            print(f"- items数量: {len(data.get('items', []))}")
            
            items = data.get('items', [])
            for i, item in enumerate(items, 1):
                print(f"\n记录 {i}: {item.get('name')} - {item.get('status')}")
        else:
            print(f"\n错误响应: {response.text}")
            
    else:
        print(f"登录失败: {login_response.text}")
        
except Exception as e:
    print(f"请求失败: {e}")