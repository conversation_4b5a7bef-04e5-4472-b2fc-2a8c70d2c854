#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复评估数据问题 - 为移动端创建测试评估数据
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'YUN', 'backend'))

import requests
import json
from datetime import datetime, timedelta

# API配置
BASE_URL = "http://localhost:8006"

def get_auth_token():
    """获取认证token"""
    print("=== 获取认证token ===")
    
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/auth/login_json",
            json=login_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            token = data.get('access_token')
            custom_id = data.get('custom_id')
            print(f"登录成功，获取到token: {token[:20]}...")
            print(f"用户custom_id: {custom_id}")
            return token, custom_id
        else:
            print(f"登录失败: {response.status_code} - {response.text}")
            return None, None
            
    except Exception as e:
        print(f"登录异常: {e}")
        return None, None

def create_assessment_via_api(token, custom_id):
    """通过API创建评估数据"""
    print("\n=== 通过API创建评估数据 ===")
    
    if not token:
        print("没有有效的token，无法创建评估数据")
        return
    
    # 创建评估数据
    assessment_data = {
        "name": "心理健康评估量表",
        "custom_id": "SM_006",
        "template_id": 1,  # 假设存在ID为1的模板
        "status": "pending",
        "assessment_type": "psychological"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }
    
    try:
        # 尝试创建评估
        response = requests.post(
            f"{BASE_URL}/api/assessments",
            json=assessment_data,
            headers=headers,
            timeout=10
        )
        
        print(f"创建评估响应状态码: {response.status_code}")
        print(f"创建评估响应内容: {response.text}")
        
        if response.status_code in [200, 201]:
            print("评估创建成功！")
        else:
            print(f"评估创建失败: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"创建评估异常: {e}")

def check_assessment_templates(token):
    """检查评估模板"""
    print("\n=== 检查评估模板 ===")
    
    if not token:
        print("没有有效的token，无法检查模板")
        return
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }
    
    try:
        # 获取评估模板列表
        response = requests.get(
            f"{BASE_URL}/api/assessment-templates",
            headers=headers,
            timeout=10
        )
        
        print(f"获取模板响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"模板数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            # 检查模板是否包含description和instructions
            if isinstance(data, dict) and 'data' in data:
                templates = data['data']
            elif isinstance(data, list):
                templates = data
            else:
                templates = []
            
            print(f"\n找到 {len(templates)} 个模板:")
            for template in templates:
                print(f"  ID: {template.get('id')}")
                print(f"  名称: {template.get('name')}")
                print(f"  描述: {template.get('description')}")
                print(f"  说明: {template.get('instructions')}")
                print("  ---")
        else:
            print(f"获取模板失败: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"检查模板异常: {e}")

def test_mobile_api_after_fix(token):
    """修复后测试移动端API"""
    print("\n=== 修复后测试移动端API ===")
    
    if not token:
        print("没有有效的token，无法测试API")
        return
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}",
        "X-User-ID": "SM_006"
    }
    
    try:
        response = requests.get(
            f"{BASE_URL}/api/mobile/assessments",
            headers=headers,
            timeout=10
        )
        
        print(f"移动端API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"移动端API响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            # 检查template字段
            if isinstance(data, dict) and 'data' in data:
                assessments = data['data'].get('assessments', [])
            else:
                assessments = data if isinstance(data, list) else []
            
            print(f"\n返回 {len(assessments)} 个评估:")
            for assessment in assessments:
                template = assessment.get('template')
                print(f"  评估ID: {assessment.get('id')}")
                print(f"  评估名称: {assessment.get('name')}")
                print(f"  Template存在: {template is not None}")
                if template:
                    print(f"  Template描述: {template.get('description')}")
                    print(f"  Template说明: {template.get('instructions')}")
                print("  ---")
        else:
            print(f"移动端API调用失败: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"测试移动端API异常: {e}")

def main():
    """主函数"""
    print("开始修复评估数据问题...")
    
    # 1. 获取认证token
    token, user_custom_id = get_auth_token()
    
    # 2. 检查评估模板
    check_assessment_templates(token)
    
    # 3. 创建评估数据
    create_assessment_via_api(token, user_custom_id)
    
    # 4. 测试修复后的移动端API
    test_mobile_api_after_fix(token)
    
    print("\n修复完成！")

if __name__ == "__main__":
    main()