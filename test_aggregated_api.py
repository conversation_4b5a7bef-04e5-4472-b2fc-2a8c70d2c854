# -*- coding: utf-8 -*-
import requests
import json

# 测试聚合API
def test_aggregated_api():
    base_url = "http://localhost:8006"
    
    # 先登录获取token
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        print("开始测试聚合API...")
        
        # 登录
        login_response = requests.post(f"{base_url}/auth/login", data=login_data)
        print(f"登录状态码: {login_response.status_code}")
        
        if login_response.status_code == 200:
            print(f"登录响应原始内容: {login_response.text}")
            try:
                token_data = login_response.json()
                print(f"登录响应数据: {token_data}")
                access_token = token_data.get("access_token")
                if not access_token:
                    print("未找到access_token，检查响应数据结构")
                    return
                print(f"获取到token: {access_token[:20]}...")
            except Exception as json_error:
                print(f"解析JSON失败: {json_error}")
                return
            
            # 测试聚合API
            headers = {
                "Authorization": f"Bearer {access_token}"
            }
            
            # 测试用户数据聚合API
            api_url = f"{base_url}/api/v1/aggregated/users/SM_001/data"
            print(f"\n测试API: {api_url}")
            
            response = requests.get(api_url, headers=headers)
            print(f"API响应状态码: {response.status_code}")
            print(f"API响应内容: {response.text[:500]}...")
            
            if response.status_code == 200:
                data = response.json()
                print("\n聚合API测试成功!")
                print(f"返回数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")
            else:
                print(f"\nAPI调用失败: {response.status_code}")
                print(f"错误信息: {response.text}")
        else:
            print(f"登录失败: {login_response.text}")
            
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")

if __name__ == "__main__":
    test_aggregated_api()