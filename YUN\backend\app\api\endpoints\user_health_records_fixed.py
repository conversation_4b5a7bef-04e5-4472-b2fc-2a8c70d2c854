
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户健康记录API端点 - 修复版本
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime

from app.db.session import get_db, get_async_session
from app.core.auth import get_current_user
from app.models.user import User
from app.core.response_handler import success_response, error_response

router = APIRouter()

@router.get("/users/{custom_id}/health-records")
async def get_user_health_records(
    custom_id: str = Path(..., description="用户自定义ID"),
    record_type: Optional[str] = Query(None, description="记录类型"),
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期"),
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    session: AsyncSession = Depends(get_async_session)
):
    """获取用户健康记录"""
    try:
        # 模拟健康记录数据
        health_records = [
            {
                "id": 1,
                "custom_id": custom_id,
                "record_type": "questionnaire",
                "title": "健康状况调查",
                "status": "completed",
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            },
            {
                "id": 2,
                "custom_id": custom_id,
                "record_type": "assessment",
                "title": "心理健康评估",
                "status": "pending",
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }
        ]
        
        # 应用过滤条件
        if record_type:
            health_records = [r for r in health_records if r["record_type"] == record_type]
        
        # 分页
        total = len(health_records)
        paginated_records = health_records[skip:skip+limit]
        
        return success_response(
            data={
                "records": paginated_records,
                "total": total,
                "skip": skip,
                "limit": limit
            },
            message="获取用户健康记录成功"
        )
        
    except Exception as e:
        return error_response(
            error_type="system_error",
            error_key="GET_USER_HEALTH_RECORDS_ERROR",
            message="获取用户健康记录失败",
            details={"exception": str(e)}
        )
