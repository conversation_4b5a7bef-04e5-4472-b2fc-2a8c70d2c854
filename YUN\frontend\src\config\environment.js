/**
 * 环境配置管理
 * 统一管理不同环境下的配置和模拟数据
 */

// 环境类型
export const ENV_TYPES = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production',
  TEST: 'test'
}

// 当前环境
export const CURRENT_ENV = import.meta.env.MODE || ENV_TYPES.DEVELOPMENT

// 是否为开发环境
export const isDevelopment = CURRENT_ENV === ENV_TYPES.DEVELOPMENT
export const isProduction = CURRENT_ENV === ENV_TYPES.PRODUCTION
export const isTest = CURRENT_ENV === ENV_TYPES.TEST

// 是否启用模拟数据
export const ENABLE_MOCK_DATA = import.meta.env.VITE_ENABLE_MOCK === 'true' || isDevelopment

// API配置
export const API_CONFIG = {
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: parseInt(import.meta.env.VITE_API_TIMEOUT) || 15000,
  retryCount: parseInt(import.meta.env.VITE_API_RETRY_COUNT) || 3,
  retryDelay: parseInt(import.meta.env.VITE_API_RETRY_DELAY) || 1000
}

// 日志配置
export const LOG_CONFIG = {
  level: import.meta.env.VITE_LOG_LEVEL || (isProduction ? 'error' : 'debug'),
  enableConsole: import.meta.env.VITE_ENABLE_CONSOLE_LOG !== 'false',
  enableRemote: import.meta.env.VITE_ENABLE_REMOTE_LOG === 'true'
}

// 缓存配置
export const CACHE_CONFIG = {
  enabled: import.meta.env.VITE_ENABLE_CACHE !== 'false',
  defaultTTL: parseInt(import.meta.env.VITE_CACHE_TTL) || 5 * 60 * 1000, // 5分钟
  maxSize: parseInt(import.meta.env.VITE_CACHE_MAX_SIZE) || 100
}

// 性能监控配置
export const PERFORMANCE_CONFIG = {
  enabled: import.meta.env.VITE_ENABLE_PERFORMANCE_MONITOR === 'true',
  slowRequestThreshold: parseInt(import.meta.env.VITE_SLOW_REQUEST_THRESHOLD) || 3000,
  enableResourceTiming: import.meta.env.VITE_ENABLE_RESOURCE_TIMING === 'true'
}

// 安全配置
export const SECURITY_CONFIG = {
  enableCSRF: import.meta.env.VITE_ENABLE_CSRF !== 'false',
  tokenRefreshThreshold: parseInt(import.meta.env.VITE_TOKEN_REFRESH_THRESHOLD) || 5 * 60 * 1000, // 5分钟
  maxLoginAttempts: parseInt(import.meta.env.VITE_MAX_LOGIN_ATTEMPTS) || 5
}

// 功能开关配置
export const FEATURE_FLAGS = {
  enableVirtualScroll: import.meta.env.VITE_ENABLE_VIRTUAL_SCROLL === 'true',
  enableLazyLoading: import.meta.env.VITE_ENABLE_LAZY_LOADING !== 'false',
  enableOfflineMode: import.meta.env.VITE_ENABLE_OFFLINE_MODE === 'true',
  enablePWA: import.meta.env.VITE_ENABLE_PWA === 'true'
}

// 调试配置
export const DEBUG_CONFIG = {
  showApiLogs: isDevelopment || import.meta.env.VITE_SHOW_API_LOGS === 'true',
  showPerformanceLogs: isDevelopment || import.meta.env.VITE_SHOW_PERFORMANCE_LOGS === 'true',
  showErrorDetails: isDevelopment || import.meta.env.VITE_SHOW_ERROR_DETAILS === 'true'
}

// 导出统一配置对象
export const CONFIG = {
  env: CURRENT_ENV,
  isDevelopment,
  isProduction,
  isTest,
  enableMockData: ENABLE_MOCK_DATA,
  api: API_CONFIG,
  log: LOG_CONFIG,
  cache: CACHE_CONFIG,
  performance: PERFORMANCE_CONFIG,
  security: SECURITY_CONFIG,
  features: FEATURE_FLAGS,
  debug: DEBUG_CONFIG
}

export default CONFIG