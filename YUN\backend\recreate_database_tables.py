#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新创建数据库表的脚本
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

print("脚本开始执行...")
print(f"当前工作目录: {os.getcwd()}")
print(f"Python路径: {sys.path[:3]}")

try:
    print("尝试导入模块...")
    from app.db.session import create_tables, engine
    from sqlalchemy import text
    print("模块导入成功！")
    
    print("开始重新创建数据库表...")
    
    # 首先检查当前数据库中的表
    with engine.connect() as conn:
        result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table'"))
        existing_tables = [row[0] for row in result.fetchall()]
        print(f"当前数据库中的表: {existing_tables}")
    
    # 创建所有表
    create_tables()
    
    # 再次检查创建后的表
    with engine.connect() as conn:
        result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table'"))
        new_tables = [row[0] for row in result.fetchall()]
        print(f"创建后数据库中的表: {new_tables}")
        
        # 检查关键表是否存在
        required_tables = ['assessments', 'assessment_results', 'assessment_responses', 
                          'questionnaires', 'questionnaire_results', 'questionnaire_responses']
        
        missing_tables = []
        for table in required_tables:
            if table not in new_tables:
                missing_tables.append(table)
        
        if missing_tables:
            print(f"警告：以下关键表仍然缺失: {missing_tables}")
        else:
            print("所有关键表都已成功创建！")
            
        # 检查assessment_results表的结构
        if 'assessment_results' in new_tables:
            result = conn.execute(text("PRAGMA table_info(assessment_results)"))
            columns = result.fetchall()
            print(f"\nassessment_results表结构:")
            for col in columns:
                print(f"  {col[1]} {col[2]}")
                
except Exception as e:
    print(f"重新创建数据库表时出错: {e}")
    import traceback
    traceback.print_exc()