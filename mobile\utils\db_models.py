import logging
from datetime import datetime
from .database import get_db_manager

# 配置日志
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('db_models')

class Schema:
    """数据库表结构定义"""
    
    # 数据表分类
    TABLE_CATEGORIES = {
        'user': '用户信息',
        'personal': '个人基本信息',
        'health': '健康基本信息',
        'hospital': '住院记录',
        'outpatient': '门诊记录',
        'lab': '实验室化验报告',
        'exam': '技诊检查报告',
        'questionnaire': '调查问卷',
        'assessment': '评估量表',
        'health_log': '健康日志',
        'doctor_log': '医生管理日志',
        'medication': '药物使用信息',
        'wearable': '可穿戴设备数据'
    }
    
    # 用户信息表
    USER_INFO = """
    CREATE TABLE IF NOT EXISTS user_info (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id TEXT NOT NULL UNIQUE,
        username TEXT NOT NULL,
        real_name TEXT,
        gender TEXT,
        birth_date TEXT,
        id_card TEXT,
        phone TEXT,
        email TEXT,
        address TEXT,
        ethnic TEXT,
        education TEXT,
        emergency_contact TEXT,
        emergency_phone TEXT,
        registration_time TEXT NOT NULL,
        last_update_time TEXT NOT NULL,
        last_login_time TEXT
    )
    """
    
    # 用户授权关系表
    USER_AUTHORIZATION = """
    CREATE TABLE IF NOT EXISTS user_authorization (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        custom_id TEXT NOT NULL,
        authorized_to_custom_id TEXT NOT NULL,
        auth_type TEXT NOT NULL,
        start_time TEXT NOT NULL,
        end_time TEXT,
        is_active INTEGER DEFAULT 1,
        UNIQUE(custom_id, authorized_to_custom_id, auth_type)
    )
    """
    
    # 健康基本信息表
    HEALTH_INFO = """
    CREATE TABLE IF NOT EXISTS health_info (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        custom_id TEXT NOT NULL,
        height REAL,
        weight REAL,
        blood_type TEXT,
        rh_factor TEXT,
        last_update_time TEXT NOT NULL
    )
    """
    
    # 疾病史表
    DISEASE_HISTORY = """
    CREATE TABLE IF NOT EXISTS disease_history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        custom_id TEXT NOT NULL,
        disease_name TEXT NOT NULL,
        onset_time TEXT,
        duration TEXT,
        treatment TEXT,
        medication TEXT,
        control_effect TEXT,
        hospital TEXT,
        doctor TEXT,
        notes TEXT,
        record_time TEXT NOT NULL,
        last_update_time TEXT NOT NULL
    )
    """
    
    # 家族病史表
    FAMILY_DISEASE = """
    CREATE TABLE IF NOT EXISTS family_disease (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        custom_id TEXT NOT NULL,
        disease_name TEXT NOT NULL,
        relation TEXT NOT NULL,
        age_of_onset INTEGER,
        treatment TEXT,
        notes TEXT,
        record_time TEXT NOT NULL,
        last_update_time TEXT NOT NULL
    )
    """
    
    # 药物过敏表
    DRUG_ALLERGY = """
    CREATE TABLE IF NOT EXISTS drug_allergy (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        custom_id TEXT NOT NULL,
        drug_name TEXT NOT NULL,
        reaction TEXT,
        severity TEXT,
        diagnosis_time TEXT,
        hospital TEXT,
        doctor TEXT,
        notes TEXT,
        record_time TEXT NOT NULL,
        last_update_time TEXT NOT NULL
    )
    """
    
    # 基因信息表
    GENE_INFO = """
    CREATE TABLE IF NOT EXISTS gene_info (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        custom_id TEXT NOT NULL,
        gene_name TEXT NOT NULL,
        gene_value TEXT,
        interpretation TEXT,
        test_time TEXT,
        test_organization TEXT,
        notes TEXT,
        record_time TEXT NOT NULL,
        last_update_time TEXT NOT NULL
    )
    """
    
    # 住院记录表
    HOSPITAL_RECORD = """
    CREATE TABLE IF NOT EXISTS hospital_record (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        custom_id TEXT NOT NULL,
        hospital_name TEXT NOT NULL,
        department TEXT,
        admission_time TEXT,
        discharge_time TEXT,
        admission_diagnosis TEXT,
        discharge_diagnosis TEXT,
        chief_doctor TEXT,
        treatment TEXT,
        notes TEXT,
        local_file_path TEXT,
        cloud_file_id TEXT,
        record_time TEXT NOT NULL,
        last_update_time TEXT NOT NULL
    )
    """
    
    # 手术记录表
    SURGERY_RECORD = """
    CREATE TABLE IF NOT EXISTS surgery_record (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        custom_id TEXT NOT NULL,
        hospital_record_id INTEGER,
        surgery_name TEXT NOT NULL,
        surgery_time TEXT,
        surgeon TEXT,
        anesthesia_type TEXT,
        anesthesiologist TEXT,
        duration TEXT,
        outcome TEXT,
        notes TEXT,
        record_time TEXT NOT NULL,
        last_update_time TEXT NOT NULL,
        FOREIGN KEY(hospital_record_id) REFERENCES hospital_record(id)
    )
    """
    
    # 门诊记录表
    OUTPATIENT_RECORD = """
    CREATE TABLE IF NOT EXISTS outpatient_record (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        custom_id TEXT NOT NULL,
        hospital_name TEXT NOT NULL,
        department TEXT,
        visit_time TEXT,
        doctor TEXT,
        chief_complaint TEXT,
        diagnosis TEXT,
        treatment TEXT,
        prescription TEXT,
        follow_up TEXT,
        notes TEXT,
        local_file_path TEXT,
        cloud_file_id TEXT,
        record_time TEXT NOT NULL,
        last_update_time TEXT NOT NULL
    )
    """
    
    # 化验报告主表
    LAB_REPORT = """
    CREATE TABLE IF NOT EXISTS lab_report (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        custom_id TEXT NOT NULL,
        hospital_name TEXT NOT NULL,
        report_type TEXT NOT NULL,
        specimen_type TEXT,
        collection_time TEXT,
        report_time TEXT,
        doctor TEXT,
        notes TEXT,
        local_file_path TEXT,
        cloud_file_id TEXT,
        record_time TEXT NOT NULL,
        last_update_time TEXT NOT NULL
    )
    """
    
    # 化验报告明细表
    LAB_REPORT_ITEM = """
    CREATE TABLE IF NOT EXISTS lab_report_item (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        lab_report_id INTEGER NOT NULL,
        item_name TEXT NOT NULL,
        item_value TEXT,
        unit TEXT,
        reference_range TEXT,
        abnormal INTEGER DEFAULT 0,
        notes TEXT,
        FOREIGN KEY(lab_report_id) REFERENCES lab_report(id)
    )
    """
    
    # 技诊报告表
    EXAMINATION_REPORT = """
    CREATE TABLE IF NOT EXISTS examination_report (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        custom_id TEXT NOT NULL,
        hospital_name TEXT NOT NULL,
        exam_type TEXT NOT NULL,
        exam_part TEXT,
        exam_time TEXT,
        report_time TEXT,
        device TEXT,
        doctor TEXT,
        description TEXT,
        conclusion TEXT,
        notes TEXT,
        local_file_path TEXT,
        cloud_file_id TEXT,
        record_time TEXT NOT NULL,
        last_update_time TEXT NOT NULL
    )
    """
    
    # 问卷表
    QUESTIONNAIRE = """
    CREATE TABLE IF NOT EXISTS questionnaire (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        custom_id TEXT NOT NULL,
        questionnaire_type TEXT NOT NULL,
        questionnaire_name TEXT NOT NULL,
        version TEXT,
        complete_time TEXT,
        score TEXT,
        conclusion TEXT,
        notes TEXT,
        record_time TEXT NOT NULL,
        last_update_time TEXT NOT NULL
    )
    """
    
    # 问卷答案表
    QUESTIONNAIRE_ANSWER = """
    CREATE TABLE IF NOT EXISTS questionnaire_answer (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        questionnaire_id INTEGER NOT NULL,
        question_id TEXT NOT NULL,
        question_text TEXT,
        answer TEXT,
        score INTEGER,
        notes TEXT,
        FOREIGN KEY(questionnaire_id) REFERENCES questionnaire(id)
    )
    """
    
    # 评估量表表
    ASSESSMENT_SCALE = """
    CREATE TABLE IF NOT EXISTS assessment_scale (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        custom_id TEXT NOT NULL,
        scale_type TEXT NOT NULL,
        scale_name TEXT NOT NULL,
        version TEXT,
        complete_time TEXT,
        assessor TEXT,
        score TEXT,
        conclusion TEXT,
        notes TEXT,
        record_time TEXT NOT NULL,
        last_update_time TEXT NOT NULL
    )
    """
    
    # 评估量表项目表
    ASSESSMENT_SCALE_ITEM = """
    CREATE TABLE IF NOT EXISTS assessment_scale_item (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        assessment_scale_id INTEGER NOT NULL,
        item_id TEXT NOT NULL,
        item_text TEXT,
        score INTEGER,
        notes TEXT,
        FOREIGN KEY(assessment_scale_id) REFERENCES assessment_scale(id)
    )
    """
    
    # 健康日志表
    HEALTH_LOG = """
    CREATE TABLE IF NOT EXISTS health_log (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        custom_id TEXT NOT NULL,
        log_type TEXT NOT NULL,
        log_time TEXT NOT NULL,
        title TEXT,
        content TEXT,
        mood TEXT,
        symptoms TEXT,
        tags TEXT,
        images TEXT,
        record_time TEXT NOT NULL,
        last_update_time TEXT NOT NULL
    )
    """
    
    # 医生管理日志表
    DOCTOR_LOG = """
    CREATE TABLE IF NOT EXISTS doctor_log (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        doctor_id TEXT NOT NULL,
        custom_id TEXT NOT NULL,
        log_type TEXT NOT NULL,
        log_time TEXT NOT NULL,
        location TEXT,
        title TEXT,
        content TEXT,
        diagnosis TEXT,
        treatment TEXT,
        follow_up_plan TEXT,
        tags TEXT,
        images TEXT,
        record_time TEXT NOT NULL,
        last_update_time TEXT NOT NULL
    )
    """
    
    # 药物使用信息表
    MEDICATION = """
    CREATE TABLE IF NOT EXISTS medication (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        custom_id TEXT NOT NULL,
        name TEXT NOT NULL,
        dosage TEXT,
        frequency TEXT,
        start_date TEXT,
        end_date TEXT,
        instructions TEXT,
        prescription_required INTEGER DEFAULT 0,
        notes TEXT,
        medication_type TEXT,
        is_current INTEGER DEFAULT 1,
        stop_reason TEXT,
        prescriber TEXT,
        hospital TEXT,
        purpose TEXT,
        side_effects TEXT,
        specification TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
    )
    """
    
    # 药物服用记录表
    MEDICATION_USAGE = """
    CREATE TABLE IF NOT EXISTS medication_usage (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        medication_id INTEGER NOT NULL,
        taken_at TEXT NOT NULL,
        dosage_taken TEXT,
        notes TEXT,
        created_at TEXT NOT NULL,
        FOREIGN KEY(medication_id) REFERENCES medication(id)
    )
    """
    
    # 可穿戴设备数据表
    WEARABLE_DEVICE = """
    CREATE TABLE IF NOT EXISTS wearable_device (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        custom_id TEXT NOT NULL,
        device_type TEXT NOT NULL,
        device_name TEXT,
        device_id TEXT,
        manufacturer TEXT,
        notes TEXT,
        is_active INTEGER DEFAULT 1,
        record_time TEXT NOT NULL,
        last_update_time TEXT NOT NULL
    )
    """
    
    # 可穿戴设备心率数据表
    WEARABLE_HEART_RATE = """
    CREATE TABLE IF NOT EXISTS wearable_heart_rate (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        wearable_device_id INTEGER NOT NULL,
        measure_time TEXT NOT NULL,
        heart_rate INTEGER,
        activity_level TEXT,
        notes TEXT,
        FOREIGN KEY(wearable_device_id) REFERENCES wearable_device(id)
    )
    """
    
    # 可穿戴设备血压数据表
    WEARABLE_BLOOD_PRESSURE = """
    CREATE TABLE IF NOT EXISTS wearable_blood_pressure (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        wearable_device_id INTEGER NOT NULL,
        measure_time TEXT NOT NULL,
        systolic INTEGER,
        diastolic INTEGER,
        pulse INTEGER,
        position TEXT,
        notes TEXT,
        FOREIGN KEY(wearable_device_id) REFERENCES wearable_device(id)
    )
    """
    
    # 可穿戴设备睡眠数据表
    WEARABLE_SLEEP = """
    CREATE TABLE IF NOT EXISTS wearable_sleep (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        wearable_device_id INTEGER NOT NULL,
        sleep_start TEXT NOT NULL,
        sleep_end TEXT,
        deep_sleep_duration INTEGER,
        light_sleep_duration INTEGER,
        rem_sleep_duration INTEGER,
        awake_duration INTEGER,
        total_duration INTEGER,
        sleep_quality TEXT,
        notes TEXT,
        FOREIGN KEY(wearable_device_id) REFERENCES wearable_device(id)
    )
    """
    
    # 可穿戴设备活动数据表
    WEARABLE_ACTIVITY = """
    CREATE TABLE IF NOT EXISTS wearable_activity (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        wearable_device_id INTEGER NOT NULL,
        activity_date TEXT NOT NULL,
        steps INTEGER,
        distance REAL,
        calories REAL,
        active_minutes INTEGER,
        floors INTEGER,
        notes TEXT,
        FOREIGN KEY(wearable_device_id) REFERENCES wearable_device(id)
    )
    """
    
    # 文件上传记录表
    FILE_UPLOAD = """
    CREATE TABLE IF NOT EXISTS file_upload (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        custom_id TEXT NOT NULL,
        file_name TEXT NOT NULL,
        file_type TEXT NOT NULL,
        upload_type TEXT NOT NULL,
        upload_time TEXT NOT NULL,
        local_path TEXT,
        cloud_id TEXT,
        ocr_result TEXT,
        related_table TEXT,
        related_id INTEGER,
        notes TEXT,
        processing_status TEXT,
        record_time TEXT NOT NULL,
        last_update_time TEXT NOT NULL
    )
    """
    
    # 同步记录表
    SYNC_RECORD = """
    CREATE TABLE IF NOT EXISTS sync_record (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        table_name TEXT NOT NULL,
        record_id INTEGER NOT NULL,
        sync_status TEXT NOT NULL,
        sync_time TEXT,
        error_message TEXT,
        retry_count INTEGER DEFAULT 0,
        last_retry_time TEXT,
        record_time TEXT NOT NULL,
        last_update_time TEXT NOT NULL,
        UNIQUE(table_name, record_id)
    )
    """

class DBInitializer:
    """数据库初始化器"""
    
    @staticmethod
    def create_tables(user_id):
        """为用户创建所有需要的数据表"""
        db_manager = get_db_manager()
        
        # 连接到用户数据库
        if not db_manager.connect(user_id):
            logger.error(f"无法连接到用户 {user_id} 的数据库")
            return False
        
        # 创建所有表
        tables = [
            ('user_info', Schema.USER_INFO),
            ('user_authorization', Schema.USER_AUTHORIZATION),
            ('health_info', Schema.HEALTH_INFO),
            ('disease_history', Schema.DISEASE_HISTORY),
            ('family_disease', Schema.FAMILY_DISEASE),
            ('drug_allergy', Schema.DRUG_ALLERGY),
            ('gene_info', Schema.GENE_INFO),
            ('hospital_record', Schema.HOSPITAL_RECORD),
            ('surgery_record', Schema.SURGERY_RECORD),
            ('outpatient_record', Schema.OUTPATIENT_RECORD),
            ('lab_report', Schema.LAB_REPORT),
            ('lab_report_item', Schema.LAB_REPORT_ITEM),
            ('examination_report', Schema.EXAMINATION_REPORT),
            ('questionnaire', Schema.QUESTIONNAIRE),
            ('questionnaire_answer', Schema.QUESTIONNAIRE_ANSWER),
            ('assessment_scale', Schema.ASSESSMENT_SCALE),
            ('assessment_scale_item', Schema.ASSESSMENT_SCALE_ITEM),
            ('health_log', Schema.HEALTH_LOG),
            ('doctor_log', Schema.DOCTOR_LOG),
            ('medication', Schema.MEDICATION),
            ('medication_usage', Schema.MEDICATION_USAGE),
            ('wearable_device', Schema.WEARABLE_DEVICE),
            ('wearable_heart_rate', Schema.WEARABLE_HEART_RATE),
            ('wearable_blood_pressure', Schema.WEARABLE_BLOOD_PRESSURE),
            ('wearable_sleep', Schema.WEARABLE_SLEEP),
            ('wearable_activity', Schema.WEARABLE_ACTIVITY),
            ('file_upload', Schema.FILE_UPLOAD),
            ('sync_record', Schema.SYNC_RECORD)
        ]
        
        success = True
        for table_name, table_schema in tables:
            if not db_manager.execute_script(table_schema, user_id):
                logger.error(f"创建表 {table_name} 失败")
                success = False
        
        # 创建必要的索引
        if success:
            # 用户ID索引 - 使用custom_id字段
            db_manager.create_index('health_info', ['custom_id'], user_id=user_id)
            db_manager.create_index('disease_history', ['custom_id'], user_id=user_id)
            db_manager.create_index('family_disease', ['custom_id'], user_id=user_id)
            db_manager.create_index('drug_allergy', ['custom_id'], user_id=user_id)
            db_manager.create_index('gene_info', ['custom_id'], user_id=user_id)
            db_manager.create_index('hospital_record', ['custom_id'], user_id=user_id)
            db_manager.create_index('outpatient_record', ['custom_id'], user_id=user_id)
            db_manager.create_index('lab_report', ['custom_id'], user_id=user_id)
            db_manager.create_index('examination_report', ['custom_id'], user_id=user_id)
            db_manager.create_index('questionnaire', ['custom_id'], user_id=user_id)
            db_manager.create_index('assessment_scale', ['custom_id'], user_id=user_id)
            db_manager.create_index('health_log', ['custom_id'], user_id=user_id)
            db_manager.create_index('doctor_log', ['custom_id'], user_id=user_id)
            db_manager.create_index('medication', ['custom_id'], user_id=user_id)
            db_manager.create_index('wearable_device', ['custom_id'], user_id=user_id)
            db_manager.create_index('file_upload', ['custom_id'], user_id=user_id)
            
            # 关联表索引
            db_manager.create_index('surgery_record', ['hospital_record_id'], user_id=user_id)
            db_manager.create_index('lab_report_item', ['lab_report_id'], user_id=user_id)
            db_manager.create_index('questionnaire_answer', ['questionnaire_id'], user_id=user_id)
            db_manager.create_index('assessment_scale_item', ['assessment_scale_id'], user_id=user_id)
            db_manager.create_index('medication_usage', ['medication_id'], user_id=user_id)
            db_manager.create_index('wearable_heart_rate', ['wearable_device_id'], user_id=user_id)
            db_manager.create_index('wearable_blood_pressure', ['wearable_device_id'], user_id=user_id)
            db_manager.create_index('wearable_sleep', ['wearable_device_id'], user_id=user_id)
            db_manager.create_index('wearable_activity', ['wearable_device_id'], user_id=user_id)
            
            # 同步记录索引
            db_manager.create_index('sync_record', ['table_name', 'record_id'], unique=True, user_id=user_id)
            db_manager.create_index('sync_record', ['sync_status'], user_id=user_id)
            
            logger.info(f"成功为用户 {user_id} 创建所有数据表和索引")
        
        # 断开连接
        db_manager.disconnect()
        
        return success