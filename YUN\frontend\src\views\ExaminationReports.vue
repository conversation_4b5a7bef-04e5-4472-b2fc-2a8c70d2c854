<template>
  <div class="examination-reports-container">
    <h1>技诊检查报告管理</h1>

    <!-- 用户查询组件 -->
    <user-search @select-user="handleUserSelect" />

    <el-card class="filter-card">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="类型">
          <el-select v-model="filterForm.examType" placeholder="检查类型" clearable>
            <el-option label="心电图" value="ecg" />
            <el-option label="动态心电图" value="dynamic_ecg" />
            <el-option label="动态血压" value="dynamic_bp" />
            <el-option label="运动平板" value="exercise_ecg" />
            <el-option label="运动心肺试验" value="cpet" />
            <el-option label="超声检查" value="ultrasound" />
            <el-option label="X线" value="xray" />
            <el-option label="CT" value="ct" />
            <el-option label="MR" value="mr" />
            <el-option label="肌电图" value="emg" />
            <el-option label="脑电图" value="eeg" />
            <el-option label="TCD" value="tcd" />
            <el-option label="肝硬度" value="liver_stiffness" />
            <el-option label="身体成份分析" value="body_composition" />
            <el-option label="内镜" value="endoscopy" />
            <el-option label="PET" value="pet" />
            <el-option label="肺功能" value="pulmonary_function" />
            <el-option label="骨密度" value="bone_density" />
            <el-option label="呼气试验" value="breath_test" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>

        <el-form-item label="检查部位" v-if="filterForm.examType === 'ultrasound' || filterForm.examType === 'endoscopy'">
          <el-select v-model="filterForm.examPart" placeholder="检查部位" clearable>
            <template v-if="filterForm.examType === 'ultrasound'">
              <el-option label="心脏" value="heart" />
              <el-option label="腹部" value="abdomen" />
              <el-option label="血管" value="vascular" />
              <el-option label="甲状腺" value="thyroid" />
              <el-option label="乳腺" value="breast" />
              <el-option label="其他" value="other" />
            </template>
            <template v-if="filterForm.examType === 'endoscopy'">
              <el-option label="胃镜" value="gastroscopy" />
              <el-option label="肠镜" value="colonoscopy" />
              <el-option label="鼻咽镜" value="nasopharyngoscopy" />
              <el-option label="支气管镜" value="bronchoscopy" />
              <el-option label="其他" value="other" />
            </template>
          </el-select>
        </el-form-item>

        <el-form-item label="医院">
          <el-input v-model="filterForm.hospitalName" placeholder="医院名称" clearable />
        </el-form-item>

        <el-form-item label="异常">
          <el-select v-model="filterForm.isAbnormal" placeholder="是否异常" clearable>
            <el-option label="是" :value="true" />
            <el-option label="否" :value="false" />
          </el-select>
        </el-form-item>

        <el-form-item label="日期范围">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="fetchUserHealthRecords(selectedUser?.id)">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="reports-card">
      <template #header>
        <div class="card-header">
          <span>技诊检查报告列表</span>
          <div>
            <el-button type="success" @click="exportReports">导出数据</el-button>
            <el-button type="primary" @click="handleCreate" v-if="isAdmin">新增报告</el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="reports"
        style="width: 100%"
        border
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="exam_type" label="类型" width="120">
          <template #default="scope">
            <el-tag :type="getExamTypeTag(scope.row.exam_type)">
              {{ getExamTypeLabel(scope.row.exam_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="hospital_name" label="医院" width="180" />
        <el-table-column prop="department" label="科室" width="120" />
        <el-table-column prop="exam_part" label="检查部位" width="120" />
        <el-table-column prop="exam_date" label="检查日期" width="180" />
        <el-table-column prop="is_abnormal" label="异常" width="80">
          <template #default="scope">
            <el-tag v-if="scope.row.is_abnormal" type="danger">异常</el-tag>
            <el-tag v-else type="success">正常</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="handleView(scope.row)">查看</el-button>
            <el-button size="small" type="primary" @click="handleEdit(scope.row)" v-if="isAdmin">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)" v-if="isAdmin">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 查看/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'view' ? '查看检查报告' : (dialogType === 'create' ? '新增检查报告' : '编辑检查报告')"
      width="60%"
    >
      <el-form
        ref="reportForm"
        :model="currentReport"
        :rules="formRules"
        label-width="100px"
        :disabled="dialogType === 'view'"
      >
        <el-form-item label="检查类型" prop="exam_type">
          <el-select v-model="currentReport.exam_type" placeholder="请选择检查类型">
            <el-option label="心电图" value="ecg" />
            <el-option label="动态心电图" value="dynamic_ecg" />
            <el-option label="动态血压" value="dynamic_bp" />
            <el-option label="运动平板" value="exercise_ecg" />
            <el-option label="超声检查" value="ultrasound" />
            <el-option label="X线" value="xray" />
            <el-option label="CT" value="ct" />
            <el-option label="MRI" value="mri" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>

        <el-form-item label="医院名称" prop="hospital_name">
          <el-input v-model="currentReport.hospital_name" placeholder="请输入医院名称" />
        </el-form-item>

        <el-form-item label="科室">
          <el-input v-model="currentReport.department" placeholder="请输入科室" />
        </el-form-item>

        <el-form-item label="检查部位">
          <el-input v-model="currentReport.exam_part" placeholder="请输入检查部位" />
        </el-form-item>

        <el-form-item label="检查日期">
          <el-date-picker
            v-model="currentReport.exam_date"
            type="datetime"
            placeholder="请选择检查日期"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>

        <el-form-item label="报告日期">
          <el-date-picker
            v-model="currentReport.report_date"
            type="datetime"
            placeholder="请选择报告日期"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>

        <el-form-item label="检查设备">
          <el-input v-model="currentReport.device" placeholder="请输入检查设备" />
        </el-form-item>

        <el-form-item label="医生姓名">
          <el-input v-model="currentReport.doctor_name" placeholder="请输入医生姓名" />
        </el-form-item>

        <el-form-item label="检查所见">
          <el-input
            v-model="currentReport.description"
            type="textarea"
            :rows="3"
            placeholder="请输入检查所见"
          />
        </el-form-item>

        <el-form-item label="检查结论">
          <el-input
            v-model="currentReport.conclusion"
            type="textarea"
            :rows="3"
            placeholder="请输入检查结论"
          />
        </el-form-item>

        <el-form-item label="建议">
          <el-input
            v-model="currentReport.recommendation"
            type="textarea"
            :rows="3"
            placeholder="请输入建议"
          />
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            v-model="currentReport.notes"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
          />
        </el-form-item>

        <el-form-item label="异常标记">
          <el-switch v-model="currentReport.is_abnormal" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveReport" v-if="dialogType !== 'view'">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import axios from 'axios'
import UserSearch from '../components/UserSearch.vue'
import { useUserStore } from '../store/user'

const users = ref([])
const selectedUser = ref(null)
const reports = ref([])
const loading = ref(false)
const userStore = useUserStore()
const isAdmin = computed(() => {
  return userStore.user?.role === 'admin' || userStore.user?.role === 'super_admin'
})
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})
const filterForm = reactive({
  examType: '',
  examPart: '',
  hospitalName: '',
  isAbnormal: null,
  dateRange: []
})

// 统一聚合接口请求
const fetchUserHealthRecords = async (customId) => {
  loading.value = true
  try {
    const response = await axios.get(`/api/user-health-records/user/${customId}`)
    // 分类处理
    const allRecords = response.data || []
    let examRecords = allRecords.filter(r => r.type === 'exam')
    if (filterForm.examType) {
      examRecords = examRecords.filter(r => r.exam_type === filterForm.examType)
    }
    if (filterForm.examPart) {
      examRecords = examRecords.filter(r => r.exam_part === filterForm.examPart)
    }
    if (filterForm.hospitalName) {
      examRecords = examRecords.filter(r => r.hospital_name && r.hospital_name.includes(filterForm.hospitalName))
    }
    if (filterForm.isAbnormal !== null) {
      examRecords = examRecords.filter(r => r.is_abnormal === filterForm.isAbnormal)
    }
    if (filterForm.dateRange && filterForm.dateRange.length === 2) {
      examRecords = examRecords.filter(r => r.exam_date >= filterForm.dateRange[0] && r.exam_date <= filterForm.dateRange[1])
    }
    pagination.total = examRecords.length
    const start = (pagination.currentPage - 1) * pagination.pageSize
    const end = start + pagination.pageSize
    reports.value = examRecords.slice(start, end)
  } catch (error) {
    ElMessage.error('获取检查报告失败')
  } finally {
    loading.value = false
  }
}

const fetchUsers = async () => {
  try {
    const response = await axios.get('/api/users')
    users.value = response.data
  } catch (error) {
    ElMessage.error('获取用户列表失败')
  }
}

const handleUserSelect = (user) => {
  selectedUser.value = user
  if (user && user.id) fetchUserHealthRecords(user.id)
}
const resetFilter = () => {
  filterForm.examType = ''
  filterForm.examPart = ''
  filterForm.hospitalName = ''
  filterForm.isAbnormal = null
  filterForm.dateRange = []
  if (selectedUser.value && selectedUser.value.id) fetchUserHealthRecords(selectedUser.value.id)
}
const handleSizeChange = (size) => {
  pagination.pageSize = size
  if (selectedUser.value && selectedUser.value.id) fetchUserHealthRecords(selectedUser.value.id)
}
const handleCurrentChange = (page) => {
  pagination.currentPage = page
  if (selectedUser.value && selectedUser.value.id) fetchUserHealthRecords(selectedUser.value.id)
}

onMounted(() => {
  fetchUsers()
})
</script>

<style scoped>
.examination-reports-container {
  padding: 20px;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
