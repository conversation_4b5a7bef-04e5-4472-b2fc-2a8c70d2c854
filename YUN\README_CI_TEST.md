# 测试体系、CI/CD、依赖安全最佳实践

## 1. 后端
- 所有业务/接口代码必须有 pytest 单元/集成测试，覆盖率>80%。
- 依赖锁定在 requirements.txt，定期 pip-audit。
- pytest.ini 配置覆盖率统计，CI 自动上传到 Codecov。

## 2. 前端
- 组件/工具函数用 Vitest 单元测试，页面/流程用 Cypress 端到端测试。
- 所有依赖锁定 package-lock.json，定期 npm audit。
- package.json scripts 统一测试、覆盖率、依赖安全命令。

## 3. CI/CD
- 推荐用 GitHub Actions（.github/workflows/ci.yml），自动化测试、构建、依赖安全扫描。
- 所有 PR 必须通过 CI 检查后合并。

## 4. 依赖安全
- 后端用 pip-audit，前端用 npm audit，CI 阶段强制执行。
- 推荐引入 Dependabot/Snyk 等自动依赖升级和安全提醒。

---
如需自动生成更多测试/CI模板或脚本，请联系维护者。 