#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库迁移脚本：为量表和问卷系统添加维度字段

此脚本将为以下表添加维度相关字段：
1. assessment_templates - 添加维度定义
2. questionnaire_templates - 添加维度定义
3. assessment_template_questions - 添加维度归属
4. questionnaire_template_questions - 添加维度归属
5. assessment_responses - 添加维度分值
6. questionnaire_responses - 添加维度分值
7. assessment_results - 添加维度分值
8. questionnaire_results - 添加维度分值
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
from app.db.base_session import get_db
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def add_dimension_fields():
    """添加维度相关字段"""
    
    # 创建数据库连接
    engine = create_engine(settings.DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        logger.info("开始添加维度相关字段...")
        
        # 1. 为 assessment_templates 表添加维度定义字段
        logger.info("为 assessment_templates 表添加维度字段...")
        db.execute(text("""
            ALTER TABLE assessment_templates 
            ADD COLUMN IF NOT EXISTS dimensions JSON COMMENT '维度定义，包含维度名称、描述、权重等信息'
        """))
        
        # 2. 为 questionnaire_templates 表添加维度定义字段
        logger.info("为 questionnaire_templates 表添加维度字段...")
        db.execute(text("""
            ALTER TABLE questionnaire_templates 
            ADD COLUMN IF NOT EXISTS dimensions JSON COMMENT '维度定义，包含维度名称、描述、权重等信息'
        """))
        
        # 3. 为 assessment_template_questions 表添加维度归属字段
        logger.info("为 assessment_template_questions 表添加维度字段...")
        db.execute(text("""
            ALTER TABLE assessment_template_questions 
            ADD COLUMN IF NOT EXISTS dimension_key VARCHAR(100) COMMENT '所属维度的键值'
        """))
        
        # 4. 为 questionnaire_template_questions 表添加维度归属字段
        logger.info("为 questionnaire_template_questions 表添加维度字段...")
        db.execute(text("""
            ALTER TABLE questionnaire_template_questions 
            ADD COLUMN IF NOT EXISTS dimension_key VARCHAR(100) COMMENT '所属维度的键值'
        """))
        
        # 5. 为 assessment_responses 表添加维度分值字段
        logger.info("为 assessment_responses 表添加维度字段...")
        db.execute(text("""
            ALTER TABLE assessment_responses 
            ADD COLUMN IF NOT EXISTS dimension_scores JSON COMMENT '各维度得分，JSON格式存储'
        """))
        
        # 6. 为 questionnaire_responses 表添加维度分值字段
        logger.info("为 questionnaire_responses 表添加维度字段...")
        db.execute(text("""
            ALTER TABLE questionnaire_responses 
            ADD COLUMN IF NOT EXISTS dimension_scores JSON COMMENT '各维度得分，JSON格式存储'
        """))
        
        # 7. 检查并为 assessment_results 表添加维度字段（如果表存在）
        logger.info("检查 assessment_results 表...")
        result = db.execute(text("""
            SELECT COUNT(*) as count FROM information_schema.tables 
            WHERE table_schema = DATABASE() AND table_name = 'assessment_results'
        """)).fetchone()
        
        if result and result[0] > 0:
            logger.info("为 assessment_results 表添加维度字段...")
            db.execute(text("""
                ALTER TABLE assessment_results 
                ADD COLUMN IF NOT EXISTS dimension_scores JSON COMMENT '各维度得分，JSON格式存储'
            """))
        else:
            logger.info("assessment_results 表不存在，跳过")
        
        # 8. 检查并为 questionnaire_results 表添加维度字段（如果表存在）
        logger.info("检查 questionnaire_results 表...")
        result = db.execute(text("""
            SELECT COUNT(*) as count FROM information_schema.tables 
            WHERE table_schema = DATABASE() AND table_name = 'questionnaire_results'
        """)).fetchone()
        
        if result and result[0] > 0:
            logger.info("为 questionnaire_results 表添加维度字段...")
            db.execute(text("""
                ALTER TABLE questionnaire_results 
                ADD COLUMN IF NOT EXISTS dimension_scores JSON COMMENT '各维度得分，JSON格式存储'
            """))
        else:
            logger.info("questionnaire_results 表不存在，跳过")
        
        # 9. 为 assessment_distributions 表添加维度字段（如果表存在）
        logger.info("检查 assessment_distributions 表...")
        result = db.execute(text("""
            SELECT COUNT(*) as count FROM information_schema.tables 
            WHERE table_schema = DATABASE() AND table_name = 'assessment_distributions'
        """)).fetchone()
        
        if result and result[0] > 0:
            logger.info("为 assessment_distributions 表添加维度字段...")
            db.execute(text("""
                ALTER TABLE assessment_distributions 
                ADD COLUMN IF NOT EXISTS dimension_requirements JSON COMMENT '维度要求，指定需要评估的维度'
            """))
        else:
            logger.info("assessment_distributions 表不存在，跳过")
        
        # 10. 为 questionnaire_distributions 表添加维度字段（如果表存在）
        logger.info("检查 questionnaire_distributions 表...")
        result = db.execute(text("""
            SELECT COUNT(*) as count FROM information_schema.tables 
            WHERE table_schema = DATABASE() AND table_name = 'questionnaire_distributions'
        """)).fetchone()
        
        if result and result[0] > 0:
            logger.info("为 questionnaire_distributions 表添加维度字段...")
            db.execute(text("""
                ALTER TABLE questionnaire_distributions 
                ADD COLUMN IF NOT EXISTS dimension_requirements JSON COMMENT '维度要求，指定需要评估的维度'
            """))
        else:
            logger.info("questionnaire_distributions 表不存在，跳过")
        
        # 提交更改
        db.commit()
        logger.info("维度字段添加完成！")
        
        return True
        
    except Exception as e:
        logger.error(f"添加维度字段时发生错误: {e}")
        db.rollback()
        return False
    finally:
        db.close()

def main():
    """主函数"""
    logger.info("开始数据库迁移：添加维度字段")
    
    success = add_dimension_fields()
    
    if success:
        logger.info("数据库迁移成功完成！")
        print("\n=== 迁移完成 ===")
        print("已为以下表添加维度相关字段：")
        print("1. assessment_templates - dimensions (JSON)")
        print("2. questionnaire_templates - dimensions (JSON)")
        print("3. assessment_template_questions - dimension_key (VARCHAR)")
        print("4. questionnaire_template_questions - dimension_key (VARCHAR)")
        print("5. assessment_responses - dimension_scores (JSON)")
        print("6. questionnaire_responses - dimension_scores (JSON)")
        print("7. assessment_results - dimension_scores (JSON) [如果表存在]")
        print("8. questionnaire_results - dimension_scores (JSON) [如果表存在]")
        print("9. assessment_distributions - dimension_requirements (JSON) [如果表存在]")
        print("10. questionnaire_distributions - dimension_requirements (JSON) [如果表存在]")
    else:
        logger.error("数据库迁移失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()