import sqlite3

conn = sqlite3.connect('YUN/backend/app.db')
cursor = conn.cursor()

print('=== Distribution Tables ===')
cursor.execute('SELECT name FROM sqlite_master WHERE type="table" AND name LIKE "%distribution%"')
tables = cursor.fetchall()
for table in tables:
    print(f'Table: {table[0]}')

print('\n=== Assessment Distributions ===')
cursor.execute('SELECT COUNT(*) FROM assessment_distributions')
count = cursor.fetchone()[0]
print(f'AssessmentDistribution records: {count}')

print('\n=== Questionnaire Distributions ===')
try:
    cursor.execute('SELECT COUNT(*) FROM questionnaire_distributions')
    count = cursor.fetchone()[0]
    print(f'QuestionnaireDistribution records: {count}')
except Exception as e:
    print(f'Error accessing questionnaire_distributions: {e}')

conn.close()