<template>
  <div class="dashboard-tab">
    <div class="tab-header">
      <h3>健康数据仪表盘</h3>
      <div class="filter-container">
        <el-select v-model="timeRange" placeholder="时间范围" @change="refreshData">
          <el-option label="最近3个月" value="3months" />
          <el-option label="最近6个月" value="6months" />
          <el-option label="最近1年" value="1year" />
          <el-option label="全部" value="all" />
        </el-select>
        <el-button type="primary" size="small" @click="refreshData">刷新数据</el-button>
      </div>
    </div>

    <el-row :gutter="20" class="dashboard-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-value">{{ stats.totalRecords }}</div>
          <div class="stat-label">健康记录总数</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-value">{{ stats.abnormalCount }}</div>
          <div class="stat-label">异常记录数</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-value">{{ stats.lastCheckupDays }}</div>
          <div class="stat-label">距上次体检天数</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-value">{{ stats.completedQuestionnaires }}</div>
          <div class="stat-label">已完成问卷数</div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="dashboard-row">
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>体重变化趋势</span>
            </div>
          </template>
          <div class="chart-container" ref="weightChartContainer"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>血压变化趋势</span>
            </div>
          </template>
          <div class="chart-container" ref="bpChartContainer"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="dashboard-row">
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>检查记录分布</span>
            </div>
          </template>
          <div class="chart-container" ref="examPieContainer"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>健康指标对比</span>
            </div>
          </template>
          <div class="chart-container" ref="radarContainer"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="dashboard-row">
      <el-col :span="24">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>健康记录时间线</span>
            </div>
          </template>
          <el-timeline>
            <el-timeline-item
              v-for="(activity, index) in timelineData"
              :key="index"
              :timestamp="activity.date"
              :type="activity.type"
              :color="getTimelineColor(activity.category)"
            >
              {{ activity.content }}
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import axios from 'axios';
import { getMockData, isMockEnabled } from '../../mocks/mockDataManager';
import * as echarts from 'echarts/core';
import { LineChart, BarChart, PieChart, RadarChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';

// 注册必要的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  LineChart,
  BarChart,
  PieChart,
  RadarChart,
  CanvasRenderer
]);

// 接收用户ID作为属性
const props = defineProps({
  customId: {
    type: [Number, String],
    required: true
  }
});

// 状态变量
const loading = ref(false);
const timeRange = ref('6months');
const stats = reactive({
  totalRecords: 0,
  abnormalCount: 0,
  lastCheckupDays: 0,
  completedQuestionnaires: 0
});
const timelineData = ref([]);

// 图表容器引用
const weightChartContainer = ref(null);
const bpChartContainer = ref(null);
const examPieContainer = ref(null);
const radarContainer = ref(null);

// 图表实例
let weightChart = null;
let bpChart = null;
let examPieChart = null;
let radarChart = null;



// 初始化
onMounted(() => {
  if (props.customId) {
    fetchDashboardData();
  }
  
  window.addEventListener('resize', handleResize);
});

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  weightChart?.dispose();
  bpChart?.dispose();
  examPieChart?.dispose();
  radarChart?.dispose();
});

// 获取仪表盘数据
const fetchDashboardData = async () => {
  if (!props.customId) return;
  
  loading.value = true;
  
  try {
    // 使用聚合API获取所有健康数据
    const aggregatedResponse = await axios.get(`/api/v1/aggregated/health-profile/${props.customId}`, {
      params: { 
        timeRange: timeRange.value,
        include_dashboard: true,
        include_timeline: true,
        include_statistics: true
      }
    });
    
    const profileData = aggregatedResponse.data.profile_data || {};
    
    // 从聚合数据中提取各部分数据
    const statsData = profileData.statistics || {};
    const weightData = profileData.weight_trend || [];
    const bpData = profileData.bp_trend || [];
    const examDistData = profileData.exam_distribution || [];
    const healthIndexData = profileData.health_index || {};
    const timelineData = profileData.timeline || [];
    
    // 处理响应数据
    if (statsData) {
      Object.assign(stats, statsData);
    }
    
    if (timelineData) {
      timelineData.value = timelineData;
    }
    
    // 初始化图表
    initWeightChart(weightData || generateMockWeightData());
    initBPChart(bpData || generateMockBPData());
    initExamPieChart(examDistData || generateMockExamDistData());
    initRadarChart(healthIndexData || generateMockHealthIndexData());
    
  } catch (error) {
    console.error('获取仪表盘数据失败:', error);
    ElMessage.error('获取仪表盘数据失败，请稍后重试');
    
    // 如果启用了模拟数据，使用模拟数据
    if (isMockEnabled()) {
      Object.assign(stats, generateMockStats());
      timelineData.value = generateMockTimelineData();
      
      // 初始化图表（使用模拟数据）
      initWeightChart(generateMockWeightData());
      initBPChart(generateMockBPData());
      initExamPieChart(generateMockExamDistData());
      initRadarChart(generateMockHealthIndexData());
    }
  } finally {
    loading.value = false;
  }
};

// 监听用户ID变化
watch(() => props.customId, (newVal) => {
  if (newVal) {
    fetchDashboardData();
  }
}, { immediate: true });

// 监听窗口大小变化
const handleResize = () => {
  weightChart?.resize();
  bpChart?.resize();
  examPieChart?.resize();
  radarChart?.resize();
};

// 刷新数据
const refreshData = () => {
  fetchDashboardData();
};

// 初始化体重变化趋势图
const initWeightChart = (data) => {
  if (!weightChartContainer.value) return;
  
  if (weightChart) {
    weightChart.dispose();
  }
  
  weightChart = echarts.init(weightChartContainer.value);
  
  // 确保数据格式正确
  const safeData = {
    dates: Array.isArray(data?.dates) ? data.dates : [],
    weights: Array.isArray(data?.weights) ? data.weights : []
  };
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: safeData.dates,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '体重 (kg)',
      min: function(value) {
        return value.min > 0 ? Math.floor(value.min - 2) : 0;
      }
    },
    series: [
      {
        name: '体重',
        type: 'line',
        data: safeData.weights,
        markPoint: {
          data: [
            { type: 'max', name: '最大值' },
            { type: 'min', name: '最小值' }
          ]
        },
        markLine: {
          data: [
            { type: 'average', name: '平均值' }
          ]
        },
        lineStyle: {
          width: 3,
          color: '#409EFF'
        },
        itemStyle: {
          color: '#409EFF'
        },
        smooth: true
      }
    ]
  };
  
  try {
    weightChart.setOption(option);
  } catch (error) {
    console.error('体重图表初始化失败:', error);
  }
};

// 初始化血压变化趋势图
const initBPChart = (data) => {
  if (!bpChartContainer.value) return;
  
  if (bpChart) {
    bpChart.dispose();
  }
  
  bpChart = echarts.init(bpChartContainer.value);
  
  // 确保数据格式正确
  const safeData = {
    dates: Array.isArray(data?.dates) ? data.dates : [],
    systolic: Array.isArray(data?.systolic) ? data.systolic : [],
    diastolic: Array.isArray(data?.diastolic) ? data.diastolic : []
  };
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['收缩压', '舒张压']
    },
    xAxis: {
      type: 'category',
      data: safeData.dates,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '血压 (mmHg)'
    },
    series: [
      {
        name: '收缩压',
        type: 'line',
        data: safeData.systolic,
        lineStyle: {
          width: 3,
          color: '#F56C6C'
        },
        itemStyle: {
          color: '#F56C6C'
        },
        smooth: true
      },
      {
        name: '舒张压',
        type: 'line',
        data: safeData.diastolic,
        lineStyle: {
          width: 3,
          color: '#67C23A'
        },
        itemStyle: {
          color: '#67C23A'
        },
        smooth: true
      }
    ]
  };
  
  try {
    bpChart.setOption(option);
  } catch (error) {
    console.error('血压图表初始化失败:', error);
  }
};

// 初始化检查记录分布图
const initExamPieChart = (data) => {
  if (!examPieContainer.value) return;
  
  if (examPieChart) {
    examPieChart.dispose();
  }
  
  examPieChart = echarts.init(examPieContainer.value);
  
  // 确保数据格式正确
  const safeData = Array.isArray(data) ? data : [];
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 10,
      data: safeData.map(item => item?.name || '').filter(name => name)
    },
    series: [
      {
        name: '检查记录',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: safeData
      }
    ]
  };
  
  try {
    examPieChart.setOption(option);
  } catch (error) {
    console.error('饼图初始化失败:', error);
  }
};

// 初始化健康指标雷达图
const initRadarChart = (data) => {
  if (!radarContainer.value) return;
  
  if (radarChart) {
    radarChart.dispose();
  }
  
  radarChart = echarts.init(radarContainer.value);
  
  // 数据验证和清理
  let safeData = {
    indicators: [],
    currentValues: [],
    referenceValues: []
  };
  
  try {
    // 验证并处理indicators
    if (data && Array.isArray(data.indicators)) {
      safeData.indicators = data.indicators.filter(item => 
        item && typeof item === 'object' && 
        typeof item.name === 'string' && 
        typeof item.max === 'number'
      );
    }
    
    // 验证并处理currentValues
    if (data && Array.isArray(data.currentValues)) {
      safeData.currentValues = data.currentValues.filter(val => 
        typeof val === 'number' && !isNaN(val)
      );
    }
    
    // 验证并处理referenceValues
    if (data && Array.isArray(data.referenceValues)) {
      safeData.referenceValues = data.referenceValues.filter(val => 
        typeof val === 'number' && !isNaN(val)
      );
    }
  } catch (error) {
    console.error('雷达图数据验证失败:', error);
    safeData = {
      indicators: [],
      currentValues: [],
      referenceValues: []
    };
  }
  
  // 如果没有有效数据，使用默认数据
  if (safeData.indicators.length === 0) {
    safeData.indicators = [
      { name: 'BMI', max: 30 },
      { name: '血压', max: 180 },
      { name: '血糖', max: 10 },
      { name: '胆固醇', max: 7 },
      { name: '心率', max: 120 },
      { name: '体脂率', max: 40 }
    ];
    safeData.currentValues = [0, 0, 0, 0, 0, 0];
    safeData.referenceValues = [0, 0, 0, 0, 0, 0];
  }
  
  // 确保数组长度一致
  const indicatorCount = safeData.indicators.length;
  if (safeData.currentValues.length !== indicatorCount) {
    safeData.currentValues = Array(indicatorCount).fill(0);
  }
  if (safeData.referenceValues.length !== indicatorCount) {
    safeData.referenceValues = Array(indicatorCount).fill(0);
  }
  
  const option = {
    tooltip: {},
    legend: {
      data: ['当前值', '参考值']
    },
    radar: {
      indicator: safeData.indicators
    },
    series: [
      {
        name: '健康指标',
        type: 'radar',
        data: [
          {
            value: safeData.currentValues,
            name: '当前值',
            areaStyle: {
              color: 'rgba(64, 158, 255, 0.6)'
            },
            lineStyle: {
              color: '#409EFF'
            },
            itemStyle: {
              color: '#409EFF'
            }
          },
          {
            value: safeData.referenceValues,
            name: '参考值',
            areaStyle: {
              color: 'rgba(103, 194, 58, 0.6)'
            },
            lineStyle: {
              color: '#67C23A'
            },
            itemStyle: {
              color: '#67C23A'
            }
          }
        ]
      }
    ]
  };
  
  try {
    radarChart.setOption(option);
  } catch (error) {
    console.error('雷达图初始化失败:', error);
  }
};

// 获取时间线项目颜色
const getTimelineColor = (category) => {
  const colorMap = {
    'checkup': '#409EFF',
    'medical': '#F56C6C',
    'lab': '#E6A23C',
    'examination': '#67C23A',
    'medication': '#909399',
    'questionnaire': '#9966FF'
  };
  
  return colorMap[category] || '#409EFF';
};

// 生成模拟统计数据
const generateMockStats = () => {
  return {
    totalRecords: 28,
    abnormalCount: 5,
    lastCheckupDays: 45,
    completedQuestionnaires: 3
  };
};

// 生成模拟体重数据
const generateMockWeightData = () => {
  const dates = [];
  const weights = [];
  
  const today = new Date();
  for (let i = 0; i < 6; i++) {
    const date = new Date();
    date.setMonth(today.getMonth() - i);
    dates.unshift(date.toLocaleDateString());
    
    // 生成70-75之间的随机体重
    const weight = (70 + Math.random() * 5).toFixed(1);
    weights.unshift(parseFloat(weight));
  }
  
  return { dates, weights };
};

// 生成模拟血压数据
const generateMockBPData = () => {
  const dates = [];
  const systolic = [];
  const diastolic = [];
  
  const today = new Date();
  for (let i = 0; i < 6; i++) {
    const date = new Date();
    date.setMonth(today.getMonth() - i);
    dates.unshift(date.toLocaleDateString());
    
    // 生成120-140之间的随机收缩压
    const sys = Math.floor(120 + Math.random() * 20);
    systolic.unshift(sys);
    
    // 生成70-90之间的随机舒张压
    const dia = Math.floor(70 + Math.random() * 20);
    diastolic.unshift(dia);
  }
  
  return { dates, systolic, diastolic };
};

// 生成模拟检查记录分布数据
const generateMockExamDistData = () => {
  return [
    { value: 10, name: '实验室检验' },
    { value: 8, name: '影像学检查' },
    { value: 5, name: '心电图检查' },
    { value: 3, name: '超声检查' },
    { value: 2, name: '其他检查' }
  ];
}

// 生成模拟健康指标数据
const generateMockHealthIndexData = () => {
  return {
    indicators: [
      { name: 'BMI', max: 30 },
      { name: '血压', max: 180 },
      { name: '血糖', max: 10 },
      { name: '胆固醇', max: 7 },
      { name: '心率', max: 120 },
      { name: '体脂率', max: 40 }
    ],
    currentValues: [23.5, 130, 5.2, 4.8, 75, 22],
    referenceValues: [22, 120, 5.0, 4.5, 70, 20]
  };
};

// 生成模拟时间线数据
const generateMockTimelineData = () => {
  return [
    {
      date: '2025-04-15',
      content: '年度体检，各项指标正常',
      category: 'checkup',
      type: 'primary'
    },
    {
      date: '2025-03-20',
      content: '完成健康生活方式调查问卷',
      category: 'questionnaire',
      type: 'info'
    },
    {
      date: '2025-02-10',
      content: '血脂检查，总胆固醇偏高',
      category: 'lab',
      type: 'warning'
    },
    {
      date: '2025-01-05',
      content: '感冒就诊，开具抗生素处方',
      category: 'medical',
      type: 'danger'
    },
    {
      date: '2024-12-20',
      content: '胸部X光检查，未见异常',
      category: 'examination',
      type: 'success'
    }
  ];
};
</script>

<style scoped>
.dashboard-tab {
  padding: 10px;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.tab-header h3 {
  margin: 0;
}

.filter-container {
  display: flex;
  gap: 10px;
}

.dashboard-row {
  margin-bottom: 20px;
}

.stat-card {
  height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.stat-value {
  font-size: 36px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 10px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.chart-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 300px;
  width: 100%;
}

.el-timeline {
  padding: 10px;
  max-height: 400px;
  overflow-y: auto;
}
</style>
