/* Element UI 兼容性和可访问性修复 */

/* 修复 -webkit-appearance 兼容性问题 */
.el-button,
.el-button:focus,
.el-button:hover {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
}

.el-input__inner,
.el-input__inner:focus,
.el-input__inner:hover {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
}

.el-select .el-input__inner,
.el-select .el-input__inner:focus,
.el-select .el-input__inner:hover {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
}

.el-textarea__inner,
.el-textarea__inner:focus,
.el-textarea__inner:hover {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
}

.el-date-editor .el-input__inner,
.el-date-editor .el-input__inner:focus,
.el-date-editor .el-input__inner:hover {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
}

/* 修复 Element UI 内部按钮的 webkit-appearance */
.el-button--primary,
.el-button--success,
.el-button--warning,
.el-button--danger,
.el-button--info,
.el-button--text {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
}

/* 修复分页组件按钮 */
.el-pagination button,
.el-pagination .el-pager li {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
}

/* 修复上传组件按钮 */
.el-upload .el-button {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
}

/* 修复对话框按钮 */
.el-dialog__footer .el-button {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
}

/* 修复表格操作按钮 */
.el-table .el-button {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
}

/* 修复 ARIA 属性问题 - 确保选择框样式正确 */
.el-select .el-input__inner[aria-activedescendant=""] {
  /* 确保样式正确，移除空的ARIA属性将通过JavaScript处理 */
  border-color: #dcdfe6;
  background-color: #fff;
}

/* 提升可访问性 - 增强焦点样式 */
.el-button:focus {
  outline: 2px solid #409eff !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
}

.el-input:focus-within {
  outline: 2px solid #409eff !important;
  outline-offset: 1px !important;
}

.el-select:focus-within {
  outline: 2px solid #409eff !important;
  outline-offset: 1px !important;
}

.el-textarea:focus-within {
  outline: 2px solid #409eff !important;
  outline-offset: 1px !important;
}

.el-date-editor:focus-within {
  outline: 2px solid #409eff !important;
  outline-offset: 1px !important;
}

/* 确保所有表单控件都有合适的焦点样式 */
.el-form-item__content > *:focus {
  outline: 2px solid #409eff !important;
  outline-offset: 1px !important;
}

/* 修复按钮在不同状态下的 appearance */
.el-button:active,
.el-button.is-active,
.el-button.is-disabled {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
}

/* 修复输入框在不同状态下的 appearance */
.el-input__inner:active,
.el-input__inner.is-active,
.el-input__inner:disabled {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
}

/* 修复选择框下拉箭头 */
.el-select .el-input .el-select__caret {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
}

/* 修复日期选择器图标 */
.el-date-editor .el-input__prefix,
.el-date-editor .el-input__suffix {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
}

/* 确保所有可交互元素都有正确的cursor */
.el-button,
.el-select,
.el-date-editor,
.el-upload .el-button {
  cursor: pointer !important;
}

.el-input__inner,
.el-textarea__inner {
  cursor: text !important;
}

/* 修复可能的其他webkit-appearance问题 */
input[type="text"],
input[type="password"],
input[type="email"],
input[type="number"],
input[type="tel"],
input[type="url"],
input[type="search"],
input[type="date"],
input[type="time"],
input[type="datetime-local"],
select,
textarea,
button {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
}

/* 特别处理可能遗漏的元素 */
[class*="el-"] button,
[class*="el-"] input,
[class*="el-"] select,
[class*="el-"] textarea {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
}