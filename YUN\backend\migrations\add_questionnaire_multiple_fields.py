#!/usr/bin/env python3
"""
为问卷表添加多次评估支持字段的数据库迁移脚本
"""

import sqlite3
import os
from datetime import datetime

def add_questionnaire_multiple_fields():
    """
    为questionnaires表添加支持多次评估的字段
    """
    # 可能的数据库路径
    db_paths = [
        '/www/wwwroot/healthapp/app.db',
        '/www/wwwroot/healthapp/backend/app.db',
        '/www/wwwroot/healthapp/data/health_app.db',
        '/www/wwwroot/healthapp/backend/health_app.db',
        '/www/wwwroot/healthapp/backend/healthapp.db'
    ]
    
    db_path = None
    for path in db_paths:
        if os.path.exists(path):
            db_path = path
            break
    
    if not db_path:
        print("错误：找不到数据库文件")
        return False
    
    print(f"找到数据库文件: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查questionnaires表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='questionnaires';")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("错误：questionnaires表不存在")
            return False
        
        # 检查现有字段
        cursor.execute("PRAGMA table_info(questionnaires);")
        columns = [column[1] for column in cursor.fetchall()]
        print(f"现有字段: {columns}")
        
        # 添加新字段（如果不存在）
        fields_added = []
        
        if 'round_number' not in columns:
            cursor.execute('ALTER TABLE questionnaires ADD COLUMN round_number INTEGER DEFAULT 1;')
            fields_added.append('round_number')
            print('✓ 添加了 round_number 字段')
        else:
            print('- round_number 字段已存在')
        
        if 'sequence_number' not in columns:
            cursor.execute('ALTER TABLE questionnaires ADD COLUMN sequence_number INTEGER DEFAULT 1;')
            fields_added.append('sequence_number')
            print('✓ 添加了 sequence_number 字段')
        else:
            print('- sequence_number 字段已存在')
        
        if 'unique_identifier' not in columns:
            cursor.execute('ALTER TABLE questionnaires ADD COLUMN unique_identifier TEXT;')
            fields_added.append('unique_identifier')
            print('✓ 添加了 unique_identifier 字段')
        else:
            print('- unique_identifier 字段已存在')
        
        if 'template_id' not in columns:
            cursor.execute('ALTER TABLE questionnaires ADD COLUMN template_id INTEGER;')
            fields_added.append('template_id')
            print('✓ 添加了 template_id 字段')
        else:
            print('- template_id 字段已存在')
        
        if 'completed_at' not in columns:
            cursor.execute('ALTER TABLE questionnaires ADD COLUMN completed_at DATETIME;')
            fields_added.append('completed_at')
            print('✓ 添加了 completed_at 字段')
        else:
            print('- completed_at 字段已存在')
        
        # 如果添加了新字段，更新现有记录的unique_identifier
        if 'unique_identifier' in fields_added:
            cursor.execute("""
                UPDATE questionnaires 
                SET unique_identifier = 
                    CASE 
                        WHEN template_id IS NOT NULL THEN 
                            template_id || '_' || custom_id || '_' || 
                            COALESCE(round_number, 1) || '_' || 
                            COALESCE(sequence_number, 1)
                        ELSE 
                            'questionnaire_' || id || '_' || custom_id || '_' || 
                            COALESCE(round_number, 1) || '_' || 
                            COALESCE(sequence_number, 1)
                    END
                WHERE unique_identifier IS NULL;
            """)
            print('✓ 更新了现有记录的unique_identifier')
        
        # 提交更改
        conn.commit()
        
        if fields_added:
            print(f"\n成功添加字段: {', '.join(fields_added)}")
        else:
            print("\n所有字段都已存在，无需添加")
        
        # 验证更改
        cursor.execute("PRAGMA table_info(questionnaires);")
        updated_columns = [column[1] for column in cursor.fetchall()]
        print(f"\n更新后的字段: {updated_columns}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"迁移失败: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

if __name__ == "__main__":
    print("开始问卷数据库迁移...")
    success = add_questionnaire_multiple_fields()
    if success:
        print("\n问卷数据库迁移完成！")
    else:
        print("\n问卷数据库迁移失败！")