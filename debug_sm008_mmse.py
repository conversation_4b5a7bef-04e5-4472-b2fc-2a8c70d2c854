#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import json
from datetime import datetime

def check_sm008_mmse_data():
    """检查SM_008用户的简易精神状态检查量表数据"""
    print("=== 检查SM_008用户的简易精神状态检查量表数据 ===")
    
    db_path = "c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 查看所有表
        print("\n1. 查看数据库中的所有表:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        for table in tables:
            print(f"  {table[0]}")
        
        # 2. 查看assessment_templates表结构
        print("\n2. 查看assessment_templates表结构:")
        cursor.execute("PRAGMA table_info(assessment_templates)")
        columns = cursor.fetchall()
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
        
        # 3. 查看assessment_responses表结构
        print("\n3. 查看assessment_responses表结构:")
        cursor.execute("PRAGMA table_info(assessment_responses)")
        columns = cursor.fetchall()
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
        
        # 4. 查看assessment_results表结构
        print("\n4. 查看assessment_results表结构:")
        cursor.execute("PRAGMA table_info(assessment_results)")
        columns = cursor.fetchall()
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
        
        # 5. 检查用户信息
        print("\n5. 检查用户信息:")
        cursor.execute("SELECT id, custom_id, username FROM users WHERE custom_id = 'SM_008'")
        user = cursor.fetchone()
        if user:
            print(f"用户ID: {user[0]}, Custom ID: {user[1]}, 用户名: {user[2]}")
            user_id = user[0]
        else:
            print("未找到SM_008用户")
            return
        
        # 6. 查找简易精神状态检查量表模板
        print("\n6. 查找简易精神状态检查量表模板:")
        cursor.execute("SELECT * FROM assessment_templates WHERE name LIKE '%简易精神状态%' OR name LIKE '%MMSE%'")
        templates = cursor.fetchall()
        
        if not templates:
            print("未找到简易精神状态检查量表模板，查看所有模板:")
            cursor.execute("SELECT id, name FROM assessment_templates LIMIT 10")
            all_templates = cursor.fetchall()
            for t in all_templates:
                print(f"  {t[0]}: {t[1]}")
        else:
            for template in templates:
                print(f"找到模板: {template}")
                template_id = template[0]
        
        # 7. 查找对应的评估
        print("\n7. 查找对应的评估:")
        cursor.execute("SELECT * FROM assessments WHERE name LIKE '%简易精神状态%' OR name LIKE '%MMSE%'")
        assessments = cursor.fetchall()
        
        assessment_id = None
        for assessment in assessments:
            print(f"找到评估: {assessment}")
            assessment_id = assessment[0]
        
        if not assessment_id:
            print("未找到对应的评估")
            return
        
        # 8. 查找SM_008的回答记录
        print("\n8. 查找SM_008的回答记录:")
        cursor.execute("SELECT * FROM assessment_responses WHERE custom_id = 'SM_008' AND assessment_id = ?", (assessment_id,))
        responses = cursor.fetchall()
        
        for response in responses:
            print(f"\n回答记录: {response}")
            
            # 解析answers字段
            answers_field = None
            for i, col in enumerate(['id', 'custom_id', 'assessment_id', 'answers', 'score', 'status', 'created_at', 'updated_at']):
                if i < len(response) and col == 'answers':
                    answers_field = response[i]
                    break
            
            if answers_field:
                try:
                    answers = json.loads(answers_field)
                    print(f"解析后的回答数据 ({len(answers)} 个回答):")
                    for j, answer in enumerate(answers[:5]):
                        print(f"  回答{j+1}: {answer}")
                    if len(answers) > 5:
                        print(f"  ... 还有{len(answers)-5}个回答")
                except json.JSONDecodeError as e:
                    print(f"回答数据解析失败: {e}")
                    print(f"原始数据: {answers_field[:200]}...")
        
        # 9. 查找SM_008的结果记录
        print("\n9. 查找SM_008的结果记录:")
        cursor.execute("SELECT * FROM assessment_results WHERE custom_id = 'SM_008' AND assessment_id = ?", (assessment_id,))
        results = cursor.fetchall()
        
        for result in results:
            print(f"\n结果记录: {result}")
        
        conn.close()
        
    except Exception as e:
        print(f"检查过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_sm008_mmse_data()