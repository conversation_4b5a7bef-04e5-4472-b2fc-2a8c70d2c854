import sqlite3

def add_questionnaire_type_column():
    try:
        conn = sqlite3.connect('app.db')
        cursor = conn.cursor()
        
        # 检查列是否已存在
        cursor.execute('PRAGMA table_info(questionnaires)')
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        if 'questionnaire_type' not in column_names:
            # 添加questionnaire_type列
            cursor.execute('ALTER TABLE questionnaires ADD COLUMN questionnaire_type VARCHAR(50)')
            conn.commit()
            print('成功添加questionnaire_type列')
        else:
            print('questionnaire_type列已存在')
        
        # 验证列是否添加成功
        cursor.execute('PRAGMA table_info(questionnaires)')
        columns = cursor.fetchall()
        print('\n更新后的questionnaires表结构:')
        for col in columns:
            print(f'  {col[1]} ({col[2]})')
        
        conn.close()
        
    except Exception as e:
        print(f'添加列时出错: {e}')

if __name__ == '__main__':
    add_questionnaire_type_column()