from kivy.uix.screenmanager import Screen
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.scrollview import ScrollView
from kivy.uix.label import Label
from kivy.uix.button import Button
from kivy.metrics import dp
from kivy.clock import Clock
from kivy.app import App
from kivy.properties import StringProperty, ListProperty, ObjectProperty
from kivy.uix.popup import Popup
from kivy.uix.image import Image
from kivy.uix.behaviors import ButtonBehavior
from kivy.core.window import Window

from kivymd.uix.list import MDList, MDListItem, MDListItemLeadingIcon, MDListItemHeadlineText, MDListItemSupportingText
from kivymd.uix.button import MDButton, MDButtonText
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.scrollview import MDScrollView
from kivy.uix.progressbar import ProgressBar
from kivymd.uix.divider import MDDivider

import os
import logging
from datetime import datetime
from functools import partial

from theme import AppTheme
from api.api_client import APIClient

# 配置日志
logger = logging.getLogger(__name__)

# 定义屏幕布局
document_list_screen_kv = """
<DocumentListScreen>:
    BoxLayout:
        orientation: 'vertical'
        padding: dp(10)
        spacing: dp(10)

        # 标题栏
        BoxLayout:
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(50)

            Button:
                text: "返回"
                size_hint_x: None
                width: dp(80)
                background_color: app.theme.PRIMARY_COLOR
                color: app.theme.TEXT_LIGHT
                on_release: root.go_back()

            Label:
                text: "文档列表"
                font_size: dp(20)
                color: app.theme.TEXT_PRIMARY

            Button:
                text: "刷新"
                size_hint_x: None
                width: dp(80)
                background_color: app.theme.PRIMARY_COLOR
                color: app.theme.TEXT_LIGHT
                on_release: root.load_documents()

        # 文档列表
        BoxLayout:
            orientation: 'vertical'

            # 文档列表滚动视图
            MDScrollView:
                do_scroll_x: False

                MDList:
                    id: document_list
                    padding: dp(5)
                    spacing: dp(5)
"""

from kivy.lang import Builder
Builder.load_string(document_list_screen_kv)

class DocumentListScreen(Screen):
    """文档列表屏幕"""

    documents = ListProperty([])

    def __init__(self, **kwargs):
        super(DocumentListScreen, self).__init__(**kwargs)
        self.theme = AppTheme()
        self.api_client = APIClient()

    def on_enter(self):
        """每次进入屏幕时检查用户登录状态并刷新数据"""
        # 检查用户是否已登录
        from utils.user_manager import get_user_manager
        user_manager = get_user_manager()
        current_user = user_manager.get_current_user()
        # 兼容：如果app.user_data有custom_id也视为已登录
        if (current_user and current_user.get('custom_id')) or (hasattr(App.get_running_app(), 'user_data') and App.get_running_app().user_data.get('custom_id')):
            # 用户已登录，加载文档列表
            logger.info(f"用户已登录, 加载文档列表")
            Clock.schedule_once(self.load_documents, 0.5)
        else:
            # 用户未登录，显示提示信息
            logger.warning("用户未登录，无法加载文档列表")
            self.ids.document_list.clear_widgets()
            empty_item = MDListItem()
            empty_item.add_widget(MDListItemHeadlineText(
                text="请先登录后查看文档",
                theme_text_color="Secondary"
            ))
            self.ids.document_list.add_widget(empty_item)

    def go_back(self):
        """返回上一个屏幕"""
        self.manager.current = 'homepage_screen'

    def load_documents(self, *args):
        """加载文档列表"""
        try:
            # 在后台线程中加载数据
            Clock.schedule_once(self._fetch_documents, 0.1)
        except Exception as e:
            logger.error(f"加载文档列表时出错: {str(e)}")
            self._show_error_popup(f"加载文档列表失败: {str(e)}")

    def _fetch_documents(self, *args):
        """从API获取文档列表"""
        try:
            # 获取当前用户ID
            from utils.user_manager import get_user_manager
            user_manager = get_user_manager()
            current_user = user_manager.get_current_user()
            
            if not current_user or not current_user.custom_id:
                self.show_error("无法获取用户信息")
                return

            # 调用API获取文档列表
            result = self.api_client.get_documents(page=1, page_size=50, custom_id=current_user.custom_id)

            if result and (result.get('success') or result.get('documents')):
                # 更新文档列表
                self.documents = result.get('documents', result)
                self._update_document_list()
            else:
                error_msg = result.get('message', '获取文档列表失败') if result else '获取文档列表失败'
                logger.error(error_msg)
                self._show_error_popup(error_msg)
        except Exception as e:
            logger.error(f"获取文档列表时出错: {str(e)}")
            self._show_error_popup(f"获取文档列表失败: {str(e)}")

    def _update_document_list(self):
        """更新文档列表显示（手动 add_widget 方式，兼容 KivyMD 2.x）"""
        doc_list = self.ids.document_list
        doc_list.clear_widgets()  # 先清空

        if not self.documents:
            # 如果没有文档，显示提示信息
            empty_item = MDListItem()
            empty_item.add_widget(MDListItemHeadlineText(text="暂无文档", theme_text_color="Secondary"))
            doc_list.add_widget(empty_item)
            self.load_progress = 100
            self.is_refreshing = False
            return

        for doc in self.documents:
            icon_name = self._get_document_icon(doc.get('file_type', ''), doc.get('mime_type', ''))
            file_size = self._format_file_size(doc.get('file_size', 0))
            created_at = self._format_date(doc.get('created_at', ''))
            item = MDListItem()
            # 可选：添加左侧图标
            # item.add_widget(MDListItemLeadingIcon(icon=icon_name))
            item.add_widget(MDListItemHeadlineText(text=doc.get('filename', '未知文件')))
            item.add_widget(MDListItemSupportingText(text=f"{file_size} | {created_at}"))
            item.bind(on_release=partial(self._show_document_details, doc))
            doc_list.add_widget(item)

        self.load_progress = 100
        self.is_refreshing = False
        self._cleanup_recycled_items()

    def _track_visible_item(self, instance, parent):
        """跟踪可见的列表项"""
        if parent is not None:
            self._visible_items.add(instance.doc_data['id'])
        else:
            # 列表项被回收时清理资源
            doc_id = instance.doc_data['id']
            if doc_id in self._visible_items:
                self._visible_items.remove(doc_id)
                # 这里可以添加资源清理逻辑

    def _load_next_batch(self, *args):
        """加载下一批文档"""
        if self.loading:
            return
            
        self.loading = True
        
        start = self.current_batch * self.batch_size
        end = start + self.batch_size
        batch = self._document_data[start:end]
        
        if not batch:
            self.loading = False
            return
            
        # 添加新批次文档
        self._loaded_documents.extend(batch)
        self.ids.document_list.data = self._loaded_documents
        
        # 更新批次计数器
        self.current_batch += 1
        self.loading = False
        
        # 如果还有更多文档，安排下一次加载
        if end < len(self._document_data):
            Clock.schedule_once(self._load_next_batch, 0.1)  # 100ms后加载下一批

    def _get_document_icon(self, file_type, mime_type):
        """根据文件类型获取图标名称"""
        if 'pdf' in file_type.lower() or 'pdf' in mime_type.lower():
            return 'file-pdf-box'
        elif 'image' in file_type.lower() or 'image' in mime_type.lower():
            return 'file-image'
        elif 'word' in file_type.lower() or 'doc' in mime_type.lower():
            return 'file-word'
        elif 'excel' in file_type.lower() or 'sheet' in mime_type.lower():
            return 'file-excel'
        else:
            return 'file-document'

    def _format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"

    def _format_date(self, date_str):
        """格式化日期"""
        try:
            # 尝试解析ISO格式日期
            date_obj = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
            return date_obj.strftime("%Y-%m-%d %H:%M")
        except:
            # 如果解析失败，直接返回原始字符串
            return date_str

    def _show_document_details(self, doc, instance=None):
        """显示文档详情"""
        # 创建详情弹窗
        content = BoxLayout(orientation='vertical', padding=dp(10), spacing=dp(10))

        # 添加文档信息
        content.add_widget(Label(
            text=f"文件名: {doc.get('filename', '未知')}",
            size_hint_y=None,
            height=dp(30),
            halign='left',
            text_size=(Window.width - dp(40), None)
        ))

        content.add_widget(Label(
            text=f"文件类型: {doc.get('file_type', '未知')}",
            size_hint_y=None,
            height=dp(30),
            halign='left',
            text_size=(Window.width - dp(40), None)
        ))

        content.add_widget(Label(
            text=f"文件大小: {self._format_file_size(doc.get('file_size', 0))}",
            size_hint_y=None,
            height=dp(30),
            halign='left',
            text_size=(Window.width - dp(40), None)
        ))

        content.add_widget(Label(
            text=f"上传时间: {self._format_date(doc.get('created_at', ''))}",
            size_hint_y=None,
            height=dp(30),
            halign='left',
            text_size=(Window.width - dp(40), None)
        ))

        # 添加OCR状态信息
        ocr_status = doc.get('ocr_status', 'unknown')
        ocr_text = "OCR状态: "
        if ocr_status == 'completed':
            ocr_text += "已完成"
        elif ocr_status == 'processing':
            ocr_text += "处理中"
        elif ocr_status == 'failed':
            ocr_text += "失败"
        else:
            ocr_text += "未处理"

        content.add_widget(Label(
            text=ocr_text,
            size_hint_y=None,
            height=dp(30),
            halign='left',
            text_size=(Window.width - dp(40), None)
        ))

        # 添加按钮
        buttons = BoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height=dp(50),
            spacing=dp(10)
        )

        # 关闭按钮
        close_btn = Button(
            text="关闭",
            size_hint_x=0.5
        )

        # 查看按钮
        view_btn = Button(
            text="查看",
            size_hint_x=0.5,
            background_color=self.theme.PRIMARY_COLOR,
            color=self.theme.TEXT_LIGHT
        )

        buttons.add_widget(close_btn)
        buttons.add_widget(view_btn)
        content.add_widget(buttons)

        # 创建弹窗
        popup = Popup(
            title="文档详情",
            content=content,
            size_hint=(0.9, 0.6),
            auto_dismiss=True
        )

        # 绑定按钮事件
        close_btn.bind(on_release=popup.dismiss)
        view_btn.bind(on_release=lambda x: self._view_document(doc, popup))

        # 显示弹窗
        popup.open()

    def _view_document(self, doc, popup):
        """查看文档"""
        # 关闭详情弹窗
        popup.dismiss()

        # 这里应该实现文档查看功能
        # 可以打开文档查看器或者下载文档
        self._show_error_popup("文档查看功能尚未实现")

    def _show_error_popup(self, message):
        """显示错误弹窗"""
        content = BoxLayout(orientation='vertical', padding=dp(10))
        content.add_widget(Label(
            text=message,
            size_hint_y=None,
            height=dp(50)
        ))

        button = Button(
            text="确定",
            size_hint_y=None,
            height=dp(50)
        )
        content.add_widget(button)

        popup = Popup(
            title="错误",
            content=content,
            size_hint=(0.8, 0.4),
            auto_dismiss=True
        )

        button.bind(on_release=popup.dismiss)
        popup.open()

    def _cleanup_recycled_items(self):
        """清理不可见的列表项资源（占位方法，后续可扩展）"""
        pass