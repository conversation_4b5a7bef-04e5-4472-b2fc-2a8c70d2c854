#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为documents表添加缺失的外键关联字段
"""

import sqlite3
import os
from pathlib import Path

def add_missing_foreign_keys():
    """为documents表添加缺失的外键关联字段"""
    
    # 数据库文件路径
    db_path = Path(__file__).parent / "app.db"
    
    if not db_path.exists():
        print(f"数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='documents'
        """)
        
        if not cursor.fetchone():
            print("documents表不存在")
            return False
        
        # 获取当前表结构
        cursor.execute("PRAGMA table_info(documents)")
        columns = [row[1] for row in cursor.fetchall()]
        print(f"当前documents表包含 {len(columns)} 个列")
        print(f"现有列: {', '.join(columns)}")
        
        # 需要添加的外键字段
        foreign_key_fields = [
            ('health_record_id', 'INTEGER'),
            ('medical_record_id', 'INTEGER'),
            ('lab_report_id', 'INTEGER'),
            ('examination_report_id', 'INTEGER'),
            ('documentrecord_id', 'INTEGER')
        ]
        
        added_columns = []
        
        for field_name, field_type in foreign_key_fields:
            if field_name not in columns:
                try:
                    # 添加列
                    alter_sql = f"ALTER TABLE documents ADD COLUMN {field_name} {field_type}"
                    cursor.execute(alter_sql)
                    added_columns.append(field_name)
                    print(f"✓ 成功添加列: {field_name} ({field_type})")
                except sqlite3.Error as e:
                    print(f"✗ 添加列 {field_name} 失败: {e}")
            else:
                print(f"○ 列 {field_name} 已存在")
        
        # 提交更改
        conn.commit()
        
        # 验证添加结果
        cursor.execute("PRAGMA table_info(documents)")
        updated_columns = [row[1] for row in cursor.fetchall()]
        print(f"\n更新后documents表包含 {len(updated_columns)} 个列")
        
        if added_columns:
            print(f"本次添加的列: {', '.join(added_columns)}")
        else:
            print("没有添加新列")
        
        return True
        
    except sqlite3.Error as e:
        print(f"数据库操作错误: {e}")
        return False
    except Exception as e:
        print(f"未知错误: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    print("开始为documents表添加缺失的外键关联字段...")
    success = add_missing_foreign_keys()
    
    if success:
        print("\n✓ 外键字段添加完成")
    else:
        print("\n✗ 外键字段添加失败")