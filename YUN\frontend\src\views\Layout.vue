<template>
  <el-container class="layout-container">
    <!-- 侧边栏 -->
    <el-aside width="200px" class="aside">
      <div class="logo-container">
        <h1 class="logo">健康管理系统</h1>
      </div>

      <el-menu
        :default-active="activeMenu"
        class="el-menu-vertical"
        :router="true"
        background-color="#304156"
        text-color="#bfcbd9"
        active-text-color="#409EFF"
      >
        <el-menu-item index="/admin/dashboard">
          <el-icon><el-icon-odometer /></el-icon>
          <span>仪表盘</span>
        </el-menu-item>

        <!-- 用户管理选项 - 仅管理员可见 -->
        <el-menu-item index="/admin/users" class="admin-menu-item" v-if="userStore.isAdmin">
          <el-icon><el-icon-user /></el-icon>
          <span>用户管理</span>
        </el-menu-item>



        <!-- 健康资料 -->
        <el-menu-item index="/admin/health-data-overview">
          <el-icon><el-icon-document /></el-icon>
          <span>健康资料</span>
        </el-menu-item>

        <el-menu-item index="/admin/documents">
          <el-icon><el-icon-folder /></el-icon>
          <span>文档管理</span>
        </el-menu-item>

        <!-- 功能订制 -->
        <el-sub-menu index="custom-functions">
          <template #title>
            <el-icon><el-icon-set-up /></el-icon>
            <span>功能订制</span>
          </template>

          <!-- 评估量表 -->
          <el-menu-item index="/admin/assessment-management">
            <el-icon><el-icon-reading /></el-icon>
            <span>评估量表</span>
          </el-menu-item>

          <!-- 调查问卷 -->
          <el-menu-item index="/admin/questionnaires">
            <el-icon><el-icon-tickets /></el-icon>
            <span>调查问卷管理</span>
          </el-menu-item>

          <!-- 模板管理 -->
          <el-menu-item index="/admin/template-management">
            <el-icon><el-icon-files /></el-icon>
            <span>模板管理</span>
          </el-menu-item>
        </el-sub-menu>

        <!-- AI管理 -->
        <el-menu-item index="/admin/ai-management" v-if="userStore.isAdmin">
          <el-icon><el-icon-cpu /></el-icon>
          <span>AI管理</span>
        </el-menu-item>

        <el-menu-item index="/admin/profile">
          <el-icon><el-icon-user /></el-icon>
          <span>个人资料</span>
        </el-menu-item>

        <el-menu-item index="/admin/operation-logs" v-if="userStore.isAdmin">
          <el-icon><el-icon-tickets /></el-icon>
          <span>操作日志</span>
        </el-menu-item>

        <!-- 导航栏底部的系统功能 -->
        <div class="menu-spacer"></div>



        <!-- 权限管理 -->
        <el-menu-item index="/admin/permission-management" v-if="userStore.isAdmin">
          <el-icon><el-icon-key /></el-icon>
          <span>权限管理</span>
        </el-menu-item>

        <!-- 系统管理 -->
        <el-sub-menu index="system-management" v-if="userStore.isAdmin">
          <template #title>
            <el-icon><el-icon-monitor /></el-icon>
            <span>系统管理</span>
          </template>

          <!-- 系统管理主页 -->
          <el-menu-item index="/admin/system-management">
            <el-icon><el-icon-monitor /></el-icon>
            <span>系统概览</span>
          </el-menu-item>

          <!-- 项目管理 -->
          <el-menu-item index="/admin/project-management">
            <el-icon><el-icon-folder-opened /></el-icon>
            <span>项目管理</span>
          </el-menu-item>

          <!-- 服务管理 -->
          <el-menu-item index="/admin/service-management">
            <el-icon><el-icon-service /></el-icon>
            <span>服务管理</span>
          </el-menu-item>

          <!-- 配置管理 -->
          <el-menu-item index="/admin/config-management">
            <el-icon><el-icon-setting /></el-icon>
            <span>配置管理</span>
          </el-menu-item>

          <!-- 数据模式管理 -->
          <el-menu-item index="/admin/data-mode-management">
            <el-icon><el-icon-switch-button /></el-icon>
            <span>数据模式管理</span>
          </el-menu-item>

          <!-- 测试管理 -->
          <el-menu-item index="/admin/test-management">
            <el-icon><el-icon-check /></el-icon>
            <span>测试管理</span>
          </el-menu-item>

          <!-- 部署管理 -->
          <el-menu-item index="/admin/deployment-management">
            <el-icon><el-icon-upload /></el-icon>
            <span>部署管理</span>
          </el-menu-item>

          <!-- 监控告警 -->
          <el-menu-item index="/admin/monitoring-alerts">
            <el-icon><el-icon-warning /></el-icon>
            <span>监控告警</span>
          </el-menu-item>
        </el-sub-menu>

        <!-- 系统设置 -->
        <el-menu-item index="/admin/system-settings">
          <el-icon><el-icon-setting /></el-icon>
          <span>系统设置</span>
        </el-menu-item>
      </el-menu>
    </el-aside>

    <!-- 主要内容区域 -->
    <el-container>
      <!-- 头部 -->
      <el-header class="header">
        <div class="header-left">
          <el-icon class="toggle-sidebar" @click="toggleSidebar">
            <el-icon-menu />
          </el-icon>
          <breadcrumb />
        </div>

        <div class="header-right">


          <el-dropdown trigger="click">
            <div class="avatar-container">
              <el-avatar :size="30" :src="userAvatar">{{ userInitials }}</el-avatar>
              <span class="username">{{ userName }}</span>
            </div>

            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="$router.push('/admin/profile')">
                  个人资料
                </el-dropdown-item>
                <el-dropdown-item divided @click="logout">
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>

      <!-- 内容区域 -->
      <el-main class="main">
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup>
import { ref, computed, onMounted, h } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '../store/user'
import axios from 'axios'

// 面包屑组件（简化版）
const Breadcrumb = {
  setup() {
    const route = useRoute()
    return () => h('div', { class: 'breadcrumb' }, route.meta.title || route.name)
  }
}

// 获取路由和用户状态
const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 计算当前激活的菜单项
const activeMenu = computed(() => route.path)

// 用户信息
const userName = computed(() => userStore.user?.full_name || userStore.user?.username || '用户')
const userAvatar = computed(() => userStore.user?.avatar || '')
const userInitials = computed(() => {
  const name = userName.value
  return name ? name.charAt(0).toUpperCase() : 'U'
})



// 移除调试日志以提高性能

// 侧边栏状态
const isCollapse = ref(false)
const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
}

// 登出
const logout = () => {
  userStore.logout()
}
</script>

<style scoped>
.layout-container {
  height: 100%;
}

.aside {
  background-color: #304156;
  color: #bfcbd9;
  overflow-x: hidden;
}

.logo-container {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #2b3649;
}

.logo {
  color: #fff;
  font-size: 18px;
  margin: 0;
}

.header {
  background-color: #fff;
  color: #333;
  line-height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.header-left {
  display: flex;
  align-items: center;
}

.toggle-sidebar {
  padding: 0 15px;
  cursor: pointer;
  font-size: 20px;
}

.breadcrumb {
  font-size: 14px;
}

.header-right {
  margin-right: 20px;
  display: flex;
  align-items: center;
}

.admin-button {
  margin-right: 15px;
}

.avatar-container {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.username {
  margin-left: 8px;
  font-size: 14px;
}

.main {
  background-color: #f0f2f5;
  padding: 20px;
}

.el-menu-vertical:not(.el-menu--collapse) {
  width: 200px;
}

.admin-menu-item {
  color: #f6d365 !important;
  font-weight: bold;
}

.admin-menu-item:hover, .admin-menu-item.is-active {
  color: #f9c74f !important;
  background-color: #2c3e50 !important;
}



.menu-badge {
  margin-left: 5px;
  margin-right: 20px;
}

.menu-spacer {
  flex-grow: 1;
  min-height: 20px;
  border-bottom: 1px solid #3e526d;
  margin-bottom: 10px;
}

.el-menu {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 60px);
}
</style>
