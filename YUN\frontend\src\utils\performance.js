/**
 * 性能监控和优化工具
 * 提供页面性能监控、组件性能分析、内存使用监控等功能
 */
import { CONFIG } from '@/config/environment'

/**
 * 性能指标收集器
 */
class PerformanceCollector {
  constructor() {
    this.metrics = new Map()
    this.observers = new Map()
    this.isSupported = this.checkSupport()
    
    if (this.isSupported) {
      this.initObservers()
    }
  }

  /**
   * 检查浏览器支持
   */
  checkSupport() {
    return (
      typeof window !== 'undefined' &&
      'performance' in window &&
      'PerformanceObserver' in window
    )
  }

  /**
   * 初始化性能观察器
   */
  initObservers() {
    // 导航性能
    this.observeNavigation()
    
    // 资源加载性能
    this.observeResources()
    
    // 长任务监控
    this.observeLongTasks()
    
    // 布局偏移监控
    this.observeLayoutShift()
    
    // 首次内容绘制
    this.observePaint()
  }

  /**
   * 监控导航性能
   */
  observeNavigation() {
    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach(entry => {
          if (entry.entryType === 'navigation') {
            this.collectNavigationMetrics(entry)
          }
        })
      })
      
      observer.observe({ entryTypes: ['navigation'] })
      this.observers.set('navigation', observer)
    } catch (error) {
      console.warn('导航性能监控不支持:', error)
    }
  }

  /**
   * 监控资源加载性能
   */
  observeResources() {
    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach(entry => {
          if (entry.entryType === 'resource') {
            this.collectResourceMetrics(entry)
          }
        })
      })
      
      observer.observe({ entryTypes: ['resource'] })
      this.observers.set('resource', observer)
    } catch (error) {
      console.warn('资源性能监控不支持:', error)
    }
  }

  /**
   * 监控长任务
   */
  observeLongTasks() {
    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach(entry => {
          if (entry.entryType === 'longtask') {
            this.collectLongTaskMetrics(entry)
          }
        })
      })
      
      observer.observe({ entryTypes: ['longtask'] })
      this.observers.set('longtask', observer)
    } catch (error) {
      console.warn('长任务监控不支持:', error)
    }
  }

  /**
   * 监控布局偏移
   */
  observeLayoutShift() {
    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach(entry => {
          if (entry.entryType === 'layout-shift' && !entry.hadRecentInput) {
            this.collectLayoutShiftMetrics(entry)
          }
        })
      })
      
      observer.observe({ entryTypes: ['layout-shift'] })
      this.observers.set('layout-shift', observer)
    } catch (error) {
      console.warn('布局偏移监控不支持:', error)
    }
  }

  /**
   * 监控绘制性能
   */
  observePaint() {
    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach(entry => {
          if (entry.entryType === 'paint') {
            this.collectPaintMetrics(entry)
          }
        })
      })
      
      observer.observe({ entryTypes: ['paint'] })
      this.observers.set('paint', observer)
    } catch (error) {
      console.warn('绘制性能监控不支持:', error)
    }
  }

  /**
   * 收集导航性能指标
   */
  collectNavigationMetrics(entry) {
    const metrics = {
      // DNS 查询时间
      dnsLookup: entry.domainLookupEnd - entry.domainLookupStart,
      // TCP 连接时间
      tcpConnect: entry.connectEnd - entry.connectStart,
      // SSL 握手时间
      sslHandshake: entry.secureConnectionStart > 0 ? entry.connectEnd - entry.secureConnectionStart : 0,
      // 请求响应时间
      requestResponse: entry.responseEnd - entry.requestStart,
      // DOM 解析时间
      domParse: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
      // 资源加载时间
      resourceLoad: entry.loadEventEnd - entry.loadEventStart,
      // 首字节时间 (TTFB)
      ttfb: entry.responseStart - entry.requestStart,
      // 页面加载总时间
      pageLoad: entry.loadEventEnd - entry.navigationStart,
      // DOM 准备时间
      domReady: entry.domContentLoadedEventEnd - entry.navigationStart
    }

    this.metrics.set('navigation', {
      ...metrics,
      timestamp: Date.now(),
      url: entry.name
    })

    if (CONFIG.performance.enableReporting) {
      this.reportMetrics('navigation', metrics)
    }
  }

  /**
   * 收集资源性能指标
   */
  collectResourceMetrics(entry) {
    const resourceType = this.getResourceType(entry.name)
    const metrics = {
      name: entry.name,
      type: resourceType,
      size: entry.transferSize || 0,
      duration: entry.duration,
      startTime: entry.startTime,
      // 缓存命中
      fromCache: entry.transferSize === 0 && entry.decodedBodySize > 0
    }

    const resourceMetrics = this.metrics.get('resources') || []
    resourceMetrics.push({
      ...metrics,
      timestamp: Date.now()
    })
    
    // 只保留最近的 100 个资源记录
    if (resourceMetrics.length > 100) {
      resourceMetrics.splice(0, resourceMetrics.length - 100)
    }
    
    this.metrics.set('resources', resourceMetrics)
  }

  /**
   * 收集长任务指标
   */
  collectLongTaskMetrics(entry) {
    const longTasks = this.metrics.get('longTasks') || []
    longTasks.push({
      duration: entry.duration,
      startTime: entry.startTime,
      timestamp: Date.now()
    })
    
    // 只保留最近的 50 个长任务记录
    if (longTasks.length > 50) {
      longTasks.splice(0, longTasks.length - 50)
    }
    
    this.metrics.set('longTasks', longTasks)

    // 长任务告警
    if (entry.duration > 100) {
      console.warn(`检测到长任务: ${entry.duration.toFixed(2)}ms`)
    }
  }

  /**
   * 收集布局偏移指标
   */
  collectLayoutShiftMetrics(entry) {
    const layoutShifts = this.metrics.get('layoutShifts') || []
    layoutShifts.push({
      value: entry.value,
      startTime: entry.startTime,
      timestamp: Date.now()
    })
    
    // 只保留最近的 50 个布局偏移记录
    if (layoutShifts.length > 50) {
      layoutShifts.splice(0, layoutShifts.length - 50)
    }
    
    this.metrics.set('layoutShifts', layoutShifts)

    // 计算累积布局偏移 (CLS)
    const cls = layoutShifts.reduce((sum, shift) => sum + shift.value, 0)
    this.metrics.set('cls', cls)
  }

  /**
   * 收集绘制性能指标
   */
  collectPaintMetrics(entry) {
    const paintMetrics = this.metrics.get('paint') || {}
    paintMetrics[entry.name] = {
      startTime: entry.startTime,
      timestamp: Date.now()
    }
    this.metrics.set('paint', paintMetrics)
  }

  /**
   * 获取资源类型
   */
  getResourceType(url) {
    const extension = url.split('.').pop()?.toLowerCase()
    
    if (['js', 'mjs'].includes(extension)) return 'script'
    if (['css'].includes(extension)) return 'stylesheet'
    if (['png', 'jpg', 'jpeg', 'gif', 'svg', 'webp'].includes(extension)) return 'image'
    if (['woff', 'woff2', 'ttf', 'otf'].includes(extension)) return 'font'
    if (url.includes('/api/')) return 'api'
    
    return 'other'
  }

  /**
   * 获取性能指标
   */
  getMetrics(type = null) {
    if (type) {
      return this.metrics.get(type)
    }
    return Object.fromEntries(this.metrics)
  }

  /**
   * 获取性能摘要
   */
  getPerformanceSummary() {
    const navigation = this.metrics.get('navigation')
    const resources = this.metrics.get('resources') || []
    const longTasks = this.metrics.get('longTasks') || []
    const cls = this.metrics.get('cls') || 0
    const paint = this.metrics.get('paint') || {}

    return {
      // 核心 Web 指标
      coreWebVitals: {
        // 最大内容绘制
        lcp: this.getLCP(),
        // 首次输入延迟
        fid: this.getFID(),
        // 累积布局偏移
        cls: cls
      },
      
      // 导航性能
      navigation: navigation ? {
        ttfb: navigation.ttfb,
        pageLoad: navigation.pageLoad,
        domReady: navigation.domReady
      } : null,
      
      // 资源统计
      resources: {
        total: resources.length,
        totalSize: resources.reduce((sum, r) => sum + r.size, 0),
        avgDuration: resources.length > 0 ? resources.reduce((sum, r) => sum + r.duration, 0) / resources.length : 0,
        cacheHitRate: resources.length > 0 ? resources.filter(r => r.fromCache).length / resources.length : 0
      },
      
      // 性能问题
      issues: {
        longTasksCount: longTasks.length,
        avgLongTaskDuration: longTasks.length > 0 ? longTasks.reduce((sum, t) => sum + t.duration, 0) / longTasks.length : 0,
        highCLS: cls > 0.1
      },
      
      // 绘制指标
      paint: {
        fcp: paint['first-contentful-paint']?.startTime || 0,
        fp: paint['first-paint']?.startTime || 0
      }
    }
  }

  /**
   * 获取最大内容绘制时间 (LCP)
   */
  getLCP() {
    // 这里需要使用 largest-contentful-paint API
    // 简化实现，实际应该使用 PerformanceObserver
    return 0
  }

  /**
   * 获取首次输入延迟 (FID)
   */
  getFID() {
    // 这里需要使用 first-input API
    // 简化实现，实际应该使用 PerformanceObserver
    return 0
  }

  /**
   * 上报性能指标
   */
  reportMetrics(type, metrics) {
    if (!CONFIG.performance.enableReporting) return

    // 这里可以发送到性能监控服务
    console.log(`性能指标 [${type}]:`, metrics)
    
    // 示例：发送到远程服务
    // fetch('/api/performance', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify({ type, metrics, timestamp: Date.now() })
    // }).catch(console.error)
  }

  /**
   * 清理观察器
   */
  disconnect() {
    this.observers.forEach(observer => {
      observer.disconnect()
    })
    this.observers.clear()
  }
}

/**
 * 内存监控器
 */
class MemoryMonitor {
  constructor() {
    this.isSupported = 'memory' in performance
    this.samples = []
    this.maxSamples = 100
  }

  /**
   * 获取内存使用情况
   */
  getMemoryUsage() {
    if (!this.isSupported) {
      return null
    }

    const memory = performance.memory
    return {
      used: memory.usedJSHeapSize,
      total: memory.totalJSHeapSize,
      limit: memory.jsHeapSizeLimit,
      usage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit * 100).toFixed(2)
    }
  }

  /**
   * 开始监控内存
   */
  startMonitoring(interval = 5000) {
    if (!this.isSupported) {
      console.warn('浏览器不支持内存监控')
      return
    }

    this.monitoringInterval = setInterval(() => {
      const usage = this.getMemoryUsage()
      if (usage) {
        this.samples.push({
          ...usage,
          timestamp: Date.now()
        })

        // 保持样本数量在限制内
        if (this.samples.length > this.maxSamples) {
          this.samples.shift()
        }

        // 内存使用率告警
        if (parseFloat(usage.usage) > 80) {
          console.warn(`内存使用率过高: ${usage.usage}%`)
        }
      }
    }, interval)
  }

  /**
   * 停止监控内存
   */
  stopMonitoring() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
      this.monitoringInterval = null
    }
  }

  /**
   * 获取内存使用趋势
   */
  getMemoryTrend() {
    if (this.samples.length < 2) {
      return null
    }

    const recent = this.samples.slice(-10)
    const avgUsage = recent.reduce((sum, sample) => sum + parseFloat(sample.usage), 0) / recent.length
    const trend = recent.length > 1 ? recent[recent.length - 1].usage - recent[0].usage : 0

    return {
      avgUsage: avgUsage.toFixed(2),
      trend: trend.toFixed(2),
      samples: recent
    }
  }
}

/**
 * 组件性能分析器
 */
class ComponentProfiler {
  constructor() {
    this.profiles = new Map()
    this.renderTimes = new Map()
  }

  /**
   * 开始分析组件
   */
  startProfile(componentName) {
    const startTime = performance.now()
    this.profiles.set(componentName, {
      startTime,
      name: componentName
    })
  }

  /**
   * 结束分析组件
   */
  endProfile(componentName) {
    const endTime = performance.now()
    const profile = this.profiles.get(componentName)
    
    if (profile) {
      const duration = endTime - profile.startTime
      
      // 记录渲染时间
      const renderTimes = this.renderTimes.get(componentName) || []
      renderTimes.push({
        duration,
        timestamp: Date.now()
      })
      
      // 只保留最近的 20 次渲染记录
      if (renderTimes.length > 20) {
        renderTimes.shift()
      }
      
      this.renderTimes.set(componentName, renderTimes)
      this.profiles.delete(componentName)
      
      // 慢渲染告警
      if (duration > 16) { // 超过一帧的时间
        console.warn(`组件 ${componentName} 渲染耗时: ${duration.toFixed(2)}ms`)
      }
      
      return duration
    }
    
    return null
  }

  /**
   * 获取组件性能统计
   */
  getComponentStats(componentName) {
    const renderTimes = this.renderTimes.get(componentName)
    
    if (!renderTimes || renderTimes.length === 0) {
      return null
    }
    
    const durations = renderTimes.map(r => r.duration)
    const avgDuration = durations.reduce((sum, d) => sum + d, 0) / durations.length
    const maxDuration = Math.max(...durations)
    const minDuration = Math.min(...durations)
    
    return {
      componentName,
      renderCount: renderTimes.length,
      avgDuration: avgDuration.toFixed(2),
      maxDuration: maxDuration.toFixed(2),
      minDuration: minDuration.toFixed(2),
      recentRenders: renderTimes.slice(-5)
    }
  }

  /**
   * 获取所有组件性能统计
   */
  getAllStats() {
    const stats = []
    
    for (const componentName of this.renderTimes.keys()) {
      const componentStats = this.getComponentStats(componentName)
      if (componentStats) {
        stats.push(componentStats)
      }
    }
    
    return stats.sort((a, b) => parseFloat(b.avgDuration) - parseFloat(a.avgDuration))
  }
}

/**
 * 防抖函数
 */
export function debounce(func, wait, immediate = false) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      timeout = null
      if (!immediate) func.apply(this, args)
    }
    const callNow = immediate && !timeout
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    if (callNow) func.apply(this, args)
  }
}

/**
 * 节流函数
 */
export function throttle(func, limit) {
  let inThrottle
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 虚拟滚动优化
 */
export class VirtualScroller {
  constructor(options = {}) {
    this.itemHeight = options.itemHeight || 50
    this.containerHeight = options.containerHeight || 400
    this.buffer = options.buffer || 5
    this.scrollTop = 0
  }

  /**
   * 计算可见范围
   */
  getVisibleRange(totalItems) {
    const visibleCount = Math.ceil(this.containerHeight / this.itemHeight)
    const startIndex = Math.max(0, Math.floor(this.scrollTop / this.itemHeight) - this.buffer)
    const endIndex = Math.min(totalItems - 1, startIndex + visibleCount + this.buffer * 2)
    
    return {
      startIndex,
      endIndex,
      visibleCount,
      offsetY: startIndex * this.itemHeight
    }
  }

  /**
   * 更新滚动位置
   */
  updateScrollTop(scrollTop) {
    this.scrollTop = scrollTop
  }
}

/**
 * 图片懒加载
 */
export class LazyLoader {
  constructor(options = {}) {
    this.rootMargin = options.rootMargin || '50px'
    this.threshold = options.threshold || 0.1
    this.observer = null
    this.init()
  }

  init() {
    if ('IntersectionObserver' in window) {
      this.observer = new IntersectionObserver(
        this.handleIntersection.bind(this),
        {
          rootMargin: this.rootMargin,
          threshold: this.threshold
        }
      )
    }
  }

  handleIntersection(entries) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target
        const src = img.dataset.src
        
        if (src) {
          img.src = src
          img.removeAttribute('data-src')
          this.observer.unobserve(img)
        }
      }
    })
  }

  observe(element) {
    if (this.observer) {
      this.observer.observe(element)
    }
  }

  unobserve(element) {
    if (this.observer) {
      this.observer.unobserve(element)
    }
  }

  disconnect() {
    if (this.observer) {
      this.observer.disconnect()
    }
  }
}

// 创建全局实例
const performanceCollector = new PerformanceCollector()
const memoryMonitor = new MemoryMonitor()
const componentProfiler = new ComponentProfiler()

// 自动开始监控
if (CONFIG.performance.enableMonitoring) {
  memoryMonitor.startMonitoring()
}

export {
  performanceCollector,
  memoryMonitor,
  componentProfiler,
  PerformanceCollector,
  MemoryMonitor,
  ComponentProfiler,
  VirtualScroller,
  LazyLoader
}

export default {
  performanceCollector,
  memoryMonitor,
  componentProfiler,
  debounce,
  throttle,
  VirtualScroller,
  LazyLoader
}