#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
移动端量表分发问题调试脚本
分析后端API返回数据和移动端过滤逻辑
"""

import requests
import json
import sqlite3
from datetime import datetime

def check_backend_api():
    """检查后端API返回的数据"""
    print("=== 检查后端API返回数据 ===")
    
    base_url = "http://localhost:8000"
    headers = {
        'Content-Type': 'application/json',
        'X-User-ID': 'SM_006'
    }
    
    try:
        # 测试移动端API
        response = requests.get(f"{base_url}/api/mobile/assessments?custom_id=SM_006&limit=20", 
                              headers=headers, timeout=10)
        print(f"API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"API响应数据结构: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if 'data' in data and 'assessments' in data['data']:
                assessments = data['data']['assessments']
                print(f"\n返回的量表数量: {len(assessments)}")
                
                for i, assessment in enumerate(assessments, 1):
                    print(f"\n量表 {i}:")
                    print(f"  ID: {assessment.get('id')}")
                    print(f"  名称: {assessment.get('name')}")
                    print(f"  状态: {assessment.get('status')}")
                    print(f"  创建时间: {assessment.get('created_at')}")
                    print(f"  完成时间: {assessment.get('completed_at')}")
                    print(f"  模板信息: {assessment.get('template')}")
                    
                # 分析状态分布
                status_count = {}
                for assessment in assessments:
                    status = assessment.get('status', 'unknown')
                    status_count[status] = status_count.get(status, 0) + 1
                    
                print(f"\n状态分布: {status_count}")
                
                # 检查pending状态的量表
                pending_assessments = [a for a in assessments if a.get('status') == 'pending']
                print(f"\nPending状态量表数量: {len(pending_assessments)}")
                
                for assessment in pending_assessments:
                    print(f"  - {assessment.get('name')} (ID: {assessment.get('id')})")
                    
            else:
                print("API返回数据格式异常")
        else:
            print(f"API请求失败: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("无法连接到后端服务器，请确保服务器正在运行")
    except Exception as e:
        print(f"检查API时出错: {e}")

def check_database_data():
    """直接检查数据库中的数据"""
    print("\n=== 检查数据库中的数据 ===")
    
    try:
        # 连接数据库
        conn = sqlite3.connect('YUN/backend/app.db')
        cursor = conn.cursor()
        
        # 查询SM_006用户的所有量表
        cursor.execute("""
            SELECT id, name, status, created_at, completed_at, template_id
            FROM assessments 
            WHERE custom_id = 'SM_006'
            ORDER BY created_at DESC
            LIMIT 10
        """)
        
        assessments = cursor.fetchall()
        print(f"数据库中SM_006用户的量表数量: {len(assessments)}")
        
        for assessment in assessments:
            print(f"\n量表:")
            print(f"  ID: {assessment[0]}")
            print(f"  名称: {assessment[1]}")
            print(f"  状态: {assessment[2]}")
            print(f"  创建时间: {assessment[3]}")
            print(f"  完成时间: {assessment[4]}")
            print(f"  模板ID: {assessment[5]}")
            
        # 统计状态分布
        cursor.execute("""
            SELECT status, COUNT(*) 
            FROM assessments 
            WHERE custom_id = 'SM_006'
            GROUP BY status
        """)
        
        status_stats = cursor.fetchall()
        print(f"\n数据库中状态分布:")
        for status, count in status_stats:
            print(f"  {status}: {count}")
            
        # 检查最新的pending量表
        cursor.execute("""
            SELECT id, name, created_at
            FROM assessments 
            WHERE custom_id = 'SM_006' AND status = 'pending'
            ORDER BY created_at DESC
            LIMIT 5
        """)
        
        pending_assessments = cursor.fetchall()
        print(f"\n最新的pending量表:")
        for assessment in pending_assessments:
            print(f"  - {assessment[1]} (ID: {assessment[0]}, 创建时间: {assessment[2]})")
            
        conn.close()
        
    except Exception as e:
        print(f"检查数据库时出错: {e}")

def analyze_mobile_filter_logic():
    """分析移动端过滤逻辑"""
    print("\n=== 分析移动端过滤逻辑 ===")
    
    print("移动端SurveyScreen.load_assessments()方法中的过滤逻辑:")
    print("  self.assessments = [a for a in assessments_data if a.get('status') != 'completed']")
    print("")
    print("这意味着:")
    print("  - 只显示状态不是'completed'的量表")
    print("  - 'pending'状态的量表应该被显示")
    print("  - 'in_progress'状态的量表也应该被显示")
    print("")
    print("如果移动端看不到新分发的量表，可能的原因:")
    print("  1. 后端API没有返回新分发的量表")
    print("  2. 新分发的量表状态不是'pending'")
    print("  3. API查询条件有问题")
    print("  4. 移动端缓存了旧数据")

def check_api_query_logic():
    """检查API查询逻辑"""
    print("\n=== 检查API查询逻辑 ===")
    
    print("后端mobile_notifications.py中的查询逻辑:")
    print("  query = db.query(Assessment).outerjoin(AssessmentDistribution)")
    print("  if custom_id:")
    print("      query = query.filter(Assessment.custom_id == custom_id)")
    print("")
    print("这个查询应该:")
    print("  - 使用outer join包含没有分发记录的评估")
    print("  - 直接通过Assessment表的custom_id字段过滤")
    print("  - 按created_at降序排序")
    print("")
    print("如果查询有问题，可能的原因:")
    print("  1. Assessment表中custom_id字段值不正确")
    print("  2. 查询条件过滤掉了新分发的量表")
    print("  3. 数据库连接或事务问题")

def main():
    """主函数"""
    print("移动端量表分发问题调试")
    print("=" * 50)
    
    # 检查数据库数据
    check_database_data()
    
    # 检查后端API
    check_backend_api()
    
    # 分析移动端过滤逻辑
    analyze_mobile_filter_logic()
    
    # 检查API查询逻辑
    check_api_query_logic()
    
    print("\n=== 调试建议 ===")
    print("1. 确认后端服务器正在运行")
    print("2. 检查API是否返回了新分发的pending量表")
    print("3. 确认移动端没有缓存旧数据")
    print("4. 检查移动端过滤逻辑是否正确")
    print("5. 验证数据库中的数据是否正确")

if __name__ == "__main__":
    main()