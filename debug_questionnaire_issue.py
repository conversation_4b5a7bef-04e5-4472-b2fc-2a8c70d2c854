#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def debug_questionnaire_issue():
    db_path = "c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend\\app.db"
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("=== 检查问卷分发记录详情 ===")
        cursor.execute("""
            SELECT qd.id, qd.questionnaire_id, qd.custom_id, qd.status, qd.due_date, qd.created_at
            FROM questionnaire_distributions qd
            WHERE qd.custom_id = 'SM_008'
        """)
        
        distributions = cursor.fetchall()
        print(f"找到 {len(distributions)} 条问卷分发记录:")
        for dist in distributions:
            print(f"  分发ID: {dist[0]}, 问卷ID: {dist[1]}, 状态: {dist[3]}")
            
            # 检查对应的问卷是否存在
            cursor.execute("SELECT id, title FROM questionnaires WHERE id = ?", (dist[1],))
            questionnaire = cursor.fetchone()
            if questionnaire:
                print(f"  对应问卷: ID={questionnaire[0]}, 标题={questionnaire[1]}")
            else:
                print(f"  警告: 问卷ID {dist[1]} 不存在于questionnaires表中！")
            print()
        
        print("=== 检查所有问卷记录 ===")
        cursor.execute("SELECT id, title, status FROM questionnaires")
        questionnaires = cursor.fetchall()
        print(f"questionnaires表中总共有 {len(questionnaires)} 条记录:")
        for q in questionnaires:
            print(f"  问卷ID: {q[0]}, 标题: {q[1]}, 状态: {q[2]}")
        
        print("\n=== 测试联接查询 ===")
        cursor.execute("""
            SELECT qd.id as dist_id, qd.questionnaire_id, qd.status as dist_status,
                   q.id as q_id, q.title, q.status as q_status
            FROM questionnaire_distributions qd
            JOIN questionnaires q ON qd.questionnaire_id = q.id
            WHERE qd.custom_id = 'SM_008' AND qd.status = 'pending'
        """)
        
        joined_results = cursor.fetchall()
        print(f"联接查询结果: {len(joined_results)} 条记录")
        for result in joined_results:
            print(f"  分发ID: {result[0]}, 问卷ID: {result[1]}, 分发状态: {result[2]}")
            print(f"  问卷标题: {result[4]}, 问卷状态: {result[5]}")
        
        conn.close()
        
    except Exception as e:
        print(f"检查数据时出错: {e}")

if __name__ == "__main__":
    debug_questionnaire_issue()