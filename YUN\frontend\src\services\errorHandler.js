/**
 * 统一错误处理和日志服务
 * 提供细致的错误分类、用户友好的错误提示和完整的错误日志记录
 */
import { ElMessage, ElNotification } from 'element-plus'
import { CONFIG } from '@/config/environment'

// 错误类型枚举
export const ERROR_TYPES = {
  NETWORK: 'NETWORK_ERROR',
  SERVER: 'SERVER_ERROR',
  CLIENT: 'CLIENT_ERROR',
  VALIDATION: 'VALIDATION_ERROR',
  AUTH: 'AUTH_ERROR',
  PERMISSION: 'PERMISSION_ERROR',
  BUSINESS: 'BUSINESS_ERROR',
  UNKNOWN: 'UNKNOWN_ERROR'
}

// 错误级别
export const ERROR_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
}

// 错误消息映射
const ERROR_MESSAGES = {
  [ERROR_TYPES.NETWORK]: {
    title: '网络错误',
    message: '网络连接失败，请检查网络设置后重试',
    level: ERROR_LEVELS.MEDIUM
  },
  [ERROR_TYPES.SERVER]: {
    title: '服务器错误',
    message: '服务器暂时无法响应，请稍后重试',
    level: ERROR_LEVELS.HIGH
  },
  [ERROR_TYPES.CLIENT]: {
    title: '客户端错误',
    message: '请求格式错误，请检查输入信息',
    level: ERROR_LEVELS.MEDIUM
  },
  [ERROR_TYPES.VALIDATION]: {
    title: '数据验证错误',
    message: '输入数据不符合要求，请检查后重新提交',
    level: ERROR_LEVELS.LOW
  },
  [ERROR_TYPES.AUTH]: {
    title: '身份验证失败',
    message: '登录状态已过期，请重新登录',
    level: ERROR_LEVELS.HIGH
  },
  [ERROR_TYPES.PERMISSION]: {
    title: '权限不足',
    message: '您没有执行此操作的权限',
    level: ERROR_LEVELS.MEDIUM
  },
  [ERROR_TYPES.BUSINESS]: {
    title: '业务逻辑错误',
    message: '操作不符合业务规则',
    level: ERROR_LEVELS.MEDIUM
  },
  [ERROR_TYPES.UNKNOWN]: {
    title: '未知错误',
    message: '发生了未知错误，请联系技术支持',
    level: ERROR_LEVELS.HIGH
  }
}

/**
 * 错误分类器
 * 根据错误对象自动分类错误类型
 */
class ErrorClassifier {
  static classify(error) {
    // 网络错误
    if (!error.response) {
      return ERROR_TYPES.NETWORK
    }

    const status = error.response?.status
    const code = error.code
    const message = error.message?.toLowerCase() || ''

    // 根据HTTP状态码分类
    if (status) {
      if (status === 401) return ERROR_TYPES.AUTH
      if (status === 403) return ERROR_TYPES.PERMISSION
      if (status >= 400 && status < 500) return ERROR_TYPES.CLIENT
      if (status >= 500) return ERROR_TYPES.SERVER
    }

    // 根据错误代码分类
    if (code) {
      if (code === 'ECONNABORTED' || code === 'TIMEOUT') return ERROR_TYPES.NETWORK
      if (code === 'NETWORK_ERROR') return ERROR_TYPES.NETWORK
    }

    // 根据错误消息分类
    if (message.includes('network') || message.includes('timeout')) {
      return ERROR_TYPES.NETWORK
    }
    if (message.includes('validation') || message.includes('invalid')) {
      return ERROR_TYPES.VALIDATION
    }
    if (message.includes('unauthorized') || message.includes('forbidden')) {
      return ERROR_TYPES.AUTH
    }

    return ERROR_TYPES.UNKNOWN
  }
}

/**
 * 日志记录器
 */
class Logger {
  constructor() {
    this.logs = []
    this.maxLogs = 1000
  }

  /**
   * 记录日志
   * @param {string} level 日志级别
   * @param {string} message 日志消息
   * @param {Object} data 附加数据
   */
  log(level, message, data = {}) {
    const logEntry = {
      id: Date.now() + Math.random(),
      timestamp: new Date().toISOString(),
      level,
      message,
      data,
      url: window.location.href,
      userAgent: navigator.userAgent
    }

    // 添加到本地日志
    this.logs.unshift(logEntry)
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(0, this.maxLogs)
    }

    // 控制台输出（开发环境）
    if (CONFIG.debug.showErrorDetails) {
      const consoleMethod = this.getConsoleMethod(level)
      consoleMethod(`[${level.toUpperCase()}] ${message}`, data)
    }

    // 发送到远程日志服务（生产环境）
    if (CONFIG.log.enableRemote && level === ERROR_LEVELS.HIGH || level === ERROR_LEVELS.CRITICAL) {
      this.sendToRemoteLogger(logEntry)
    }
  }

  /**
   * 获取对应的控制台方法
   * @param {string} level 日志级别
   * @returns {Function} 控制台方法
   */
  getConsoleMethod(level) {
    switch (level) {
      case ERROR_LEVELS.CRITICAL:
      case ERROR_LEVELS.HIGH:
        return console.error
      case ERROR_LEVELS.MEDIUM:
        return console.warn
      case ERROR_LEVELS.LOW:
        return console.info
      default:
        return console.log
    }
  }

  /**
   * 发送日志到远程服务
   * @param {Object} logEntry 日志条目
   */
  async sendToRemoteLogger(logEntry) {
    try {
      // 这里可以集成第三方日志服务，如 Sentry、LogRocket 等
      // await fetch('/api/logs', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(logEntry)
      // })
      console.log('发送日志到远程服务:', logEntry)
    } catch (error) {
      console.error('发送日志失败:', error)
    }
  }

  /**
   * 获取日志列表
   * @param {Object} filters 过滤条件
   * @returns {Array} 日志列表
   */
  getLogs(filters = {}) {
    let logs = [...this.logs]

    if (filters.level) {
      logs = logs.filter(log => log.level === filters.level)
    }

    if (filters.startTime) {
      logs = logs.filter(log => new Date(log.timestamp) >= new Date(filters.startTime))
    }

    if (filters.endTime) {
      logs = logs.filter(log => new Date(log.timestamp) <= new Date(filters.endTime))
    }

    if (filters.search) {
      const searchTerm = filters.search.toLowerCase()
      logs = logs.filter(log => 
        log.message.toLowerCase().includes(searchTerm) ||
        JSON.stringify(log.data).toLowerCase().includes(searchTerm)
      )
    }

    return logs
  }

  /**
   * 清除日志
   */
  clearLogs() {
    this.logs = []
  }

  /**
   * 导出日志
   * @param {string} format 导出格式
   * @returns {string} 导出内容
   */
  exportLogs(format = 'json') {
    if (format === 'json') {
      return JSON.stringify(this.logs, null, 2)
    }
    
    if (format === 'csv') {
      const headers = ['时间', '级别', '消息', '数据', 'URL']
      const rows = this.logs.map(log => [
        log.timestamp,
        log.level,
        log.message,
        JSON.stringify(log.data),
        log.url
      ])
      
      return [headers, ...rows].map(row => row.join(',')).join('\n')
    }
    
    return this.logs.map(log => 
      `[${log.timestamp}] [${log.level.toUpperCase()}] ${log.message} ${JSON.stringify(log.data)}`
    ).join('\n')
  }
}

/**
 * 错误处理器主类
 */
class ErrorHandler {
  constructor() {
    this.logger = new Logger()
    this.retryAttempts = new Map()
    this.maxRetries = 3
    this.retryDelay = 1000
  }

  /**
   * 处理错误
   * @param {Error} error 错误对象
   * @param {Object} context 错误上下文
   * @param {Object} options 处理选项
   */
  handle(error, context = {}, options = {}) {
    const {
      showMessage = true,
      showNotification = false,
      logError = true,
      retryable = false,
      customMessage = null
    } = options

    // 分类错误
    const errorType = ErrorClassifier.classify(error)
    const errorConfig = ERROR_MESSAGES[errorType]
    
    // 构建错误信息
    const errorInfo = {
      type: errorType,
      level: errorConfig.level,
      title: errorConfig.title,
      message: customMessage || error.response?.data?.message || errorConfig.message,
      originalError: error,
      context,
      timestamp: new Date().toISOString(),
      stack: error.stack
    }

    // 记录日志
    if (logError) {
      this.logger.log(errorConfig.level, errorInfo.message, {
        type: errorType,
        context,
        error: {
          message: error.message,
          stack: error.stack,
          response: error.response?.data
        }
      })
    }

    // 显示用户提示
    if (showMessage) {
      this.showUserMessage(errorInfo)
    }

    if (showNotification) {
      this.showUserNotification(errorInfo)
    }

    // 处理特殊错误类型
    this.handleSpecialErrors(errorType, error, context)

    // 重试逻辑
    if (retryable && this.shouldRetry(context)) {
      return this.scheduleRetry(context)
    }

    return Promise.reject(errorInfo)
  }

  /**
   * 显示用户消息
   * @param {Object} errorInfo 错误信息
   */
  showUserMessage(errorInfo) {
    const messageType = this.getMessageType(errorInfo.level)
    
    ElMessage({
      type: messageType,
      message: errorInfo.message,
      duration: this.getMessageDuration(errorInfo.level),
      showClose: true,
      grouping: true
    })
  }

  /**
   * 显示用户通知
   * @param {Object} errorInfo 错误信息
   */
  showUserNotification(errorInfo) {
    const notificationType = this.getMessageType(errorInfo.level)
    
    ElNotification({
      type: notificationType,
      title: errorInfo.title,
      message: errorInfo.message,
      duration: this.getMessageDuration(errorInfo.level),
      position: 'top-right'
    })
  }

  /**
   * 获取消息类型
   * @param {string} level 错误级别
   * @returns {string} 消息类型
   */
  getMessageType(level) {
    switch (level) {
      case ERROR_LEVELS.CRITICAL:
      case ERROR_LEVELS.HIGH:
        return 'error'
      case ERROR_LEVELS.MEDIUM:
        return 'warning'
      case ERROR_LEVELS.LOW:
        return 'info'
      default:
        return 'info'
    }
  }

  /**
   * 获取消息显示时长
   * @param {string} level 错误级别
   * @returns {number} 显示时长（毫秒）
   */
  getMessageDuration(level) {
    switch (level) {
      case ERROR_LEVELS.CRITICAL:
        return 0 // 不自动关闭
      case ERROR_LEVELS.HIGH:
        return 8000
      case ERROR_LEVELS.MEDIUM:
        return 5000
      case ERROR_LEVELS.LOW:
        return 3000
      default:
        return 3000
    }
  }

  /**
   * 处理特殊错误类型
   * @param {string} errorType 错误类型
   * @param {Error} error 原始错误
   * @param {Object} context 错误上下文
   */
  handleSpecialErrors(errorType, error, context) {
    switch (errorType) {
      case ERROR_TYPES.AUTH:
        this.handleAuthError(error, context)
        break
      case ERROR_TYPES.PERMISSION:
        this.handlePermissionError(error, context)
        break
      case ERROR_TYPES.NETWORK:
        this.handleNetworkError(error, context)
        break
    }
  }

  /**
   * 处理认证错误
   * @param {Error} error 错误对象
   * @param {Object} context 上下文
   */
  handleAuthError(error, context) {
    // 清除本地认证信息
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    localStorage.removeItem('custom_id')
    
    // 延迟跳转到登录页，避免在请求过程中立即跳转
    setTimeout(() => {
      if (window.location.pathname !== '/login') {
        window.location.href = '/login'
      }
    }, 1000)
  }

  /**
   * 处理权限错误
   * @param {Error} error 错误对象
   * @param {Object} context 上下文
   */
  handlePermissionError(error, context) {
    // 可以跳转到权限不足页面或返回上一页
    console.warn('权限不足:', context)
  }

  /**
   * 处理网络错误
   * @param {Error} error 错误对象
   * @param {Object} context 上下文
   */
  handleNetworkError(error, context) {
    // 可以显示网络状态指示器
    console.warn('网络错误:', context)
  }

  /**
   * 判断是否应该重试
   * @param {Object} context 上下文
   * @returns {boolean} 是否应该重试
   */
  shouldRetry(context) {
    const key = this.getRetryKey(context)
    const attempts = this.retryAttempts.get(key) || 0
    return attempts < this.maxRetries
  }

  /**
   * 安排重试
   * @param {Object} context 上下文
   * @returns {Promise} 重试Promise
   */
  scheduleRetry(context) {
    const key = this.getRetryKey(context)
    const attempts = this.retryAttempts.get(key) || 0
    
    this.retryAttempts.set(key, attempts + 1)
    
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 这里应该重新执行原始请求
        // 具体实现取决于重试的上下文
        resolve()
      }, this.retryDelay * Math.pow(2, attempts)) // 指数退避
    })
  }

  /**
   * 获取重试键
   * @param {Object} context 上下文
   * @returns {string} 重试键
   */
  getRetryKey(context) {
    return `${context.url || 'unknown'}_${context.method || 'GET'}`
  }

  /**
   * 清除重试计数
   * @param {Object} context 上下文
   */
  clearRetryCount(context) {
    const key = this.getRetryKey(context)
    this.retryAttempts.delete(key)
  }

  /**
   * 获取错误统计
   * @returns {Object} 错误统计信息
   */
  getErrorStats() {
    const logs = this.logger.getLogs()
    const stats = {
      total: logs.length,
      byType: {},
      byLevel: {},
      recent: logs.slice(0, 10)
    }

    logs.forEach(log => {
      const type = log.data.type || 'unknown'
      const level = log.level
      
      stats.byType[type] = (stats.byType[type] || 0) + 1
      stats.byLevel[level] = (stats.byLevel[level] || 0) + 1
    })

    return stats
  }
}

// 创建全局错误处理器实例
const errorHandler = new ErrorHandler()

// 全局错误监听
window.addEventListener('error', (event) => {
  errorHandler.handle(event.error, {
    type: 'global_error',
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno
  })
})

window.addEventListener('unhandledrejection', (event) => {
  errorHandler.handle(event.reason, {
    type: 'unhandled_promise_rejection'
  })
})

export default errorHandler
export { ERROR_TYPES, ERROR_LEVELS }