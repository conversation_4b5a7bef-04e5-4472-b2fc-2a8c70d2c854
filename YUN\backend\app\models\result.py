"""量表和问卷计算结果及报告模型"""
from sqlalchemy import Column, Integer, String, Text, Float, DateTime, ForeignKey, Boolean, JSON
from sqlalchemy.orm import relationship
from datetime import datetime

from app.db.base_session import Base

class AssessmentResult(Base):
    """评估量表计算结果模型"""
    __tablename__ = "assessment_results"

    id = Column(Integer, primary_key=True, index=True)
    assessment_id = Column(Integer, ForeignKey("assessments.id", ondelete="CASCADE"), nullable=False, comment="关联的评估量表ID")
    custom_id = Column(String(50), ForeignKey("users.custom_id"), nullable=False, comment="用户ID")
    template_id = Column(Integer, ForeignKey("assessment_templates.id"), nullable=True, comment="模板ID")
    
    # 计算结果相关字段
    total_score = Column(Float, nullable=True, comment="总分")
    max_score = Column(Float, nullable=True, comment="满分")
    percentage = Column(Float, nullable=True, comment="得分百分比")
    result_level = Column(String(50), nullable=True, comment="结果等级（如：正常、轻度、中度、重度）")
    result_category = Column(String(100), nullable=True, comment="结果分类")
    interpretation = Column(Text, nullable=True, comment="结果解释")
    recommendations = Column(Text, nullable=True, comment="建议")
    
    # 详细计算数据
    dimension_scores = Column(JSON, nullable=True, comment="各维度得分，JSON格式")
    calculation_details = Column(JSON, nullable=True, comment="计算详情，JSON格式")
    raw_answers = Column(JSON, nullable=True, comment="原始答案数据，JSON格式")
    
    # 报告相关字段
    report_generated = Column(Boolean, default=False, comment="是否已生成报告")
    report_content = Column(Text, nullable=True, comment="报告内容")
    report_format = Column(String(20), default="html", comment="报告格式：html, pdf, json")
    report_template = Column(String(100), nullable=True, comment="使用的报告模板")
    
    # 状态和时间
    status = Column(String(20), default="calculated", comment="状态：calculating-计算中，calculated-已计算，error-计算错误")
    calculated_at = Column(DateTime, default=datetime.now, comment="计算时间")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")
    
    # 关联关系
    assessment = relationship("Assessment", backref="results")
    user = relationship("User", backref="assessment_results")
    template = relationship("AssessmentTemplate", backref="results")

class QuestionnaireResult(Base):
    """问卷调查计算结果模型"""
    __tablename__ = "questionnaire_results"

    id = Column(Integer, primary_key=True, index=True)
    questionnaire_id = Column(Integer, ForeignKey("questionnaires.id", ondelete="CASCADE"), nullable=False, comment="关联的问卷ID")
    response_id = Column(Integer, ForeignKey("questionnaire_responses.id", ondelete="CASCADE"), nullable=True, comment="关联的问卷回复ID")
    custom_id = Column(String(50), ForeignKey("users.custom_id"), nullable=False, comment="用户ID")
    template_id = Column(Integer, ForeignKey("questionnaire_templates.id"), nullable=True, comment="模板ID")
    
    # 计算结果相关字段
    total_score = Column(Float, nullable=True, comment="总分")
    max_score = Column(Float, nullable=True, comment="满分")
    percentage = Column(Float, nullable=True, comment="得分百分比")
    result_level = Column(String(50), nullable=True, comment="结果等级")
    result_category = Column(String(100), nullable=True, comment="结果分类")
    interpretation = Column(Text, nullable=True, comment="结果解释")
    recommendations = Column(Text, nullable=True, comment="建议")
    
    # 详细计算数据
    dimension_scores = Column(JSON, nullable=True, comment="各维度得分，JSON格式")
    calculation_details = Column(JSON, nullable=True, comment="计算详情，JSON格式")
    raw_answers = Column(JSON, nullable=True, comment="原始答案数据，JSON格式")
    
    # 报告相关字段
    report_generated = Column(Boolean, default=False, comment="是否已生成报告")
    report_content = Column(Text, nullable=True, comment="报告内容")
    report_format = Column(String(20), default="html", comment="报告格式：html, pdf, json")
    report_template = Column(String(100), nullable=True, comment="使用的报告模板")
    
    # 状态和时间
    status = Column(String(20), default="calculated", comment="状态：calculating-计算中，calculated-已计算，error-计算错误")
    calculated_at = Column(DateTime, default=datetime.now, comment="计算时间")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")
    
    # 关联关系
    questionnaire = relationship("Questionnaire", backref="results")
    response = relationship("QuestionnaireResponse", backref="results")
    user = relationship("User", backref="questionnaire_results")
    template = relationship("QuestionnaireTemplate", backref="results")

class ReportTemplate(Base):
    """报告模板模型"""
    __tablename__ = "report_templates"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, comment="模板名称")
    template_type = Column(String(50), nullable=False, comment="模板类型：assessment-量表，questionnaire-问卷")
    target_type = Column(String(50), nullable=True, comment="目标类型（具体的量表或问卷类型）")
    
    # 模板内容
    template_content = Column(Text, nullable=False, comment="模板内容（HTML格式）")
    css_styles = Column(Text, nullable=True, comment="CSS样式")
    variables = Column(JSON, nullable=True, comment="模板变量定义，JSON格式")
    
    # 配置
    is_default = Column(Boolean, default=False, comment="是否为默认模板")
    is_active = Column(Boolean, default=True, comment="是否激活")
    version = Column(String(20), nullable=True, comment="版本号")
    
    # 时间字段
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")
    created_by = Column(Integer, ForeignKey("users.id"), nullable=True, comment="创建者ID")
    
    # 关联关系
    creator = relationship("User", backref="created_report_templates")