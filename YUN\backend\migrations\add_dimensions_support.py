# -*- coding: utf-8 -*-
"""
数据库迁移脚本：添加维度支持

此脚本用于为现有数据库添加维度功能所需的字段：
1. AssessmentTemplate.dimensions - 量表模板维度定义
2. AssessmentTemplateQuestion.dimension_key - 问题维度归属
3. AssessmentResponse.dimension_scores - 评估回答维度分数
4. QuestionnaireTemplate.dimensions - 问卷模板维度定义
5. QuestionnaireTemplateQuestion.dimension_key - 问卷问题维度归属
6. QuestionnaireResponse.dimension_scores - 问卷回答维度分数
"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers
revision = 'add_dimensions_support'
down_revision = None  # 请根据实际情况设置上一个迁移的revision
branch_labels = None
depends_on = None


def upgrade():
    """添加维度支持字段"""
    
    # 1. 为 assessment_templates 表添加 dimensions 字段
    op.add_column('assessment_templates', 
                  sa.Column('dimensions', sa.JSON(), nullable=True, comment='维度定义'))
    
    # 2. 为 assessment_template_questions 表添加 dimension_key 字段
    op.add_column('assessment_template_questions', 
                  sa.Column('dimension_key', sa.String(50), nullable=True, comment='维度键值'))
    
    # 3. 为 assessment_responses 表添加 dimension_scores 字段
    op.add_column('assessment_responses', 
                  sa.Column('dimension_scores', sa.JSON(), nullable=True, comment='维度分数'))
    
    # 4. 为 questionnaire_templates 表添加 dimensions 字段
    op.add_column('questionnaire_templates', 
                  sa.Column('dimensions', sa.JSON(), nullable=True, comment='维度定义'))
    
    # 5. 为 questionnaire_template_questions 表添加 dimension_key 字段
    op.add_column('questionnaire_template_questions', 
                  sa.Column('dimension_key', sa.String(50), nullable=True, comment='维度键值'))
    
    # 6. 为 questionnaire_responses 表添加 dimension_scores 字段
    op.add_column('questionnaire_responses', 
                  sa.Column('dimension_scores', sa.JSON(), nullable=True, comment='维度分数'))
    
    # 7. 为 dimension_key 字段添加索引以提高查询性能
    op.create_index('idx_assessment_template_questions_dimension_key', 
                    'assessment_template_questions', ['dimension_key'])
    
    op.create_index('idx_questionnaire_template_questions_dimension_key', 
                    'questionnaire_template_questions', ['dimension_key'])


def downgrade():
    """回滚维度支持字段"""
    
    # 删除索引
    op.drop_index('idx_questionnaire_template_questions_dimension_key', 
                  table_name='questionnaire_template_questions')
    
    op.drop_index('idx_assessment_template_questions_dimension_key', 
                  table_name='assessment_template_questions')
    
    # 删除字段
    op.drop_column('questionnaire_responses', 'dimension_scores')
    op.drop_column('questionnaire_template_questions', 'dimension_key')
    op.drop_column('questionnaire_templates', 'dimensions')
    op.drop_column('assessment_responses', 'dimension_scores')
    op.drop_column('assessment_template_questions', 'dimension_key')
    op.drop_column('assessment_templates', 'dimensions')


# 手动执行的SQL脚本（如果不使用Alembic）
MANUAL_SQL_UPGRADE = """
-- 为 assessment_templates 表添加 dimensions 字段
ALTER TABLE assessment_templates 
ADD COLUMN dimensions JSON COMMENT '维度定义';

-- 为 assessment_template_questions 表添加 dimension_key 字段
ALTER TABLE assessment_template_questions 
ADD COLUMN dimension_key VARCHAR(50) COMMENT '维度键值';

-- 为 assessment_responses 表添加 dimension_scores 字段
ALTER TABLE assessment_responses 
ADD COLUMN dimension_scores JSON COMMENT '维度分数';

-- 为 questionnaire_templates 表添加 dimensions 字段
ALTER TABLE questionnaire_templates 
ADD COLUMN dimensions JSON COMMENT '维度定义';

-- 为 questionnaire_template_questions 表添加 dimension_key 字段
ALTER TABLE questionnaire_template_questions 
ADD COLUMN dimension_key VARCHAR(50) COMMENT '维度键值';

-- 为 questionnaire_responses 表添加 dimension_scores 字段
ALTER TABLE questionnaire_responses 
ADD COLUMN dimension_scores JSON COMMENT '维度分数';

-- 添加索引
CREATE INDEX idx_assessment_template_questions_dimension_key 
ON assessment_template_questions(dimension_key);

CREATE INDEX idx_questionnaire_template_questions_dimension_key 
ON questionnaire_template_questions(dimension_key);
"""

MANUAL_SQL_DOWNGRADE = """
-- 删除索引
DROP INDEX idx_questionnaire_template_questions_dimension_key 
ON questionnaire_template_questions;

DROP INDEX idx_assessment_template_questions_dimension_key 
ON assessment_template_questions;

-- 删除字段
ALTER TABLE questionnaire_responses DROP COLUMN dimension_scores;
ALTER TABLE questionnaire_template_questions DROP COLUMN dimension_key;
ALTER TABLE questionnaire_templates DROP COLUMN dimensions;
ALTER TABLE assessment_responses DROP COLUMN dimension_scores;
ALTER TABLE assessment_template_questions DROP COLUMN dimension_key;
ALTER TABLE assessment_templates DROP COLUMN dimensions;
"""


def print_manual_instructions():
    """打印手动执行说明"""
    print("\n=== 数据库迁移说明 ===")
    print("\n如果您使用Alembic进行数据库迁移，请执行：")
    print("alembic upgrade head")
    
    print("\n如果您需要手动执行SQL，请使用以下脚本：")
    print("\n--- 升级脚本 ---")
    print(MANUAL_SQL_UPGRADE)
    
    print("\n--- 回滚脚本 ---")
    print(MANUAL_SQL_DOWNGRADE)
    
    print("\n=== 注意事项 ===")
    print("1. 执行迁移前请备份数据库")
    print("2. 确保数据库连接正常")
    print("3. 在生产环境中请谨慎执行")
    print("4. 迁移完成后请验证功能是否正常")


if __name__ == "__main__":
    print_manual_instructions()