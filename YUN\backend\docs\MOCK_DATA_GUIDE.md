# 模拟数据管理指南

## 概述

本系统实现了统一的模拟数据管理，用于在开发和测试环境中提供一致的模拟数据。所有硬编码的模拟数据已被重构为使用统一的模拟数据管理器。

## 核心组件

### MockDataManager 类

位置：`app/core/mock_data_manager.py`

这是模拟数据管理的核心类，负责：
- 统一管理所有模拟数据
- 提供环境变量控制的开关
- 确保数据的一致性和可维护性

### 主要功能

1. **仪表盘数据模拟**
   - `get_mock_dashboard_stats()` - 仪表盘统计数据
   - `get_mock_weight_data()` - 体重趋势数据
   - `get_mock_bp_data()` - 血压趋势数据
   - `get_mock_exam_dist_data()` - 检查分布数据
   - `get_mock_health_index_data()` - 健康指数数据
   - `get_mock_timeline_data()` - 时间线数据

2. **服务统计数据模拟**
   - `get_mock_service_stats()` - 基础服务统计
   - `get_mock_public_metrics()` - 公开指标数据
   - `get_mock_detailed_service_stats()` - 详细服务统计

3. **系统监控数据模拟**
   - `get_mock_system_metrics()` - 系统性能指标
   - `get_mock_health_status()` - 系统健康状态

## 配置选项

### 环境变量

在 `.env` 文件中配置以下选项：

```env
# 主开关 - 启用/禁用模拟数据管理
MOCK_DATA_ENABLED=true

# 细分控制
MOCK_DASHBOARD_ENABLED=true
MOCK_SERVICE_STATS_ENABLED=true
MOCK_HEALTH_MONITOR_ENABLED=true
```

### 配置说明

- `MOCK_DATA_ENABLED`: 主开关，控制整个模拟数据系统
- `MOCK_DASHBOARD_ENABLED`: 控制仪表盘相关的模拟数据
- `MOCK_SERVICE_STATS_ENABLED`: 控制服务统计相关的模拟数据
- `MOCK_HEALTH_MONITOR_ENABLED`: 控制健康监控相关的模拟数据

## 使用方式

### 在代码中使用

```python
from app.core.mock_data_manager import (
    is_mock_enabled,
    get_mock_dashboard_stats,
    get_mock_service_stats
)

# 检查是否启用模拟数据
if is_mock_enabled():
    # 获取模拟数据
    mock_data = get_mock_dashboard_stats("3months")
    if mock_data:
        return mock_data

# 如果模拟数据未启用或不可用，使用原有逻辑
return original_data_logic()
```

### 数据结构

所有模拟数据都遵循与真实API相同的数据结构，确保前端兼容性。

## 已重构的文件

### API端点文件

1. **dashboard.py** - 仪表盘API端点
   - 重构了所有硬编码的模拟数据生成函数
   - 使用统一的模拟数据管理器

2. **service_stats.py** - 服务统计API端点
   - 重构了服务统计相关的模拟数据
   - 统一了错误处理时的模拟数据返回

### 核心模块文件

3. **health_monitor.py** - 健康监控模块
   - 重构了系统指标的模拟数据生成
   - 统一了异常情况下的模拟数据处理

## 优势

### 1. 统一管理
- 所有模拟数据集中在一个地方管理
- 避免了代码重复和不一致
- 便于维护和更新

### 2. 环境控制
- 通过环境变量灵活控制模拟数据的启用
- 支持细粒度的功能开关
- 便于不同环境的配置

### 3. 数据一致性
- 确保所有模拟数据的格式和结构一致
- 减少了数据不匹配的问题
- 提高了测试的可靠性

### 4. 可扩展性
- 新的模拟数据类型可以轻松添加
- 支持动态数据生成
- 便于未来功能扩展

## 开发指南

### 添加新的模拟数据类型

1. 在 `MockDataManager` 类中添加新的方法：

```python
def get_mock_new_feature_data(self) -> Optional[Dict[str, Any]]:
    """获取新功能的模拟数据"""
    if not self.is_enabled or not self._get_env_bool('MOCK_NEW_FEATURE_ENABLED', True):
        return None
    
    return {
        "feature_data": "mock_value",
        "timestamp": datetime.now().isoformat()
    }
```

2. 在模块级别添加便捷函数：

```python
def get_mock_new_feature_data() -> Optional[Dict[str, Any]]:
    """获取新功能的模拟数据"""
    return mock_data_manager.get_mock_new_feature_data()
```

3. 在相关的API端点中使用：

```python
from app.core.mock_data_manager import get_mock_new_feature_data

# 在API函数中
mock_data = get_mock_new_feature_data()
if mock_data:
    return mock_data
```

### 最佳实践

1. **保持数据结构一致**：确保模拟数据与真实数据具有相同的结构
2. **使用环境变量控制**：为新的模拟数据类型添加相应的环境变量开关
3. **提供回退机制**：当模拟数据不可用时，提供合理的默认值
4. **文档更新**：添加新功能时及时更新文档

## 故障排除

### 常见问题

1. **模拟数据未生效**
   - 检查 `MOCK_DATA_ENABLED` 是否设置为 `true`
   - 确认相关的子开关是否启用
   - 检查环境变量是否正确加载

2. **数据格式不匹配**
   - 确认模拟数据结构与API规范一致
   - 检查数据类型是否正确

3. **性能问题**
   - 模拟数据生成应该是轻量级的
   - 避免在模拟数据中进行复杂计算

### 调试技巧

1. 使用日志记录模拟数据的使用情况
2. 在开发环境中启用详细的调试信息
3. 使用单元测试验证模拟数据的正确性

## 总结

统一的模拟数据管理系统提供了一个可维护、可扩展的解决方案，用于管理应用程序中的所有模拟数据。通过环境变量控制和统一的API，开发者可以轻松地在不同环境中切换真实数据和模拟数据，提高了开发效率和测试质量。