#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试移动端状态过滤修复效果
"""

import requests
import json
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_mobile_api_status_filtering():
    """测试移动端API状态过滤"""
    base_url = "http://127.0.0.1:8000"
    
    # 测试用户登录信息
    login_data = {
        "username": "markey",
        "password": "markey0308@163"
    }
    
    try:
        # 1. 登录获取token
        logger.info("=== 步骤1: 用户登录 ===")
        login_response = requests.post(f"{base_url}/api/auth/login", data=login_data)
        logger.info(f"登录响应状态码: {login_response.status_code}")
        
        if login_response.status_code != 200:
            logger.error(f"登录失败: {login_response.text}")
            return
            
        login_result = login_response.json()
        logger.info(f"登录响应内容: {login_result}")
        
        # 尝试不同的token获取方式
        token = None
        if 'data' in login_result:
            token = login_result['data'].get('access_token') or login_result['data'].get('token')
        else:
            token = login_result.get('access_token') or login_result.get('token')
            
        if not token:
            logger.error(f"未获取到访问令牌，响应内容: {login_result}")
            return
            
        logger.info("登录成功，获取到访问令牌")
        
        # 设置请求头
        headers = {
            "Authorization": f"Bearer {token}",
            "X-User-ID": "SM_006",
            "Content-Type": "application/json"
        }
        
        # 2. 获取所有量表
        logger.info("\n=== 步骤2: 获取所有量表 ===")
        assessments_response = requests.get(f"{base_url}/api/mobile/assessments", headers=headers)
        logger.info(f"获取量表响应状态码: {assessments_response.status_code}")
        
        if assessments_response.status_code == 200:
            assessments_result = assessments_response.json()
            assessments_data = assessments_result.get('data', {})
            assessments = assessments_data.get('assessments', []) if isinstance(assessments_data, dict) else assessments_data
            
            logger.info(f"获取到量表总数: {len(assessments)}")
            
            # 统计不同状态的量表
            pending_assessments = [a for a in assessments if a.get('status') == 'pending']
            completed_assessments = [a for a in assessments if a.get('status') == 'completed']
            
            logger.info(f"Pending状态量表数量: {len(pending_assessments)}")
            logger.info(f"Completed状态量表数量: {len(completed_assessments)}")
            
            # 显示每个量表的详细信息
            for i, assessment in enumerate(assessments):
                logger.info(f"量表{i+1}: ID={assessment.get('id')}, 标题={assessment.get('title', 'N/A')}, 状态={assessment.get('status', 'N/A')}")
        else:
            logger.error(f"获取量表失败: {assessments_response.text}")
            
        # 3. 获取所有问卷
        logger.info("\n=== 步骤3: 获取所有问卷 ===")
        questionnaires_response = requests.get(f"{base_url}/api/mobile/questionnaires", headers=headers)
        logger.info(f"获取问卷响应状态码: {questionnaires_response.status_code}")
        
        if questionnaires_response.status_code == 200:
            questionnaires_result = questionnaires_response.json()
            questionnaires_data = questionnaires_result.get('data', {})
            questionnaires = questionnaires_data.get('questionnaires', []) if isinstance(questionnaires_data, dict) else questionnaires_data
            
            logger.info(f"获取到问卷总数: {len(questionnaires)}")
            
            # 统计不同状态的问卷
            pending_questionnaires = [q for q in questionnaires if q.get('status') == 'pending']
            completed_questionnaires = [q for q in questionnaires if q.get('status') == 'completed']
            
            logger.info(f"Pending状态问卷数量: {len(pending_questionnaires)}")
            logger.info(f"Completed状态问卷数量: {len(completed_questionnaires)}")
            
            # 显示每个问卷的详细信息
            for i, questionnaire in enumerate(questionnaires):
                logger.info(f"问卷{i+1}: ID={questionnaire.get('id')}, 标题={questionnaire.get('title', 'N/A')}, 状态={questionnaire.get('status', 'N/A')}")
        else:
            logger.error(f"获取问卷失败: {questionnaires_response.text}")
            
        # 4. 模拟移动端标签页过滤逻辑
        logger.info("\n=== 步骤4: 模拟移动端标签页过滤逻辑 ===")
        
        if 'assessments' in locals():
            # 评估量表tab - 只显示pending状态
            assessment_tab_data = [a for a in assessments if a.get('status') == 'pending']
            logger.info(f"评估量表tab应显示: {len(assessment_tab_data)} 条pending状态量表")
            
            # 历史记录tab - 只显示completed状态
            history_assessments = [a for a in assessments if a.get('status') == 'completed']
            logger.info(f"历史记录tab应显示: {len(history_assessments)} 条completed状态量表")
            
        if 'questionnaires' in locals():
            # 问卷tab - 只显示pending状态
            questionnaire_tab_data = [q for q in questionnaires if q.get('status') == 'pending']
            logger.info(f"问卷tab应显示: {len(questionnaire_tab_data)} 条pending状态问卷")
            
            # 历史记录tab - 只显示completed状态
            history_questionnaires = [q for q in questionnaires if q.get('status') == 'completed']
            logger.info(f"历史记录tab应显示: {len(history_questionnaires)} 条completed状态问卷")
            
        logger.info("\n=== 测试完成 ===")
        logger.info("修复说明:")
        logger.info("1. 移动端现在会获取所有状态的量表和问卷")
        logger.info("2. 在'评估量表'tab中只显示pending状态的量表")
        logger.info("3. 在'问卷'tab中只显示pending状态的问卷")
        logger.info("4. 在'历史记录'tab中显示completed状态的量表和问卷")
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    test_mobile_api_status_filtering()