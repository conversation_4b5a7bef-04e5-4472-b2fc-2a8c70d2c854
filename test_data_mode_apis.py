#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据模式管理API的所有功能
"""

import requests
import json

# API基础URL
BASE_URL = "http://localhost:8006/api/data-mode"

def test_api(endpoint, method="GET", data=None):
    """测试API接口"""
    url = f"{BASE_URL}{endpoint}"
    try:
        if method == "GET":
            response = requests.get(url)
        elif method == "POST":
            response = requests.post(url, json=data)
        
        print(f"\n=== {method} {endpoint} ===")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"错误响应: {response.text}")
            
        return response
    except Exception as e:
        print(f"请求失败: {e}")
        return None

def main():
    print("开始测试数据模式管理API...")
    
    # 1. 测试获取状态
    print("\n1. 测试获取数据模式状态")
    status_response = test_api("/status")
    
    # 2. 测试模式测试
    print("\n2. 测试数据模式测试")
    test_response = test_api("/test")
    
    # 3. 测试切换历史
    print("\n3. 测试获取切换历史")
    history_response = test_api("/history")
    
    # 4. 测试模式切换
    print("\n4. 测试切换到模拟数据模式")
    switch_data = {
        "mode": "mock",
        "reason": "测试切换功能"
    }
    switch_response = test_api("/switch", "POST", switch_data)
    
    # 5. 再次获取状态确认切换
    print("\n5. 确认切换后的状态")
    status_after_switch = test_api("/status")
    
    # 6. 切换回自动模式
    print("\n6. 切换回自动模式")
    switch_back_data = {
        "mode": "auto",
        "reason": "测试完成，恢复自动模式"
    }
    switch_back_response = test_api("/switch", "POST", switch_back_data)
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()