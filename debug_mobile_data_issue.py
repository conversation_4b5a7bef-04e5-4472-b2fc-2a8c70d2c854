#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os
from datetime import datetime

def check_database_data():
    # 检查可能的数据库文件位置
    possible_db_paths = [
        "c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend\\app.db",
        "c:\\Users\\<USER>\\Desktop\\health-Trea\\app.db",
        "c:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend\\health_management.db"
    ]
    
    db_path = None
    for path in possible_db_paths:
        if os.path.exists(path):
            db_path = path
            print(f"找到数据库文件: {path}")
            break
    
    if not db_path:
        print("未找到数据库文件")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\n=== 检查用户SM_008的数据 ===")
        
        # 检查用户信息
        cursor.execute("SELECT id, custom_id, username, full_name FROM users WHERE custom_id = 'SM_008'")
        user = cursor.fetchone()
        if user:
            print(f"用户信息: ID={user[0]}, custom_id={user[1]}, username={user[2]}, full_name={user[3]}")
        else:
            print("未找到用户SM_008")
            return
        
        # 检查量表分发记录
        print("\n=== 量表分发记录 ===")
        cursor.execute("""
            SELECT ad.id, ad.assessment_id, ad.custom_id, ad.status, ad.due_date, ad.created_at,
                   a.name as assessment_name, at.name as template_name
            FROM assessment_distributions ad
            LEFT JOIN assessments a ON ad.assessment_id = a.id
            LEFT JOIN assessment_templates at ON a.template_id = at.id
            WHERE ad.custom_id = 'SM_008'
            ORDER BY ad.created_at DESC
        """)
        
        distributions = cursor.fetchall()
        print(f"总共找到 {len(distributions)} 条量表分发记录:")
        for dist in distributions:
            print(f"  ID: {dist[0]}, 量表ID: {dist[1]}, 状态: {dist[3]}, 截止日期: {dist[4]}")
            print(f"  量表名称: {dist[6] or '无'}, 模板名称: {dist[7] or '无'}")
            print(f"  创建时间: {dist[5]}")
            print()
        
        # 检查问卷分发记录
        print("\n=== 问卷分发记录 ===")
        cursor.execute("""
            SELECT qd.id, qd.questionnaire_id, qd.custom_id, qd.status, qd.due_date, qd.created_at,
                   q.title as questionnaire_title
            FROM questionnaire_distributions qd
            LEFT JOIN questionnaires q ON qd.questionnaire_id = q.id
            WHERE qd.custom_id = 'SM_008'
            ORDER BY qd.created_at DESC
        """)
        
        q_distributions = cursor.fetchall()
        print(f"总共找到 {len(q_distributions)} 条问卷分发记录:")
        for dist in q_distributions:
            print(f"  ID: {dist[0]}, 问卷ID: {dist[1]}, 状态: {dist[3]}, 截止日期: {dist[4]}")
            print(f"  问卷标题: {dist[6] or '无'}")
            print(f"  创建时间: {dist[5]}")
            print()
        
        # 检查量表结果记录
        print("\n=== 量表结果记录 ===")
        cursor.execute("""
            SELECT ar.id, ar.assessment_id, ar.custom_id, ar.total_score, ar.result_level, 
                   ar.report_generated, ar.calculated_at,
                   a.name as assessment_name
            FROM assessment_results ar
            LEFT JOIN assessments a ON ar.assessment_id = a.id
            WHERE ar.custom_id = 'SM_008'
            ORDER BY ar.calculated_at DESC
        """)
        
        results = cursor.fetchall()
        print(f"总共找到 {len(results)} 条量表结果记录:")
        for result in results:
            print(f"  ID: {result[0]}, 量表ID: {result[1]}, 总分: {result[3]}, 等级: {result[4]}")
            print(f"  报告生成: {result[5]}, 计算时间: {result[6]}")
            print(f"  量表名称: {result[7] or '无'}")
            print()
        
        # 检查问卷结果记录
        print("\n=== 问卷结果记录 ===")
        cursor.execute("""
            SELECT qr.id, qr.questionnaire_id, qr.custom_id, qr.total_score, qr.result_level,
                   qr.report_generated, qr.calculated_at,
                   q.title as questionnaire_title
            FROM questionnaire_results qr
            LEFT JOIN questionnaires q ON qr.questionnaire_id = q.id
            WHERE qr.custom_id = 'SM_008'
            ORDER BY qr.calculated_at DESC
        """)
        
        q_results = cursor.fetchall()
        print(f"总共找到 {len(q_results)} 条问卷结果记录:")
        for result in q_results:
            print(f"  ID: {result[0]}, 问卷ID: {result[1]}, 总分: {result[3]}, 等级: {result[4]}")
            print(f"  报告生成: {result[5]}, 计算时间: {result[6]}")
            print(f"  问卷标题: {result[7] or '无'}")
            print()
        
        conn.close()
        
    except Exception as e:
        print(f"检查数据时出错: {e}")

if __name__ == "__main__":
    check_database_data()