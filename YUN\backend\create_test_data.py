#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试数据 - 为用户SM_001添加问卷和评估记录
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from app.db.base_session import SessionLocal
from app.models.user import User
from app.models.questionnaire import Questionnaire
from app.models.assessment import Assessment
from app.models.alert import Alert

def create_test_data():
    """创建测试数据"""
    db: Session = SessionLocal()
    
    try:
        # 查找用户SM_001
        user = db.query(User).filter(User.custom_id == "SM_001").first()
        if not user:
            print("用户SM_001不存在")
            return
        
        print(f"找到用户: {user.custom_id} - {user.full_name}")
        
        # 创建问卷记录
        questionnaires_data = [
            {
                "name": "健康状况调查问卷",
                "questionnaire_type": "health_survey",
                "notes": "基础健康状况调查",
                "status": "completed",
                "version": "1.0"
            },
            {
                "name": "生活方式问卷",
                "questionnaire_type": "lifestyle",
                "notes": "生活习惯和方式调查",
                "status": "completed",
                "version": "1.0"
            },
            {
                "name": "饮食习惯问卷",
                "questionnaire_type": "diet",
                "notes": "饮食习惯调查",
                "status": "in_progress",
                "version": "1.0"
            }
        ]
        
        for i, q_data in enumerate(questionnaires_data):
            questionnaire = Questionnaire(
                custom_id=user.custom_id,
                title=q_data["name"],
                questionnaire_type=q_data["questionnaire_type"],
                description=q_data["notes"],
                status=q_data["status"],
                version=q_data["version"],
                created_at=datetime.now() - timedelta(days=10-i*2),
                updated_at=datetime.now() - timedelta(days=9-i*2)
            )
            db.add(questionnaire)
            print(f"创建问卷: {q_data['name']}")
        
        # 创建评估记录
        assessments_data = [
            {
                "name": "焦虑自评量表(SAS)",
                "assessment_type": "anxiety",
                "notes": "焦虑程度评估",
                "status": "completed",
                "version": "1.0"
            },
            {
                "name": "抑郁自评量表(SDS)",
                "assessment_type": "depression",
                "notes": "抑郁程度评估",
                "status": "completed",
                "version": "1.0"
            },
            {
                "name": "睡眠质量评估",
                "assessment_type": "sleep",
                "notes": "睡眠质量评估",
                "status": "in_progress",
                "version": "1.0"
            }
        ]
        
        for i, a_data in enumerate(assessments_data):
            assessment = Assessment(
                custom_id=user.custom_id,
                name=a_data["name"],
                assessment_type=a_data["assessment_type"],
                notes=a_data["notes"],
                status=a_data["status"],
                version=a_data["version"],
                created_at=datetime.now() - timedelta(days=8-i*2),
                updated_at=datetime.now() - timedelta(days=7-i*2),
                completed_at=datetime.now() - timedelta(days=7-i*2) if a_data["status"] == "completed" else None
            )
            db.add(assessment)
            print(f"创建评估: {a_data['name']}")
        
        # 为用户 SM_001 创建一些告警
        for i in range(3):
            alert = Alert(
                custom_id=user.custom_id,
                title=f"测试告警 {i+1}",
                description=f"这是给 {user.full_name} 的测试告警内容。",
                severity="info",
                status="active",
                created_at=datetime.now() - timedelta(days=1)
            )
            db.add(alert)
            print(f"创建告警: 测试告警 {i+1}")

        # 提交事务
        db.commit()
        
        # 验证数据
        questionnaire_count = db.query(Questionnaire).filter(Questionnaire.custom_id == user.custom_id).count()
        assessment_count = db.query(Assessment).filter(Assessment.custom_id == user.custom_id).count()
        alert_count = db.query(Alert).filter(Alert.custom_id == user.custom_id).count()
        
        print(f"用户 {user.custom_id} 的问卷记录数: {questionnaire_count}")
        print(f"用户 {user.custom_id} 的评估记录数: {assessment_count}")
        print(f"用户 {user.custom_id} 的告警记录数: {alert_count}")
        
    except Exception as e:
        print(f"创建测试数据时出错: {str(e)}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_test_data()