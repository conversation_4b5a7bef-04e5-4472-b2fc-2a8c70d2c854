# -*- coding: utf-8 -*-
"""
调试API响应数据
分析为什么量表数据没有正确返回
"""

import requests
import json
from datetime import datetime
import sys
import os

# 添加后端路径以导入MockDataManager
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
try:
    from backend.app.core.mock_data_manager import get_api_test_cases_config
except ImportError:
    # 如果导入失败，使用默认配置
    def get_api_test_cases_config():
        return []

# 配置
BASE_URL = "http://localhost:8006"
LOGIN_URL = f"{BASE_URL}/api/auth/login"
API_URL = f"{BASE_URL}/api/user-health-records/SM_008"

def debug_api_response():
    """调试API响应数据"""
    print("=== 调试API响应数据 ===")
    
    # 登录获取token
    login_data = {
        "username": "markey",
        "password": "markey0308@163"
    }
    
    print("1. 正在登录...")
    login_response = requests.post(LOGIN_URL, data=login_data)
    print(f"登录状态码: {login_response.status_code}")
    
    if login_response.status_code != 200:
        print(f"登录失败: {login_response.text}")
        return
    
    login_data_response = login_response.json()
    token = login_data_response.get("access_token")
    if not token:
        print("未能获取到token")
        return
    print(f"获取到token: {token[:20]}...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 从MockDataManager获取测试用例配置
    test_cases = get_api_test_cases_config()
    
    # 如果无法获取配置，使用默认配置
    if not test_cases:
        test_cases = [
            {
                "name": "只获取问卷数据",
                "params": {"record_type": "questionnaire", "status": "pending"}
            },
            {
                "name": "只获取量表数据", 
                "params": {"record_type": "assessment", "status": "pending"}
            },
            {
                "name": "获取问卷和量表数据",
                "params": {"record_type": "questionnaire,assessment", "status": "pending"}
            },
            {
                "name": "获取所有已完成的问卷和量表",
                "params": {"record_type": "questionnaire,assessment", "status": "completed"}
            },
            {
                "name": "获取所有问卷和量表（不限状态）",
                "params": {"record_type": "questionnaire,assessment"}
            }
        ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}...")
        
        response = requests.get(API_URL, headers=headers, params=test_case['params'])
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"响应数据结构: {list(data.keys())}")
                
                # 分析数据结构
                if "data" in data:
                    data_content = data["data"]
                    print(f"data字段结构: {list(data_content.keys()) if isinstance(data_content, dict) else type(data_content)}")
                    
                    if "records" in data_content:
                        records = data_content["records"]
                        print(f"找到 {len(records)} 条记录")
                        
                        # 统计记录类型
                        questionnaire_count = 0
                        assessment_count = 0
                        
                        for record in records:
                            record_type = record.get('type')
                            if record_type == 'questionnaire':
                                questionnaire_count += 1
                            elif record_type == 'assessment':
                                assessment_count += 1
                        
                        print(f"  问卷数量: {questionnaire_count}")
                        print(f"  量表数量: {assessment_count}")
                        
                        # 显示前3条记录的详细信息
                        if records:
                            print("\n  前3条记录详情:")
                            for j, record in enumerate(records[:3]):
                                print(f"    记录 {j+1}:")
                                print(f"      ID: {record.get('id')}")
                                print(f"      名称: {record.get('name')}")
                                print(f"      类型: {record.get('type')}")
                                print(f"      状态: {record.get('status')}")
                                if record.get('type') == 'questionnaire':
                                    print(f"      问卷ID: {record.get('questionnaire_id')}")
                                elif record.get('type') == 'assessment':
                                    print(f"      评估ID: {record.get('assessment_id')}")
                
                elif "items" in data:
                    items = data["items"]
                    print(f"找到 {len(items)} 条记录")
                    
                    # 统计记录类型
                    questionnaire_count = 0
                    assessment_count = 0
                    
                    for item in items:
                        item_type = item.get('type')
                        if item_type == 'questionnaire':
                            questionnaire_count += 1
                        elif item_type == 'assessment':
                            assessment_count += 1
                    
                    print(f"  问卷数量: {questionnaire_count}")
                    print(f"  量表数量: {assessment_count}")
                    
                    # 显示前3条记录的详细信息
                    if items:
                        print("\n  前3条记录详情:")
                        for j, item in enumerate(items[:3]):
                            print(f"    记录 {j+1}:")
                            print(f"      ID: {item.get('id')}")
                            print(f"      名称: {item.get('name')}")
                            print(f"      类型: {item.get('type')}")
                            print(f"      状态: {item.get('status')}")
                            if item.get('type') == 'questionnaire':
                                print(f"      问卷ID: {item.get('questionnaire_id')}")
                            elif item.get('type') == 'assessment':
                                print(f"      评估ID: {item.get('assessment_id')}")
                
                else:
                    print("  未找到records或items字段")
                    print(f"  完整响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
                    
            except json.JSONDecodeError as e:
                print(f"JSON解析错误: {e}")
                print(f"原始响应: {response.text}")
        else:
            print(f"请求失败: {response.text}")
    
    print("\n=== 调试完成 ===")

if __name__ == "__main__":
    debug_api_response()