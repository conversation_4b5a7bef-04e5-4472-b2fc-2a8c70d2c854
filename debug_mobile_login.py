#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'mobile'))

from utils.cloud_api import get_cloud_api
import logging

# 设置日志级别为DEBUG以查看详细信息
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def test_mobile_login():
    """测试移动端登录功能"""
    print("=== 测试移动端登录功能 ===")
    
    # 获取API实例
    api = get_cloud_api()
    print(f"当前使用的服务器: {api.base_url}")
    
    # 测试登录
    print("\n发送登录请求...")
    result = api.authenticate(username="admin", password="admin123")
    
    print(f"\n登录结果: {result}")
    
    if result and result.get('status') == 'success':
        print("✓ 登录成功")
        print(f"Token: {result.get('token', '')[:20]}...")
        print(f"User ID: {result.get('user_id')}")
        print(f"Custom ID: {result.get('custom_id')}")
    else:
        print("✗ 登录失败")
        if result:
            print(f"错误信息: {result.get('message', '未知错误')}")
        else:
            print("返回结果为空")

if __name__ == "__main__":
    test_mobile_login()