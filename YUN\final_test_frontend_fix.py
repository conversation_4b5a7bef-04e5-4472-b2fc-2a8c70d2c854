# -*- coding: utf-8 -*-
"""
最终测试：验证前端修复后的量表列表显示功能
"""

import requests
import json
from datetime import datetime

# 配置
BASE_URL = "http://127.0.0.1:8006"
USERNAME = "admin"
PASSWORD = "admin123"

def login_and_get_token():
    """登录并获取token"""
    try:
        login_data = {
            "username": USERNAME,
            "password": PASSWORD
        }
        
        response = requests.post(f"{BASE_URL}/auth/login", data=login_data)
        print(f"登录请求状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"登录响应数据: {data}")
                
                if data and isinstance(data, dict):
                    token = data.get('access_token') or data.get('token')
                    if token:
                        print(f"✅ 登录成功，获取到token: {token[:20]}...")
                        return token
                    else:
                        print(f"❌ 登录响应中没有找到token字段")
                        return None
                else:
                    print(f"❌ 登录响应数据格式异常")
                    return None
                    
            except Exception as json_error:
                print(f"❌ 登录响应JSON解析失败: {json_error}")
                print(f"原始响应: {response.text}")
                return None
        else:
            print(f"❌ 登录失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 登录请求异常: {str(e)}")
        return None

def test_api_response_structure(token):
    """测试API响应结构"""
    print("\n=== 测试API响应结构 ===")
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(f"{BASE_URL}/api/clinical-scales/standard-assessments", headers=headers)
        print(f"API请求状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"\n📊 API响应数据结构分析:")
            print(f"响应数据类型: {type(data)}")
            
            if isinstance(data, dict):
                print(f"响应字段: {list(data.keys())}")
                
                if 'data' in data and isinstance(data['data'], list):
                    assessments = data['data']
                    print(f"\n📋 量表数据分析:")
                    print(f"量表总数: {len(assessments)}")
                    
                    if assessments:
                        first_assessment = assessments[0]
                        print(f"\n🔍 第一个量表的字段结构:")
                        for key, value in first_assessment.items():
                            print(f"  {key}: {type(value).__name__} = {str(value)[:50]}{'...' if len(str(value)) > 50 else ''}")
                        
                        print(f"\n🎯 前端字段映射验证:")
                        frontend_mapping = {
                            'id': first_assessment.get('template_id') or first_assessment.get('id'),
                            'name': first_assessment.get('template_name') or first_assessment.get('name'),
                            'category': first_assessment.get('category', '未分类'),
                            'item_count': first_assessment.get('question_count') or first_assessment.get('item_count', 0),
                            'target': first_assessment.get('target', '通用'),
                            'created_at': first_assessment.get('created_at') or first_assessment.get('create_time', '')
                        }
                        
                        print("映射后的前端数据:")
                        for key, value in frontend_mapping.items():
                            status = "✅" if value else "❌"
                            print(f"  {status} {key}: {value}")
                        
                        # 检查是否所有必要字段都有值
                        missing_fields = [k for k, v in frontend_mapping.items() if not v]
                        if missing_fields:
                            print(f"\n⚠️  缺失的字段: {missing_fields}")
                        else:
                            print(f"\n✅ 所有必要字段都已正确映射")
                            
                    else:
                        print("❌ 量表数据为空")
                else:
                    print("❌ 响应中没有找到data字段或data不是数组")
            else:
                print("❌ 响应数据不是对象格式")
                
        else:
            print(f"❌ API请求失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ API请求异常: {str(e)}")

def generate_frontend_fix_summary():
    """生成前端修复总结"""
    print("\n=== 前端修复总结 ===")
    
    print("\n🔧 已实施的修复:")
    print("1. ✅ 在fetchAssessments函数中添加了字段映射逻辑")
    print("2. ✅ 将API字段(template_id, template_name等)映射到前端期望字段(id, name等)")
    print("3. ✅ 保留了原始数据以备其他功能使用")
    print("4. ✅ 添加了默认值处理，避免空值显示")
    
    print("\n📋 字段映射对照表:")
    mappings = [
        ("template_id", "id", "量表ID"),
        ("template_name", "name", "量表名称"),
        ("category", "category", "分类"),
        ("question_count", "item_count", "项目数"),
        ("target", "target", "适用对象"),
        ("created_at", "created_at", "创建时间")
    ]
    
    for api_field, frontend_field, description in mappings:
        print(f"  {api_field} → {frontend_field} ({description})")
    
    print("\n🎯 预期效果:")
    print("• 前端页面应该能正确显示量表列表")
    print("• 每个量表的ID、名称、分类、项目数、适用对象、创建时间都应正确显示")
    print("• 预览、开始评估等功能应该正常工作")
    
    print("\n🔍 验证步骤:")
    print("1. 打开浏览器访问 http://localhost:8081")
    print("2. 登录系统(用户名: admin, 密码: admin123)")
    print("3. 进入评估量表页面")
    print("4. 检查'可用量表'列表是否正确显示")
    print("5. 验证各个字段是否有正确的值")

def main():
    print("=== 前端修复验证测试 ===")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"后端地址: {BASE_URL}")
    
    # 登录获取token
    token = login_and_get_token()
    if not token:
        print("❌ 无法获取token，测试终止")
        return
    
    # 测试API响应结构
    test_api_response_structure(token)
    
    # 生成修复总结
    generate_frontend_fix_summary()
    
    print("\n=== 测试完成 ===")
    print("\n💡 如果API数据结构正确，前端应该能正常显示量表列表")
    print("请在浏览器中验证实际效果")

if __name__ == "__main__":
    main()