#!/usr/bin/env python3
"""
简单的模拟数据测试脚本
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.core.mock_data_manager import (
    BackendMockDataManager,
    MockDataManager,
    is_mock_enabled,
    get_mock_dashboard_stats,
    get_mock_weight_data,
    get_mock_bp_data
)

def test_mock_data():
    """测试模拟数据功能"""
    print("🧪 测试模拟数据管理器")
    print("=" * 50)
    
    # 设置环境变量启用模拟数据
    os.environ['ENABLE_MOCK_DATA'] = 'true'
    os.environ['ENVIRONMENT'] = 'development'
    
    print(f"✅ 模拟数据启用状态: {is_mock_enabled()}")
    
    # 测试BackendMockDataManager
    manager = BackendMockDataManager()
    print(f"✅ BackendMockDataManager 启用状态: {manager.is_enabled()}")
    
    # 测试MockDataManager别名
    alias_manager = MockDataManager()
    print(f"✅ MockDataManager 别名启用状态: {alias_manager.is_enabled()}")
    
    # 测试模块级函数
    print("\n📊 测试数据生成:")
    
    try:
        dashboard_data = get_mock_dashboard_stats()
        print(f"✅ 仪表盘数据: {type(dashboard_data)} - {bool(dashboard_data)}")
        if dashboard_data:
            print(f"   包含字段: {list(dashboard_data.keys())}")
    except Exception as e:
        print(f"❌ 仪表盘数据生成失败: {e}")
    
    try:
        weight_data = get_mock_weight_data()
        print(f"✅ 体重数据: {type(weight_data)} - {bool(weight_data)}")
        if weight_data:
            print(f"   包含字段: {list(weight_data.keys())}")
    except Exception as e:
        print(f"❌ 体重数据生成失败: {e}")
    
    try:
        bp_data = get_mock_bp_data()
        print(f"✅ 血压数据: {type(bp_data)} - {bool(bp_data)}")
        if bp_data:
            print(f"   包含字段: {list(bp_data.keys())}")
    except Exception as e:
        print(f"❌ 血压数据生成失败: {e}")
    
    print("\n🎉 测试完成！")

if __name__ == '__main__':
    test_mock_data()