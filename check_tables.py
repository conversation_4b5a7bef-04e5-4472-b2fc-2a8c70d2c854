from sqlalchemy import create_engine, text

import sqlite3
import os

# 数据库路径列表，按优先级排序
db_paths = [
    'c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db'
]

db_path = None
for path in db_paths:
    if os.path.exists(path):
        print(f"找到数据库文件: {path}")
        db_path = path
        break

if not db_path:
    print("未找到任何数据库文件")
    print("检查的路径:")
    for path in db_paths:
        print(f"  {path}")
    exit(1)

try:
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 获取所有表名
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    
    print(f'使用数据库: {db_path}')
    print('数据库中的表:')
    
    if not tables:
        print('  数据库中没有表')
        # 检查是否有其他对象
        cursor.execute("SELECT name, type FROM sqlite_master")
        all_objects = cursor.fetchall()
        if all_objects:
            print('\n数据库中的其他对象:')
            for obj in all_objects:
                print(f'  {obj[0]} ({obj[1]})')
        else:
            print('  数据库完全为空')
    else:
        for table in tables:
            print(f'  {table[0]}')
        
        # 显示每个表的结构和数据量
        print('\n表结构详情:')
        for table in tables:
            table_name = table[0]
            print(f'\n表: {table_name}')
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            for col in columns:
                print(f'  {col[1]} ({col[2]})')
            
            # 检查表中的数据量
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f'  数据行数: {count}')
    
    conn.close()
    
except Exception as e:
    print(f"错误: {e}")