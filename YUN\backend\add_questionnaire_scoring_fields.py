#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为问卷模板添加计分相关字段，使其与量表保持一致
"""

import sqlite3
import json

def add_scoring_fields_to_questionnaire_templates():
    """为questionnaire_templates表添加计分相关字段"""
    try:
        conn = sqlite3.connect('c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db')
        cursor = conn.cursor()
        
        print("为questionnaire_templates表添加计分字段")
        print("=" * 60)
        
        # 检查当前表结构
        cursor.execute("PRAGMA table_info(questionnaire_templates)")
        columns = [col[1] for col in cursor.fetchall()]
        print(f"当前字段: {', '.join(columns)}")
        
        # 需要添加的字段
        fields_to_add = [
            ('scoring_method', 'TEXT'),
            ('max_score', 'FLOAT'),
            ('result_ranges', 'JSON')
        ]
        
        # 添加缺少的字段
        for field_name, field_type in fields_to_add:
            if field_name not in columns:
                print(f"\n添加字段: {field_name} ({field_type})")
                cursor.execute(f"ALTER TABLE questionnaire_templates ADD COLUMN {field_name} {field_type}")
            else:
                print(f"\n✅ 字段 {field_name} 已存在")
        
        # 为现有问卷模板添加默认的计分配置
        print("\n为现有问卷模板添加计分配置:")
        
        # 获取所有问卷模板
        cursor.execute("SELECT id, template_key, name FROM questionnaire_templates")
        templates = cursor.fetchall()
        
        for template_id, template_key, name in templates:
            print(f"\n处理问卷: {name} (key: {template_key})")
            
            # 根据问卷类型设置不同的计分配置
            if template_key == 'psqi':
                # 匹兹堡睡眠质量指数
                scoring_method = 'sum'
                max_score = 21.0
                result_ranges = [
                    {"min": 0, "max": 5, "result": "良好", "description": "睡眠质量良好，无需干预"},
                    {"min": 6, "max": 10, "result": "一般", "description": "睡眠质量一般，可能需要改善睡眠习惯"},
                    {"min": 11, "max": 15, "result": "较差", "description": "睡眠质量较差，建议咨询医生"},
                    {"min": 16, "max": 21, "result": "极差", "description": "睡眠质量极差，需要专业干预"}
                ]
            elif template_key == 'sf36':
                # SF-36健康调查简表
                scoring_method = 'weighted_sum'
                max_score = 100.0
                result_ranges = [
                    {"min": 80, "max": 100, "result": "优秀", "description": "健康状况优秀"},
                    {"min": 60, "max": 79, "result": "良好", "description": "健康状况良好"},
                    {"min": 40, "max": 59, "result": "一般", "description": "健康状况一般"},
                    {"min": 0, "max": 39, "result": "较差", "description": "健康状况较差，需要关注"}
                ]
            elif template_key == 'health_questionnaire':
                # 健康状况调查问卷
                scoring_method = 'sum'
                max_score = 50.0
                result_ranges = [
                    {"min": 40, "max": 50, "result": "优秀", "description": "健康状况优秀"},
                    {"min": 30, "max": 39, "result": "良好", "description": "健康状况良好"},
                    {"min": 20, "max": 29, "result": "一般", "description": "健康状况一般"},
                    {"min": 0, "max": 19, "result": "需要改善", "description": "健康状况需要改善"}
                ]
            elif template_key == 'satisfaction_survey':
                # 满意度调查
                scoring_method = 'average'
                max_score = 5.0
                result_ranges = [
                    {"min": 4.5, "max": 5.0, "result": "非常满意", "description": "服务质量优秀"},
                    {"min": 3.5, "max": 4.4, "result": "满意", "description": "服务质量良好"},
                    {"min": 2.5, "max": 3.4, "result": "一般", "description": "服务质量一般"},
                    {"min": 1.0, "max": 2.4, "result": "不满意", "description": "服务质量需要改善"}
                ]
            else:
                # 默认配置（如medical_history等信息收集类问卷）
                scoring_method = 'none'  # 不计分
                max_score = 0.0
                result_ranges = [
                    {"min": 0, "max": 0, "result": "已完成", "description": "信息收集完成"}
                ]
            
            # 更新数据库
            cursor.execute("""
                UPDATE questionnaire_templates 
                SET scoring_method = ?, max_score = ?, result_ranges = ?
                WHERE id = ?
            """, (scoring_method, max_score, json.dumps(result_ranges), template_id))
            
            print(f"  ✅ 设置计分方法: {scoring_method}, 最高分: {max_score}")
        
        # 为questionnaire_template_questions表添加scoring字段
        print("\n检查questionnaire_template_questions表的scoring字段:")
        cursor.execute("PRAGMA table_info(questionnaire_template_questions)")
        question_columns = [col[1] for col in cursor.fetchall()]
        
        if 'scoring' not in question_columns:
            print("  添加scoring字段...")
            cursor.execute("ALTER TABLE questionnaire_template_questions ADD COLUMN scoring JSON")
            
            # 为现有问题添加默认计分规则
            print("  为现有问题添加计分规则...")
            cursor.execute("""
                SELECT id, question_type, options 
                FROM questionnaire_template_questions
            """)
            questions = cursor.fetchall()
            
            updated_count = 0
            for question_id, question_type, options in questions:
                if question_type == 'single_choice' and options:
                    try:
                        options_data = json.loads(options) if isinstance(options, str) else options
                        if isinstance(options_data, list) and len(options_data) > 0:
                            # 为单选题添加计分规则
                            scoring = {}
                            for i, option in enumerate(options_data):
                                if isinstance(option, dict) and 'value' in option:
                                    scoring[str(option['value'])] = i  # 按选项顺序给分
                                else:
                                    scoring[str(i)] = i
                            
                            cursor.execute(
                                "UPDATE questionnaire_template_questions SET scoring = ? WHERE id = ?",
                                (json.dumps(scoring), question_id)
                            )
                            updated_count += 1
                    except:
                        pass
                elif question_type in ['number', 'text']:
                    # 数值和文本题默认不计分
                    scoring = {"default": 0}
                    cursor.execute(
                        "UPDATE questionnaire_template_questions SET scoring = ? WHERE id = ?",
                        (json.dumps(scoring), question_id)
                    )
                    updated_count += 1
            
            print(f"  ✅ 为{updated_count}个问题添加了计分规则")
        else:
            print("  ✅ scoring字段已存在")
        
        # 提交更改
        conn.commit()
        print("\n✅ 所有更改已提交到数据库")
        
        # 验证结果
        print("\n验证结果:")
        cursor.execute("""
            SELECT template_key, name, scoring_method, max_score
            FROM questionnaire_templates
        """)
        results = cursor.fetchall()
        
        for template_key, name, scoring_method, max_score in results:
            print(f"  {name}: {scoring_method}, 最高分: {max_score}")
        
        conn.close()
        
    except Exception as e:
        print(f"添加计分字段时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    add_scoring_fields_to_questionnaire_templates()