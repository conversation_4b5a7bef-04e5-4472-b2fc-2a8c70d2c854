#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试API差异测试脚本
用于比较 /api/assessments 和 /api/user-health-records API 的差异
"""

import requests
import json
from datetime import datetime

# 配置
BASE_URL = "http://localhost:8006"

def test_login():
    """测试登录并获取token"""
    login_url = f"{BASE_URL}/api/auth/login"
    login_data = {
        "username": "markey",
        "password": "markey0308@163"
    }
    
    print("正在登录...")
    response = requests.post(login_url, data=login_data)
    
    if response.status_code == 200:
        result = response.json()
        if result.get('status') == 'success':
            token = result.get('access_token')
            custom_id = result.get('custom_id')
            print(f"✅ 登录成功")
            print(f"Custom ID: {custom_id}")
            return token, custom_id
        else:
            print(f"❌ 登录失败: {result.get('message', '未知错误')}")
            return None, None
    else:
        print(f"❌ 登录请求失败，状态码: {response.status_code}")
        return None, None

def test_assessments_api(token):
    """测试量表列表API"""
    if not token:
        print("❌ 缺少token，跳过测试")
        return
    
    api_url = f"{BASE_URL}/api/assessments"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    print(f"\n=== 测试量表列表API ===")
    print(f"URL: {api_url}")
    
    response = requests.get(api_url, headers=headers)
    
    print(f"响应状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ API调用成功")
        print(f"数据类型: {type(result)}")
        
        if isinstance(result, list):
            print(f"量表数量: {len(result)}")
            if result:
                print(f"第一个量表字段: {list(result[0].keys())}")
                print(f"第一个量表示例: {json.dumps(result[0], indent=2, ensure_ascii=False)}")
        elif isinstance(result, dict):
            print(f"返回对象字段: {list(result.keys())}")
            print(f"完整响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"未知数据格式: {result}")
    else:
        print(f"❌ API调用失败")
        print(f"错误响应: {response.text}")

def test_user_health_records_api(token, custom_id):
    """测试用户健康记录API"""
    if not token or not custom_id:
        print("❌ 缺少token或custom_id，跳过测试")
        return
    
    api_url = f"{BASE_URL}/api/user-health-records/{custom_id}?record_type=assessment"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    print(f"\n=== 测试用户健康记录API ===")
    print(f"URL: {api_url}")
    
    response = requests.get(api_url, headers=headers)
    
    print(f"响应状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ API调用成功")
        print(f"数据类型: {type(result)}")
        
        if isinstance(result, dict):
            print(f"返回对象字段: {list(result.keys())}")
            if 'records' in result:
                records = result['records']
                print(f"记录数量: {len(records)}")
                print(f"总数: {result.get('total', 'N/A')}")
                if records:
                    print(f"第一条记录字段: {list(records[0].keys())}")
                    print(f"第一条记录示例: {json.dumps(records[0], indent=2, ensure_ascii=False)}")
            print(f"完整响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"未知数据格式: {result}")
    else:
        print(f"❌ API调用失败")
        print(f"错误响应: {response.text}")

def test_user_health_records_all_types(token, custom_id):
    """测试用户健康记录API的所有记录类型"""
    if not token or not custom_id:
        print("❌ 缺少token或custom_id，跳过测试")
        return
    
    # 测试不同的记录类型
    record_types = ['assessment', 'medical', 'lab', 'examination']
    
    for record_type in record_types:
        api_url = f"{BASE_URL}/api/user-health-records/{custom_id}?record_type={record_type}"
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        print(f"\n=== 测试记录类型: {record_type} ===")
        print(f"URL: {api_url}")
        
        response = requests.get(api_url, headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            if isinstance(result, dict) and 'records' in result:
                records = result['records']
                total = result.get('total', 0)
                print(f"✅ {record_type} 记录数量: {len(records)}, 总数: {total}")
            else:
                print(f"⚠️ {record_type} 返回格式异常")
        else:
            print(f"❌ {record_type} API调用失败，状态码: {response.status_code}")

def main():
    print("=== API差异调试测试 ===")
    print(f"测试时间: {datetime.now()}")
    print(f"后端URL: {BASE_URL}")
    
    # 登录获取token
    token, custom_id = test_login()
    
    if token and custom_id:
        # 测试量表列表API
        test_assessments_api(token)
        
        # 测试用户健康记录API
        test_user_health_records_api(token, custom_id)
        
        # 测试所有记录类型
        test_user_health_records_all_types(token, custom_id)
        
        print("\n=== 总结 ===")
        print("1. /api/assessments - 获取可用的量表列表（用于前端显示可选量表）")
        print("2. /api/user-health-records/{custom_id}?record_type=assessment - 获取用户的评估记录历史")
        print("3. 这两个API的作用完全不同，不应该混淆")
        print("4. 前端应该使用 /api/assessments 来显示量表列表")
        print("5. 前端应该使用 /api/user-health-records 来显示用户的历史记录")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()