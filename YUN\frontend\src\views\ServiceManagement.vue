<template>
  <div class="service-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><Setting /></el-icon>
        服务管理
      </h1>
      <p class="page-description">管理系统服务、监控运行状态和性能指标</p>
    </div>

    <!-- 服务概览 -->
    <el-row :gutter="20" class="service-overview">
      <el-col :span="6">
        <el-card class="overview-card" shadow="hover">
          <div class="overview-content">
            <div class="overview-icon running">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="overview-info">
              <h3>{{ runningServices }}</h3>
              <p>运行中服务</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card" shadow="hover">
          <div class="overview-content">
            <div class="overview-icon stopped">
              <el-icon><CircleClose /></el-icon>
            </div>
            <div class="overview-info">
              <h3>{{ stoppedServices }}</h3>
              <p>已停止服务</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card" shadow="hover">
          <div class="overview-content">
            <div class="overview-icon warning">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="overview-info">
              <h3>{{ errorServices }}</h3>
              <p>异常服务</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card" shadow="hover">
          <div class="overview-content">
            <div class="overview-icon total">
              <el-icon><Grid /></el-icon>
            </div>
            <div class="overview-info">
              <h3>{{ totalServices }}</h3>
              <p>总服务数</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 服务列表 -->
    <el-card class="service-list" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>服务列表</span>
          <div class="header-actions">
            <el-input
              v-model="searchText"
              placeholder="搜索服务"
              size="small"
              style="width: 200px; margin-right: 10px;"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            
            <el-select v-model="statusFilter" size="small" style="width: 120px; margin-right: 10px;">
              <el-option label="全部状态" value="all" />
              <el-option label="运行中" value="running" />
              <el-option label="已停止" value="stopped" />
              <el-option label="异常" value="error" />
            </el-select>
            
            <el-button 
              type="primary" 
              size="small" 
              @click="refreshServices"
              :loading="refreshLoading"
            >
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table 
        :data="filteredServices" 
        stripe 
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="name" label="服务名称" width="150">
          <template #default="{ row }">
            <div class="service-name">
              <div class="service-icon" :class="row.status">
                <el-icon><component :is="row.icon" /></el-icon>
              </div>
              <span>{{ row.displayName }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="port" label="端口" width="80" />
        
        <el-table-column prop="uptime" label="运行时间" width="120" />
        
        <el-table-column label="资源使用" width="200">
          <template #default="{ row }">
            <div class="resource-usage">
              <div class="resource-item">
                <span class="resource-label">CPU:</span>
                <el-progress 
                  :percentage="row.metrics?.cpu || 0" 
                  :stroke-width="4"
                  :show-text="false"
                  style="width: 60px;"
                />
                <span class="resource-value">{{ row.metrics?.cpu || 0 }}%</span>
              </div>
              <div class="resource-item">
                <span class="resource-label">内存:</span>
                <el-progress 
                  :percentage="row.metrics?.memory || 0" 
                  :stroke-width="4"
                  :show-text="false"
                  color="#e6a23c"
                  style="width: 60px;"
                />
                <span class="resource-value">{{ row.metrics?.memory || 0 }}%</span>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="lastUpdate" label="最后更新" width="150">
          <template #default="{ row }">
            {{ formatTime(row.lastUpdate) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button-group size="small">
              <el-button 
                type="success" 
                @click="startService(row.name)"
                :disabled="row.status === 'running'"
                :loading="row.loading?.start"
              >
                <el-icon><VideoPlay /></el-icon>
              </el-button>
              
              <el-button 
                type="warning" 
                @click="restartService(row.name)"
                :disabled="row.status === 'stopped'"
                :loading="row.loading?.restart"
              >
                <el-icon><Refresh /></el-icon>
              </el-button>
              
              <el-button 
                type="danger" 
                @click="stopService(row.name)"
                :disabled="row.status === 'stopped'"
                :loading="row.loading?.stop"
              >
                <el-icon><VideoPause /></el-icon>
              </el-button>
              
              <el-button 
                type="info" 
                @click="showServiceDetails(row)"
              >
                <el-icon><View /></el-icon>
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 批量操作 -->
      <div class="batch-operations" v-if="selectedServices.length > 0">
        <div class="batch-info">
          已选择 {{ selectedServices.length }} 个服务
        </div>
        <el-button-group>
          <el-button 
            type="success" 
            size="small"
            @click="batchStart"
            :loading="batchLoading.start"
          >
            <el-icon><VideoPlay /></el-icon>
            批量启动
          </el-button>
          
          <el-button 
            type="warning" 
            size="small"
            @click="batchRestart"
            :loading="batchLoading.restart"
          >
            <el-icon><Refresh /></el-icon>
            批量重启
          </el-button>
          
          <el-button 
            type="danger" 
            size="small"
            @click="batchStop"
            :loading="batchLoading.stop"
          >
            <el-icon><VideoPause /></el-icon>
            批量停止
          </el-button>
        </el-button-group>
      </div>
    </el-card>

    <!-- 性能监控 -->
    <el-card class="performance-monitor" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>性能监控</span>
          <el-button-group size="small">
            <el-button 
              :type="monitorInterval === 5000 ? 'primary' : ''"
              @click="setMonitorInterval(5000)"
            >
              5秒
            </el-button>
            <el-button 
              :type="monitorInterval === 10000 ? 'primary' : ''"
              @click="setMonitorInterval(10000)"
            >
              10秒
            </el-button>
            <el-button 
              :type="monitorInterval === 30000 ? 'primary' : ''"
              @click="setMonitorInterval(30000)"
            >
              30秒
            </el-button>
          </el-button-group>
        </div>
      </template>
      
      <div class="performance-charts">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="chart-container">
              <h4>CPU 使用率</h4>
              <div class="chart-placeholder">
                <div class="chart-line" v-for="(value, index) in cpuHistory" :key="index">
                  <div 
                    class="chart-bar" 
                    :style="{ height: value + '%', backgroundColor: getChartColor(value) }"
                  ></div>
                </div>
              </div>
            </div>
          </el-col>
          
          <el-col :span="12">
            <div class="chart-container">
              <h4>内存使用率</h4>
              <div class="chart-placeholder">
                <div class="chart-line" v-for="(value, index) in memoryHistory" :key="index">
                  <div 
                    class="chart-bar" 
                    :style="{ height: value + '%', backgroundColor: getChartColor(value) }"
                  ></div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 服务详情对话框 -->
    <el-dialog 
      v-model="detailDialogVisible" 
      :title="currentService?.displayName + ' 详情'"
      width="70%"
      top="5vh"
    >
      <div class="service-details" v-if="currentService">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="基本信息" name="info">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="服务名称">{{ currentService.displayName }}</el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag :type="getStatusType(currentService.status)">
                  {{ getStatusText(currentService.status) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="端口">{{ currentService.port }}</el-descriptions-item>
              <el-descriptions-item label="运行时间">{{ currentService.uptime }}</el-descriptions-item>
              <el-descriptions-item label="进程ID">{{ currentService.pid || '未知' }}</el-descriptions-item>
              <el-descriptions-item label="启动时间">{{ formatTime(currentService.startTime) }}</el-descriptions-item>
              <el-descriptions-item label="配置文件">{{ currentService.configFile || '默认' }}</el-descriptions-item>
              <el-descriptions-item label="日志文件">{{ currentService.logFile || '默认' }}</el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>
          
          <el-tab-pane label="性能指标" name="metrics">
            <div class="metrics-grid">
              <div class="metric-card">
                <h4>CPU 使用率</h4>
                <div class="metric-value">{{ currentService.metrics?.cpu || 0 }}%</div>
                <el-progress :percentage="currentService.metrics?.cpu || 0" />
              </div>
              
              <div class="metric-card">
                <h4>内存使用率</h4>
                <div class="metric-value">{{ currentService.metrics?.memory || 0 }}%</div>
                <el-progress :percentage="currentService.metrics?.memory || 0" color="#e6a23c" />
              </div>
              
              <div class="metric-card">
                <h4>网络连接</h4>
                <div class="metric-value">{{ currentService.metrics?.connections || 0 }}</div>
              </div>
              
              <div class="metric-card">
                <h4>请求数/分钟</h4>
                <div class="metric-value">{{ currentService.metrics?.requests || 0 }}</div>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="日志" name="logs">
            <div class="log-viewer">
              <div class="log-controls">
                <el-button-group size="small">
                  <el-button @click="refreshServiceLogs">
                    <el-icon><Refresh /></el-icon>
                    刷新
                  </el-button>
                  <el-button @click="downloadServiceLogs">
                    <el-icon><Download /></el-icon>
                    下载
                  </el-button>
                  <el-button @click="clearServiceLogs">
                    <el-icon><Delete /></el-icon>
                    清空
                  </el-button>
                </el-button-group>
              </div>
              
              <div class="log-content">
                <pre v-if="serviceLogs.length > 0">{{ serviceLogs.join('\n') }}</pre>
                <el-empty v-else description="暂无日志" />
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Setting,
  CircleCheck,
  CircleClose,
  Warning,
  Grid,
  Search,
  Refresh,
  VideoPlay,
  VideoPause,
  View,
  Download,
  Delete,
  Monitor,
  DataBoard,
  Service
} from '@element-plus/icons-vue';
import axios from 'axios';

// 响应式数据
const searchText = ref('');
const statusFilter = ref('all');
const refreshLoading = ref(false);
const selectedServices = ref([]);
const detailDialogVisible = ref(false);
const currentService = ref(null);
const activeTab = ref('info');
const serviceLogs = ref([]);
const monitorInterval = ref(10000);
const monitorTimer = ref(null);
const cpuHistory = ref(Array(20).fill(0));
const memoryHistory = ref(Array(20).fill(0));

const batchLoading = reactive({
  start: false,
  restart: false,
  stop: false
});

const services = ref([
  {
    name: 'backend-api',
    displayName: '后端API服务',
    icon: 'Monitor',
    status: 'running',
    port: 8000,
    uptime: '2小时30分钟',
    lastUpdate: new Date().toISOString(),
    startTime: new Date(Date.now() - 2.5 * 60 * 60 * 1000).toISOString(),
    pid: 12345,
    configFile: '/etc/backend/config.yaml',
    logFile: '/var/log/backend/api.log',
    metrics: {
      cpu: 25,
      memory: 45,
      connections: 150,
      requests: 1200
    },
    loading: {
      start: false,
      restart: false,
      stop: false
    }
  },
  {
    name: 'database',
    displayName: '数据库服务',
    icon: 'DataBoard',
    status: 'running',
    port: 5432,
    uptime: '5天12小时',
    lastUpdate: new Date().toISOString(),
    startTime: new Date(Date.now() - 5.5 * 24 * 60 * 60 * 1000).toISOString(),
    pid: 54321,
    configFile: '/etc/postgresql/postgresql.conf',
    logFile: '/var/log/postgresql/postgresql.log',
    metrics: {
      cpu: 15,
      memory: 60,
      connections: 50,
      requests: 800
    },
    loading: {
      start: false,
      restart: false,
      stop: false
    }
  },
  {
    name: 'redis-cache',
    displayName: 'Redis缓存',
    icon: 'Service',
    status: 'running',
    port: 6379,
    uptime: '3天8小时',
    lastUpdate: new Date().toISOString(),
    startTime: new Date(Date.now() - 3.3 * 24 * 60 * 60 * 1000).toISOString(),
    pid: 67890,
    configFile: '/etc/redis/redis.conf',
    logFile: '/var/log/redis/redis.log',
    metrics: {
      cpu: 5,
      memory: 20,
      connections: 25,
      requests: 2000
    },
    loading: {
      start: false,
      restart: false,
      stop: false
    }
  },
  {
    name: 'nginx-proxy',
    displayName: 'Nginx代理',
    icon: 'Service',
    status: 'stopped',
    port: 80,
    uptime: '0分钟',
    lastUpdate: new Date().toISOString(),
    startTime: null,
    pid: null,
    configFile: '/etc/nginx/nginx.conf',
    logFile: '/var/log/nginx/access.log',
    metrics: {
      cpu: 0,
      memory: 0,
      connections: 0,
      requests: 0
    },
    loading: {
      start: false,
      restart: false,
      stop: false
    }
  }
]);

// 计算属性
const filteredServices = computed(() => {
  let filtered = services.value;
  
  // 状态过滤
  if (statusFilter.value !== 'all') {
    filtered = filtered.filter(service => service.status === statusFilter.value);
  }
  
  // 搜索过滤
  if (searchText.value) {
    const search = searchText.value.toLowerCase();
    filtered = filtered.filter(service => 
      service.displayName.toLowerCase().includes(search) ||
      service.name.toLowerCase().includes(search)
    );
  }
  
  return filtered;
});

const runningServices = computed(() => {
  return services.value.filter(s => s.status === 'running').length;
});

const stoppedServices = computed(() => {
  return services.value.filter(s => s.status === 'stopped').length;
});

const errorServices = computed(() => {
  return services.value.filter(s => s.status === 'error').length;
});

const totalServices = computed(() => {
  return services.value.length;
});

// 方法
const getStatusType = (status) => {
  const typeMap = {
    'running': 'success',
    'stopped': 'info',
    'error': 'danger',
    'starting': 'warning',
    'stopping': 'warning'
  };
  return typeMap[status] || 'info';
};

const getStatusText = (status) => {
  const textMap = {
    'running': '运行中',
    'stopped': '已停止',
    'error': '异常',
    'starting': '启动中',
    'stopping': '停止中'
  };
  return textMap[status] || '未知';
};

const formatTime = (timestamp) => {
  if (!timestamp) return '未知';
  return new Date(timestamp).toLocaleString();
};

const getChartColor = (value) => {
  if (value < 30) return '#67c23a';
  if (value < 70) return '#e6a23c';
  return '#f56c6c';
};

const refreshServices = async () => {
  refreshLoading.value = true;
  try {
    const response = await axios.get('/api/management/services');
    services.value = response.data;
    ElMessage.success('服务状态已刷新');
  } catch (error) {
    console.error('刷新服务状态失败:', error);
    ElMessage.error('刷新服务状态失败');
  } finally {
    refreshLoading.value = false;
  }
};

const startService = async (serviceName) => {
  await serviceOperation(serviceName, 'start', '启动');
};

const restartService = async (serviceName) => {
  await serviceOperation(serviceName, 'restart', '重启');
};

const stopService = async (serviceName) => {
  await serviceOperation(serviceName, 'stop', '停止');
};

const serviceOperation = async (serviceName, action, actionText) => {
  const service = services.value.find(s => s.name === serviceName);
  if (!service) return;
  
  try {
    await ElMessageBox.confirm(
      `确定要${actionText} ${service.displayName} 吗？`,
      '确认操作',
      { type: 'warning' }
    );
    
    service.loading[action] = true;
    service.status = action + 'ing';
    
    const response = await axios.post(`/api/management/services/${serviceName}/${action}`);
    
    service.status = response.data.status;
    service.lastUpdate = new Date().toISOString();
    
    if (action === 'start' && response.data.status === 'running') {
      service.startTime = new Date().toISOString();
      service.pid = response.data.pid;
    } else if (action === 'stop' && response.data.status === 'stopped') {
      service.startTime = null;
      service.pid = null;
      service.uptime = '0分钟';
    }
    
    ElMessage.success(`${service.displayName} ${actionText}成功`);
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${actionText}服务失败:`, error);
      service.status = 'error';
      ElMessage.error(`${service.displayName} ${actionText}失败`);
    }
  } finally {
    service.loading[action] = false;
  }
};

const handleSelectionChange = (selection) => {
  selectedServices.value = selection;
};

const batchStart = async () => {
  await batchOperation('start', '启动');
};

const batchRestart = async () => {
  await batchOperation('restart', '重启');
};

const batchStop = async () => {
  await batchOperation('stop', '停止');
};

const batchOperation = async (action, actionText) => {
  try {
    await ElMessageBox.confirm(
      `确定要${actionText}选中的 ${selectedServices.value.length} 个服务吗？`,
      '确认批量操作',
      { type: 'warning' }
    );
    
    batchLoading[action] = true;
    
    const promises = selectedServices.value.map(async (service) => {
      try {
        service.loading[action] = true;
        service.status = action + 'ing';
        
        const response = await axios.post(`/api/management/services/${service.name}/${action}`);
        
        service.status = response.data.status;
        service.lastUpdate = new Date().toISOString();
        
        return { service: service.name, success: true };
      } catch (error) {
        service.status = 'error';
        return { service: service.name, success: false, error: error.message };
      } finally {
        service.loading[action] = false;
      }
    });
    
    const results = await Promise.all(promises);
    const successCount = results.filter(r => r.success).length;
    const failCount = results.length - successCount;
    
    if (failCount === 0) {
      ElMessage.success(`批量${actionText}完成，${successCount} 个服务成功`);
    } else {
      ElMessage.warning(`批量${actionText}完成，${successCount} 个成功，${failCount} 个失败`);
    }
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`批量${actionText}失败:`, error);
      ElMessage.error(`批量${actionText}失败`);
    }
  } finally {
    batchLoading[action] = false;
  }
};

const showServiceDetails = (service) => {
  currentService.value = service;
  detailDialogVisible.value = true;
  activeTab.value = 'info';
  refreshServiceLogs();
};

const refreshServiceLogs = async () => {
  if (!currentService.value) return;
  
  try {
    const response = await axios.get(`/api/management/services/${currentService.value.name}/logs`);
    serviceLogs.value = response.data.logs || [];
  } catch (error) {
    console.error('获取服务日志失败:', error);
    serviceLogs.value = ['获取日志失败: ' + error.message];
  }
};

const downloadServiceLogs = () => {
  if (!currentService.value || serviceLogs.value.length === 0) return;
  
  const content = serviceLogs.value.join('\n');
  const blob = new Blob([content], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `${currentService.value.name}_logs_${new Date().toISOString().slice(0, 10)}.txt`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

const clearServiceLogs = async () => {
  if (!currentService.value) return;
  
  try {
    await ElMessageBox.confirm('确定要清空日志吗？', '确认操作', { type: 'warning' });
    
    await axios.delete(`/api/management/services/${currentService.value.name}/logs`);
    serviceLogs.value = [];
    ElMessage.success('日志已清空');
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空日志失败:', error);
      ElMessage.error('清空日志失败');
    }
  }
};

const setMonitorInterval = (interval) => {
  monitorInterval.value = interval;
  startPerformanceMonitor();
};

const startPerformanceMonitor = () => {
  if (monitorTimer.value) {
    clearInterval(monitorTimer.value);
  }
  
  monitorTimer.value = setInterval(() => {
    updatePerformanceData();
  }, monitorInterval.value);
};

const updatePerformanceData = () => {
  // 模拟性能数据更新
  const avgCpu = services.value.reduce((sum, s) => sum + (s.metrics?.cpu || 0), 0) / services.value.length;
  const avgMemory = services.value.reduce((sum, s) => sum + (s.metrics?.memory || 0), 0) / services.value.length;
  
  cpuHistory.value.shift();
  cpuHistory.value.push(Math.round(avgCpu));
  
  memoryHistory.value.shift();
  memoryHistory.value.push(Math.round(avgMemory));
};

const stopPerformanceMonitor = () => {
  if (monitorTimer.value) {
    clearInterval(monitorTimer.value);
    monitorTimer.value = null;
  }
};

// 生命周期
onMounted(() => {
  refreshServices();
  startPerformanceMonitor();
});

onUnmounted(() => {
  stopPerformanceMonitor();
});
</script>

<style scoped>
.service-management {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.service-overview {
  margin-bottom: 20px;
}

.overview-card {
  height: 100px;
}

.overview-content {
  display: flex;
  align-items: center;
  gap: 15px;
  height: 100%;
}

.overview-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.overview-icon.running {
  background-color: #67c23a;
}

.overview-icon.stopped {
  background-color: #909399;
}

.overview-icon.warning {
  background-color: #e6a23c;
}

.overview-icon.total {
  background-color: #409eff;
}

.overview-info h3 {
  margin: 0 0 5px 0;
  font-size: 24px;
  color: #303133;
  font-weight: 600;
}

.overview-info p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.service-list {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
}

.service-name {
  display: flex;
  align-items: center;
  gap: 10px;
}

.service-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
}

.service-icon.running {
  background-color: #67c23a;
}

.service-icon.stopped {
  background-color: #909399;
}

.service-icon.error {
  background-color: #f56c6c;
}

.resource-usage {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.resource-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.resource-label {
  min-width: 35px;
  color: #606266;
}

.resource-value {
  min-width: 30px;
  color: #303133;
  text-align: right;
}

.batch-operations {
  margin-top: 15px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.batch-info {
  color: #606266;
  font-size: 14px;
}

.performance-monitor {
  margin-bottom: 20px;
}

.performance-charts {
  margin-top: 20px;
}

.chart-container {
  text-align: center;
}

.chart-container h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
}

.chart-placeholder {
  height: 200px;
  background-color: #fafafa;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
  padding: 10px;
}

.chart-line {
  flex: 1;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  margin: 0 1px;
}

.chart-bar {
  width: 100%;
  min-height: 2px;
  border-radius: 2px 2px 0 0;
  transition: all 0.3s ease;
}

.service-details {
  min-height: 400px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-top: 20px;
}

.metric-card {
  padding: 20px;
  background-color: #fafafa;
  border-radius: 8px;
  text-align: center;
}

.metric-card h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 14px;
}

.metric-value {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 10px;
}

.log-viewer {
  height: 400px;
  display: flex;
  flex-direction: column;
}

.log-controls {
  margin-bottom: 10px;
  display: flex;
  justify-content: flex-end;
}

.log-content {
  flex: 1;
  background-color: #1e1e1e;
  color: #d4d4d4;
  padding: 16px;
  border-radius: 4px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.log-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

@media (max-width: 768px) {
  .service-management {
    padding: 10px;
  }
  
  .service-overview {
    margin-bottom: 15px;
  }
  
  .overview-content {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }
  
  .header-actions {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .batch-operations {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .chart-placeholder {
    height: 150px;
  }
}
</style>