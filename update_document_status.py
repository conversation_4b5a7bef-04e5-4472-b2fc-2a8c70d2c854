#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新文档状态脚本
将所有pending_processing状态的文档更新为active状态
"""

import sqlite3
import os

def update_document_status():
    """更新文档状态"""
    db_path = 'c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db'
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查看更新前的状态
        cursor.execute("SELECT COUNT(*) FROM documents WHERE status = 'pending_processing'")
        pending_count = cursor.fetchone()[0]
        print(f"待更新的文档数量: {pending_count}")
        
        # 更新状态
        cursor.execute("UPDATE documents SET status = 'active' WHERE status = 'pending_processing'")
        affected = cursor.rowcount
        conn.commit()
        
        print(f"已更新 {affected} 条文档记录状态为 active")
        
        # 查看更新后的结果
        cursor.execute("""
            SELECT id, custom_id, title, filename, document_type, status, created_at 
            FROM documents 
            WHERE id >= 26 
            ORDER BY created_at DESC
        """)
        results = cursor.fetchall()
        
        print("\n更新后的文档记录:")
        for r in results:
            print(f"ID: {r[0]}, 用户: {r[1]}, 标题: {r[2]}, 文件名: {r[3]}, 类型: {r[4]}, 状态: {r[5]}, 创建时间: {r[6]}")
        
        conn.close()
        print("\n数据库状态更新完成!")
        
    except Exception as e:
        print(f"更新失败: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

if __name__ == "__main__":
    update_document_status()