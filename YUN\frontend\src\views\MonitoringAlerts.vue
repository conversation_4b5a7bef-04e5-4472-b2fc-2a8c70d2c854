<template>
  <div class="monitoring-alerts">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><Bell /></el-icon>
        监控告警
      </h1>
      <p class="page-description">系统监控、告警管理和性能分析</p>
    </div>

    <!-- 告警概览 -->
    <el-row :gutter="20" class="alert-overview">
      <el-col :span="6">
        <el-card class="overview-card" shadow="hover">
          <div class="overview-content">
            <div class="overview-icon critical">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="overview-info">
              <h3>{{ criticalAlerts }}</h3>
              <p>严重告警</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card" shadow="hover">
          <div class="overview-content">
            <div class="overview-icon warning">
              <el-icon><InfoFilled /></el-icon>
            </div>
            <div class="overview-info">
              <h3>{{ warningAlerts }}</h3>
              <p>警告告警</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card" shadow="hover">
          <div class="overview-content">
            <div class="overview-icon info">
              <el-icon><Bell /></el-icon>
            </div>
            <div class="overview-info">
              <h3>{{ infoAlerts }}</h3>
              <p>信息告警</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card" shadow="hover">
          <div class="overview-content">
            <div class="overview-icon resolved">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="overview-info">
              <h3>{{ resolvedAlerts }}</h3>
              <p>已解决</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统监控 -->
    <el-row :gutter="20" class="monitoring-section">
      <el-col :span="12">
        <el-card class="monitoring-card" shadow="hover">
          <template #header>
            <span>系统性能监控</span>
          </template>
          
          <div class="performance-metrics">
            <div class="metric-item">
              <div class="metric-header">
                <span class="metric-name">CPU 使用率</span>
                <span class="metric-value" :class="getCpuStatusClass()">{{ cpuUsage }}%</span>
              </div>
              <el-progress 
                :percentage="cpuUsage" 
                :status="getCpuProgressStatus()"
                :stroke-width="8"
              />
            </div>
            
            <div class="metric-item">
              <div class="metric-header">
                <span class="metric-name">内存使用率</span>
                <span class="metric-value" :class="getMemoryStatusClass()">{{ memoryUsage }}%</span>
              </div>
              <el-progress 
                :percentage="memoryUsage" 
                :status="getMemoryProgressStatus()"
                :stroke-width="8"
              />
            </div>
            
            <div class="metric-item">
              <div class="metric-header">
                <span class="metric-name">磁盘使用率</span>
                <span class="metric-value" :class="getDiskStatusClass()">{{ diskUsage }}%</span>
              </div>
              <el-progress 
                :percentage="diskUsage" 
                :status="getDiskProgressStatus()"
                :stroke-width="8"
              />
            </div>
            
            <div class="metric-item">
              <div class="metric-header">
                <span class="metric-name">网络延迟</span>
                <span class="metric-value" :class="getNetworkStatusClass()">{{ networkLatency }}ms</span>
              </div>
              <div class="network-status">
                <el-tag :type="getNetworkTagType()" size="small">
                  {{ getNetworkStatusText() }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="monitoring-card" shadow="hover">
          <template #header>
            <span>服务状态监控</span>
          </template>
          
          <div class="service-status">
            <div class="service-item" v-for="service in services" :key="service.name">
              <div class="service-info">
                <div class="service-name">
                  <el-icon><component :is="getServiceIcon(service.type)" /></el-icon>
                  {{ service.name }}
                </div>
                <div class="service-details">
                  <span class="service-version">{{ service.version }}</span>
                  <span class="service-uptime">运行时间: {{ formatUptime(service.uptime) }}</span>
                </div>
              </div>
              
              <div class="service-status-indicator">
                <el-tag :type="getServiceStatusType(service.status)" size="small">
                  {{ getServiceStatusText(service.status) }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 告警列表 -->
    <el-card class="alerts-list" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>告警列表</span>
          <div class="header-actions">
            <el-input
              v-model="searchText"
              placeholder="搜索告警"
              size="small"
              style="width: 200px; margin-right: 10px;"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            
            <el-select v-model="severityFilter" size="small" style="width: 120px; margin-right: 10px;">
              <el-option label="全部级别" value="all" />
              <el-option label="严重" value="critical" />
              <el-option label="警告" value="warning" />
              <el-option label="信息" value="info" />
            </el-select>
            
            <el-select v-model="statusFilter" size="small" style="width: 120px; margin-right: 10px;">
              <el-option label="全部状态" value="all" />
              <el-option label="活跃" value="active" />
              <el-option label="已确认" value="acknowledged" />
              <el-option label="已解决" value="resolved" />
            </el-select>
            
            <el-button 
              type="primary" 
              size="small" 
              @click="refreshAlerts"
              :loading="refreshLoading"
            >
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            
            <el-button 
              type="success" 
              size="small" 
              @click="showAlertRuleDialog"
            >
              <el-icon><Plus /></el-icon>
              添加规则
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table :data="filteredAlerts" stripe style="width: 100%">
        <el-table-column prop="id" label="告警ID" width="100" />
        
        <el-table-column prop="severity" label="级别" width="100">
          <template #default="{ row }">
            <el-tag :type="getSeverityType(row.severity)" size="small">
              <el-icon><component :is="getSeverityIcon(row.severity)" /></el-icon>
              {{ getSeverityText(row.severity) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="title" label="告警标题" min-width="200">
          <template #default="{ row }">
            <div class="alert-title">
              <div class="title">{{ row.title }}</div>
              <div class="source">{{ row.source }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="description" label="描述" min-width="250" show-overflow-tooltip />
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getAlertStatusType(row.status)" size="small">
              {{ getAlertStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="count" label="次数" width="80">
          <template #default="{ row }">
            <el-badge :value="row.count" :max="99" class="alert-count">
              <span>{{ row.count }}</span>
            </el-badge>
          </template>
        </el-table-column>
        
        <el-table-column prop="firstTime" label="首次时间" width="150">
          <template #default="{ row }">
            {{ formatTime(row.firstTime) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="lastTime" label="最近时间" width="150">
          <template #default="{ row }">
            {{ formatTime(row.lastTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button-group size="small">
              <el-button type="info" @click="viewAlertDetails(row)">
                <el-icon><View /></el-icon>
                详情
              </el-button>
              
              <el-button 
                v-if="row.status === 'active'"
                type="warning" 
                @click="acknowledgeAlert(row)"
              >
                <el-icon><Check /></el-icon>
                确认
              </el-button>
              
              <el-button 
                v-if="row.status !== 'resolved'"
                type="success" 
                @click="resolveAlert(row)"
              >
                <el-icon><CircleCheck /></el-icon>
                解决
              </el-button>
              
              <el-button 
                type="danger" 
                @click="deleteAlert(row)"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 告警规则管理 -->
    <el-card class="alert-rules" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>告警规则</span>
          <div class="header-actions">
            <el-button 
              type="primary" 
              size="small" 
              @click="refreshRules"
              :loading="rulesLoading"
            >
              <el-icon><Refresh /></el-icon>
              刷新规则
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table :data="alertRules" stripe style="width: 100%">
        <el-table-column prop="name" label="规则名称" width="200" />
        
        <el-table-column prop="metric" label="监控指标" width="150" />
        
        <el-table-column prop="condition" label="触发条件" width="200">
          <template #default="{ row }">
            <span>{{ row.operator }} {{ row.threshold }}{{ row.unit }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="severity" label="告警级别" width="100">
          <template #default="{ row }">
            <el-tag :type="getSeverityType(row.severity)" size="small">
              {{ getSeverityText(row.severity) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="enabled" label="状态" width="100">
          <template #default="{ row }">
            <el-switch 
              v-model="row.enabled" 
              @change="toggleRule(row)"
              active-text="启用"
              inactive-text="禁用"
            />
          </template>
        </el-table-column>
        
        <el-table-column prop="lastTriggered" label="最后触发" width="150">
          <template #default="{ row }">
            {{ row.lastTriggered ? formatTime(row.lastTriggered) : '从未' }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button-group size="small">
              <el-button type="primary" @click="editRule(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              
              <el-button type="danger" @click="deleteRule(row)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 告警详情对话框 -->
    <el-dialog 
      v-model="alertDetailsVisible" 
      :title="'告警详情 - ' + (currentAlert?.title || '')"
      width="70%"
      top="5vh"
    >
      <div class="alert-details" v-if="currentAlert">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="基本信息" name="info">
            <div class="alert-info">
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="info-group">
                    <h4>告警信息</h4>
                    <div class="info-item">
                      <span class="label">告警ID:</span>
                      <span class="value">{{ currentAlert.id }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">标题:</span>
                      <span class="value">{{ currentAlert.title }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">级别:</span>
                      <el-tag :type="getSeverityType(currentAlert.severity)" size="small">
                        {{ getSeverityText(currentAlert.severity) }}
                      </el-tag>
                    </div>
                    <div class="info-item">
                      <span class="label">状态:</span>
                      <el-tag :type="getAlertStatusType(currentAlert.status)" size="small">
                        {{ getAlertStatusText(currentAlert.status) }}
                      </el-tag>
                    </div>
                    <div class="info-item">
                      <span class="label">来源:</span>
                      <span class="value">{{ currentAlert.source }}</span>
                    </div>
                  </div>
                </el-col>
                
                <el-col :span="12">
                  <div class="info-group">
                    <h4>时间信息</h4>
                    <div class="info-item">
                      <span class="label">首次时间:</span>
                      <span class="value">{{ formatTime(currentAlert.firstTime) }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">最近时间:</span>
                      <span class="value">{{ formatTime(currentAlert.lastTime) }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">触发次数:</span>
                      <span class="value">{{ currentAlert.count }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">持续时间:</span>
                      <span class="value">{{ formatDuration(currentAlert.duration) }}</span>
                    </div>
                  </div>
                </el-col>
              </el-row>
              
              <div class="info-group">
                <h4>描述</h4>
                <p>{{ currentAlert.description }}</p>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="处理记录" name="history">
            <div class="alert-history">
              <el-timeline>
                <el-timeline-item 
                  v-for="record in alertHistory" 
                  :key="record.id"
                  :type="getHistoryType(record.action)"
                  :timestamp="formatTime(record.timestamp)"
                >
                  <div class="history-content">
                    <h4>{{ getHistoryActionText(record.action) }}</h4>
                    <p>操作人: {{ record.operator }}</p>
                    <p v-if="record.comment">备注: {{ record.comment }}</p>
                  </div>
                </el-timeline-item>
              </el-timeline>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="相关指标" name="metrics">
            <div class="alert-metrics">
              <div class="metrics-chart">
                <h4>指标趋势图</h4>
                <div class="chart-placeholder">
                  <p>指标图表将在此显示</p>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>

    <!-- 告警规则对话框 -->
    <el-dialog 
      v-model="ruleDialogVisible" 
      :title="ruleEditMode === 'create' ? '添加告警规则' : '编辑告警规则'"
      width="50%"
    >
      <el-form :model="currentRule" :rules="ruleRules" ref="ruleForm" label-width="100px">
        <el-form-item label="规则名称" prop="name">
          <el-input v-model="currentRule.name" />
        </el-form-item>
        
        <el-form-item label="监控指标" prop="metric">
          <el-select v-model="currentRule.metric" style="width: 100%">
            <el-option label="CPU使用率" value="cpu_usage" />
            <el-option label="内存使用率" value="memory_usage" />
            <el-option label="磁盘使用率" value="disk_usage" />
            <el-option label="网络延迟" value="network_latency" />
            <el-option label="响应时间" value="response_time" />
            <el-option label="错误率" value="error_rate" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="触发条件" prop="operator">
          <el-row :gutter="10">
            <el-col :span="8">
              <el-select v-model="currentRule.operator" style="width: 100%">
                <el-option label="大于" value=">" />
                <el-option label="大于等于" value=">=" />
                <el-option label="小于" value="<" />
                <el-option label="小于等于" value="<=" />
                <el-option label="等于" value="=" />
              </el-select>
            </el-col>
            <el-col :span="10">
              <el-input-number v-model="currentRule.threshold" style="width: 100%" />
            </el-col>
            <el-col :span="6">
              <el-input v-model="currentRule.unit" placeholder="单位" />
            </el-col>
          </el-row>
        </el-form-item>
        
        <el-form-item label="告警级别" prop="severity">
          <el-select v-model="currentRule.severity" style="width: 100%">
            <el-option label="严重" value="critical" />
            <el-option label="警告" value="warning" />
            <el-option label="信息" value="info" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input v-model="currentRule.description" type="textarea" :rows="3" />
        </el-form-item>
        
        <el-form-item label="启用规则">
          <el-switch v-model="currentRule.enabled" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="ruleDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveRule" :loading="saveRuleLoading">
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Bell,
  Warning,
  InfoFilled,
  CircleCheck,
  Search,
  Refresh,
  Plus,
  View,
  Check,
  Delete,
  Edit
} from '@element-plus/icons-vue';
import axios from 'axios';

// 响应式数据
const searchText = ref('');
const severityFilter = ref('all');
const statusFilter = ref('all');
const refreshLoading = ref(false);
const rulesLoading = ref(false);
const alertDetailsVisible = ref(false);
const ruleDialogVisible = ref(false);
const ruleEditMode = ref('create');
const saveRuleLoading = ref(false);
const activeTab = ref('info');
const currentAlert = ref(null);

// 系统性能指标
const cpuUsage = ref(45);
const memoryUsage = ref(68);
const diskUsage = ref(32);
const networkLatency = ref(25);

const currentRule = reactive({
  name: '',
  metric: '',
  operator: '>',
  threshold: 0,
  unit: '',
  severity: 'warning',
  description: '',
  enabled: true
});

const alerts = ref([
  {
    id: 'A001',
    severity: 'critical',
    title: 'CPU使用率过高',
    description: 'CPU使用率持续超过90%，可能影响系统性能',
    source: 'system-monitor',
    status: 'active',
    count: 15,
    firstTime: new Date(Date.now() - 3600000).toISOString(),
    lastTime: new Date(Date.now() - 300000).toISOString(),
    duration: 3300000
  },
  {
    id: 'A002',
    severity: 'warning',
    title: '内存使用率较高',
    description: '内存使用率超过80%，建议检查内存泄漏',
    source: 'system-monitor',
    status: 'acknowledged',
    count: 8,
    firstTime: new Date(Date.now() - 7200000).toISOString(),
    lastTime: new Date(Date.now() - 1800000).toISOString(),
    duration: 5400000
  },
  {
    id: 'A003',
    severity: 'info',
    title: '服务重启',
    description: '后端服务已自动重启',
    source: 'service-monitor',
    status: 'resolved',
    count: 1,
    firstTime: new Date(Date.now() - 86400000).toISOString(),
    lastTime: new Date(Date.now() - 86400000).toISOString(),
    duration: 0
  },
  {
    id: 'A004',
    severity: 'warning',
    title: '磁盘空间不足',
    description: '系统磁盘使用率超过85%',
    source: 'disk-monitor',
    status: 'active',
    count: 3,
    firstTime: new Date(Date.now() - 1800000).toISOString(),
    lastTime: new Date(Date.now() - 600000).toISOString(),
    duration: 1200000
  }
]);

const services = ref([
  {
    name: '后端API服务',
    type: 'api',
    version: 'v1.0.0',
    status: 'healthy',
    uptime: 86400000
  },
  {
    name: '数据库服务',
    type: 'database',
    version: 'v8.0',
    status: 'healthy',
    uptime: 172800000
  },
  {
    name: '缓存服务',
    type: 'cache',
    version: 'v6.2',
    status: 'warning',
    uptime: 43200000
  },
  {
    name: '消息队列',
    type: 'queue',
    version: 'v3.8',
    status: 'healthy',
    uptime: 259200000
  }
]);

const alertRules = ref([
  {
    id: 1,
    name: 'CPU使用率告警',
    metric: 'cpu_usage',
    operator: '>',
    threshold: 80,
    unit: '%',
    severity: 'warning',
    enabled: true,
    lastTriggered: new Date(Date.now() - 3600000).toISOString()
  },
  {
    id: 2,
    name: '内存使用率告警',
    metric: 'memory_usage',
    operator: '>',
    threshold: 85,
    unit: '%',
    severity: 'critical',
    enabled: true,
    lastTriggered: new Date(Date.now() - 7200000).toISOString()
  },
  {
    id: 3,
    name: '磁盘空间告警',
    metric: 'disk_usage',
    operator: '>',
    threshold: 90,
    unit: '%',
    severity: 'critical',
    enabled: false,
    lastTriggered: null
  },
  {
    id: 4,
    name: '响应时间告警',
    metric: 'response_time',
    operator: '>',
    threshold: 1000,
    unit: 'ms',
    severity: 'warning',
    enabled: true,
    lastTriggered: new Date(Date.now() - 86400000).toISOString()
  }
]);

const alertHistory = ref([
  {
    id: 1,
    action: 'created',
    operator: 'system',
    timestamp: new Date(Date.now() - 3600000).toISOString(),
    comment: '系统自动创建告警'
  },
  {
    id: 2,
    action: 'acknowledged',
    operator: 'admin',
    timestamp: new Date(Date.now() - 1800000).toISOString(),
    comment: '已确认告警，正在处理'
  },
  {
    id: 3,
    action: 'escalated',
    operator: 'system',
    timestamp: new Date(Date.now() - 900000).toISOString(),
    comment: '告警升级，通知管理员'
  }
]);

// 表单验证规则
const ruleRules = {
  name: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
  metric: [{ required: true, message: '请选择监控指标', trigger: 'change' }],
  operator: [{ required: true, message: '请选择操作符', trigger: 'change' }],
  threshold: [{ required: true, message: '请输入阈值', trigger: 'blur' }],
  severity: [{ required: true, message: '请选择告警级别', trigger: 'change' }]
};

// 计算属性
const filteredAlerts = computed(() => {
  let filtered = alerts.value;
  
  // 级别过滤
  if (severityFilter.value !== 'all') {
    filtered = filtered.filter(alert => alert.severity === severityFilter.value);
  }
  
  // 状态过滤
  if (statusFilter.value !== 'all') {
    filtered = filtered.filter(alert => alert.status === statusFilter.value);
  }
  
  // 搜索过滤
  if (searchText.value) {
    const search = searchText.value.toLowerCase();
    filtered = filtered.filter(alert => 
      alert.title.toLowerCase().includes(search) ||
      alert.description.toLowerCase().includes(search) ||
      alert.source.toLowerCase().includes(search)
    );
  }
  
  return filtered;
});

const criticalAlerts = computed(() => {
  return alerts.value.filter(a => a.severity === 'critical' && a.status !== 'resolved').length;
});

const warningAlerts = computed(() => {
  return alerts.value.filter(a => a.severity === 'warning' && a.status !== 'resolved').length;
});

const infoAlerts = computed(() => {
  return alerts.value.filter(a => a.severity === 'info' && a.status !== 'resolved').length;
});

const resolvedAlerts = computed(() => {
  return alerts.value.filter(a => a.status === 'resolved').length;
});

// 方法
const getCpuStatusClass = () => {
  if (cpuUsage.value > 80) return 'critical';
  if (cpuUsage.value > 60) return 'warning';
  return 'normal';
};

const getCpuProgressStatus = () => {
  if (cpuUsage.value > 80) return 'exception';
  if (cpuUsage.value > 60) return 'warning';
  return 'success';
};

const getMemoryStatusClass = () => {
  if (memoryUsage.value > 85) return 'critical';
  if (memoryUsage.value > 70) return 'warning';
  return 'normal';
};

const getMemoryProgressStatus = () => {
  if (memoryUsage.value > 85) return 'exception';
  if (memoryUsage.value > 70) return 'warning';
  return 'success';
};

const getDiskStatusClass = () => {
  if (diskUsage.value > 90) return 'critical';
  if (diskUsage.value > 75) return 'warning';
  return 'normal';
};

const getDiskProgressStatus = () => {
  if (diskUsage.value > 90) return 'exception';
  if (diskUsage.value > 75) return 'warning';
  return 'success';
};

const getNetworkStatusClass = () => {
  if (networkLatency.value > 100) return 'critical';
  if (networkLatency.value > 50) return 'warning';
  return 'normal';
};

const getNetworkTagType = () => {
  if (networkLatency.value > 100) return 'danger';
  if (networkLatency.value > 50) return 'warning';
  return 'success';
};

const getNetworkStatusText = () => {
  if (networkLatency.value > 100) return '网络异常';
  if (networkLatency.value > 50) return '网络较慢';
  return '网络正常';
};

const getServiceIcon = (type) => {
  const iconMap = {
    'api': 'Connection',
    'database': 'Coin',
    'cache': 'Lightning',
    'queue': 'List'
  };
  return iconMap[type] || 'Monitor';
};

const getServiceStatusType = (status) => {
  const typeMap = {
    'healthy': 'success',
    'warning': 'warning',
    'error': 'danger',
    'unknown': 'info'
  };
  return typeMap[status] || 'info';
};

const getServiceStatusText = (status) => {
  const textMap = {
    'healthy': '健康',
    'warning': '警告',
    'error': '错误',
    'unknown': '未知'
  };
  return textMap[status] || '未知';
};

const getSeverityType = (severity) => {
  const typeMap = {
    'critical': 'danger',
    'warning': 'warning',
    'info': 'info'
  };
  return typeMap[severity] || 'info';
};

const getSeverityText = (severity) => {
  const textMap = {
    'critical': '严重',
    'warning': '警告',
    'info': '信息'
  };
  return textMap[severity] || '未知';
};

const getSeverityIcon = (severity) => {
  const iconMap = {
    'critical': 'Warning',
    'warning': 'InfoFilled',
    'info': 'Bell'
  };
  return iconMap[severity] || 'Bell';
};

const getAlertStatusType = (status) => {
  const typeMap = {
    'active': 'danger',
    'acknowledged': 'warning',
    'resolved': 'success'
  };
  return typeMap[status] || 'info';
};

const getAlertStatusText = (status) => {
  const textMap = {
    'active': '活跃',
    'acknowledged': '已确认',
    'resolved': '已解决'
  };
  return textMap[status] || '未知';
};

const getHistoryType = (action) => {
  const typeMap = {
    'created': 'primary',
    'acknowledged': 'warning',
    'resolved': 'success',
    'escalated': 'danger'
  };
  return typeMap[action] || 'info';
};

const getHistoryActionText = (action) => {
  const textMap = {
    'created': '创建告警',
    'acknowledged': '确认告警',
    'resolved': '解决告警',
    'escalated': '告警升级'
  };
  return textMap[action] || '未知操作';
};

const formatUptime = (uptime) => {
  const days = Math.floor(uptime / (24 * 60 * 60 * 1000));
  const hours = Math.floor((uptime % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000));
  const minutes = Math.floor((uptime % (60 * 60 * 1000)) / (60 * 1000));
  
  if (days > 0) {
    return `${days}天 ${hours}小时`;
  } else if (hours > 0) {
    return `${hours}小时 ${minutes}分钟`;
  } else {
    return `${minutes}分钟`;
  }
};

const formatTime = (timestamp) => {
  if (!timestamp) return '-';
  return new Date(timestamp).toLocaleString();
};

const formatDuration = (duration) => {
  if (!duration) return '-';
  const hours = Math.floor(duration / (60 * 60 * 1000));
  const minutes = Math.floor((duration % (60 * 60 * 1000)) / (60 * 1000));
  return `${hours}小时 ${minutes}分钟`;
};

const refreshAlerts = async () => {
  refreshLoading.value = true;
  try {
    const response = await axios.get('/api/management/alerts');
    alerts.value = response.data.alerts || [];
    
    // 更新性能指标
    const metricsResponse = await axios.get('/api/management/metrics');
    const metrics = metricsResponse.data;
    cpuUsage.value = metrics.cpu_usage || 0;
    memoryUsage.value = metrics.memory_usage || 0;
    diskUsage.value = metrics.disk_usage || 0;
    networkLatency.value = metrics.network_latency || 0;
    
    // 更新服务状态
    const servicesResponse = await axios.get('/api/management/services/status');
    services.value = servicesResponse.data.services || [];
    
    ElMessage.success('告警数据已刷新');
  } catch (error) {
    console.error('刷新告警数据失败:', error);
    ElMessage.error('刷新告警数据失败');
  } finally {
    refreshLoading.value = false;
  }
};

const refreshRules = async () => {
  rulesLoading.value = true;
  try {
    const response = await axios.get('/api/management/alert-rules');
    alertRules.value = response.data.rules || [];
    ElMessage.success('告警规则已刷新');
  } catch (error) {
    console.error('刷新告警规则失败:', error);
    ElMessage.error('刷新告警规则失败');
  } finally {
    rulesLoading.value = false;
  }
};

const viewAlertDetails = async (alert) => {
  currentAlert.value = alert;
  
  try {
    const response = await axios.get(`/api/management/alerts/${alert.id}/history`);
    alertHistory.value = response.data.history || [];
  } catch (error) {
    console.error('获取告警历史失败:', error);
    ElMessage.error('获取告警历史失败');
  }
  
  alertDetailsVisible.value = true;
};

const acknowledgeAlert = async (alert) => {
  try {
    await axios.post(`/api/management/alerts/${alert.id}/acknowledge`);
    alert.status = 'acknowledged';
    ElMessage.success('告警已确认');
  } catch (error) {
    console.error('确认告警失败:', error);
    ElMessage.error('确认告警失败');
  }
};

const resolveAlert = async (alert) => {
  try {
    await axios.post(`/api/management/alerts/${alert.id}/resolve`);
    alert.status = 'resolved';
    ElMessage.success('告警已解决');
  } catch (error) {
    console.error('解决告警失败:', error);
    ElMessage.error('解决告警失败');
  }
};

const deleteAlert = async (alert) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除告警 ${alert.id} 吗？`,
      '确认删除',
      { type: 'warning' }
    );
    
    await axios.delete(`/api/management/alerts/${alert.id}`);
    const index = alerts.value.findIndex(a => a.id === alert.id);
    if (index !== -1) {
      alerts.value.splice(index, 1);
    }
    ElMessage.success('告警已删除');
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除告警失败:', error);
      ElMessage.error('删除告警失败');
    }
  }
};

const showAlertRuleDialog = () => {
  ruleEditMode.value = 'create';
  Object.assign(currentRule, {
    name: '',
    metric: '',
    operator: '>',
    threshold: 0,
    unit: '',
    severity: 'warning',
    description: '',
    enabled: true
  });
  ruleDialogVisible.value = true;
};

const editRule = (rule) => {
  ruleEditMode.value = 'edit';
  Object.assign(currentRule, { ...rule });
  ruleDialogVisible.value = true;
};

const saveRule = async () => {
  try {
    saveRuleLoading.value = true;
    
    if (ruleEditMode.value === 'create') {
      const response = await axios.post('/api/management/alert-rules', currentRule);
      alertRules.value.push({
        ...currentRule,
        id: response.data.id,
        lastTriggered: null
      });
      ElMessage.success('告警规则添加成功');
    } else {
      await axios.put(`/api/management/alert-rules/${currentRule.id}`, currentRule);
      const index = alertRules.value.findIndex(r => r.id === currentRule.id);
      if (index !== -1) {
        alertRules.value[index] = { ...currentRule };
      }
      ElMessage.success('告警规则保存成功');
    }
    
    ruleDialogVisible.value = false;
  } catch (error) {
    console.error('保存告警规则失败:', error);
    ElMessage.error('保存告警规则失败');
  } finally {
    saveRuleLoading.value = false;
  }
};

const toggleRule = async (rule) => {
  try {
    await axios.patch(`/api/management/alert-rules/${rule.id}`, {
      enabled: rule.enabled
    });
    ElMessage.success(`告警规则已${rule.enabled ? '启用' : '禁用'}`);
  } catch (error) {
    console.error('切换告警规则状态失败:', error);
    ElMessage.error('切换告警规则状态失败');
    rule.enabled = !rule.enabled; // 回滚状态
  }
};

const deleteRule = async (rule) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除告警规则 ${rule.name} 吗？`,
      '确认删除',
      { type: 'warning' }
    );
    
    await axios.delete(`/api/management/alert-rules/${rule.id}`);
    const index = alertRules.value.findIndex(r => r.id === rule.id);
    if (index !== -1) {
      alertRules.value.splice(index, 1);
    }
    ElMessage.success('告警规则已删除');
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除告警规则失败:', error);
      ElMessage.error('删除告警规则失败');
    }
  }
};

// 定时刷新
let refreshInterval;
let metricsInterval;

// 生命周期
onMounted(() => {
  refreshAlerts();
  refreshRules();
  
  // 每30秒刷新一次告警
  refreshInterval = setInterval(() => {
    refreshAlerts();
  }, 30000);
  
  // 每5秒刷新一次性能指标
  metricsInterval = setInterval(async () => {
    try {
      const response = await axios.get('/api/management/metrics');
      const metrics = response.data;
      cpuUsage.value = metrics.cpu_usage || 0;
      memoryUsage.value = metrics.memory_usage || 0;
      diskUsage.value = metrics.disk_usage || 0;
      networkLatency.value = metrics.network_latency || 0;
    } catch (error) {
      console.error('获取性能指标失败:', error);
    }
  }, 5000);
});

onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval);
  }
  if (metricsInterval) {
    clearInterval(metricsInterval);
  }
});
</script>

<style scoped>
.monitoring-alerts {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.alert-overview {
  margin-bottom: 20px;
}

.overview-card {
  height: 100px;
}

.overview-content {
  display: flex;
  align-items: center;
  gap: 15px;
  height: 100%;
}

.overview-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.overview-icon.critical {
  background-color: #f56c6c;
}

.overview-icon.warning {
  background-color: #e6a23c;
}

.overview-icon.info {
  background-color: #409eff;
}

.overview-icon.resolved {
  background-color: #67c23a;
}

.overview-info h3 {
  margin: 0 0 5px 0;
  font-size: 24px;
  color: #303133;
  font-weight: 600;
}

.overview-info p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.monitoring-section {
  margin-bottom: 20px;
}

.monitoring-card {
  height: 300px;
}

.performance-metrics {
  padding: 10px 0;
}

.metric-item {
  margin-bottom: 20px;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.metric-name {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.metric-value {
  font-size: 16px;
  font-weight: 600;
}

.metric-value.normal {
  color: #67c23a;
}

.metric-value.warning {
  color: #e6a23c;
}

.metric-value.critical {
  color: #f56c6c;
}

.network-status {
  margin-top: 8px;
}

.service-status {
  padding: 10px 0;
  max-height: 250px;
  overflow-y: auto;
}

.service-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #ebeef5;
}

.service-item:last-child {
  border-bottom: none;
}

.service-info {
  flex: 1;
}

.service-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #303133;
  font-weight: 500;
  margin-bottom: 4px;
}

.service-details {
  display: flex;
  gap: 15px;
  font-size: 12px;
  color: #909399;
}

.service-version {
  background-color: #f0f2f5;
  padding: 2px 6px;
  border-radius: 3px;
}

.alerts-list,
.alert-rules {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
}

.alert-title {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.alert-title .title {
  font-weight: 500;
  color: #303133;
}

.alert-title .source {
  font-size: 12px;
  color: #909399;
}

.alert-count {
  display: inline-block;
}

.alert-details {
  height: 60vh;
}

.alert-info {
  padding: 20px 0;
}

.info-group {
  margin-bottom: 20px;
}

.info-group h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.info-group .info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 14px;
}

.info-group .info-item .label {
  color: #606266;
  font-weight: 500;
  min-width: 80px;
}

.info-group .info-item .value {
  color: #303133;
}

.info-group p {
  margin: 0;
  color: #606266;
  line-height: 1.6;
}

.alert-history {
  padding: 20px 0;
  max-height: 400px;
  overflow-y: auto;
}

.history-content h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 14px;
}

.history-content p {
  margin: 0 0 4px 0;
  color: #606266;
  font-size: 12px;
}

.alert-metrics {
  padding: 20px 0;
}

.metrics-chart {
  text-align: center;
}

.metrics-chart h4 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 16px;
}

.chart-placeholder {
  height: 300px;
  background-color: #f5f7fa;
  border: 2px dashed #dcdfe6;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
}

.dialog-footer {
  text-align: right;
}

@media (max-width: 768px) {
  .monitoring-alerts {
    padding: 10px;
  }
  
  .alert-overview,
  .monitoring-section {
    margin-bottom: 15px;
  }
  
  .overview-content {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }
  
  .monitoring-card {
    height: auto;
    min-height: 250px;
  }
  
  .header-actions {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .service-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .service-details {
    flex-direction: column;
    gap: 5px;
  }
  
  .alert-title {
    align-items: flex-start;
  }
}
</style>