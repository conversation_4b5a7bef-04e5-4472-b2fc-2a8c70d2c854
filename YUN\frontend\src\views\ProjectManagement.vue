<template>
  <div class="project-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><FolderOpened /></el-icon>
        项目管理
      </h1>
      <p class="page-description">管理项目组件、监控状态和操作日志</p>
    </div>

    <!-- 项目组件状态 -->
    <el-card class="components-status" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>项目组件状态</span>
          <el-button 
            type="primary" 
            size="small" 
            @click="refreshComponents"
            :loading="refreshLoading"
          >
            <el-icon><Refresh /></el-icon>
            刷新状态
          </el-button>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="8" v-for="component in components" :key="component.name">
          <div class="component-card">
            <div class="component-header">
              <div class="component-icon" :class="component.status">
                <el-icon><component :is="component.icon" /></el-icon>
              </div>
              <div class="component-info">
                <h3>{{ component.title }}</h3>
                <p class="status-text" :class="component.status">
                  {{ getStatusText(component.status) }}
                </p>
              </div>
              <div class="component-actions">
                <el-dropdown @command="handleComponentAction">
                  <el-button link size="small">
                    <el-icon><MoreFilled /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item 
                        :command="{action: 'start', component: component.name}"
                        :disabled="component.status === 'running'"
                      >
                        <el-icon><VideoPlay /></el-icon>
                        启动
                      </el-dropdown-item>
                      <el-dropdown-item 
                        :command="{action: 'stop', component: component.name}"
                        :disabled="component.status === 'stopped'"
                      >
                        <el-icon><VideoPause /></el-icon>
                        停止
                      </el-dropdown-item>
                      <el-dropdown-item 
                        :command="{action: 'restart', component: component.name}"
                      >
                        <el-icon><Refresh /></el-icon>
                        重启
                      </el-dropdown-item>
                      <el-dropdown-item 
                        :command="{action: 'logs', component: component.name}"
                      >
                        <el-icon><Document /></el-icon>
                        查看日志
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
            
            <div class="component-details">
              <div class="detail-item">
                <span class="label">运行时间:</span>
                <span class="value">{{ component.uptime || '未知' }}</span>
              </div>
              <div class="detail-item">
                <span class="label">最后更新:</span>
                <span class="value">{{ formatTime(component.lastUpdate) }}</span>
              </div>
              <div class="detail-item">
                <span class="label">版本:</span>
                <span class="value">{{ component.version || '未知' }}</span>
              </div>
            </div>
            
            <div class="component-metrics" v-if="component.metrics">
              <div class="metric-item">
                <span class="metric-label">CPU:</span>
                <el-progress 
                  :percentage="component.metrics.cpu || 0" 
                  :stroke-width="6"
                  :show-text="false"
                />
                <span class="metric-value">{{ component.metrics.cpu || 0 }}%</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">内存:</span>
                <el-progress 
                  :percentage="component.metrics.memory || 0" 
                  :stroke-width="6"
                  :show-text="false"
                  color="#e6a23c"
                />
                <span class="metric-value">{{ component.metrics.memory || 0 }}%</span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 操作日志 -->
    <el-card class="operation-logs" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>操作日志</span>
          <div class="log-controls">
            <el-select v-model="logFilter" size="small" style="width: 120px; margin-right: 10px;">
              <el-option label="全部" value="all" />
              <el-option label="启动" value="start" />
              <el-option label="停止" value="stop" />
              <el-option label="重启" value="restart" />
              <el-option label="错误" value="error" />
            </el-select>
            <el-button size="small" @click="clearLogs">
              <el-icon><Delete /></el-icon>
              清空
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="logs-container">
        <div 
          v-for="(log, index) in filteredLogs" 
          :key="index"
          class="log-entry"
          :class="log.level"
        >
          <span class="log-time">{{ formatTime(log.timestamp) }}</span>
          <span class="log-component">{{ log.component }}</span>
          <span class="log-action">{{ log.action }}</span>
          <span class="log-message">{{ log.message }}</span>
          <span class="log-status" :class="log.status">{{ log.status }}</span>
        </div>
        
        <div v-if="filteredLogs.length === 0" class="no-logs">
          <el-empty description="暂无日志" />
        </div>
      </div>
    </el-card>

    <!-- 批量操作 -->
    <el-card class="batch-operations" shadow="hover">
      <template #header>
        <span>批量操作</span>
      </template>
      
      <div class="batch-controls">
        <div class="selection-area">
          <el-checkbox 
            v-model="selectAll" 
            @change="handleSelectAll"
            :indeterminate="isIndeterminate"
          >
            全选
          </el-checkbox>
          <el-checkbox-group v-model="selectedComponents" @change="handleSelectionChange">
            <el-checkbox 
              v-for="component in components" 
              :key="component.name"
              :value="component.name"
            >
              {{ component.title }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
        
        <div class="batch-actions">
          <el-button-group>
            <el-button 
              type="success" 
              @click="batchStart"
              :disabled="selectedComponents.length === 0"
              :loading="batchLoading.start"
            >
              <el-icon><VideoPlay /></el-icon>
              批量启动
            </el-button>
            
            <el-button 
              type="warning" 
              @click="batchRestart"
              :disabled="selectedComponents.length === 0"
              :loading="batchLoading.restart"
            >
              <el-icon><Refresh /></el-icon>
              批量重启
            </el-button>
            
            <el-button 
              type="danger" 
              @click="batchStop"
              :disabled="selectedComponents.length === 0"
              :loading="batchLoading.stop"
            >
              <el-icon><VideoPause /></el-icon>
              批量停止
            </el-button>
          </el-button-group>
        </div>
      </div>
    </el-card>

    <!-- 日志查看对话框 -->
    <el-dialog 
      v-model="logDialogVisible" 
      :title="`${currentLogComponent} 组件日志`"
      width="80%"
      top="5vh"
    >
      <div class="log-viewer">
        <div class="log-viewer-header">
          <el-button-group size="small">
            <el-button @click="refreshComponentLogs">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button @click="downloadLogs">
              <el-icon><Download /></el-icon>
              下载
            </el-button>
          </el-button-group>
        </div>
        
        <div class="log-content">
          <pre v-if="componentLogs.length > 0">{{ componentLogs.join('\n') }}</pre>
          <el-empty v-else description="暂无日志" />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  FolderOpened,
  Refresh,
  MoreFilled,
  VideoPlay,
  VideoPause,
  Document,
  Delete,
  Download,
  Monitor,
  DataBoard,
  Setting
} from '@element-plus/icons-vue';
import axios from 'axios';

// 响应式数据
const refreshLoading = ref(false);
const logFilter = ref('all');
const selectAll = ref(false);
const selectedComponents = ref([]);
const logDialogVisible = ref(false);
const currentLogComponent = ref('');
const componentLogs = ref([]);
const statusCheckInterval = ref(null);

const batchLoading = reactive({
  start: false,
  restart: false,
  stop: false
});

const components = ref([
  {
    name: 'backend',
    title: '后端服务',
    icon: 'Monitor',
    status: 'running',
    uptime: '2小时30分钟',
    lastUpdate: new Date().toISOString(),
    version: '1.0.0',
    metrics: {
      cpu: 25,
      memory: 45
    }
  },
  {
    name: 'frontend',
    title: '前端服务',
    icon: 'Monitor',
    status: 'running',
    uptime: '2小时28分钟',
    lastUpdate: new Date().toISOString(),
    version: '1.0.0',
    metrics: {
      cpu: 15,
      memory: 30
    }
  },
  {
    name: 'database',
    title: '数据库',
    icon: 'DataBoard',
    status: 'running',
    uptime: '5天12小时',
    lastUpdate: new Date().toISOString(),
    version: '14.2',
    metrics: {
      cpu: 10,
      memory: 60
    }
  }
]);

const operationLogs = ref([
  {
    timestamp: new Date().toISOString(),
    component: 'backend',
    action: 'start',
    message: '后端服务启动成功',
    status: 'success',
    level: 'info'
  },
  {
    timestamp: new Date(Date.now() - 60000).toISOString(),
    component: 'frontend',
    action: 'start',
    message: '前端服务启动成功',
    status: 'success',
    level: 'info'
  }
]);

// 计算属性
const filteredLogs = computed(() => {
  if (logFilter.value === 'all') {
    return operationLogs.value;
  }
  return operationLogs.value.filter(log => log.action === logFilter.value || log.level === logFilter.value);
});

const isIndeterminate = computed(() => {
  const selectedCount = selectedComponents.value.length;
  return selectedCount > 0 && selectedCount < components.value.length;
});

// 方法
const getStatusText = (status) => {
  const statusMap = {
    'running': '运行中',
    'stopped': '已停止',
    'error': '错误',
    'starting': '启动中',
    'stopping': '停止中'
  };
  return statusMap[status] || '未知';
};

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleString();
};

const refreshComponents = async () => {
  refreshLoading.value = true;
  try {
    const response = await axios.get('/api/management/project/components');
    components.value = response.data;
    
    addLog('system', 'refresh', '组件状态已刷新', 'success');
    ElMessage.success('组件状态已刷新');
  } catch (error) {
    console.error('刷新组件状态失败:', error);
    addLog('system', 'refresh', `刷新失败: ${error.message}`, 'error');
    ElMessage.error('刷新组件状态失败');
  } finally {
    refreshLoading.value = false;
  }
};

const handleComponentAction = async (command) => {
  const { action, component } = command;
  
  try {
    let confirmMessage = '';
    switch (action) {
      case 'start':
        confirmMessage = `确定要启动 ${getComponentTitle(component)} 吗？`;
        break;
      case 'stop':
        confirmMessage = `确定要停止 ${getComponentTitle(component)} 吗？`;
        break;
      case 'restart':
        confirmMessage = `确定要重启 ${getComponentTitle(component)} 吗？`;
        break;
      case 'logs':
        showComponentLogs(component);
        return;
    }
    
    await ElMessageBox.confirm(confirmMessage, '确认操作', {
      type: 'warning'
    });
    
    // 更新组件状态为操作中
    updateComponentStatus(component, action + 'ing');
    
    const response = await axios.post(`/api/management/project/components/${component}/${action}`);
    
    // 更新组件状态
    updateComponentStatus(component, response.data.status);
    
    addLog(component, action, `${getComponentTitle(component)} ${action} 成功`, 'success');
    ElMessage.success(`${getComponentTitle(component)} ${action} 成功`);
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${action} ${component} 失败:`, error);
      updateComponentStatus(component, 'error');
      addLog(component, action, `${getComponentTitle(component)} ${action} 失败: ${error.message}`, 'error');
      ElMessage.error(`${getComponentTitle(component)} ${action} 失败`);
    }
  }
};

const getComponentTitle = (componentName) => {
  const component = components.value.find(c => c.name === componentName);
  return component ? component.title : componentName;
};

const updateComponentStatus = (componentName, status) => {
  const component = components.value.find(c => c.name === componentName);
  if (component) {
    component.status = status;
    component.lastUpdate = new Date().toISOString();
  }
};

const showComponentLogs = async (componentName) => {
  currentLogComponent.value = getComponentTitle(componentName);
  logDialogVisible.value = true;
  
  try {
    const response = await axios.get(`/api/management/project/components/${componentName}/logs`);
    componentLogs.value = response.data.logs || [];
  } catch (error) {
    console.error('获取组件日志失败:', error);
    componentLogs.value = ['获取日志失败: ' + error.message];
  }
};

const refreshComponentLogs = async () => {
  if (currentLogComponent.value) {
    const componentName = components.value.find(c => c.title === currentLogComponent.value)?.name;
    if (componentName) {
      await showComponentLogs(componentName);
    }
  }
};

const downloadLogs = () => {
  const content = componentLogs.value.join('\n');
  const blob = new Blob([content], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `${currentLogComponent.value}_logs_${new Date().toISOString().slice(0, 10)}.txt`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

const handleSelectAll = (checked) => {
  if (checked) {
    selectedComponents.value = components.value.map(c => c.name);
  } else {
    selectedComponents.value = [];
  }
};

const handleSelectionChange = () => {
  selectAll.value = selectedComponents.value.length === components.value.length;
};

const batchStart = async () => {
  await batchOperation('start', '启动');
};

const batchRestart = async () => {
  await batchOperation('restart', '重启');
};

const batchStop = async () => {
  await batchOperation('stop', '停止');
};

const batchOperation = async (action, actionText) => {
  try {
    await ElMessageBox.confirm(
      `确定要${actionText}选中的 ${selectedComponents.value.length} 个组件吗？`,
      '确认批量操作',
      { type: 'warning' }
    );
    
    batchLoading[action] = true;
    
    const promises = selectedComponents.value.map(async (componentName) => {
      try {
        updateComponentStatus(componentName, action + 'ing');
        const response = await axios.post(`/api/management/project/components/${componentName}/${action}`);
        updateComponentStatus(componentName, response.data.status);
        addLog(componentName, action, `${getComponentTitle(componentName)} ${actionText}成功`, 'success');
        return { component: componentName, success: true };
      } catch (error) {
        updateComponentStatus(componentName, 'error');
        addLog(componentName, action, `${getComponentTitle(componentName)} ${actionText}失败: ${error.message}`, 'error');
        return { component: componentName, success: false, error: error.message };
      }
    });
    
    const results = await Promise.all(promises);
    const successCount = results.filter(r => r.success).length;
    const failCount = results.length - successCount;
    
    if (failCount === 0) {
      ElMessage.success(`批量${actionText}完成，${successCount} 个组件成功`);
    } else {
      ElMessage.warning(`批量${actionText}完成，${successCount} 个成功，${failCount} 个失败`);
    }
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`批量${actionText}失败:`, error);
      ElMessage.error(`批量${actionText}失败`);
    }
  } finally {
    batchLoading[action] = false;
  }
};

const addLog = (component, action, message, status) => {
  operationLogs.value.unshift({
    timestamp: new Date().toISOString(),
    component,
    action,
    message,
    status,
    level: status === 'error' ? 'error' : 'info'
  });
  
  // 保持日志数量
  if (operationLogs.value.length > 100) {
    operationLogs.value = operationLogs.value.slice(0, 50);
  }
};

const clearLogs = () => {
  operationLogs.value = [];
  ElMessage.success('日志已清空');
};

const startStatusCheck = () => {
  statusCheckInterval.value = setInterval(() => {
    refreshComponents();
  }, 30000); // 每30秒检查一次
};

const stopStatusCheck = () => {
  if (statusCheckInterval.value) {
    clearInterval(statusCheckInterval.value);
    statusCheckInterval.value = null;
  }
};

// 生命周期
onMounted(() => {
  addLog('system', 'init', '项目管理页面已加载', 'success');
  refreshComponents();
  startStatusCheck();
});

onUnmounted(() => {
  stopStatusCheck();
});
</script>

<style scoped>
.project-management {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.components-status {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.component-card {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 16px;
  background-color: white;
  transition: all 0.3s ease;
}

.component-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.component-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.component-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.component-icon.running {
  background-color: #67c23a;
}

.component-icon.stopped {
  background-color: #909399;
}

.component-icon.error {
  background-color: #f56c6c;
}

.component-icon.starting,
.component-icon.stopping {
  background-color: #e6a23c;
}

.component-info {
  flex: 1;
}

.component-info h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #303133;
}

.status-text {
  margin: 0;
  font-size: 12px;
  font-weight: 500;
}

.status-text.running {
  color: #67c23a;
}

.status-text.stopped {
  color: #909399;
}

.status-text.error {
  color: #f56c6c;
}

.status-text.starting,
.status-text.stopping {
  color: #e6a23c;
}

.component-actions {
  margin-left: auto;
}

.component-details {
  margin-bottom: 12px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 12px;
}

.detail-item .label {
  color: #909399;
}

.detail-item .value {
  color: #303133;
}

.component-metrics {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.metric-label {
  font-size: 12px;
  color: #606266;
  min-width: 40px;
}

.metric-value {
  font-size: 12px;
  color: #303133;
  min-width: 35px;
  text-align: right;
}

.operation-logs {
  margin-bottom: 20px;
}

.log-controls {
  display: flex;
  align-items: center;
}

.logs-container {
  max-height: 300px;
  overflow-y: auto;
  background-color: #fafafa;
  border-radius: 4px;
  padding: 10px;
}

.log-entry {
  display: grid;
  grid-template-columns: 140px 80px 60px 1fr 80px;
  gap: 10px;
  padding: 8px 0;
  border-bottom: 1px solid #ebeef5;
  font-size: 12px;
  align-items: center;
}

.log-entry:last-child {
  border-bottom: none;
}

.log-time {
  color: #909399;
  font-family: 'Courier New', monospace;
}

.log-component {
  color: #606266;
  font-weight: 500;
}

.log-action {
  color: #409eff;
  font-weight: 500;
}

.log-message {
  color: #303133;
}

.log-status {
  text-align: center;
  font-weight: 500;
}

.log-status.success {
  color: #67c23a;
}

.log-status.error {
  color: #f56c6c;
}

.no-logs {
  text-align: center;
  padding: 40px 0;
}

.batch-operations {
  margin-bottom: 20px;
}

.batch-controls {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.selection-area {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.batch-actions {
  display: flex;
  justify-content: flex-end;
}

.log-viewer {
  height: 60vh;
  display: flex;
  flex-direction: column;
}

.log-viewer-header {
  margin-bottom: 10px;
  display: flex;
  justify-content: flex-end;
}

.log-content {
  flex: 1;
  background-color: #1e1e1e;
  color: #d4d4d4;
  padding: 16px;
  border-radius: 4px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.log-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

@media (max-width: 768px) {
  .project-management {
    padding: 10px;
  }
  
  .component-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .component-actions {
    margin-left: 0;
    align-self: flex-end;
  }
  
  .log-entry {
    grid-template-columns: 1fr;
    gap: 4px;
  }
  
  .batch-controls {
    gap: 12px;
  }
  
  .selection-area {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>