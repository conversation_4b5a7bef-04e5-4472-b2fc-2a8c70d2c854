<template>
  <div class="health-records-container">
    <h1>健康记录管理</h1>
    
    <el-card class="filter-card">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="用户">
          <el-select v-model="filterForm.customId" placeholder="选择用户" clearable>
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="user.username"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="类型">
          <el-select v-model="filterForm.type" placeholder="记录类型" clearable>
            <el-option label="血压" value="blood_pressure" />
            <el-option label="血糖" value="blood_glucose" />
            <el-option label="体重" value="weight" />
            <el-option label="体温" value="temperature" />
            <el-option label="心率" value="heart_rate" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleFilter">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <el-card class="records-card">
      <template #header>
        <div class="card-header">
          <span>健康记录列表</span>
          <el-button type="primary" size="small" @click="handleAddRecord">添加记录</el-button>
        </div>
      </template>
      
      <el-table :data="records" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="user.username" label="用户" />
        <el-table-column prop="type" label="类型">
          <template #default="scope">
            <el-tag :type="getRecordTypeTag(scope.row.type)">{{ getRecordTypeLabel(scope.row.type) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="value" label="数值" />
        <el-table-column prop="unit" label="单位" />
        <el-table-column prop="recorded_at" label="记录时间" />
        <el-table-column prop="notes" label="备注" show-overflow-tooltip />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="handleEditRecord(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDeleteRecord(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 健康记录表单对话框 -->
    <el-dialog 
      :title="dialogTitle" 
      v-model="dialogVisible" 
      width="500px"
    >
      <el-form 
        ref="recordFormRef" 
        :model="recordForm" 
        :rules="recordRules" 
        label-width="100px"
      >
        <el-form-item label="用户" prop="customId">
          <el-select v-model="recordForm.customId" placeholder="选择用户">
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="user.username"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="类型" prop="type">
          <el-select v-model="recordForm.type" placeholder="记录类型">
            <el-option label="血压" value="blood_pressure" />
            <el-option label="血糖" value="blood_glucose" />
            <el-option label="体重" value="weight" />
            <el-option label="体温" value="temperature" />
            <el-option label="心率" value="heart_rate" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="数值" prop="value">
          <el-input v-model="recordForm.value" />
        </el-form-item>
        
        <el-form-item label="单位" prop="unit">
          <el-input v-model="recordForm.unit" />
        </el-form-item>
        
        <el-form-item label="记录时间" prop="recordedAt">
          <el-date-picker
            v-model="recordForm.recordedAt"
            type="datetime"
            placeholder="选择日期时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        
        <el-form-item label="备注" prop="notes">
          <el-input v-model="recordForm.notes" type="textarea" :rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitRecordForm" :loading="submitting">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import axios from 'axios'

const users = ref([])
const records = ref([])
const loading = ref(false)
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})
const filterForm = reactive({
  customId: '',
  type: '',
  dateRange: []
})

// 统一聚合接口请求
const fetchUserHealthRecords = async (customId) => {
  loading.value = true
  try {
    const response = await axios.get(`/api/user-health-records/user/${customId}`)
    // 分类处理
    const allRecords = response.data || []
    // 这里只筛选 type 为 health 的记录
    const healthRecords = allRecords.filter(r => r.type === 'health')
    // 支持筛选
    let filtered = healthRecords
    if (filterForm.type) {
      filtered = filtered.filter(r => r.sub_type === filterForm.type)
    }
    if (filterForm.dateRange && filterForm.dateRange.length === 2) {
      filtered = filtered.filter(r => r.recorded_at >= filterForm.dateRange[0] && r.recorded_at <= filterForm.dateRange[1])
    }
    // 分页
    pagination.total = filtered.length
    const start = (pagination.currentPage - 1) * pagination.pageSize
    const end = start + pagination.pageSize
    records.value = filtered.slice(start, end)
  } catch (error) {
    ElMessage.error('获取健康资料失败')
  } finally {
    loading.value = false
  }
}

const fetchUsers = async () => {
  try {
    const response = await axios.get('/api/users')
    
    // 验证返回数据格式
    if (response.data && Array.isArray(response.data)) {
      users.value = response.data
    } else if (response.data && response.data.users && Array.isArray(response.data.users)) {
      users.value = response.data.users
    } else {
      console.warn('用户数据格式不正确:', response.data)
      users.value = []
      ElMessage.warning('用户数据格式异常，已重置为空列表')
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    const errorMsg = error.response?.data?.message || error.message || '未知错误'
    ElMessage.error(`获取用户列表失败: ${errorMsg}`)
    users.value = []
  }
}

const handleFilter = () => {
  pagination.currentPage = 1
  if (filterForm.customId) fetchUserHealthRecords(filterForm.customId)
}
const resetFilter = () => {
  filterForm.type = ''
  filterForm.dateRange = []
  pagination.currentPage = 1
  if (filterForm.customId) fetchUserHealthRecords(filterForm.customId)
}
const handleSizeChange = (size) => {
  pagination.pageSize = size
  if (filterForm.customId) fetchUserHealthRecords(filterForm.customId)
}
const handleCurrentChange = (page) => {
  pagination.currentPage = page
  if (filterForm.customId) fetchUserHealthRecords(filterForm.customId)
}

onMounted(() => {
  fetchUsers()
})
</script>

<style scoped>
.health-records-container {
  padding: 20px;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
}

.records-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
