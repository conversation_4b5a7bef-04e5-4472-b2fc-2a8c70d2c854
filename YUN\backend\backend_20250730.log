2025-07-30 07:50:24,395 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-30 07:50:24,461 - auth_service - INFO - 统一认证服务初始化完成
2025-07-30 07:50:24,928 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-30 07:50:25,095 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-30 07:50:26,724 - BackendMockDataManager - INFO - 后端模拟数据模式已启用
2025-07-30 07:50:28,936 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\psutil\__init__.py
2025-07-30 07:50:28,944 - fallback_manager - DEBUG - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-07-30 07:50:28,946 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-07-30 07:50:35,080 - health_monitor - INFO - 健康监控器初始化完成
2025-07-30 07:50:35,218 - app.core.system_monitor - INFO - 已加载 288 个历史数据点
2025-07-30 07:50:35,245 - app.core.alert_detector - INFO - 已加载 6 个告警规则
2025-07-30 07:50:35,247 - app.core.alert_detector - INFO - 已加载 0 个当前告警
2025-07-30 07:50:35,254 - app.core.alert_detector - INFO - 已加载 370 个历史告警
2025-07-30 07:50:35,261 - app.core.alert_detector - INFO - 已加载 1 个通知渠道
2025-07-30 07:50:35,356 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-30 07:50:35,586 - alert_manager - INFO - 已初始化默认告警规则
2025-07-30 07:50:35,707 - alert_manager - INFO - 已初始化默认通知渠道
2025-07-30 07:50:35,894 - alert_manager - INFO - 告警管理器初始化完成
2025-07-30 07:50:38,241 - db_service - INFO - 数据库引擎和会话工厂创建成功
2025-07-30 07:50:38,335 - db_service - INFO - 数据库服务初始化完成
2025-07-30 07:50:38,425 - notification_service - INFO - 通知服务初始化完成
2025-07-30 07:50:38,443 - main - INFO - 错误处理模块导入成功
2025-07-30 07:50:38,579 - main - INFO - 监控模块导入成功
2025-07-30 07:50:38,697 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-07-30 07:50:45,009 - app.services.ocr_service - WARNING - OpenCV未安装，图像预处理功能将不可用
2025-07-30 07:50:45,267 - main - INFO - 应用启动中...
2025-07-30 07:50:45,279 - error_handling - INFO - 错误处理已设置
2025-07-30 07:50:45,323 - main - INFO - 错误处理系统初始化完成
2025-07-30 07:50:45,373 - monitoring - INFO - 添加指标端点成功: /metrics
2025-07-30 07:50:45,429 - monitoring - INFO - 添加健康检查端点成功: /health
2025-07-30 07:50:45,464 - monitoring - INFO - 添加详细健康检查端点成功: /api/health/detailed
2025-07-30 07:50:45,524 - monitoring - INFO - 系统信息初始化完成: Markey, Windows-10-10.0.19045-SP0, Python 3.13.2, CPU核心数: 4
2025-07-30 07:50:45,692 - monitoring - INFO - 启动资源监控线程成功
2025-07-30 07:50:45,764 - monitoring - INFO - 监控系统初始化成功（不使用中间件）
2025-07-30 07:50:45,898 - monitoring - DEBUG - 资源指标更新: 内存使用率 43.6%, CPU使用率 100.0%
2025-07-30 07:50:45,899 - monitoring - INFO - 监控系统初始化完成
2025-07-30 07:50:45,919 - main - INFO - 监控系统初始化完成
2025-07-30 07:50:45,922 - app.db.init_db - INFO - 所有模型导入成功
2025-07-30 07:50:45,925 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-07-30 07:50:45,940 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 07:50:45,977 - app.db.init_db - INFO - 所有模型导入成功
2025-07-30 07:50:46,043 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-07-30 07:50:46,066 - app.db.init_db - INFO - 正在运行数据库迁移...
2025-07-30 07:50:46,089 - app.db.init_db - INFO - 正在检查并更新数据库表结构...
2025-07-30 07:50:46,138 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-30 07:50:46,219 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alerts")
2025-07-30 07:50:46,277 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:46,349 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_rules")
2025-07-30 07:50:46,354 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:46,429 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_channels")
2025-07-30 07:50:46,505 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:46,632 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-30 07:50:46,810 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:46,934 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_records")
2025-07-30 07:50:47,055 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:47,123 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_overviews")
2025-07-30 07:50:47,220 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:47,373 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medical_records")
2025-07-30 07:50:47,493 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:47,661 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("inpatient_records")
2025-07-30 07:50:47,837 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:47,906 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("surgery_records")
2025-07-30 07:50:48,002 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:48,079 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_reports")
2025-07-30 07:50:48,191 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:48,393 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_report_items")
2025-07-30 07:50:48,583 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:48,690 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_reports")
2025-07-30 07:50:48,732 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:48,791 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("examination_reports")
2025-07-30 07:50:48,917 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:49,026 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("documents")
2025-07-30 07:50:49,088 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:49,183 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("other_records")
2025-07-30 07:50:49,265 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:49,326 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("operation_logs")
2025-07-30 07:50:49,381 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:49,422 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("follow_up_records")
2025-07-30 07:50:49,477 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:49,556 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_diaries")
2025-07-30 07:50:49,662 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:49,769 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("registration_records")
2025-07-30 07:50:49,907 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:50,064 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("prescription_records")
2025-07-30 07:50:50,133 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:50,216 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("laboratory_records")
2025-07-30 07:50:50,329 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:50,477 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_records")
2025-07-30 07:50:50,606 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:50,660 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medications")
2025-07-30 07:50:50,726 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:50,882 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medication_usages")
2025-07-30 07:50:50,925 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:51,003 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("role_applications")
2025-07-30 07:50:51,088 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:51,165 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessments")
2025-07-30 07:50:51,248 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:51,332 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_items")
2025-07-30 07:50:51,424 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:51,516 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_responses")
2025-07-30 07:50:51,579 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:51,610 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_templates")
2025-07-30 07:50:51,663 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:51,695 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_template_questions")
2025-07-30 07:50:51,755 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:51,854 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaires")
2025-07-30 07:50:51,966 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:52,028 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_items")
2025-07-30 07:50:52,112 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:52,205 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_responses")
2025-07-30 07:50:52,314 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:52,390 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_answers")
2025-07-30 07:50:52,495 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:52,608 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_templates")
2025-07-30 07:50:52,752 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:52,862 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_template_questions")
2025-07-30 07:50:52,959 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:53,026 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_results")
2025-07-30 07:50:53,110 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:53,212 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_results")
2025-07-30 07:50:53,271 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:53,406 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("report_templates")
2025-07-30 07:50:53,462 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:53,525 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_distributions")
2025-07-30 07:50:53,558 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:53,600 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_distributions")
2025-07-30 07:50:53,671 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:53,759 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("service_stats")
2025-07-30 07:50:53,877 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:50:53,919 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-30 07:50:53,957 - app.db.init_db - INFO - 数据库表结构检查和更新完成
2025-07-30 07:50:54,055 - app.db.init_db - INFO - 模型关系初始化完成
2025-07-30 07:50:54,112 - app.db.init_db - INFO - 模型关系设置完成
2025-07-30 07:50:54,175 - main - INFO - 数据库初始化完成（强制重建）
2025-07-30 07:50:54,217 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 07:50:54,243 - main - INFO - 数据库连接正常
2025-07-30 07:50:54,304 - main - INFO - 开始初始化模板数据
2025-07-30 07:50:54,340 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 07:50:55,337 - app.db.init_templates - INFO - 开始初始化评估量表模板...
2025-07-30 07:50:55,422 - app.db.init_templates - INFO - 成功初始化 5 个评估量表模板
2025-07-30 07:50:55,486 - app.db.init_templates - INFO - 开始初始化调查问卷模板...
2025-07-30 07:50:55,568 - app.db.init_templates - INFO - 成功初始化 5 个调查问卷模板
2025-07-30 07:50:55,598 - main - INFO - 模板数据初始化完成
2025-07-30 07:50:55,609 - main - INFO - 数据库表结构已经标准化，不需要迁移
2025-07-30 07:50:55,638 - main - INFO - 应用启动完成
2025-07-30 07:51:02,745 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.7%, CPU使用率 100.0%
2025-07-30 07:51:19,576 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.3%, CPU使用率 100.0%
2025-07-30 07:51:34,278 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 50.5%, 磁盘: 94.2%
2025-07-30 07:51:37,042 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.0%, CPU使用率 100.0%
2025-07-30 07:51:53,980 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.5%, CPU使用率 100.0%
2025-07-30 07:52:09,646 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.1%, CPU使用率 100.0%
2025-07-30 07:52:24,934 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.5%, CPU使用率 100.0%
2025-07-30 07:52:37,143 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 62.1%, 磁盘: 94.2%
2025-07-30 07:52:40,807 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.5%, CPU使用率 100.0%
2025-07-30 07:52:56,216 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.0%, CPU使用率 100.0%
2025-07-30 07:53:03,599 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-30 07:53:03,666 - main - INFO - 请求没有认证头部
2025-07-30 07:53:03,675 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 07:53:03,680 - main - INFO - --- 请求结束: 200 ---

2025-07-30 07:53:06,395 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 07:53:06,398 - main - INFO - 请求没有认证头部
2025-07-30 07:53:06,402 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 07:53:06,407 - app.core.db_connection - DEBUG - 当前线程ID: 17468
2025-07-30 07:53:06,408 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-30 07:53:06,416 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-30 07:53:06,420 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-30 07:53:06,425 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 07:53:06,433 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-30 07:53:08,597 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-30 07:53:08,626 - main - INFO - --- 请求结束: 200 ---

2025-07-30 07:53:11,540 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 100.0%
2025-07-30 07:53:27,358 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.1%, CPU使用率 100.0%
2025-07-30 07:57:52,725 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-30 07:57:52,904 - auth_service - INFO - 统一认证服务初始化完成
2025-07-30 07:57:53,421 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-30 07:57:53,441 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-30 07:57:54,445 - BackendMockDataManager - INFO - 后端模拟数据模式已启用
2025-07-30 07:57:57,357 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\psutil\__init__.py
2025-07-30 07:57:57,361 - fallback_manager - DEBUG - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-07-30 07:57:57,370 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-07-30 07:57:57,483 - health_monitor - INFO - 健康监控器初始化完成
2025-07-30 07:57:57,505 - app.core.system_monitor - INFO - 已加载 288 个历史数据点
2025-07-30 07:57:57,541 - app.core.alert_detector - INFO - 已加载 6 个告警规则
2025-07-30 07:57:57,623 - app.core.alert_detector - INFO - 已加载 0 个当前告警
2025-07-30 07:57:57,675 - app.core.alert_detector - INFO - 已加载 370 个历史告警
2025-07-30 07:57:57,745 - app.core.alert_detector - INFO - 已加载 1 个通知渠道
2025-07-30 07:57:57,822 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-30 07:57:57,837 - alert_manager - INFO - 已初始化默认告警规则
2025-07-30 07:57:57,838 - alert_manager - INFO - 已初始化默认通知渠道
2025-07-30 07:57:57,842 - alert_manager - INFO - 告警管理器初始化完成
2025-07-30 07:57:59,657 - db_service - INFO - 数据库引擎和会话工厂创建成功
2025-07-30 07:57:59,678 - db_service - INFO - 数据库服务初始化完成
2025-07-30 07:57:59,693 - notification_service - INFO - 通知服务初始化完成
2025-07-30 07:57:59,703 - main - INFO - 错误处理模块导入成功
2025-07-30 07:57:59,821 - main - INFO - 监控模块导入成功
2025-07-30 07:57:59,841 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-07-30 07:58:10,040 - app.services.ocr_service - WARNING - OpenCV未安装，图像预处理功能将不可用
2025-07-30 07:58:10,431 - main - INFO - 应用启动中...
2025-07-30 07:58:10,574 - error_handling - INFO - 错误处理已设置
2025-07-30 07:58:10,847 - main - INFO - 错误处理系统初始化完成
2025-07-30 07:58:11,035 - monitoring - INFO - 添加指标端点成功: /metrics
2025-07-30 07:58:11,292 - monitoring - INFO - 添加健康检查端点成功: /health
2025-07-30 07:58:11,389 - monitoring - INFO - 添加详细健康检查端点成功: /api/health/detailed
2025-07-30 07:58:11,691 - monitoring - INFO - 系统信息初始化完成: Markey, Windows-10-10.0.19045-SP0, Python 3.13.2, CPU核心数: 4
2025-07-30 07:58:11,958 - monitoring - INFO - 启动资源监控线程成功
2025-07-30 07:58:12,505 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.2%, CPU使用率 100.0%
2025-07-30 07:58:12,506 - monitoring - INFO - 监控系统初始化成功（不使用中间件）
2025-07-30 07:58:13,231 - monitoring - INFO - 监控系统初始化完成
2025-07-30 07:58:13,470 - main - INFO - 监控系统初始化完成
2025-07-30 07:58:13,775 - app.db.init_db - INFO - 所有模型导入成功
2025-07-30 07:58:13,790 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-07-30 07:58:13,819 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 07:58:13,821 - app.db.init_db - INFO - 所有模型导入成功
2025-07-30 07:58:13,823 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-07-30 07:58:13,839 - app.db.init_db - INFO - 正在运行数据库迁移...
2025-07-30 07:58:13,850 - app.db.init_db - INFO - 正在检查并更新数据库表结构...
2025-07-30 07:58:13,858 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-30 07:58:13,867 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alerts")
2025-07-30 07:58:13,869 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:13,874 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_rules")
2025-07-30 07:58:13,881 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:13,885 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_channels")
2025-07-30 07:58:13,887 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:13,932 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-30 07:58:13,972 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:13,975 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_records")
2025-07-30 07:58:13,976 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:13,983 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_overviews")
2025-07-30 07:58:13,985 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:13,986 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medical_records")
2025-07-30 07:58:13,988 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:13,990 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("inpatient_records")
2025-07-30 07:58:14,036 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:14,153 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("surgery_records")
2025-07-30 07:58:14,256 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:14,332 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_reports")
2025-07-30 07:58:14,446 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:14,483 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_report_items")
2025-07-30 07:58:14,513 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:14,532 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_reports")
2025-07-30 07:58:14,537 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:14,542 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("examination_reports")
2025-07-30 07:58:14,550 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:14,555 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("documents")
2025-07-30 07:58:14,581 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:14,631 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("other_records")
2025-07-30 07:58:14,654 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:14,679 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("operation_logs")
2025-07-30 07:58:14,684 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:14,692 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("follow_up_records")
2025-07-30 07:58:14,697 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:14,703 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_diaries")
2025-07-30 07:58:14,707 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:14,714 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("registration_records")
2025-07-30 07:58:14,719 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:14,722 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("prescription_records")
2025-07-30 07:58:14,725 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:14,739 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("laboratory_records")
2025-07-30 07:58:14,742 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:14,752 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_records")
2025-07-30 07:58:14,781 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:14,788 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medications")
2025-07-30 07:58:14,819 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:14,837 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medication_usages")
2025-07-30 07:58:14,843 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:14,854 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("role_applications")
2025-07-30 07:58:15,007 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:15,053 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessments")
2025-07-30 07:58:15,058 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:15,067 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_items")
2025-07-30 07:58:15,149 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:15,170 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_responses")
2025-07-30 07:58:15,173 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:15,201 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_templates")
2025-07-30 07:58:15,253 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:15,292 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_template_questions")
2025-07-30 07:58:15,337 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:15,687 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaires")
2025-07-30 07:58:16,347 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:16,765 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_items")
2025-07-30 07:58:17,066 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:17,208 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_responses")
2025-07-30 07:58:17,326 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:17,454 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_answers")
2025-07-30 07:58:17,514 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:17,586 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_templates")
2025-07-30 07:58:17,669 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:17,698 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_template_questions")
2025-07-30 07:58:17,814 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:17,871 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_results")
2025-07-30 07:58:17,948 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:18,029 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_results")
2025-07-30 07:58:18,247 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:18,413 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("report_templates")
2025-07-30 07:58:18,928 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:19,069 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_distributions")
2025-07-30 07:58:19,203 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:19,267 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_distributions")
2025-07-30 07:58:19,291 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:19,321 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("service_stats")
2025-07-30 07:58:19,355 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 07:58:19,385 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-30 07:58:19,432 - app.db.init_db - INFO - 数据库表结构检查和更新完成
2025-07-30 07:58:19,488 - app.db.init_db - INFO - 模型关系初始化完成
2025-07-30 07:58:19,507 - app.db.init_db - INFO - 模型关系设置完成
2025-07-30 07:58:19,514 - main - INFO - 数据库初始化完成（强制重建）
2025-07-30 07:58:19,526 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 07:58:19,544 - main - INFO - 数据库连接正常
2025-07-30 07:58:19,556 - main - INFO - 开始初始化模板数据
2025-07-30 07:58:19,571 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 07:58:20,699 - app.db.init_templates - INFO - 开始初始化评估量表模板...
2025-07-30 07:58:20,819 - app.db.init_templates - INFO - 成功初始化 5 个评估量表模板
2025-07-30 07:58:20,893 - app.db.init_templates - INFO - 开始初始化调查问卷模板...
2025-07-30 07:58:21,010 - app.db.init_templates - INFO - 成功初始化 5 个调查问卷模板
2025-07-30 07:58:21,014 - main - INFO - 模板数据初始化完成
2025-07-30 07:58:21,017 - main - INFO - 数据库表结构已经标准化，不需要迁移
2025-07-30 07:58:21,072 - main - INFO - 应用启动完成
2025-07-30 07:58:28,196 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.1%, CPU使用率 91.7%
2025-07-30 07:58:43,709 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.4%, CPU使用率 100.0%
2025-07-30 07:58:58,743 - health_monitor - DEBUG - 系统指标 - CPU: 99.6%, 内存: 61.5%, 磁盘: 94.2%
2025-07-30 07:58:58,848 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.5%, CPU使用率 100.0%
2025-07-30 07:59:01,764 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-30 07:59:01,768 - main - INFO - 请求没有认证头部
2025-07-30 07:59:01,771 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 07:59:01,775 - main - INFO - --- 请求结束: 200 ---

2025-07-30 07:59:04,159 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 07:59:04,161 - main - INFO - 请求没有认证头部
2025-07-30 07:59:04,164 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 07:59:04,167 - app.core.db_connection - DEBUG - 当前线程ID: 14848
2025-07-30 07:59:04,168 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-30 07:59:04,173 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-30 07:59:04,176 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-30 07:59:04,179 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 07:59:04,180 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-30 07:59:06,045 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-30 07:59:06,051 - main - INFO - --- 请求结束: 200 ---

2025-07-30 07:59:14,158 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.9%, CPU使用率 100.0%
2025-07-30 07:59:16,355 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-30 07:59:16,362 - main - INFO - 请求没有认证头部
2025-07-30 07:59:16,365 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 07:59:16,368 - main - INFO - --- 请求结束: 200 ---

2025-07-30 07:59:18,654 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 07:59:18,759 - main - INFO - 请求没有认证头部
2025-07-30 07:59:18,761 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 07:59:18,765 - app.core.db_connection - DEBUG - 当前线程ID: 14848
2025-07-30 07:59:18,767 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-30 07:59:18,791 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-30 07:59:18,968 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-30 07:59:19,160 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 07:59:19,254 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-30 07:59:26,213 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-30 07:59:27,022 - main - INFO - --- 请求结束: 200 ---

2025-07-30 07:59:31,209 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.2%, CPU使用率 100.0%
2025-07-30 07:59:48,108 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.9%, CPU使用率 100.0%
2025-07-30 08:00:00,609 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 60.2%, 磁盘: 94.2%
2025-07-30 08:00:04,029 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.8%, CPU使用率 100.0%
2025-07-30 08:00:19,571 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.1%, CPU使用率 100.0%
2025-07-30 08:00:35,048 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 97.1%
2025-07-30 08:00:50,248 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 97.3%
2025-07-30 08:01:02,626 - health_monitor - DEBUG - 系统指标 - CPU: 98.8%, 内存: 58.6%, 磁盘: 94.2%
2025-07-30 08:01:05,619 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.4%, CPU使用率 95.8%
2025-07-30 08:01:20,733 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.6%, CPU使用率 100.0%
2025-07-30 08:01:35,843 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.9%, CPU使用率 82.1%
2025-07-30 08:01:50,960 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.9%, CPU使用率 100.0%
2025-07-30 08:02:03,720 - health_monitor - DEBUG - 系统指标 - CPU: 91.6%, 内存: 57.6%, 磁盘: 94.2%
2025-07-30 08:02:06,152 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.3%, CPU使用率 100.0%
2025-07-30 08:02:21,319 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.5%, CPU使用率 100.0%
2025-07-30 08:02:36,836 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.4%, CPU使用率 100.0%
2025-07-30 08:02:53,721 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 100.0%
2025-07-30 08:03:06,002 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 62.5%, 磁盘: 94.2%
2025-07-30 08:03:10,352 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.1%, CPU使用率 100.0%
2025-07-30 08:03:25,923 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.6%, CPU使用率 100.0%
2025-07-30 08:03:41,393 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.3%, CPU使用率 100.0%
2025-07-30 08:03:56,701 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 100.0%
2025-07-30 08:03:58,004 - alert_manager - WARNING - 触发告警: disk_free_space, 当前值: 0.0, 阈值: 1024
2025-07-30 08:04:08,125 - health_monitor - DEBUG - 系统指标 - CPU: 98.0%, 内存: 61.6%, 磁盘: 94.2%
2025-07-30 08:04:11,845 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.0%, CPU使用率 96.4%
2025-07-30 08:04:26,954 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.6%, CPU使用率 50.0%
2025-07-30 08:04:42,068 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.1%, CPU使用率 46.4%
2025-07-30 08:04:57,466 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.2%, CPU使用率 100.0%
2025-07-30 08:04:58,085 - alert_manager - WARNING - 触发告警: cpu_usage, 当前值: 98.0, 阈值: 90
2025-07-30 08:04:58,164 - alert_manager - WARNING - 触发告警: disk_usage, 当前值: 94.2, 阈值: 90
2025-07-30 08:05:09,239 - health_monitor - DEBUG - 系统指标 - CPU: 41.3%, 内存: 59.1%, 磁盘: 94.2%
2025-07-30 08:05:12,649 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.5%, CPU使用率 94.6%
2025-07-30 08:05:27,826 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.0%, CPU使用率 89.7%
2025-07-30 08:05:43,908 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.5%, CPU使用率 100.0%
2025-07-30 08:05:59,429 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 100.0%
2025-07-30 08:06:10,268 - health_monitor - DEBUG - 系统指标 - CPU: 34.8%, 内存: 58.4%, 磁盘: 94.2%
2025-07-30 08:06:14,561 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.3%, CPU使用率 20.8%
2025-07-30 08:06:29,666 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.3%, CPU使用率 4.2%
2025-07-30 08:06:44,772 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.3%, CPU使用率 27.3%
2025-07-30 08:06:59,880 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.3%, CPU使用率 32.1%
2025-07-30 08:07:11,294 - health_monitor - DEBUG - 系统指标 - CPU: 17.3%, 内存: 56.7%, 磁盘: 94.2%
2025-07-30 08:07:14,986 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.5%, CPU使用率 11.1%
2025-07-30 08:07:30,091 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.4%, CPU使用率 0.0%
2025-07-30 08:07:45,196 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.3%, CPU使用率 33.3%
2025-07-30 08:08:00,303 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.2%, CPU使用率 39.3%
2025-07-30 08:08:12,319 - health_monitor - DEBUG - 系统指标 - CPU: 34.9%, 内存: 56.2%, 磁盘: 94.2%
2025-07-30 08:08:15,409 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.3%, CPU使用率 10.7%
2025-07-30 08:08:30,516 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.1%, CPU使用率 25.0%
2025-07-30 08:08:45,621 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.1%, CPU使用率 12.5%
2025-07-30 08:09:00,729 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.7%, CPU使用率 37.5%
2025-07-30 08:09:13,340 - health_monitor - DEBUG - 系统指标 - CPU: 13.2%, 内存: 54.6%, 磁盘: 94.2%
2025-07-30 08:09:15,835 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.5%, CPU使用率 39.1%
2025-07-30 08:09:30,941 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.5%, CPU使用率 7.1%
2025-07-30 08:09:46,146 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.4%, CPU使用率 100.0%
2025-07-30 08:10:01,256 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.1%, CPU使用率 67.9%
2025-07-30 08:10:14,367 - health_monitor - DEBUG - 系统指标 - CPU: 71.1%, 内存: 57.5%, 磁盘: 94.2%
2025-07-30 08:10:16,365 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.4%, CPU使用率 82.1%
2025-07-30 08:10:31,537 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.5%, CPU使用率 100.0%
2025-07-30 08:10:46,650 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.3%, CPU使用率 81.5%
2025-07-30 08:11:01,756 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.1%, CPU使用率 50.0%
2025-07-30 08:11:15,392 - health_monitor - DEBUG - 系统指标 - CPU: 77.8%, 内存: 58.3%, 磁盘: 94.3%
2025-07-30 08:11:16,864 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.3%, CPU使用率 60.7%
2025-07-30 08:11:31,986 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.5%, CPU使用率 79.3%
2025-07-30 08:11:47,093 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.5%, CPU使用率 86.2%
2025-07-30 08:12:02,200 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.0%, CPU使用率 42.3%
2025-07-30 08:12:16,418 - health_monitor - DEBUG - 系统指标 - CPU: 72.5%, 内存: 58.2%, 磁盘: 94.1%
2025-07-30 08:12:17,310 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.2%, CPU使用率 41.7%
2025-07-30 08:12:32,416 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.1%, CPU使用率 50.0%
2025-07-30 08:12:47,522 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.1%, CPU使用率 56.0%
2025-07-30 08:13:02,629 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.0%, CPU使用率 36.7%
2025-07-30 08:13:17,446 - health_monitor - DEBUG - 系统指标 - CPU: 49.8%, 内存: 58.0%, 磁盘: 94.2%
2025-07-30 08:13:17,735 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.1%, CPU使用率 25.0%
2025-07-30 08:13:32,846 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.8%, CPU使用率 50.0%
2025-07-30 08:13:47,952 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.8%, CPU使用率 60.7%
2025-07-30 08:14:03,059 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.8%, CPU使用率 33.3%
2025-07-30 08:14:18,165 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.7%, CPU使用率 56.0%
2025-07-30 08:14:18,476 - health_monitor - DEBUG - 系统指标 - CPU: 54.4%, 内存: 57.8%, 磁盘: 94.3%
2025-07-30 08:14:33,271 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.6%, CPU使用率 50.0%
2025-07-30 08:14:48,377 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.7%, CPU使用率 0.0%
2025-07-30 08:15:03,483 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.2%, CPU使用率 12.5%
2025-07-30 08:15:18,589 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.3%, CPU使用率 12.5%
2025-07-30 08:15:19,500 - health_monitor - DEBUG - 系统指标 - CPU: 23.4%, 内存: 56.3%, 磁盘: 94.3%
2025-07-30 08:15:33,695 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.3%, CPU使用率 15.4%
2025-07-30 08:15:48,800 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.4%, CPU使用率 50.0%
2025-07-30 08:16:03,911 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.4%, CPU使用率 46.4%
2025-07-30 08:16:19,016 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.3%, CPU使用率 25.0%
2025-07-30 08:16:20,521 - health_monitor - DEBUG - 系统指标 - CPU: 35.4%, 内存: 56.3%, 磁盘: 94.3%
2025-07-30 08:16:34,126 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.3%, CPU使用率 3.7%
2025-07-30 08:16:49,232 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.3%, CPU使用率 12.5%
2025-07-30 08:17:04,339 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.3%, CPU使用率 40.0%
2025-07-30 08:17:19,453 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.4%, CPU使用率 84.0%
2025-07-30 08:17:21,542 - health_monitor - DEBUG - 系统指标 - CPU: 17.6%, 内存: 56.4%, 磁盘: 94.3%
2025-07-30 08:17:34,559 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.8%, CPU使用率 0.0%
2025-07-30 08:17:49,665 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.8%, CPU使用率 25.0%
2025-07-30 08:18:04,770 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.7%, CPU使用率 3.7%
2025-07-30 08:18:19,877 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.7%, CPU使用率 0.0%
2025-07-30 08:18:22,595 - health_monitor - DEBUG - 系统指标 - CPU: 24.8%, 内存: 56.7%, 磁盘: 94.3%
2025-07-30 08:18:34,983 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.5%, CPU使用率 29.2%
2025-07-30 08:18:50,090 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.5%, CPU使用率 8.3%
2025-07-30 08:19:05,196 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.5%, CPU使用率 20.0%
2025-07-30 08:19:20,303 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.5%, CPU使用率 14.3%
2025-07-30 08:19:23,617 - health_monitor - DEBUG - 系统指标 - CPU: 14.4%, 内存: 56.5%, 磁盘: 94.3%
2025-07-30 08:19:35,409 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.6%, CPU使用率 31.0%
2025-07-30 08:19:50,517 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.8%, CPU使用率 25.0%
2025-07-30 08:20:05,624 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.8%, CPU使用率 40.7%
2025-07-30 08:20:20,730 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.6%, CPU使用率 0.0%
2025-07-30 08:20:24,639 - health_monitor - DEBUG - 系统指标 - CPU: 27.3%, 内存: 56.5%, 磁盘: 94.3%
2025-07-30 08:20:35,836 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.5%, CPU使用率 24.0%
2025-07-30 08:20:50,942 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.6%, CPU使用率 7.7%
2025-07-30 08:21:06,050 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.5%, CPU使用率 0.0%
2025-07-30 08:21:21,155 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.5%, CPU使用率 16.7%
2025-07-30 08:21:25,667 - health_monitor - DEBUG - 系统指标 - CPU: 27.2%, 内存: 56.5%, 磁盘: 94.3%
2025-07-30 08:21:36,261 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.5%, CPU使用率 12.5%
2025-07-30 08:21:51,366 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.5%, CPU使用率 24.0%
2025-07-30 08:22:06,471 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.4%, CPU使用率 32.1%
2025-07-30 08:22:21,577 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.4%, CPU使用率 8.3%
2025-07-30 08:22:26,701 - health_monitor - DEBUG - 系统指标 - CPU: 39.1%, 内存: 56.4%, 磁盘: 94.3%
2025-07-30 08:22:36,685 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.7%, CPU使用率 100.0%
2025-07-30 08:22:40,631 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-30 08:22:40,633 - main - INFO - 请求没有认证头部
2025-07-30 08:22:40,636 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 08:22:40,639 - main - INFO - --- 请求结束: 200 ---

2025-07-30 08:22:42,654 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 08:22:42,674 - main - INFO - 请求没有认证头部
2025-07-30 08:22:42,677 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 08:22:42,680 - app.core.db_connection - DEBUG - 当前线程ID: 14848
2025-07-30 08:22:42,682 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-30 08:22:42,689 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-30 08:22:42,691 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-30 08:22:42,693 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 08:22:42,694 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-30 08:22:43,782 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-30 08:22:43,789 - main - INFO - --- 请求结束: 200 ---

2025-07-30 08:22:51,795 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.0%, CPU使用率 4.2%
2025-07-30 08:23:06,901 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.5%, CPU使用率 12.5%
2025-07-30 08:23:22,006 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.7%, CPU使用率 0.0%
2025-07-30 08:23:27,727 - health_monitor - DEBUG - 系统指标 - CPU: 53.3%, 内存: 56.8%, 磁盘: 94.3%
2025-07-30 08:23:35,038 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-30 08:23:35,039 - main - INFO - 请求没有认证头部
2025-07-30 08:23:35,039 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 08:23:35,041 - main - INFO - --- 请求结束: 200 ---

2025-07-30 08:23:37,085 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 08:23:37,086 - main - INFO - 请求没有认证头部
2025-07-30 08:23:37,087 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 08:23:37,089 - app.core.db_connection - DEBUG - 当前线程ID: 14848
2025-07-30 08:23:37,090 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-30 08:23:37,092 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-30 08:23:37,092 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-30 08:23:37,093 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 08:23:37,094 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-30 08:23:37,120 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.5%, CPU使用率 25.0%
2025-07-30 08:23:37,942 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-30 08:23:37,944 - main - INFO - --- 请求结束: 200 ---

2025-07-30 08:23:52,303 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.0%, CPU使用率 37.9%
2025-07-30 08:24:07,407 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.1%, CPU使用率 28.6%
2025-07-30 08:24:22,511 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.1%, CPU使用率 50.0%
2025-07-30 08:24:28,747 - health_monitor - DEBUG - 系统指标 - CPU: 40.7%, 内存: 55.9%, 磁盘: 94.3%
2025-07-30 08:24:37,617 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.0%, CPU使用率 27.3%
2025-07-30 08:24:52,724 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.5%, CPU使用率 31.0%
2025-07-30 08:25:07,834 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.4%, CPU使用率 53.6%
2025-07-30 08:25:22,941 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.1%, CPU使用率 85.7%
2025-07-30 08:25:29,773 - health_monitor - DEBUG - 系统指标 - CPU: 62.8%, 内存: 57.6%, 磁盘: 94.3%
2025-07-30 08:25:38,244 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.4%, CPU使用率 100.0%
2025-07-30 08:25:53,351 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.4%, CPU使用率 66.7%
2025-07-30 08:26:08,648 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.0%, CPU使用率 100.0%
2025-07-30 08:26:23,778 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 80.0%
2025-07-30 08:26:31,117 - health_monitor - DEBUG - 系统指标 - CPU: 92.9%, 内存: 60.5%, 磁盘: 94.3%
2025-07-30 08:26:38,894 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.7%, CPU使用率 60.7%
2025-07-30 08:26:53,999 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.3%, CPU使用率 91.7%
2025-07-30 08:27:09,462 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.9%, CPU使用率 100.0%
2025-07-30 08:27:24,945 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 100.0%
2025-07-30 08:27:32,178 - health_monitor - DEBUG - 系统指标 - CPU: 83.0%, 内存: 59.5%, 磁盘: 94.3%
2025-07-30 08:27:40,254 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.4%, CPU使用率 89.3%
2025-07-30 08:27:55,443 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.8%, CPU使用率 100.0%
2025-07-30 08:28:10,613 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.6%, CPU使用率 89.7%
2025-07-30 08:28:26,036 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.1%, CPU使用率 100.0%
2025-07-30 08:28:33,671 - health_monitor - DEBUG - 系统指标 - CPU: 95.6%, 内存: 59.2%, 磁盘: 94.3%
2025-07-30 08:28:41,903 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.6%, CPU使用率 100.0%
2025-07-30 08:28:57,150 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.1%, CPU使用率 67.9%
2025-07-30 08:29:12,894 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.5%, CPU使用率 100.0%
2025-07-30 08:29:28,072 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.9%, CPU使用率 64.5%
2025-07-30 08:29:34,742 - health_monitor - DEBUG - 系统指标 - CPU: 92.3%, 内存: 60.6%, 磁盘: 94.3%
2025-07-30 08:29:43,179 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.1%, CPU使用率 82.1%
2025-07-30 08:29:58,287 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.9%, CPU使用率 92.9%
2025-07-30 08:30:13,452 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.0%, CPU使用率 90.6%
2025-07-30 08:30:28,673 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.8%, CPU使用率 93.1%
2025-07-30 08:30:35,765 - health_monitor - DEBUG - 系统指标 - CPU: 70.8%, 内存: 59.1%, 磁盘: 94.3%
2025-07-30 08:30:43,822 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.2%, CPU使用率 65.5%
2025-07-30 08:30:59,376 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 100.0%
2025-07-30 08:31:14,484 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 78.6%
2025-07-30 08:31:29,595 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.3%, CPU使用率 51.7%
2025-07-30 08:31:36,799 - health_monitor - DEBUG - 系统指标 - CPU: 84.8%, 内存: 59.2%, 磁盘: 94.3%
2025-07-30 08:31:44,706 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.7%, CPU使用率 82.1%
2025-07-30 08:31:59,836 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.0%, CPU使用率 87.5%
2025-07-30 08:32:14,963 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 75.0%
2025-07-30 08:32:30,471 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.3%, CPU使用率 100.0%
2025-07-30 08:32:37,935 - health_monitor - DEBUG - 系统指标 - CPU: 99.6%, 内存: 61.7%, 磁盘: 94.3%
2025-07-30 08:32:45,663 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.0%, CPU使用率 96.6%
2025-07-30 08:33:00,767 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.7%, CPU使用率 50.0%
2025-07-30 08:33:16,157 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 100.0%
2025-07-30 08:33:31,321 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.5%, CPU使用率 92.6%
2025-07-30 08:33:39,466 - health_monitor - DEBUG - 系统指标 - CPU: 85.6%, 内存: 60.4%, 磁盘: 94.3%
2025-07-30 08:33:46,567 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.2%, CPU使用率 85.7%
2025-07-30 08:34:02,150 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.7%, CPU使用率 100.0%
2025-07-30 08:34:17,434 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 60.6%
2025-07-30 08:34:32,730 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.4%, CPU使用率 100.0%
2025-07-30 08:34:40,504 - health_monitor - DEBUG - 系统指标 - CPU: 87.7%, 内存: 60.6%, 磁盘: 94.3%
2025-07-30 08:34:47,889 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.0%, CPU使用率 100.0%
2025-07-30 08:35:02,994 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.9%, CPU使用率 58.3%
2025-07-30 08:35:18,142 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.2%, CPU使用率 76.5%
2025-07-30 08:35:33,501 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 100.0%
2025-07-30 08:35:41,591 - health_monitor - DEBUG - 系统指标 - CPU: 99.6%, 内存: 60.3%, 磁盘: 94.3%
2025-07-30 08:35:48,738 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.0%, CPU使用率 100.0%
2025-07-30 08:36:03,879 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.1%, CPU使用率 96.4%
2025-07-30 08:36:19,004 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 89.3%
2025-07-30 08:36:34,110 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.1%, CPU使用率 64.3%
2025-07-30 08:36:42,787 - health_monitor - DEBUG - 系统指标 - CPU: 83.6%, 内存: 59.5%, 磁盘: 94.3%
2025-07-30 08:36:49,217 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 75.0%
2025-07-30 08:37:04,844 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.3%, CPU使用率 100.0%
2025-07-30 08:37:20,059 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.3%, CPU使用率 96.4%
2025-07-30 08:37:35,188 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 90.6%
2025-07-30 08:37:43,995 - health_monitor - DEBUG - 系统指标 - CPU: 94.3%, 内存: 60.4%, 磁盘: 94.3%
2025-07-30 08:37:50,450 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.6%, CPU使用率 100.0%
2025-07-30 08:38:06,169 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 100.0%
2025-07-30 08:38:21,394 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 100.0%
2025-07-30 08:38:36,516 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.4%, CPU使用率 100.0%
2025-07-30 08:38:45,029 - health_monitor - DEBUG - 系统指标 - CPU: 64.1%, 内存: 59.8%, 磁盘: 94.3%
2025-07-30 08:38:51,621 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 29.2%
2025-07-30 08:39:06,729 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.4%, CPU使用率 46.4%
2025-07-30 08:39:21,834 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.1%, CPU使用率 38.5%
2025-07-30 08:39:36,939 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.1%, CPU使用率 21.4%
2025-07-30 08:39:46,049 - health_monitor - DEBUG - 系统指标 - CPU: 36.3%, 内存: 56.1%, 磁盘: 94.3%
2025-07-30 08:39:52,044 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.2%, CPU使用率 41.7%
2025-07-30 08:40:07,148 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.1%, CPU使用率 21.4%
2025-07-30 08:40:22,253 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.2%, CPU使用率 34.6%
2025-07-30 08:40:37,357 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.1%, CPU使用率 32.0%
2025-07-30 08:40:47,069 - health_monitor - DEBUG - 系统指标 - CPU: 37.2%, 内存: 56.2%, 磁盘: 94.3%
2025-07-30 08:40:52,463 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.3%, CPU使用率 52.0%
2025-07-30 08:41:07,567 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.1%, CPU使用率 33.3%
2025-07-30 08:41:22,672 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.3%, CPU使用率 51.7%
2025-07-30 08:41:37,777 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.1%, CPU使用率 41.7%
2025-07-30 08:41:48,096 - health_monitor - DEBUG - 系统指标 - CPU: 62.8%, 内存: 58.3%, 磁盘: 94.3%
2025-07-30 08:41:52,934 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.4%, CPU使用率 92.9%
2025-07-30 08:42:08,149 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.2%, CPU使用率 100.0%
2025-07-30 08:42:23,304 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 80.8%
2025-07-30 08:42:38,917 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.7%, CPU使用率 100.0%
2025-07-30 08:42:49,391 - health_monitor - DEBUG - 系统指标 - CPU: 78.8%, 内存: 57.6%, 磁盘: 94.3%
2025-07-30 08:42:54,131 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.3%, CPU使用率 100.0%
2025-07-30 08:43:09,238 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.1%, CPU使用率 48.1%
2025-07-30 08:43:24,366 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 93.1%
2025-07-30 08:43:39,505 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.1%, CPU使用率 94.4%
2025-07-30 08:43:50,477 - health_monitor - DEBUG - 系统指标 - CPU: 57.7%, 内存: 58.5%, 磁盘: 94.3%
2025-07-30 08:43:54,663 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.4%, CPU使用率 97.2%
2025-07-30 08:44:09,768 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.8%, CPU使用率 57.1%
2025-07-30 08:44:24,877 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.0%, CPU使用率 81.5%
2025-07-30 08:44:40,083 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.2%, CPU使用率 70.0%
2025-07-30 08:44:51,710 - health_monitor - DEBUG - 系统指标 - CPU: 96.9%, 内存: 58.0%, 磁盘: 94.3%
2025-07-30 08:44:55,510 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.7%, CPU使用率 100.0%
2025-07-30 08:45:10,748 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.0%, CPU使用率 100.0%
2025-07-30 08:45:25,857 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.1%, CPU使用率 37.5%
2025-07-30 08:45:40,982 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.5%, CPU使用率 83.3%
2025-07-30 08:45:52,744 - health_monitor - DEBUG - 系统指标 - CPU: 84.7%, 内存: 58.1%, 磁盘: 94.3%
2025-07-30 08:45:56,160 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.5%, CPU使用率 100.0%
2025-07-30 08:46:11,614 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 97.5%
2025-07-30 08:46:26,782 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 87.9%
2025-07-30 08:46:42,073 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 90.5%
2025-07-30 08:46:54,435 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 58.6%, 磁盘: 94.3%
2025-07-30 08:46:57,354 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.2%, CPU使用率 97.9%
2025-07-30 08:47:12,475 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.5%, CPU使用率 82.1%
2025-07-30 08:47:27,604 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 93.8%
2025-07-30 08:47:43,056 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.5%, CPU使用率 100.0%
2025-07-30 08:47:55,743 - health_monitor - DEBUG - 系统指标 - CPU: 86.4%, 内存: 60.8%, 磁盘: 94.3%
2025-07-30 08:47:58,176 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.5%, CPU使用率 93.1%
2025-07-30 08:48:13,304 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.5%, CPU使用率 96.3%
2025-07-30 08:48:29,057 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 100.0%
2025-07-30 08:48:44,371 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 100.0%
2025-07-30 08:48:56,771 - health_monitor - DEBUG - 系统指标 - CPU: 89.1%, 内存: 61.9%, 磁盘: 94.3%
2025-07-30 08:48:59,586 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 82.8%
2025-07-30 08:49:14,693 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 64.3%
2025-07-30 08:49:29,799 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.0%, CPU使用率 92.6%
2025-07-30 08:49:44,905 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.9%, CPU使用率 87.5%
2025-07-30 08:49:57,814 - health_monitor - DEBUG - 系统指标 - CPU: 76.3%, 内存: 59.9%, 磁盘: 94.3%
2025-07-30 08:50:00,040 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.9%, CPU使用率 93.1%
2025-07-30 08:50:15,276 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.4%, CPU使用率 100.0%
2025-07-30 08:50:30,381 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.0%, CPU使用率 53.6%
2025-07-30 08:50:45,580 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.8%, CPU使用率 100.0%
2025-07-30 08:50:59,026 - health_monitor - DEBUG - 系统指标 - CPU: 96.4%, 内存: 59.3%, 磁盘: 94.3%
2025-07-30 08:51:01,084 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 100.0%
2025-07-30 08:51:16,485 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.8%, CPU使用率 100.0%
2025-07-30 08:51:31,597 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 28.6%
2025-07-30 08:51:46,701 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.8%, CPU使用率 62.5%
2025-07-30 08:52:00,046 - health_monitor - DEBUG - 系统指标 - CPU: 45.0%, 内存: 56.9%, 磁盘: 94.3%
2025-07-30 08:52:01,806 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.0%, CPU使用率 20.8%
2025-07-30 08:52:16,913 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.6%, CPU使用率 66.7%
2025-07-30 08:52:25,408 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-30 08:52:25,409 - main - INFO - 请求没有认证头部
2025-07-30 08:52:25,410 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 08:52:25,411 - main - INFO - --- 请求结束: 200 ---

2025-07-30 08:52:27,443 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 08:52:27,444 - main - INFO - 请求没有认证头部
2025-07-30 08:52:27,445 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 08:52:27,446 - app.core.db_connection - DEBUG - 当前线程ID: 14848
2025-07-30 08:52:27,447 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-30 08:52:27,448 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-30 08:52:27,449 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-30 08:52:27,450 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 08:52:27,451 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-30 08:52:27,455 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-07-30 08:52:27,456 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-07-30 08:52:27,457 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-07-30 08:52:28,436 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-30 08:52:28,439 - main - INFO - --- 请求结束: 200 ---

2025-07-30 08:52:32,019 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.1%, CPU使用率 28.6%
2025-07-30 08:52:47,198 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.3%, CPU使用率 94.4%
2025-07-30 08:53:01,067 - health_monitor - DEBUG - 系统指标 - CPU: 12.9%, 内存: 59.4%, 磁盘: 94.3%
2025-07-30 08:53:02,308 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.4%, CPU使用率 29.2%
2025-07-30 08:53:17,418 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.5%, CPU使用率 33.3%
2025-07-30 08:53:32,523 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.2%, CPU使用率 28.0%
2025-07-30 08:53:47,628 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.4%, CPU使用率 57.1%
2025-07-30 08:54:02,087 - health_monitor - DEBUG - 系统指标 - CPU: 29.2%, 内存: 57.2%, 磁盘: 94.3%
2025-07-30 08:54:02,733 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.2%, CPU使用率 40.0%
2025-07-30 08:54:17,839 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 58.3%
2025-07-30 08:54:32,980 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.0%, CPU使用率 85.2%
2025-07-30 08:54:48,161 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 100.0%
2025-07-30 08:55:03,127 - health_monitor - DEBUG - 系统指标 - CPU: 72.8%, 内存: 60.8%, 磁盘: 94.3%
2025-07-30 08:55:03,270 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.9%, CPU使用率 53.6%
2025-07-30 08:55:18,376 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.9%, CPU使用率 82.1%
2025-07-30 08:55:33,486 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.5%, CPU使用率 78.6%
2025-07-30 08:55:48,602 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 100.0%
2025-07-30 08:56:03,897 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.0%, CPU使用率 100.0%
2025-07-30 08:56:04,460 - health_monitor - DEBUG - 系统指标 - CPU: 98.7%, 内存: 63.0%, 磁盘: 94.3%
2025-07-30 08:56:19,096 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 100.0%
2025-07-30 08:56:34,231 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.6%, CPU使用率 87.5%
2025-07-30 08:56:49,579 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.4%, CPU使用率 100.0%
2025-07-30 08:57:05,064 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 95.2%
2025-07-30 08:57:05,575 - health_monitor - DEBUG - 系统指标 - CPU: 90.0%, 内存: 60.5%, 磁盘: 94.3%
2025-07-30 08:57:20,192 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.7%, CPU使用率 48.3%
2025-07-30 08:57:35,616 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.4%, CPU使用率 100.0%
2025-07-30 08:57:50,766 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.3%, CPU使用率 100.0%
2025-07-30 08:58:05,879 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.2%, CPU使用率 82.1%
2025-07-30 08:58:06,800 - health_monitor - DEBUG - 系统指标 - CPU: 92.6%, 内存: 62.5%, 磁盘: 94.3%
2025-07-30 08:58:21,120 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.9%, CPU使用率 100.0%
2025-07-30 08:58:36,346 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.2%, CPU使用率 93.3%
2025-07-30 08:58:51,455 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.7%, CPU使用率 96.6%
2025-07-30 08:59:06,560 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.2%, CPU使用率 75.0%
2025-07-30 08:59:07,897 - health_monitor - DEBUG - 系统指标 - CPU: 94.7%, 内存: 62.2%, 磁盘: 94.3%
2025-07-30 08:59:21,751 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.2%, CPU使用率 100.0%
2025-07-30 08:59:36,969 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.1%, CPU使用率 87.5%
2025-07-30 08:59:52,287 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.3%, CPU使用率 100.0%
2025-07-30 09:00:07,475 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.8%, CPU使用率 95.2%
2025-07-30 09:00:09,029 - health_monitor - DEBUG - 系统指标 - CPU: 77.7%, 内存: 61.9%, 磁盘: 94.3%
2025-07-30 09:00:22,720 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.2%, CPU使用率 100.0%
2025-07-30 09:00:37,967 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.8%, CPU使用率 100.0%
2025-07-30 09:00:53,126 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.9%, CPU使用率 90.6%
2025-07-30 09:01:08,471 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.2%, CPU使用率 100.0%
2025-07-30 09:01:10,199 - health_monitor - DEBUG - 系统指标 - CPU: 95.1%, 内存: 60.5%, 磁盘: 94.3%
2025-07-30 09:01:23,657 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.3%, CPU使用率 100.0%
2025-07-30 09:01:38,793 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 96.9%
2025-07-30 09:01:53,921 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 29.6%
2025-07-30 09:01:55,099 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-30 09:01:55,107 - main - INFO - 请求没有认证头部
2025-07-30 09:01:55,108 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 09:01:55,110 - main - INFO - --- 请求结束: 200 ---

2025-07-30 09:01:57,167 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 09:01:57,168 - main - INFO - 请求没有认证头部
2025-07-30 09:01:57,169 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 09:01:57,170 - app.core.db_connection - DEBUG - 当前线程ID: 14848
2025-07-30 09:01:57,171 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-30 09:01:57,172 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-30 09:01:57,173 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 09:01:57,174 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-30 09:01:58,053 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-30 09:01:58,055 - main - INFO - --- 请求结束: 200 ---

2025-07-30 09:02:09,026 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 37.5%
2025-07-30 09:02:11,243 - health_monitor - DEBUG - 系统指标 - CPU: 48.5%, 内存: 60.8%, 磁盘: 94.3%
2025-07-30 09:02:24,135 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.3%, CPU使用率 62.5%
2025-07-30 09:02:39,244 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.3%, CPU使用率 62.5%
2025-07-30 09:02:54,349 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.5%, CPU使用率 17.9%
2025-07-30 09:03:09,455 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.8%, CPU使用率 57.1%
2025-07-30 09:03:12,275 - health_monitor - DEBUG - 系统指标 - CPU: 34.6%, 内存: 61.8%, 磁盘: 94.3%
2025-07-30 09:03:24,560 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.7%, CPU使用率 36.0%
2025-07-30 09:03:39,667 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.7%, CPU使用率 42.3%
2025-07-30 09:03:54,772 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.5%, CPU使用率 27.6%
2025-07-30 09:04:09,876 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.4%, CPU使用率 38.5%
2025-07-30 09:04:13,303 - health_monitor - DEBUG - 系统指标 - CPU: 45.7%, 内存: 61.5%, 磁盘: 94.3%
2025-07-30 09:04:24,983 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.4%, CPU使用率 0.0%
2025-07-30 09:04:40,098 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.6%, CPU使用率 100.0%
2025-07-30 09:04:55,203 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.9%, CPU使用率 70.4%
2025-07-30 09:05:10,309 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.9%, CPU使用率 0.0%
2025-07-30 09:05:14,327 - health_monitor - DEBUG - 系统指标 - CPU: 22.3%, 内存: 59.0%, 磁盘: 94.3%
2025-07-30 09:05:25,414 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.1%, CPU使用率 20.0%
2025-07-30 09:05:40,529 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.2%, CPU使用率 78.6%
2025-07-30 09:05:55,636 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.2%, CPU使用率 0.0%
2025-07-30 09:06:10,741 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.1%, CPU使用率 3.6%
2025-07-30 09:06:15,354 - health_monitor - DEBUG - 系统指标 - CPU: 24.0%, 内存: 59.2%, 磁盘: 94.3%
2025-07-30 09:06:25,847 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.2%, CPU使用率 41.4%
2025-07-30 09:06:40,953 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.6%, CPU使用率 78.6%
2025-07-30 09:06:56,060 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.8%, CPU使用率 8.3%
2025-07-30 09:07:11,165 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.0%, CPU使用率 25.0%
2025-07-30 09:07:16,380 - health_monitor - DEBUG - 系统指标 - CPU: 52.0%, 内存: 60.1%, 磁盘: 94.3%
2025-07-30 09:07:26,274 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.8%, CPU使用率 29.2%
2025-07-30 09:07:41,379 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.0%, CPU使用率 42.9%
2025-07-30 09:07:56,486 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.2%, CPU使用率 28.6%
2025-07-30 09:08:11,591 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.3%, CPU使用率 33.3%
2025-07-30 09:08:17,405 - health_monitor - DEBUG - 系统指标 - CPU: 37.3%, 内存: 59.2%, 磁盘: 94.3%
2025-07-30 09:08:26,699 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.3%, CPU使用率 12.5%
2025-07-30 09:08:41,814 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.3%, CPU使用率 26.9%
2025-07-30 09:08:56,926 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.3%, CPU使用率 48.3%
2025-07-30 09:09:12,032 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.5%, CPU使用率 11.5%
2025-07-30 09:09:18,430 - health_monitor - DEBUG - 系统指标 - CPU: 20.3%, 内存: 59.7%, 磁盘: 94.3%
2025-07-30 09:09:27,139 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.8%, CPU使用率 4.2%
2025-07-30 09:09:42,244 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.9%, CPU使用率 58.3%
2025-07-30 09:09:57,348 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 14.3%
2025-07-30 09:10:12,455 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 32.1%
2025-07-30 09:10:19,456 - health_monitor - DEBUG - 系统指标 - CPU: 23.7%, 内存: 60.1%, 磁盘: 94.3%
2025-07-30 09:10:27,560 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 62.5%
2025-07-30 09:10:42,666 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 45.8%
2025-07-30 09:10:57,773 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 40.0%
2025-07-30 09:11:12,880 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 21.4%
2025-07-30 09:11:20,481 - health_monitor - DEBUG - 系统指标 - CPU: 23.0%, 内存: 60.2%, 磁盘: 94.3%
2025-07-30 09:11:27,985 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 44.4%
2025-07-30 09:11:43,090 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 20.8%
2025-07-30 09:11:58,194 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 29.6%
2025-07-30 09:12:13,300 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 37.9%
2025-07-30 09:12:21,540 - health_monitor - DEBUG - 系统指标 - CPU: 40.4%, 内存: 60.4%, 磁盘: 94.3%
2025-07-30 09:12:28,406 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 15.4%
2025-07-30 09:12:43,510 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 0.0%
2025-07-30 09:12:58,617 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 83.3%
2025-07-30 09:13:13,722 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 34.6%
2025-07-30 09:13:22,567 - health_monitor - DEBUG - 系统指标 - CPU: 41.0%, 内存: 60.4%, 磁盘: 94.3%
2025-07-30 09:13:28,829 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 0.0%
2025-07-30 09:13:43,933 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 20.8%
2025-07-30 09:13:59,039 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 36.0%
2025-07-30 09:14:14,144 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 7.1%
2025-07-30 09:14:23,592 - health_monitor - DEBUG - 系统指标 - CPU: 53.3%, 内存: 60.3%, 磁盘: 94.3%
2025-07-30 09:14:29,250 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 11.5%
2025-07-30 09:14:44,354 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 58.3%
2025-07-30 09:14:59,459 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 22.2%
2025-07-30 09:15:14,566 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 27.6%
2025-07-30 09:15:24,668 - health_monitor - DEBUG - 系统指标 - CPU: 69.0%, 内存: 60.4%, 磁盘: 94.3%
2025-07-30 09:15:29,671 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 62.5%
2025-07-30 09:15:44,776 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 0.0%
2025-07-30 09:15:59,880 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 28.6%
2025-07-30 09:16:14,987 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 21.4%
2025-07-30 09:16:25,694 - health_monitor - DEBUG - 系统指标 - CPU: 32.9%, 内存: 60.2%, 磁盘: 94.3%
2025-07-30 09:16:30,093 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 40.0%
2025-07-30 09:16:45,199 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 50.0%
2025-07-30 09:17:00,305 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 21.7%
2025-07-30 09:17:15,414 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 44.8%
2025-07-30 09:17:26,728 - health_monitor - DEBUG - 系统指标 - CPU: 72.6%, 内存: 60.6%, 磁盘: 94.3%
2025-07-30 09:17:30,518 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 25.0%
2025-07-30 09:17:45,625 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 7.1%
2025-07-30 09:18:00,768 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 96.8%
2025-07-30 09:18:15,879 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 0.0%
2025-07-30 09:18:27,760 - health_monitor - DEBUG - 系统指标 - CPU: 26.4%, 内存: 60.5%, 磁盘: 94.3%
2025-07-30 09:18:30,986 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 67.9%
2025-07-30 09:18:46,091 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 44.0%
2025-07-30 09:19:01,222 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 86.7%
2025-07-30 09:19:16,332 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 17.9%
2025-07-30 09:19:28,784 - health_monitor - DEBUG - 系统指标 - CPU: 26.5%, 内存: 60.1%, 磁盘: 94.3%
2025-07-30 09:19:31,437 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 32.1%
2025-07-30 09:19:46,547 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 3.8%
2025-07-30 09:20:01,679 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 82.8%
2025-07-30 09:20:16,784 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 50.0%
2025-07-30 09:20:29,809 - health_monitor - DEBUG - 系统指标 - CPU: 25.8%, 内存: 60.2%, 磁盘: 94.3%
2025-07-30 09:20:31,891 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 0.0%
2025-07-30 09:20:46,996 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 12.5%
2025-07-30 09:21:02,104 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 36.0%
2025-07-30 09:21:17,210 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 38.5%
2025-07-30 09:21:30,877 - health_monitor - DEBUG - 系统指标 - CPU: 41.9%, 内存: 60.1%, 磁盘: 94.3%
2025-07-30 09:21:32,317 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.0%, CPU使用率 46.4%
2025-07-30 09:21:47,424 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 57.1%
2025-07-30 09:22:02,533 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 59.3%
2025-07-30 09:22:17,766 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.9%, CPU使用率 85.7%
2025-07-30 09:22:31,907 - health_monitor - DEBUG - 系统指标 - CPU: 41.6%, 内存: 60.8%, 磁盘: 94.3%
2025-07-30 09:22:32,873 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 29.2%
2025-07-30 09:22:48,145 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.0%, CPU使用率 86.0%
2025-07-30 09:23:03,250 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 22.2%
2025-07-30 09:23:18,355 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.0%, CPU使用率 25.0%
2025-07-30 09:23:32,931 - health_monitor - DEBUG - 系统指标 - CPU: 19.1%, 内存: 60.8%, 磁盘: 94.3%
2025-07-30 09:23:33,460 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 0.0%
2025-07-30 09:23:48,565 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.9%, CPU使用率 28.6%
2025-07-30 09:24:03,669 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 33.3%
2025-07-30 09:24:18,774 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 54.2%
2025-07-30 09:24:33,879 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.9%, CPU使用率 21.4%
2025-07-30 09:24:33,956 - health_monitor - DEBUG - 系统指标 - CPU: 32.8%, 内存: 60.8%, 磁盘: 94.3%
2025-07-30 09:24:48,984 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 21.4%
2025-07-30 09:25:04,089 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 33.3%
2025-07-30 09:25:19,193 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 10.7%
2025-07-30 09:25:34,299 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 3.6%
2025-07-30 09:25:34,980 - health_monitor - DEBUG - 系统指标 - CPU: 26.0%, 内存: 60.7%, 磁盘: 94.3%
2025-07-30 09:25:49,403 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.9%, CPU使用率 41.7%
2025-07-30 09:26:04,509 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 16.7%
2025-07-30 09:26:19,614 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 23.1%
2025-07-30 09:26:34,727 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 71.4%
2025-07-30 09:26:36,002 - health_monitor - DEBUG - 系统指标 - CPU: 54.8%, 内存: 60.7%, 磁盘: 94.3%
2025-07-30 09:26:49,831 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 11.1%
2025-07-30 09:27:04,939 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 35.7%
2025-07-30 09:27:20,054 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 100.0%
2025-07-30 09:27:35,160 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 62.1%
2025-07-30 09:27:37,026 - health_monitor - DEBUG - 系统指标 - CPU: 59.5%, 内存: 60.7%, 磁盘: 94.3%
2025-07-30 09:27:50,268 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 39.3%
2025-07-30 09:28:05,374 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 8.3%
2025-07-30 09:28:20,502 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 96.9%
2025-07-30 09:28:35,607 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 4.2%
2025-07-30 09:28:38,047 - health_monitor - DEBUG - 系统指标 - CPU: 31.0%, 内存: 60.6%, 磁盘: 94.3%
2025-07-30 09:28:50,725 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 96.4%
2025-07-30 09:29:05,829 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 15.4%
2025-07-30 09:29:20,935 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 29.2%
2025-07-30 09:29:36,041 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 8.3%
2025-07-30 09:29:39,069 - health_monitor - DEBUG - 系统指标 - CPU: 52.1%, 内存: 60.7%, 磁盘: 94.3%
2025-07-30 09:29:51,147 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 12.5%
2025-07-30 09:30:06,252 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 0.0%
2025-07-30 09:30:21,363 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 14.3%
2025-07-30 09:30:36,467 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 40.0%
2025-07-30 09:30:40,093 - health_monitor - DEBUG - 系统指标 - CPU: 30.7%, 内存: 60.7%, 磁盘: 94.3%
2025-07-30 09:30:51,578 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 67.9%
2025-07-30 09:31:06,687 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 44.0%
2025-07-30 09:31:21,838 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 100.0%
2025-07-30 09:31:36,947 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 45.8%
2025-07-30 09:31:41,118 - health_monitor - DEBUG - 系统指标 - CPU: 46.7%, 内存: 60.8%, 磁盘: 94.3%
2025-07-30 09:31:52,507 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.4%, CPU使用率 100.0%
2025-07-30 09:32:07,664 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.6%, CPU使用率 89.3%
2025-07-30 09:32:22,886 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 86.1%
2025-07-30 09:32:38,097 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.2%, CPU使用率 94.4%
2025-07-30 09:32:42,153 - health_monitor - DEBUG - 系统指标 - CPU: 93.4%, 内存: 62.5%, 磁盘: 94.3%
2025-07-30 09:32:53,291 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 97.7%
2025-07-30 09:33:08,402 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 58.3%
2025-07-30 09:33:23,542 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.7%, CPU使用率 92.9%
2025-07-30 09:33:38,728 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 92.9%
2025-07-30 09:33:43,483 - health_monitor - DEBUG - 系统指标 - CPU: 77.1%, 内存: 61.8%, 磁盘: 94.3%
2025-07-30 09:33:53,864 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.3%, CPU使用率 100.0%
2025-07-30 09:34:09,057 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 97.3%
2025-07-30 09:34:24,167 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.6%, CPU使用率 84.0%
2025-07-30 09:34:39,294 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.9%, CPU使用率 89.3%
2025-07-30 09:34:44,553 - health_monitor - DEBUG - 系统指标 - CPU: 64.5%, 内存: 60.8%, 磁盘: 94.3%
2025-07-30 09:34:54,401 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.2%, CPU使用率 17.4%
2025-07-30 09:35:09,507 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.4%, CPU使用率 21.4%
2025-07-30 09:35:24,613 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.0%, CPU使用率 55.6%
2025-07-30 09:35:39,719 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.5%, CPU使用率 62.1%
2025-07-30 09:35:45,579 - health_monitor - DEBUG - 系统指标 - CPU: 28.4%, 内存: 58.4%, 磁盘: 94.3%
2025-07-30 09:35:54,826 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.3%, CPU使用率 16.0%
2025-07-30 09:36:09,948 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.3%, CPU使用率 41.4%
2025-07-30 09:36:25,053 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.2%, CPU使用率 38.5%
2025-07-30 09:36:40,190 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.7%, CPU使用率 67.9%
2025-07-30 09:36:46,681 - health_monitor - DEBUG - 系统指标 - CPU: 91.3%, 内存: 62.1%, 磁盘: 94.3%
2025-07-30 09:36:55,300 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 63.0%
2025-07-30 09:37:10,407 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.6%, CPU使用率 53.6%
2025-07-30 09:37:25,561 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.1%, CPU使用率 83.8%
2025-07-30 09:37:40,871 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.7%, CPU使用率 50.0%
2025-07-30 09:37:47,713 - health_monitor - DEBUG - 系统指标 - CPU: 57.7%, 内存: 60.8%, 磁盘: 94.3%
2025-07-30 09:37:55,978 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.9%, CPU使用率 43.5%
2025-07-30 09:38:11,110 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.1%, CPU使用率 84.4%
2025-07-30 09:38:26,304 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.4%, CPU使用率 71.4%
2025-07-30 09:38:41,681 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.3%, CPU使用率 100.0%
2025-07-30 09:38:48,835 - health_monitor - DEBUG - 系统指标 - CPU: 87.7%, 内存: 60.6%, 磁盘: 94.3%
2025-07-30 09:38:56,949 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.5%, CPU使用率 100.0%
2025-07-30 09:39:12,061 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.6%, CPU使用率 100.0%
2025-07-30 09:39:27,171 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.8%, CPU使用率 51.9%
2025-07-30 09:39:43,168 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.0%, CPU使用率 100.0%
2025-07-30 09:39:49,896 - health_monitor - DEBUG - 系统指标 - CPU: 83.3%, 内存: 61.5%, 磁盘: 94.3%
2025-07-30 09:39:58,383 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 80.6%
2025-07-30 09:40:13,491 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 72.0%
2025-07-30 09:40:28,702 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.1%, CPU使用率 100.0%
2025-07-30 09:40:43,818 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.3%, CPU使用率 67.9%
2025-07-30 09:40:50,978 - health_monitor - DEBUG - 系统指标 - CPU: 99.2%, 内存: 64.5%, 磁盘: 94.3%
2025-07-30 09:40:59,133 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.5%, CPU使用率 100.0%
2025-07-30 09:41:14,294 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.4%, CPU使用率 50.0%
2025-07-30 09:41:29,744 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.0%, CPU使用率 100.0%
2025-07-30 09:41:44,857 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.1%, CPU使用率 91.7%
2025-07-30 09:41:52,257 - health_monitor - DEBUG - 系统指标 - CPU: 92.5%, 内存: 62.7%, 磁盘: 94.3%
2025-07-30 09:41:59,970 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.8%, CPU使用率 100.0%
2025-07-30 09:42:15,445 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.9%, CPU使用率 100.0%
2025-07-30 09:42:30,831 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.9%, CPU使用率 100.0%
2025-07-30 09:42:46,189 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 100.0%
2025-07-30 09:42:53,744 - health_monitor - DEBUG - 系统指标 - CPU: 94.6%, 内存: 66.4%, 磁盘: 94.3%
2025-07-30 09:43:01,352 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.9%, CPU使用率 97.4%
2025-07-30 09:43:17,149 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.2%, CPU使用率 100.0%
2025-07-30 09:43:32,533 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.2%, CPU使用率 85.7%
2025-07-30 09:43:47,643 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.1%, CPU使用率 82.1%
2025-07-30 09:43:55,193 - health_monitor - DEBUG - 系统指标 - CPU: 94.6%, 内存: 66.7%, 磁盘: 94.3%
2025-07-30 09:44:02,804 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.6%, CPU使用率 90.0%
2025-07-30 09:44:18,084 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.8%, CPU使用率 90.4%
2025-07-30 09:44:33,321 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.4%, CPU使用率 95.7%
2025-07-30 09:44:49,566 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 99.4%
2025-07-30 09:44:57,467 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 69.4%, 磁盘: 94.3%
2025-07-30 09:45:05,072 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.0%, CPU使用率 85.7%
2025-07-30 09:45:20,301 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.3%, CPU使用率 100.0%
2025-07-30 09:45:35,579 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 60.7%
2025-07-30 09:45:50,702 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.7%, CPU使用率 91.7%
2025-07-30 09:45:58,679 - health_monitor - DEBUG - 系统指标 - CPU: 90.9%, 内存: 67.3%, 磁盘: 94.3%
2025-07-30 09:45:59,218 - alert_manager - WARNING - 触发告警: cpu_usage, 当前值: 90.9, 阈值: 90
2025-07-30 09:46:05,869 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.5%, CPU使用率 100.0%
2025-07-30 09:46:20,989 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 100.0%
2025-07-30 09:46:36,207 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.3%, CPU使用率 100.0%
2025-07-30 09:46:51,316 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.5%, CPU使用率 53.6%
2025-07-30 09:46:59,744 - health_monitor - DEBUG - 系统指标 - CPU: 99.2%, 内存: 69.7%, 磁盘: 94.3%
2025-07-30 09:47:07,340 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.9%, CPU使用率 100.0%
2025-07-30 09:47:22,910 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.7%, CPU使用率 100.0%
2025-07-30 09:47:38,474 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.0%, CPU使用率 100.0%
2025-07-30 09:47:53,590 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.8%, CPU使用率 66.7%
2025-07-30 09:48:01,023 - health_monitor - DEBUG - 系统指标 - CPU: 97.6%, 内存: 69.3%, 磁盘: 94.3%
2025-07-30 09:48:08,776 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.9%, CPU使用率 84.8%
2025-07-30 09:48:24,790 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.7%, CPU使用率 100.0%
2025-07-30 09:48:40,661 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.6%, CPU使用率 100.0%
2025-07-30 09:48:56,536 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.0%, CPU使用率 100.0%
2025-07-30 09:49:03,484 - health_monitor - DEBUG - 系统指标 - CPU: 97.8%, 内存: 69.3%, 磁盘: 94.3%
2025-07-30 09:49:11,902 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.8%, CPU使用率 100.0%
2025-07-30 09:49:27,915 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.1%, CPU使用率 100.0%
2025-07-30 09:49:43,562 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.7%, CPU使用率 100.0%
2025-07-30 09:49:58,915 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.1%, CPU使用率 100.0%
2025-07-30 09:50:05,022 - health_monitor - DEBUG - 系统指标 - CPU: 99.0%, 内存: 71.7%, 磁盘: 94.3%
2025-07-30 09:50:14,103 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.2%, CPU使用率 100.0%
2025-07-30 09:50:29,214 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.5%, CPU使用率 87.5%
2025-07-30 09:50:44,334 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.2%, CPU使用率 96.6%
2025-07-30 09:50:59,751 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.4%, CPU使用率 100.0%
2025-07-30 09:51:06,231 - health_monitor - DEBUG - 系统指标 - CPU: 77.7%, 内存: 71.1%, 磁盘: 94.3%
2025-07-30 09:51:15,037 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.1%, CPU使用率 100.0%
2025-07-30 09:51:30,161 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.8%, CPU使用率 96.4%
2025-07-30 09:51:45,466 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.7%, CPU使用率 100.0%
2025-07-30 09:52:00,820 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.9%, CPU使用率 98.0%
2025-07-30 09:52:07,519 - health_monitor - DEBUG - 系统指标 - CPU: 84.2%, 内存: 72.2%, 磁盘: 94.3%
2025-07-30 09:52:16,138 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.0%, CPU使用率 100.0%
2025-07-30 09:52:31,278 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.4%, CPU使用率 87.9%
2025-07-30 09:52:46,467 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.0%, CPU使用率 88.5%
2025-07-30 09:53:01,822 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.0%, CPU使用率 100.0%
2025-07-30 09:53:08,586 - health_monitor - DEBUG - 系统指标 - CPU: 90.3%, 内存: 70.9%, 磁盘: 94.3%
2025-07-30 09:53:17,045 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.9%, CPU使用率 95.8%
2025-07-30 09:53:32,472 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.0%, CPU使用率 100.0%
2025-07-30 09:53:47,610 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.0%, CPU使用率 96.4%
2025-07-30 09:54:02,989 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.1%, CPU使用率 100.0%
2025-07-30 09:54:09,738 - health_monitor - DEBUG - 系统指标 - CPU: 95.3%, 内存: 73.9%, 磁盘: 94.3%
2025-07-30 09:54:18,267 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.1%, CPU使用率 100.0%
2025-07-30 09:54:33,673 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.3%, CPU使用率 98.4%
2025-07-30 09:54:48,794 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.0%, CPU使用率 87.5%
2025-07-30 09:55:03,901 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.5%, CPU使用率 62.5%
2025-07-30 09:55:11,067 - health_monitor - DEBUG - 系统指标 - CPU: 98.6%, 内存: 72.4%, 磁盘: 94.3%
2025-07-30 09:55:19,011 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.9%, CPU使用率 88.0%
2025-07-30 09:55:34,121 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.9%, CPU使用率 75.0%
2025-07-30 09:55:49,670 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.5%, CPU使用率 100.0%
2025-07-30 09:56:05,104 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.1%, CPU使用率 100.0%
2025-07-30 09:56:12,158 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 75.2%, 磁盘: 94.3%
2025-07-30 09:56:20,458 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.3%, CPU使用率 95.3%
2025-07-30 09:56:35,856 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.6%, CPU使用率 100.0%
2025-07-30 09:56:51,255 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.2%, CPU使用率 97.1%
2025-07-30 09:57:06,367 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.5%, CPU使用率 80.8%
2025-07-30 09:57:13,406 - health_monitor - DEBUG - 系统指标 - CPU: 98.2%, 内存: 73.0%, 磁盘: 94.3%
2025-07-30 09:57:21,482 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.5%, CPU使用率 83.3%
2025-07-30 09:57:36,639 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.4%, CPU使用率 100.0%
2025-07-30 09:57:51,996 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.6%, CPU使用率 98.2%
2025-07-30 09:58:07,328 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.1%, CPU使用率 90.0%
2025-07-30 09:58:14,547 - health_monitor - DEBUG - 系统指标 - CPU: 90.8%, 内存: 76.4%, 磁盘: 94.3%
2025-07-30 09:58:22,505 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.9%, CPU使用率 89.3%
2025-07-30 09:58:37,782 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.5%, CPU使用率 100.0%
2025-07-30 09:58:53,005 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.6%, CPU使用率 79.4%
2025-07-30 09:58:59,800 - alert_manager - WARNING - 触发告警: cpu_usage, 当前值: 90.8, 阈值: 90
2025-07-30 09:59:08,366 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.3%, CPU使用率 100.0%
2025-07-30 09:59:15,701 - health_monitor - DEBUG - 系统指标 - CPU: 98.2%, 内存: 77.3%, 磁盘: 94.3%
2025-07-30 09:59:23,714 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.0%, CPU使用率 100.0%
2025-07-30 09:59:38,970 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.2%, CPU使用率 100.0%
2025-07-30 09:59:54,191 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.3%, CPU使用率 96.9%
2025-07-30 10:00:09,572 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.7%, CPU使用率 100.0%
2025-07-30 10:00:16,751 - health_monitor - DEBUG - 系统指标 - CPU: 81.3%, 内存: 77.4%, 磁盘: 94.3%
2025-07-30 10:00:24,705 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.7%, CPU使用率 71.4%
2025-07-30 10:00:39,816 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.0%, CPU使用率 74.1%
2025-07-30 10:00:54,933 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.6%, CPU使用率 79.2%
2025-07-30 10:01:10,216 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.9%, CPU使用率 100.0%
2025-07-30 10:01:17,795 - health_monitor - DEBUG - 系统指标 - CPU: 75.0%, 内存: 76.3%, 磁盘: 94.3%
2025-07-30 10:01:25,329 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.8%, CPU使用率 44.4%
2025-07-30 10:01:40,455 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.9%, CPU使用率 53.6%
2025-07-30 10:01:55,762 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.2%, CPU使用率 100.0%
2025-07-30 10:02:10,869 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.8%, CPU使用率 70.8%
2025-07-30 10:02:18,955 - health_monitor - DEBUG - 系统指标 - CPU: 93.8%, 内存: 75.9%, 磁盘: 94.3%
2025-07-30 10:02:26,128 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.0%, CPU使用率 89.2%
2025-07-30 10:02:41,474 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.8%, CPU使用率 100.0%
2025-07-30 10:02:56,882 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.1%, CPU使用率 100.0%
2025-07-30 10:03:12,029 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.2%, CPU使用率 96.8%
2025-07-30 10:03:20,229 - health_monitor - DEBUG - 系统指标 - CPU: 78.3%, 内存: 73.7%, 磁盘: 94.3%
2025-07-30 10:03:27,208 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.2%, CPU使用率 100.0%
2025-07-30 10:03:42,391 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.7%, CPU使用率 90.0%
2025-07-30 10:03:57,515 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.7%, CPU使用率 96.4%
2025-07-30 10:04:12,633 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.8%, CPU使用率 93.1%
2025-07-30 10:04:21,261 - health_monitor - DEBUG - 系统指标 - CPU: 95.0%, 内存: 71.2%, 磁盘: 94.3%
2025-07-30 10:04:27,972 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.6%, CPU使用率 100.0%
2025-07-30 10:04:43,106 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.8%, CPU使用率 83.3%
2025-07-30 10:04:58,220 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.8%, CPU使用率 64.3%
2025-07-30 10:05:13,529 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.4%, CPU使用率 100.0%
2025-07-30 10:05:22,295 - health_monitor - DEBUG - 系统指标 - CPU: 86.2%, 内存: 71.5%, 磁盘: 94.3%
2025-07-30 10:05:28,733 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.6%, CPU使用率 75.0%
2025-07-30 10:05:43,842 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.8%, CPU使用率 70.8%
2025-07-30 10:05:59,198 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.3%, CPU使用率 100.0%
2025-07-30 10:06:14,310 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.0%, CPU使用率 40.0%
2025-07-30 10:06:23,327 - health_monitor - DEBUG - 系统指标 - CPU: 67.4%, 内存: 72.6%, 磁盘: 94.3%
2025-07-30 10:06:29,489 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.2%, CPU使用率 60.7%
2025-07-30 10:06:44,743 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.1%, CPU使用率 97.8%
2025-07-30 10:06:59,859 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.6%, CPU使用率 92.9%
2025-07-30 10:07:14,966 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.5%, CPU使用率 66.7%
2025-07-30 10:07:24,371 - health_monitor - DEBUG - 系统指标 - CPU: 99.6%, 内存: 70.2%, 磁盘: 94.3%
2025-07-30 10:07:30,075 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.4%, CPU使用率 66.7%
2025-07-30 10:07:45,183 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.9%, CPU使用率 60.0%
2025-07-30 10:08:00,320 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.6%, CPU使用率 92.3%
2025-07-30 10:08:15,627 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.1%, CPU使用率 100.0%
2025-07-30 10:08:25,423 - health_monitor - DEBUG - 系统指标 - CPU: 63.8%, 内存: 70.7%, 磁盘: 94.3%
2025-07-30 10:08:30,746 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.7%, CPU使用率 75.0%
2025-07-30 10:08:45,855 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.3%, CPU使用率 88.5%
2025-07-30 10:09:00,962 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.8%, CPU使用率 50.0%
2025-07-30 10:09:16,070 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.5%, CPU使用率 66.7%
2025-07-30 10:09:26,493 - health_monitor - DEBUG - 系统指标 - CPU: 66.5%, 内存: 68.8%, 磁盘: 94.3%
2025-07-30 10:09:31,270 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.5%, CPU使用率 94.1%
2025-07-30 10:09:46,380 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.3%, CPU使用率 50.0%
2025-07-30 10:10:01,673 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.0%, CPU使用率 97.6%
2025-07-30 10:10:16,913 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.9%, CPU使用率 100.0%
2025-07-30 10:10:27,853 - health_monitor - DEBUG - 系统指标 - CPU: 83.7%, 内存: 68.3%, 磁盘: 94.3%
2025-07-30 10:10:32,022 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.6%, CPU使用率 72.0%
2025-07-30 10:10:47,164 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.6%, CPU使用率 55.2%
2025-07-30 10:11:02,429 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.5%, CPU使用率 57.1%
2025-07-30 10:11:17,673 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.9%, CPU使用率 66.7%
2025-07-30 10:11:28,990 - health_monitor - DEBUG - 系统指标 - CPU: 89.1%, 内存: 68.9%, 磁盘: 94.3%
2025-07-30 10:11:33,139 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.7%, CPU使用率 100.0%
2025-07-30 10:11:48,248 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.0%, CPU使用率 79.2%
2025-07-30 10:12:03,503 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.7%, CPU使用率 69.2%
2025-07-30 10:12:18,624 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.8%, CPU使用率 50.0%
2025-07-30 10:12:30,024 - health_monitor - DEBUG - 系统指标 - CPU: 79.8%, 内存: 66.5%, 磁盘: 94.3%
2025-07-30 10:12:33,734 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.8%, CPU使用率 75.9%
2025-07-30 10:12:48,844 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.7%, CPU使用率 62.1%
2025-07-30 10:13:03,970 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 79.3%
2025-07-30 10:13:19,082 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.8%, CPU使用率 89.3%
2025-07-30 10:13:31,174 - health_monitor - DEBUG - 系统指标 - CPU: 70.5%, 内存: 68.1%, 磁盘: 94.3%
2025-07-30 10:13:34,315 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 100.0%
2025-07-30 10:13:49,440 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.2%, CPU使用率 63.0%
2025-07-30 10:14:04,553 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 53.6%
2025-07-30 10:14:19,660 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.8%, CPU使用率 35.7%
2025-07-30 10:14:32,231 - health_monitor - DEBUG - 系统指标 - CPU: 86.0%, 内存: 66.6%, 磁盘: 94.3%
2025-07-30 10:14:34,768 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.5%, CPU使用率 85.7%
2025-07-30 10:14:49,879 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.6%, CPU使用率 44.4%
2025-07-30 10:15:05,323 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.1%, CPU使用率 100.0%
2025-07-30 10:15:20,555 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.9%, CPU使用率 64.0%
2025-07-30 10:15:33,259 - health_monitor - DEBUG - 系统指标 - CPU: 70.7%, 内存: 64.6%, 磁盘: 94.3%
2025-07-30 10:15:35,665 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.7%, CPU使用率 75.0%
2025-07-30 10:15:50,772 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.6%, CPU使用率 82.1%
2025-07-30 10:16:06,089 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.6%, CPU使用率 100.0%
2025-07-30 10:16:21,326 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.7%, CPU使用率 66.7%
2025-07-30 10:16:34,593 - health_monitor - DEBUG - 系统指标 - CPU: 82.6%, 内存: 64.6%, 磁盘: 94.3%
2025-07-30 10:16:36,463 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.5%, CPU使用率 84.0%
2025-07-30 10:16:51,614 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.5%, CPU使用率 100.0%
2025-07-30 10:17:06,744 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.7%, CPU使用率 79.2%
2025-07-30 10:17:22,384 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.8%, CPU使用率 100.0%
2025-07-30 10:17:35,673 - health_monitor - DEBUG - 系统指标 - CPU: 98.8%, 内存: 64.1%, 磁盘: 94.3%
2025-07-30 10:17:37,704 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.2%, CPU使用率 93.9%
2025-07-30 10:17:52,899 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.2%, CPU使用率 97.2%
2025-07-30 10:18:08,102 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.5%, CPU使用率 69.2%
2025-07-30 10:18:23,211 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.7%, CPU使用率 95.8%
2025-07-30 10:18:36,727 - health_monitor - DEBUG - 系统指标 - CPU: 77.9%, 内存: 63.3%, 磁盘: 94.3%
2025-07-30 10:18:38,335 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.6%, CPU使用率 56.0%
2025-07-30 10:18:53,591 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.5%, CPU使用率 86.1%
2025-07-30 10:19:08,930 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.2%, CPU使用率 100.0%
2025-07-30 10:19:24,088 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.3%, CPU使用率 62.5%
2025-07-30 10:19:37,755 - health_monitor - DEBUG - 系统指标 - CPU: 78.8%, 内存: 63.1%, 磁盘: 94.3%
2025-07-30 10:19:39,194 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.8%, CPU使用率 53.8%
2025-07-30 10:19:54,314 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.3%, CPU使用率 100.0%
2025-07-30 10:20:09,480 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.2%, CPU使用率 90.9%
2025-07-30 10:20:24,608 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.2%, CPU使用率 95.8%
2025-07-30 10:20:38,787 - health_monitor - DEBUG - 系统指标 - CPU: 69.1%, 内存: 62.0%, 磁盘: 94.3%
2025-07-30 10:20:39,718 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.7%, CPU使用率 96.4%
2025-07-30 10:20:54,828 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 65.5%
2025-07-30 10:21:09,984 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.3%, CPU使用率 100.0%
2025-07-30 10:21:25,111 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 46.4%
2025-07-30 10:21:39,827 - health_monitor - DEBUG - 系统指标 - CPU: 69.0%, 内存: 60.6%, 磁盘: 94.3%
2025-07-30 10:21:40,507 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.6%, CPU使用率 94.4%
2025-07-30 10:21:55,900 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 62.5%
2025-07-30 10:22:11,007 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.6%, CPU使用率 67.9%
2025-07-30 10:22:26,113 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.7%, CPU使用率 53.6%
2025-07-30 10:22:40,861 - health_monitor - DEBUG - 系统指标 - CPU: 86.9%, 内存: 63.7%, 磁盘: 94.3%
2025-07-30 10:22:41,220 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.3%, CPU使用率 85.7%
2025-07-30 10:22:56,327 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.8%, CPU使用率 91.7%
2025-07-30 10:23:11,435 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.4%, CPU使用率 80.8%
2025-07-30 10:23:26,565 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.9%, CPU使用率 93.1%
2025-07-30 10:23:41,748 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.5%, CPU使用率 82.9%
2025-07-30 10:23:41,887 - health_monitor - DEBUG - 系统指标 - CPU: 82.2%, 内存: 62.6%, 磁盘: 94.3%
2025-07-30 10:23:57,065 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.7%, CPU使用率 93.7%
2025-07-30 10:24:07,724 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-30 10:24:07,864 - main - INFO - 请求没有认证头部
2025-07-30 10:24:07,873 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 10:24:07,909 - main - INFO - --- 请求结束: 200 ---

2025-07-30 10:24:10,262 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 10:24:10,266 - main - INFO - 请求没有认证头部
2025-07-30 10:24:10,268 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 10:24:10,271 - app.core.db_connection - DEBUG - 当前线程ID: 14848
2025-07-30 10:24:10,272 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-30 10:24:10,274 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-30 10:24:10,277 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 10:24:10,280 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-30 10:24:12,223 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.1%, CPU使用率 81.2%
2025-07-30 10:24:12,229 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-30 10:24:12,235 - main - INFO - --- 请求结束: 200 ---

2025-07-30 10:24:27,337 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.6%, CPU使用率 91.7%
2025-07-30 10:24:42,510 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.1%, CPU使用率 100.0%
2025-07-30 10:24:43,449 - health_monitor - DEBUG - 系统指标 - CPU: 74.3%, 内存: 65.7%, 磁盘: 94.3%
2025-07-30 10:24:57,622 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.3%, CPU使用率 50.0%
2025-07-30 10:25:12,756 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.2%, CPU使用率 96.7%
2025-07-30 10:25:28,021 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.1%, CPU使用率 92.1%
2025-07-30 10:25:43,131 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.0%, CPU使用率 85.7%
2025-07-30 10:25:44,689 - health_monitor - DEBUG - 系统指标 - CPU: 86.4%, 内存: 65.4%, 磁盘: 94.3%
2025-07-30 10:25:58,303 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.2%, CPU使用率 97.0%
2025-07-30 10:26:13,416 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.9%, CPU使用率 54.2%
2025-07-30 10:26:28,524 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.2%, CPU使用率 72.0%
2025-07-30 10:26:43,636 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.1%, CPU使用率 33.3%
2025-07-30 10:26:46,479 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 67.2%, 磁盘: 94.3%
2025-07-30 10:26:58,768 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.9%, CPU使用率 100.0%
2025-07-30 10:27:13,886 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.5%, CPU使用率 76.0%
2025-07-30 10:27:28,994 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.8%, CPU使用率 91.7%
2025-07-30 10:27:44,109 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.1%, CPU使用率 91.7%
2025-07-30 10:27:47,826 - health_monitor - DEBUG - 系统指标 - CPU: 66.2%, 内存: 65.1%, 磁盘: 94.3%
2025-07-30 10:27:59,216 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.1%, CPU使用率 29.2%
2025-07-30 10:28:14,385 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.4%, CPU使用率 72.4%
2025-07-30 10:28:29,490 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.9%, CPU使用率 4.2%
2025-07-30 10:28:44,597 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 14.3%
2025-07-30 10:28:48,850 - health_monitor - DEBUG - 系统指标 - CPU: 24.2%, 内存: 60.3%, 磁盘: 94.3%
2025-07-30 10:28:59,702 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 8.3%
2025-07-30 10:29:14,808 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 16.7%
2025-07-30 10:29:29,913 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 0.0%
2025-07-30 10:29:45,024 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 53.6%
2025-07-30 10:29:49,879 - health_monitor - DEBUG - 系统指标 - CPU: 27.7%, 内存: 60.4%, 磁盘: 94.3%
2025-07-30 10:30:00,160 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 55.6%
2025-07-30 10:30:15,267 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 0.0%
2025-07-30 10:30:30,375 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 78.6%
2025-07-30 10:30:45,481 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 4.2%
2025-07-30 10:30:50,901 - health_monitor - DEBUG - 系统指标 - CPU: 30.7%, 内存: 60.4%, 磁盘: 94.3%
2025-07-30 10:31:00,586 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 3.8%
2025-07-30 10:31:15,693 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 14.3%
2025-07-30 10:31:30,798 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 7.7%
2025-07-30 10:31:45,904 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 16.7%
2025-07-30 10:31:51,922 - health_monitor - DEBUG - 系统指标 - CPU: 9.0%, 内存: 60.4%, 磁盘: 94.3%
2025-07-30 10:32:01,010 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 8.3%
2025-07-30 10:32:16,116 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 0.0%
2025-07-30 10:32:31,226 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 55.6%
2025-07-30 10:32:46,332 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 0.0%
2025-07-30 10:32:52,945 - health_monitor - DEBUG - 系统指标 - CPU: 19.1%, 内存: 60.8%, 磁盘: 94.3%
2025-07-30 10:33:01,437 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 7.7%
2025-07-30 10:33:16,543 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 4.2%
2025-07-30 10:33:31,649 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 0.0%
2025-07-30 10:33:46,754 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 17.9%
2025-07-30 10:33:53,969 - health_monitor - DEBUG - 系统指标 - CPU: 16.7%, 内存: 60.5%, 磁盘: 94.3%
2025-07-30 10:34:01,859 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 23.1%
2025-07-30 10:34:16,964 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 8.3%
2025-07-30 10:34:32,077 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 50.0%
2025-07-30 10:34:47,182 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 16.7%
2025-07-30 10:34:54,999 - health_monitor - DEBUG - 系统指标 - CPU: 11.0%, 内存: 60.5%, 磁盘: 94.3%
2025-07-30 10:35:02,288 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 8.0%
2025-07-30 10:35:17,563 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 87.0%
2025-07-30 10:35:32,669 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 12.5%
2025-07-30 10:35:47,775 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.1%, CPU使用率 12.5%
2025-07-30 10:35:56,030 - health_monitor - DEBUG - 系统指标 - CPU: 24.2%, 内存: 61.0%, 磁盘: 94.3%
2025-07-30 10:36:02,881 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.1%, CPU使用率 11.1%
2025-07-30 10:36:17,987 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.2%, CPU使用率 7.1%
2025-07-30 10:36:33,094 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.3%, CPU使用率 26.9%
2025-07-30 10:36:48,207 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.1%, CPU使用率 20.0%
2025-07-30 10:36:57,053 - health_monitor - DEBUG - 系统指标 - CPU: 19.9%, 内存: 61.1%, 磁盘: 94.3%
2025-07-30 10:37:03,315 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.0%, CPU使用率 0.0%
2025-07-30 10:37:18,420 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.0%, CPU使用率 0.0%
2025-07-30 10:37:33,527 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.0%, CPU使用率 4.2%
2025-07-30 10:37:48,632 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.0%, CPU使用率 24.0%
2025-07-30 10:37:58,078 - health_monitor - DEBUG - 系统指标 - CPU: 21.4%, 内存: 61.1%, 磁盘: 94.3%
2025-07-30 10:38:03,747 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.9%, CPU使用率 79.2%
2025-07-30 10:38:18,853 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 45.8%
2025-07-30 10:38:33,959 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 3.6%
2025-07-30 10:38:49,066 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 14.3%
2025-07-30 10:38:59,100 - health_monitor - DEBUG - 系统指标 - CPU: 35.5%, 内存: 60.9%, 磁盘: 94.3%
2025-07-30 10:39:04,174 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 29.6%
2025-07-30 10:39:19,280 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 0.0%
2025-07-30 10:39:34,386 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 8.3%
2025-07-30 10:39:49,491 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 0.0%
2025-07-30 10:40:00,123 - health_monitor - DEBUG - 系统指标 - CPU: 15.8%, 内存: 60.8%, 磁盘: 94.3%
2025-07-30 10:40:04,597 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 26.9%
2025-07-30 10:40:19,704 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 3.6%
2025-07-30 10:40:34,811 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 20.8%
2025-07-30 10:40:49,916 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 0.0%
2025-07-30 10:41:01,145 - health_monitor - DEBUG - 系统指标 - CPU: 17.4%, 内存: 60.7%, 磁盘: 94.3%
2025-07-30 10:41:05,022 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 10.7%
2025-07-30 10:41:20,128 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 7.1%
2025-07-30 10:41:35,234 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 3.8%
2025-07-30 10:41:50,339 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 4.2%
2025-07-30 10:42:02,167 - health_monitor - DEBUG - 系统指标 - CPU: 9.0%, 内存: 60.7%, 磁盘: 94.3%
2025-07-30 10:42:05,446 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 12.5%
2025-07-30 10:42:20,552 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 7.1%
2025-07-30 10:42:35,659 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 35.7%
2025-07-30 10:42:50,764 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 8.7%
2025-07-30 10:43:03,188 - health_monitor - DEBUG - 系统指标 - CPU: 20.7%, 内存: 60.6%, 磁盘: 94.3%
2025-07-30 10:43:05,871 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 20.8%
2025-07-30 10:43:20,977 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 16.7%
2025-07-30 10:43:36,082 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 46.4%
2025-07-30 10:43:51,187 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 22.2%
2025-07-30 10:44:04,212 - health_monitor - DEBUG - 系统指标 - CPU: 19.1%, 内存: 60.8%, 磁盘: 94.3%
2025-07-30 10:44:06,294 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 4.2%
2025-07-30 10:44:21,399 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 22.7%
2025-07-30 10:44:36,508 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 25.0%
2025-07-30 10:44:51,613 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 0.0%
2025-07-30 10:45:05,233 - health_monitor - DEBUG - 系统指标 - CPU: 19.5%, 内存: 60.7%, 磁盘: 94.3%
2025-07-30 10:45:06,722 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 3.7%
2025-07-30 10:45:21,838 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 70.8%
2025-07-30 10:45:36,945 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 7.7%
2025-07-30 10:45:52,050 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 0.0%
2025-07-30 10:46:06,254 - health_monitor - DEBUG - 系统指标 - CPU: 8.7%, 内存: 60.8%, 磁盘: 94.3%
2025-07-30 10:46:07,156 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 14.8%
2025-07-30 10:46:22,262 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 20.8%
2025-07-30 10:46:37,367 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 16.0%
2025-07-30 10:46:52,475 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 15.4%
2025-07-30 10:47:07,294 - health_monitor - DEBUG - 系统指标 - CPU: 17.0%, 内存: 60.8%, 磁盘: 94.3%
2025-07-30 10:47:07,581 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 14.3%
2025-07-30 10:47:22,688 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 24.0%
2025-07-30 10:47:37,795 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 8.3%
2025-07-30 10:47:52,902 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 29.2%
2025-07-30 10:48:08,008 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 0.0%
2025-07-30 10:48:08,316 - health_monitor - DEBUG - 系统指标 - CPU: 20.3%, 内存: 60.7%, 磁盘: 94.3%
2025-07-30 10:48:23,114 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 22.2%
2025-07-30 10:48:38,219 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 10.7%
2025-07-30 10:48:53,324 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 0.0%
2025-07-30 10:49:08,430 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 20.8%
2025-07-30 10:49:09,338 - health_monitor - DEBUG - 系统指标 - CPU: 10.5%, 内存: 60.7%, 磁盘: 94.3%
2025-07-30 10:49:23,536 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 7.1%
2025-07-30 10:49:38,641 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 11.5%
2025-07-30 10:49:53,748 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 0.0%
2025-07-30 10:50:08,854 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 16.7%
2025-07-30 10:50:10,360 - health_monitor - DEBUG - 系统指标 - CPU: 20.3%, 内存: 60.7%, 磁盘: 94.3%
2025-07-30 10:50:23,959 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 32.1%
2025-07-30 10:50:39,064 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 0.0%
2025-07-30 10:50:54,170 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 16.7%
2025-07-30 10:51:09,401 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 94.7%
2025-07-30 10:51:11,382 - health_monitor - DEBUG - 系统指标 - CPU: 26.7%, 内存: 60.7%, 磁盘: 94.3%
2025-07-30 10:51:24,507 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 0.0%
2025-07-30 10:51:39,613 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 0.0%
2025-07-30 10:51:54,718 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 21.4%
2025-07-30 10:52:09,824 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 16.7%
2025-07-30 10:52:12,404 - health_monitor - DEBUG - 系统指标 - CPU: 9.4%, 内存: 60.6%, 磁盘: 94.3%
2025-07-30 10:52:24,930 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 29.2%
2025-07-30 10:52:40,035 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.1%, CPU使用率 0.0%
2025-07-30 10:52:55,141 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.9%, CPU使用率 21.4%
2025-07-30 10:53:10,248 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.0%, CPU使用率 29.2%
2025-07-30 10:53:13,433 - health_monitor - DEBUG - 系统指标 - CPU: 16.0%, 内存: 61.1%, 磁盘: 94.3%
2025-07-30 10:53:25,354 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.0%, CPU使用率 16.7%
2025-07-30 10:53:40,460 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.9%, CPU使用率 20.0%
2025-07-30 10:53:55,566 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 25.0%
2025-07-30 10:54:10,672 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 0.0%
2025-07-30 10:54:14,459 - health_monitor - DEBUG - 系统指标 - CPU: 37.7%, 内存: 60.8%, 磁盘: 94.3%
2025-07-30 10:54:25,780 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 29.2%
2025-07-30 10:54:40,885 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.0%, CPU使用率 12.5%
2025-07-30 10:54:55,993 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.2%, CPU使用率 8.3%
2025-07-30 10:55:11,099 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.1%, CPU使用率 14.3%
2025-07-30 10:55:15,480 - health_monitor - DEBUG - 系统指标 - CPU: 26.6%, 内存: 61.2%, 磁盘: 94.3%
2025-07-30 10:55:26,205 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.0%, CPU使用率 10.7%
2025-07-30 10:55:41,311 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.0%, CPU使用率 25.0%
2025-07-30 10:55:56,417 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.4%, CPU使用率 12.5%
2025-07-30 10:56:11,522 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.2%, CPU使用率 8.0%
2025-07-30 10:56:16,503 - health_monitor - DEBUG - 系统指标 - CPU: 28.4%, 内存: 61.3%, 磁盘: 94.3%
2025-07-30 10:56:26,627 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.2%, CPU使用率 0.0%
2025-07-30 10:56:41,736 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.2%, CPU使用率 3.6%
2025-07-30 10:56:56,841 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.5%, CPU使用率 0.0%
2025-07-30 10:57:11,946 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.3%, CPU使用率 41.7%
2025-07-30 10:57:17,530 - health_monitor - DEBUG - 系统指标 - CPU: 27.2%, 内存: 61.4%, 磁盘: 94.3%
2025-07-30 10:57:27,053 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.3%, CPU使用率 70.4%
2025-07-30 10:57:42,160 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.2%, CPU使用率 18.5%
2025-07-30 10:57:57,266 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.3%, CPU使用率 42.3%
2025-07-30 10:58:12,374 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.2%, CPU使用率 3.6%
2025-07-30 10:58:18,552 - health_monitor - DEBUG - 系统指标 - CPU: 12.8%, 内存: 61.3%, 磁盘: 94.3%
2025-07-30 10:58:27,481 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.1%, CPU使用率 62.5%
2025-07-30 10:58:42,586 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.3%, CPU使用率 50.0%
2025-07-30 10:58:57,691 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.2%, CPU使用率 8.0%
2025-07-30 10:59:12,800 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.5%, CPU使用率 56.7%
2025-07-30 10:59:19,603 - health_monitor - DEBUG - 系统指标 - CPU: 86.3%, 内存: 63.1%, 磁盘: 94.3%
2025-07-30 10:59:27,909 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.4%, CPU使用率 82.1%
2025-07-30 10:59:43,015 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.9%, CPU使用率 75.0%
2025-07-30 10:59:58,140 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.0%, CPU使用率 83.3%
2025-07-30 11:00:13,256 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.4%, CPU使用率 75.0%
2025-07-30 11:00:20,730 - health_monitor - DEBUG - 系统指标 - CPU: 69.9%, 内存: 64.9%, 磁盘: 94.3%
2025-07-30 11:00:28,367 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.9%, CPU使用率 61.5%
2025-07-30 11:00:43,487 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.7%, CPU使用率 100.0%
2025-07-30 11:00:58,631 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.6%, CPU使用率 100.0%
2025-07-30 11:01:13,747 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.7%, CPU使用率 66.7%
2025-07-30 11:01:21,780 - health_monitor - DEBUG - 系统指标 - CPU: 98.1%, 内存: 63.0%, 磁盘: 94.3%
2025-07-30 11:01:28,897 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.2%, CPU使用率 96.4%
2025-07-30 11:01:44,075 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.0%, CPU使用率 79.2%
2025-07-30 11:01:59,182 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 65.2%
2025-07-30 11:02:14,289 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.2%, CPU使用率 62.5%
2025-07-30 11:02:23,337 - health_monitor - DEBUG - 系统指标 - CPU: 97.9%, 内存: 64.2%, 磁盘: 94.3%
2025-07-30 11:02:29,471 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.2%, CPU使用率 100.0%
2025-07-30 11:02:44,643 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.0%, CPU使用率 86.2%
2025-07-30 11:02:59,999 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.9%, CPU使用率 100.0%
2025-07-30 11:03:15,206 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.2%, CPU使用率 85.7%
2025-07-30 11:03:24,439 - health_monitor - DEBUG - 系统指标 - CPU: 91.5%, 内存: 64.1%, 磁盘: 94.3%
2025-07-30 11:03:30,466 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.8%, CPU使用率 100.0%
2025-07-30 11:03:45,596 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.3%, CPU使用率 96.6%
2025-07-30 11:04:01,012 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.1%, CPU使用率 100.0%
2025-07-30 11:04:16,188 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.5%, CPU使用率 94.4%
2025-07-30 11:04:25,562 - health_monitor - DEBUG - 系统指标 - CPU: 77.7%, 内存: 67.2%, 磁盘: 94.3%
2025-07-30 11:04:31,304 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.4%, CPU使用率 92.6%
2025-07-30 11:04:46,674 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.1%, CPU使用率 100.0%
2025-07-30 11:05:02,090 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.8%, CPU使用率 100.0%
2025-07-30 11:05:17,246 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.5%, CPU使用率 91.7%
2025-07-30 11:05:26,980 - health_monitor - DEBUG - 系统指标 - CPU: 88.8%, 内存: 65.6%, 磁盘: 94.3%
2025-07-30 11:05:32,585 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.6%, CPU使用率 100.0%
2025-07-30 11:05:47,846 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.1%, CPU使用率 84.8%
2025-07-30 11:06:03,200 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.1%, CPU使用率 100.0%
2025-07-30 11:06:18,364 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.9%, CPU使用率 70.4%
2025-07-30 11:06:28,115 - health_monitor - DEBUG - 系统指标 - CPU: 88.7%, 内存: 66.5%, 磁盘: 94.3%
2025-07-30 11:06:33,544 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.7%, CPU使用率 86.7%
2025-07-30 11:06:48,804 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.6%, CPU使用率 98.2%
2025-07-30 11:07:03,923 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.4%, CPU使用率 92.9%
2025-07-30 11:07:19,482 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.5%, CPU使用率 100.0%
2025-07-30 11:07:29,470 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 68.4%, 磁盘: 94.3%
2025-07-30 11:07:35,022 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.8%, CPU使用率 100.0%
2025-07-30 11:07:50,302 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.7%, CPU使用率 96.4%
2025-07-30 11:08:05,559 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.0%, CPU使用率 100.0%
2025-07-30 11:08:20,733 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.8%, CPU使用率 100.0%
2025-07-30 11:08:30,955 - health_monitor - DEBUG - 系统指标 - CPU: 99.7%, 内存: 66.4%, 磁盘: 94.3%
2025-07-30 11:08:36,087 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.6%, CPU使用率 100.0%
2025-07-30 11:08:51,924 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.8%, CPU使用率 100.0%
2025-07-30 11:09:07,219 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.4%, CPU使用率 92.9%
2025-07-30 11:09:22,548 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.1%, CPU使用率 100.0%
2025-07-30 11:09:32,007 - health_monitor - DEBUG - 系统指标 - CPU: 80.5%, 内存: 66.8%, 磁盘: 94.3%
2025-07-30 11:09:37,672 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.3%, CPU使用率 88.9%
2025-07-30 11:09:52,787 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.5%, CPU使用率 82.1%
2025-07-30 11:10:08,034 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.1%, CPU使用率 100.0%
2025-07-30 11:10:23,498 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.8%, CPU使用率 100.0%
2025-07-30 11:10:33,681 - health_monitor - DEBUG - 系统指标 - CPU: 92.3%, 内存: 68.1%, 磁盘: 94.3%
2025-07-30 11:10:38,913 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.4%, CPU使用率 93.1%
2025-07-30 11:10:54,201 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 100.0%
2025-07-30 11:11:09,584 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.8%, CPU使用率 100.0%
2025-07-30 11:11:24,738 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.1%, CPU使用率 29.6%
2025-07-30 11:11:34,812 - health_monitor - DEBUG - 系统指标 - CPU: 54.3%, 内存: 58.9%, 磁盘: 94.3%
2025-07-30 11:11:39,844 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.8%, CPU使用率 44.4%
2025-07-30 11:11:54,949 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.7%, CPU使用率 50.0%
2025-07-30 11:12:10,054 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.0%, CPU使用率 11.5%
2025-07-30 11:12:25,171 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.9%, CPU使用率 70.8%
2025-07-30 11:12:35,834 - health_monitor - DEBUG - 系统指标 - CPU: 52.9%, 内存: 59.0%, 磁盘: 94.3%
2025-07-30 11:12:40,282 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.9%, CPU使用率 28.6%
2025-07-30 11:12:55,389 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.9%, CPU使用率 41.7%
2025-07-30 11:13:10,496 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.2%, CPU使用率 54.2%
2025-07-30 11:13:25,603 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.2%, CPU使用率 37.5%
2025-07-30 11:13:36,860 - health_monitor - DEBUG - 系统指标 - CPU: 39.0%, 内存: 59.4%, 磁盘: 94.3%
2025-07-30 11:13:40,710 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.3%, CPU使用率 17.9%
2025-07-30 11:13:55,815 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.2%, CPU使用率 25.9%
2025-07-30 11:14:10,921 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.1%, CPU使用率 16.7%
2025-07-30 11:14:26,030 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.1%, CPU使用率 83.3%
2025-07-30 11:14:37,924 - health_monitor - DEBUG - 系统指标 - CPU: 73.8%, 内存: 61.1%, 磁盘: 94.3%
2025-07-30 11:14:41,139 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.4%, CPU使用率 54.2%
2025-07-30 11:48:09,512 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-30 11:48:09,724 - auth_service - INFO - 统一认证服务初始化完成
2025-07-30 11:48:10,087 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-30 11:48:10,342 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-30 11:48:12,085 - BackendMockDataManager - INFO - 后端模拟数据模式已启用
2025-07-30 11:48:14,495 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\psutil\__init__.py
2025-07-30 11:48:14,594 - fallback_manager - DEBUG - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-07-30 11:48:14,695 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-07-30 11:48:15,822 - health_monitor - INFO - 健康监控器初始化完成
2025-07-30 11:48:15,930 - app.core.system_monitor - INFO - 已加载 288 个历史数据点
2025-07-30 11:48:16,119 - app.core.alert_detector - INFO - 已加载 6 个告警规则
2025-07-30 11:48:16,334 - app.core.alert_detector - INFO - 已加载 0 个当前告警
2025-07-30 11:48:16,481 - app.core.alert_detector - INFO - 已加载 370 个历史告警
2025-07-30 11:48:16,689 - app.core.alert_detector - INFO - 已加载 1 个通知渠道
2025-07-30 11:48:16,732 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-30 11:48:16,755 - alert_manager - INFO - 已初始化默认告警规则
2025-07-30 11:48:16,765 - alert_manager - INFO - 已初始化默认通知渠道
2025-07-30 11:48:16,778 - alert_manager - INFO - 告警管理器初始化完成
2025-07-30 11:48:19,056 - db_service - INFO - 数据库引擎和会话工厂创建成功
2025-07-30 11:48:19,062 - db_service - INFO - 数据库服务初始化完成
2025-07-30 11:48:19,076 - notification_service - INFO - 通知服务初始化完成
2025-07-30 11:48:19,082 - main - INFO - 错误处理模块导入成功
2025-07-30 11:48:19,398 - main - INFO - 监控模块导入成功
2025-07-30 11:48:19,537 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-07-30 11:48:27,679 - app.services.ocr_service - WARNING - OpenCV未安装，图像预处理功能将不可用
2025-07-30 11:48:27,794 - main - INFO - 应用启动中...
2025-07-30 11:48:27,798 - error_handling - INFO - 错误处理已设置
2025-07-30 11:48:27,803 - main - INFO - 错误处理系统初始化完成
2025-07-30 11:48:27,812 - monitoring - INFO - 添加指标端点成功: /metrics
2025-07-30 11:48:27,814 - monitoring - INFO - 添加健康检查端点成功: /health
2025-07-30 11:48:27,816 - monitoring - INFO - 添加详细健康检查端点成功: /api/health/detailed
2025-07-30 11:48:27,819 - monitoring - INFO - 系统信息初始化完成: Markey, Windows-10-10.0.19045-SP0, Python 3.13.2, CPU核心数: 4
2025-07-30 11:48:27,837 - monitoring - INFO - 启动资源监控线程成功
2025-07-30 11:48:27,838 - monitoring - INFO - 监控系统初始化成功（不使用中间件）
2025-07-30 11:48:27,844 - monitoring - INFO - 监控系统初始化完成
2025-07-30 11:48:27,847 - main - INFO - 监控系统初始化完成
2025-07-30 11:48:27,850 - app.db.init_db - INFO - 所有模型导入成功
2025-07-30 11:48:27,851 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-07-30 11:48:27,855 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 11:48:27,861 - app.db.init_db - INFO - 所有模型导入成功
2025-07-30 11:48:27,864 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-07-30 11:48:27,865 - app.db.init_db - INFO - 正在运行数据库迁移...
2025-07-30 11:48:27,867 - app.db.init_db - INFO - 正在检查并更新数据库表结构...
2025-07-30 11:48:27,869 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-30 11:48:27,871 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alerts")
2025-07-30 11:48:27,880 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:27,886 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_rules")
2025-07-30 11:48:27,888 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:27,896 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_channels")
2025-07-30 11:48:27,898 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:27,901 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-30 11:48:27,903 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:27,916 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_records")
2025-07-30 11:48:27,920 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:27,929 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_overviews")
2025-07-30 11:48:27,932 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:27,934 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medical_records")
2025-07-30 11:48:27,936 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:27,938 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.4%, CPU使用率 100.0%
2025-07-30 11:48:27,944 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("inpatient_records")
2025-07-30 11:48:27,948 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:27,951 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("surgery_records")
2025-07-30 11:48:27,953 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:27,958 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_reports")
2025-07-30 11:48:27,963 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:27,966 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_report_items")
2025-07-30 11:48:27,968 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:27,970 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_reports")
2025-07-30 11:48:27,978 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:27,983 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("examination_reports")
2025-07-30 11:48:27,984 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:27,987 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("documents")
2025-07-30 11:48:27,992 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:27,998 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("other_records")
2025-07-30 11:48:28,000 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:28,003 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("operation_logs")
2025-07-30 11:48:28,005 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:28,014 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("follow_up_records")
2025-07-30 11:48:28,017 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:28,019 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_diaries")
2025-07-30 11:48:28,021 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:28,030 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("registration_records")
2025-07-30 11:48:28,032 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:28,036 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("prescription_records")
2025-07-30 11:48:28,038 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:28,047 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("laboratory_records")
2025-07-30 11:48:28,052 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:28,063 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_records")
2025-07-30 11:48:28,067 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:28,071 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medications")
2025-07-30 11:48:28,080 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:28,084 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medication_usages")
2025-07-30 11:48:28,087 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:28,097 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("role_applications")
2025-07-30 11:48:28,101 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:28,104 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessments")
2025-07-30 11:48:28,116 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:28,121 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_items")
2025-07-30 11:48:28,131 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:28,135 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_responses")
2025-07-30 11:48:28,155 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:28,167 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_templates")
2025-07-30 11:48:28,171 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:28,182 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_template_questions")
2025-07-30 11:48:28,186 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:28,197 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaires")
2025-07-30 11:48:28,201 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:28,211 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_items")
2025-07-30 11:48:28,231 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:28,236 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_responses")
2025-07-30 11:48:28,244 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:28,249 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_answers")
2025-07-30 11:48:28,252 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:28,283 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_templates")
2025-07-30 11:48:28,297 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:28,301 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_template_questions")
2025-07-30 11:48:28,313 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:28,318 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_results")
2025-07-30 11:48:28,325 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:28,331 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_results")
2025-07-30 11:48:28,335 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:28,344 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("report_templates")
2025-07-30 11:48:28,348 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:28,352 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_distributions")
2025-07-30 11:48:28,361 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:28,365 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_distributions")
2025-07-30 11:48:28,369 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:28,376 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("service_stats")
2025-07-30 11:48:28,381 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 11:48:28,395 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-30 11:48:28,399 - app.db.init_db - INFO - 数据库表结构检查和更新完成
2025-07-30 11:48:28,414 - app.db.init_db - INFO - 模型关系初始化完成
2025-07-30 11:48:28,417 - app.db.init_db - INFO - 模型关系设置完成
2025-07-30 11:48:28,420 - main - INFO - 数据库初始化完成（强制重建）
2025-07-30 11:48:28,429 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 11:48:28,434 - main - INFO - 数据库连接正常
2025-07-30 11:48:28,436 - main - INFO - 开始初始化模板数据
2025-07-30 11:48:28,438 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 11:48:29,214 - app.db.init_templates - INFO - 开始初始化评估量表模板...
2025-07-30 11:48:29,329 - app.db.init_templates - INFO - 成功初始化 5 个评估量表模板
2025-07-30 11:48:29,494 - app.db.init_templates - INFO - 开始初始化调查问卷模板...
2025-07-30 11:48:29,646 - app.db.init_templates - INFO - 成功初始化 5 个调查问卷模板
2025-07-30 11:48:29,827 - main - INFO - 模板数据初始化完成
2025-07-30 11:48:30,126 - main - INFO - 数据库表结构已经标准化，不需要迁移
2025-07-30 11:48:30,484 - main - INFO - 应用启动完成
2025-07-30 11:48:43,417 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.8%, CPU使用率 100.0%
2025-07-30 11:48:44,756 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-30 11:48:44,902 - main - INFO - 请求没有认证头部
2025-07-30 11:48:44,990 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 11:48:44,992 - main - INFO - --- 请求结束: 200 ---

2025-07-30 11:48:47,368 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 11:48:47,377 - main - INFO - 请求没有认证头部
2025-07-30 11:48:47,378 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 11:48:47,478 - app.core.db_connection - DEBUG - 当前线程ID: 10988
2025-07-30 11:48:47,508 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-30 11:48:47,548 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-30 11:48:47,561 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-30 11:48:47,563 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 11:48:47,564 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-30 11:48:51,342 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-30 11:48:51,862 - main - INFO - --- 请求结束: 200 ---

2025-07-30 11:48:59,933 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.9%, CPU使用率 100.0%
2025-07-30 11:49:15,553 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.8%, CPU使用率 100.0%
2025-07-30 11:49:16,682 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 66.1%, 磁盘: 94.3%
2025-07-30 11:49:31,226 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.7%, CPU使用率 100.0%
2025-07-30 11:49:46,449 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.1%, CPU使用率 70.8%
2025-07-30 11:50:01,625 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.0%, CPU使用率 100.0%
2025-07-30 11:50:16,933 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.2%, CPU使用率 96.9%
2025-07-30 11:50:17,862 - health_monitor - DEBUG - 系统指标 - CPU: 87.1%, 内存: 66.0%, 磁盘: 94.3%
2025-07-30 11:50:32,499 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.8%, CPU使用率 100.0%
2025-07-30 11:50:48,365 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 100.0%
2025-07-30 11:51:04,425 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.1%, CPU使用率 100.0%
2025-07-30 11:51:19,511 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 65.4%, 磁盘: 94.3%
2025-07-30 11:51:19,649 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.4%, CPU使用率 100.0%
2025-07-30 11:51:35,120 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.8%, CPU使用率 100.0%
2025-07-30 11:51:50,300 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.3%, CPU使用率 96.7%
2025-07-30 11:52:05,553 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.4%, CPU使用率 94.1%
2025-07-30 11:52:20,630 - health_monitor - DEBUG - 系统指标 - CPU: 99.3%, 内存: 64.7%, 磁盘: 94.3%
2025-07-30 11:52:20,686 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.7%, CPU使用率 95.8%
2025-07-30 11:52:36,484 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.8%, CPU使用率 100.0%
2025-07-30 11:52:52,000 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.7%, CPU使用率 93.0%
2025-07-30 11:53:07,244 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.6%, CPU使用率 100.0%
2025-07-30 11:53:21,867 - health_monitor - DEBUG - 系统指标 - CPU: 99.6%, 内存: 67.2%, 磁盘: 94.3%
2025-07-30 11:53:22,363 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.8%, CPU使用率 82.8%
2025-07-30 11:53:37,469 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.7%, CPU使用率 44.4%
2025-07-30 11:53:52,574 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.5%, CPU使用率 45.8%
2025-07-30 11:54:07,702 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.2%, CPU使用率 30.8%
2025-07-30 11:54:16,921 - alert_manager - WARNING - 触发告警: disk_usage, 当前值: 94.3, 阈值: 90
2025-07-30 11:54:16,947 - alert_manager - WARNING - 触发告警: disk_free_space, 当前值: 0.0, 阈值: 1024
2025-07-30 11:54:22,807 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.6%, CPU使用率 37.5%
2025-07-30 11:54:22,924 - health_monitor - DEBUG - 系统指标 - CPU: 51.0%, 内存: 64.4%, 磁盘: 94.3%
2025-07-30 11:54:37,915 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.1%, CPU使用率 62.5%
2025-07-30 11:54:53,040 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.6%, CPU使用率 100.0%
2025-07-30 11:55:08,194 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.3%, CPU使用率 100.0%
2025-07-30 11:55:23,312 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.3%, CPU使用率 81.5%
2025-07-30 11:55:24,042 - health_monitor - DEBUG - 系统指标 - CPU: 70.3%, 内存: 66.1%, 磁盘: 94.3%
2025-07-30 11:55:38,420 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.9%, CPU使用率 75.0%
2025-07-30 11:55:53,525 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.2%, CPU使用率 37.5%
2025-07-30 11:56:08,733 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 100.0%
2025-07-30 11:56:23,896 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.1%, CPU使用率 75.0%
2025-07-30 11:56:25,105 - health_monitor - DEBUG - 系统指标 - CPU: 69.1%, 内存: 62.4%, 磁盘: 94.3%
2025-07-30 11:56:39,001 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.2%, CPU使用率 37.0%
2025-07-30 11:56:54,107 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.3%, CPU使用率 4.2%
2025-07-30 11:56:54,192 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-30 11:56:54,193 - main - INFO - 请求没有认证头部
2025-07-30 11:56:54,194 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 11:56:54,195 - main - INFO - --- 请求结束: 200 ---

2025-07-30 11:56:56,229 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 11:56:56,230 - main - INFO - 请求没有认证头部
2025-07-30 11:56:56,230 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 11:56:56,232 - app.core.db_connection - DEBUG - 当前线程ID: 10988
2025-07-30 11:56:56,233 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-30 11:56:56,234 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-30 11:56:56,235 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-30 11:56:56,236 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 11:56:56,236 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-30 11:56:57,002 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-30 11:56:57,004 - main - INFO - --- 请求结束: 200 ---

2025-07-30 11:57:09,212 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.5%, CPU使用率 33.3%
2025-07-30 11:57:24,317 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.8%, CPU使用率 28.6%
2025-07-30 11:57:26,134 - health_monitor - DEBUG - 系统指标 - CPU: 37.7%, 内存: 62.8%, 磁盘: 94.3%
2025-07-30 11:57:39,435 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.5%, CPU使用率 92.9%
2025-07-30 11:57:54,543 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 41.7%
2025-07-30 11:58:09,648 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 37.5%
2025-07-30 11:58:24,752 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.0%, CPU使用率 37.0%
2025-07-30 11:58:27,158 - health_monitor - DEBUG - 系统指标 - CPU: 39.9%, 内存: 60.0%, 磁盘: 94.3%
2025-07-30 11:58:34,627 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-30 11:58:34,628 - main - INFO - 请求没有认证头部
2025-07-30 11:58:34,629 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 11:58:34,630 - main - INFO - --- 请求结束: 200 ---

2025-07-30 11:58:36,683 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 11:58:36,684 - main - INFO - 请求没有认证头部
2025-07-30 11:58:36,684 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 11:58:36,686 - app.core.db_connection - DEBUG - 当前线程ID: 10988
2025-07-30 11:58:36,687 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-30 11:58:36,688 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-30 11:58:36,688 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-30 11:58:36,689 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 11:58:36,690 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-30 11:58:37,455 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-30 11:58:37,458 - main - INFO - --- 请求结束: 200 ---

2025-07-30 11:58:39,856 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.4%, CPU使用率 12.5%
2025-07-30 11:58:54,962 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.2%, CPU使用率 37.5%
2025-07-30 11:59:10,067 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.2%, CPU使用率 25.0%
2025-07-30 11:59:25,176 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.9%, CPU使用率 39.3%
2025-07-30 11:59:28,192 - health_monitor - DEBUG - 系统指标 - CPU: 45.5%, 内存: 62.0%, 磁盘: 94.3%
2025-07-30 11:59:40,298 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.1%, CPU使用率 89.3%
2025-07-30 11:59:55,411 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.4%, CPU使用率 50.0%
2025-07-30 12:00:10,524 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.4%, CPU使用率 100.0%
2025-07-30 12:00:25,629 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.8%, CPU使用率 60.7%
2025-07-30 12:00:29,215 - health_monitor - DEBUG - 系统指标 - CPU: 66.2%, 内存: 62.3%, 磁盘: 94.3%
2025-07-30 12:00:40,798 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 96.7%
2025-07-30 12:00:56,114 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.0%, CPU使用率 100.0%
2025-07-30 12:01:11,228 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.9%, CPU使用率 88.5%
2025-07-30 12:01:26,566 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.8%, CPU使用率 90.2%
2025-07-30 12:01:30,408 - health_monitor - DEBUG - 系统指标 - CPU: 97.3%, 内存: 63.2%, 磁盘: 94.3%
2025-07-30 12:01:41,677 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.4%, CPU使用率 66.7%
2025-07-30 12:01:56,784 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.0%, CPU使用率 71.4%
2025-07-30 12:02:12,300 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.4%, CPU使用率 100.0%
2025-07-30 12:02:27,534 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.6%, CPU使用率 66.7%
2025-07-30 12:02:31,738 - health_monitor - DEBUG - 系统指标 - CPU: 70.7%, 内存: 66.3%, 磁盘: 94.3%
2025-07-30 12:02:42,639 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.5%, CPU使用率 70.8%
2025-07-30 12:02:57,816 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.8%, CPU使用率 100.0%
2025-07-30 12:03:12,928 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 71.4%
2025-07-30 12:03:28,034 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.2%, CPU使用率 78.6%
2025-07-30 12:03:32,873 - health_monitor - DEBUG - 系统指标 - CPU: 67.4%, 内存: 63.7%, 磁盘: 94.3%
2025-07-30 12:03:43,142 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.7%, CPU使用率 92.9%
2025-07-30 12:03:58,249 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.2%, CPU使用率 62.5%
2025-07-30 12:04:13,354 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.3%, CPU使用率 83.3%
2025-07-30 12:04:28,503 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.5%, CPU使用率 93.5%
2025-07-30 12:04:34,033 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 65.5%, 磁盘: 94.3%
2025-07-30 12:04:34,885 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-30 12:04:34,894 - main - INFO - 请求没有认证头部
2025-07-30 12:04:34,894 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 12:04:34,899 - main - INFO - --- 请求结束: 200 ---

2025-07-30 12:04:37,310 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 12:04:37,310 - main - INFO - 请求没有认证头部
2025-07-30 12:04:37,311 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 12:04:37,314 - app.core.db_connection - DEBUG - 当前线程ID: 10988
2025-07-30 12:04:37,316 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-30 12:04:37,317 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-30 12:04:37,318 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-30 12:04:37,319 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 12:04:37,320 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-30 12:04:39,555 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-30 12:04:39,558 - main - INFO - --- 请求结束: 200 ---

2025-07-30 12:04:43,899 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.2%, CPU使用率 100.0%
2025-07-30 12:04:59,011 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 95.8%
2025-07-30 12:05:14,188 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.3%, CPU使用率 96.6%
2025-07-30 12:05:29,447 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 88.5%
2025-07-30 12:05:35,119 - health_monitor - DEBUG - 系统指标 - CPU: 84.9%, 内存: 66.7%, 磁盘: 94.3%
2025-07-30 12:05:44,877 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.0%, CPU使用率 100.0%
2025-07-30 12:06:00,174 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.0%, CPU使用率 60.7%
2025-07-30 12:06:15,293 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.7%, CPU使用率 85.7%
2025-07-30 12:06:30,400 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.3%, CPU使用率 25.0%
2025-07-30 12:06:36,456 - health_monitor - DEBUG - 系统指标 - CPU: 99.3%, 内存: 68.6%, 磁盘: 94.3%
2025-07-30 12:06:45,517 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.0%, CPU使用率 80.8%
2025-07-30 12:07:00,924 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.3%, CPU使用率 98.5%
2025-07-30 12:07:16,096 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.4%, CPU使用率 11.5%
2025-07-30 12:07:31,201 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 29.2%
2025-07-30 12:07:37,477 - health_monitor - DEBUG - 系统指标 - CPU: 37.5%, 内存: 60.3%, 磁盘: 94.3%
2025-07-30 12:07:46,306 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.0%, CPU使用率 16.7%
2025-07-30 12:08:01,410 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 15.4%
2025-07-30 12:08:16,622 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 84.0%
2025-07-30 14:20:39,975 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-30 14:20:39,991 - auth_service - INFO - 统一认证服务初始化完成
2025-07-30 14:20:40,986 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-30 14:20:40,996 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-30 14:20:43,563 - BackendMockDataManager - INFO - 后端模拟数据模式已启用
2025-07-30 14:20:46,344 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\psutil\__init__.py
2025-07-30 14:20:46,407 - fallback_manager - DEBUG - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-07-30 14:20:46,454 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-07-30 14:20:46,768 - health_monitor - INFO - 健康监控器初始化完成
2025-07-30 14:20:46,807 - app.core.system_monitor - INFO - 已加载 288 个历史数据点
2025-07-30 14:20:46,825 - app.core.alert_detector - INFO - 已加载 6 个告警规则
2025-07-30 14:20:46,842 - app.core.alert_detector - INFO - 已加载 0 个当前告警
2025-07-30 14:20:46,890 - app.core.alert_detector - INFO - 已加载 370 个历史告警
2025-07-30 14:20:46,904 - app.core.alert_detector - INFO - 已加载 1 个通知渠道
2025-07-30 14:20:46,925 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-30 14:20:46,987 - alert_manager - INFO - 已初始化默认告警规则
2025-07-30 14:20:47,090 - alert_manager - INFO - 已初始化默认通知渠道
2025-07-30 14:20:47,194 - alert_manager - INFO - 告警管理器初始化完成
2025-07-30 14:20:49,467 - db_service - INFO - 数据库引擎和会话工厂创建成功
2025-07-30 14:20:49,472 - db_service - INFO - 数据库服务初始化完成
2025-07-30 14:20:49,490 - notification_service - INFO - 通知服务初始化完成
2025-07-30 14:20:49,493 - main - INFO - 错误处理模块导入成功
2025-07-30 14:20:49,566 - main - INFO - 监控模块导入成功
2025-07-30 14:20:49,608 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-07-30 14:20:55,250 - app.services.ocr_service - WARNING - OpenCV未安装，图像预处理功能将不可用
2025-07-30 14:20:55,419 - main - INFO - 应用启动中...
2025-07-30 14:20:55,422 - error_handling - INFO - 错误处理已设置
2025-07-30 14:20:55,424 - main - INFO - 错误处理系统初始化完成
2025-07-30 14:20:55,470 - monitoring - INFO - 添加指标端点成功: /metrics
2025-07-30 14:20:55,502 - monitoring - INFO - 添加健康检查端点成功: /health
2025-07-30 14:20:55,537 - monitoring - INFO - 添加详细健康检查端点成功: /api/health/detailed
2025-07-30 14:20:55,587 - monitoring - INFO - 系统信息初始化完成: Markey, Windows-10-10.0.19045-SP0, Python 3.13.2, CPU核心数: 4
2025-07-30 14:20:55,789 - monitoring - INFO - 启动资源监控线程成功
2025-07-30 14:20:55,791 - monitoring - INFO - 监控系统初始化成功（不使用中间件）
2025-07-30 14:20:55,832 - monitoring - INFO - 监控系统初始化完成
2025-07-30 14:20:55,872 - main - INFO - 监控系统初始化完成
2025-07-30 14:20:55,950 - app.db.init_db - INFO - 所有模型导入成功
2025-07-30 14:20:55,984 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-07-30 14:20:55,991 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 14:20:56,064 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.7%, CPU使用率 100.0%
2025-07-30 14:20:56,064 - app.db.init_db - INFO - 所有模型导入成功
2025-07-30 14:20:56,094 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-07-30 14:20:56,125 - app.db.init_db - INFO - 正在运行数据库迁移...
2025-07-30 14:20:56,133 - app.db.init_db - INFO - 正在检查并更新数据库表结构...
2025-07-30 14:20:56,247 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-30 14:20:56,331 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alerts")
2025-07-30 14:20:56,619 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:20:56,810 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_rules")
2025-07-30 14:20:56,860 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:20:57,019 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_channels")
2025-07-30 14:20:57,259 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:20:57,366 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-30 14:20:57,503 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:20:57,550 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_records")
2025-07-30 14:20:57,568 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:20:57,618 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_overviews")
2025-07-30 14:20:57,654 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:20:57,724 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medical_records")
2025-07-30 14:20:57,742 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:20:57,776 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("inpatient_records")
2025-07-30 14:20:57,852 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:20:57,906 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("surgery_records")
2025-07-30 14:20:57,927 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:20:57,993 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_reports")
2025-07-30 14:20:58,117 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:20:58,454 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_report_items")
2025-07-30 14:20:58,634 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:20:58,797 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_reports")
2025-07-30 14:20:58,851 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:20:58,894 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("examination_reports")
2025-07-30 14:20:58,920 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:20:59,034 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("documents")
2025-07-30 14:20:59,123 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:20:59,273 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("other_records")
2025-07-30 14:20:59,401 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:20:59,617 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("operation_logs")
2025-07-30 14:20:59,872 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:21:00,003 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("follow_up_records")
2025-07-30 14:21:00,146 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:21:00,242 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_diaries")
2025-07-30 14:21:00,350 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:21:00,407 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("registration_records")
2025-07-30 14:21:00,482 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:21:00,488 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("prescription_records")
2025-07-30 14:21:00,559 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:21:00,696 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("laboratory_records")
2025-07-30 14:21:00,763 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:21:01,047 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_records")
2025-07-30 14:21:01,151 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:21:01,416 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medications")
2025-07-30 14:21:01,642 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:21:01,993 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medication_usages")
2025-07-30 14:21:02,244 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:21:02,483 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("role_applications")
2025-07-30 14:21:02,663 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:21:02,768 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessments")
2025-07-30 14:21:02,837 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:21:02,885 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_items")
2025-07-30 14:21:02,973 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:21:03,052 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_responses")
2025-07-30 14:21:03,154 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:21:03,319 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_templates")
2025-07-30 14:21:03,533 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:21:03,662 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_template_questions")
2025-07-30 14:21:03,805 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:21:03,855 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaires")
2025-07-30 14:21:03,900 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:21:03,934 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_items")
2025-07-30 14:21:04,019 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:21:04,231 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_responses")
2025-07-30 14:21:04,593 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:21:04,768 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_answers")
2025-07-30 14:21:04,815 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:21:04,830 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_templates")
2025-07-30 14:21:04,887 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:21:04,950 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_template_questions")
2025-07-30 14:21:05,076 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:21:05,202 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_results")
2025-07-30 14:21:05,284 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:21:05,390 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_results")
2025-07-30 14:21:05,470 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:21:05,691 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("report_templates")
2025-07-30 14:21:05,955 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:21:06,178 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_distributions")
2025-07-30 14:21:06,284 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:21:06,385 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_distributions")
2025-07-30 14:21:06,789 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:21:07,080 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("service_stats")
2025-07-30 14:21:07,240 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 14:21:07,463 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-30 14:21:07,663 - app.db.init_db - INFO - 数据库表结构检查和更新完成
2025-07-30 14:21:07,881 - app.db.init_db - INFO - 模型关系初始化完成
2025-07-30 14:21:08,021 - app.db.init_db - INFO - 模型关系设置完成
2025-07-30 14:21:08,202 - main - INFO - 数据库初始化完成（强制重建）
2025-07-30 14:21:08,312 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 14:21:08,438 - main - INFO - 数据库连接正常
2025-07-30 14:21:08,646 - main - INFO - 开始初始化模板数据
2025-07-30 14:21:08,846 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 14:21:11,375 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.3%, CPU使用率 100.0%
2025-07-30 14:21:11,565 - app.db.init_templates - INFO - 开始初始化评估量表模板...
2025-07-30 14:21:11,733 - app.db.init_templates - INFO - 成功初始化 5 个评估量表模板
2025-07-30 14:21:12,022 - app.db.init_templates - INFO - 开始初始化调查问卷模板...
2025-07-30 14:21:12,486 - app.db.init_templates - INFO - 成功初始化 5 个调查问卷模板
2025-07-30 14:21:12,490 - main - INFO - 模板数据初始化完成
2025-07-30 14:21:12,495 - main - INFO - 数据库表结构已经标准化，不需要迁移
2025-07-30 14:21:12,789 - main - INFO - 应用启动完成
2025-07-30 14:21:27,097 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 100.0%
2025-07-30 14:21:42,583 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 100.0%
2025-07-30 14:21:47,918 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 67.8%, 磁盘: 94.3%
2025-07-30 14:21:57,713 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.9%, CPU使用率 67.9%
2025-07-30 14:22:02,516 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-30 14:22:02,517 - main - INFO - 请求没有认证头部
2025-07-30 14:22:02,518 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 14:22:02,520 - main - INFO - --- 请求结束: 200 ---

2025-07-30 14:22:04,559 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 14:22:04,560 - main - INFO - 请求没有认证头部
2025-07-30 14:22:04,561 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 14:22:04,563 - app.core.db_connection - DEBUG - 当前线程ID: 2244
2025-07-30 14:22:04,564 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-30 14:22:04,566 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-30 14:22:04,567 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-30 14:22:04,568 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 14:22:04,569 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-30 14:22:05,417 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-30 14:22:05,420 - main - INFO - --- 请求结束: 200 ---

2025-07-30 14:22:12,819 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.8%, CPU使用率 57.1%
2025-07-30 14:22:27,924 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.7%, CPU使用率 7.1%
2025-07-30 14:22:43,029 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.5%, CPU使用率 4.2%
2025-07-30 14:22:49,019 - health_monitor - DEBUG - 系统指标 - CPU: 10.9%, 内存: 68.5%, 磁盘: 94.3%
2025-07-30 14:22:58,134 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.5%, CPU使用率 32.1%
2025-07-30 14:23:13,238 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.5%, CPU使用率 20.0%
2025-07-30 14:23:28,343 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.5%, CPU使用率 4.2%
2025-07-30 14:23:43,452 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.2%, CPU使用率 100.0%
2025-07-30 14:23:50,043 - health_monitor - DEBUG - 系统指标 - CPU: 28.7%, 内存: 69.8%, 磁盘: 94.3%
2025-07-30 14:23:58,562 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.8%, CPU使用率 37.5%
2025-07-30 14:24:13,708 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.1%, CPU使用率 73.5%
2025-07-30 14:24:28,816 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.1%, CPU使用率 46.4%
2025-07-30 14:24:44,240 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.9%, CPU使用率 100.0%
2025-07-30 14:24:51,067 - health_monitor - DEBUG - 系统指标 - CPU: 32.0%, 内存: 67.3%, 磁盘: 94.3%
2025-07-30 14:24:59,700 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 76.3%
2025-07-30 14:25:14,816 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.1%, CPU使用率 28.6%
2025-07-30 14:25:30,065 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.4%, CPU使用率 95.9%
2025-07-30 14:25:45,182 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.3%, CPU使用率 89.7%
2025-07-30 14:25:52,155 - health_monitor - DEBUG - 系统指标 - CPU: 81.4%, 内存: 68.5%, 磁盘: 94.3%
2025-07-30 14:26:00,291 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.8%, CPU使用率 73.1%
2025-07-30 14:26:15,424 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.2%, CPU使用率 96.7%
2025-07-30 14:26:30,541 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.0%, CPU使用率 96.4%
2025-07-30 14:26:45,710 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.2%, CPU使用率 96.6%
2025-07-30 14:26:47,310 - alert_manager - WARNING - 触发告警: disk_free_space, 当前值: 0.0, 阈值: 1024
2025-07-30 14:26:53,184 - health_monitor - DEBUG - 系统指标 - CPU: 88.3%, 内存: 67.8%, 磁盘: 94.3%
2025-07-30 14:27:00,816 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 39.3%
2025-07-30 14:27:15,923 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.5%, CPU使用率 62.5%
2025-07-30 14:27:31,035 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.4%, CPU使用率 85.7%
2025-07-30 14:27:46,182 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.4%, CPU使用率 100.0%
2025-07-30 14:27:47,520 - alert_manager - WARNING - 触发告警: disk_usage, 当前值: 94.3, 阈值: 90
2025-07-30 14:27:54,218 - health_monitor - DEBUG - 系统指标 - CPU: 88.3%, 内存: 67.7%, 磁盘: 94.3%
2025-07-30 14:28:01,338 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.2%, CPU使用率 70.8%
2025-07-30 14:28:16,720 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.5%, CPU使用率 100.0%
2025-07-30 14:28:32,429 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.5%, CPU使用率 100.0%
2025-07-30 14:28:47,608 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.0%, CPU使用率 100.0%
2025-07-30 14:28:55,430 - health_monitor - DEBUG - 系统指标 - CPU: 92.5%, 内存: 69.2%, 磁盘: 94.3%
2025-07-30 14:29:02,777 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.2%, CPU使用率 100.0%
2025-07-30 14:29:17,927 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 100.0%
2025-07-30 14:29:33,305 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.2%, CPU使用率 100.0%
2025-07-30 14:29:48,646 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.4%, CPU使用率 100.0%
2025-07-30 14:29:56,483 - health_monitor - DEBUG - 系统指标 - CPU: 83.6%, 内存: 70.7%, 磁盘: 94.4%
2025-07-30 14:30:03,766 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.6%, CPU使用率 96.0%
2025-07-30 14:30:18,885 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.0%, CPU使用率 96.4%
2025-07-30 14:30:33,996 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.4%, CPU使用率 77.8%
2025-07-30 14:30:49,131 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.2%, CPU使用率 82.1%
2025-07-30 14:30:57,760 - health_monitor - DEBUG - 系统指标 - CPU: 97.4%, 内存: 68.7%, 磁盘: 94.4%
2025-07-30 14:31:04,268 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.7%, CPU使用率 100.0%
2025-07-30 14:31:19,388 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.4%, CPU使用率 100.0%
2025-07-30 14:31:34,505 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.9%, CPU使用率 95.8%
2025-07-30 14:31:49,722 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.3%, CPU使用率 70.4%
2025-07-30 14:31:59,056 - health_monitor - DEBUG - 系统指标 - CPU: 97.9%, 内存: 71.4%, 磁盘: 94.3%
2025-07-30 14:32:04,881 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.2%, CPU使用率 97.4%
2025-07-30 14:32:20,032 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.9%, CPU使用率 76.0%
2025-07-30 14:32:35,141 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 50.0%
2025-07-30 14:32:50,413 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.5%, CPU使用率 100.0%
2025-07-30 14:33:01,064 - health_monitor - DEBUG - 系统指标 - CPU: 80.5%, 内存: 68.7%, 磁盘: 94.3%
2025-07-30 14:33:05,540 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.1%, CPU使用率 100.0%
2025-07-30 14:33:20,839 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.0%, CPU使用率 100.0%
2025-07-30 14:33:36,103 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.3%, CPU使用率 96.7%
2025-07-30 14:33:51,208 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.8%, CPU使用率 100.0%
2025-07-30 14:34:02,108 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 65.2%, 磁盘: 94.3%
2025-07-30 14:34:06,319 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.7%, CPU使用率 100.0%
2025-07-30 14:34:21,439 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.0%, CPU使用率 100.0%
2025-07-30 14:34:36,547 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.5%, CPU使用率 100.0%
2025-07-30 14:34:52,075 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.2%, CPU使用率 100.0%
2025-07-30 14:35:03,378 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 67.2%, 磁盘: 94.4%
2025-07-30 14:35:07,478 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.1%, CPU使用率 100.0%
2025-07-30 14:35:23,160 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.0%, CPU使用率 100.0%
2025-07-30 14:35:38,721 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.1%, CPU使用率 100.0%
2025-07-30 14:35:53,988 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.8%, CPU使用率 100.0%
2025-07-30 14:36:04,536 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 71.0%, 磁盘: 94.4%
2025-07-30 14:36:09,665 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.8%, CPU使用率 88.5%
2025-07-30 14:36:24,772 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.9%, CPU使用率 75.0%
2025-07-30 14:36:40,184 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 100.0%
2025-07-30 14:36:55,445 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.8%, CPU使用率 100.0%
2025-07-30 14:37:05,629 - health_monitor - DEBUG - 系统指标 - CPU: 98.8%, 内存: 72.0%, 磁盘: 94.4%
2025-07-30 14:37:10,554 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.9%, CPU使用率 75.0%
2025-07-30 14:37:25,658 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.0%, CPU使用率 46.4%
2025-07-30 14:37:41,027 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.3%, CPU使用率 100.0%
2025-07-30 14:37:56,501 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.6%, CPU使用率 97.6%
2025-07-30 14:38:06,812 - health_monitor - DEBUG - 系统指标 - CPU: 94.7%, 内存: 69.0%, 磁盘: 94.4%
2025-07-30 14:38:11,618 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.8%, CPU使用率 89.3%
2025-07-30 14:38:26,728 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.8%, CPU使用率 82.1%
2025-07-30 14:38:42,336 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.2%, CPU使用率 100.0%
2025-07-30 14:38:57,595 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.5%, CPU使用率 100.0%
2025-07-30 14:39:07,939 - health_monitor - DEBUG - 系统指标 - CPU: 94.5%, 内存: 70.6%, 磁盘: 94.4%
2025-07-30 14:39:12,874 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.9%, CPU使用率 100.0%
2025-07-30 14:39:28,290 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.9%, CPU使用率 100.0%
2025-07-30 14:39:43,610 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.5%, CPU使用率 92.3%
2025-07-30 14:39:47,850 - alert_manager - WARNING - 触发告警: cpu_usage, 当前值: 94.5, 阈值: 90
2025-07-30 14:39:59,111 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.3%, CPU使用率 100.0%
2025-07-30 14:40:09,221 - health_monitor - DEBUG - 系统指标 - CPU: 98.7%, 内存: 71.2%, 磁盘: 94.4%
2025-07-30 14:40:14,647 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.4%, CPU使用率 100.0%
2025-07-30 14:40:30,729 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.1%, CPU使用率 99.1%
2025-07-30 14:40:46,142 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.7%, CPU使用率 100.0%
2025-07-30 14:41:01,380 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.9%, CPU使用率 98.0%
2025-07-30 14:41:10,627 - health_monitor - DEBUG - 系统指标 - CPU: 94.0%, 内存: 70.6%, 磁盘: 94.4%
2025-07-30 14:41:16,524 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.9%, CPU使用率 83.3%
2025-07-30 14:41:31,630 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.9%, CPU使用率 64.3%
2025-07-30 14:41:47,049 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.7%, CPU使用率 100.0%
2025-07-30 14:42:02,539 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.7%, CPU使用率 95.2%
2025-07-30 14:42:11,799 - health_monitor - DEBUG - 系统指标 - CPU: 99.2%, 内存: 71.8%, 磁盘: 94.4%
2025-07-30 14:42:17,813 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.0%, CPU使用率 100.0%
2025-07-30 14:42:33,131 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.3%, CPU使用率 100.0%
2025-07-30 14:42:48,252 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.9%, CPU使用率 78.6%
2025-07-30 14:43:03,677 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.7%, CPU使用率 100.0%
2025-07-30 14:43:12,840 - health_monitor - DEBUG - 系统指标 - CPU: 65.4%, 内存: 75.3%, 磁盘: 94.4%
2025-07-30 14:43:18,797 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.3%, CPU使用率 100.0%
2025-07-30 14:43:33,912 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.9%, CPU使用率 96.6%
2025-07-30 14:43:49,202 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.6%, CPU使用率 100.0%
2025-07-30 14:44:04,336 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.1%, CPU使用率 96.4%
2025-07-30 14:44:14,024 - health_monitor - DEBUG - 系统指标 - CPU: 85.9%, 内存: 73.0%, 磁盘: 94.4%
2025-07-30 14:44:19,708 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.4%, CPU使用率 100.0%
2025-07-30 14:44:34,882 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.6%, CPU使用率 96.9%
2025-07-30 14:44:50,189 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.9%, CPU使用率 100.0%
2025-07-30 14:45:05,464 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.7%, CPU使用率 100.0%
2025-07-30 14:45:15,229 - health_monitor - DEBUG - 系统指标 - CPU: 76.8%, 内存: 75.1%, 磁盘: 94.4%
2025-07-30 14:45:20,579 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.9%, CPU使用率 58.3%
2025-07-30 14:45:36,128 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.9%, CPU使用率 100.0%
2025-07-30 14:45:51,236 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.4%, CPU使用率 33.3%
2025-07-30 14:46:06,378 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.3%, CPU使用率 100.0%
2025-07-30 14:46:16,736 - health_monitor - DEBUG - 系统指标 - CPU: 88.0%, 内存: 72.7%, 磁盘: 94.4%
2025-07-30 14:46:21,571 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.0%, CPU使用率 100.0%
2025-07-30 14:46:36,733 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.6%, CPU使用率 100.0%
2025-07-30 14:46:51,855 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.7%, CPU使用率 92.9%
2025-07-30 14:47:07,092 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.5%, CPU使用率 96.9%
2025-07-30 14:47:17,795 - health_monitor - DEBUG - 系统指标 - CPU: 86.1%, 内存: 72.5%, 磁盘: 94.4%
2025-07-30 14:47:22,197 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.3%, CPU使用率 79.2%
2025-07-30 14:47:37,307 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.2%, CPU使用率 70.8%
2025-07-30 14:47:52,472 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.0%, CPU使用率 96.9%
2025-07-30 14:48:07,585 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.5%, CPU使用率 82.8%
2025-07-30 14:48:18,967 - health_monitor - DEBUG - 系统指标 - CPU: 67.8%, 内存: 73.7%, 磁盘: 94.4%
2025-07-30 14:48:22,702 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.1%, CPU使用率 92.9%
2025-07-30 14:48:37,985 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.7%, CPU使用率 100.0%
2025-07-30 14:48:53,112 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.0%, CPU使用率 100.0%
2025-07-30 14:49:08,219 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.5%, CPU使用率 95.8%
2025-07-30 14:49:20,148 - health_monitor - DEBUG - 系统指标 - CPU: 88.7%, 内存: 73.3%, 磁盘: 94.4%
2025-07-30 14:49:23,664 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.7%, CPU使用率 96.2%
2025-07-30 14:49:38,771 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.8%, CPU使用率 71.4%
2025-07-30 14:49:53,878 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.6%, CPU使用率 37.0%
2025-07-30 14:50:08,984 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.7%, CPU使用率 79.2%
2025-07-30 14:50:21,455 - health_monitor - DEBUG - 系统指标 - CPU: 97.9%, 内存: 71.5%, 磁盘: 94.4%
2025-07-30 14:50:24,190 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.7%, CPU使用率 83.3%
2025-07-30 14:50:39,297 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.5%, CPU使用率 50.0%
2025-07-30 14:50:54,404 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.0%, CPU使用率 58.3%
2025-07-30 14:51:09,513 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.0%, CPU使用率 91.7%
2025-07-30 14:51:22,510 - health_monitor - DEBUG - 系统指标 - CPU: 84.0%, 内存: 72.5%, 磁盘: 94.4%
2025-07-30 14:51:24,655 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.0%, CPU使用率 75.0%
2025-07-30 14:51:39,762 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.2%, CPU使用率 45.8%
2025-07-30 14:51:55,126 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.3%, CPU使用率 83.0%
2025-07-30 14:52:10,353 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.6%, CPU使用率 96.6%
2025-07-30 14:52:23,804 - health_monitor - DEBUG - 系统指标 - CPU: 79.6%, 内存: 74.2%, 磁盘: 94.4%
2025-07-30 14:52:25,537 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.9%, CPU使用率 100.0%
2025-07-30 14:52:40,647 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.4%, CPU使用率 82.1%
2025-07-30 14:52:55,772 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.8%, CPU使用率 92.9%
2025-07-30 14:53:11,136 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.9%, CPU使用率 100.0%
2025-07-30 14:53:24,846 - health_monitor - DEBUG - 系统指标 - CPU: 70.8%, 内存: 71.6%, 磁盘: 94.5%
2025-07-30 14:53:26,247 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.6%, CPU使用率 67.9%
2025-07-30 14:53:41,357 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.5%, CPU使用率 50.0%
2025-07-30 14:53:56,507 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.1%, CPU使用率 100.0%
2025-07-30 14:54:11,842 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.6%, CPU使用率 100.0%
2025-07-30 14:54:25,896 - health_monitor - DEBUG - 系统指标 - CPU: 79.2%, 内存: 71.6%, 磁盘: 94.4%
2025-07-30 14:54:27,271 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.1%, CPU使用率 100.0%
2025-07-30 14:54:42,476 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.2%, CPU使用率 100.0%
2025-07-30 14:54:57,598 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.8%, CPU使用率 85.7%
2025-07-30 14:55:12,705 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.9%, CPU使用率 66.7%
2025-07-30 14:55:27,149 - health_monitor - DEBUG - 系统指标 - CPU: 72.2%, 内存: 72.8%, 磁盘: 94.4%
2025-07-30 14:55:27,975 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.2%, CPU使用率 100.0%
2025-07-30 14:55:43,318 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.9%, CPU使用率 97.8%
2025-07-30 14:55:58,811 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.7%, CPU使用率 100.0%
2025-07-30 14:56:13,929 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.4%, CPU使用率 46.4%
2025-07-30 14:56:28,381 - health_monitor - DEBUG - 系统指标 - CPU: 92.6%, 内存: 71.2%, 磁盘: 94.4%
2025-07-30 14:56:29,037 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.5%, CPU使用率 57.1%
2025-07-30 14:56:44,586 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.2%, CPU使用率 98.7%
2025-07-30 14:56:59,732 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.3%, CPU使用率 66.7%
2025-07-30 14:57:14,842 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.0%, CPU使用率 41.7%
2025-07-30 14:57:29,409 - health_monitor - DEBUG - 系统指标 - CPU: 75.2%, 内存: 71.9%, 磁盘: 94.4%
2025-07-30 14:57:29,949 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.1%, CPU使用率 100.0%
2025-07-30 14:57:45,409 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.8%, CPU使用率 100.0%
2025-07-30 14:58:00,515 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.1%, CPU使用率 66.7%
2025-07-30 14:58:15,622 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.6%, CPU使用率 79.2%
2025-07-30 14:58:30,445 - health_monitor - DEBUG - 系统指标 - CPU: 88.8%, 内存: 70.3%, 磁盘: 94.5%
2025-07-30 14:58:30,728 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.2%, CPU使用率 25.0%
2025-07-30 14:58:46,066 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.2%, CPU使用率 100.0%
2025-07-30 14:59:01,304 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.0%, CPU使用率 100.0%
2025-07-30 14:59:16,432 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.6%, CPU使用率 100.0%
2025-07-30 14:59:31,482 - health_monitor - DEBUG - 系统指标 - CPU: 93.0%, 内存: 69.2%, 磁盘: 94.4%
2025-07-30 14:59:31,539 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.2%, CPU使用率 67.9%
2025-07-30 14:59:46,826 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.7%, CPU使用率 100.0%
2025-07-30 15:00:02,028 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.7%, CPU使用率 92.1%
2025-07-30 15:00:17,333 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.1%, CPU使用率 100.0%
2025-07-30 15:00:32,443 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.2%, CPU使用率 59.3%
2025-07-30 15:00:32,510 - health_monitor - DEBUG - 系统指标 - CPU: 79.0%, 内存: 71.2%, 磁盘: 94.4%
2025-07-30 15:00:47,627 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.8%, CPU使用率 84.2%
2025-07-30 15:01:02,835 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.9%, CPU使用率 100.0%
2025-07-30 15:01:17,942 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.8%, CPU使用率 78.6%
2025-07-30 15:01:33,170 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.5%, CPU使用率 100.0%
2025-07-30 15:01:33,544 - health_monitor - DEBUG - 系统指标 - CPU: 81.9%, 内存: 69.9%, 磁盘: 94.4%
2025-07-30 15:01:48,391 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.0%, CPU使用率 100.0%
2025-07-30 15:02:03,900 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.7%, CPU使用率 98.6%
2025-07-30 15:02:19,490 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.9%, CPU使用率 100.0%
2025-07-30 15:02:34,630 - health_monitor - DEBUG - 系统指标 - CPU: 90.5%, 内存: 71.7%, 磁盘: 94.4%
2025-07-30 15:02:34,704 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.5%, CPU使用率 100.0%
2025-07-30 15:02:50,069 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.6%, CPU使用率 98.2%
2025-07-30 15:03:05,349 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.7%, CPU使用率 97.4%
2025-07-30 15:03:20,531 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.0%, CPU使用率 100.0%
2025-07-30 15:03:35,654 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.6%, CPU使用率 100.0%
2025-07-30 15:03:35,758 - health_monitor - DEBUG - 系统指标 - CPU: 98.5%, 内存: 68.1%, 磁盘: 94.4%
2025-07-30 15:03:50,859 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.8%, CPU使用率 100.0%
2025-07-30 15:04:06,095 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.6%, CPU使用率 100.0%
2025-07-30 15:04:21,235 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.4%, CPU使用率 87.5%
2025-07-30 15:04:36,364 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.6%, CPU使用率 82.1%
2025-07-30 15:04:36,908 - health_monitor - DEBUG - 系统指标 - CPU: 94.4%, 内存: 68.6%, 磁盘: 94.4%
2025-07-30 15:04:51,656 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.2%, CPU使用率 100.0%
2025-07-30 15:05:06,843 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.6%, CPU使用率 96.2%
2025-07-30 15:05:22,008 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.6%, CPU使用率 100.0%
2025-07-30 15:05:37,116 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.1%, CPU使用率 66.7%
2025-07-30 15:05:37,935 - health_monitor - DEBUG - 系统指标 - CPU: 88.1%, 内存: 69.6%, 磁盘: 94.4%
2025-07-30 15:05:52,337 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.6%, CPU使用率 100.0%
2025-07-30 15:06:07,448 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.2%, CPU使用率 83.3%
2025-07-30 15:06:22,557 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 88.0%
2025-07-30 15:06:37,712 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.9%, CPU使用率 97.2%
2025-07-30 15:06:38,958 - health_monitor - DEBUG - 系统指标 - CPU: 75.1%, 内存: 66.2%, 磁盘: 94.4%
2025-07-30 15:06:53,103 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.1%, CPU使用率 100.0%
2025-07-30 15:07:08,655 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.0%, CPU使用率 100.0%
2025-07-30 15:07:23,839 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.3%, CPU使用率 100.0%
2025-07-30 15:07:38,976 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.6%, CPU使用率 96.4%
2025-07-30 15:07:40,147 - health_monitor - DEBUG - 系统指标 - CPU: 90.1%, 内存: 64.6%, 磁盘: 94.4%
2025-07-30 15:07:54,086 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.1%, CPU使用率 71.4%
2025-07-30 15:08:09,190 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.9%, CPU使用率 39.3%
2025-07-30 15:08:24,294 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.6%, CPU使用率 54.2%
2025-07-30 15:08:39,406 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.5%, CPU使用率 56.0%
2025-07-30 15:08:41,169 - health_monitor - DEBUG - 系统指标 - CPU: 39.8%, 内存: 61.4%, 磁盘: 94.4%
2025-07-30 15:08:54,511 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.4%, CPU使用率 21.4%
2025-07-30 15:09:08,747 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-30 15:09:08,749 - main - INFO - 请求没有认证头部
2025-07-30 15:09:08,749 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 15:09:08,751 - main - INFO - --- 请求结束: 200 ---

2025-07-30 15:09:09,615 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.1%, CPU使用率 0.0%
2025-07-30 15:09:10,769 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 15:09:10,776 - main - INFO - 请求没有认证头部
2025-07-30 15:09:10,777 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 15:09:10,779 - app.core.db_connection - DEBUG - 当前线程ID: 2244
2025-07-30 15:09:10,780 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-30 15:09:10,781 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-30 15:09:10,782 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-30 15:09:10,783 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 15:09:10,783 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-30 15:09:11,671 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-30 15:09:11,673 - main - INFO - --- 请求结束: 200 ---

2025-07-30 15:09:24,721 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.1%, CPU使用率 28.0%
2025-07-30 15:09:39,827 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 16.7%
2025-07-30 15:09:42,193 - health_monitor - DEBUG - 系统指标 - CPU: 37.1%, 内存: 62.8%, 磁盘: 94.4%
2025-07-30 15:09:54,933 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.3%, CPU使用率 45.8%
2025-07-30 15:10:10,038 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.1%, CPU使用率 26.9%
2025-07-30 15:10:25,143 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.0%, CPU使用率 48.1%
2025-07-30 15:10:40,247 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.3%, CPU使用率 41.7%
2025-07-30 15:10:43,214 - health_monitor - DEBUG - 系统指标 - CPU: 44.9%, 内存: 61.2%, 磁盘: 94.4%
2025-07-30 15:10:55,352 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.2%, CPU使用率 39.3%
2025-07-30 15:11:10,458 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.2%, CPU使用率 22.2%
2025-07-30 15:11:25,562 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.1%, CPU使用率 52.2%
2025-07-30 15:11:40,667 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.3%, CPU使用率 34.6%
2025-07-30 15:11:44,237 - health_monitor - DEBUG - 系统指标 - CPU: 38.5%, 内存: 61.3%, 磁盘: 94.4%
2025-07-30 15:11:55,790 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.3%, CPU使用率 46.7%
2025-07-30 15:12:10,899 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.1%, CPU使用率 28.6%
2025-07-30 15:12:26,004 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.2%, CPU使用率 21.4%
2025-07-30 15:12:41,109 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.3%, CPU使用率 4.2%
2025-07-30 15:12:45,403 - health_monitor - DEBUG - 系统指标 - CPU: 94.6%, 内存: 61.6%, 磁盘: 94.4%
2025-07-30 15:12:53,233 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-30 15:12:53,236 - main - INFO - 请求没有认证头部
2025-07-30 15:12:53,237 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 15:12:53,239 - main - INFO - --- 请求结束: 200 ---

2025-07-30 15:12:55,269 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 15:12:55,270 - main - INFO - 请求没有认证头部
2025-07-30 15:12:55,271 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 15:12:55,273 - app.core.db_connection - DEBUG - 当前线程ID: 2244
2025-07-30 15:12:55,273 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-30 15:12:55,275 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-30 15:12:55,276 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-30 15:12:55,277 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 15:12:55,277 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-30 15:12:56,026 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-30 15:12:56,028 - main - INFO - --- 请求结束: 200 ---

2025-07-30 15:12:56,215 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.4%, CPU使用率 75.0%
2025-07-30 15:13:11,326 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.5%, CPU使用率 87.5%
2025-07-30 15:13:26,431 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.2%, CPU使用率 0.0%
2025-07-30 15:13:41,537 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.0%, CPU使用率 24.0%
2025-07-30 15:13:46,431 - health_monitor - DEBUG - 系统指标 - CPU: 18.4%, 内存: 63.0%, 磁盘: 94.4%
2025-07-30 15:13:56,643 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.1%, CPU使用率 19.2%
2025-07-30 15:14:11,751 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.0%, CPU使用率 29.2%
2025-07-30 15:14:26,856 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.1%, CPU使用率 16.7%
2025-07-30 15:14:41,963 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.0%, CPU使用率 8.7%
2025-07-30 15:14:47,452 - health_monitor - DEBUG - 系统指标 - CPU: 25.3%, 内存: 63.1%, 磁盘: 94.4%
2025-07-30 15:14:57,067 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.1%, CPU使用率 7.7%
2025-07-30 15:15:12,173 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.0%, CPU使用率 25.0%
2025-07-30 15:15:27,279 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.1%, CPU使用率 4.2%
2025-07-30 15:15:42,383 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.1%, CPU使用率 3.6%
2025-07-30 15:15:48,481 - health_monitor - DEBUG - 系统指标 - CPU: 50.8%, 内存: 65.4%, 磁盘: 94.4%
2025-07-30 15:15:57,488 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.9%, CPU使用率 29.6%
2025-07-30 15:16:12,592 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.8%, CPU使用率 8.3%
2025-07-30 15:16:27,765 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 76.5%
2025-07-30 15:16:42,876 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.9%, CPU使用率 37.5%
2025-07-30 15:16:49,512 - health_monitor - DEBUG - 系统指标 - CPU: 79.0%, 内存: 67.3%, 磁盘: 94.4%
2025-07-30 15:16:57,983 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.4%, CPU使用率 68.0%
2025-07-30 15:17:13,090 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.3%, CPU使用率 87.5%
2025-07-30 15:17:28,197 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.3%, CPU使用率 72.0%
2025-07-30 15:17:43,401 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.2%, CPU使用率 92.7%
2025-07-30 15:17:50,626 - health_monitor - DEBUG - 系统指标 - CPU: 76.8%, 内存: 68.6%, 磁盘: 94.5%
2025-07-30 15:17:58,506 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.5%, CPU使用率 26.9%
2025-07-30 15:18:13,627 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.5%, CPU使用率 79.3%
2025-07-30 15:18:28,847 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 100.0%
2025-07-30 15:18:44,004 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.8%, CPU使用率 82.8%
2025-07-30 15:18:51,751 - health_monitor - DEBUG - 系统指标 - CPU: 86.1%, 内存: 68.0%, 磁盘: 94.4%
2025-07-30 15:18:59,112 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.5%, CPU使用率 67.9%
2025-07-30 15:19:14,227 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 60.7%
2025-07-30 15:19:29,343 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 96.0%
2025-07-30 15:19:44,493 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.8%, CPU使用率 87.1%
2025-07-30 15:19:52,837 - health_monitor - DEBUG - 系统指标 - CPU: 99.2%, 内存: 66.0%, 磁盘: 94.4%
2025-07-30 15:19:59,938 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.4%, CPU使用率 100.0%
2025-07-30 15:20:15,047 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 58.3%
2025-07-30 15:20:30,183 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.1%, CPU使用率 96.4%
2025-07-30 15:20:45,386 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.6%, CPU使用率 100.0%
2025-07-30 15:20:53,911 - health_monitor - DEBUG - 系统指标 - CPU: 99.2%, 内存: 69.5%, 磁盘: 94.4%
2025-07-30 15:21:00,528 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.0%, CPU使用率 100.0%
2025-07-30 15:21:15,691 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.0%, CPU使用率 77.8%
2025-07-30 15:21:30,892 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.7%, CPU使用率 97.1%
2025-07-30 15:21:46,005 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.9%, CPU使用率 71.4%
2025-07-30 15:21:55,463 - health_monitor - DEBUG - 系统指标 - CPU: 97.5%, 内存: 66.7%, 磁盘: 94.4%
2025-07-30 15:22:01,338 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 100.0%
2025-07-30 15:22:16,645 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 57.1%
2025-07-30 15:22:32,020 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.9%, CPU使用率 100.0%
2025-07-30 15:22:47,189 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.2%, CPU使用率 87.9%
2025-07-30 15:22:56,498 - health_monitor - DEBUG - 系统指标 - CPU: 96.2%, 内存: 69.6%, 磁盘: 94.5%
2025-07-30 15:23:02,681 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.7%, CPU使用率 100.0%
2025-07-30 15:23:18,280 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 100.0%
2025-07-30 15:23:34,047 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.1%, CPU使用率 100.0%
2025-07-30 15:23:49,157 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.1%, CPU使用率 45.8%
2025-07-30 15:23:57,643 - health_monitor - DEBUG - 系统指标 - CPU: 56.6%, 内存: 68.6%, 磁盘: 94.5%
2025-07-30 15:24:04,264 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.1%, CPU使用率 79.2%
2025-07-30 15:24:19,372 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.7%, CPU使用率 87.5%
2025-07-30 15:24:34,486 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 83.3%
2025-07-30 15:24:49,592 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.2%, CPU使用率 45.8%
2025-07-30 15:24:58,953 - health_monitor - DEBUG - 系统指标 - CPU: 81.6%, 内存: 67.9%, 磁盘: 94.5%
2025-07-30 15:25:04,905 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.2%, CPU使用率 90.2%
2025-07-30 15:25:20,677 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.0%, CPU使用率 100.0%
2025-07-30 15:25:35,798 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.7%, CPU使用率 100.0%
2025-07-30 15:25:50,910 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.1%, CPU使用率 85.7%
2025-07-30 15:26:00,215 - health_monitor - DEBUG - 系统指标 - CPU: 86.1%, 内存: 69.1%, 磁盘: 94.5%
2025-07-30 15:26:06,383 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.0%, CPU使用率 100.0%
2025-07-30 15:26:21,825 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 100.0%
2025-07-30 15:26:37,104 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.5%, CPU使用率 100.0%
2025-07-30 15:26:52,385 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.6%, CPU使用率 94.2%
2025-07-30 15:27:01,430 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 68.4%, 磁盘: 94.5%
2025-07-30 15:27:08,017 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.4%, CPU使用率 100.0%
2025-07-30 15:27:23,728 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.4%, CPU使用率 100.0%
2025-07-30 15:27:39,100 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.7%, CPU使用率 100.0%
2025-07-30 15:27:54,223 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.7%, CPU使用率 100.0%
2025-07-30 15:28:03,023 - health_monitor - DEBUG - 系统指标 - CPU: 95.3%, 内存: 70.5%, 磁盘: 94.5%
2025-07-30 15:28:09,787 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.6%, CPU使用率 100.0%
2025-07-30 15:28:25,241 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.4%, CPU使用率 100.0%
2025-07-30 15:28:40,874 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.1%, CPU使用率 100.0%
2025-07-30 15:28:56,224 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.9%, CPU使用率 100.0%
2025-07-30 15:29:04,745 - health_monitor - DEBUG - 系统指标 - CPU: 99.2%, 内存: 72.2%, 磁盘: 94.5%
2025-07-30 15:29:11,782 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.3%, CPU使用率 100.0%
2025-07-30 15:29:27,344 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.1%, CPU使用率 100.0%
2025-07-30 15:29:42,623 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.2%, CPU使用率 100.0%
2025-07-30 15:29:57,859 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.8%, CPU使用率 100.0%
2025-07-30 15:30:06,131 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 69.6%, 磁盘: 94.5%
2025-07-30 15:30:13,794 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.1%, CPU使用率 100.0%
2025-07-30 15:30:28,952 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.4%, CPU使用率 41.7%
2025-07-30 15:30:44,057 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.1%, CPU使用率 58.3%
2025-07-30 15:30:59,317 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.9%, CPU使用率 100.0%
2025-07-30 15:31:07,339 - health_monitor - DEBUG - 系统指标 - CPU: 90.2%, 内存: 71.4%, 磁盘: 94.5%
2025-07-30 15:31:14,428 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.0%, CPU使用率 58.6%
2025-07-30 15:31:29,752 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.4%, CPU使用率 96.2%
2025-07-30 15:31:45,484 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.1%, CPU使用率 100.0%
2025-07-30 15:32:01,207 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.2%, CPU使用率 100.0%
2025-07-30 15:32:08,564 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 71.1%, 磁盘: 94.5%
2025-07-30 15:32:16,373 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.4%, CPU使用率 100.0%
2025-07-30 15:32:31,592 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.1%, CPU使用率 100.0%
2025-07-30 15:32:47,069 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.4%, CPU使用率 98.4%
2025-07-30 15:32:48,733 - alert_manager - WARNING - 触发告警: cpu_usage, 当前值: 100.0, 阈值: 90
2025-07-30 15:33:02,271 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.1%, CPU使用率 97.2%
2025-07-30 15:33:10,087 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 73.6%, 磁盘: 94.5%
2025-07-30 15:33:17,500 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.7%, CPU使用率 92.3%
2025-07-30 15:33:32,762 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.8%, CPU使用率 100.0%
2025-07-30 15:33:47,895 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.2%, CPU使用率 100.0%
2025-07-30 15:34:03,001 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.7%, CPU使用率 83.3%
2025-07-30 15:34:11,215 - health_monitor - DEBUG - 系统指标 - CPU: 87.7%, 内存: 73.9%, 磁盘: 94.5%
2025-07-30 15:34:18,289 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.2%, CPU使用率 71.8%
2025-07-30 15:34:33,396 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.7%, CPU使用率 46.4%
2025-07-30 15:34:48,503 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.1%, CPU使用率 57.1%
2025-07-30 15:35:03,609 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.6%, CPU使用率 83.3%
2025-07-30 15:35:12,266 - health_monitor - DEBUG - 系统指标 - CPU: 53.1%, 内存: 73.5%, 磁盘: 94.5%
2025-07-30 15:35:18,738 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.4%, CPU使用率 76.7%
2025-07-30 15:35:33,905 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.9%, CPU使用率 58.3%
2025-07-30 15:35:49,019 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.7%, CPU使用率 92.9%
2025-07-30 15:36:04,344 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.7%, CPU使用率 84.2%
2025-07-30 15:36:13,348 - health_monitor - DEBUG - 系统指标 - CPU: 79.4%, 内存: 75.0%, 磁盘: 94.5%
2025-07-30 15:36:19,524 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.8%, CPU使用率 60.7%
2025-07-30 15:36:34,947 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.9%, CPU使用率 100.0%
2025-07-30 15:36:50,053 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.5%, CPU使用率 46.4%
2025-07-30 15:37:05,158 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.4%, CPU使用率 89.3%
2025-07-30 15:37:14,377 - health_monitor - DEBUG - 系统指标 - CPU: 82.1%, 内存: 72.0%, 磁盘: 94.5%
2025-07-30 15:37:20,554 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.2%, CPU使用率 100.0%
2025-07-30 15:37:35,994 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.2%, CPU使用率 100.0%
2025-07-30 15:37:51,170 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.6%, CPU使用率 83.3%
2025-07-30 15:38:06,278 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.2%, CPU使用率 92.0%
2025-07-30 15:38:15,415 - health_monitor - DEBUG - 系统指标 - CPU: 84.4%, 内存: 74.5%, 磁盘: 94.5%
2025-07-30 15:38:21,470 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.1%, CPU使用率 100.0%
2025-07-30 15:38:36,578 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.1%, CPU使用率 61.5%
2025-07-30 15:38:51,685 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.1%, CPU使用率 83.3%
2025-07-30 15:39:06,792 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.7%, CPU使用率 52.0%
2025-07-30 15:39:16,632 - health_monitor - DEBUG - 系统指标 - CPU: 95.8%, 内存: 71.5%, 磁盘: 94.5%
2025-07-30 15:39:22,037 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.9%, CPU使用率 94.3%
2025-07-30 15:39:37,153 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.7%, CPU使用率 100.0%
2025-07-30 15:39:52,287 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.8%, CPU使用率 71.4%
2025-07-30 15:40:07,482 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.2%, CPU使用率 100.0%
2025-07-30 15:40:17,849 - health_monitor - DEBUG - 系统指标 - CPU: 71.9%, 内存: 71.3%, 磁盘: 94.5%
2025-07-30 15:40:22,589 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.2%, CPU使用率 75.0%
2025-07-30 15:40:37,696 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.6%, CPU使用率 82.1%
2025-07-30 15:40:52,804 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.1%, CPU使用率 78.6%
2025-07-30 15:41:08,080 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.3%, CPU使用率 100.0%
2025-07-30 15:41:19,159 - health_monitor - DEBUG - 系统指标 - CPU: 76.8%, 内存: 73.4%, 磁盘: 94.5%
2025-07-30 15:41:23,252 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.0%, CPU使用率 83.9%
2025-07-30 15:41:38,407 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.6%, CPU使用率 91.2%
2025-07-30 15:41:53,629 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.0%, CPU使用率 100.0%
2025-07-30 15:42:09,281 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.6%, CPU使用率 100.0%
2025-07-30 15:42:20,236 - health_monitor - DEBUG - 系统指标 - CPU: 71.0%, 内存: 70.4%, 磁盘: 94.5%
2025-07-30 15:42:24,670 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.4%, CPU使用率 100.0%
2025-07-30 15:42:39,865 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.5%, CPU使用率 87.1%
2025-07-30 15:42:54,977 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.5%, CPU使用率 89.3%
2025-07-30 15:43:10,368 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.1%, CPU使用率 95.2%
2025-07-30 15:43:21,474 - health_monitor - DEBUG - 系统指标 - CPU: 86.9%, 内存: 72.7%, 磁盘: 94.5%
2025-07-30 15:43:25,688 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.0%, CPU使用率 79.5%
2025-07-30 15:43:40,924 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.2%, CPU使用率 53.6%
2025-07-30 15:43:56,031 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.8%, CPU使用率 100.0%
2025-07-30 15:44:11,397 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.5%, CPU使用率 94.1%
2025-07-30 15:44:22,789 - health_monitor - DEBUG - 系统指标 - CPU: 73.3%, 内存: 69.3%, 磁盘: 94.5%
2025-07-30 15:44:26,673 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.5%, CPU使用率 71.4%
2025-07-30 15:44:41,788 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.9%, CPU使用率 57.1%
2025-07-30 15:44:57,092 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.3%, CPU使用率 100.0%
2025-07-30 15:45:12,199 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 54.2%
2025-07-30 15:45:24,346 - health_monitor - DEBUG - 系统指标 - CPU: 85.1%, 内存: 72.3%, 磁盘: 94.5%
2025-07-30 15:45:27,306 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.2%, CPU使用率 53.6%
2025-07-30 15:45:42,426 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.9%, CPU使用率 89.3%
2025-07-30 15:45:57,538 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.2%, CPU使用率 42.9%
2025-07-30 15:46:12,643 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.5%, CPU使用率 50.0%
2025-07-30 15:46:25,394 - health_monitor - DEBUG - 系统指标 - CPU: 98.1%, 内存: 70.2%, 磁盘: 94.4%
2025-07-30 15:46:27,899 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.1%, CPU使用率 100.0%
2025-07-30 15:46:43,043 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.7%, CPU使用率 95.8%
2025-07-30 15:46:58,176 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.7%, CPU使用率 85.7%
2025-07-30 15:47:13,283 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.4%, CPU使用率 95.8%
2025-07-30 15:47:26,437 - health_monitor - DEBUG - 系统指标 - CPU: 82.4%, 内存: 69.1%, 磁盘: 94.5%
2025-07-30 15:47:28,390 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.4%, CPU使用率 66.7%
2025-07-30 15:47:43,515 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.8%, CPU使用率 100.0%
2025-07-30 15:47:58,625 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.7%, CPU使用率 50.0%
2025-07-30 15:48:14,040 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.3%, CPU使用率 100.0%
2025-07-30 15:48:27,469 - health_monitor - DEBUG - 系统指标 - CPU: 93.4%, 内存: 70.5%, 磁盘: 94.5%
2025-07-30 15:48:29,557 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.8%, CPU使用率 100.0%
2025-07-30 15:48:44,884 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.4%, CPU使用率 100.0%
2025-07-30 15:49:00,182 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.3%, CPU使用率 100.0%
2025-07-30 15:49:15,809 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.6%, CPU使用率 98.9%
2025-07-30 15:49:28,513 - health_monitor - DEBUG - 系统指标 - CPU: 82.9%, 内存: 69.9%, 磁盘: 94.5%
2025-07-30 15:49:31,238 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.3%, CPU使用率 98.5%
2025-07-30 15:49:46,839 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.4%, CPU使用率 100.0%
2025-07-30 15:50:02,336 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.1%, CPU使用率 100.0%
2025-07-30 15:50:17,720 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.3%, CPU使用率 95.9%
2025-07-30 15:50:29,670 - health_monitor - DEBUG - 系统指标 - CPU: 95.5%, 内存: 73.2%, 磁盘: 94.5%
2025-07-30 15:50:33,041 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.5%, CPU使用率 94.1%
2025-07-30 15:50:48,173 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.3%, CPU使用率 96.4%
2025-07-30 15:51:03,280 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.3%, CPU使用率 83.3%
2025-07-30 15:51:18,337 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-07-30 15:51:18,660 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.4%, CPU使用率 100.0%
2025-07-30 15:51:30,964 - health_monitor - DEBUG - 系统指标 - CPU: 85.0%, 内存: 70.4%, 磁盘: 94.5%
2025-07-30 15:51:34,313 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.4%, CPU使用率 100.0%
2025-07-30 15:51:49,479 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.8%, CPU使用率 100.0%
2025-07-30 15:52:04,604 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.2%, CPU使用率 92.9%
2025-07-30 15:52:19,905 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.8%, CPU使用率 96.3%
2025-07-30 15:52:32,767 - health_monitor - DEBUG - 系统指标 - CPU: 90.2%, 内存: 71.0%, 磁盘: 94.5%
2025-07-30 15:52:35,011 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.8%, CPU使用率 79.2%
2025-07-30 15:52:50,137 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.8%, CPU使用率 87.5%
2025-07-30 15:53:05,543 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.9%, CPU使用率 98.0%
2025-07-30 15:53:20,684 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.2%, CPU使用率 83.3%
2025-07-30 15:53:34,423 - health_monitor - DEBUG - 系统指标 - CPU: 93.3%, 内存: 70.7%, 磁盘: 94.5%
2025-07-30 15:53:35,967 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.9%, CPU使用率 90.5%
2025-07-30 15:53:51,320 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.7%, CPU使用率 100.0%
2025-07-30 15:54:06,925 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.4%, CPU使用率 100.0%
2025-07-30 15:54:22,344 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.2%, CPU使用率 100.0%
2025-07-30 15:54:36,152 - health_monitor - DEBUG - 系统指标 - CPU: 99.3%, 内存: 72.2%, 磁盘: 94.5%
2025-07-30 15:54:37,510 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.9%, CPU使用率 100.0%
2025-07-30 15:54:52,837 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.9%, CPU使用率 100.0%
2025-07-30 15:55:07,946 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.5%, CPU使用率 64.3%
2025-07-30 15:55:23,425 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.0%, CPU使用率 100.0%
2025-07-30 15:55:37,481 - health_monitor - DEBUG - 系统指标 - CPU: 92.5%, 内存: 71.2%, 磁盘: 94.5%
2025-07-30 15:55:38,683 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.8%, CPU使用率 100.0%
2025-07-30 15:55:53,814 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.6%, CPU使用率 92.9%
2025-07-30 15:56:08,922 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.7%, CPU使用率 62.5%
2025-07-30 15:56:24,029 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.2%, CPU使用率 87.5%
2025-07-30 15:56:38,567 - health_monitor - DEBUG - 系统指标 - CPU: 95.7%, 内存: 72.5%, 磁盘: 94.5%
2025-07-30 15:56:39,137 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.3%, CPU使用率 70.8%
2025-07-30 15:56:54,244 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.0%, CPU使用率 54.2%
2025-07-30 15:57:09,378 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.8%, CPU使用率 94.1%
2025-07-30 15:57:24,761 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.8%, CPU使用率 100.0%
2025-07-30 15:57:39,914 - health_monitor - DEBUG - 系统指标 - CPU: 89.6%, 内存: 69.1%, 磁盘: 94.5%
2025-07-30 15:57:40,091 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 100.0%
2025-07-30 15:57:55,327 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.2%, CPU使用率 98.0%
2025-07-30 15:58:10,437 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.3%, CPU使用率 79.2%
2025-07-30 15:58:25,644 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.3%, CPU使用率 94.7%
2025-07-30 15:58:40,749 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.1%, CPU使用率 4.3%
2025-07-30 15:58:41,002 - health_monitor - DEBUG - 系统指标 - CPU: 13.2%, 内存: 67.1%, 磁盘: 94.5%
2025-07-30 15:58:55,855 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.9%, CPU使用率 19.2%
2025-07-30 15:59:10,965 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.2%, CPU使用率 13.0%
2025-07-30 15:59:26,075 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.9%, CPU使用率 45.8%
2025-07-30 15:59:41,182 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.1%, CPU使用率 100.0%
2025-07-30 15:59:42,060 - health_monitor - DEBUG - 系统指标 - CPU: 97.7%, 内存: 66.5%, 磁盘: 94.5%
2025-07-30 15:59:51,605 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-30 15:59:51,606 - main - INFO - 请求没有认证头部
2025-07-30 15:59:51,606 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 15:59:51,608 - main - INFO - --- 请求结束: 200 ---

2025-07-30 15:59:53,657 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 15:59:53,658 - main - INFO - 请求没有认证头部
2025-07-30 15:59:53,659 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 15:59:53,661 - app.core.db_connection - DEBUG - 当前线程ID: 2244
2025-07-30 15:59:53,661 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-30 15:59:53,662 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-30 15:59:53,663 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 15:59:53,664 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-30 15:59:54,506 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-30 15:59:54,509 - main - INFO - --- 请求结束: 200 ---

2025-07-30 15:59:56,287 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.8%, CPU使用率 30.8%
2025-07-30 16:00:11,392 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.9%, CPU使用率 0.0%
2025-07-30 16:00:26,510 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.9%, CPU使用率 75.0%
2025-07-30 16:00:41,616 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 21.4%
2025-07-30 16:00:43,090 - health_monitor - DEBUG - 系统指标 - CPU: 32.6%, 内存: 67.3%, 磁盘: 94.5%
2025-07-30 16:00:56,720 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 20.0%
2025-07-30 16:01:11,825 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 8.3%
2025-07-30 16:01:26,930 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 17.9%
2025-07-30 16:01:42,042 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 32.1%
2025-07-30 16:01:44,110 - health_monitor - DEBUG - 系统指标 - CPU: 16.8%, 内存: 67.2%, 磁盘: 94.5%
2025-07-30 16:01:57,148 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.1%, CPU使用率 21.4%
2025-07-30 16:02:12,253 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 14.8%
2025-07-30 16:02:27,359 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 25.0%
2025-07-30 16:02:42,464 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 41.7%
2025-07-30 16:02:45,134 - health_monitor - DEBUG - 系统指标 - CPU: 14.6%, 内存: 67.1%, 磁盘: 94.5%
2025-07-30 16:02:57,569 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 7.1%
2025-07-30 16:03:12,674 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 3.8%
2025-07-30 16:03:27,779 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 0.0%
2025-07-30 16:03:42,884 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 7.7%
2025-07-30 16:03:46,161 - health_monitor - DEBUG - 系统指标 - CPU: 16.4%, 内存: 67.2%, 磁盘: 94.5%
2025-07-30 16:03:57,988 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.1%, CPU使用率 0.0%
2025-07-30 16:04:13,094 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.0%, CPU使用率 16.7%
2025-07-30 16:04:28,198 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.1%, CPU使用率 12.5%
2025-07-30 16:04:43,303 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 14.3%
2025-07-30 16:04:47,195 - health_monitor - DEBUG - 系统指标 - CPU: 24.5%, 内存: 67.2%, 磁盘: 94.5%
2025-07-30 16:04:58,408 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 0.0%
2025-07-30 16:05:13,514 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 0.0%
2025-07-30 16:05:28,619 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 7.7%
2025-07-30 16:05:43,727 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 67.9%
2025-07-30 16:05:48,217 - health_monitor - DEBUG - 系统指标 - CPU: 38.1%, 内存: 67.3%, 磁盘: 94.5%
2025-07-30 16:05:58,832 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 12.0%
2025-07-30 16:06:13,936 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 4.2%
2025-07-30 16:06:29,054 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 62.5%
2025-07-30 16:06:44,159 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 0.0%
2025-07-30 16:06:49,396 - health_monitor - DEBUG - 系统指标 - CPU: 28.6%, 内存: 67.4%, 磁盘: 94.5%
2025-07-30 16:06:59,263 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 29.2%
2025-07-30 16:07:14,369 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 12.5%
2025-07-30 16:07:29,473 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 23.1%
2025-07-30 16:07:44,577 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 16.7%
2025-07-30 16:07:50,418 - health_monitor - DEBUG - 系统指标 - CPU: 19.1%, 内存: 67.3%, 磁盘: 94.5%
2025-07-30 16:07:59,682 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 25.0%
2025-07-30 16:08:14,787 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 53.6%
2025-07-30 16:08:29,892 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 4.2%
2025-07-30 16:08:44,996 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 25.0%
2025-07-30 16:08:51,439 - health_monitor - DEBUG - 系统指标 - CPU: 24.2%, 内存: 67.3%, 磁盘: 94.5%
2025-07-30 16:09:00,101 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 28.6%
2025-07-30 16:09:15,206 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 7.4%
2025-07-30 16:09:30,311 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 32.0%
2025-07-30 16:09:45,424 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 23.1%
2025-07-30 16:09:52,461 - health_monitor - DEBUG - 系统指标 - CPU: 10.2%, 内存: 67.2%, 磁盘: 94.5%
2025-07-30 16:10:00,529 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 25.0%
2025-07-30 16:10:15,634 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 14.3%
2025-07-30 16:10:30,737 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 26.9%
2025-07-30 16:10:45,843 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 12.5%
2025-07-30 16:10:53,482 - health_monitor - DEBUG - 系统指标 - CPU: 14.8%, 内存: 67.4%, 磁盘: 94.5%
2025-07-30 16:11:00,947 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 0.0%
2025-07-30 16:11:16,056 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 64.3%
2025-07-30 16:11:31,160 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 15.4%
2025-07-30 16:11:46,265 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 4.2%
2025-07-30 16:11:54,504 - health_monitor - DEBUG - 系统指标 - CPU: 26.2%, 内存: 67.4%, 磁盘: 94.5%
2025-07-30 16:12:01,370 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 24.0%
2025-07-30 16:12:16,474 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 25.9%
2025-07-30 16:12:31,586 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 28.6%
2025-07-30 16:12:46,697 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 23.1%
2025-07-30 16:12:55,526 - health_monitor - DEBUG - 系统指标 - CPU: 23.2%, 内存: 67.4%, 磁盘: 94.5%
2025-07-30 16:13:01,801 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 3.8%
2025-07-30 16:13:16,907 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 20.8%
2025-07-30 16:13:32,011 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 8.3%
2025-07-30 16:13:47,115 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 17.9%
2025-07-30 16:13:56,546 - health_monitor - DEBUG - 系统指标 - CPU: 33.6%, 内存: 67.4%, 磁盘: 94.5%
2025-07-30 16:14:02,220 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 16.7%
2025-07-30 16:14:17,335 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 42.9%
2025-07-30 16:14:32,439 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 4.2%
2025-07-30 16:14:47,545 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 12.5%
2025-07-30 16:14:57,570 - health_monitor - DEBUG - 系统指标 - CPU: 20.3%, 内存: 67.3%, 磁盘: 94.5%
2025-07-30 16:15:02,650 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 19.2%
2025-07-30 16:15:17,754 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 11.5%
2025-07-30 16:15:32,859 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 8.3%
2025-07-30 16:15:48,195 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.7%, CPU使用率 17.9%
2025-07-30 16:15:58,592 - health_monitor - DEBUG - 系统指标 - CPU: 10.5%, 内存: 67.8%, 磁盘: 94.5%
2025-07-30 16:16:03,299 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.7%, CPU使用率 14.8%
2025-07-30 16:16:18,404 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 4.2%
2025-07-30 16:16:33,510 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.7%, CPU使用率 28.0%
2025-07-30 16:16:48,614 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 24.1%
2025-07-30 16:16:59,612 - health_monitor - DEBUG - 系统指标 - CPU: 9.8%, 内存: 67.4%, 磁盘: 94.5%
2025-07-30 16:17:03,725 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 50.0%
2025-07-30 16:17:18,834 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 32.1%
2025-07-30 16:17:33,939 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.9%, CPU使用率 41.7%
2025-07-30 16:17:49,043 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.8%, CPU使用率 0.0%
2025-07-30 16:18:00,635 - health_monitor - DEBUG - 系统指标 - CPU: 20.2%, 内存: 67.8%, 磁盘: 94.5%
2025-07-30 16:18:04,149 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.9%, CPU使用率 7.1%
2025-07-30 16:18:19,255 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.6%, CPU使用率 7.1%
2025-07-30 16:18:34,359 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.7%, CPU使用率 16.0%
2025-07-30 16:18:49,465 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 54.2%
2025-07-30 16:19:01,656 - health_monitor - DEBUG - 系统指标 - CPU: 12.9%, 内存: 67.6%, 磁盘: 94.5%
2025-07-30 16:19:04,702 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.6%, CPU使用率 97.6%
2025-07-30 16:19:19,806 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 12.5%
2025-07-30 16:19:34,912 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 17.9%
2025-07-30 16:19:50,017 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 4.2%
2025-07-30 16:20:02,677 - health_monitor - DEBUG - 系统指标 - CPU: 14.1%, 内存: 67.4%, 磁盘: 94.5%
2025-07-30 16:20:05,121 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 8.3%
2025-07-30 16:20:20,227 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 7.1%
2025-07-30 16:20:35,331 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 11.5%
2025-07-30 16:20:50,437 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 4.2%
2025-07-30 16:21:03,697 - health_monitor - DEBUG - 系统指标 - CPU: 14.6%, 内存: 67.5%, 磁盘: 94.5%
2025-07-30 16:21:05,541 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 11.1%
2025-07-30 16:21:20,646 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 17.9%
2025-07-30 16:21:35,751 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 0.0%
2025-07-30 16:21:50,855 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 0.0%
2025-07-30 16:22:04,722 - health_monitor - DEBUG - 系统指标 - CPU: 15.2%, 内存: 67.5%, 磁盘: 94.5%
2025-07-30 16:22:05,960 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 7.1%
2025-07-30 16:22:21,071 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 64.3%
2025-07-30 16:22:36,178 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 3.8%
2025-07-30 16:22:51,282 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 12.5%
2025-07-30 16:23:05,743 - health_monitor - DEBUG - 系统指标 - CPU: 22.7%, 内存: 67.4%, 磁盘: 94.5%
2025-07-30 16:23:06,386 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 0.0%
2025-07-30 16:23:21,499 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 75.0%
2025-07-30 16:23:36,604 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.6%, CPU使用率 4.0%
2025-07-30 16:23:51,709 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.8%, CPU使用率 7.7%
2025-07-30 16:24:06,764 - health_monitor - DEBUG - 系统指标 - CPU: 12.1%, 内存: 67.7%, 磁盘: 94.5%
2025-07-30 16:24:06,813 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.7%, CPU使用率 4.2%
2025-07-30 16:24:21,917 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.7%, CPU使用率 16.7%
2025-07-30 16:24:37,021 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.2%, CPU使用率 3.6%
2025-07-30 16:24:52,127 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.2%, CPU使用率 35.7%
2025-07-30 16:25:07,237 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.9%, CPU使用率 11.5%
2025-07-30 16:25:07,786 - health_monitor - DEBUG - 系统指标 - CPU: 36.2%, 内存: 64.7%, 磁盘: 94.5%
2025-07-30 16:25:22,341 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.8%, CPU使用率 33.3%
2025-07-30 16:25:37,447 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.6%, CPU使用率 3.6%
2025-07-30 16:25:52,552 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.7%, CPU使用率 34.6%
2025-07-30 16:26:07,657 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.6%, CPU使用率 65.4%
2025-07-30 16:26:08,816 - health_monitor - DEBUG - 系统指标 - CPU: 60.2%, 内存: 64.7%, 磁盘: 94.4%
2025-07-30 16:26:15,516 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-30 16:26:15,517 - main - INFO - 请求没有认证头部
2025-07-30 16:26:15,518 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 16:26:15,519 - main - INFO - --- 请求结束: 200 ---

2025-07-30 16:26:17,550 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 16:26:17,552 - main - INFO - 请求没有认证头部
2025-07-30 16:26:17,552 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 16:26:17,553 - app.core.db_connection - DEBUG - 当前线程ID: 2244
2025-07-30 16:26:17,554 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-30 16:26:17,555 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-30 16:26:17,556 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-30 16:26:17,556 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 16:26:17,557 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-30 16:26:18,409 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-30 16:26:18,411 - main - INFO - --- 请求结束: 200 ---

2025-07-30 16:26:22,765 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.4%, CPU使用率 58.3%
2025-07-30 16:26:37,871 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.2%, CPU使用率 45.8%
2025-07-30 16:26:52,975 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.1%, CPU使用率 3.8%
2025-07-30 16:27:08,080 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.2%, CPU使用率 16.0%
2025-07-30 16:27:09,838 - health_monitor - DEBUG - 系统指标 - CPU: 21.1%, 内存: 66.2%, 磁盘: 94.4%
2025-07-30 16:27:23,186 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.2%, CPU使用率 16.7%
2025-07-30 16:27:38,291 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.1%, CPU使用率 19.2%
2025-07-30 16:27:53,396 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.2%, CPU使用率 35.7%
2025-07-30 16:28:08,500 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.4%, CPU使用率 20.0%
2025-07-30 16:28:10,858 - health_monitor - DEBUG - 系统指标 - CPU: 16.7%, 内存: 66.3%, 磁盘: 94.4%
2025-07-30 16:28:23,604 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.3%, CPU使用率 4.2%
2025-07-30 16:28:38,710 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.4%, CPU使用率 21.4%
2025-07-30 16:28:53,816 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.2%, CPU使用率 19.2%
2025-07-30 16:29:08,921 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.4%, CPU使用率 0.0%
2025-07-30 16:29:11,880 - health_monitor - DEBUG - 系统指标 - CPU: 53.2%, 内存: 66.4%, 磁盘: 94.4%
2025-07-30 16:29:24,027 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.4%, CPU使用率 66.7%
2025-07-30 16:29:39,133 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.4%, CPU使用率 10.7%
2025-07-30 16:29:54,238 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.3%, CPU使用率 10.7%
2025-07-30 16:30:09,344 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.3%, CPU使用率 72.0%
2025-07-30 16:30:12,902 - health_monitor - DEBUG - 系统指标 - CPU: 26.6%, 内存: 66.2%, 磁盘: 94.4%
2025-07-30 16:30:24,449 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.3%, CPU使用率 41.7%
2025-07-30 16:30:39,555 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.3%, CPU使用率 19.2%
2025-07-30 16:30:54,663 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.3%, CPU使用率 39.3%
2025-07-30 16:31:09,770 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.8%, CPU使用率 78.6%
2025-07-30 16:31:13,926 - health_monitor - DEBUG - 系统指标 - CPU: 84.8%, 内存: 69.7%, 磁盘: 94.4%
2025-07-30 16:31:24,875 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.2%, CPU使用率 37.5%
2025-07-30 16:31:39,980 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 79.2%
2025-07-30 16:31:55,088 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.9%, CPU使用率 76.0%
2025-07-30 16:32:10,288 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.5%, CPU使用率 91.2%
2025-07-30 16:32:14,950 - health_monitor - DEBUG - 系统指标 - CPU: 70.8%, 内存: 68.7%, 磁盘: 94.4%
2025-07-30 16:32:25,467 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.8%, CPU使用率 100.0%
2025-07-30 16:32:41,062 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.4%, CPU使用率 100.0%
2025-07-30 16:32:56,186 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.9%, CPU使用率 93.1%
2025-07-30 16:33:11,408 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.7%, CPU使用率 100.0%
2025-07-30 16:33:16,000 - health_monitor - DEBUG - 系统指标 - CPU: 91.2%, 内存: 68.6%, 磁盘: 94.4%
2025-07-30 16:33:26,532 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.3%, CPU使用率 83.3%
2025-07-30 16:33:41,638 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 91.7%
2025-07-30 16:33:56,744 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.2%, CPU使用率 48.0%
2025-07-30 16:34:11,908 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.0%, CPU使用率 97.1%
2025-07-30 16:34:17,444 - health_monitor - DEBUG - 系统指标 - CPU: 91.2%, 内存: 70.6%, 磁盘: 94.5%
2025-07-30 16:34:27,165 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.1%, CPU使用率 100.0%
2025-07-30 16:34:42,340 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.5%, CPU使用率 75.0%
2025-07-30 16:34:57,497 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.1%, CPU使用率 100.0%
2025-07-30 16:35:12,918 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.2%, CPU使用率 95.2%
2025-07-30 16:35:18,699 - health_monitor - DEBUG - 系统指标 - CPU: 76.3%, 内存: 69.2%, 磁盘: 94.4%
2025-07-30 16:35:28,267 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.7%, CPU使用率 100.0%
2025-07-30 16:35:43,376 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.3%, CPU使用率 60.0%
2025-07-30 16:35:58,594 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.7%, CPU使用率 96.8%
2025-07-30 16:36:13,977 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.3%, CPU使用率 100.0%
2025-07-30 16:36:19,889 - health_monitor - DEBUG - 系统指标 - CPU: 83.5%, 内存: 71.0%, 磁盘: 94.4%
2025-07-30 16:36:29,157 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.9%, CPU使用率 66.7%
2025-07-30 16:36:44,420 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.9%, CPU使用率 90.2%
2025-07-30 16:36:59,722 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.5%, CPU使用率 96.6%
2025-07-30 16:37:14,831 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.1%, CPU使用率 96.4%
2025-07-30 16:37:20,927 - health_monitor - DEBUG - 系统指标 - CPU: 72.0%, 内存: 67.3%, 磁盘: 94.5%
2025-07-30 16:37:29,939 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.2%, CPU使用率 50.0%
2025-07-30 16:37:45,045 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.0%, CPU使用率 29.2%
2025-07-30 16:38:00,178 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.6%, CPU使用率 100.0%
2025-07-30 16:38:06,075 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-30 16:38:06,076 - main - INFO - 请求没有认证头部
2025-07-30 16:38:06,077 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 16:38:06,078 - main - INFO - --- 请求结束: 200 ---

2025-07-30 16:38:08,111 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 16:38:08,139 - main - INFO - 请求没有认证头部
2025-07-30 16:38:08,161 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 16:38:08,176 - app.core.db_connection - DEBUG - 当前线程ID: 2244
2025-07-30 16:38:08,176 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-30 16:38:08,178 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-30 16:38:08,178 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-30 16:38:08,179 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 16:38:08,180 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-30 16:38:10,993 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-30 16:38:10,997 - main - INFO - --- 请求结束: 200 ---

2025-07-30 16:38:15,283 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.7%, CPU使用率 70.8%
2025-07-30 16:38:22,580 - health_monitor - DEBUG - 系统指标 - CPU: 96.1%, 内存: 70.8%, 磁盘: 94.5%
2025-07-30 16:38:30,710 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.3%, CPU使用率 98.7%
2025-07-30 16:38:46,483 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.2%, CPU使用率 99.2%
2025-07-30 16:39:01,871 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.9%, CPU使用率 97.4%
2025-07-30 16:39:17,462 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.7%, CPU使用率 100.0%
2025-07-30 16:39:23,897 - health_monitor - DEBUG - 系统指标 - CPU: 92.5%, 内存: 70.5%, 磁盘: 94.4%
2025-07-30 16:39:32,650 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.4%, CPU使用率 100.0%
2025-07-30 16:39:48,010 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.7%, CPU使用率 100.0%
2025-07-30 16:40:03,136 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.8%, CPU使用率 80.0%
2025-07-30 16:40:18,290 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.2%, CPU使用率 100.0%
2025-07-30 16:40:25,054 - health_monitor - DEBUG - 系统指标 - CPU: 89.1%, 内存: 70.0%, 磁盘: 94.4%
2025-07-30 16:40:33,566 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.8%, CPU使用率 91.2%
2025-07-30 16:40:48,849 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.9%, CPU使用率 67.9%
2025-07-30 16:41:04,007 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.7%, CPU使用率 95.0%
2025-07-30 16:41:19,186 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.1%, CPU使用率 87.1%
2025-07-30 16:41:26,077 - health_monitor - DEBUG - 系统指标 - CPU: 47.7%, 内存: 62.9%, 磁盘: 94.4%
2025-07-30 16:41:34,299 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 38.5%
2025-07-30 16:41:49,405 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.0%, CPU使用率 37.5%
2025-07-30 16:42:04,509 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.1%, CPU使用率 19.2%
2025-07-30 16:42:19,621 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.2%, CPU使用率 75.0%
2025-07-30 16:42:27,125 - health_monitor - DEBUG - 系统指标 - CPU: 47.3%, 内存: 63.2%, 磁盘: 94.4%
2025-07-30 16:42:34,726 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.3%, CPU使用率 35.7%
2025-07-30 16:42:49,833 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.1%, CPU使用率 53.6%
2025-07-30 16:43:04,939 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.9%, CPU使用率 48.1%
2025-07-30 16:43:20,116 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.8%, CPU使用率 92.3%
2025-07-30 16:43:28,199 - health_monitor - DEBUG - 系统指标 - CPU: 70.4%, 内存: 65.6%, 磁盘: 94.4%
2025-07-30 16:43:35,224 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.1%, CPU使用率 72.4%
2025-07-30 16:43:50,418 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 100.0%
2025-07-30 16:44:05,633 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.4%, CPU使用率 100.0%
2025-07-30 16:44:20,745 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.1%, CPU使用率 37.5%
2025-07-30 16:44:29,447 - health_monitor - DEBUG - 系统指标 - CPU: 69.8%, 内存: 66.1%, 磁盘: 94.4%
2025-07-30 16:44:35,851 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.1%, CPU使用率 78.6%
2025-07-30 16:44:51,033 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.4%, CPU使用率 100.0%
2025-07-30 16:45:06,150 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.6%, CPU使用率 95.8%
2025-07-30 16:45:21,367 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.5%, CPU使用率 96.8%
2025-07-30 16:45:30,481 - health_monitor - DEBUG - 系统指标 - CPU: 67.4%, 内存: 68.4%, 磁盘: 94.4%
2025-07-30 16:45:36,532 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.4%, CPU使用率 100.0%
2025-07-30 16:45:51,834 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.1%, CPU使用率 100.0%
2025-07-30 16:46:07,083 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.3%, CPU使用率 100.0%
2025-07-30 16:46:22,200 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.0%, CPU使用率 50.0%
2025-07-30 16:46:31,511 - health_monitor - DEBUG - 系统指标 - CPU: 62.9%, 内存: 67.7%, 磁盘: 94.4%
2025-07-30 16:46:37,653 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.7%, CPU使用率 100.0%
2025-07-30 16:46:52,766 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.3%, CPU使用率 58.3%
2025-07-30 16:47:07,871 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 37.5%
2025-07-30 16:47:23,015 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.6%, CPU使用率 96.7%
2025-07-30 16:47:32,539 - health_monitor - DEBUG - 系统指标 - CPU: 79.8%, 内存: 66.6%, 磁盘: 94.4%
2025-07-30 16:47:38,218 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.0%, CPU使用率 95.7%
2025-07-30 16:47:53,324 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.7%, CPU使用率 62.5%
2025-07-30 16:48:08,431 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.6%, CPU使用率 70.8%
2025-07-30 16:48:23,549 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.7%, CPU使用率 93.9%
2025-07-30 16:48:33,573 - health_monitor - DEBUG - 系统指标 - CPU: 81.5%, 内存: 67.5%, 磁盘: 94.4%
2025-07-30 16:48:38,674 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.8%, CPU使用率 100.0%
2025-07-30 16:48:53,795 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 66.7%
2025-07-30 16:49:08,903 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.5%, CPU使用率 79.2%
2025-07-30 16:49:24,160 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.2%, CPU使用率 86.1%
2025-07-30 16:49:34,667 - health_monitor - DEBUG - 系统指标 - CPU: 85.4%, 内存: 66.9%, 磁盘: 94.4%
2025-07-30 16:49:39,269 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.1%, CPU使用率 55.6%
2025-07-30 16:49:54,574 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.2%, CPU使用率 89.5%
2025-07-30 16:50:09,760 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.6%, CPU使用率 100.0%
2025-07-30 16:50:24,950 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.9%, CPU使用率 97.4%
2025-07-30 16:50:35,874 - health_monitor - DEBUG - 系统指标 - CPU: 80.8%, 内存: 68.1%, 磁盘: 94.4%
2025-07-30 16:50:40,100 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.0%, CPU使用率 93.3%
2025-07-30 16:50:55,246 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.0%, CPU使用率 87.5%
2025-07-30 16:51:01,718 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-30 16:51:01,719 - main - INFO - 请求没有认证头部
2025-07-30 16:51:01,720 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 16:51:01,721 - main - INFO - --- 请求结束: 200 ---

2025-07-30 16:51:10,357 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.6%, CPU使用率 87.5%
2025-07-30 16:51:25,466 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.6%, CPU使用率 83.3%
2025-07-30 16:51:36,988 - health_monitor - DEBUG - 系统指标 - CPU: 66.8%, 内存: 70.1%, 磁盘: 94.4%
2025-07-30 16:51:40,887 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.2%, CPU使用率 100.0%
2025-07-30 16:51:56,498 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.8%, CPU使用率 100.0%
2025-07-30 16:52:11,789 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.8%, CPU使用率 95.7%
2025-07-30 16:52:27,314 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.4%, CPU使用率 100.0%
2025-07-30 16:52:38,155 - health_monitor - DEBUG - 系统指标 - CPU: 75.6%, 内存: 67.6%, 磁盘: 94.4%
2025-07-30 16:52:42,550 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.6%, CPU使用率 42.9%
2025-07-30 16:52:58,109 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.9%, CPU使用率 100.0%
2025-07-30 16:53:13,818 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 100.0%
2025-07-30 16:53:28,997 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.8%, CPU使用率 96.4%
2025-07-30 16:53:39,250 - health_monitor - DEBUG - 系统指标 - CPU: 94.3%, 内存: 67.6%, 磁盘: 94.4%
2025-07-30 16:53:44,352 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.8%, CPU使用率 98.0%
2025-07-30 16:53:59,460 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.7%, CPU使用率 89.3%
2025-07-30 16:54:14,587 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 84.4%
2025-07-30 16:54:29,790 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.7%, CPU使用率 100.0%
2025-07-30 16:54:40,419 - health_monitor - DEBUG - 系统指标 - CPU: 76.2%, 内存: 68.8%, 磁盘: 94.4%
2025-07-30 16:54:45,045 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.5%, CPU使用率 100.0%
2025-07-30 16:55:00,262 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.2%, CPU使用率 100.0%
2025-07-30 16:55:15,382 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.3%, CPU使用率 72.4%
2025-07-30 16:55:30,685 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.9%, CPU使用率 100.0%
2025-07-30 16:55:41,640 - health_monitor - DEBUG - 系统指标 - CPU: 68.8%, 内存: 69.0%, 磁盘: 94.4%
2025-07-30 16:55:45,791 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.4%, CPU使用率 75.0%
2025-07-30 16:56:01,186 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.9%, CPU使用率 100.0%
2025-07-30 16:56:15,807 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-30 16:56:15,811 - main - INFO - 请求没有认证头部
2025-07-30 16:56:15,811 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 16:56:15,813 - main - INFO - --- 请求结束: 200 ---

2025-07-30 16:56:16,411 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.3%, CPU使用率 100.0%
2025-07-30 16:56:18,107 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 16:56:18,115 - main - INFO - 请求没有认证头部
2025-07-30 16:56:18,119 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 16:56:18,120 - app.core.db_connection - DEBUG - 当前线程ID: 2244
2025-07-30 16:56:18,123 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-30 16:56:18,125 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-30 16:56:18,126 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-30 16:56:18,127 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 16:56:18,128 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-30 16:56:24,281 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-30 16:56:24,284 - main - INFO - --- 请求结束: 200 ---

2025-07-30 16:56:31,715 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.4%, CPU使用率 100.0%
2025-07-30 16:56:43,306 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 69.2%, 磁盘: 94.4%
2025-07-30 16:56:47,091 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.7%, CPU使用率 100.0%
2025-07-30 16:56:54,912 - main - INFO - 
--- 请求开始: POST /api/medications ---
2025-07-30 16:56:54,913 - main - INFO - 请求没有认证头部
2025-07-30 16:56:54,914 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 16:56:54,918 - main - INFO - --- 请求结束: 404 ---

2025-07-30 16:57:02,740 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.3%, CPU使用率 100.0%
2025-07-30 16:57:17,858 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.2%, CPU使用率 100.0%
2025-07-30 16:57:32,986 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 91.7%
2025-07-30 16:57:44,406 - health_monitor - DEBUG - 系统指标 - CPU: 98.1%, 内存: 68.5%, 磁盘: 94.5%
2025-07-30 16:57:48,094 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.1%, CPU使用率 58.3%
2025-07-30 16:58:03,301 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 97.2%
2025-07-30 16:58:18,412 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.7%, CPU使用率 92.9%
2025-07-30 16:58:33,564 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.5%, CPU使用率 84.0%
2025-07-30 16:58:45,705 - health_monitor - DEBUG - 系统指标 - CPU: 99.4%, 内存: 66.4%, 磁盘: 94.4%
2025-07-30 16:58:48,734 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.9%, CPU使用率 100.0%
2025-07-30 16:59:03,982 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.5%, CPU使用率 100.0%
2025-07-30 16:59:19,091 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.6%, CPU使用率 79.2%
2025-07-30 16:59:34,196 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.2%, CPU使用率 34.5%
2025-07-30 16:59:46,735 - health_monitor - DEBUG - 系统指标 - CPU: 52.3%, 内存: 60.3%, 磁盘: 94.4%
2025-07-30 16:59:49,304 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 7.1%
2025-07-30 17:00:04,411 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.1%, CPU使用率 50.0%
2025-07-30 17:00:11,907 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-30 17:00:11,910 - main - INFO - 请求没有认证头部
2025-07-30 17:00:11,913 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 17:00:11,915 - main - INFO - --- 请求结束: 200 ---

2025-07-30 17:00:13,950 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 17:00:13,952 - main - INFO - 请求没有认证头部
2025-07-30 17:00:13,955 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 17:00:13,957 - app.core.db_connection - DEBUG - 当前线程ID: 2244
2025-07-30 17:00:13,958 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-30 17:00:13,960 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-30 17:00:13,961 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-30 17:00:13,963 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 17:00:13,964 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-30 17:00:14,890 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-30 17:00:14,897 - main - INFO - --- 请求结束: 200 ---

2025-07-30 17:00:19,517 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.0%, CPU使用率 45.8%
2025-07-30 17:00:34,625 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.8%, CPU使用率 37.5%
2025-07-30 17:00:43,678 - main - INFO - 
--- 请求开始: POST /api/medications ---
2025-07-30 17:00:43,680 - main - INFO - 请求没有认证头部
2025-07-30 17:00:43,683 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 17:00:43,686 - main - INFO - --- 请求结束: 404 ---

2025-07-30 17:00:47,766 - health_monitor - DEBUG - 系统指标 - CPU: 27.6%, 内存: 62.8%, 磁盘: 94.4%
2025-07-30 17:00:49,733 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.7%, CPU使用率 28.0%
2025-07-30 17:01:04,840 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 16.7%
2025-07-30 17:01:19,945 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 17.9%
2025-07-30 17:01:35,054 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 22.2%
2025-07-30 17:01:48,787 - health_monitor - DEBUG - 系统指标 - CPU: 12.8%, 内存: 63.1%, 磁盘: 94.4%
2025-07-30 17:01:50,160 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.0%, CPU使用率 46.4%
2025-07-30 17:02:05,267 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.1%, CPU使用率 12.5%
2025-07-30 17:02:20,379 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.1%, CPU使用率 92.9%
2025-07-30 17:02:35,492 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.0%, CPU使用率 0.0%
2025-07-30 17:02:49,838 - health_monitor - DEBUG - 系统指标 - CPU: 27.9%, 内存: 63.1%, 磁盘: 94.4%
2025-07-30 17:02:50,598 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.1%, CPU使用率 22.2%
2025-07-30 17:03:05,746 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.0%, CPU使用率 65.7%
2025-07-30 17:03:20,860 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.5%, CPU使用率 37.5%
2025-07-30 17:03:35,967 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.2%, CPU使用率 75.0%
2025-07-30 17:03:50,869 - health_monitor - DEBUG - 系统指标 - CPU: 24.5%, 内存: 63.9%, 磁盘: 94.4%
2025-07-30 17:03:51,075 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.9%, CPU使用率 33.3%
2025-07-30 17:04:06,184 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.3%, CPU使用率 100.0%
2025-07-30 17:04:21,436 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.9%, CPU使用率 100.0%
2025-07-30 17:04:36,546 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.9%, CPU使用率 62.5%
2025-07-30 17:04:51,730 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.1%, CPU使用率 92.9%
2025-07-30 17:04:51,899 - health_monitor - DEBUG - 系统指标 - CPU: 85.7%, 内存: 65.7%, 磁盘: 94.4%
2025-07-30 17:05:06,909 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 100.0%
2025-07-30 17:05:22,082 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.1%, CPU使用率 100.0%
2025-07-30 17:05:37,195 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.2%, CPU使用率 92.9%
2025-07-30 17:05:52,311 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.7%, CPU使用率 91.7%
2025-07-30 17:05:53,436 - health_monitor - DEBUG - 系统指标 - CPU: 95.0%, 内存: 64.6%, 磁盘: 94.5%
2025-07-30 17:06:07,424 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 93.1%
2025-07-30 17:06:22,671 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 95.6%
2025-07-30 17:06:37,783 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.2%, CPU使用率 87.5%
2025-07-30 17:06:52,896 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.5%, CPU使用率 92.9%
2025-07-30 17:06:54,598 - health_monitor - DEBUG - 系统指标 - CPU: 72.3%, 内存: 65.7%, 磁盘: 94.4%
2025-07-30 17:07:08,082 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.0%, CPU使用率 97.7%
2025-07-30 17:07:23,346 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.6%, CPU使用率 95.7%
2025-07-30 17:07:38,572 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.2%, CPU使用率 97.6%
2025-07-30 17:07:53,908 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.9%, CPU使用率 93.0%
2025-07-30 17:07:55,839 - health_monitor - DEBUG - 系统指标 - CPU: 95.8%, 内存: 64.7%, 磁盘: 94.4%
2025-07-30 17:08:09,018 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.1%, CPU使用率 71.4%
2025-07-30 17:08:24,132 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.2%, CPU使用率 78.6%
2025-07-30 17:08:39,530 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.4%, CPU使用率 100.0%
2025-07-30 17:08:54,972 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 100.0%
2025-07-30 17:08:57,382 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 66.5%, 磁盘: 94.5%
2025-07-30 17:09:10,199 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.6%, CPU使用率 96.8%
2025-07-30 17:09:25,762 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.0%, CPU使用率 100.0%
2025-07-30 17:09:40,988 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.9%, CPU使用率 57.1%
2025-07-30 17:09:56,522 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.6%, CPU使用率 100.0%
2025-07-30 17:09:58,638 - health_monitor - DEBUG - 系统指标 - CPU: 78.2%, 内存: 69.6%, 磁盘: 94.4%
2025-07-30 17:10:12,087 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.0%, CPU使用率 100.0%
2025-07-30 17:10:27,333 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.1%, CPU使用率 94.3%
2025-07-30 17:10:42,547 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.8%, CPU使用率 95.0%
2025-07-30 17:10:58,147 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.4%, CPU使用率 100.0%
2025-07-30 17:10:59,944 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 68.2%, 磁盘: 94.4%
2025-07-30 17:11:13,413 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 96.6%
2025-07-30 17:11:28,756 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.0%, CPU使用率 97.7%
2025-07-30 17:11:43,863 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.3%, CPU使用率 57.1%
2025-07-30 17:11:59,504 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.0%, CPU使用率 100.0%
2025-07-30 17:12:01,871 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 67.1%, 磁盘: 94.4%
2025-07-30 17:12:14,819 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.3%, CPU使用率 100.0%
2025-07-30 17:12:29,971 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.2%, CPU使用率 100.0%
2025-07-30 17:12:45,508 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.1%, CPU使用率 100.0%
2025-07-30 17:13:00,932 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.3%, CPU使用率 100.0%
2025-07-30 17:13:03,381 - health_monitor - DEBUG - 系统指标 - CPU: 97.0%, 内存: 68.7%, 磁盘: 94.4%
2025-07-30 17:13:16,593 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.6%, CPU使用率 100.0%
2025-07-30 17:13:32,581 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.5%, CPU使用率 100.0%
2025-07-30 17:13:47,914 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 82.1%
2025-07-30 17:14:03,046 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.8%, CPU使用率 92.6%
2025-07-30 17:14:04,539 - health_monitor - DEBUG - 系统指标 - CPU: 89.6%, 内存: 72.2%, 磁盘: 94.4%
2025-07-30 17:14:18,160 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.1%, CPU使用率 92.9%
2025-07-30 17:14:33,499 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.9%, CPU使用率 98.1%
2025-07-30 17:14:48,609 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.6%, CPU使用率 70.8%
2025-07-30 17:15:04,166 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.9%, CPU使用率 100.0%
2025-07-30 17:15:05,838 - health_monitor - DEBUG - 系统指标 - CPU: 91.3%, 内存: 72.1%, 磁盘: 94.4%
2025-07-30 17:15:19,399 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.6%, CPU使用率 78.6%
2025-07-30 17:15:34,760 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.0%, CPU使用率 100.0%
2025-07-30 17:15:50,448 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.4%, CPU使用率 100.0%
2025-07-30 17:16:06,123 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.2%, CPU使用率 100.0%
2025-07-30 17:16:06,904 - health_monitor - DEBUG - 系统指标 - CPU: 87.0%, 内存: 71.8%, 磁盘: 94.4%
2025-07-30 17:16:21,709 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.8%, CPU使用率 100.0%
2025-07-30 17:16:36,941 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.9%, CPU使用率 92.9%
2025-07-30 17:16:52,055 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.0%, CPU使用率 50.0%
2025-07-30 17:17:07,271 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.6%, CPU使用率 100.0%
2025-07-30 17:17:08,224 - health_monitor - DEBUG - 系统指标 - CPU: 85.2%, 内存: 72.4%, 磁盘: 94.4%
2025-07-30 17:17:22,410 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.2%, CPU使用率 100.0%
2025-07-30 17:17:37,696 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.7%, CPU使用率 100.0%
2025-07-30 17:17:53,356 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.8%, CPU使用率 100.0%
2025-07-30 17:18:08,570 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.5%, CPU使用率 100.0%
2025-07-30 17:18:09,982 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 72.8%, 磁盘: 94.4%
2025-07-30 17:18:23,679 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.2%, CPU使用率 96.6%
2025-07-30 17:18:39,125 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.2%, CPU使用率 100.0%
2025-07-30 17:18:54,380 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.3%, CPU使用率 95.1%
2025-07-30 17:19:09,978 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.2%, CPU使用率 100.0%
2025-07-30 17:19:11,503 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 74.6%, 磁盘: 94.4%
2025-07-30 17:19:25,177 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.2%, CPU使用率 87.5%
2025-07-30 17:19:40,473 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.6%, CPU使用率 100.0%
2025-07-30 17:19:55,589 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.6%, CPU使用率 100.0%
2025-07-30 17:20:10,968 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.3%, CPU使用率 100.0%
2025-07-30 17:20:12,778 - health_monitor - DEBUG - 系统指标 - CPU: 91.9%, 内存: 74.0%, 磁盘: 94.4%
2025-07-30 17:20:26,535 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.4%, CPU使用率 98.6%
2025-07-30 17:20:42,206 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.3%, CPU使用率 97.1%
2025-07-30 17:20:57,714 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.3%, CPU使用率 100.0%
2025-07-30 17:21:12,829 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.7%, CPU使用率 79.2%
2025-07-30 17:21:14,001 - health_monitor - DEBUG - 系统指标 - CPU: 83.7%, 内存: 76.4%, 磁盘: 94.4%
2025-07-30 17:21:28,135 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.4%, CPU使用率 98.2%
2025-07-30 17:21:43,246 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.2%, CPU使用率 62.5%
2025-07-30 17:21:58,357 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.4%, CPU使用率 100.0%
2025-07-30 17:22:13,467 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.9%, CPU使用率 83.3%
2025-07-30 17:22:15,252 - health_monitor - DEBUG - 系统指标 - CPU: 96.2%, 内存: 76.0%, 磁盘: 94.5%
2025-07-30 17:22:28,584 - monitoring - DEBUG - 资源指标更新: 内存使用率 78.3%, CPU使用率 53.6%
2025-07-30 17:22:43,692 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.2%, CPU使用率 64.3%
2025-07-30 17:22:58,871 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.0%, CPU使用率 100.0%
2025-07-30 17:23:14,120 - monitoring - DEBUG - 资源指标更新: 内存使用率 78.8%, CPU使用率 97.1%
2025-07-30 17:23:16,616 - health_monitor - DEBUG - 系统指标 - CPU: 97.2%, 内存: 76.5%, 磁盘: 94.4%
2025-07-30 17:23:29,408 - monitoring - DEBUG - 资源指标更新: 内存使用率 79.3%, CPU使用率 97.9%
2025-07-30 17:23:44,822 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.9%, CPU使用率 83.3%
2025-07-30 17:24:00,478 - monitoring - DEBUG - 资源指标更新: 内存使用率 78.5%, CPU使用率 100.0%
2025-07-30 17:24:15,598 - monitoring - DEBUG - 资源指标更新: 内存使用率 78.0%, CPU使用率 85.7%
2025-07-30 17:24:18,749 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 80.1%, 磁盘: 94.5%
2025-07-30 17:24:31,016 - monitoring - DEBUG - 资源指标更新: 内存使用率 79.5%, CPU使用率 98.2%
2025-07-30 17:24:46,500 - monitoring - DEBUG - 资源指标更新: 内存使用率 78.2%, CPU使用率 100.0%
2025-07-30 17:25:01,864 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.9%, CPU使用率 100.0%
2025-07-30 17:25:17,013 - monitoring - DEBUG - 资源指标更新: 内存使用率 80.6%, CPU使用率 96.4%
2025-07-30 17:25:20,431 - health_monitor - DEBUG - 系统指标 - CPU: 92.7%, 内存: 78.3%, 磁盘: 94.4%
2025-07-30 17:25:32,130 - monitoring - DEBUG - 资源指标更新: 内存使用率 80.5%, CPU使用率 89.7%
2025-07-30 17:25:47,341 - monitoring - DEBUG - 资源指标更新: 内存使用率 79.4%, CPU使用率 93.3%
2025-07-30 17:26:03,035 - monitoring - DEBUG - 资源指标更新: 内存使用率 79.6%, CPU使用率 100.0%
2025-07-30 17:26:18,156 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.8%, CPU使用率 79.2%
2025-07-30 17:26:21,470 - health_monitor - DEBUG - 系统指标 - CPU: 83.1%, 内存: 80.0%, 磁盘: 94.4%
2025-07-30 17:26:33,359 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.8%, CPU使用率 100.0%
2025-07-30 17:26:48,558 - monitoring - DEBUG - 资源指标更新: 内存使用率 78.8%, CPU使用率 97.6%
2025-07-30 17:27:03,672 - monitoring - DEBUG - 资源指标更新: 内存使用率 78.5%, CPU使用率 70.8%
2025-07-30 17:27:18,850 - monitoring - DEBUG - 资源指标更新: 内存使用率 80.8%, CPU使用率 48.1%
2025-07-30 17:27:22,526 - health_monitor - DEBUG - 系统指标 - CPU: 71.5%, 内存: 78.8%, 磁盘: 94.4%
2025-07-30 17:27:34,126 - monitoring - DEBUG - 资源指标更新: 内存使用率 81.2%, CPU使用率 100.0%
2025-07-30 17:27:49,263 - monitoring - DEBUG - 资源指标更新: 内存使用率 79.4%, CPU使用率 95.8%
2025-07-30 17:28:04,652 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.3%, CPU使用率 100.0%
2025-07-30 17:28:19,834 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.6%, CPU使用率 94.6%
2025-07-30 17:28:23,555 - health_monitor - DEBUG - 系统指标 - CPU: 66.8%, 内存: 81.1%, 磁盘: 94.4%
2025-07-30 17:28:34,945 - monitoring - DEBUG - 资源指标更新: 内存使用率 80.4%, CPU使用率 40.7%
2025-07-30 17:28:50,099 - monitoring - DEBUG - 资源指标更新: 内存使用率 78.6%, CPU使用率 60.0%
2025-07-30 17:29:05,253 - monitoring - DEBUG - 资源指标更新: 内存使用率 80.3%, CPU使用率 84.4%
2025-07-30 17:29:20,365 - monitoring - DEBUG - 资源指标更新: 内存使用率 79.2%, CPU使用率 78.6%
2025-07-30 17:29:24,672 - health_monitor - DEBUG - 系统指标 - CPU: 83.3%, 内存: 80.2%, 磁盘: 94.4%
2025-07-30 17:29:35,716 - monitoring - DEBUG - 资源指标更新: 内存使用率 80.3%, CPU使用率 100.0%
2025-07-30 17:29:51,133 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.9%, CPU使用率 100.0%
2025-07-30 17:30:06,248 - monitoring - DEBUG - 资源指标更新: 内存使用率 80.9%, CPU使用率 91.7%
2025-07-30 17:30:21,356 - monitoring - DEBUG - 资源指标更新: 内存使用率 79.2%, CPU使用率 66.7%
2025-07-30 17:30:25,755 - health_monitor - DEBUG - 系统指标 - CPU: 96.9%, 内存: 79.0%, 磁盘: 94.4%
2025-07-30 17:30:36,465 - monitoring - DEBUG - 资源指标更新: 内存使用率 81.5%, CPU使用率 91.7%
2025-07-30 17:30:51,821 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.8%, CPU使用率 97.9%
2025-07-30 17:31:07,158 - monitoring - DEBUG - 资源指标更新: 内存使用率 78.1%, CPU使用率 100.0%
2025-07-30 17:31:22,459 - monitoring - DEBUG - 资源指标更新: 内存使用率 78.0%, CPU使用率 100.0%
2025-07-30 17:31:26,791 - health_monitor - DEBUG - 系统指标 - CPU: 71.7%, 内存: 81.7%, 磁盘: 94.5%
2025-07-30 17:31:37,921 - monitoring - DEBUG - 资源指标更新: 内存使用率 78.2%, CPU使用率 100.0%
2025-07-30 17:31:53,431 - monitoring - DEBUG - 资源指标更新: 内存使用率 80.6%, CPU使用率 100.0%
2025-07-30 17:32:08,617 - monitoring - DEBUG - 资源指标更新: 内存使用率 79.5%, CPU使用率 60.7%
2025-07-30 17:32:23,848 - monitoring - DEBUG - 资源指标更新: 内存使用率 80.0%, CPU使用率 95.6%
2025-07-30 17:32:27,826 - health_monitor - DEBUG - 系统指标 - CPU: 86.0%, 内存: 82.2%, 磁盘: 94.4%
2025-07-30 17:32:39,179 - monitoring - DEBUG - 资源指标更新: 内存使用率 80.3%, CPU使用率 87.5%
2025-07-30 17:32:54,293 - monitoring - DEBUG - 资源指标更新: 内存使用率 81.5%, CPU使用率 100.0%
2025-07-30 17:33:09,652 - monitoring - DEBUG - 资源指标更新: 内存使用率 79.9%, CPU使用率 100.0%
2025-07-30 17:33:24,920 - monitoring - DEBUG - 资源指标更新: 内存使用率 80.5%, CPU使用率 97.0%
2025-07-30 17:33:29,012 - health_monitor - DEBUG - 系统指标 - CPU: 57.5%, 内存: 78.1%, 磁盘: 94.4%
2025-07-30 17:33:40,031 - monitoring - DEBUG - 资源指标更新: 内存使用率 80.0%, CPU使用率 83.3%
2025-07-30 17:33:55,235 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.4%, CPU使用率 100.0%
2025-07-30 17:34:10,558 - monitoring - DEBUG - 资源指标更新: 内存使用率 78.8%, CPU使用率 100.0%
2025-07-30 17:34:25,700 - monitoring - DEBUG - 资源指标更新: 内存使用率 79.7%, CPU使用率 83.3%
2025-07-30 17:34:30,110 - health_monitor - DEBUG - 系统指标 - CPU: 95.7%, 内存: 79.2%, 磁盘: 94.4%
2025-07-30 17:34:40,814 - monitoring - DEBUG - 资源指标更新: 内存使用率 78.4%, CPU使用率 100.0%
2025-07-30 17:34:55,936 - monitoring - DEBUG - 资源指标更新: 内存使用率 79.5%, CPU使用率 75.0%
2025-07-30 17:35:11,068 - monitoring - DEBUG - 资源指标更新: 内存使用率 79.1%, CPU使用率 78.6%
2025-07-30 17:35:26,218 - monitoring - DEBUG - 资源指标更新: 内存使用率 79.3%, CPU使用率 93.8%
2025-07-30 17:35:31,142 - health_monitor - DEBUG - 系统指标 - CPU: 85.2%, 内存: 77.0%, 磁盘: 94.4%
2025-07-30 17:35:41,330 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.5%, CPU使用率 41.7%
2025-07-30 17:35:56,462 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.6%, CPU使用率 75.9%
2025-07-30 17:36:11,570 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.5%, CPU使用率 67.9%
2025-07-30 17:36:26,790 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.5%, CPU使用率 97.4%
2025-07-30 17:36:32,337 - health_monitor - DEBUG - 系统指标 - CPU: 92.1%, 内存: 78.4%, 磁盘: 94.4%
2025-07-30 17:36:41,974 - monitoring - DEBUG - 资源指标更新: 内存使用率 78.5%, CPU使用率 90.0%
2025-07-30 17:36:57,174 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.1%, CPU使用率 100.0%
2025-07-30 17:37:12,366 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.6%, CPU使用率 96.8%
2025-07-30 17:37:27,534 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.4%, CPU使用率 90.0%
2025-07-30 17:37:33,703 - health_monitor - DEBUG - 系统指标 - CPU: 96.0%, 内存: 76.3%, 磁盘: 94.4%
2025-07-30 17:37:42,685 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.9%, CPU使用率 78.1%
2025-07-30 17:37:57,794 - monitoring - DEBUG - 资源指标更新: 内存使用率 79.6%, CPU使用率 25.0%
2025-07-30 17:38:12,900 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.1%, CPU使用率 53.8%
2025-07-30 17:38:28,072 - monitoring - DEBUG - 资源指标更新: 内存使用率 78.7%, CPU使用率 90.0%
2025-07-30 17:38:34,747 - health_monitor - DEBUG - 系统指标 - CPU: 74.0%, 内存: 76.4%, 磁盘: 94.4%
2025-07-30 17:38:43,344 - monitoring - DEBUG - 资源指标更新: 内存使用率 79.3%, CPU使用率 82.4%
2025-07-30 17:38:58,543 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.6%, CPU使用率 100.0%
2025-07-30 17:39:13,855 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.0%, CPU使用率 87.5%
2025-07-30 17:39:29,066 - monitoring - DEBUG - 资源指标更新: 内存使用率 80.7%, CPU使用率 79.3%
2025-07-30 17:39:35,821 - health_monitor - DEBUG - 系统指标 - CPU: 84.3%, 内存: 77.3%, 磁盘: 94.4%
2025-07-30 17:39:44,357 - monitoring - DEBUG - 资源指标更新: 内存使用率 79.2%, CPU使用率 96.4%
2025-07-30 17:39:59,558 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.7%, CPU使用率 100.0%
2025-07-30 18:20:27,233 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-30 18:20:27,245 - auth_service - INFO - 统一认证服务初始化完成
2025-07-30 18:20:27,447 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-30 18:20:27,450 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-30 18:20:29,248 - BackendMockDataManager - INFO - 后端模拟数据模式已启用
2025-07-30 18:20:32,895 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\psutil\__init__.py
2025-07-30 18:20:32,896 - fallback_manager - DEBUG - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-07-30 18:20:32,902 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-07-30 18:20:33,035 - health_monitor - INFO - 健康监控器初始化完成
2025-07-30 18:20:33,057 - app.core.system_monitor - INFO - 已加载 288 个历史数据点
2025-07-30 18:20:33,066 - app.core.alert_detector - INFO - 已加载 6 个告警规则
2025-07-30 18:20:33,076 - app.core.alert_detector - INFO - 已加载 0 个当前告警
2025-07-30 18:20:33,146 - app.core.alert_detector - INFO - 已加载 370 个历史告警
2025-07-30 18:20:33,218 - app.core.alert_detector - INFO - 已加载 1 个通知渠道
2025-07-30 18:20:33,325 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-30 18:20:33,411 - alert_manager - INFO - 已初始化默认告警规则
2025-07-30 18:20:33,413 - alert_manager - INFO - 已初始化默认通知渠道
2025-07-30 18:20:33,414 - alert_manager - INFO - 告警管理器初始化完成
2025-07-30 18:20:35,709 - db_service - INFO - 数据库引擎和会话工厂创建成功
2025-07-30 18:20:35,723 - db_service - INFO - 数据库服务初始化完成
2025-07-30 18:20:35,754 - notification_service - INFO - 通知服务初始化完成
2025-07-30 18:20:35,760 - main - INFO - 错误处理模块导入成功
2025-07-30 18:20:35,967 - main - INFO - 监控模块导入成功
2025-07-30 18:20:35,975 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-07-30 18:20:42,590 - app.services.ocr_service - WARNING - OpenCV未安装，图像预处理功能将不可用
2025-07-30 18:20:42,958 - main - INFO - 应用启动中...
2025-07-30 18:20:43,007 - error_handling - INFO - 错误处理已设置
2025-07-30 18:20:43,043 - main - INFO - 错误处理系统初始化完成
2025-07-30 18:20:43,050 - monitoring - INFO - 添加指标端点成功: /metrics
2025-07-30 18:20:43,105 - monitoring - INFO - 添加健康检查端点成功: /health
2025-07-30 18:20:43,116 - monitoring - INFO - 添加详细健康检查端点成功: /api/health/detailed
2025-07-30 18:20:43,256 - monitoring - INFO - 系统信息初始化完成: Markey, Windows-10-10.0.19045-SP0, Python 3.13.2, CPU核心数: 4
2025-07-30 18:20:43,527 - monitoring - INFO - 启动资源监控线程成功
2025-07-30 18:20:43,534 - monitoring - INFO - 监控系统初始化成功（不使用中间件）
2025-07-30 18:20:43,536 - monitoring - INFO - 监控系统初始化完成
2025-07-30 18:20:43,541 - main - INFO - 监控系统初始化完成
2025-07-30 18:20:43,578 - app.db.init_db - INFO - 所有模型导入成功
2025-07-30 18:20:43,623 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-07-30 18:20:43,645 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.2%, CPU使用率 100.0%
2025-07-30 18:20:43,649 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 18:20:43,692 - app.db.init_db - INFO - 所有模型导入成功
2025-07-30 18:20:43,731 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-07-30 18:20:43,740 - app.db.init_db - INFO - 正在运行数据库迁移...
2025-07-30 18:20:43,749 - app.db.init_db - INFO - 正在检查并更新数据库表结构...
2025-07-30 18:20:43,762 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-30 18:20:43,779 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alerts")
2025-07-30 18:20:43,787 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:43,878 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_rules")
2025-07-30 18:20:43,906 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:43,911 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_channels")
2025-07-30 18:20:44,008 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:44,041 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-30 18:20:44,070 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:44,105 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_records")
2025-07-30 18:20:44,126 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:44,150 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_overviews")
2025-07-30 18:20:44,211 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:44,263 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medical_records")
2025-07-30 18:20:44,291 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:44,325 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("inpatient_records")
2025-07-30 18:20:44,363 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:44,388 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("surgery_records")
2025-07-30 18:20:44,397 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:44,409 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_reports")
2025-07-30 18:20:44,414 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:44,441 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_report_items")
2025-07-30 18:20:44,456 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:44,473 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_reports")
2025-07-30 18:20:44,483 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:44,499 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("examination_reports")
2025-07-30 18:20:44,519 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:44,530 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("documents")
2025-07-30 18:20:44,573 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:44,577 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("other_records")
2025-07-30 18:20:44,583 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:44,592 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("operation_logs")
2025-07-30 18:20:44,596 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:44,599 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("follow_up_records")
2025-07-30 18:20:44,606 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:44,610 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_diaries")
2025-07-30 18:20:44,632 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:44,675 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("registration_records")
2025-07-30 18:20:44,694 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:44,743 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("prescription_records")
2025-07-30 18:20:44,786 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:44,791 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("laboratory_records")
2025-07-30 18:20:44,808 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:44,814 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_records")
2025-07-30 18:20:44,819 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:44,849 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medications")
2025-07-30 18:20:44,857 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:44,862 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medication_usages")
2025-07-30 18:20:44,896 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:44,903 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("role_applications")
2025-07-30 18:20:44,908 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:44,925 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessments")
2025-07-30 18:20:44,955 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:44,966 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_items")
2025-07-30 18:20:44,976 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:44,981 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_responses")
2025-07-30 18:20:44,998 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:45,007 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_templates")
2025-07-30 18:20:45,011 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:45,015 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_template_questions")
2025-07-30 18:20:45,023 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:45,029 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaires")
2025-07-30 18:20:45,033 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:45,042 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_items")
2025-07-30 18:20:45,046 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:45,053 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_responses")
2025-07-30 18:20:45,061 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:45,079 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_answers")
2025-07-30 18:20:45,083 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:45,091 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_templates")
2025-07-30 18:20:45,094 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:45,098 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_template_questions")
2025-07-30 18:20:45,108 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:45,112 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_results")
2025-07-30 18:20:45,121 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:45,126 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_results")
2025-07-30 18:20:45,131 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:45,142 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("report_templates")
2025-07-30 18:20:45,146 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:45,149 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_distributions")
2025-07-30 18:20:45,157 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:45,164 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_distributions")
2025-07-30 18:20:45,168 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:45,177 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("service_stats")
2025-07-30 18:20:45,180 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:20:45,190 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-30 18:20:45,194 - app.db.init_db - INFO - 数据库表结构检查和更新完成
2025-07-30 18:20:45,200 - app.db.init_db - INFO - 模型关系初始化完成
2025-07-30 18:20:45,207 - app.db.init_db - INFO - 模型关系设置完成
2025-07-30 18:20:45,211 - main - INFO - 数据库初始化完成（强制重建）
2025-07-30 18:20:45,216 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 18:20:45,222 - main - INFO - 数据库连接正常
2025-07-30 18:20:45,226 - main - INFO - 开始初始化模板数据
2025-07-30 18:20:45,229 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 18:20:45,671 - app.db.init_templates - INFO - 开始初始化评估量表模板...
2025-07-30 18:20:45,736 - app.db.init_templates - INFO - 成功初始化 5 个评估量表模板
2025-07-30 18:20:45,787 - app.db.init_templates - INFO - 开始初始化调查问卷模板...
2025-07-30 18:20:45,856 - app.db.init_templates - INFO - 成功初始化 5 个调查问卷模板
2025-07-30 18:20:45,862 - main - INFO - 模板数据初始化完成
2025-07-30 18:20:45,865 - main - INFO - 数据库表结构已经标准化，不需要迁移
2025-07-30 18:20:45,875 - main - INFO - 应用启动完成
2025-07-30 18:20:58,792 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.1%, CPU使用率 38.5%
2025-07-30 18:20:59,410 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-30 18:20:59,411 - main - INFO - 请求没有认证头部
2025-07-30 18:20:59,412 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 18:20:59,415 - main - INFO - --- 请求结束: 200 ---

2025-07-30 18:21:01,469 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 18:21:01,471 - main - INFO - 请求没有认证头部
2025-07-30 18:21:01,471 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 18:21:01,474 - app.core.db_connection - DEBUG - 当前线程ID: 15892
2025-07-30 18:21:01,475 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-30 18:21:01,478 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-30 18:21:01,478 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-30 18:21:01,479 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 18:21:01,480 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-30 18:21:02,913 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-30 18:21:02,916 - main - INFO - --- 请求结束: 200 ---

2025-07-30 18:21:13,897 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.1%, CPU使用率 24.1%
2025-07-30 18:21:29,003 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.6%, CPU使用率 29.6%
2025-07-30 18:21:33,930 - health_monitor - DEBUG - 系统指标 - CPU: 27.9%, 内存: 65.3%, 磁盘: 94.5%
2025-07-30 18:21:44,109 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.4%, CPU使用率 4.2%
2025-07-30 18:21:59,217 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.4%, CPU使用率 54.2%
2025-07-30 18:22:14,321 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.3%, CPU使用率 42.9%
2025-07-30 18:22:29,436 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.7%, CPU使用率 100.0%
2025-07-30 18:22:34,951 - health_monitor - DEBUG - 系统指标 - CPU: 23.0%, 内存: 64.5%, 磁盘: 94.5%
2025-07-30 18:22:44,554 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 17.9%
2025-07-30 18:22:59,659 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.8%, CPU使用率 29.6%
2025-07-30 18:23:14,763 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 4.2%
2025-07-30 18:23:29,868 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 78.6%
2025-07-30 18:23:35,976 - health_monitor - DEBUG - 系统指标 - CPU: 51.6%, 内存: 60.2%, 磁盘: 94.5%
2025-07-30 18:23:44,972 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 37.0%
2025-07-30 18:24:00,078 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 8.3%
2025-07-30 18:24:15,182 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 50.0%
2025-07-30 18:24:30,287 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 50.0%
2025-07-30 18:24:36,999 - health_monitor - DEBUG - 系统指标 - CPU: 46.9%, 内存: 60.3%, 磁盘: 94.5%
2025-07-30 18:24:45,402 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 46.2%
2025-07-30 18:56:40,440 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-30 18:56:40,449 - auth_service - INFO - 统一认证服务初始化完成
2025-07-30 18:56:40,779 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-07-30 18:56:40,793 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-30 18:56:42,375 - BackendMockDataManager - INFO - 后端模拟数据模式已启用
2025-07-30 18:56:44,639 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\psutil\__init__.py
2025-07-30 18:56:44,643 - fallback_manager - DEBUG - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-07-30 18:56:44,645 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-07-30 18:56:45,159 - health_monitor - INFO - 健康监控器初始化完成
2025-07-30 18:56:45,179 - app.core.system_monitor - INFO - 已加载 288 个历史数据点
2025-07-30 18:56:45,194 - app.core.alert_detector - INFO - 已加载 6 个告警规则
2025-07-30 18:56:45,203 - app.core.alert_detector - INFO - 已加载 0 个当前告警
2025-07-30 18:56:45,214 - app.core.alert_detector - INFO - 已加载 370 个历史告警
2025-07-30 18:56:45,362 - app.core.alert_detector - INFO - 已加载 1 个通知渠道
2025-07-30 18:56:45,490 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-07-30 18:56:45,504 - alert_manager - INFO - 已初始化默认告警规则
2025-07-30 18:56:45,506 - alert_manager - INFO - 已初始化默认通知渠道
2025-07-30 18:56:45,512 - alert_manager - INFO - 告警管理器初始化完成
2025-07-30 18:56:47,957 - db_service - INFO - 数据库引擎和会话工厂创建成功
2025-07-30 18:56:47,962 - db_service - INFO - 数据库服务初始化完成
2025-07-30 18:56:47,979 - notification_service - INFO - 通知服务初始化完成
2025-07-30 18:56:47,991 - main - INFO - 错误处理模块导入成功
2025-07-30 18:56:48,459 - main - INFO - 监控模块导入成功
2025-07-30 18:56:48,472 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-07-30 18:56:58,738 - app.services.ocr_service - WARNING - OpenCV未安装，图像预处理功能将不可用
2025-07-30 18:56:58,921 - main - INFO - 应用启动中...
2025-07-30 18:56:58,924 - error_handling - INFO - 错误处理已设置
2025-07-30 18:56:58,935 - main - INFO - 错误处理系统初始化完成
2025-07-30 18:56:58,940 - monitoring - INFO - 添加指标端点成功: /metrics
2025-07-30 18:56:58,945 - monitoring - INFO - 添加健康检查端点成功: /health
2025-07-30 18:56:58,956 - monitoring - INFO - 添加详细健康检查端点成功: /api/health/detailed
2025-07-30 18:56:58,960 - monitoring - INFO - 系统信息初始化完成: Markey, Windows-10-10.0.19045-SP0, Python 3.13.2, CPU核心数: 4
2025-07-30 18:56:58,988 - monitoring - INFO - 启动资源监控线程成功
2025-07-30 18:56:58,989 - monitoring - INFO - 监控系统初始化成功（不使用中间件）
2025-07-30 18:56:58,996 - monitoring - INFO - 监控系统初始化完成
2025-07-30 18:56:59,000 - main - INFO - 监控系统初始化完成
2025-07-30 18:56:59,007 - app.db.init_db - INFO - 所有模型导入成功
2025-07-30 18:56:59,012 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-07-30 18:56:59,024 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 18:56:59,033 - app.db.init_db - INFO - 所有模型导入成功
2025-07-30 18:56:59,044 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-07-30 18:56:59,061 - app.db.init_db - INFO - 正在运行数据库迁移...
2025-07-30 18:56:59,073 - app.db.init_db - INFO - 正在检查并更新数据库表结构...
2025-07-30 18:56:59,078 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-30 18:56:59,094 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.7%, CPU使用率 100.0%
2025-07-30 18:56:59,089 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alerts")
2025-07-30 18:56:59,107 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:56:59,123 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_rules")
2025-07-30 18:56:59,128 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:56:59,137 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_channels")
2025-07-30 18:56:59,142 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:56:59,155 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-30 18:56:59,158 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:56:59,167 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_records")
2025-07-30 18:56:59,171 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:56:59,176 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_overviews")
2025-07-30 18:56:59,187 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:56:59,196 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medical_records")
2025-07-30 18:56:59,203 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:56:59,213 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("inpatient_records")
2025-07-30 18:56:59,220 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:56:59,227 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("surgery_records")
2025-07-30 18:56:59,231 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:56:59,236 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_reports")
2025-07-30 18:56:59,242 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:56:59,245 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_report_items")
2025-07-30 18:56:59,252 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:56:59,258 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_reports")
2025-07-30 18:56:59,262 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:56:59,271 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("examination_reports")
2025-07-30 18:56:59,275 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:56:59,280 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("documents")
2025-07-30 18:56:59,291 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:56:59,304 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("other_records")
2025-07-30 18:56:59,308 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:56:59,317 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("operation_logs")
2025-07-30 18:56:59,321 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:56:59,325 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("follow_up_records")
2025-07-30 18:56:59,336 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:56:59,341 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_diaries")
2025-07-30 18:56:59,352 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:56:59,357 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("registration_records")
2025-07-30 18:56:59,369 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:56:59,374 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("prescription_records")
2025-07-30 18:56:59,383 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:56:59,387 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("laboratory_records")
2025-07-30 18:56:59,402 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:56:59,408 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_records")
2025-07-30 18:56:59,412 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:56:59,434 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medications")
2025-07-30 18:56:59,441 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:56:59,452 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medication_usages")
2025-07-30 18:56:59,461 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:56:59,496 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("role_applications")
2025-07-30 18:56:59,528 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:56:59,672 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessments")
2025-07-30 18:56:59,838 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:56:59,969 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_items")
2025-07-30 18:56:59,987 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:57:00,028 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_responses")
2025-07-30 18:57:00,157 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:57:00,226 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_templates")
2025-07-30 18:57:00,236 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:57:00,255 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_template_questions")
2025-07-30 18:57:00,276 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:57:00,327 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaires")
2025-07-30 18:57:00,370 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:57:00,436 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_items")
2025-07-30 18:57:00,479 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:57:00,489 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_responses")
2025-07-30 18:57:00,526 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:57:00,569 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_answers")
2025-07-30 18:57:00,585 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:57:00,661 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_templates")
2025-07-30 18:57:00,922 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:57:01,176 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_template_questions")
2025-07-30 18:57:01,524 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:57:01,681 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_results")
2025-07-30 18:57:01,851 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:57:02,018 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_results")
2025-07-30 18:57:02,091 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:57:02,118 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("report_templates")
2025-07-30 18:57:02,153 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:57:02,207 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_distributions")
2025-07-30 18:57:02,257 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:57:02,392 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_distributions")
2025-07-30 18:57:02,454 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:57:02,535 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("service_stats")
2025-07-30 18:57:02,578 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-30 18:57:02,607 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-30 18:57:02,635 - app.db.init_db - INFO - 数据库表结构检查和更新完成
2025-07-30 18:57:02,661 - app.db.init_db - INFO - 模型关系初始化完成
2025-07-30 18:57:02,691 - app.db.init_db - INFO - 模型关系设置完成
2025-07-30 18:57:02,725 - main - INFO - 数据库初始化完成（强制重建）
2025-07-30 18:57:02,838 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 18:57:02,962 - main - INFO - 数据库连接正常
2025-07-30 18:57:03,105 - main - INFO - 开始初始化模板数据
2025-07-30 18:57:03,570 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 18:57:04,538 - app.db.init_templates - INFO - 开始初始化评估量表模板...
2025-07-30 18:57:04,723 - app.db.init_templates - INFO - 成功初始化 5 个评估量表模板
2025-07-30 18:57:04,890 - app.db.init_templates - INFO - 开始初始化调查问卷模板...
2025-07-30 18:57:05,070 - app.db.init_templates - INFO - 成功初始化 5 个调查问卷模板
2025-07-30 18:57:05,085 - main - INFO - 模板数据初始化完成
2025-07-30 18:57:05,094 - main - INFO - 数据库表结构已经标准化，不需要迁移
2025-07-30 18:57:05,129 - main - INFO - 应用启动完成
2025-07-30 18:57:09,783 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-30 18:57:09,792 - main - INFO - 请求没有认证头部
2025-07-30 18:57:09,795 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 18:57:09,801 - main - INFO - --- 请求结束: 200 ---

2025-07-30 18:57:12,101 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 18:57:12,118 - main - INFO - 请求没有认证头部
2025-07-30 18:57:12,196 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 18:57:12,621 - app.core.db_connection - DEBUG - 当前线程ID: 5096
2025-07-30 18:57:12,631 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-30 18:57:12,635 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-30 18:57:12,636 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-30 18:57:12,637 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 18:57:12,638 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-30 18:57:14,289 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 100.0%
2025-07-30 18:57:15,904 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-30 18:57:15,908 - main - INFO - --- 请求结束: 200 ---

2025-07-30 18:57:29,456 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.1%, CPU使用率 92.9%
2025-07-30 18:57:44,566 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.8%, CPU使用率 53.6%
2025-07-30 18:57:45,809 - health_monitor - DEBUG - 系统指标 - CPU: 99.2%, 内存: 68.4%, 磁盘: 94.5%
2025-07-30 18:57:59,677 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.0%, CPU使用率 62.1%
2025-07-30 18:58:14,966 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.6%, CPU使用率 100.0%
2025-07-30 18:58:30,192 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.6%, CPU使用率 46.4%
2025-07-30 18:58:45,298 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.5%, CPU使用率 64.0%
2025-07-30 18:58:46,891 - health_monitor - DEBUG - 系统指标 - CPU: 98.1%, 内存: 68.0%, 磁盘: 94.5%
2025-07-30 18:59:00,568 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.9%, CPU使用率 96.7%
2025-07-30 18:59:16,001 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.6%, CPU使用率 100.0%
2025-07-30 18:59:31,634 - monitoring - DEBUG - 资源指标更新: 内存使用率 39.8%, CPU使用率 100.0%
2025-07-30 18:59:47,253 - monitoring - DEBUG - 资源指标更新: 内存使用率 41.9%, CPU使用率 100.0%
2025-07-30 18:59:48,186 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 42.2%, 磁盘: 94.5%
2025-07-30 19:00:02,706 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.2%, CPU使用率 100.0%
2025-07-30 19:00:13,903 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-30 19:00:13,904 - main - INFO - 请求没有认证头部
2025-07-30 19:00:13,905 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 19:00:13,906 - main - INFO - --- 请求结束: 200 ---

2025-07-30 19:00:15,953 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 19:00:15,954 - main - INFO - 请求没有认证头部
2025-07-30 19:00:15,954 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 19:00:15,957 - app.core.db_connection - DEBUG - 当前线程ID: 5096
2025-07-30 19:00:15,958 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-30 19:00:15,959 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-30 19:00:15,960 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-30 19:00:15,961 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 19:00:15,961 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-30 19:00:16,784 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-30 19:00:16,787 - main - INFO - --- 请求结束: 200 ---

2025-07-30 19:00:17,847 - monitoring - DEBUG - 资源指标更新: 内存使用率 43.7%, CPU使用率 78.1%
2025-07-30 19:00:32,950 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.2%, CPU使用率 8.3%
2025-07-30 19:00:48,056 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.0%, CPU使用率 16.0%
2025-07-30 19:00:49,392 - health_monitor - DEBUG - 系统指标 - CPU: 20.9%, 内存: 44.0%, 磁盘: 94.6%
2025-07-30 19:01:03,160 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.1%, CPU使用率 3.8%
2025-07-30 19:01:18,266 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.2%, CPU使用率 20.8%
2025-07-30 19:01:33,370 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.1%, CPU使用率 4.2%
2025-07-30 19:01:48,475 - monitoring - DEBUG - 资源指标更新: 内存使用率 43.9%, CPU使用率 32.1%
2025-07-30 19:01:50,415 - health_monitor - DEBUG - 系统指标 - CPU: 66.5%, 内存: 44.1%, 磁盘: 94.7%
2025-07-30 19:02:03,586 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.5%, CPU使用率 64.3%
2025-07-30 19:02:18,692 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.3%, CPU使用率 28.6%
2025-07-30 19:02:33,797 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.2%, CPU使用率 29.2%
2025-07-30 19:02:45,564 - alert_manager - WARNING - 触发告警: disk_free_space, 当前值: 0.0, 阈值: 1024
2025-07-30 19:02:48,902 - monitoring - DEBUG - 资源指标更新: 内存使用率 42.0%, CPU使用率 37.5%
2025-07-30 19:02:51,436 - health_monitor - DEBUG - 系统指标 - CPU: 63.0%, 内存: 42.0%, 磁盘: 94.8%
2025-07-30 19:03:04,008 - monitoring - DEBUG - 资源指标更新: 内存使用率 42.0%, CPU使用率 57.1%
2025-07-30 19:03:19,112 - monitoring - DEBUG - 资源指标更新: 内存使用率 42.5%, CPU使用率 73.1%
2025-07-30 19:03:34,217 - monitoring - DEBUG - 资源指标更新: 内存使用率 42.9%, CPU使用率 62.5%
2025-07-30 19:03:45,565 - alert_manager - WARNING - 触发告警: disk_usage, 当前值: 94.8, 阈值: 90
2025-07-30 19:03:49,424 - monitoring - DEBUG - 资源指标更新: 内存使用率 42.9%, CPU使用率 96.9%
2025-07-30 19:03:52,821 - health_monitor - DEBUG - 系统指标 - CPU: 98.0%, 内存: 43.6%, 磁盘: 94.8%
2025-07-30 19:04:04,531 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.0%, CPU使用率 79.2%
2025-07-30 19:04:19,636 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.2%, CPU使用率 52.2%
2025-07-30 19:04:34,741 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.2%, CPU使用率 55.2%
2025-07-30 19:04:49,846 - monitoring - DEBUG - 资源指标更新: 内存使用率 43.8%, CPU使用率 38.5%
2025-07-30 19:04:53,895 - health_monitor - DEBUG - 系统指标 - CPU: 52.7%, 内存: 43.8%, 磁盘: 94.8%
2025-07-30 19:05:04,949 - monitoring - DEBUG - 资源指标更新: 内存使用率 43.7%, CPU使用率 52.2%
2025-07-30 19:05:20,054 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.3%, CPU使用率 57.1%
2025-07-30 19:05:35,159 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.3%, CPU使用率 35.7%
2025-07-30 19:05:50,266 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.4%, CPU使用率 36.0%
2025-07-30 19:05:54,916 - health_monitor - DEBUG - 系统指标 - CPU: 42.6%, 内存: 44.5%, 磁盘: 94.8%
2025-07-30 19:06:05,371 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.4%, CPU使用率 8.3%
2025-07-30 19:06:20,478 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.0%, CPU使用率 60.0%
2025-07-30 19:06:35,583 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.3%, CPU使用率 52.0%
2025-07-30 19:06:50,689 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.0%, CPU使用率 50.0%
2025-07-30 19:06:55,962 - health_monitor - DEBUG - 系统指标 - CPU: 74.7%, 内存: 45.7%, 磁盘: 94.8%
2025-07-30 19:07:00,223 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-30 19:07:00,225 - main - INFO - 请求没有认证头部
2025-07-30 19:07:00,225 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 19:07:00,227 - main - INFO - --- 请求结束: 200 ---

2025-07-30 19:07:02,276 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 19:07:02,277 - main - INFO - 请求没有认证头部
2025-07-30 19:07:02,278 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 19:07:02,279 - app.core.db_connection - DEBUG - 当前线程ID: 5096
2025-07-30 19:07:02,280 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-30 19:07:02,281 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-30 19:07:02,282 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-30 19:07:02,283 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 19:07:02,284 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-30 19:07:03,041 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-30 19:07:03,044 - main - INFO - --- 请求结束: 200 ---

2025-07-30 19:07:05,795 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.4%, CPU使用率 45.8%
2025-07-30 19:07:20,899 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.0%, CPU使用率 0.0%
2025-07-30 19:07:36,003 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.8%, CPU使用率 3.6%
2025-07-30 19:07:51,112 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.6%, CPU使用率 100.0%
2025-07-30 19:07:57,154 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 47.5%, 磁盘: 94.8%
2025-07-30 19:08:06,220 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.6%, CPU使用率 16.7%
2025-07-30 19:08:21,327 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.2%, CPU使用率 25.0%
2025-07-30 19:08:36,432 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.3%, CPU使用率 37.5%
2025-07-30 19:08:51,537 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.8%, CPU使用率 14.3%
2025-07-30 19:08:58,192 - health_monitor - DEBUG - 系统指标 - CPU: 9.7%, 内存: 46.7%, 磁盘: 94.8%
2025-07-30 19:09:06,642 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.7%, CPU使用率 0.0%
2025-07-30 19:09:21,769 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.3%, CPU使用率 100.0%
2025-07-30 19:09:36,876 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.6%, CPU使用率 54.2%
2025-07-30 19:09:51,994 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.9%, CPU使用率 50.0%
2025-07-30 19:09:59,213 - health_monitor - DEBUG - 系统指标 - CPU: 29.6%, 内存: 47.2%, 磁盘: 94.8%
2025-07-30 19:10:07,098 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.3%, CPU使用率 35.7%
2025-07-30 19:10:22,203 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.4%, CPU使用率 8.3%
2025-07-30 19:10:37,310 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.4%, CPU使用率 20.8%
2025-07-30 19:10:52,594 - monitoring - DEBUG - 资源指标更新: 内存使用率 33.0%, CPU使用率 100.0%
2025-07-30 19:11:00,233 - health_monitor - DEBUG - 系统指标 - CPU: 40.2%, 内存: 32.6%, 磁盘: 94.6%
2025-07-30 19:11:07,753 - monitoring - DEBUG - 资源指标更新: 内存使用率 32.7%, CPU使用率 14.3%
2025-07-30 19:11:23,221 - monitoring - DEBUG - 资源指标更新: 内存使用率 39.6%, CPU使用率 100.0%
2025-07-30 19:11:38,814 - monitoring - DEBUG - 资源指标更新: 内存使用率 43.8%, CPU使用率 100.0%
2025-07-30 19:11:54,611 - monitoring - DEBUG - 资源指标更新: 内存使用率 50.1%, CPU使用率 100.0%
2025-07-30 19:12:01,909 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 49.1%, 磁盘: 94.5%
2025-07-30 19:12:10,000 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.3%, CPU使用率 78.6%
2025-07-30 19:12:25,105 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.3%, CPU使用率 58.3%
2025-07-30 19:12:40,214 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.4%, CPU使用率 69.0%
2025-07-30 19:12:55,318 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.4%, CPU使用率 64.3%
2025-07-30 19:13:02,952 - health_monitor - DEBUG - 系统指标 - CPU: 68.2%, 内存: 45.2%, 磁盘: 94.5%
2025-07-30 19:13:10,425 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.4%, CPU使用率 82.1%
2025-07-30 19:13:25,529 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.4%, CPU使用率 88.0%
2025-07-30 19:13:40,665 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.2%, CPU使用率 81.2%
2025-07-30 19:13:55,771 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.3%, CPU使用率 65.5%
2025-07-30 19:14:03,982 - health_monitor - DEBUG - 系统指标 - CPU: 58.4%, 内存: 44.4%, 磁盘: 94.5%
2025-07-30 19:14:10,876 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.2%, CPU使用率 88.0%
2025-07-30 19:14:25,981 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.2%, CPU使用率 45.8%
2025-07-30 19:14:41,086 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.3%, CPU使用率 59.3%
2025-07-30 19:14:56,193 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.1%, CPU使用率 67.9%
2025-07-30 19:15:05,003 - health_monitor - DEBUG - 系统指标 - CPU: 59.1%, 内存: 44.1%, 磁盘: 94.5%
2025-07-30 19:15:11,297 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.1%, CPU使用率 68.0%
2025-07-30 19:15:26,404 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.2%, CPU使用率 77.8%
2025-07-30 19:15:41,510 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.4%, CPU使用率 66.7%
2025-07-30 19:15:56,614 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.3%, CPU使用率 40.0%
2025-07-30 19:16:06,028 - health_monitor - DEBUG - 系统指标 - CPU: 61.9%, 内存: 44.1%, 磁盘: 94.5%
2025-07-30 19:16:11,720 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.2%, CPU使用率 37.5%
2025-07-30 19:16:26,825 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.1%, CPU使用率 56.5%
2025-07-30 19:16:41,931 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.4%, CPU使用率 53.6%
2025-07-30 19:16:57,035 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.2%, CPU使用率 50.0%
2025-07-30 19:17:07,050 - health_monitor - DEBUG - 系统指标 - CPU: 50.6%, 内存: 44.3%, 磁盘: 94.5%
2025-07-30 19:17:12,142 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.4%, CPU使用率 66.7%
2025-07-30 19:17:27,282 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.7%, CPU使用率 90.6%
2025-07-30 19:17:42,387 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.5%, CPU使用率 50.0%
2025-07-30 19:17:57,492 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.5%, CPU使用率 35.7%
2025-07-30 19:18:08,071 - health_monitor - DEBUG - 系统指标 - CPU: 49.0%, 内存: 44.6%, 磁盘: 94.5%
2025-07-30 19:18:12,596 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.5%, CPU使用率 65.4%
2025-07-30 19:18:27,701 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.3%, CPU使用率 50.0%
2025-07-30 19:18:42,806 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.0%, CPU使用率 70.0%
2025-07-30 19:18:57,923 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.7%, CPU使用率 85.2%
2025-07-30 19:19:09,194 - health_monitor - DEBUG - 系统指标 - CPU: 82.8%, 内存: 45.2%, 磁盘: 94.5%
2025-07-30 19:19:13,028 - monitoring - DEBUG - 资源指标更新: 内存使用率 44.9%, CPU使用率 33.3%
2025-07-30 19:19:28,133 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.0%, CPU使用率 76.0%
2025-07-30 19:19:43,242 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.0%, CPU使用率 67.9%
2025-07-30 19:19:58,347 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.0%, CPU使用率 89.3%
2025-07-30 19:20:10,245 - health_monitor - DEBUG - 系统指标 - CPU: 57.3%, 内存: 45.2%, 磁盘: 94.5%
2025-07-30 19:20:13,453 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.0%, CPU使用率 58.3%
2025-07-30 19:20:28,558 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.1%, CPU使用率 37.5%
2025-07-30 19:20:43,664 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.2%, CPU使用率 51.7%
2025-07-30 19:20:58,768 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.3%, CPU使用率 48.0%
2025-07-30 19:21:11,269 - health_monitor - DEBUG - 系统指标 - CPU: 46.9%, 内存: 45.2%, 磁盘: 94.5%
2025-07-30 19:21:13,873 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.2%, CPU使用率 43.5%
2025-07-30 19:21:28,978 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.3%, CPU使用率 30.8%
2025-07-30 19:21:44,083 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.3%, CPU使用率 42.3%
2025-07-30 19:21:59,189 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.3%, CPU使用率 45.8%
2025-07-30 19:22:12,323 - health_monitor - DEBUG - 系统指标 - CPU: 66.9%, 内存: 45.4%, 磁盘: 94.5%
2025-07-30 19:22:14,294 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.1%, CPU使用率 50.0%
2025-07-30 19:22:29,400 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.2%, CPU使用率 50.0%
2025-07-30 19:22:44,505 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.3%, CPU使用率 33.3%
2025-07-30 19:22:59,609 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.2%, CPU使用率 54.5%
2025-07-30 19:23:13,348 - health_monitor - DEBUG - 系统指标 - CPU: 48.3%, 内存: 45.1%, 磁盘: 94.5%
2025-07-30 19:23:14,714 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.1%, CPU使用率 50.0%
2025-07-30 19:23:29,819 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.0%, CPU使用率 46.2%
2025-07-30 19:23:44,927 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.6%, CPU使用率 60.7%
2025-07-30 19:24:00,031 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.4%, CPU使用率 58.3%
2025-07-30 19:24:14,376 - health_monitor - DEBUG - 系统指标 - CPU: 69.9%, 内存: 45.3%, 磁盘: 94.5%
2025-07-30 19:24:15,142 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.4%, CPU使用率 53.6%
2025-07-30 19:24:30,247 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.3%, CPU使用率 54.2%
2025-07-30 19:24:45,352 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.3%, CPU使用率 25.0%
2025-07-30 19:25:00,456 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.2%, CPU使用率 48.0%
2025-07-30 19:25:15,398 - health_monitor - DEBUG - 系统指标 - CPU: 44.9%, 内存: 45.4%, 磁盘: 94.5%
2025-07-30 19:25:15,562 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.4%, CPU使用率 52.0%
2025-07-30 19:25:30,666 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.3%, CPU使用率 65.5%
2025-07-30 19:25:45,771 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.5%, CPU使用率 44.8%
2025-07-30 19:26:00,877 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.9%, CPU使用率 46.4%
2025-07-30 19:26:15,982 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.4%, CPU使用率 57.1%
2025-07-30 19:26:16,423 - health_monitor - DEBUG - 系统指标 - CPU: 40.4%, 内存: 45.4%, 磁盘: 94.5%
2025-07-30 19:26:31,087 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.5%, CPU使用率 48.3%
2025-07-30 19:26:46,794 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.8%, CPU使用率 100.0%
2025-07-30 19:27:02,187 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.8%, CPU使用率 93.2%
2025-07-30 19:27:17,297 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.6%, CPU使用率 58.3%
2025-07-30 19:27:17,449 - health_monitor - DEBUG - 系统指标 - CPU: 47.3%, 内存: 45.6%, 磁盘: 94.5%
2025-07-30 19:27:32,468 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.6%, CPU使用率 100.0%
2025-07-30 19:27:47,574 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.9%, CPU使用率 50.0%
2025-07-30 19:28:02,679 - monitoring - DEBUG - 资源指标更新: 内存使用率 45.7%, CPU使用率 42.9%
2025-07-30 19:28:17,785 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.1%, CPU使用率 86.2%
2025-07-30 19:28:18,539 - health_monitor - DEBUG - 系统指标 - CPU: 87.8%, 内存: 46.3%, 磁盘: 94.5%
2025-07-30 19:28:32,892 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.3%, CPU使用率 20.8%
2025-07-30 19:28:47,610 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-30 19:28:47,611 - main - INFO - 请求没有认证头部
2025-07-30 19:28:47,612 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 19:28:47,613 - main - INFO - --- 请求结束: 200 ---

2025-07-30 19:28:47,995 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.7%, CPU使用率 28.0%
2025-07-30 19:28:49,652 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 19:28:49,653 - main - INFO - 请求没有认证头部
2025-07-30 19:28:49,654 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 19:28:49,655 - app.core.db_connection - DEBUG - 当前线程ID: 5096
2025-07-30 19:28:49,656 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-30 19:28:49,657 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-30 19:28:49,658 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-30 19:28:49,659 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 19:28:49,660 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-30 19:28:50,439 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-30 19:28:50,442 - main - INFO - --- 请求结束: 200 ---

2025-07-30 19:29:03,099 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.5%, CPU使用率 35.7%
2025-07-30 19:29:18,204 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.2%, CPU使用率 20.8%
2025-07-30 19:29:19,568 - health_monitor - DEBUG - 系统指标 - CPU: 36.3%, 内存: 48.2%, 磁盘: 94.5%
2025-07-30 19:29:33,308 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.2%, CPU使用率 33.3%
2025-07-30 19:29:48,414 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.0%, CPU使用率 28.6%
2025-07-30 19:30:03,603 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.9%, CPU使用率 100.0%
2025-07-30 19:30:18,712 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.6%, CPU使用率 23.1%
2025-07-30 19:30:20,589 - health_monitor - DEBUG - 系统指标 - CPU: 14.8%, 内存: 47.5%, 磁盘: 94.5%
2025-07-30 19:30:33,818 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.5%, CPU使用率 60.7%
2025-07-30 19:30:48,922 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.5%, CPU使用率 0.0%
2025-07-30 19:31:04,026 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.5%, CPU使用率 0.0%
2025-07-30 19:31:19,130 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.3%, CPU使用率 14.3%
2025-07-30 19:31:21,610 - health_monitor - DEBUG - 系统指标 - CPU: 19.1%, 内存: 47.4%, 磁盘: 94.5%
2025-07-30 19:31:34,235 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.9%, CPU使用率 29.2%
2025-07-30 19:31:49,342 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.6%, CPU使用率 4.2%
2025-07-30 19:32:04,447 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.4%, CPU使用率 30.8%
2025-07-30 19:32:19,552 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.5%, CPU使用率 42.9%
2025-07-30 19:32:22,632 - health_monitor - DEBUG - 系统指标 - CPU: 34.1%, 内存: 47.9%, 磁盘: 94.5%
2025-07-30 19:32:34,657 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.2%, CPU使用率 4.2%
2025-07-30 19:32:49,762 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.1%, CPU使用率 16.7%
2025-07-30 19:33:04,867 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.1%, CPU使用率 31.0%
2025-07-30 19:33:19,971 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.0%, CPU使用率 0.0%
2025-07-30 19:33:23,654 - health_monitor - DEBUG - 系统指标 - CPU: 9.4%, 内存: 47.9%, 磁盘: 94.5%
2025-07-30 19:33:35,075 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.9%, CPU使用率 12.5%
2025-07-30 19:33:50,180 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.8%, CPU使用率 17.2%
2025-07-30 19:34:05,284 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.8%, CPU使用率 0.0%
2025-07-30 19:34:20,417 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.0%, CPU使用率 81.2%
2025-07-30 19:34:24,677 - health_monitor - DEBUG - 系统指标 - CPU: 19.8%, 内存: 48.1%, 磁盘: 94.5%
2025-07-30 19:34:35,523 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.0%, CPU使用率 21.4%
2025-07-30 19:34:50,628 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.2%, CPU使用率 4.0%
2025-07-30 19:35:05,733 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.4%, CPU使用率 50.0%
2025-07-30 19:35:20,838 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.1%, CPU使用率 53.8%
2025-07-30 19:35:25,700 - health_monitor - DEBUG - 系统指标 - CPU: 43.5%, 内存: 46.1%, 磁盘: 94.5%
2025-07-30 19:35:35,942 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.3%, CPU使用率 48.1%
2025-07-30 19:35:51,046 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.3%, CPU使用率 50.0%
2025-07-30 19:36:06,151 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.1%, CPU使用率 9.1%
2025-07-30 19:36:21,257 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.1%, CPU使用率 67.9%
2025-07-30 19:36:26,728 - health_monitor - DEBUG - 系统指标 - CPU: 48.7%, 内存: 46.2%, 磁盘: 94.5%
2025-07-30 19:36:36,362 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.1%, CPU使用率 52.0%
2025-07-30 19:36:51,466 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.1%, CPU使用率 65.2%
2025-07-30 19:37:06,572 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.2%, CPU使用率 76.0%
2025-07-30 19:37:21,676 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.1%, CPU使用率 55.6%
2025-07-30 19:37:27,751 - health_monitor - DEBUG - 系统指标 - CPU: 43.2%, 内存: 46.3%, 磁盘: 94.5%
2025-07-30 19:37:36,785 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.3%, CPU使用率 61.5%
2025-07-30 19:37:51,889 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.2%, CPU使用率 45.8%
2025-07-30 19:38:06,994 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.2%, CPU使用率 48.1%
2025-07-30 19:38:22,100 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.2%, CPU使用率 48.3%
2025-07-30 19:38:28,771 - health_monitor - DEBUG - 系统指标 - CPU: 57.8%, 内存: 46.2%, 磁盘: 94.5%
2025-07-30 19:38:37,205 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.2%, CPU使用率 56.0%
2025-07-30 19:38:52,310 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.2%, CPU使用率 53.8%
2025-07-30 19:39:07,414 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.5%, CPU使用率 40.0%
2025-07-30 19:39:22,519 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.4%, CPU使用率 15.4%
2025-07-30 19:39:29,795 - health_monitor - DEBUG - 系统指标 - CPU: 54.4%, 内存: 46.4%, 磁盘: 94.5%
2025-07-30 19:39:37,623 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.5%, CPU使用率 72.0%
2025-07-30 19:39:52,730 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.4%, CPU使用率 52.0%
2025-07-30 19:40:07,835 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.2%, CPU使用率 42.3%
2025-07-30 19:40:22,939 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.3%, CPU使用率 68.0%
2025-07-30 19:40:30,819 - health_monitor - DEBUG - 系统指标 - CPU: 48.2%, 内存: 46.3%, 磁盘: 94.5%
2025-07-30 19:40:38,044 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.3%, CPU使用率 50.0%
2025-07-30 19:40:53,149 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.4%, CPU使用率 46.2%
2025-07-30 19:41:08,255 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.3%, CPU使用率 28.6%
2025-07-30 19:41:23,359 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.2%, CPU使用率 66.7%
2025-07-30 19:41:31,844 - health_monitor - DEBUG - 系统指标 - CPU: 57.2%, 内存: 46.3%, 磁盘: 94.5%
2025-07-30 19:41:38,467 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.6%, CPU使用率 70.8%
2025-07-30 19:41:53,620 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.1%, CPU使用率 100.0%
2025-07-30 19:42:08,727 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.9%, CPU使用率 57.1%
2025-07-30 19:42:23,831 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.0%, CPU使用率 48.0%
2025-07-30 19:42:32,866 - health_monitor - DEBUG - 系统指标 - CPU: 54.5%, 内存: 47.0%, 磁盘: 94.5%
2025-07-30 19:42:38,971 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.0%, CPU使用率 73.3%
2025-07-30 19:42:54,075 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.9%, CPU使用率 50.0%
2025-07-30 19:43:09,180 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.7%, CPU使用率 75.0%
2025-07-30 19:43:21,036 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-07-30 19:43:21,037 - main - INFO - 请求没有认证头部
2025-07-30 19:43:21,038 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 19:43:21,040 - main - INFO - --- 请求结束: 200 ---

2025-07-30 19:43:23,087 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-07-30 19:43:23,088 - main - INFO - 请求没有认证头部
2025-07-30 19:43:23,088 - main - INFO - 没有认证头部，设置用户为None
2025-07-30 19:43:23,090 - app.core.db_connection - DEBUG - 当前线程ID: 5096
2025-07-30 19:43:23,091 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-07-30 19:43:23,092 - app.core.db_connection - INFO - 数据库连接已创建
2025-07-30 19:43:23,093 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-07-30 19:43:23,094 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-07-30 19:43:23,095 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-07-30 19:43:23,982 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-07-30 19:43:23,987 - main - INFO - --- 请求结束: 200 ---

2025-07-30 19:43:24,288 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.6%, CPU使用率 78.6%
2025-07-30 19:43:33,895 - health_monitor - DEBUG - 系统指标 - CPU: 26.8%, 内存: 48.5%, 磁盘: 94.5%
2025-07-30 19:43:39,393 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.6%, CPU使用率 38.5%
2025-07-30 19:43:54,500 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.6%, CPU使用率 33.3%
2025-07-30 19:44:09,605 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.4%, CPU使用率 36.0%
2025-07-30 19:44:24,723 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.5%, CPU使用率 89.3%
2025-07-30 19:44:34,915 - health_monitor - DEBUG - 系统指标 - CPU: 35.1%, 内存: 48.8%, 磁盘: 94.5%
2025-07-30 19:44:39,837 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.8%, CPU使用率 50.0%
2025-07-30 19:44:54,943 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.7%, CPU使用率 35.7%
2025-07-30 19:45:10,048 - monitoring - DEBUG - 资源指标更新: 内存使用率 40.6%, CPU使用率 75.0%
