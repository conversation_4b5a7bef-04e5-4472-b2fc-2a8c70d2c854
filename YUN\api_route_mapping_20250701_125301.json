{"available_paths": ["/api/auth/register/login", "/api/auth/create_temp_admin", "/api/auth/register", "/api/auth/frontend_login", "/api/auth/login", "/api/auth/frontend_login_json", "/api/auth/login_json", "/api/auth/test-auth", "/api/auth/simple_login", "/api/auth/test-unified-auth", "/api/auth/logout", "/api/users", "/api/users/me", "/api/users/search", "/api/users/mobile-users", "/api/users/mobile-profile/{custom_id}", "/api/users/profile", "/api/users/{custom_id}", "/api/users/safe-delete/{custom_id}", "/api/users/force-delete/{custom_id}", "/api/update-password", "/api/", "/api/medical-records/user/{custom_id}", "/api/medical-records/medical-records", "/api/lab-reports/user/{custom_id}", "/api/lab-reports/lab-reports/user/{custom_id}", "/api/lab-reports/lab-reports", "/api/documents/", "/api/documents/{document_id}", "/api/documents/upload", "/api/documents/{document_id}/preview", "/api/documents/{document_id}/download", "/api/documents/mobile-upload", "/api/documents/{document_id}/ocr", "/api/documents/{document_id}/ocr-result", "/api/examination-reports/user/{custom_id}", "/api/examination-reports/", "/api/examination-reports/{report_id}", "/api/physical-exams/user/{custom_id}", "/api/follow-ups/user/{custom_id}", "/api/follow-ups/", "/api/follow-ups/{record_id}", "/api/health-diaries/user/{custom_id}", "/api/health-diaries/", "/api/health-diaries/{diary_id}", "/api/other-records/user/{custom_id}", "/api/other-records/", "/api/other-records/{record_id}", "/api/other-records/search/{custom_id}", "/api/templates/assessment-templates", "/api/templates/assessment-templates/{template_id}", "/api/templates/assessment-templates/{template_id}/distribute", "/api/templates/questionnaire-templates", "/api/templates/questionnaire-templates/{template_id}", "/api/templates/assessment-templates/{template_id}/status", "/api/templates/questionnaire-templates/{template_id}/status", "/api/templates/questionnaire-templates/all", "/api/templates/questionnaire-templates/{template_id}/distribute", "/api/templates/questionnaire-templates/{template_id}/review", "/api/assessments", "/api/assessments/{assessment_id}", "/api/assessments/{assessment_id}/questions", "/api/assessments/{assessment_id}/review", "/api/assessments/{assessment_id}/push", "/api/assessments/{assessment_id}/import-questions", "/api/assessments/user/{custom_id}", "/api/assessments/{assessment_id}/distribute", "/api/questionnaires", "/api/questionnaires/{questionnaire_id}", "/api/questionnaires/{questionnaire_id}/questions", "/api/questionnaires/{questionnaire_id}/review", "/api/questionnaires/{questionnaire_id}/push", "/api/questionnaires/{questionnaire_id}/import-questions", "/api/questionnaires/{questionnaire_id}/distribute", "/api/questionnaires/user/{custom_id}", "/api/questionnaires/history", "/api/questionnaires/{questionnaire_id}/submit", "/api/questionnaires/generate", "/api/user-health-records/", "/api/user-health-records/{record_id}", "/api/user-health-records/user/{custom_id}", "/api/api/user-health-statistics/{custom_id}", "/api/api/performance/summary", "/api/api/performance/api-metrics", "/api/api/performance/system-metrics", "/api/api/performance/clear-metrics", "/api/api/performance/health-check", "/api/mobile/templates/assessment-templates", "/api/mobile/assessments", "/api/mobile/assessments/{assessment_id}/submit", "/api/mobile/templates/questionnaire-templates", "/api/mobile/questionnaires", "/api/mobile/questionnaires/{questionnaire_id}/submit", "/api/mobile/pending-questionnaires", "/api/mobile/history-questionnaires", "/api/mobile/completed-questionnaires", "/api/mobile/pending-assessments", "/api/mobile/history-assessments", "/api/mobile/completed-assessments", "/api/mobile/assessment-reports/{report_id}", "/api/mobile/questionnaire-reports/{report_id}", "/api/health/ping", "/api/health/check", "/api/health", "/api/service-stats", "/api/service-stats/auth", "/api/service-stats/db", "/api/service-stats/token", "/api/service-stats/public", "/api/service-stats/metrics-public", "/api/v1/aggregated/users/{custom_id}/data", "/api/v1/aggregated/users/{custom_id}/mobile", "/api/v1/aggregated/users/{custom_id}/frontend", "/api/v1/aggregated/users/{custom_id}/export", "/api/v1/aggregated/data-sources/status", "/api/v1/aggregated/data-sources/{source_type}/config", "/api/v1/aggregated/users/{custom_id}/cache", "/api/v1/aggregated/users/{custom_id}/questionnaires", "/api/v1/aggregated/health", "/api/v1/aggregated/health-profile/{custom_id}", "/api/metrics-public", "/api/dashboard/stats/{user_id}", "/api/dashboard/", "/api/dashboard/debug", "/api/dashboard/weight-trend/{user_id}", "/api/dashboard/bp-trend/{user_id}", "/api/dashboard/exam-distribution/{user_id}", "/api/dashboard/health-index/{user_id}", "/api/dashboard/timeline/{user_id}", "/api/metrics/", "/api/metrics/public", "/api/metrics/historical/{metric_type}", "/api/alerts/", "/api/alerts/resolve/{alert_id}", "/api/alerts/user/{custom_id}/database", "/api/alerts/database", "/api/alerts/database/{alert_id}", "/api/alerts/rules/database", "/api/alerts/rules", "/api/alerts/rules/{rule_id}", "/api/alerts/channels", "/api/alerts/channels/{channel_id}", "/api/alerts/channels/{channel_id}/test", "/api/permissions/permissions", "/api/permissions/permissions/roles", "/api/permissions/permissions/update", "/api/permissions/approvals", "/api/permissions/permissions/approvals", "/api/permissions/approve/{approval_id}", "/api/permissions/permissions/approve/{approval_id}", "/api/permissions/reject/{approval_id}", "/api/permissions/permissions/reject/{approval_id}", "/api/role-applications/apply", "/api/role-applications/my-applications", "/api/role-applications/applications", "/api/role-applications/applications/{application_id}/process", "/api/role-applications/upload-certificate", "/api/clinical-scales/assessments", "/api/clinical-scales/questionnaires", "/api/clinical-scales/standard-assessments", "/api/clinical-scales/standard-assessments/{assessment_id}", "/api/clinical-scales/standard-questionnaires", "/api/clinical-scales/templates", "/api/monitoring/assessment-questionnaire", "/api/monitoring/status", "/api/monitoring/fix-issues", "/api/monitoring/fix", "/api/monitoring/export-report", "/api/monitoring/export", "/api/monitoring/user/{user_id}", "/api/monitoring/users", "/api/monitoring/realtime", "/api/assessment-results/user/{custom_id}", "/api/assessment-results/", "/api/assessment-results/{result_id}", "/api/assessment-results/{result_id}/generate-report", "/api/assessment-results/export/{result_id}", "/api/assessment-results/trend/{custom_id}/{assessment_id}", "/api/assessment-results/list", "/api/assessment_results/user/{custom_id}", "/api/assessment_results/", "/api/assessment_results/{result_id}", "/api/assessment_results/{result_id}/generate-report", "/api/assessment_results/export/{result_id}", "/api/assessment_results/trend/{custom_id}/{assessment_id}", "/api/assessment_results/list", "/api/questionnaire-results/user/{custom_id}", "/api/questionnaire-results/", "/api/questionnaire-results/{result_id}", "/api/questionnaire-results/{result_id}/generate-report", "/api/questionnaire-results/calculate-from-response/{response_id}", "/api/questionnaire-results/export/{result_id}", "/api/questionnaire-results/trend/{custom_id}/{questionnaire_id}", "/api/questionnaire-results/list", "/api/questionnaire_results/user/{custom_id}", "/api/questionnaire_results/", "/api/questionnaire_results/{result_id}", "/api/questionnaire_results/{result_id}/generate-report", "/api/questionnaire_results/calculate-from-response/{response_id}", "/api/questionnaire_results/export/{result_id}", "/api/questionnaire_results/trend/{custom_id}/{questionnaire_id}", "/api/questionnaire_results/list", "/api/questionnaire-distributions/user/{custom_id}", "/api/questionnaire-distributions/{distribution_id}", "/api/assessment-distributions/user/{custom_id}", "/api/assessment-distributions/{distribution_id}", "/api/questionnaire-responses/{response_id}", "/api/questionnaire-responses/user/{custom_id}", "/api/assessment-responses/{response_id}", "/api/assessment-responses/user/{custom_id}", "/api/dimensions/assessment/response/{response_id}/dimension-scores", "/api/dimensions/questionnaire/response/{response_id}/dimension-scores", "/api/dimensions/assessment/response/{response_id}/recalculate-dimensions", "/api/mobile/assessments/{assessment_id}/submit-raw", "/api/mobile/questionnaires/{questionnaire_id}/submit-raw", "/api/register/login", "/api/create_temp_admin", "/api/register", "/api/frontend_login", "/api/login", "/api/frontend_login_json", "/api/login_json", "/api/test-auth", "/api/simple_login", "/api/test-unified-auth", "/api/logout", "/auth/register/login", "/auth/create_temp_admin", "/auth/register", "/auth/frontend_login", "/auth/login", "/auth/frontend_login_json", "/auth/login_json", "/auth/test-auth", "/auth/simple_login", "/auth/test-unified-auth", "/auth/logout", "/auth/update-password", "/api/management/status", "/api/management/events", "/api/management/health-report", "/api/management/projects", "/api/management/services", "/api/management/services/{service_id}/restart", "/api/management/config", "/api/management/configs", "/api/management/configs/env", "/api/management/tests", "/api/management/tests/run", "/api/management/deployments", "/api/management/alerts", "/api/management/metrics", "/api/management/environments", "/api/management/services/status", "/api/management/project/components", "/api/management/project/components/{component_name}/{action}", "/api/management/project/components/{component_name}/logs", "/api/management/configs/{config_id}/content", "/api/management/configs/{config_id}/backup", "/api/management/configs/{config_id}", "/api/management/configs/env/{env_key}", "/api/management/alerts/rules", "/api/management/alert-rules", "/api/management/alert-rules/{rule_id}", "/api/management/tests/suites", "/api/management/services/start-all", "/api/management/services/restart-all", "/api/management/services/stop-all", "/api/management/tests/run-all", "/api/management/deployment/build", "/api/management/deployment/deploy", "/api/management/export", "/api/simple/user", "/api/simple/health-records", "/api/simple/health-records/{record_id}", "/api/documents/{file_id}/ocr", "/api/documents/{file_id}/ocr-result", "/api/debug/test-public", "/api/debug/test-auth", "/api/debug/test-users", "/api/debug/test-headers", "/api/metrics-public/", "/api/test-login", "/", "/login", "/api/public-test", "/api/auth-test", "/api/user-test", "/api/direct-login", "/api/json-login"], "total_endpoints": 290, "methods_summary": {"POST": 107, "GET": 182, "PUT": 26, "DELETE": 27, "PATCH": 1}}